using PBRTools;
using System.Collections;
using TKFrame;
using TKPlugins;
using UnityEngine;
using ZGame;
using ZGame.Stage;
using ZGameChess;

public class LobbyTinyController : TKBehaviour
{

    public CustomLightingTool lightingTool;
    public GameObject hero_root;

    private int m_currentTinyId = -1;
    private GameObject goTinyHero_Cur;
    private UnityEngine.Coroutine m_loadCoroutine = null;

    private void InitDS()
    {
        DeviceSettingPerformer gs;
        if (DeviceSettingPerformer.Instance == null)
        {
            gs = new DeviceSettingPerformer();
            Services.AddService<DeviceSettingPerformer>(gs);

            EDevicePower displayMode = EDevicePower.EDP_Ultra;
            var fpsMode = 3;
            var resolutionSetting = 0;
            gs.InitGameDisplayMode(ref displayMode, ref resolutionSetting, ref fpsMode, false, false, 0);
        }
    }

    protected override void Awake()
    {
        base.Awake();

        DataBaseManager.Instance.Initialize();

        InitDS();
    }

    public void LoadTinyHero(int tinyId)
    {
        DataBaseManager.Instance.Initialize();
        if (m_loadCoroutine != null)
            SystemManager.getInstance().StopCoroutine(m_loadCoroutine);

        m_currentTinyId = tinyId;
        bool fromCos = ChessPlayerBodyCache.GetBodyResNameLobby(tinyId, out string model_path, out string model_name);
        m_loadCoroutine = SystemManager.getInstance().StartCoroutine(ChessPlayerLoader.Load(model_path, model_name, Callback_LoadedBodyAsset_Local, this));
    }

    public void DestoryTinyHero()
    {
        if (m_loadCoroutine != null)
            SystemManager.getInstance().StopCoroutine(m_loadCoroutine);

        if (null != goTinyHero_Cur)
        {
            var tmpCompAudioFrameHandler = goTinyHero_Cur.GetComponent<LobbyAnimationFrameAudioEventHandlerV2>();
            if (null != tmpCompAudioFrameHandler)
            {
                tmpCompAudioFrameHandler.DisposeReleaseList();
            }
            GameObject.Destroy(goTinyHero_Cur);
            goTinyHero_Cur = null;
        }
    }

    void Callback_LoadedBodyAsset_Local(LoadedAsset loadAsset, LittleLegendCfg cfg)
    {
        GameObject tmpGo = loadAsset.GetAsset<GameObject>();

        Diagnostic.Log($"LobbyTinyHeroReplace::Callback_LoadedBodyAsset with: {loadAsset.BundlePath} {loadAsset.AssetName}, tmpGo is null:{tmpGo == null}");

        Callback_LoadedBodyAsset(tmpGo, cfg);
    }

    void Callback_LoadedBodyAsset(GameObject tmpGo, LittleLegendCfg cfg)
    {
        //GameObject tmpGo = loadAsset.GetAsset<GameObject>();
        if (null == tmpGo)
        {
            return;
        }
        //
        GameObject tmpGoTarget = GameObject.Instantiate<GameObject>(tmpGo);

        if (null != tmpGoTarget)
        {
            ToolKit.SetLayer(tmpGoTarget, LayerMask.NameToLayer("Level"));
            var tmpCompAnim = tmpGoTarget.GetComponentInChildren<LobbyAnimationFrameAudioEventHandlerV2>();
            if (null != tmpCompAnim)
            {
                tmpCompAnim.EmHandler_Scene_Cur = LobbyAnimationFrameAudioEventHandlerV2.EmHandler_Scene.em_Lobby;
            }
            tmpGoTarget.transform.SetParent(hero_root.transform);
            tmpGoTarget.transform.localPosition = Vector3.zero;
            tmpGoTarget.transform.localRotation = tmpGo.transform.localRotation;//Quaternion.identity;
            tmpGoTarget.transform.localScale = tmpGo.transform.localScale;//Vector3.one;
            //�滻Animator
            Animator tmpComponent = tmpGoTarget.GetComponent<Animator>();
            string strPlayInfoClipName = "idle_UI";//"UI_Normal_enter";
            Diagnostic.Log("LobbyTinyHeroReplace::IntendToPlay:" + strPlayInfoClipName);
            if (null != tmpComponent)
            {
                tmpComponent.Play(strPlayInfoClipName);
            }

            //
            var tmpComp = tmpGoTarget.GetComponentInChildren<BoxCollider>(true);
            if (null != tmpComp)
            {
                var tmpArrTargetComp = tmpComp.gameObject.GetComponents<LobbySceneCharacterAnimEntry>();
                if (null == tmpArrTargetComp)
                {
                    //

                }
                else
                {
                    //
                    if (tmpArrTargetComp.Length < 2)
                    {
                        for (int i = 2 - tmpArrTargetComp.Length; i > 0; i--)
                        {
                            tmpComp.gameObject.AddComponent<LobbySceneCharacterAnimEntry>();
                        }

                        tmpArrTargetComp = tmpComp.gameObject.GetComponents<LobbySceneCharacterAnimEntry>();
                    }
                    else
                    {
                        //
                    }

                    //
                    tmpArrTargetComp[0].emSceneObj2SysVal = LobbySceneCharacterAnimEntry.EmCharacterAnimatorObj2System.em_CharacterAnimatorObj2Sys_Role_Hero;
                    //tmpArrTargetComp[0].objValParam = null;
                    tmpArrTargetComp[1].emSceneObj2SysVal = LobbySceneCharacterAnimEntry.EmCharacterAnimatorObj2System.em_CharacterAnimatorObj2Sys_None;
                }

            }


            var tmpCharacterHangPoint = tmpGoTarget.GetComponent<CharacterHangPoint>();
            if (null != tmpCharacterHangPoint)
            {
                tmpCharacterHangPoint.SetLittleLegendCfg(cfg);
                //
                var tmpTinyHero = DataBaseManager.Instance.SearchTinyHero(m_currentTinyId);
                if (null != tmpTinyHero && tmpTinyHero.sEffectLayerScene == "1")//��Ч��ӰӰ�쵽��С�����������⴦����ʾ.
                {
                    ChessUtil.CheckInitEffectDataList(tmpGoTarget, tmpCharacterHangPoint, GameObjectLayer.BattleEffect);
                }
                else
                {
                    ChessUtil.CheckInitEffectDataList(tmpGoTarget, tmpCharacterHangPoint, GameObjectLayer.Scene);
                }
            }

            lightingTool.UpdateLightingInternal();
            //�����Ч������
            var tmpObjComp = tmpGoTarget.TryGetComponent<ChessPlayerEffectEvent>();
            if (tmpObjComp != null)
            {
                tmpObjComp.CustomLight = lightingTool;
            }

            //��Todo���滻ССӢ��
            if (null != goTinyHero_Cur)
            {
                var tmpCompAudioFrameHandler = goTinyHero_Cur.GetComponent<LobbyAnimationFrameAudioEventHandlerV2>();
                if (null != tmpCompAudioFrameHandler)
                {
                    tmpCompAudioFrameHandler.DisposeReleaseList();
                }
                GameObject.Destroy(goTinyHero_Cur);
            }
            goTinyHero_Cur = tmpGoTarget;
        }
    }
}