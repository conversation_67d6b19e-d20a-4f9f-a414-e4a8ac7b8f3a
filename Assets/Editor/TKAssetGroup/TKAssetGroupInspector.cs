using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using SVNTools;
using TKPlugins;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace TKFrame.Asset
{
    [CustomEditor(typeof(TKAssetGroup))]
    public class TKAssetGroupInspector : Editor
    {
        private const string tkassetgproup_debug = "tkassetgroup_debug";
        private TKAssetGroup m_assetGroup;
        private SerializedProperty m_lods_Property;

        private void OnEnable()
        {
            m_assetGroup = target as TKAssetGroup;
            m_lods_Property = serializedObject.FindProperty("m_lods");
            if (!EditorPrefs.HasKey(tkassetgproup_debug))
            {
                EditorPrefs.SetBool(tkassetgproup_debug, tkassetgroup_dbug_value);
            }
            tkassetgroup_dbug_value = EditorPrefs.GetBool(tkassetgproup_debug);
        }

        public override void OnInspectorGUI()
        {
            // base.OnInspectorGUI();
            // serializedObject.Update();
            #region 原有属性绘制
            SerializedProperty property = serializedObject.GetIterator();
            bool enterChildren = true;
            while (property.NextVisible(enterChildren))
            {
                enterChildren = false;
                if (property.propertyPath == m_lods_Property.propertyPath)
                {
                    continue;
                }
                EditorGUILayout.PropertyField(property, true);
            }
            #region 自定义list属性绘制
            EditorGUILayout.LabelField("Lod数据集合", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;

            for (int i = 0; i < m_lods_Property.arraySize; i++)
            {
                SerializedProperty tkAssetPathProperty = m_lods_Property.GetArrayElementAtIndex(i);
                SerializedProperty lodTypeProperty = tkAssetPathProperty.FindPropertyRelative("m_lodType");
                LodType lodTypeValue = (LodType)lodTypeProperty.enumValueIndex;
                string lodTypeStr = Enum.GetName(typeof(LodType), lodTypeValue);
                EnumNameAttribute enumNameAttribute = CommonUtilGlobal.GetEnumNameArrAttributeValue(lodTypeValue)[0];
                EditorGUILayout.PropertyField(tkAssetPathProperty, new GUIContent(string.Format("{0} - {1}", lodTypeStr, enumNameAttribute.Value)), true);
                // 添加按钮以选中对应的资源
                EditorGUILayout.BeginHorizontal();
                {
                    UnityEngine.Object detailConfigObj = tkAssetPathProperty.GetMemberValue("DetailConfig") as UnityEngine.Object;
                    if (detailConfigObj && GUILayout.Button("查看配置"))
                    {
                        EditorGUIUtility.PingObject(detailConfigObj);
                        Selection.activeObject = detailConfigObj;
                    }
                    EditorGUILayout.EndHorizontal();
                }
                EditorGUILayout.Space();
            }

            EditorGUI.indentLevel--;
            #endregion
            serializedObject.ApplyModifiedProperties();
            #endregion

            if (this.IsStoredPrefab(m_assetGroup.gameObject))
            {
                GUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button(new GUIContent("分离Lods资源信息", "通过既定规则找到对应Lod的信息存放进配置文件中")))
                    {
                        if (EditorUtility.DisplayDialog("二次确认", "一般不需要操作这一步，因为蓝盾构建线会定时分离资源", "是的，我确定要分离资源", "取消"))
                        {
                            m_assetGroup.RecoverAllLodShows();
                            m_assetGroup.CollectLods();
                        }
                    }
                    if (GUILayout.Button(new GUIContent("显示", "通过ab, asset找到对应资源")))
                    {
                        m_assetGroup.RecoverAllLodShows();
                    }
                    GUILayout.EndHorizontal();
                }
                tkassetgroup_dbug_value = EditorGUILayout.ToggleLeft("是否自动显示", tkassetgroup_dbug_value);
                if (GUI.changed)
                {
                    EditorPrefs.SetBool(tkassetgproup_debug, tkassetgroup_dbug_value);
                }
            }

            #region SVN操作

            EditorGUILayout.HelpBox("会将当前show+lod的分离记录文件一起提交", MessageType.Info);
            if (GUILayout.Button("SVN 提交"))
            {
                GameObject prefab = GetPrefab(m_assetGroup.gameObject);
                TKAssetGroup curAssetGroup = prefab.GetComponent<TKAssetGroup>();
                if (curAssetGroup.CheckTransformLodHasAssets()) //检查下是否资源没分离完;
                {
                    curAssetGroup.CollectLods();
                }
                
                List<string> svnCmitPathList = new List<string>();
                string prefabPath = AssetDatabase.GetAssetPath(prefab);
                //show;
                CheckAddToCommitFile(prefabPath, svnCmitPathList, true);
                //lod configs;
                for (int i = (int)LodType.sealow; i <= (int)LodType.high; i++)
                {
                    TKAssetPath tkAssetPath = curAssetGroup.GetTkAssetPath((LodType)i);
                    if (tkAssetPath != null)
                    {
                        string lodCfgPath = tkAssetPath.GetLodCfgPathToSaveDiskByTkAssetPath(curAssetGroup.transform);
                        if (!string.IsNullOrEmpty(lodCfgPath))
                        {
                            CheckAddToCommitFile(lodCfgPath, svnCmitPathList, true);
                        }
                    }
                }
                SVNTool.SvnCommit(svnCmitPathList, string.Format("编辑器下一键提交: {0}", curAssetGroup.gameObject.name));
            }

            #endregion
        }
        
        private GameObject GetPrefab(GameObject instance)
        {
            return (GameObject)PrefabUtility.GetCorrespondingObjectFromSource(instance);
        }
        
        private static void CheckAddToCommitFile(string realPath, List<string> svnCmitPathList, bool autoWithMeta)
        {
            if (File.Exists(realPath))
            {
                svnCmitPathList.Add(realPath);
            }

            string realPathWithMeta = string.Format("{0}.meta", realPath);
            if (File.Exists(realPathWithMeta))
            {
                svnCmitPathList.Add(realPathWithMeta);
            }
        }
        
        private bool IsStoredPrefab(GameObject gameObject)
        {
            PrefabAssetType prefabType = PrefabUtility.GetPrefabAssetType(gameObject);
            return prefabType == PrefabAssetType.Regular || prefabType == PrefabAssetType.Variant;
        }
        
        public static bool tkassetgroup_dbug_value = true;
        private static int previousHierarchyCount;
        private static Object[] draggedObjects = null;
        [InitializeOnLoadMethod]
        private static void InitializePrefabInstanceUpdatedListener()
        {
            PrefabUtility.prefabInstanceUpdated += OnPrefabInstanceUpdated;
            EditorApplication.hierarchyChanged += OnHierarchyChanged;
            EditorApplication.update += CheckDragAndDrop;
            previousHierarchyCount = 0;
        }
        
        private static void CheckDragAndDrop()
        {
            if (DragAndDrop.visualMode != DragAndDropVisualMode.None && draggedObjects == null)
            {
                // 拖拽开始，保存对象的引用
                draggedObjects = DragAndDrop.objectReferences;
                previousHierarchyCount = GetHierarchyCount();
            }
            else if (DragAndDrop.visualMode == DragAndDropVisualMode.None && draggedObjects != null)
            {
                // 拖拽结束，清空引用
                bool isHasTkAssetGroup = false;
                for (int i = 0; i < draggedObjects.Length; i++)
                {
                    var droppedObj = draggedObjects[i];
                    if (droppedObj is GameObject)
                    {
                        TKAssetGroup findGroup = ((GameObject)droppedObj).GetComponent<TKAssetGroup>();
                        if (findGroup)
                        {
                            isHasTkAssetGroup = true;
                            break; 
                        }
                    }
                }
                if (!isHasTkAssetGroup)
                {
                    draggedObjects = null;
                }
            }
        }

        private static void OnHierarchyChanged()
        {
            if (!tkassetgroup_dbug_value || draggedObjects == null)
            {
                previousHierarchyCount = 0;
                return;
            }

            int currentHierarchyCount = GetHierarchyCount();

            if (currentHierarchyCount > previousHierarchyCount)
            {
                Object[] droppedObjects = draggedObjects;
                List<GameObject> droppedObjList = new List<GameObject>();
                for (int i = 0; i < droppedObjects.Length; i++)
                {
                    var droppedObj = droppedObjects[i];
                    if (droppedObj is GameObject)
                    {
                        TKAssetGroup findGroup = ((GameObject)droppedObj).GetComponent<TKAssetGroup>();
                        if (findGroup)
                        {
                            droppedObjList.Add((GameObject)droppedObj);   
                        }
                    }
                }
                var assetGroupArr = GameObject.FindObjectsOfType<TKAssetGroup>();
                Array.Sort(assetGroupArr, (a, b) =>
                {
                    return b.transform.GetSiblingIndex().CompareTo(a.transform.GetSiblingIndex());
                });
                for (int i = 0; i < assetGroupArr.Length; i++)
                {
                    var assetItem = assetGroupArr[i];
                    var assetSourceName = assetItem.gameObject.name.Split(" ")[0].Replace("(Clone)", "");
                    for (int k = 0; k < droppedObjList.Count; k++)
                    {
                        var curDroppedAssetGroupGo = droppedObjList[k];
                        if (curDroppedAssetGroupGo.name.Equals(assetSourceName))
                        {
                            assetItem.RecoverAllLodShows();
                            droppedObjList.RemoveAt(k);
                            break;
                        }
                    }
                }
            }

            draggedObjects = null;
            previousHierarchyCount = currentHierarchyCount;
        }

        private static int GetHierarchyCount()
        {
            GameObject[] allObjects = Object.FindObjectsOfType<GameObject>();
            return allObjects.Length;
        }

        private static void OnPrefabInstanceUpdated(GameObject instance)
        {
            TKAssetGroup group = instance.GetComponent<TKAssetGroup>();
            if (group && group.CheckTransformLodHasAssets())
            {
                group.CollectLods();
                //重新apply;
                PrefabUtility.ApplyPrefabInstance(instance, InteractionMode.UserAction);
            }
            if (!tkassetgroup_dbug_value) return;
            if (group)
            {
                group.RecoverAllLodShows();   
            }
        }
    }
}

