#if ACGGAME_CLIENT
using System.IO;

namespace UniqueInfo.Editor.ChessPlayerConfig_Gen
{
    public class WriteUniqueInfo_Config
    {
        // Write Method
        // UnityEngine.AnimationCurve Class Func

        // UnityEngine.AnimationCurve Ref Func
        public static void UnityEngine_AnimationCurve_RefWrite(UnityEngine.AnimationCurve data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.UnityEngine_AnimationCurve_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // string Class Func

        // string Ref Func
        public static void System_String_RefWrite(string data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.System_String_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // System.Collections.Generic.KeyValuePair<string, int> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_Write(System.Collections.Generic.KeyValuePair<string, int> data, BinaryWriter bw)
        {     
            // Field
            WriteUniqueInfo_Config.System_String_RefWrite(data.Key, bw);
            TransferBaseField_Write.WriteRaw(data.Value, bw);
        }

        // System.Collections.Generic.KeyValuePair<string, int> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_RefWrite(System.Collections.Generic.KeyValuePair<string, int> data, BinaryWriter bw)
        {
            WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_Write(data, bw);
        }

        // System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> Class Func
        public static void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Write(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> dataCollection, BinaryWriter bw)
        {
            int len = dataCollection.Count;
            AlignTool.WriteLength(len, bw);
            foreach(System.Collections.Generic.KeyValuePair<string, int> data in dataCollection)
            {
                WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_RefWrite(data, bw);
            }
        }

        // System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> Ref Func
        public static void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_RefWrite(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // ChessPlayerAnimConfig Class Func
        public static void ChessPlayerAnimConfig_Write(ChessPlayerAnimConfig data, BinaryWriter bw)
        {     
            // Field
            WriteUniqueInfo_Config.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_RefWrite(data.animList, bw);
            TransferBaseField_Write.WriteRaw(data.totalWeight, bw);
            AlignTool.Align(bw);
        }

        // ChessPlayerAnimConfig Ref Func
        public static void ChessPlayerAnimConfig_RefWrite(ChessPlayerAnimConfig data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.ChessPlayerAnimConfig_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_Write(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> data, BinaryWriter bw)
        {     
            // Field
            WriteUniqueInfo_Config.System_String_RefWrite(data.Key, bw);
            WriteUniqueInfo_Config.ChessPlayerAnimConfig_RefWrite(data.Value, bw);
        }

        // System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_RefWrite(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> data, BinaryWriter bw)
        {
            WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_Write(data, bw);
        }

        // TKFrame.TKDictionary<string, ChessPlayerAnimConfig> Class Func
        public static void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Write(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> dataCollection, BinaryWriter bw)
        {
            int len = dataCollection.Count;
            AlignTool.WriteLength(len, bw);
            foreach(System.Collections.Generic.KeyValuePair<string, ChessPlayerAnimConfig> data in dataCollection)
            {
                WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_String_ChessPlayerAnimConfig_End_RefWrite(data, bw);
            }
        }

        // TKFrame.TKDictionary<string, ChessPlayerAnimConfig> Ref Func
        public static void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_RefWrite(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // float[,] Class Func
        public static void System_Single_2_Array_Write(float[,] data, BinaryWriter bw)
        {
            int len = data.Length;
            TransferBaseField_Write.WriteRaw(len, bw);
        
            int l0 = data.GetLength(0);
            TransferBaseField_Write.WriteRaw(l0, bw);
            int l1 = data.GetLength(1);
            TransferBaseField_Write.WriteRaw(l1, bw);
        
            for (int i = 0; i < len; i++)
            {
                int index = i;
                int d1 = index % l1; index /= l1;
                int d0 = index % l0; index /= l0;
                TransferBaseField_Write.WriteRaw(data[d0,d1], bw);
            }
        }

        // float[,] Ref Func
        public static void System_Single_2_Array_RefWrite(float[,] data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.System_Single_2_Array_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // AnimSheetConfig Class Func
        public static void AnimSheetConfig_Write(AnimSheetConfig data, BinaryWriter bw)
        {     
            // Field
            WriteUniqueInfo_Config.System_Single_2_Array_RefWrite(data.m_crossFades, bw);
            AlignTool.Align(bw);
        }

        // AnimSheetConfig Ref Func
        public static void AnimSheetConfig_RefWrite(AnimSheetConfig data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.AnimSheetConfig_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // bool[] Class Func
        public static void System_Boolean_1_Array_Write(bool[] data, BinaryWriter bw)
        {
            int len = data.Length;
            TransferBaseField_Write.WriteRaw(len, bw);
        
            int l0 = data.GetLength(0);
            TransferBaseField_Write.WriteRaw(l0, bw);
        
            for (int i = 0; i < len; i++)
            {
                int index = i;
                int d0 = index % l0; index /= l0;
                TransferBaseField_Write.WriteRaw(data[d0], bw);
            }
        }

        // bool[] Ref Func
        public static void System_Boolean_1_Array_RefWrite(bool[] data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.System_Boolean_1_Array_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // AnimSpringManagerConfig Class Func
        public static void AnimSpringManagerConfig_Write(AnimSpringManagerConfig data, BinaryWriter bw)
        {     
            // Field
            WriteUniqueInfo_Config.System_Boolean_1_Array_RefWrite(data.m_animSpringConfig, bw);
            AlignTool.Align(bw);
        }

        // AnimSpringManagerConfig Ref Func
        public static void AnimSpringManagerConfig_RefWrite(AnimSpringManagerConfig data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.AnimSpringManagerConfig_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // System.Collections.Generic.KeyValuePair<int, int> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_Write(System.Collections.Generic.KeyValuePair<int, int> data, BinaryWriter bw)
        {     
            // Field
            TransferBaseField_Write.WriteRaw(data.Key, bw);
            TransferBaseField_Write.WriteRaw(data.Value, bw);
        }

        // System.Collections.Generic.KeyValuePair<int, int> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_RefWrite(System.Collections.Generic.KeyValuePair<int, int> data, BinaryWriter bw)
        {
            WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_Write(data, bw);
        }

        // TKFrame.TKDictionary<int, int> Class Func
        public static void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Write(TKFrame.TKDictionary<int, int> dataCollection, BinaryWriter bw)
        {
            int len = dataCollection.Count;
            AlignTool.WriteLength(len, bw);
            foreach(System.Collections.Generic.KeyValuePair<int, int> data in dataCollection)
            {
                WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_System_Int32_End_RefWrite(data, bw);
            }
        }

        // TKFrame.TKDictionary<int, int> Ref Func
        public static void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_RefWrite(TKFrame.TKDictionary<int, int> data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // System.Collections.Generic.HashSet<string> Class Func
        public static void System_Collections_Generic_HashSet_1_Begin_System_String_End_Write(System.Collections.Generic.HashSet<string> dataCollection, BinaryWriter bw)
        {
            int len = dataCollection.Count;
            AlignTool.WriteLength(len, bw);
            foreach(string data in dataCollection)
            {
                WriteUniqueInfo_Config.System_String_RefWrite(data, bw);
            }
        }

        // System.Collections.Generic.HashSet<string> Ref Func
        public static void System_Collections_Generic_HashSet_1_Begin_System_String_End_RefWrite(System.Collections.Generic.HashSet<string> data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.System_Collections_Generic_HashSet_1_Begin_System_String_End_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // ChessPlayerConfig Class Func
        public static void ChessPlayerConfig_Write(ChessPlayerConfig data, BinaryWriter bw)
        {     
            // Field
            TransferBaseField_Write.WriteRaw(data.rotateSpeed, bw);
            TransferBaseField_Write.WriteRaw(data.walkSpeed, bw);
            TransferBaseField_Write.WriteRaw(data.walkStartDis, bw);
            TransferBaseField_Write.WriteRaw(data.walkStartTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.walkStartCurve, bw);
            TransferBaseField_Write.WriteRaw(data.walkStopDis, bw);
            TransferBaseField_Write.WriteRaw(data.walkStopTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.walkStopCurve, bw);
            TransferBaseField_Write.WriteRaw(data.walk02StopDis, bw);
            TransferBaseField_Write.WriteRaw(data.walk02StopTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.walk02StopCurve, bw);
            TransferBaseField_Write.WriteRaw(data.runSpeed, bw);
            TransferBaseField_Write.WriteRaw(data.runStartDis, bw);
            TransferBaseField_Write.WriteRaw(data.runStartTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.runStartCurve, bw);
            TransferBaseField_Write.WriteRaw(data.runStopDis, bw);
            TransferBaseField_Write.WriteRaw(data.runStopTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.runStopCurve, bw);
            TransferBaseField_Write.WriteRaw(data.run02StopDis, bw);
            TransferBaseField_Write.WriteRaw(data.run02StopTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.run02StopCurve, bw);
            TransferBaseField_Write.WriteRaw(data.rushDis, bw);
            TransferBaseField_Write.WriteRaw(data.rushTime, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.rushCurve, bw);
            TransferBaseField_Write.WriteRaw(data.hurtTime1, bw);
            TransferBaseField_Write.WriteRaw(data.hurtDis1, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.hurtCurve1, bw);
            TransferBaseField_Write.WriteRaw(data.hurtTime2, bw);
            TransferBaseField_Write.WriteRaw(data.hurtDis2, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.hurtCurve2, bw);
            TransferBaseField_Write.WriteRaw(data.deathHurtTime, bw);
            TransferBaseField_Write.WriteRaw(data.deathHurtDis, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.deathHurtCurve, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.hurtHeightCurve2, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.jumpIn, bw);
            WriteUniqueInfo_Config.UnityEngine_AnimationCurve_RefWrite(data.jumpOut, bw);
            TransferBaseField_Write.WriteRaw(data.jump_in_min_height, bw);
            TransferBaseField_Write.WriteRaw(data.jump_in_max_height, bw);
            TransferBaseField_Write.WriteRaw(data.jump_in_touch_ground_frame, bw);
            TransferBaseField_Write.WriteRaw(data.jump_out_min_height, bw);
            TransferBaseField_Write.WriteRaw(data.jump_out_max_height, bw);
            TransferBaseField_Write.WriteRaw(data.jump_in_start_frame, bw);
            TransferBaseField_Write.WriteRaw(data.jump_out_touch_ground_frame, bw);
            WriteUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_RefWrite(data.m_animList, bw);
            WriteUniqueInfo_Config.System_String_RefWrite(data.m_victoryAnimationHomeName, bw);
            WriteUniqueInfo_Config.System_String_RefWrite(data.m_victoryAnimationAwayName, bw);
            TransferBaseField_Write.WriteRaw(data.m_victoryAnimationHomeTime, bw);
            TransferBaseField_Write.WriteRaw(data.m_victoryAnimationAwayTime, bw);
            WriteUniqueInfo_Config.AnimSheetConfig_RefWrite(data.m_animSheetConfig, bw);
            WriteUniqueInfo_Config.AnimSpringManagerConfig_RefWrite(data.m_animSpringManagerConfig, bw);
            WriteUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_RefWrite(data.m_animIds, bw);
            TransferBaseField_Write.WriteRaw(data.BodyScaleChangePlan, bw);
            WriteUniqueInfo_Config.System_Collections_Generic_HashSet_1_Begin_System_String_End_RefWrite(data.m_clickRootMotionNames, bw);
            TransferBaseField_Write.WriteRaw(data.m_maxRoundSelectMaxScale, bw);
            AlignTool.Align(bw);
        }

        // ChessPlayerConfig Ref Func
        public static void ChessPlayerConfig_RefWrite(ChessPlayerConfig data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.ChessPlayerConfig_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

        // System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> Class Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_Write(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> data, BinaryWriter bw)
        {     
            // Field
            TransferBaseField_Write.WriteRaw(data.Key, bw);
            WriteUniqueInfo_Config.ChessPlayerConfig_RefWrite(data.Value, bw);
        }

        // System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> Ref Func
        public static void System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_RefWrite(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> data, BinaryWriter bw)
        {
            WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_Write(data, bw);
        }

        // UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Class Func
        public static void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Write(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> dataCollection, BinaryWriter bw)
        {
            int len = dataCollection.Count;
            AlignTool.WriteLength(len, bw);
            foreach(System.Collections.Generic.KeyValuePair<int, ChessPlayerConfig> data in dataCollection)
            {
                WriteUniqueInfo_Config.System_Collections_Generic_KeyValuePair_2_Begin_System_Int32_ChessPlayerConfig_End_RefWrite(data, bw);
            }
        }

        // UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Ref Func
        public static void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_RefWrite(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> data, BinaryWriter bw)
        {
            if (data == null)
            {
                TransferBaseField_Write.WriteRaw(-1, bw);
                return;
            }
        
            int index = WriteUniqueInfo_ConfigPool.instance.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_GetIndex(data);
            TransferBaseField_Write.WriteRaw(index, bw);
        }

    }
}
#endif
