#if ACGGAME_CLIENT
using System.Collections.Generic;
using System.IO;
using System;
using TKFrame;
using UnityEditor;
using UnityEngine;

namespace UniqueInfo.Editor
{
    public partial class UniqueInfoWriteUtil
    {
        public static void GenerateFile(string path, List<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>> dataLists)
        {
            DateTime start = DateTime.Now;
        
            if(File.Exists(path))
                File.Delete(path);
        
            foreach (var data in dataLists)
            {
                ChessPlayerConfig_Gen.WriteUniqueInfo_NormalPool.instance.Collect_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data(data);
            }
        
            using (FileStream fs = new FileStream(path, FileMode.Create))
            {
                using (CodingStream codingStream = new CodingStream(fs, CodingStream.CodingMethod.Encode))
                {
                    using (FixCharBinaryWriter bw = new FixCharBinaryWriter(codingStream))
                    {
                        ChessPlayerConfig_Gen.WriteUniqueInfo_ConfigPool.instance.GeneratePoolData(bw);
                    }
                }
            }
        
            string md5 = string.Empty;
            using (FileStream fs = new FileStream(path, FileMode.Open))
            {
                fs.Position = fs.Length;
                using (CodingStream codingStream = new CodingStream(fs, CodingStream.CodingMethod.Encode, true))
                {
                    using (BinaryWriter bw = new BinaryWriter(codingStream))
                    {
                        ChessPlayerConfig_Gen.WriteUniqueInfo_NormalPool.instance.GeneratePoolData(bw);
                    }
                }
        
                fs.Position = 0;
                md5 = MD5Tools.GenerateMD5(fs);
            }
        
            ChessPlayerConfig_Gen.WriteUniqueInfo_NormalPool.Clear();
            ChessPlayerConfig_Gen.WriteUniqueInfo_ConfigPool.Clear();
        
            DateTime end = DateTime.Now;
            TimeSpan delta = end - start;
            Diagnostic.Log("UniqueInfoTool Write UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> Finish " + delta.TotalSeconds + "s md5 " + md5);
        
            AssetDatabase.Refresh();
        }
    }
}

#endif
