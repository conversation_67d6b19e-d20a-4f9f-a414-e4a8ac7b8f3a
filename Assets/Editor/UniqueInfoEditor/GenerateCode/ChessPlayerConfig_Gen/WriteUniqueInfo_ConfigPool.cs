#if ACGGAME_CLIENT
using System.Collections.Generic;
using System.IO;
using TKFrame;

namespace UniqueInfo.Editor.ChessPlayerConfig_Gen
{
    public class WriteUniqueInfo_ConfigPool
    {
        private static object m_locker = new object();
        private static WriteUniqueInfo_ConfigPool m_instance = null;

        public static WriteUniqueInfo_ConfigPool instance
        {
            get
            {
                if (m_instance == null)
                {
                    lock (m_locker)
                    {
                        if (m_instance == null)
                        {
                            m_instance = new WriteUniqueInfo_ConfigPool();
                        }
                    }
                }

                return m_instance;
            }
        }

        private List<uint> m_posOffset = new List<uint>();
        private List<uint> m_dataPoolOffset = new List<uint>();
        private long m_startOffset = 0;

        // Class Pool
        #region UnityEngine.AnimationCurve
        
        private List<UnityEngine.AnimationCurve> m_UnityEngine_AnimationCurve_List = new List<UnityEngine.AnimationCurve>();
        private Dictionary<UnityEngine.AnimationCurve, int> m_UnityEngine_AnimationCurve_Data_Dict = new Dictionary<UnityEngine.AnimationCurve, int>(new UnityEngine_AnimationCurve_Compare());
        private Dictionary<UnityEngine.AnimationCurve, int> m_UnityEngine_AnimationCurve_Ref_Dict = new Dictionary<UnityEngine.AnimationCurve, int>();
        
        public int UnityEngine_AnimationCurve_GetIndex(UnityEngine.AnimationCurve data)
        {
            int result = -1;
            if (m_UnityEngine_AnimationCurve_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_UnityEngine_AnimationCurve_Data_Dict.TryGetValue(data, out result))
            {
                result = m_UnityEngine_AnimationCurve_List.Count;
                m_UnityEngine_AnimationCurve_Data_Dict.Add(data, result);
                m_UnityEngine_AnimationCurve_Ref_Dict.Add(data, result);
                m_UnityEngine_AnimationCurve_List.Add(data);
            }
            return result;
        }
        
        public UnityEngine.AnimationCurve UnityEngine_AnimationCurve_GetData(int index)
        {
            return m_UnityEngine_AnimationCurve_List[index];
        }
        
        private void UnityEngine_AnimationCurve_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_UnityEngine_AnimationCurve_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                TransferClassField_Write.Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class UnityEngine_AnimationCurve_Compare : IEqualityComparer<UnityEngine.AnimationCurve>
        {
            public bool Equals(UnityEngine.AnimationCurve x, UnityEngine.AnimationCurve y)
            {
                bool result = true;
                EqualUniqueInfo_Config.UnityEngine_AnimationCurve_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(UnityEngine.AnimationCurve data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.UnityEngine_AnimationCurve_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region string
        
        private List<string> m_System_String_List = new List<string>();
        private Dictionary<string, int> m_System_String_Data_Dict = new Dictionary<string, int>(new System_String_Compare());
        private Dictionary<string, int> m_System_String_Ref_Dict = new Dictionary<string, int>();
        
        public int System_String_GetIndex(string data)
        {
            int result = -1;
            if (m_System_String_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_System_String_Data_Dict.TryGetValue(data, out result))
            {
                result = m_System_String_List.Count;
                m_System_String_Data_Dict.Add(data, result);
                m_System_String_Ref_Dict.Add(data, result);
                m_System_String_List.Add(data);
            }
            return result;
        }
        
        public string System_String_GetData(int index)
        {
            return m_System_String_List[index];
        }
        
        private void System_String_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_System_String_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                TransferClassField_Write.Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class System_String_Compare : IEqualityComparer<string>
        {
            public bool Equals(string x, string y)
            {
                bool result = true;
                EqualUniqueInfo_Config.System_String_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(string data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.System_String_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion


        #region System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>
        
        private List<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>> m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_List = new List<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>>();
        private Dictionary<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>, int> m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Data_Dict = new Dictionary<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>, int>(new System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Compare());
        private Dictionary<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>, int> m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Ref_Dict = new Dictionary<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>, int>();
        
        public int System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_GetIndex(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> data)
        {
            int result = -1;
            if (m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Data_Dict.TryGetValue(data, out result))
            {
                result = m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_List.Count;
                m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Data_Dict.Add(data, result);
                m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Ref_Dict.Add(data, result);
                m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_List.Add(data);
            }
            return result;
        }
        
        public System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_GetData(int index)
        {
            return m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_List[index];
        }
        
        private void System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Compare : IEqualityComparer<System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>>>
        {
            public bool Equals(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> x, System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> y)
            {
                bool result = true;
                EqualUniqueInfo_Config.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<string, int>> data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region ChessPlayerAnimConfig
        
        private List<ChessPlayerAnimConfig> m_ChessPlayerAnimConfig_List = new List<ChessPlayerAnimConfig>();
        private Dictionary<ChessPlayerAnimConfig, int> m_ChessPlayerAnimConfig_Data_Dict = new Dictionary<ChessPlayerAnimConfig, int>(new ChessPlayerAnimConfig_Compare());
        private Dictionary<ChessPlayerAnimConfig, int> m_ChessPlayerAnimConfig_Ref_Dict = new Dictionary<ChessPlayerAnimConfig, int>();
        
        public int ChessPlayerAnimConfig_GetIndex(ChessPlayerAnimConfig data)
        {
            int result = -1;
            if (m_ChessPlayerAnimConfig_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_ChessPlayerAnimConfig_Data_Dict.TryGetValue(data, out result))
            {
                result = m_ChessPlayerAnimConfig_List.Count;
                m_ChessPlayerAnimConfig_Data_Dict.Add(data, result);
                m_ChessPlayerAnimConfig_Ref_Dict.Add(data, result);
                m_ChessPlayerAnimConfig_List.Add(data);
            }
            return result;
        }
        
        public ChessPlayerAnimConfig ChessPlayerAnimConfig_GetData(int index)
        {
            return m_ChessPlayerAnimConfig_List[index];
        }
        
        private void ChessPlayerAnimConfig_Write_Lists(BinaryWriter bw)
        {
            AlignTool.WriteLength(m_ChessPlayerAnimConfig_List.Count, bw);
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (var data in m_ChessPlayerAnimConfig_List)
            {
                WriteUniqueInfo_Config.ChessPlayerAnimConfig_Write(data, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class ChessPlayerAnimConfig_Compare : IEqualityComparer<ChessPlayerAnimConfig>
        {
            public bool Equals(ChessPlayerAnimConfig x, ChessPlayerAnimConfig y)
            {
                bool result = true;
                EqualUniqueInfo_Config.ChessPlayerAnimConfig_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(ChessPlayerAnimConfig data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.ChessPlayerAnimConfig_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion


        #region TKFrame.TKDictionary<string, ChessPlayerAnimConfig>
        
        private List<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>> m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_List = new List<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>>();
        private Dictionary<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>, int> m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Data_Dict = new Dictionary<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>, int>(new TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Compare());
        private Dictionary<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>, int> m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Ref_Dict = new Dictionary<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>, int>();
        
        public int TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_GetIndex(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> data)
        {
            int result = -1;
            if (m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Data_Dict.TryGetValue(data, out result))
            {
                result = m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_List.Count;
                m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Data_Dict.Add(data, result);
                m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Ref_Dict.Add(data, result);
                m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_List.Add(data);
            }
            return result;
        }
        
        public TKFrame.TKDictionary<string, ChessPlayerAnimConfig> TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_GetData(int index)
        {
            return m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_List[index];
        }
        
        private void TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Compare : IEqualityComparer<TKFrame.TKDictionary<string, ChessPlayerAnimConfig>>
        {
            public bool Equals(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> x, TKFrame.TKDictionary<string, ChessPlayerAnimConfig> y)
            {
                bool result = true;
                EqualUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(TKFrame.TKDictionary<string, ChessPlayerAnimConfig> data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region float[,]
        
        private List<float[,]> m_System_Single_2_Array_List = new List<float[,]>();
        private Dictionary<float[,], int> m_System_Single_2_Array_Data_Dict = new Dictionary<float[,], int>(new System_Single_2_Array_Compare());
        private Dictionary<float[,], int> m_System_Single_2_Array_Ref_Dict = new Dictionary<float[,], int>();
        
        public int System_Single_2_Array_GetIndex(float[,] data)
        {
            int result = -1;
            if (m_System_Single_2_Array_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_System_Single_2_Array_Data_Dict.TryGetValue(data, out result))
            {
                result = m_System_Single_2_Array_List.Count;
                m_System_Single_2_Array_Data_Dict.Add(data, result);
                m_System_Single_2_Array_Ref_Dict.Add(data, result);
                m_System_Single_2_Array_List.Add(data);
            }
            return result;
        }
        
        public float[,] System_Single_2_Array_GetData(int index)
        {
            return m_System_Single_2_Array_List[index];
        }
        
        private void System_Single_2_Array_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_System_Single_2_Array_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.System_Single_2_Array_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class System_Single_2_Array_Compare : IEqualityComparer<float[,]>
        {
            public bool Equals(float[,] x, float[,] y)
            {
                bool result = true;
                EqualUniqueInfo_Config.System_Single_2_Array_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(float[,] data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.System_Single_2_Array_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region AnimSheetConfig
        
        private List<AnimSheetConfig> m_AnimSheetConfig_List = new List<AnimSheetConfig>();
        private Dictionary<AnimSheetConfig, int> m_AnimSheetConfig_Data_Dict = new Dictionary<AnimSheetConfig, int>(new AnimSheetConfig_Compare());
        private Dictionary<AnimSheetConfig, int> m_AnimSheetConfig_Ref_Dict = new Dictionary<AnimSheetConfig, int>();
        
        public int AnimSheetConfig_GetIndex(AnimSheetConfig data)
        {
            int result = -1;
            if (m_AnimSheetConfig_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_AnimSheetConfig_Data_Dict.TryGetValue(data, out result))
            {
                result = m_AnimSheetConfig_List.Count;
                m_AnimSheetConfig_Data_Dict.Add(data, result);
                m_AnimSheetConfig_Ref_Dict.Add(data, result);
                m_AnimSheetConfig_List.Add(data);
            }
            return result;
        }
        
        public AnimSheetConfig AnimSheetConfig_GetData(int index)
        {
            return m_AnimSheetConfig_List[index];
        }
        
        private void AnimSheetConfig_Write_Lists(BinaryWriter bw)
        {
            AlignTool.WriteLength(m_AnimSheetConfig_List.Count, bw);
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (var data in m_AnimSheetConfig_List)
            {
                WriteUniqueInfo_Config.AnimSheetConfig_Write(data, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class AnimSheetConfig_Compare : IEqualityComparer<AnimSheetConfig>
        {
            public bool Equals(AnimSheetConfig x, AnimSheetConfig y)
            {
                bool result = true;
                EqualUniqueInfo_Config.AnimSheetConfig_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(AnimSheetConfig data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.AnimSheetConfig_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region bool[]
        
        private List<bool[]> m_System_Boolean_1_Array_List = new List<bool[]>();
        private Dictionary<bool[], int> m_System_Boolean_1_Array_Data_Dict = new Dictionary<bool[], int>(new System_Boolean_1_Array_Compare());
        private Dictionary<bool[], int> m_System_Boolean_1_Array_Ref_Dict = new Dictionary<bool[], int>();
        
        public int System_Boolean_1_Array_GetIndex(bool[] data)
        {
            int result = -1;
            if (m_System_Boolean_1_Array_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_System_Boolean_1_Array_Data_Dict.TryGetValue(data, out result))
            {
                result = m_System_Boolean_1_Array_List.Count;
                m_System_Boolean_1_Array_Data_Dict.Add(data, result);
                m_System_Boolean_1_Array_Ref_Dict.Add(data, result);
                m_System_Boolean_1_Array_List.Add(data);
            }
            return result;
        }
        
        public bool[] System_Boolean_1_Array_GetData(int index)
        {
            return m_System_Boolean_1_Array_List[index];
        }
        
        private void System_Boolean_1_Array_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_System_Boolean_1_Array_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.System_Boolean_1_Array_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class System_Boolean_1_Array_Compare : IEqualityComparer<bool[]>
        {
            public bool Equals(bool[] x, bool[] y)
            {
                bool result = true;
                EqualUniqueInfo_Config.System_Boolean_1_Array_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(bool[] data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.System_Boolean_1_Array_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region AnimSpringManagerConfig
        
        private List<AnimSpringManagerConfig> m_AnimSpringManagerConfig_List = new List<AnimSpringManagerConfig>();
        private Dictionary<AnimSpringManagerConfig, int> m_AnimSpringManagerConfig_Data_Dict = new Dictionary<AnimSpringManagerConfig, int>(new AnimSpringManagerConfig_Compare());
        private Dictionary<AnimSpringManagerConfig, int> m_AnimSpringManagerConfig_Ref_Dict = new Dictionary<AnimSpringManagerConfig, int>();
        
        public int AnimSpringManagerConfig_GetIndex(AnimSpringManagerConfig data)
        {
            int result = -1;
            if (m_AnimSpringManagerConfig_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_AnimSpringManagerConfig_Data_Dict.TryGetValue(data, out result))
            {
                result = m_AnimSpringManagerConfig_List.Count;
                m_AnimSpringManagerConfig_Data_Dict.Add(data, result);
                m_AnimSpringManagerConfig_Ref_Dict.Add(data, result);
                m_AnimSpringManagerConfig_List.Add(data);
            }
            return result;
        }
        
        public AnimSpringManagerConfig AnimSpringManagerConfig_GetData(int index)
        {
            return m_AnimSpringManagerConfig_List[index];
        }
        
        private void AnimSpringManagerConfig_Write_Lists(BinaryWriter bw)
        {
            AlignTool.WriteLength(m_AnimSpringManagerConfig_List.Count, bw);
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (var data in m_AnimSpringManagerConfig_List)
            {
                WriteUniqueInfo_Config.AnimSpringManagerConfig_Write(data, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class AnimSpringManagerConfig_Compare : IEqualityComparer<AnimSpringManagerConfig>
        {
            public bool Equals(AnimSpringManagerConfig x, AnimSpringManagerConfig y)
            {
                bool result = true;
                EqualUniqueInfo_Config.AnimSpringManagerConfig_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(AnimSpringManagerConfig data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.AnimSpringManagerConfig_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion


        #region TKFrame.TKDictionary<int, int>
        
        private List<TKFrame.TKDictionary<int, int>> m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_List = new List<TKFrame.TKDictionary<int, int>>();
        private Dictionary<TKFrame.TKDictionary<int, int>, int> m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Data_Dict = new Dictionary<TKFrame.TKDictionary<int, int>, int>(new TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Compare());
        private Dictionary<TKFrame.TKDictionary<int, int>, int> m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Ref_Dict = new Dictionary<TKFrame.TKDictionary<int, int>, int>();
        
        public int TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_GetIndex(TKFrame.TKDictionary<int, int> data)
        {
            int result = -1;
            if (m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Data_Dict.TryGetValue(data, out result))
            {
                result = m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_List.Count;
                m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Data_Dict.Add(data, result);
                m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Ref_Dict.Add(data, result);
                m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_List.Add(data);
            }
            return result;
        }
        
        public TKFrame.TKDictionary<int, int> TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_GetData(int index)
        {
            return m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_List[index];
        }
        
        private void TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Compare : IEqualityComparer<TKFrame.TKDictionary<int, int>>
        {
            public bool Equals(TKFrame.TKDictionary<int, int> x, TKFrame.TKDictionary<int, int> y)
            {
                bool result = true;
                EqualUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(TKFrame.TKDictionary<int, int> data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region System.Collections.Generic.HashSet<string>
        
        private List<System.Collections.Generic.HashSet<string>> m_System_Collections_Generic_HashSet_1_Begin_System_String_End_List = new List<System.Collections.Generic.HashSet<string>>();
        private Dictionary<System.Collections.Generic.HashSet<string>, int> m_System_Collections_Generic_HashSet_1_Begin_System_String_End_Data_Dict = new Dictionary<System.Collections.Generic.HashSet<string>, int>(new System_Collections_Generic_HashSet_1_Begin_System_String_End_Compare());
        private Dictionary<System.Collections.Generic.HashSet<string>, int> m_System_Collections_Generic_HashSet_1_Begin_System_String_End_Ref_Dict = new Dictionary<System.Collections.Generic.HashSet<string>, int>();
        
        public int System_Collections_Generic_HashSet_1_Begin_System_String_End_GetIndex(System.Collections.Generic.HashSet<string> data)
        {
            int result = -1;
            if (m_System_Collections_Generic_HashSet_1_Begin_System_String_End_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_System_Collections_Generic_HashSet_1_Begin_System_String_End_Data_Dict.TryGetValue(data, out result))
            {
                result = m_System_Collections_Generic_HashSet_1_Begin_System_String_End_List.Count;
                m_System_Collections_Generic_HashSet_1_Begin_System_String_End_Data_Dict.Add(data, result);
                m_System_Collections_Generic_HashSet_1_Begin_System_String_End_Ref_Dict.Add(data, result);
                m_System_Collections_Generic_HashSet_1_Begin_System_String_End_List.Add(data);
            }
            return result;
        }
        
        public System.Collections.Generic.HashSet<string> System_Collections_Generic_HashSet_1_Begin_System_String_End_GetData(int index)
        {
            return m_System_Collections_Generic_HashSet_1_Begin_System_String_End_List[index];
        }
        
        private void System_Collections_Generic_HashSet_1_Begin_System_String_End_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_System_Collections_Generic_HashSet_1_Begin_System_String_End_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.System_Collections_Generic_HashSet_1_Begin_System_String_End_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class System_Collections_Generic_HashSet_1_Begin_System_String_End_Compare : IEqualityComparer<System.Collections.Generic.HashSet<string>>
        {
            public bool Equals(System.Collections.Generic.HashSet<string> x, System.Collections.Generic.HashSet<string> y)
            {
                bool result = true;
                EqualUniqueInfo_Config.System_Collections_Generic_HashSet_1_Begin_System_String_End_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(System.Collections.Generic.HashSet<string> data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.System_Collections_Generic_HashSet_1_Begin_System_String_End_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        #region ChessPlayerConfig
        
        private List<ChessPlayerConfig> m_ChessPlayerConfig_List = new List<ChessPlayerConfig>();
        private Dictionary<ChessPlayerConfig, int> m_ChessPlayerConfig_Data_Dict = new Dictionary<ChessPlayerConfig, int>(new ChessPlayerConfig_Compare());
        private Dictionary<ChessPlayerConfig, int> m_ChessPlayerConfig_Ref_Dict = new Dictionary<ChessPlayerConfig, int>();
        
        public int ChessPlayerConfig_GetIndex(ChessPlayerConfig data)
        {
            int result = -1;
            if (m_ChessPlayerConfig_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_ChessPlayerConfig_Data_Dict.TryGetValue(data, out result))
            {
                result = m_ChessPlayerConfig_List.Count;
                m_ChessPlayerConfig_Data_Dict.Add(data, result);
                m_ChessPlayerConfig_Ref_Dict.Add(data, result);
                m_ChessPlayerConfig_List.Add(data);
            }
            return result;
        }
        
        public ChessPlayerConfig ChessPlayerConfig_GetData(int index)
        {
            return m_ChessPlayerConfig_List[index];
        }
        
        private void ChessPlayerConfig_Write_Lists(BinaryWriter bw)
        {
            AlignTool.WriteLength(m_ChessPlayerConfig_List.Count, bw);
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (var data in m_ChessPlayerConfig_List)
            {
                WriteUniqueInfo_Config.ChessPlayerConfig_Write(data, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class ChessPlayerConfig_Compare : IEqualityComparer<ChessPlayerConfig>
        {
            public bool Equals(ChessPlayerConfig x, ChessPlayerConfig y)
            {
                bool result = true;
                EqualUniqueInfo_Config.ChessPlayerConfig_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(ChessPlayerConfig data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.ChessPlayerConfig_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion


        #region UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>
        
        private List<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>> m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_List = new List<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>>();
        private Dictionary<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>, int> m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data_Dict = new Dictionary<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>, int>(new UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Compare());
        private Dictionary<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>, int> m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Ref_Dict = new Dictionary<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>, int>();
        
        public int UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_GetIndex(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> data)
        {
            int result = -1;
            if (m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Ref_Dict.TryGetValue(data, out result))
            {
                return result;
            }
            
            if (!m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data_Dict.TryGetValue(data, out result))
            {
                result = m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_List.Count;
                m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Data_Dict.Add(data, result);
                m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Ref_Dict.Add(data, result);
                m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_List.Add(data);
            }
            return result;
        }
        
        public UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_GetData(int index)
        {
            return m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_List[index];
        }
        
        private void UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Write_Lists(BinaryWriter bw)
        {
            m_posOffset.Clear();
        
            foreach (var data in m_UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_List)
            {
                m_posOffset.Add((uint)bw.BaseStream.Position);
                WriteUniqueInfo_Config.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Write(data, bw);
                AlignTool.Align(bw);
            }
        
            AlignTool.WriteLength(m_posOffset.Count, bw);
        
            m_dataPoolOffset.Add((uint)bw.BaseStream.Position);
            foreach (uint offset in m_posOffset)
            {
                TransferBaseField_Write.WriteRaw(offset, bw);
            }
            AlignTool.Align(bw);
        }
        
        private class UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Compare : IEqualityComparer<UniqueInfo.ConfigHashMap<int, ChessPlayerConfig>>
        {
            public bool Equals(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> x, UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> y)
            {
                bool result = true;
                EqualUniqueInfo_Config.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Equal(x, y, ref result);
                return result;
            }
            
            public int GetHashCode(UniqueInfo.ConfigHashMap<int, ChessPlayerConfig> data)
            {
                int hashCode = 0;
                HashUniqueInfo_Config.UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Hash(data, ref hashCode);
                return hashCode;
            }
        }
        
        #endregion

        
        public void GeneratePoolData(BinaryWriter bw)
        {
            m_startOffset = bw.BaseStream.Position;
            TransferBaseField_Write.WriteRaw(0, bw);
            AlignTool.Align(bw);
            
            // Generate Pool
            UnityEngine_AnimationCurve_Write_Lists(bw);
            System_String_Write_Lists(bw);
            System_Collections_Generic_List_1_Begin_System_Collections_Generic_KeyValuePair_2_Begin_System_String_System_Int32_End_End_Write_Lists(bw);
            ChessPlayerAnimConfig_Write_Lists(bw);
            TKFrame_TKDictionary_2_Begin_System_String_ChessPlayerAnimConfig_End_Write_Lists(bw);
            System_Single_2_Array_Write_Lists(bw);
            AnimSheetConfig_Write_Lists(bw);
            System_Boolean_1_Array_Write_Lists(bw);
            AnimSpringManagerConfig_Write_Lists(bw);
            TKFrame_TKDictionary_2_Begin_System_Int32_System_Int32_End_Write_Lists(bw);
            System_Collections_Generic_HashSet_1_Begin_System_String_End_Write_Lists(bw);
            ChessPlayerConfig_Write_Lists(bw);
            UniqueInfo_ConfigHashMap_2_Begin_System_Int32_ChessPlayerConfig_End_Write_Lists(bw);
            WritePoolOffset(bw);
        }

        private void WritePoolOffset(BinaryWriter bw)
        {
            uint poolOffsetDataPos = (uint)bw.BaseStream.Position;
            TransferBaseField_Write.Write(m_dataPoolOffset.Count, bw);
            foreach (uint poolOffset in m_dataPoolOffset)
            {
                TransferBaseField_Write.Write(poolOffset, bw);
            }
            bw.BaseStream.Position = m_startOffset;
            TransferBaseField_Write.WriteRaw(poolOffsetDataPos, bw);
        }

        public static void Clear()
        {
            m_instance = null;
        }
    }
}
#endif
