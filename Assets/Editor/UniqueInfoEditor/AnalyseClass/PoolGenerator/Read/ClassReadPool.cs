namespace UniqueInfo.Editor
{
    public class ClassReadPool : ClassBasePoolFile
    {
        protected override string GetPoolTemplateFile(TransferPoolType transferPoolType)
        {
            if (transferPoolType == TransferPoolType.Normal)
                return TemplateDir + "/Read/ClassReadPoolTemplate.txt";
            else
                return TemplateDir + "/Read/ConfigPool/ClassReadConfigPoolTemplate.txt";
        }

        protected override string GetGeneratePath(TransferPoolType transferPoolType)
        {
            if(transferPoolType == TransferPoolType.Normal)
                return CodeDir + "/ReadUniqueInfo_Pool.cs";
            else
                return CodeDir + "/ReadUniqueInfo_ConfigPool.cs";
        }

        protected override TransferType GetTransferType()
        {
            return TransferType.Read;
        }
    }
}


