#region {1} Data

private {2} m_{0}_data;

private void Read_{0}_Data(FastBinaryReader br)
{
    int count = 0;
    TransferBaseField_Read.Read(ref count, br);
    m_{0}_data = new {3};
            
    for (int i = 0; i < count; i++)
    {
        {1} data = null;
        ReadUniqueInfo_Normal.{0}_RefRead(ref data, br);
        m_{0}_data[i] = data;
    }
}

public {2} Get_{0}_Data()
{
    return m_{0}_data;
}
        
#endregion
