using System.Collections.Generic;
using System.IO;
using TKFrame;

namespace UniqueInfo.Editor.{0}
{
    public class WriteUniqueInfo_NormalPool
    {
        private static object m_locker = new object();
        private static WriteUniqueInfo_NormalPool m_instance = null;

        public static WriteUniqueInfo_NormalPool instance
        {
            get
            {
                if (m_instance == null)
                {
                    lock (m_locker)
                    {
                        if (m_instance == null)
                        {
                            m_instance = new WriteUniqueInfo_NormalPool();
                        }
                    }
                }

                return m_instance;
            }
        }

        // Class Pool
        
        public void GeneratePoolData(BinaryWriter bw)
        {
            // Generate Pool
        }

        public static void Clear()
        {
            m_instance = null;
        }
    }
}