#region {1}

private List<{1}> m_{0}_List = new List<{1}>();
private Dictionary<{1}, int> m_{0}_Data_Dict = new Dictionary<{1}, int>(new {0}_Compare());
private Dictionary<{1}, int> m_{0}_Ref_Dict = new Dictionary<{1}, int>();

public int {0}_GetIndex({1} data)
{
    int result = -1;
    if (m_{0}_Ref_Dict.TryGetValue(data, out result))
    {
        return result;
    }
    
    if (!m_{0}_Data_Dict.TryGetValue(data, out result))
    {
        result = m_{0}_List.Count;
        m_{0}_Data_Dict.Add(data, result);
        m_{0}_List.Add(data);
    }

    m_{0}_Ref_Dict.Add(data, result);

    return result;
}

public {1} {0}_GetData(int index)
{
    return m_{0}_List[index];
}

private void {0}_Write_Lists(BinaryWriter bw)
{
    TransferBaseField_Write.Write(m_{0}_List.Count, bw);
    foreach (var data in m_{0}_List)
    {
        /*-----content-----*/
    }
}

private class {0}_Compare : IEqualityComparer<{1}>
{
    public bool Equals({1} x, {1} y)
    {
        bool result = true;
        EqualUniqueInfo_Normal.{0}_Equal(x, y, ref result);
        return result;
    }
    
    public int GetHashCode({1} data)
    {
        int hashCode = 0;
        HashUniqueInfo_Normal.{0}_Hash(data, ref hashCode);
        return hashCode;
    }
}

#endregion