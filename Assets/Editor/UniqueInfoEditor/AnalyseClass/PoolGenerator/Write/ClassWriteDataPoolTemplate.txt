#region {1} Data

private List<{1}> m_{0}_Data = new List<{1}>();

public void Collect_{0}_Data({1} data)
{
    CollectUniqueInfo_Normal.{0}_RefCollect(ref data);
    m_{0}_Data.Add(data);
}

private void Write_{0}_Data(BinaryWriter bw)
{
    TransferBaseField_Write.Write(m_{0}_Data.Count, bw);
    foreach (var data in m_{0}_Data)
    {
        WriteUniqueInfo_Normal.{0}_RefWrite(data, bw);
    }
}
        
#endregion