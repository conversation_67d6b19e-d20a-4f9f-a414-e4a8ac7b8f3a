using System;
using System.Text;

namespace UniqueInfo.Editor
{
    public class GenericClassGeneratorConfig : ClassGeneratorConfig
    {
        public override bool isCorrect(Type type)
        {
            return type.IsGenericType && base.isCorrect(type);
        }
        
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.GenericClass;
        }
        
        #region Name

        protected override string GetFullNameImpl(Type type)
        {
            Type genericType = type.GetGenericTypeDefinition();
            string fullName = genericType.FullName.Replace('.', '_');
            fullName = HandleNestedFullName(fullName, type);
            int startIndex = fullName.IndexOf('`');

            StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
            sb.Append(fullName.Substring(0, startIndex));

            Type[] argTypes = type.GetGenericArguments();
            sb.Append('_');
            sb.Append(argTypes.Length);
            sb.Append("_Begin_");
            
            for (int i = 0; i < argTypes.Length; i++)
            {
                string className = ClassInfoUtil.GetFullName(argTypes[i]);
                sb.Append(className);
                sb.Append('_');
            }

            sb.Append("End");
            string result = sb.ToString();
            MicroObjectPool<StringBuilder>.Release(sb);
            return result;
        }
        
        protected override string GetClassNameImpl(Type type)
        {
            Type genericType = type.GetGenericTypeDefinition();
            string fullName = genericType.FullName;
            fullName = HandleNestedClassName(fullName, type);
            int startIndex = fullName.IndexOf('`');

            StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
            sb.Append(fullName.Substring(0, startIndex));
            sb.Append('<');
            
            Type[] argTypes = type.GetGenericArguments();
            int last = argTypes.Length - 1;
            for (int i = 0; i < last; i++)
            {
                string className = ClassInfoUtil.GetClassName(argTypes[i]);
                sb.Append(className);
                sb.Append(',');
                sb.Append(' ');
            }
            sb.Append(ClassInfoUtil.GetClassName(argTypes[last]));

            sb.Append('>');
            string result = sb.ToString();
            MicroObjectPool<StringBuilder>.Release(sb);
            return result;
        }
        
        #endregion
    }
}
