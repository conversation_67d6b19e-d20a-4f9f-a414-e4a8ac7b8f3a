using System;

namespace UniqueInfo.Editor
{
    public class PrimitiveGeneratorConfig : BaseGeneratorConfig
    {
        public override bool isCorrect(Type type)
        {
            return type.IsPrimitive;
        }
        
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.Primitive;
        }
        
        #region Name
        
        protected override string GetFullNameImpl(Type type)
        {
            return type.FullName.Replace('.', '_');
        }
        
        protected override string GetClassNameImpl(Type type)
        {
            if (type == typeof(bool))
                return "bool";
            if (type == typeof(byte))
                return "byte";
            if (type == typeof(sbyte))
                return "sbyte";
            if (type == typeof(short))
                return "short";
            if (type == typeof(ushort))
                return "ushort";
            if (type == typeof(int))
                return "int";
            if (type == typeof(uint))
                return "uint";
            if (type == typeof(long))
                return "long";
            if (type == typeof(ulong))
                return "ulong";
            if (type == typeof(float))
                return "float";
            if (type == typeof(double))
                return "double";
            if (type == typeof(string))
                return "string";
            return string.Empty;
        }
        
        #endregion

        #region Ref

        public override string GetRefCallFormat(GeneratorTransferType configType)
        {
            string result = string.Empty;
            
            switch (configType.transferType)
            {
                case TransferType.Equal:
                    result = "TransferBaseField_Equal.Equal(x{0}, y{0}, ref result);";
                    break;
                case TransferType.Hash:
                    result = "TransferBaseField_Hash.Hash(data{0}, ref hashCode);";
                    break;
            }

            if (string.IsNullOrEmpty(result))
            {
                if (configType.transferPoolType == TransferPoolType.Normal)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Write:
                            result = "TransferBaseField_Write.Write(data{0}, bw);";
                            break;
                        case TransferType.Read:
                            result = "TransferBaseField_Read.Read(ref data{0}, br);";
                            break;
                    }
                }
                else if (configType.transferPoolType == TransferPoolType.Config)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Write:
                            result = "TransferBaseField_Write.WriteRaw(data{0}, bw);";
                            break;
                        case TransferType.Read:
                            result = "TransferBaseField_Read.ReadRaw(ref data{0}, br);";
                            break;
                    }
                }
            }
            
            return result;
        }
        
        #endregion
        
        #region Size
        
        public override int CalculateRefSize(TypeNode typeNode)
        {
            return CalculateContentSize(typeNode);
        }
        
        public override int CalculateContentSize(TypeNode typeNode)
        {
            int result = 0;
            Type type = typeNode.type;
            TypeCode typeCode = Type.GetTypeCode(type);
            switch (typeCode)
            {
                case TypeCode.Boolean:
                case TypeCode.Byte:
                case TypeCode.SByte:
                    result = 1;
                    break;
                case TypeCode.Char:
                case TypeCode.Int16:
                case TypeCode.UInt16:
                    result = 2;
                    break;
                case TypeCode.Int32:
                case TypeCode.UInt32:
                case TypeCode.Single:
                    result = 4;
                    break;
                case TypeCode.Int64:
                case TypeCode.UInt64:
                case TypeCode.Double:
                    result = 8;
                    break;
            }

            return result;
        }
        
        #endregion
    }
}