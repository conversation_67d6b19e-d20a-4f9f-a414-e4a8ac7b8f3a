using System.IO;

namespace UniqueInfo.Editor
{
    public class CommonBuildInClassGenerator : BuildInClassGenerator
    {
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.CommonBuildInClass;
        }
        
        #region Class

        protected override void WriteClassFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            WriteFuncType(sw, "Class Func");
            
            TypeGeneratorUtil.ReplaceData(sw, splitContent, m_typeNode);

            sw.WriteLine();
        }

        #endregion

        public static BaseGenerator Begin()
        {
            return MicroIClearPool<CommonBuildInClassGenerator>.Get();
        }

        public override void End()
        {
            MicroIClearPool<CommonBuildInClassGenerator>.Release(this);
        }
    }
}