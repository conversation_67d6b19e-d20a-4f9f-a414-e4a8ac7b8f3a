using System;

namespace UniqueInfo.Editor
{
    public class StructGeneratorConfig : BaseGeneratorConfig
    {
        public override bool isCorrect(Type type)
        {
            return !type.IsPrimitive && !type.IsEnum && type.IsValueType;
        }

        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.Struct;
        }
        
        #region Init
        
        protected override void InitTemplateFileTemplate(GeneratorTransferType configType)
        {
            InitClassFunctionTemplate(configType);
            InitRefFunctionTemplate(configType);
        }
        
        #endregion
        
        #region Name

        protected override string GetFullNameImpl(Type type)
        {
            return type.FullName.Replace('.', '_');
        }

        protected override string GetClassNameImpl(Type type)
        {
            return type.FullName;
        }
        
        public override string GetPoolClassName(TypeNode typeNode)
        {
            string className = typeNode.className;
            return className + "[]";
        }
        
        #endregion

        #region Class
        
        public override string GetClassCallFormat(GeneratorTransferType configType)
        {
            string result = GetInstanceName(configType);
            switch (configType.transferType)
            {
                case TransferType.Equal:
                    result += ".{0}_Equal(x, y, ref result);";
                    break;
                case TransferType.Hash:
                    result += ".{0}_Hash(data, ref hashCode);";
                    break;
                case TransferType.Write:
                    result += ".{0}_Write(data, bw);";
                    break;
                case TransferType.Read:
                    result += ".{0}_Read(ref data, br);";
                    break;
                case TransferType.Collect:
                    result += ".{0}_Collect(ref data);";
                    break;
            }

            return result;
        }
        
        protected override string GetClassFunctionTemplateFile(GeneratorTransferType configType)
        {
            string result = string.Empty;
            string dir = TemplateFileDir() + "/Struct/ClassTemplate/";
            switch (configType.transferType)
            {
                case TransferType.Equal:
                    result = dir + "StructEqualClassTemplate.txt";
                    break;
                case TransferType.Hash:
                    result = dir + "StructHashClassTemplate.txt";
                    break;
                case TransferType.Write:
                    result = dir + "StructWriteClassTemplate.txt";
                    break;
                case TransferType.Collect:
                    result = dir + "StructCollectClassTemplate.txt";
                    break;
            }
            
            if (string.IsNullOrEmpty(result))
            {
                if (configType.transferPoolType == TransferPoolType.Normal)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Read:
                            result = dir + "StructReadClassTemplate.txt";
                            break;
                    }
                }
                else if (configType.transferPoolType == TransferPoolType.Config)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Read:
                            result = dir + "StructReadConfigClassTemplate.txt";
                            break;
                    }
                }
            }

            return result;
        }

        #endregion
        
        #region Ref

        public override string GetRefCallFormat(GeneratorTransferType configType)
        {
            string result = GetInstanceName(configType);
            switch (configType.transferType)
            {
                case TransferType.Equal:
                    result += ".{0}_RefEqual(x{1}, y{1}, ref result);";
                    break;
                case TransferType.Hash:
                    result += ".{0}_RefHash(data{1}, ref hashCode);";
                    break;
                case TransferType.Write:
                    result += ".{0}_RefWrite(data{1}, bw);";
                    break;
                case TransferType.Read:
                    result += ".{0}_RefRead(ref data{1}, br);";
                    break;
                case TransferType.Collect:
                    result += ".{0}_RefCollect(ref data{1});";
                    break;
            }

            return result;
        }
        
        protected override string GetRefFunctionTemplateFile(GeneratorTransferType configType)
        {
            string result = string.Empty;
            string dir = TemplateFileDir() + "/Struct/RefTemplate/";
            switch (configType.transferType)
            {
                case TransferType.Equal:
                    result = dir + "StructEqualRefTemplate.txt";
                    break;
                case TransferType.Hash:
                    result = dir + "StructHashRefTemplate.txt";
                    break;
                case TransferType.Write:
                    result = dir + "StructWriteRefTemplate.txt";
                    break;
                case TransferType.Collect:
                    result = dir + "StructCollectRefTemplate.txt";
                    break;
            }

            if (string.IsNullOrEmpty(result))
            {
                if (configType.transferPoolType == TransferPoolType.Normal)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Read:
                            result = dir + "StructReadRefTemplate.txt";
                            break;
                    }
                }
                else if (configType.transferPoolType == TransferPoolType.Config)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Read:
                            result = dir + "StructReadConfigRefTemplate.txt";
                            break;
                    }
                }
            }

            return result;
        }

        #endregion
        
        #region Size
        
        public override int CalculateRefSize(TypeNode typeNode)
        {
            return CalculateContentSize(typeNode);
        }

        public override int CalculateContentSize(TypeNode typeNode)
        {
            int size = 0;
            foreach (FieldNode fieldNode in typeNode.fields)
            {
                size += fieldNode.typeNode.GetRefSize();
            }
            return size;
        }
        
        #endregion
    }
}
