using System;
using System.Collections.Generic;
using System.IO;

namespace UniqueInfo.Editor
{
    public class StructGenerator : BaseGenerator
    {
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.Struct;
        }

        #region Class

        protected override void WriteClassFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            foreach (FieldNode fieldNode in m_typeNode.fields)
            {
                TypeNode fieldTypeNode = fieldNode.typeNode;
                
                BaseGenerator generator = GeneratorManager.ins.Begin(fieldTypeNode, m_transferType);
                generator.WriteClassFunction(sw);
                generator.WriteRefFunction(sw);
                GeneratorManager.ins.End(generator);
            }

            WriteOneClass(sw, splitContent, sb);
        }

        public override void WriteClassCall(TextWriter sw)
        {
            sw.WriteLine(m_config.GetClassCallFormat(m_transferType), m_typeNode.fullName);
        }
        
        protected override HashSet<TypeNode> GetSerializedClassSet()
        {
            return m_config.SerializedClass;
        }
        
        protected virtual void WriteOneClass(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            WriteClassField(sb);
            ReplaceContent(splitContent, m_contentMark, sb);

            WriteClass(sw, splitContent);
        }

        private void WriteClassField(TextWriter sw)
        {
            if (m_typeNode.fields.Count == 0)
                return;
            
            sw.WriteLine("// Field");
            
            foreach (FieldNode fieldNode in m_typeNode.fields)
            {
                TypeNode fieldTypeNode = fieldNode.typeNode;
                BaseGenerator generator = GeneratorManager.ins.Begin(fieldTypeNode, m_transferType);
                generator.WriteRefCall(sw, "." + fieldNode.fieldName);
                GeneratorManager.ins.End(generator);
            }
        }
        
        private void WriteClass(TextWriter sw, SplitContent splitContent)
        {
            WriteFuncType(sw, "Class Func");
            TypeGeneratorUtil.ReplaceData(sw, splitContent, m_typeNode);
            sw.WriteLine();
        }

        #endregion
        
        #region Ref

        protected override void WriteRefFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            WriteFuncType(sw, "Ref Func");
            TypeGeneratorUtil.ReplaceDataForRef(sw, splitContent, m_typeNode, m_transferType.transferPoolType);
            sw.WriteLine();
        }

        public override void WriteRefCall(TextWriter sw, string fieldName)
        {
            sw.WriteLine(m_config.GetRefCallFormat(m_transferType), m_typeNode.fullName, fieldName);
        }
        
        protected override HashSet<TypeNode> GetSerializedRefSet()
        {
            return m_config.SerializedRef;
        }
        
        #endregion
        
        #region Pool
        
        protected override void WriteClassPoolFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
            WriteFieldPool(sw);
        }
        
        private void WriteFieldPool(TextWriter sw)
        {
            foreach (FieldNode fieldNode in m_typeNode.fields)
            {
                BaseGenerator generator = GeneratorManager.ins.Begin(fieldNode.typeNode, m_transferType);
                generator.WriteClassPoolFunction(sw);
                GeneratorManager.ins.End(generator);
            }
        }
        
        protected override HashSet<TypeNode> GetSerializedPoolSet()
        {
            return m_config.SerializedPool;
        }
        
        protected override void WritePoolCallImpl(TextWriter sw)
        {
            WriteFieldPoolCall(sw);
        }
        
        private void WriteFieldPoolCall(TextWriter sw)
        {
            foreach (FieldNode fieldNode in m_typeNode.fields)
            {
                BaseGenerator generator = GeneratorManager.ins.Begin(fieldNode.typeNode, m_transferType);
                generator.WritePoolCall(sw);
                GeneratorManager.ins.End(generator);
            }
        }
        
        protected override HashSet<TypeNode> GetPoolCallSet()
        {
            return m_config.SerializedCallPool;
        }
        
        #endregion
        
        #region Data

        protected override void WriteDataPoolFunctionImpl(TextWriter sw, SplitContent splitContent)
        {
        }
        
        #endregion
        
        #region Data Call

        protected override void WriteDataPoolCallImpl(TextWriter sw)
        {
        }
        
        #endregion

        public static BaseGenerator Begin()
        {
            return MicroIClearPool<StructGenerator>.Get();
        }

        public override void End()
        {
            MicroIClearPool<StructGenerator>.Release(this);
        }
    }
}