using System;
using System.Collections.Generic;

namespace UniqueInfo.Editor
{
    public class BuildInStructGeneratorConfig  : StructGeneratorConfig
    {
        private HashSet<Type> m_buildInStruct = new HashSet<Type>()
        {
            typeof(UnityEngine.Vector2),   typeof(UnityEngine.Vector2Int), 
            typeof(UnityEngine.Vector3),   typeof(UnityEngine.Vector3Int), 
            typeof(UnityEngine.Vector4),   typeof(UnityEngine.Quaternion),
            typeof(UnityEngine.Color),     typeof(UnityEngine.Color32), 
            typeof(UnityEngine.Bounds),    typeof(UnityEngine.BoundsInt), 
            typeof(UnityEngine.Rect),      typeof(UnityEngine.RectInt), 
            typeof(UnityEngine.LayerMask), typeof(UnityEngine.Keyframe),
            
            typeof(UnityEngine4Server.Vector3), typeof(UnityEngine4Server.Quaternion),
        };
        
        public override bool isCorrect(Type type)
        {
            if (base.isCorrect(type))
            {
                if (m_buildInStruct.Contains(type))
                    return true;
            }
            return false;
        }
        
        
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.BuildInStruct;
        }
        
        #region Init

        protected override void InitTemplateFileTemplate(GeneratorTransferType configType)
        {
        }
        
        #endregion
        
        #region Class

        public override string GetClassCallFormat(GeneratorTransferType configType)
        {
            return string.Empty;
        }
        
        #endregion
        
        #region Ref

        public override string GetRefCallFormat(GeneratorTransferType configType)
        {
            string result = string.Empty;

            switch (configType.transferType)
            {
                case TransferType.Equal:
                    result = "TransferStructField_Equal.Equal(x{0}, y{0}, ref result);";
                    break;
                case TransferType.Hash:
                    result = "TransferStructField_Hash.Hash(data{0}, ref hashCode);";
                    break;
            }

            if (string.IsNullOrEmpty(result))
            {
                if (configType.transferPoolType == TransferPoolType.Normal)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Write:
                            result = "TransferStructField_Write.Write(data{0}, bw);";
                            break;
                        case TransferType.Read:
                            result = "TransferStructField_Read.Read(ref data{0}, br);";
                            break;
                    }
                }
                else if (configType.transferPoolType == TransferPoolType.Config)
                {
                    switch (configType.transferType)
                    {
                        case TransferType.Write:
                            result = "TransferStructField_Write.WriteRaw(data{0}, bw);";
                            break;
                        case TransferType.Read:
                            result = "TransferStructField_Read.ReadRaw(ref data{0}, br);";
                            break;
                    }
                }
            }
            
            return result;
        }

        #endregion
        
        #region Size

        public override int CalculateContentSize(TypeNode typeNode)
        {
            Type type = typeNode.type;
            if (type == typeof(UnityEngine.Color32) 
                || type == typeof(UnityEngine.LayerMask))
            {
                return 4;
            }
            if (type == typeof(UnityEngine.Vector2) 
                || type == typeof(UnityEngine.Vector2Int))
            {
                return 8;
            }
            if (type == typeof(UnityEngine.Vector3) 
                || type == typeof(UnityEngine.Vector3Int) 
                ||  type == typeof(UnityEngine4Server.Vector3) )
            {
                return 12;
            }
            if (type == typeof(UnityEngine4Server.Quaternion) 
                || type == typeof(UnityEngine.Vector4) 
                || type == typeof(UnityEngine.Color) 
                || type == typeof(UnityEngine.Rect) 
                || type == typeof(UnityEngine.RectInt) 
                ||  type == typeof(UnityEngine.Quaternion) )
            {
                return 16;
            }
            if (type == typeof(UnityEngine.Bounds) 
                || type == typeof(UnityEngine.BoundsInt))
            {
                return 24;
            }

            if (type == typeof(UnityEngine.Keyframe))
            {
                return 32;
            }

            return 0;
        }
        
        #endregion
    }
}


