using System.Collections.Generic;
using System.IO;

namespace UniqueInfo.Editor
{
    public class BuildInStructGenerator : BaseGenerator
    {
        public override TypeGenerator GetTypeGenerator()
        {
            return TypeGenerator.BuildInStruct;
        }
        
        #region Content

        public override void WriteClassCall(TextWriter sw)
        {
        }

        protected override void WriteClassFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
        }

        #endregion
        
        #region Ref
        
        public override void WriteRefCall(TextWriter sw, string fieldName)
        {
            sw.WriteLine(m_config.GetRefCallFormat(m_transferType), fieldName);
        }
        
        protected override void WriteRefFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
        }
        
        protected override HashSet<TypeNode> GetSerializedRefSet()
        {
            return m_config.SerializedRef;
        }

        #endregion
        
        #region Pool
        
        protected override void WriteClassPoolFunctionImpl(TextWriter sw, SplitContent splitContent, StringTextWriter sb)
        {
        }
        
        #endregion
        
        #region Pool Call
        
        protected override void WritePoolCallImpl(TextWriter sw)
        {
        }

        #endregion
        
        #region Data

        protected override void WriteDataPoolFunctionImpl(TextWriter sw, SplitContent splitContent)
        {
        }
        
        #endregion
        
        #region Data Call

        protected override void WriteDataPoolCallImpl(TextWriter sw)
        {
        }
        
        #endregion
        
        public static BaseGenerator Begin()
        {
            return MicroIClearPool<BuildInStructGenerator>.Get();
        }

        public override void End()
        {
            MicroIClearPool<BuildInStructGenerator>.Release(this);
        }
    }
}