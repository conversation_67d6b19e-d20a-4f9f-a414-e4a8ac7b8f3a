using System;
using System.Reflection;

namespace UniqueInfo.Editor
{
    public class JCEClassInfo : TypeNode
    {
        private static Type JCEParentType = typeof(Wup.Jce.JceStruct);
        
        public JCEClassInfo(Type type) : base(type)
        {
        }
        
        protected override ClassType GetClassType()
        {
            return ClassType.JCE;
        }

        protected override void GatherField()
        {
            FieldInfo[] publicFieldInfos = m_type.GetFields(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly);
            foreach (FieldInfo fieldInfo in publicFieldInfos)
            {
                FieldNode fieldNode = new FieldNode(fieldInfo, GetClassType());
                m_fields.Add(fieldNode);
            }
        }

        protected override void GatherParentImpl()
        {
            Type parentType = m_type.BaseType;
            if (parentType != JCEParentType && parentType != ClassInfo.ObjectClassType)
            {
                m_parentNode = AllClassInfoManager.instance.CurrentInfo.GetNode(parentType, GetClassType());
            }
        }
        
        protected override void GatherChildClass()
        {
            
        }
    }
}


