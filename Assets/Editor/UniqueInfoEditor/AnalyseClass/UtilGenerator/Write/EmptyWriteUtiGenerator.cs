using System.IO;

namespace UniqueInfo.Editor
{
    public class EmptyWriteUtilGenerator : BaseUtilGenerator
    {
        protected override string GetContentTemplateFile()
        {
            return GetTemplateDir() + "Write/EmptyWriteUtilContentTemplate.txt";
        }
        
        protected override string GetAssetContentTemplateFile()
        {
            return GetTemplateDir() + "Write/EmptyWriteUtilAssetContentTemplate.txt";
        }

        protected override string GetConfigContentTemplateFile()
        {
            return GetTemplateDir() + "Write/EmptyWriteUtilConfigContentTemplate.txt";
        }

        protected override string GetTemplateFile()
        {
            return GetTemplateDir() + "Write/WriteUtilTemplate.txt";
        }

        protected override string GetGeneratePath()
        {
            return EditorDir + "/UniqueInfoWriteUtil.cs";
        }
        
        protected override void Init()
        {
            base.Init();
            string[] editorDirFiles = Directory.GetFiles(EditorDir, "*");
            foreach (string file in editorDirFiles)
            {
                File.Delete(file);
            }
        }
    }
}