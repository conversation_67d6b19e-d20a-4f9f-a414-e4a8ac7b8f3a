public static void GenerateFile(string path, List<{1}> dataLists)
{
    DateTime start = DateTime.Now;

    if(File.Exists(path))
        File.Delete(path);

    AssetsPool assetPool = ScriptableObject.CreateInstance<AssetsPool>();
    {2}.WriteUniqueInfo_NormalPool.instance.SetAssetPool(assetPool);

    foreach (var data in dataLists)
    {
        {2}.WriteUniqueInfo_NormalPool.instance.Collect_{0}_Data(data);
    }

    using (MemoryStream ms = new MemoryStream())
    {
        using (BinaryWriter bw = new BinaryWriter(ms))
        {
            {2}.WriteUniqueInfo_NormalPool.instance.GeneratePoolData(bw);
        }
        assetPool.data = ms.GetBuffer();
    }
    {2}.WriteUniqueInfo_NormalPool.Clear();

    AssetDatabase.CreateAsset(assetPool, path);

    DateTime end = DateTime.Now;
    TimeSpan delta = end - start;
    Diagnostic.Log("UniqueInfoTool Write {1} Finish " + delta.TotalSeconds + "s");

    AssetDatabase.Refresh();
}