public static {3} Read_{0}(AssetsPool assetPool)
{
    DateTime start = DateTime.Now;

    {3} result = null;
    using (MemoryStream ms = new MemoryStream(assetPool.data))
    {
        result = Read_{0}(ms, assetPool);
    }

    DateTime end = DateTime.Now;
    TimeSpan delta = end - start;
    Diagnostic.Log("UniqueInfoTool Read {1} Finish " + delta.TotalSeconds + "s");

    return result;
}
        
private static {3} Read_{0}(Stream stream, AssetsPool assetPool)
{
    using (FastBinaryReader br = new FastBinaryReader(stream))
    {
        {2}.ReadUniqueInfo_NormalPool.instance.SetAssetPool(assetPool);
        {2}.ReadUniqueInfo_NormalPool.instance.GeneratePoolData(br);
    }
            
    {3} result = {2}.ReadUniqueInfo_NormalPool.instance.Get_{0}_Data();
    {2}.ReadUniqueInfo_NormalPool.Clear();
    return result;
}