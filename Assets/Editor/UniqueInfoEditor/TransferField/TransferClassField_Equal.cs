namespace UniqueInfo.Editor
{
    public class TransferClassField_Equal
    {
        public static void Equal(string x, string y, ref bool result)
        {
            if (result)
                result = x == y;
        }
        
#if ACGGAME_CLIENT
        
        public static void Equal(UnityEngine.AnimationCurve x, UnityEngine.AnimationCurve y, ref bool result)
        {
            TransferBaseField_Equal.Equal(x.length, y.length, ref result);
            if (!result)
                return;
            
            UnityEngine.Keyframe[] xKeys = x.keys;
            UnityEngine.Keyframe[] yKeys = y.keys;
            for (int i = 0; i < xKeys.Length; i++)
            {
                TransferStructField_Equal.Equal(xKeys[i], yKeys[i], ref result);
                if (!result)
                    return;
            }
            
            TransferBaseField_Equal.Equal((int) x.postWrapMode, (int) y.postWrapMode, ref result);
            if (!result)
                return;
            
            TransferBaseField_Equal.Equal((int) x.preWrapMode, (int) y.preWrapMode, ref result);
        }
        
        public static void Equal(UnityEngine.Object x, UnityEngine.Object y, ref bool result)
        {
            if (result)
            {
                result = x.Equals(y);
            }
        }
        
#endif
        
    }
}


