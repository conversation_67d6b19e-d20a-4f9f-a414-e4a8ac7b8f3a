namespace UniqueInfo.Editor
{
    public class TransferStructField_Hash
    {
        public static void Hash(UnityEngine4Server.Vector3 data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine4Server.Quaternion data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
#if ACGGAME_CLIENT

        public static void Hash(UnityEngine.Vector2 data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Vector2Int data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Vector3 data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Vector3Int data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Vector4 data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Quaternion data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Color data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Color32 data, ref int hashCode)
        {
            TransferBaseField_Hash.Hash(data.r, ref hashCode);
            TransferBaseField_Hash.Hash(data.g, ref hashCode);
            TransferBaseField_Hash.Hash(data.b, ref hashCode);
            TransferBaseField_Hash.Hash(data.a, ref hashCode);
        }
        
        public static void Hash(UnityEngine.Bounds data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.BoundsInt data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.Rect data, ref int hashCode)
        {
            hashCode ^= data.GetHashCode();
        }
        
        public static void Hash(UnityEngine.RectInt data, ref int hashCode)
        {
            TransferBaseField_Hash.Hash(data.x, ref hashCode);
            TransferBaseField_Hash.Hash(data.y, ref hashCode);
            TransferBaseField_Hash.Hash(data.width, ref hashCode);
            TransferBaseField_Hash.Hash(data.height, ref hashCode);
        }

        public static void Hash(UnityEngine.LayerMask data, ref int hashCode)
        {
            TransferBaseField_Hash.Hash(data.value, ref hashCode);
        }
        
        public static void Hash(UnityEngine.Keyframe data, ref int hashCode)
        {
            TransferBaseField_Hash.Hash(data.time, ref hashCode);
            TransferBaseField_Hash.Hash(data.value, ref hashCode);
            TransferBaseField_Hash.Hash(data.inTangent, ref hashCode);
            TransferBaseField_Hash.Hash(data.outTangent, ref hashCode);
            TransferBaseField_Hash.Hash((int) data.weightedMode, ref hashCode);
            TransferBaseField_Hash.Hash(data.inWeight, ref hashCode);
            TransferBaseField_Hash.Hash(data.outWeight, ref hashCode);
            TransferBaseField_Hash.Hash(data.tangentMode, ref hashCode);
        }
        
#endif
        
    }
}
