#if TKF_EDITOR && (TKF_ALL_EXTEND)//TKFrame Auto Gen
using System;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

public class ChangeFunctionSet {

    public static bool ChangeName(SerializedObject serializedObject,DelegateArgModel argModel)
    {
        return EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
    }
    public static bool HideProperty(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        return false;
    }

    public static bool WhetherShowPropertyNormal(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        
        //遍历找到所有的影响条件
        bool showProperty = WhetherShowProperty(serializedObject, argModel);
        if (showProperty)
        {
            //显示toogle
            showProperty = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        else
        {
            showProperty = argModel.ToogleState;
        }
        return showProperty;
    }

    public static bool WhetherShowPropertyReg(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        bool showProperty = WhetherShowProperty(serializedObject, argModel);
        if (showProperty)
        {
            //获得显示名称
            SerializedProperty serializedProperty = serializedObject.FindProperty("m_Transition");
            showProperty = EditorGUILayout.ToggleLeft(serializedProperty.displayName, argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        else
        {
            showProperty = argModel.ToogleState;
        }
        return showProperty;
    }

    private static bool WhetherShowProperty(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        bool showProperty = false;

        foreach (ToogleConditionModel toogleConditionModel in argModel.ToogleConditionModelList)
        {
            SerializedProperty useLightProperty = serializedObject.FindProperty(toogleConditionModel.PropertyName);

            bool tempShowProperty = false;
            if (toogleConditionModel.Condition.Equals("=="))
            {
                if (toogleConditionModel.ValueType.Equals("int"))
                {

                    tempShowProperty = (useLightProperty.intValue == Int32.Parse(toogleConditionModel.Value));
                }
                else if (toogleConditionModel.ValueType.Equals("bool"))
                {
                    if (toogleConditionModel.Value.Equals("false"))
                    {
                        tempShowProperty = (useLightProperty.boolValue == false);
                    }
                    else
                    {
                        tempShowProperty = useLightProperty.boolValue == true;
                    }
                }
                else if (toogleConditionModel.ValueType.Equals("object"))
                {
                    tempShowProperty = (useLightProperty.objectReferenceValue == null);
                }

            }
            else if (toogleConditionModel.Condition.Equals("!="))
            {
                if (toogleConditionModel.ValueType.Equals("int"))
                {

                    tempShowProperty = (useLightProperty.intValue != Int32.Parse(toogleConditionModel.Value));
                }
                else if (toogleConditionModel.ValueType.Equals("bool"))
                {
                    if (toogleConditionModel.Value.Equals("false"))
                    {
                        tempShowProperty = (useLightProperty.boolValue != false);
                    }
                    else
                    {
                        tempShowProperty = useLightProperty.boolValue != true;
                    }
                }
                else if (toogleConditionModel.ValueType.Equals("object"))
                {
                    tempShowProperty = (useLightProperty.objectReferenceValue != null);
                }
            }

            if (toogleConditionModel.LogicCondition == null || toogleConditionModel.LogicCondition.Equals(""))
            {
                //只有一个条件，或者是第一个条件，则设置初始条件值
                showProperty = tempShowProperty;
            }
            else
            {
                //不是第一个条件，则根据LogicCondition，逻辑处理条件
                if (toogleConditionModel.LogicCondition.Equals("||"))
                {
                    showProperty = showProperty || tempShowProperty;
                }
                else if (toogleConditionModel.LogicCondition.Equals("&&"))
                {
                    showProperty = showProperty && tempShowProperty;
                }
            }

        }
        return showProperty;
    }

    public static bool WhetherShowCanvasScalerProperty2(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        //决定当前属性是否显示
        SerializedProperty uiScaleModeProperty = serializedObject.FindProperty("m_UiScaleMode");
        bool flag = Selection.activeGameObject.GetComponent<Canvas>().renderMode == RenderMode.WorldSpace;
        if (uiScaleModeProperty.intValue == 2 && !flag)
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        return argModel.ToogleState;
    }
    public static bool WhetherShowCanvasScalerProperty3(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        //决定当前属性是否显示
        bool flag = Selection.activeGameObject.GetComponent<Canvas>().renderMode == RenderMode.WorldSpace;
        if (flag)
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        return argModel.ToogleState;
    }
    public static bool WhetherShowNavMeshObstacleProperty_Extents(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        //决定当前属性是否显示
        SerializedProperty type = serializedObject.FindProperty("m_Shape");
        if (type.intValue == 0)
        {
            if (argModel.OldName.Equals("X") || argModel.OldName.Equals("Y"))
            {
                argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState,
                    GUILayout.ExpandWidth(true));
            }
        }
        else
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.OldName, argModel.ToogleState,
                                GUILayout.ExpandWidth(true));
        }
        return argModel.ToogleState;
    }

    public static bool WhetherSelectableEditorProperty_Navigation(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        //决定当前属性是否显示
        
        if (argModel.ShowName.Equals("Navigation") || argModel.ShowName.Equals("Mode"))
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
            return argModel.ToogleState;
        }
        var inputField = Selection.activeGameObject.GetComponent<Selectable>();


        if (inputField.navigation.mode == Navigation.Mode.Explicit)
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        return argModel.ToogleState;
    }

    public static bool WhetherShowCanvasProperty_RenderCamera(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        //决定当前属性是否显示

        SerializedProperty type = serializedObject.FindProperty("m_RenderMode");
        if (type.intValue == 1)
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft("Render Camera", argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        else if (type.intValue == 2)
        {
            argModel.ToogleState = EditorGUILayout.ToggleLeft("Event Camera", argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        return argModel.ToogleState;
    }
    public static bool WhetherShowCanvasProperty_RenderSortOrder(SerializedObject serializedObject, string changeName,
        string oldName, bool toogleState)
    {
        //决定当前属性是否显示

        bool showProperty = toogleState;
        SerializedProperty type = serializedObject.FindProperty("m_RenderMode");
        if (type.intValue == 0)
        {
            showProperty = EditorGUILayout.ToggleLeft("Sort Order", toogleState, GUILayout.ExpandWidth(true));
        }
        else
        {
            showProperty = false;
        }
        return showProperty;
    }
    public static bool WhetherShowCanvasProperty_RenderSortLayer(SerializedObject serializedObject, string changeName,
        string oldName, bool toogleState)
    {
        //决定当前属性是否显示
        bool showProperty = toogleState;
        SerializedProperty type = serializedObject.FindProperty("m_RenderMode");
        if (type.intValue != 0)
        {
            showProperty = EditorGUILayout.ToggleLeft(changeName, toogleState, GUILayout.ExpandWidth(true));
        }
        else
        {
            showProperty = false;
        }
        return showProperty;
    }
    public static bool WhetherShowColliderMask(SerializedObject serializedObject, string changeName,
        string oldName, bool toogleState)
    {
        //决定当前属性是否显示
        bool showProperty = toogleState;
        SerializedProperty type = serializedObject.FindProperty("m_UseColliderMask");
        if (type.boolValue == true)
        {
            showProperty = EditorGUILayout.ToggleLeft(changeName, toogleState, GUILayout.ExpandWidth(true));
        }
        else
        {
            showProperty = false;
        }
        return showProperty;
    }

    public static bool WhetherShowCanvasScaler_ScaleFactor(SerializedObject serializedObject, DelegateArgModel argModel)
    {
        SerializedProperty serializedProperty = serializedObject.FindProperty("m_UiScaleMode");
        bool isShow = (serializedProperty.intValue == 0);
        var canvasComponent = Selection.activeGameObject.GetComponent<Canvas>();
        if (canvasComponent.renderMode != RenderMode.WorldSpace && serializedProperty.intValue == 0)
        {
            //显示
            argModel.ToogleState = EditorGUILayout.ToggleLeft(argModel.ShowName, argModel.ToogleState, GUILayout.ExpandWidth(true));
        }
        return argModel.ToogleState;
    }
}
#endif //TKFrame Auto Gen
