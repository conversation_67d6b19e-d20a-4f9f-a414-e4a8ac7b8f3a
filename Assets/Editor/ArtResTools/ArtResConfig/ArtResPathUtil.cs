using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.Profiling;

public class BundleDetailInfo
{
    public string ABPath;
    public long AssetSize;
    public List<ArtResInfo> resList;
}

public static class ArtResPathUtil
{
    public const long MinPackAssetbundleResSize = 512 * 1024;
    public const long MaxPackAssetbundleResSize = 1024 * 1024;
    public const long MaxAssetbundleResSize = 10 * 1024 * 1024;

    public static int SetAssetbundleCount = 0;
    public static int MaxSetAssetbundleCount = 1000;
    public static int cnt = 20;

    private static List<string> reimportTextureList = new List<string>();

    // 避免单次时间过长
    public static bool CanSetAssetbundle()
    {
        return !Application.isBatchMode || SetAssetbundleCount < MaxSetAssetbundleCount;
    }

    private static void SetAssetbundle(Dictionary<string, ArtAssetbundlePack> packDict)
    {
        foreach (var item in packDict)
        {
            var assetbundleName = item.Key;
            var assetList = item.Value.assetList;
            for (int i = 0; i < assetList.Count; ++i)
            {
                if (!CanSetAssetbundle())
                {
                    return;
                }

                var path = assetList[i];
                if (EditorUtility.DisplayCancelableProgressBar("自动设置ab中 " + assetbundleName, path, i / (float)assetList.Count))
                {
                    break;
                }
                AssetImporter importer = AssetImporter.GetAtPath(path);
                if (importer == null)
                {
                    Debug.LogErrorFormat("Path: {0} find faild!", path);
                    continue;
                }
                if (importer.assetBundleName != assetbundleName || importer.assetBundleVariant != "unity3d")
                {
                    Debug.LogFormat("Path: {0} old_assetbundle: {1} new_assetbundle: {2}", path, importer.assetBundleName, assetbundleName);
                    //importer.SetAssetBundleNameAndVariant(assetbundleName, "unity3d");
                    SetAssetbundleInMeta(path + ".meta", assetbundleName, "unity3d");
                    if (importer is TextureImporter)
                    {
                        //AssetDatabase.WriteImportSettingsIfDirty(path);     //触发PostProcess，使压缩回滚
                        //AssetDatabase.SaveAssets();

                        reimportTextureList.Add(path);
                        //if (ZgameLimitedTextureImportCheck.FindTextureLimitSet(path) != null)
                        //{
                        //    //由ZgameLimitedTextureImportCheck接管。
                        //    return new ZgameLimitedTextureImportCheck(textureImporter).CheckLimitedTexture();
                        //}
                    }
                    ++SetAssetbundleCount;
                }
            }
        }
    }

    public static void PreProcessABCombine(string abPreName, Dictionary<string, List<ArtResInfo>> abDict, Dictionary<string, ArtAssetbundlePack> assetDict, Dictionary<string, ArtAssetbundlePack> similarAntiDependencyCombinePackDict, bool autoSpitMesh_Texture_Anim, bool autoSpitMaterial_Prtefab)
    {
        Dictionary<string, List<ArtResInfo>> abDict2 = new Dictionary<string, List<ArtResInfo>>();
        Dictionary<string, List<long>> perfData = new Dictionary<string, List<long>>();
        foreach (var item in abDict)
        {
            var ab_name = item.Key;
            // AB如果被相同的AB依赖，在合并后大小不超过1M的情况下，则可以进行合并
            bool hasBeenCombineRefAssetbundles = false;
            long assetListSize = 0;
            foreach (var assetItem in item.Value)
            {
                assetListSize += assetItem.AssetSize;
                if (HasBeenCombineRefAssetbundles(assetItem))
                {
                    hasBeenCombineRefAssetbundles = true;
                }
            }

            if (!hasBeenCombineRefAssetbundles)
            {
                List<ArtResInfo> faildList = new List<ArtResInfo>();
                if (assetListSize < MinPackAssetbundleResSize) // 这类资源合并打包
                {
                    //if ((item.Value.Count <= cnt) && (item.Value.Count > 0))
                    {
                        abDict2.Add(ab_name, item.Value);
                        if (!perfData.TryGetValue(ab_name, out List<long> listData))
                        {
                            listData = new List<long>();
                            perfData.Add(ab_name, listData);
                        }

                        long assetSize = 0;
                        for (int i = 0; i < item.Value.Count; ++i)
                        {
                            assetSize += item.Value[i].AssetSize;
                        }

                        listData.Add(item.Value.Count);
                        listData.Add(assetSize);
                    }
                    /*BundlePathAndSize retAbName = FindSameRefAssetbundleList(res, abDict);
                    if (!string.IsNullOrEmpty(retAbName.ABPath))
                    {
                        if (res.AssetSize + retAbName.AssetSize < MaxPackAssetbundleResSize)
                            abName = retAbName.ABPath;
                    }*/
                }
            }
        }


        string newABName;
        bool beNeedClearOld = false;
        List<ArtResInfo> faildList2 = new List<ArtResInfo>();
        Dictionary<string, List<ArtResInfo>> noneCombineDict = new Dictionary<string, List<ArtResInfo>>();
        foreach (var item in abDict2)
        {
            beNeedClearOld = false;
            string oldABName = item.Key;
            newABName = oldABName;
            BundleDetailInfo retAbName = FindSameRefAssetbundleList(item.Value, abDict2);
            if (string.IsNullOrEmpty(retAbName.ABPath))
            {
                bool hasCombinedAB = false;
                foreach(var listItem in item.Value)
                {
                    //如果List<ArtResInfo>里面有资源AB路径跟主AB路径不同，则证明这个List<ArtResInfo>是跟其他AB一起合并过的
                    if (listItem.GetAssetbundleName(null, autoSpitMesh_Texture_Anim, autoSpitMaterial_Prtefab).Equals(oldABName) == false)
                    {
                        hasCombinedAB = true;
                        break;
                    }
                }
                //记录下完全没有做过AB合并的AB列表，后面做通用的commbine-AB策略
                if (!hasCombinedAB)
                {
                    if (!noneCombineDict.TryGetValue(oldABName, out List<ArtResInfo> _assetList))
                    {
                        _assetList = new List<ArtResInfo>();
                        _assetList.AddRange(item.Value);
                        noneCombineDict.Add(oldABName, _assetList);
                    }
                    else
                    {
                        _assetList.AddRange(item.Value);
                    }
                }
            }
            else
            {
                long assetSize = 0;
                for (int i = 0; i < item.Value.Count; ++i)
                {
                    assetSize += item.Value[i].AssetSize;
                }

                if (assetSize + retAbName.AssetSize < MaxPackAssetbundleResSize)
                {
                    newABName = retAbName.ABPath;
                }
            }

            if (!newABName.Equals(oldABName))
            {
                if (noneCombineDict.TryGetValue(newABName, out List<ArtResInfo> _assetList))
                {
                    noneCombineDict.Remove(newABName);
                }

                if (abDict2.TryGetValue(newABName, out List<ArtResInfo> assetList2))
                {
                    for (int j = 0; j < item.Value.Count; ++j)
                    {
                        assetList2.Add(item.Value[j]);
                    }
                }
                beNeedClearOld = true;
            }

            if (beNeedClearOld)
                item.Value.Clear();
        }

        foreach (var item in abDict2)
        {
            if (noneCombineDict.TryGetValue(item.Key, out List<ArtResInfo> _assetList) == false)
            {
                //该AB的资源已经被合并到其他AB里面去了，所以可以从abDict里面剔除
                if (item.Value.Count == 0)
                {
                    if (abDict.ContainsKey(item.Key))
                        abDict.Remove(item.Key);
                }
                else
                {
                    bool hasMaterial = false;
                    foreach (var assetItem in item.Value)
                    {
                        if (assetItem.IsMaterial())
                        {
                            hasMaterial = true;
                            break;
                        }
                    }

                    if(hasMaterial)
                    {
                        var ab_name = abPreName + "/" + item.Key;
                        if (similarAntiDependencyCombinePackDict.TryGetValue(ab_name, out ArtAssetbundlePack abPack))
                        {
                            for (int k = 0; k < item.Value.Count; ++k)
                            {
                                if (!abPack.AddAsset(item.Value[k]))
                                    faildList2.Add(item.Value[k]);
                            }
                        }
                        else
                        {
                            abPack = new ArtAssetbundlePack();
                            for (int m = 0; m < item.Value.Count; ++m)
                            {
                                if (!abPack.AddAsset(item.Value[m]))
                                    faildList2.Add(item.Value[m]);
                            }
                            similarAntiDependencyCombinePackDict.Add(ab_name, abPack);
                        }

                        foreach (var assetItem in item.Value)
                        {
                            var abName = assetItem.GetAssetbundleName(null, autoSpitMesh_Texture_Anim, autoSpitMaterial_Prtefab);
                            if (abDict.ContainsKey(abName))
                                abDict.Remove(abName);
                        }

                        // 重名文件处理 (换个ab)
                        {
                            int index = 0;
                            string subAssetbundleName = abPreName + "/" + item.Key + "_" + index;
                            while (faildList2.Count > 0)
                            {
                                if (!assetDict.TryGetValue(subAssetbundleName, out ArtAssetbundlePack pack))
                                {
                                    pack = new ArtAssetbundlePack();
                                    assetDict.Add(subAssetbundleName, pack);
                                }

                                for (int i = faildList2.Count - 1; i >= 0; --i)
                                {
                                    ArtAssetbundlePack curPack = pack;
                                    if (curPack.AddAsset(faildList2[i]))
                                    {
                                        faildList2.RemoveAt(i);
                                    }
                                }

                                ++index;
                                subAssetbundleName = abPreName + "/" + item.Key + "_" + index;
                            }
                        }
                    }
                }
            }
        }

        foreach (var item in similarAntiDependencyCombinePackDict)
        {
            bool hasMaterial = false;
            foreach (var assetItem in item.Value.assetList)
            {
                if (assetItem.EndsWith(".mat"))
                {
                    hasMaterial = true;
                    break;
                }
            }
            if (hasMaterial && abDict.ContainsKey(item.Key))
                abDict.Remove(item.Key);
        }

        //打印
        long maxIncCnt = 0;
        long srcABResCnt = 0;
        long destAbRescnt = 0;
        string strAB = "";
        foreach (var item in perfData)
        {
            if (item.Value.Count == 2)
            {
                string strAssetList = "";
                string strAntiDependAB = "";
                if (noneCombineDict.TryGetValue(item.Key, out List<ArtResInfo> assetList3))
                {
                    if (assetList3.Count > 0)
                    {
                        for (int kk = 0; kk < assetList3[0].refAssetbundleList.Count; kk++)
                        {
                            strAntiDependAB = "$$$：[" + kk.ToString() + "] " + assetList3[0].refAssetbundleList[kk] + "\r\n";
                        }

                        for (int p = 0; p < assetList3.Count; p++)
                        {
                            strAssetList = "$$$：[" + p.ToString() + "] " + assetList3[p].path + "\r\n";
                        }
                    }
                }
                Debug.Log("AB路径$$$：" + item.Key + "  原始AB内资源数量： " + item.Value[0] + "  原始AB内资源大小：" + item.Value[1] + "\r\n");
                Debug.Log("Assets$$$：\r\n" + strAssetList);
                Debug.Log("AntiDependAB$$$：\r\n" + strAntiDependAB);
            }
            else if (item.Value.Count == 4)
            {
                if (maxIncCnt < (item.Value[2] - item.Value[0]))
                {
                    strAB = item.Key;
                    maxIncCnt = item.Value[2] - item.Value[0];
                    srcABResCnt = item.Value[0];
                    destAbRescnt = item.Value[2];
                }

                string strAssetList = "";
                string strAntiDependAB = "";
                if (abDict2.TryGetValue(item.Key, out List<ArtResInfo> assetList3))
                {
                    if (assetList3.Count > 0)
                    {
                        for (int kk = 0; kk < assetList3[0].refAssetbundleList.Count; kk++)
                        {
                            strAntiDependAB = "$$$：[" + kk.ToString() + "] " + assetList3[0].refAssetbundleList[kk] + "\r\n";
                        }

                        for (int p = 0; p < assetList3.Count; p++)
                        {
                            strAssetList = "$$$：[" + p.ToString() + "] " + assetList3[p].path + "\r\n";
                        }
                    }
                }
                Debug.Log("AB路径$$$：" + item.Key + "  原始AB内资源数量： " + item.Value[0] + "  原始AB内资源大小：" + item.Value[1] + "  合并后AB内资源数量： " + item.Value[2] + "  合并后AB内资源大小：" + item.Value[3] + "\r\n");
                Debug.Log("Assets$$$：\r\n" + strAssetList);
                Debug.Log("AntiDependAB$$$：\r\n" + strAntiDependAB);
            }
            else
            {
                Debug.Log("Exception: AB路径：" + item.Key + "\r\n");
            }
        }

        Debug.Log("AB路径：" + strAB + "  原始AB内资源数量： " + srcABResCnt + "  合并后AB内资源数量：" + destAbRescnt + "\r\n");
        if (faildList2.Count > 0)
        {
            Debug.Log("AB路径失败：" + faildList2.Count);
        }
    }
    //输入参数1：List<ArtResInfo> resList  当前某一个AB路径包含的资源文件列表
    //输入参数2：Dictionary<string, List<ArtResInfo>> abDict  所有AB路径对应的资源文件列表
    //输出参数：BundleDetailInfo   从全部AB路径列表去找一个比当前输入AB路径的被引用AB路径更多的且assetSize最小的AB路径信息
    public static BundleDetailInfo FindSameRefAssetbundleList(List<ArtResInfo> resList, Dictionary<string, List<ArtResInfo>> abDict)
    {
        bool find = false;
        BundleDetailInfo ret = new BundleDetailInfo();

        if (resList != null && resList.Count > 0)
        {
            foreach (var abItem in abDict)
            {
                var assetList = abItem.Value;
                if (resList == assetList)
                {
                    continue;
                }

                //for (int j = 0; j < assetList.Count; ++j)
                if (assetList.Count > 0)
                {
                    int m = 0;
                    while (m < resList[0].refAssetbundleList.Count)
                    {
                        if (assetList[0].refAssetbundleList.Contains(resList[0].refAssetbundleList[m]) == false)
                        {
                            find = false;
                            break;
                        }
                        find = true;
                        m++;
                    }

                    //if (find == true)
                    //{
                    //    break;
                    //}
                    if (find == true)
                    {
                        long assetSize = 0;
                        for (int i = 0; i < abItem.Value.Count; ++i)
                        {
                            assetSize += abItem.Value[i].AssetSize;
                        }

                        if (string.IsNullOrEmpty(ret.ABPath) || ret.AssetSize > assetSize)
                        {
                            ret.ABPath = abItem.Key;
                            ret.AssetSize = assetSize;
                            ret.resList = abItem.Value;
                        }
                    }
                }
            }
        }
        return ret;
    }

    private static bool HasBeenCombineRefAssetbundles(ArtResInfo resInfo)
    {
        foreach (var item in resInfo.refAssetbundleList)
        {
            if (ArtResInfo.m_combineRefAssetbundles.Contains(item))
            {
                return true;
            }
        }
        return false;
    }

    public static bool SetAssetbundleInMeta(string metaFile, string assetbundleName, string assetBundleVariant)
    {
        if (File.Exists(metaFile))
        {
            string[] lines = File.ReadAllLines(metaFile);

            string oldAssetbundleName = lines[lines.Length - 2].Replace("  assetBundleName: ", "");
            string oldAssetBundleVariant = lines[lines.Length - 1].Replace("  assetBundleVariant: ", "");

            if (oldAssetbundleName != assetbundleName || oldAssetBundleVariant != assetBundleVariant)
            {
                lines[lines.Length - 2] = "  assetBundleName: " + assetbundleName;
                if (string.IsNullOrEmpty(assetbundleName))  // 直接設置一下 有時候有些人他設置ab的時候沒匹配上unity3d這個後綴的 我們這裏自動修復他
                    lines[lines.Length - 1] = "  assetBundleVariant: ";
                else
                    lines[lines.Length - 1] = "  assetBundleVariant: unity3d";
                File.WriteAllLines(metaFile, lines);

                return true;
            }
        }
        return false;
    }
}

