using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

namespace ArtResTools
{
    [CanEditMultipleObjects]
    [CustomEditor(typeof(ArtResPathConfig), true)]
    public class ArtResPathConfigEditor : Editor
    {
        SerializedProperty m_artPathConfigListProperty = null;
        SerializedProperty m_descProperty = null;

        private void OnEnable()
        {
            m_artPathConfigListProperty = serializedObject.FindProperty("m_artPathConfigList");
            m_descProperty = serializedObject.FindProperty("desc");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            EditorGUILayout.BeginVertical();

            EditorGUILayout.PropertyField(m_descProperty);

            if (GUILayout.Button("保存"))
            {
                AssetDatabase.SaveAssets();
                ZgameLimitedAbNameLoader.ResetSettings();
            }
            GUI.color = Color.green;
            if (GUILayout.Button("添加路径"))
            {
                AddDefaultPath();
            }
            GUI.color = Color.white;

            for (int i = 0; i < m_artPathConfigListProperty.arraySize; ++i)
            {
                SerializedProperty c = m_artPathConfigListProperty.GetArrayElementAtIndex(i);

                GUILayout.BeginVertical("box");

                foreach (SerializedProperty subItemProperty in c)
                {
                    EditorGUILayout.PropertyField(subItemProperty);
                }

                //EditorGUILayout.PropertyField(c);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("刷新Assetbundle"))
                {
                    if (EditorUtility.DisplayDialog("提示", "确定要刷新这个路径吗？(请先保存后再刷新)", "是的", "取消"))
                    {
                        var item = m_artPathConfigListProperty.GetArrayElementAtIndex(i);
                        var path = item.FindPropertyRelative("path").stringValue;
                        var autoCombine = item.FindPropertyRelative("autoCombine").boolValue;
                        var autoSpitMesh_Texture_Anim = item.FindPropertyRelative("autoSpitMesh_Texture_Anim").boolValue;
                        var autoSpitMaterial_Prtefab = item.FindPropertyRelative("autoSpitMaterial_Prtefab").boolValue;
                        var similarAntiDependencyAutoCombine = item.FindPropertyRelative("similarAntiDependencyAutoCombine").boolValue;
                        var type = (ArtResPathConfig.AssetbundleRuleType)item.FindPropertyRelative("assetbundleRuleType").intValue;
                        if (type == ArtResPathConfig.AssetbundleRuleType.Dependency)
                        {
                            //var abPreName = path.Substring(path.LastIndexOf("/") + 1).ToLower();
                            //ArtResPathUtil.AutoClassified(abPreName, path, autoCombine, autoSpitMesh_Texture_Anim, autoSpitMaterial_Prtefab, similarAntiDependencyAutoCombine);
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(path) && System.IO.Directory.Exists(path))
                                AssetDatabase.ImportAsset(path, ImportAssetOptions.ImportRecursive);
                            else
                                Debug.LogError("路径: " + path + " 不存在");
                        }
                    }
                }
                GUI.color = Color.red;
                if (GUILayout.Button("删除路径"))
                {
                    if (EditorUtility.DisplayDialog("提示", "确定要删除这个路径吗？", "是的", "取消"))
                        m_artPathConfigListProperty.DeleteArrayElementAtIndex(i);
                }
                GUI.color = Color.white;
                EditorGUILayout.EndHorizontal();

                GUILayout.EndVertical();
            }

            EditorGUILayout.EndVertical();

            serializedObject.ApplyModifiedProperties();
        }

        protected void AddDefaultPath()
        {
            m_artPathConfigListProperty.InsertArrayElementAtIndex(0);
        }
    }
}
