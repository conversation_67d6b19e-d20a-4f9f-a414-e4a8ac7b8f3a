using UnityEngine;
using System.Text;

/// <summary>
/// 日志工具类：支持分级日志输出，可用于编辑器窗口和控制台。
/// </summary>
public static class LogUtility
{
    private static StringBuilder logBuilder = new StringBuilder();

    /// <summary>
    /// 普通日志
    /// </summary>
    public static void Log(string msg)
    {
        Debug.Log(msg);
        logBuilder.AppendLine("[信息] " + msg);
    }

    /// <summary>
    /// 警告日志
    /// </summary>
    public static void LogWarning(string msg)
    {
        Debug.LogWarning(msg);
        logBuilder.AppendLine("[注意] " + msg);
    }

    /// <summary>
    /// 错误日志
    /// </summary>
    public static void LogError(string msg)
    {
        Debug.LogError(msg);
        logBuilder.AppendLine("[重要] " + msg);
    }

    /// <summary>
    /// 获取所有日志内容（用于编辑器窗口显示）
    /// </summary>
    public static string GetLogContent()
    {
        return logBuilder.ToString();
    }

    /// <summary>
    /// 清空日志内容
    /// </summary>
    public static void Clear()
    {
        logBuilder.Clear();
    }
}
