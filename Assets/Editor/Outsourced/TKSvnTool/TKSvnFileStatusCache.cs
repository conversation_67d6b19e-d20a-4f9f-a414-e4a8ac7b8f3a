using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace TK<PERSON>ramework
{
    public class TKSvnFileStatusCache
    {
        private static Dictionary<string, TKSvnFileType> ms_statusCache = new Dictionary<string, TKSvnFileType>();

        public static void Refresh(System.Action onDone = null)
        {
            Debug.Log("[TKSvn] Update File Status");
            ms_statusCache.Clear();
            TKShellCore.ShellRequest req = TKSvnCore.Status(new string[] { "Assets" }, new string[] { "--ignore-externals" });
            req.onLog += OnLogRecv;
            req.onDone += () =>
            {
                Debug.Log("[TKSvn] Update File Status Successful");
                onDone?.Invoke();
            };
        }

        protected static void OnLogRecv(int level, string log)
        {
            TKSvnFileStatus file = TKSvnCore.ParseStatusFromLog(log);
            if (file != null)
            {
                SetFileStatus(file.path, file.status);
            }
        }

        private static void SetFileStatus(string path, TKSvnFileType status)
        {
            if (ms_statusCache.ContainsKey(path))
            {
                if (ms_statusCache[path] != TKSvnFileType.Conflict)
                {
                    ms_statusCache[path] = status;
                }
            }
            else
            {
                ms_statusCache.Add(path, status);
            }

            string parent = System.IO.Path.GetDirectoryName(path);
            if (string.IsNullOrEmpty(parent))
            {
                return;
            }
            if (status == TKSvnFileType.External)
            {
                return;
            }

            TKSvnFileType parentStatus = TKSvnFileType.Modify;
            if (status == TKSvnFileType.Conflict)
            {
                parentStatus = TKSvnFileType.Conflict;
            }
            SetFileStatus(parent, parentStatus);
        }

        public static TKSvnFileType GetFileStatus(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return TKSvnFileType.None;
            }
            if (!ms_statusCache.ContainsKey(path))
            {
                TKSvnFileType rootType = GetFileStatus(System.IO.Path.GetDirectoryName(path));
                if (rootType == TKSvnFileType.New)
                {
                    return TKSvnFileType.New;
                }
                else if (rootType == TKSvnFileType.External)
                {
                    return TKSvnFileType.External;
                }
                return TKSvnFileType.None;
            }
            return ms_statusCache[path];
        }

        public static Dictionary<string, TKSvnFileType> Filter(string path)
        {
            Dictionary<string, TKSvnFileType> ret = new Dictionary<string, TKSvnFileType>();
            foreach (string key in ms_statusCache.Keys)
            {
                if (key.StartsWith(path))
                {
                    ret.Add(key, ms_statusCache[key]);
                }
            }
            return ret;
        }
    }
}
