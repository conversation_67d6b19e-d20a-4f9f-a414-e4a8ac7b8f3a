using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using static UnityEditor.IMGUI.Controls.TreeView;

namespace TKFramework
{
    public class TKSvnCommitWindow : EditorWindow
    {
        private List<TKSvnCommitItem> m_items = new List<TKSvnCommitItem>();
        private HashSet<string> m_selected = new HashSet<string>();
        private string m_focused = string.Empty;

        private bool m_needRepaint = false;
        private bool m_needClose = false;
        public bool isFetching = false;
        private Vector2 m_scrollPos;
        private string m_commitMessage;
        private float m_firstItemPosY = -1;
        private bool m_drawUnCommitItem = false;
        private int m_drawItemIndex = 0;

        private string m_commitType;
        private string m_commitId;
        private string m_commitDesc;

        private int m_recentIndex = 0;
        private string[] m_recentCommitMessages = new string[10];

        public static TKSvnCommitWindow Open()
        {
            TKSvnCommitWindow win = EditorWindow.GetWindow<TKSvnCommitWindow>("TKSvnCommit");
            win.Focus();
            win.ShowUtility();

            for (int i = 0; i < win.m_recentCommitMessages.Length; ++i)
            {
                var key = "TKSvn.Recent" + i;
                if (EditorPrefs.HasKey(key))
                    win.m_recentCommitMessages[i] = EditorPrefs.GetString(key);
            }

            return win;
        }

        public TKSvnCommitItem Add(TKSvnFile file)
        {
            TKSvnCommitItem commitItem = new TKSvnCommitItem();
            commitItem.fileInfo = file;
            commitItem.SelectAll(m_selected);
            m_items.Add(commitItem);
            return commitItem;

        }

        //public TKSvnCommitItem Log(string line)
        //{
        //    if (line.StartsWith(" "))
        //    {
        //        return null;
        //    }
        //    if (string.IsNullOrEmpty(line))
        //    {
        //        return null;
        //    }
        //    TKSvnFileStatus file = TKSvnCore.ParseStatusFromLog(line);
        //    if (file == null)
        //    {
        //        return null;
        //    }
        //    TKSvnCommitItem item = new TKSvnCommitItem();
        //    item.Selected = true;
        //    item.isFocused = false;
        //    switch (file.status)
        //    {
        //        case TKSvnFileType.Added:
        //        case TKSvnFileType.Modify:
        //        case TKSvnFileType.Delete:
        //        case TKSvnFileType.New:
        //        case TKSvnFileType.Missing:
        //            AddItem(file.status, file.path);
        //            break;
        //    }
        //    return item;
        //}

        private bool CheckFile(string path)
        {
            bool success = true;
            success &= CheckDDS(path);
            return success;
        }

        private bool CheckDDS(string path)
        {
            if (path.ToLower().EndsWith(".dds"))
            {
                ShowNotification(new GUIContent("提交的文件包含DDS后缀的资源，请删除！文件名：" + path));
                return false;
            }
            return true;
        }

        private void OnCommitClick()
        {
            if (string.IsNullOrEmpty(m_commitMessage))
            {
                EditorUtility.DisplayDialog("错误", "请填入提交说明", "懂了，我这就做");
                return;
            }

            List<string> tobeAddList = new List<string>();
            List<string> list = new List<string>();
            List<string> tobeDeleteList = new List<string>();

            HashSet<TKSvnFileType> commitType = new HashSet<TKSvnFileType>();
            commitType.Add(TKSvnFileType.Added);
            commitType.Add(TKSvnFileType.Modify);
            commitType.Add(TKSvnFileType.Delete);
            foreach (var item in m_selected)
            {
                if (!CheckFile(item))
                {
                    return;
                }

                var status = TKSvnFileStatusCache.GetFileStatus(item);
                if (commitType.Contains(status))
                {
                    list.Add(item);
                }
                if (status == TKSvnFileType.New)
                {
                    tobeAddList.Add(item);
                }
                if (status == TKSvnFileType.Missing)
                {
                    tobeDeleteList.Add(item);
                }
            }

            if (tobeDeleteList.Count > 0)
            {
                TKShellCore.ShellRequest deleteReq = TKSvnCore.Delete(tobeDeleteList.ToArray(), () =>
                {
                    list.AddRange(tobeDeleteList);
                    AddWithCommit(tobeAddList, list);
                });
            }
            else
            {
                AddWithCommit(tobeAddList, list);
            }
        }
        private void AddWithCommit(List<string> addList, List<string> commitList)
        {
            if (addList.Count == 0)
            {
                if (commitList.Count == 0)
                {
                    this.ShowNotification(new GUIContent("请选择提交的文件!"));
                    Debug.LogError("No files to be commited");
                    return;
                }
                TKSvnCore.Commit(m_commitMessage, commitList.ToArray());
                m_needClose = true;
            }
            else
            {
                TKShellCore.ShellRequest req = TKSvnCore.Add(addList.ToArray(), () =>
                {
                    commitList.AddRange(addList);
                    TKSvnCore.Commit(m_commitMessage, commitList.ToArray());
                    m_needClose = true;
                });
            }

            if (!m_recentCommitMessages.Contains(m_commitMessage))
            {
                for (int i = 0; i < m_recentCommitMessages.Length; ++i)
                {
                    if (i < m_recentCommitMessages.Length - 1)
                    {
                        m_recentCommitMessages[i + 1] = m_recentCommitMessages[i];
                    }
                }
                m_recentCommitMessages[0] = m_commitMessage;

                for (int i = 0; i < m_recentCommitMessages.Length; ++i)
                {
                    EditorPrefs.SetString("TKSvn.Recent" + i, m_recentCommitMessages[i]);
                }
            }
        }

        private bool CanCommit(TKSvnFileType status)
        {
            bool canSelected = false;
            switch (status)
            {
                case TKSvnFileType.New:
                case TKSvnFileType.Added:
                case TKSvnFileType.Modify:
                case TKSvnFileType.Delete:
                case TKSvnFileType.Missing:
                    canSelected = true;
                    break;
            }
            return canSelected;
        }

        private void DrawItem(TKSvnCommitItem item, int level)
        {
            if (!m_drawUnCommitItem)
            {
                var itemStatus = TKSvnFileStatusCache.GetFileStatus(item.fileInfo.path);
                if (!CanCommit(itemStatus))
                {
                    bool canCommit = false;
                    for (int i = 0; i < item.fileInfo.dependences.Count; ++i)
                    {
                        var depend = item.fileInfo.dependences[i];
                        if (CanCommit(TKSvnFileStatusCache.GetFileStatus(depend)))
                        {
                            canCommit = true;
                            break;
                        }
                    }
                    if (!canCommit)
                    {
                        for (int i = 0; i < item.fileInfo.antiDependences.Count; ++i)
                        {
                            var depend = item.fileInfo.antiDependences[i];
                            if (CanCommit(TKSvnFileStatusCache.GetFileStatus(depend)))
                            {
                                canCommit = true;
                                break;
                            }
                        }
                    }
                    if (!canCommit)
                    {
                        return;
                    }
                }
            }

            GUIContent content = item.Content;
            DrawBackground(item.fileInfo.path, item.position);

            var width = GUI.skin.label.CalcSize(content).x;
            EditorGUIUtility.labelWidth = width;
            EditorGUILayout.BeginHorizontal();

            for (int i = 0; i < level; i++)
            {
                GUILayout.Space(10);
            }
            string assetPath = item.fileInfo.path;
            Texture icon = AssetDatabase.GetCachedIcon(assetPath);
            if (icon == null || icon.width <= 1)
            {
                icon = Texture2D.whiteTexture;
            }
            bool hasRelationsAssets = item.fileInfo.dependences.Count > 0 || item.fileInfo.antiDependences.Count > 0;
            if (hasRelationsAssets)
            {
                GUILayout.Box(icon, GUILayout.Width(13), GUILayout.Height(13));
                GUI.changed = false;
                //GUIContent t = new GUIContent(assetPath, AssetDatabase.GetCachedIcon(assetPath));
                item.isFoldout = EditorGUILayout.Foldout(item.isFoldout, assetPath);
                if (GUI.changed)
                {
                    m_needRepaint = true;
                }
            }
            else
            {
                GUILayout.Box(icon, GUILayout.Width(13), GUILayout.Height(13));
                GUILayout.Space(10);
                EditorGUILayout.LabelField(item.fileInfo.path);
            }

            var status = DrawSvnStatus(item.fileInfo.path);

            if (CanCommit(status))
            {
                GUI.changed = false;
                bool isSelected = EditorGUILayout.Toggle(item.IsOwnerSelected(m_selected), GUILayout.Width(25));
                if (GUI.changed)
                {
                    if (isSelected)
                        item.SelectOwner(m_selected);
                    else
                        item.UnSelectOwner(m_selected);
                }
            }

            if (hasRelationsAssets)
            {
                var isAllSelect = item.IsAllSelected(m_selected);
                if (isAllSelect)
                {
                    if (GUILayout.Button("取消全选", GUILayout.Width(60)))
                    {
                        item.UnSelectAll(m_selected);
                    }
                }
                else
                {
                    if (GUILayout.Button("全选", GUILayout.Width(60)))
                    {
                        item.SelectAll(m_selected);
                    }
                }
            }

            EditorGUILayout.EndHorizontal();
            if (Event.current.type == EventType.Repaint)
            {
                Rect rect = GUILayoutUtility.GetLastRect();
                rect.height += EditorGUIUtility.standardVerticalSpacing;
                if (item.position != rect)
                {
                    m_needRepaint = true;
                }
                item.position = rect;
            }
            if (item.isFoldout)
            {
                if (item.fileInfo.dependences.Count > 0)
                {
                    DrawDependences(item, level + 1);
                }

                if (item.fileInfo.dependences.Count > 0)
                {
                    DrawAntiDependences(item, level + 1);
                }
            }

            FocusItem(item.position, item.fileInfo.path);
        }

        private void DrawBackground(string path, Rect r)
        {
            if (Event.current.type == EventType.Repaint)
            {
                if (m_focused == path)
                {
                    GUI.color = Color.blue;
                    GUI.Box(r, "");
                    GUI.color = Color.white;
                }
                else if (m_drawItemIndex % 2 == 0)
                {
                    GUI.color = Color.gray;
                    GUI.Box(r, "");
                    GUI.color = Color.white;
                }

                //var bgStyle = m_drawItemIndex % 2 == 0 ? DefaultStyles.backgroundEven : DefaultStyles.backgroundOdd;
                //bgStyle.Draw(rect, false, false, false, false);
                ++m_drawItemIndex;
            }
        }

        private TKSvnFileType DrawSvnStatus(string path)
        {
            //GUILayout.FlexibleSpace();
            Color baseColor = GUI.skin.label.normal.textColor;
            var status = TKSvnFileStatusCache.GetFileStatus(path);
            if (status == TKSvnFileType.Added)
            {
                GUI.skin.label.normal.textColor = Color.green;
                GUILayout.Label("新增", GUILayout.Width(50));
            }
            else if (status == TKSvnFileType.Modify)
            {
                GUI.skin.label.normal.textColor = Color.yellow;
                GUILayout.Label("修改", GUILayout.Width(50));
            }
            else if (status == TKSvnFileType.New)
            {
                GUI.skin.label.normal.textColor = Color.blue;
                GUILayout.Label("未知", GUILayout.Width(50));
            }
            else if (status == TKSvnFileType.Delete)
            {
                GUI.skin.label.normal.textColor = Color.red;
                GUILayout.Label("删除", GUILayout.Width(50));
            }
            else if (status == TKSvnFileType.Missing)
            {
                GUI.skin.label.normal.textColor = Color.red;
                GUILayout.Label("丢失", GUILayout.Width(50));
            }
            else if (status == TKSvnFileType.None)
            {
                GUILayout.Label("", GUILayout.Width(50));
            }
            GUI.skin.label.normal.textColor = baseColor;
            return status;
        }

        private void DrawAntiDependences(TKSvnCommitItem item, int level)
        {
            EditorGUILayout.BeginHorizontal();
            for (int j = 0; j < level; j++)
            {
                GUILayout.Space(10);
            }
            GUI.changed = false;
            item.isAntiDependencesFoldout = EditorGUILayout.Foldout(item.isAntiDependencesFoldout, "依赖的它资源 " + item.fileInfo.antiDependences.Count);
            if (GUI.changed)
            {
                m_needRepaint = true;
            }
            GUI.changed = false;
            bool isSelected = EditorGUILayout.Toggle(item.IsAllDependenceSelected(m_selected), GUILayout.Width(25));
            if (GUI.changed)
            {
                if (isSelected)
                    item.SelectDependences(m_selected);
                else
                    item.UnSelectDependences(m_selected);
            }
            EditorGUILayout.EndHorizontal();

            if (item.isAntiDependencesFoldout)
            {
                level += 1;
                for (int i = 0; i < item.fileInfo.antiDependences.Count; ++i)
                {
                    string assetPath = item.fileInfo.antiDependences[i];
                    DrawDepItem(assetPath, level, m_drawUnCommitItem);
                }
            }
        }

        private void DrawDependences(TKSvnCommitItem item, int level)
        {
            EditorGUILayout.BeginHorizontal();
            for (int j = 0; j < level; j++)
            {
                GUILayout.Space(10);
            }
            GUI.changed = false;
            item.isDependencesFoldout = EditorGUILayout.Foldout(item.isDependencesFoldout, "它依赖的资源 " + item.fileInfo.dependences.Count);
            if (GUI.changed)
            {
                m_needRepaint = true;
            }
            GUI.changed = false;
            bool isSelected = EditorGUILayout.Toggle(item.IsAllDependenceSelected(m_selected), GUILayout.Width(25));
            if (GUI.changed)
            {
                if (isSelected)
                    item.SelectDependences(m_selected);
                else
                    item.UnSelectDependences(m_selected);
            }
            EditorGUILayout.EndHorizontal();

            if (item.isDependencesFoldout)
            {
                level += 1;
                for (int i = 0; i < item.fileInfo.dependences.Count; ++i)
                {
                    string assetPath = item.fileInfo.dependences[i];
                    DrawDepItem(assetPath, level, m_drawUnCommitItem);
                }
            }
        }

        private void DrawDepItem(string assetPath, int level, bool drawUnCommitItem)
        {
            var status = TKSvnFileStatusCache.GetFileStatus(assetPath);
            var canCommit = CanCommit(status);
            if (!canCommit && !drawUnCommitItem)
                return;

            Rect r = EditorGUILayout.BeginHorizontal();
            DrawBackground(assetPath, r);
            for (int j = 0; j < level; j++)
            {
                GUILayout.Space(10);
            }
            Texture icon = AssetDatabase.GetCachedIcon(assetPath);
            if (icon == null || icon.width <= 1)
            {
                icon = Texture2D.whiteTexture;
            }
            GUILayout.Box(icon, GUILayout.Width(13), GUILayout.Height(13));
            GUILayout.Space(10);
            EditorGUILayout.LabelField(assetPath);

            DrawSvnStatus(assetPath);
            if (canCommit)
            {
                GUI.changed = false;
                bool isSelected = EditorGUILayout.Toggle(m_selected.Contains(assetPath), GUILayout.Width(25));
                if (GUI.changed)
                {
                    if (isSelected)
                        m_selected.Add(assetPath);
                    else
                        m_selected.Remove(assetPath);
                }
            }

            EditorGUILayout.EndHorizontal();

            FocusItem(r, assetPath);
        }

        private const string CommitMsgRegexStr = @"--(story|bug)=(\d+) (.+)";
        private void ParseCommitMessage()
        {
            //--bug=88673529 【#2867】【局内】【loading】loading进入游戏时会有一闪而过的黑屏
            //--story=865809373 【构件机自动提交svn专属单】部分cfg配置或者高低模拆分需要构建机自动提交
            if (m_commitMessage.StartsWith("--"))
            {
                Regex reg = new Regex(CommitMsgRegexStr);
                Match match = reg.Match(m_commitMessage);
                if (match.Groups.Count == 4)
                {
                    m_commitType = match.Groups[1].Value;
                    m_commitId = match.Groups[2].Value;
                    m_commitDesc = match.Groups[3].Value;
                }
                else
                {
                    m_commitType = string.Empty;
                    m_commitId = string.Empty;
                    m_commitDesc = string.Empty;
                }
            }
            else
            {
                m_commitType = string.Empty;
                m_commitId = string.Empty;
                m_commitDesc = string.Empty;
            }
        }

        private StringBuilder m_refershStr = new StringBuilder();
        private int m_refershIndex = 0;
        private double m_refershTime = 0;

        private void OnGUI()
        {
            EditorGUILayout.BeginHorizontal();
            m_drawUnCommitItem = GUILayout.Toggle(m_drawUnCommitItem, "显示不可提交项", "preButton", GUILayout.Width(85));

            if (GUILayout.Button("刷新状态", "preButton", GUILayout.Width(60)))
            {
                isFetching = true;
                TKSvnFileStatusCache.Refresh(() =>
                {
                    isFetching = false;
                });
            }

            if (GUILayout.Button("首次安装说明", "preButton", GUILayout.Width(90)))
            {
                Application.OpenURL("https://iwiki.woa.com/pages/viewpage.action?pageId=898979948");
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            GUILayout.Space(1);
            if (Event.current.type == EventType.Repaint)
            {
                m_drawItemIndex = 0;
                m_firstItemPosY = -GUILayoutUtility.GetLastRect().yMin - EditorGUIUtility.standardVerticalSpacing * 2;
            }
            m_scrollPos = EditorGUILayout.BeginScrollView(m_scrollPos, EditorStyles.inspectorFullWidthMargins);

            if (isFetching)
            {
                m_refershStr.Clear();
                m_refershStr.Append("刷新中");
                for (int i = 0; i < m_refershIndex; ++i)
                {
                    m_refershStr.Append(".");
                }
                if (EditorApplication.timeSinceStartup - m_refershTime > 0.5f)
                {
                    m_refershTime = EditorApplication.timeSinceStartup;
                    ++m_refershIndex;
                    if (m_refershIndex > 3)
                        m_refershIndex = 0;
                }
                m_needRepaint = true;
                EditorGUILayout.LabelField(m_refershStr.ToString());
            }
            else if (m_items.Count == 0)
            {
                EditorGUILayout.LabelField("空");
            }

            for (int i = 0; i < m_items.Count; ++i)
            {
                DrawItem(m_items[i], 0);
            }
            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
            GUI.changed = false;
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("请输入提交说明：");

            if (m_recentCommitMessages.Length > 0)
            {
                m_recentIndex = EditorGUILayout.Popup(m_recentIndex, m_recentCommitMessages);
                if (GUILayout.Button("插入说明"))
                {
                    m_commitMessage = m_recentCommitMessages[m_recentIndex];
                    ParseCommitMessage();
                }
            }

            if (GUILayout.Button("如何输入？", GUILayout.Width(80)))
            {
                Application.OpenURL("https://iwiki.woa.com/pages/viewpage.action?pageId=807031452");
            }
            EditorGUILayout.EndHorizontal();
            m_commitMessage = EditorGUILayout.TextArea(m_commitMessage, GUILayout.Height(50));
            if (GUI.changed)
            {
                ParseCommitMessage();
            }

            EditorGUILayout.BeginHorizontal();

            EditorGUILayout.LabelField("类型：" + m_commitType, GUILayout.Width(100));
            EditorGUILayout.LabelField("TAPD索引：" + m_commitId, GUILayout.Width(100));
            EditorGUILayout.LabelField("提交说明：" + m_commitDesc);

            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("提交"))
            {
                if (string.IsNullOrEmpty(m_commitType) || string.IsNullOrEmpty(m_commitId))
                {
                    EditorUtility.DisplayDialog("错误", "请填入正确的提交说明", "懂了，我这就做");
                    return;
                }
                OnCommitClick();
            }

            if (m_needRepaint)
            {
                this.Repaint();
                m_needRepaint = false;
            }
            if (m_needClose)
            {
                m_needClose = false;
                this.Close();
                return;

            }
        }

        private void FocusItem(Rect r, string path)
        {
            if (Event.current.type == EventType.MouseDown)
            {
                if (r.Contains(Event.current.mousePosition + m_scrollPos + new Vector2(0, m_firstItemPosY)))
                {
                    if (Event.current.command)
                    {
                        if (path == m_focused)
                        {
                            m_focused = string.Empty;
                        }
                        else
                        {
                            m_focused = path;
                        }
                    }
                    else
                    {
                        m_focused = path;
                    }
                    this.Repaint();
                }
            }
        }
    }
}
