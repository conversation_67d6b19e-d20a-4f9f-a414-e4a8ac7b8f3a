using System.Collections.Generic;
using TKFrame;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using ZGameChess;

[System.Reflection.Obfuscation(Exclude = true)]
public class AnimationBroadcastToolWindow : EditorWindow
{
    public int callbackOrder { get { return 0; } }

    //// 目标英雄组ID
    //private static int targetHeroGroup = 14011;
    //// 目标英雄名称
    //private static string targetHeroName = "Jinx";
    // 目标赛季ID
    private static int targetSet = 1;

    private static bool needSave = false;

    private AnimationBroadcastParams targetParams = new AnimationBroadcastParams();

    private const string LeftTextSpritePath = "Assets/ChessArt/UIAtlas_V2/v2_InGame_BroadcastEx/InGame_BroadcastEx_LeftText.png";
    private const string RightTextSpritePath = "Assets/ChessArt/UIAtlas_V2/v2_InGame_BroadcastEx/InGame_BroadcastEx_RightText.png";

    private const string BroadcastConfigPath = "Assets/ChessArt/UI/Battle/AnimationBroadcast/animationbroadcast_config_set";

    [MenuItem("Tools/动画播报配置管理")]
    private static void ShowLittleLegendWindow()
    {
        AnimationBroadcastToolWindow window = GetWindow(typeof(AnimationBroadcastToolWindow), false, "动画播报配置管理") as AnimationBroadcastToolWindow;

        if (window != null)
        {
            EditorUtility.ClearProgressBar();
            window.minSize = new Vector2(600, 400);
            window.Show();
            needSave = false;
            window.UpdateAnimationBroadcastParams();
        }
    }



    private void UpdateAnimationBroadcastParams()
    {

        var animationBroadcastInfo = FindObjectOfType<AnimationBroadcastInfo>();
        if (animationBroadcastInfo == null)
        {
            Debug.LogError("请先选中需要拷贝参数的动画播报Prefab!");
            return;
        }

        Transform targetTransform = animationBroadcastInfo.transform;
        Transform floor = targetTransform.Find("floor");
        Transform hero = targetTransform.Find("hero");
        Transform text = targetTransform.Find("text");
        Transform effectRoot = targetTransform.Find("EffectRoot");

        if (floor == null)
        {
            Debug.LogError("未找到‘floor’节点, 请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (hero == null)
        {
            Debug.LogError("未找到‘hero’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (text == null)
        {
            Debug.LogError("未找到‘text’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (effectRoot == null)
        {
            Debug.LogError("未找到‘EffectRoot’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        Transform up1 = effectRoot.Find("1up");
        Transform down2 = effectRoot.Find("2down");
        Transform down3 = effectRoot.Find("3down");
        Transform up4 = effectRoot.Find("4up");
        Transform up5 = effectRoot.Find("5up");

        if (up1 == null)
        {
            Debug.LogError("未找到‘1up’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (down2 == null)
        {
            Debug.LogError("未找到‘2down’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (down3 == null)
        {
            Debug.LogError("未找到‘3down’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (up4 == null)
        {
            Debug.LogError("未找到‘4up’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        if (up5 == null)
        {
            Debug.LogError("未找到‘5up’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’");
            return;
        }

        var floorImg = floor.GetComponent<Image>();
        var heroImg = hero.GetComponent<Image>();
        var textImg = text.GetComponent<Image>();
        var up1PSR = up1.GetComponent<ParticleSystemRenderer>();
        var down2PSR = down2.GetComponent<ParticleSystemRenderer>();
        var down3PSR = down3.GetComponent<ParticleSystemRenderer>();
        var up4PSR = up4.GetComponent<ParticleSystemRenderer>();
        var up5PSR = up5.GetComponent<ParticleSystemRenderer>();

        if (floorImg == null)
        {
            Debug.LogError("‘floor’节点Image组件丢失");
            return;
        }

        if (heroImg == null)
        {
            Debug.LogError("‘hero’节点Image组件丢失");
            return;
        }

        if (textImg == null)
        {
            Debug.LogError("‘text’节点Image组件丢失");
            return;
        }

        if (up1PSR == null || up1PSR.sharedMaterial == null)
        {
            Debug.LogError("‘1up’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (down2PSR == null || down2PSR.sharedMaterial == null)
        {
            Debug.LogError("‘2down’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (down3PSR == null || down3PSR.sharedMaterial == null)
        {
            Debug.LogError("‘3down’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (up4PSR == null || up4PSR.sharedMaterial == null)
        {
            Debug.LogError("‘4up’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (up5PSR == null || up5PSR.sharedMaterial == null)
        {
            Debug.LogError("‘5up’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (textImg.material == null)
        {
            Debug.LogError("‘text’节点材质丢失");
            return;
        }

        if (floorImg.material == null)
        {
            Debug.LogError("‘floor’节点材质丢失");
            return;
        }

        targetParams.heroImgName = heroImg.name;

        targetParams.textColor = textImg.material.GetColor(AnimationBroadcastInfo.TextColorName);
        targetParams.textGrayScale = textImg.material.GetFloat(AnimationBroadcastInfo.TextGrayScaleName);

        targetParams.bgMainColor = floorImg.material.GetColor(AnimationBroadcastInfo.BGMainColorName);
        targetParams.bgSecondColor = floorImg.material.GetColor(AnimationBroadcastInfo.BGSecondColorName);
        targetParams.bgThirdColor = floorImg.material.GetColor(AnimationBroadcastInfo.BGThirdColorName);
        targetParams.bgSpeedColor = floorImg.material.GetColor(AnimationBroadcastInfo.BGSpeedColorName);
        targetParams.bgRimColor = floorImg.material.GetColor(AnimationBroadcastInfo.BGRimColorName);
        targetParams.bgRimSecondColor = floorImg.material.GetColor(AnimationBroadcastInfo.BGRimSecondColorName);

        targetParams.effect01Color = up1PSR.sharedMaterial.GetColor(AnimationBroadcastInfo.EffectColorName);
        targetParams.effect02Color = down2PSR.sharedMaterial.GetColor(AnimationBroadcastInfo.EffectColorName);
        targetParams.effect03Color = down3PSR.sharedMaterial.GetColor(AnimationBroadcastInfo.EffectColorName);
        targetParams.effect04Color = up4PSR.sharedMaterial.GetColor(AnimationBroadcastInfo.EffectColorName);
        targetParams.effect05Color = up5PSR.sharedMaterial.GetColor(AnimationBroadcastInfo.EffectColorName);

        targetParams.effect01StartColor = up1.GetComponent<ParticleSystem>().main.startColor.color;
    }


    private void LoadParameters()
    {
        string configPath = BroadcastConfigPath + targetSet + ".asset";
        AnimationBroadcastConfig broadcastConfig = (AnimationBroadcastConfig)AssetDatabase.LoadAssetAtPath(configPath, typeof(AnimationBroadcastConfig));

        if (broadcastConfig == null)
        {
            Debug.LogError("配置文件路径不正确：" + configPath + " ， 请检查赛季是否填写正确！");
            return;
        }

        // 根据英雄组ID检查当前参数项是否已存在，若存在则覆盖，否则添加
        bool isExist = false;
        List<AnimationBroadcastParams> paramsList = new List<AnimationBroadcastParams>(broadcastConfig.paramsMap);

        for (int i = 0; i < paramsList.Count; ++i)
        {
            if (paramsList[i].heroGroup == targetParams.heroGroup)
            {
                targetParams = new AnimationBroadcastParams();
                targetParams.Set(    paramsList[i]);
                isExist = true;
                break;
            }
        }

        if (!isExist)
        {
            Debug.LogError("加载的数据不存在，请检查赛季是否填写正确以及herogoupid正确！");
        }
        

    }
    private void SaveAnimationBroadcastParams()
    {
        string configPath = BroadcastConfigPath + targetSet + ".asset";
        AnimationBroadcastConfig broadcastConfig = (AnimationBroadcastConfig)AssetDatabase.LoadAssetAtPath(configPath, typeof(AnimationBroadcastConfig));

        if (broadcastConfig == null)
        {
            Debug.LogError("配置文件路径不正确：" + configPath + " ， 请检查赛季是否填写正确！");
            return;
        }

        // 根据英雄组ID检查当前参数项是否已存在，若存在则覆盖，否则添加
        bool isExist = false;
        List<AnimationBroadcastParams> paramsList = new List<AnimationBroadcastParams>(broadcastConfig.paramsMap);
        AnimationBroadcastParams temp = new AnimationBroadcastParams();
        temp.Set(targetParams);
        for (int i = 0; i < paramsList.Count; ++i)
        {
            
            if (paramsList[i].heroGroup == targetParams.heroGroup)
            {

                if (EditorUtility.DisplayDialog("注意", "保存将会覆盖已经存在的数据，请确认", "Yes", "No"))
                {
                    paramsList[i] = temp;
                    isExist = true;
                    break;
                }
                else
                {
                    return;
                }
         
            }
        }

        if (!isExist)
        {
            paramsList.Add(temp);
        }

        broadcastConfig.paramsMap = paramsList.ToArray();

        EditorUtility.SetDirty(broadcastConfig);
        needSave = true;

        Debug.Log("保存成功");
    }

    /// <summary>
    /// 检查配置信息合法性
    /// </summary>
    /// <returns></returns>
    private bool CheckValidation()
    {
        if (targetSet <= 0)
        {
            Debug.LogError("赛季填写不正确！");
            return false;
        }

        if (targetParams.heroName.IsNullOrEmpty())
        {
            Debug.LogError("英雄名称不能为空！");
            return false;
        }

        return true;
    }

    private void GetAnicestor()
    {
        var temp = GameObject.Find("Effect_UI_JueNei_BoBao");
        if (temp != null)
        {
            _transform = temp.transform;
        }
        else
        {
            _transform = null;
        }
    
    }
    
    private void ApplyChanges()
    {
        GetAnicestor();
        if (_transform == null)
        {
            Transform[] selectedTransforms = Selection.GetTransforms(SelectionMode.TopLevel);

            if (selectedTransforms == null || selectedTransforms.Length <= 0)
            {
                return;
            }
            _transform = selectedTransforms[0];
        }
        if (_transform == null) return;
        
        if (_transform == null)
        {
            return;
        }
        
        var animationBroadcastImproveJYBL = FindObjectOfType<AnimationBroadcastImproveJYBL>();
        animationBroadcastImproveJYBL?.SetData(targetParams);
        var animationBroadcastImproveSSSH = FindObjectOfType<AnimationBroadcastImproveSSSH>();
        animationBroadcastImproveSSSH?.SetData(targetParams);
        var animationBroadcastImproveZkqc = FindObjectOfType<AnimationBroadcastImproveZKQC>();
        animationBroadcastImproveZkqc?.SetData(targetParams);
        var animationBroadcastImproveZzbs = FindObjectOfType<AnimationBroadcastImproveZZBS>();
        animationBroadcastImproveZzbs?.SetData(targetParams);
        
        ApplyChangesBroadcast();
    }
    private Transform _transform;
    private void ApplyChangesBroadcast()
    {
        Transform targetTransform = _transform;
        Transform floor = targetTransform.Find("floor");
        Transform hero = targetTransform.Find("hero");
        Transform text = targetTransform.Find("text");
        Transform effectRoot = targetTransform.Find("EffectRoot");

        if (floor == null)
        {
            Debug.LogError("未找到‘floor’节点, 请确认是否选中播报Prefab‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (hero == null)
        {
            Debug.LogError("未找到‘hero’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (text == null)
        {
            Debug.LogError("未找到‘text’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (effectRoot == null)
        {
            Debug.LogError("未找到‘EffectRoot’节点，请确认是否选中播报Prefab的根节点‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        Transform up1 = effectRoot.Find("1up");
        Transform down2 = effectRoot.Find("2down");
        Transform down3 = effectRoot.Find("3down");
        Transform up4 = effectRoot.Find("4up");
        Transform up5 = effectRoot.Find("5up");

        if (up1 == null)
        {
            Debug.LogError("未找到‘1up’节点，请确认是否选中播报Prefab‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (down2 == null)
        {
            Debug.LogError("未找到‘2down’节点，请确认是否选中播报Prefab‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (down3 == null)
        {
            Debug.LogError("未找到‘3down’节点，请确认是否选中播报Prefab的‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (up4 == null)
        {
            Debug.LogError("未找到‘4up’节点，请确认是否选中播报Prefab的‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        if (up5 == null)
        {
            Debug.LogError("未找到‘5up’节点，请确认是否选中播报Prefab的‘Effect_UI_JueNei_BoBao’的父节点");
            return;
        }

        var floorImg = floor.GetComponent<Image>();
        var heroImg = hero.GetComponent<Image>();
        var textImg = text.GetComponent<Image>();
        var up1PSR = up1.GetComponent<ParticleSystemRenderer>();
        var down2PSR = down2.GetComponent<ParticleSystemRenderer>();
        var down3PSR = down3.GetComponent<ParticleSystemRenderer>();
        var up4PSR = up4.GetComponent<ParticleSystemRenderer>();
        var up5PSR = up5.GetComponent<ParticleSystemRenderer>();

        if (floorImg == null)
        {
            Debug.LogError("‘floor’节点Image组件丢失");
            return;
        }

        if (heroImg == null)
        {
            Debug.LogError("‘hero’节点Image组件丢失");
            return;
        }

        if (textImg == null)
        {
            Debug.LogError("‘text’节点Image组件丢失");
            return;
        }

        if (up1PSR == null || up1PSR.sharedMaterial == null)
        {
            Debug.LogError("‘1up’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (down2PSR == null || down2PSR.sharedMaterial == null)
        {
            Debug.LogError("‘2down’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (down3PSR == null || down3PSR.sharedMaterial == null)
        {
            Debug.LogError("‘3down’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (up4PSR == null || up4PSR.sharedMaterial == null)
        {
            Debug.LogError("‘4up’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (up5PSR == null || up5PSR.sharedMaterial == null)
        {
            Debug.LogError("‘5up’节点ParticleSystemRenderer组件或材质丢失");
            return;
        }

        if (textImg.material == null)
        {
            Debug.LogError("‘text’节点材质丢失");
            return;
        }

        if (floorImg.material == null)
        {
            Debug.LogError("‘floor’节点材质丢失");
            return;
        }
        
        textImg.material.SetColor(AnimationBroadcastInfo.TextColorName, targetParams.textColor);
        textImg.material.SetFloat(AnimationBroadcastInfo.TextGrayScaleName,  targetParams.textGrayScale);
        floorImg.material.SetColor(AnimationBroadcastInfo.BGMainColorName,   targetParams.bgMainColor);
        floorImg.material.SetColor(AnimationBroadcastInfo.BGSecondColorName, targetParams.bgSecondColor);
        floorImg.material.SetColor(AnimationBroadcastInfo.BGThirdColorName,  targetParams.bgThirdColor );
        floorImg.material.SetColor(AnimationBroadcastInfo.BGSpeedColorName,targetParams.bgSpeedColor );
        floorImg.material.SetColor(AnimationBroadcastInfo.BGRimColorName,targetParams.bgRimColor);
        floorImg.material.SetColor(AnimationBroadcastInfo.BGRimSecondColorName, targetParams.bgRimSecondColor);
        up1PSR.sharedMaterial.SetColor(AnimationBroadcastInfo.EffectColorName,  targetParams.effect01Color );
        down2PSR.sharedMaterial.SetColor(AnimationBroadcastInfo.EffectColorName,    targetParams.effect02Color );
        down3PSR.sharedMaterial.SetColor(AnimationBroadcastInfo.EffectColorName,   targetParams.effect03Color );
        up4PSR.sharedMaterial.SetColor(AnimationBroadcastInfo.EffectColorName, targetParams.effect04Color );
        up5PSR.sharedMaterial.SetColor(AnimationBroadcastInfo.EffectColorName,  targetParams.effect05Color);

        SetParticleSystemStartColor(up1.gameObject, targetParams.effect01StartColor);
        SetParticleSystemStartColor(down2.gameObject, targetParams.effect01StartColor);
        SetParticleSystemStartColor(down3.gameObject, targetParams.effect01StartColor);
        SetParticleSystemStartColor(up4.gameObject, targetParams.effect01StartColor);
        SetParticleSystemStartColor(up5.gameObject, targetParams.effect01StartColor);
        
        
    }
    public void SetParticleSystemStartColor(GameObject obj, Color color)
    {
        var ps = obj.TryGetComponent<ParticleSystem>();
        if (ps == null)
        {
            Diagnostic.Error("The ParticleSystem Component is missing");
            return;
        }

        var main = ps.main;
        main.startColor= new Color(color.r,color.g,color.b,color.a);
    }
    int mode = 0;
    private void OnGUI()
    {
        GUI.enabled = true;
        //主宰比赛屏幕特效暂时回滚
        mode = GUILayout.Toolbar(mode, new string[] { "播报",/* "ZKQC特效", "ZZBS特效", "JYBL特效" ,"ZZQC特效"*/});

        EditorGUI.BeginChangeCheck();
        targetSet = EditorGUILayout.IntField("赛季（Set）", targetSet);
        targetParams.heroGroup = EditorGUILayout.IntField("英雄组（HeroGroup）", targetParams.heroGroup);
        targetParams.heroName = EditorGUILayout.TextField("英雄名称（HeroName）", targetParams.heroName);
        targetParams.heroAudioBank = EditorGUILayout.TextField("英雄音频Bank名称（heroAudioBank）", targetParams.heroAudioBank);
        targetParams.heroAudioName = EditorGUILayout.TextField("英雄音频事件名称（heroAudioName）", targetParams.heroAudioName);
        switch (mode)
        {
            case 0:
            {
                targetParams.heroImgName = EditorGUILayout.TextField("英雄图片名称（HeroImageName）", targetParams.heroImgName);
                targetParams.textColor = EditorGUILayout.ColorField("文字颜色（TextColor）", targetParams.textColor);
                targetParams.textGrayScale = EditorGUILayout.FloatField("文字灰度（TextGrayScale）", targetParams.textGrayScale);
                targetParams.bgMainColor = EditorGUILayout.ColorField("floor节点Color1", targetParams.bgMainColor);
                targetParams.bgSecondColor = EditorGUILayout.ColorField("floor节点Color2", targetParams.bgSecondColor);
                targetParams.bgThirdColor = EditorGUILayout.ColorField("floor节点Color3", targetParams.bgThirdColor);
                targetParams.bgSpeedColor = EditorGUILayout.ColorField("floor节点SpeedColor", targetParams.bgSpeedColor);
                targetParams.bgRimColor = EditorGUILayout.ColorField("floor节点RimColor", targetParams.bgRimColor);
                targetParams.bgRimSecondColor = EditorGUILayout.ColorField("floor节点RimColor2", targetParams.bgRimSecondColor);
                targetParams.effect01Color = EditorGUILayout.ColorField("EffectRoot.1up节点GrayColor", targetParams.effect01Color);
                targetParams.effect02Color = EditorGUILayout.ColorField("EffectRoot.2down节点GrayColor", targetParams.effect02Color);
                targetParams.effect03Color = EditorGUILayout.ColorField("EffectRoot.3down节点GrayColor", targetParams.effect03Color);
                targetParams.effect04Color = EditorGUILayout.ColorField("EffectRoot.4up节点GrayColor", targetParams.effect04Color);
                targetParams.effect05Color = EditorGUILayout.ColorField("EffectRoot.5up节点GrayColor", targetParams.effect05Color);
                targetParams.effect01StartColor = EditorGUILayout.ColorField("EffectRoot.1up节点的StartColor", targetParams.effect01StartColor);
                break;
            }
            case 1:
            {
                targetParams.ZKQCSmokeColor = EditorGUILayout.ColorField("ZKQC中Smoke节点材质Color", targetParams.ZKQCSmokeColor);
                targetParams.ZKQCSmokezkqcColor = EditorGUILayout.ColorField("ZKQC中Smoke_zkqc节点材质Color", targetParams.ZKQCSmokezkqcColor);
                break;
            }

            case 2:
            {
                targetParams.ZZBSSmokeColor = EditorGUILayout.ColorField("ZZBS中Smoke节点材质颜色", targetParams.ZZBSSmokeColor);
                targetParams.ZZBSDaoguangColor = EditorGUILayout.ColorField("ZZBS中Daoguang节点材质颜色", targetParams.ZZBSDaoguangColor);
                break;
            }
            case 3:
            {
                targetParams.JYBLLeftColor = EditorGUILayout.ColorField("JYBL中Left节点材质颜色", targetParams.JYBLLeftColor);
                targetParams.JYBLSSSHEffectColor = EditorGUILayout.ColorField("JYBL中SSSH节点材质颜色", targetParams.JYBLSSSHEffectColor);
                break;
            }
            case 4:
            {
          
                targetParams.SSSHSmokeSSShdibuColor = EditorGUILayout.ColorField("SSSH中Smoke1_SSSHdibu节点材质Color", targetParams.SSSHSmokeSSShdibuColor);
                targetParams.SSSHSmoke2SsshColor = EditorGUILayout.ColorField("SSSH中Smoke2_SSSH节点材质颜色", targetParams.SSSHSmoke2SsshColor);
                break;
            }

        }
        #region 播报
   
        #endregion
        if (EditorGUI.EndChangeCheck())
        {
            ApplyChanges();
        }
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("提取颜色参数"))
        {
            UpdateAnimationBroadcastParams();
            //UpdateJYBLParams();
            //UpdateSSSHParams();
            //UpdateZKQCParams();
            //UpdateZZBSParams();
        }

        if (GUILayout.Button("保存颜色参数"))
        {
            if (CheckValidation())
            {
                SaveAnimationBroadcastParams();
            }
        }
        if (GUILayout.Button("加载数据"))
        {
            if (targetSet <= 0)
            {
                Debug.LogError("赛季填写不正确！");
                return ;
            }

            if (targetParams.heroGroup == 0)
            {
                Debug.LogError("herogroupid不正确！");
                return ;
            }
            
            {
                LoadParameters();
            }
        }
        
        EditorGUILayout.EndHorizontal();
    }

    private void OnDestroy()
    {
        EditorUtility.ClearProgressBar();

        if (needSave)
        {
            AssetDatabase.SaveAssets();
        }
    }
    
    
    private void UpdateSSSHParams()
    {
        var item = FindObjectOfType<AnimationBroadcastImproveSSSH>();
        if (item == null )
        {
            Debug.LogError("请选中神圣守护prefab的父节点!");
            return;
        }
        Transform targetTransform = item.transform;
        Transform Smoke1_SSSHdibu = targetTransform.Find("Partical/Smoke1_SSSHdibu");
        Transform Smoke2_SSSH = targetTransform.Find("Partical/Smoke2_SSSH");
        if (Smoke1_SSSHdibu == null)
        {
            Debug.LogError("未找到Smoke1_SSSHdibu节点，请确认是否选中神圣守护Prefab‘Effect_UI_JueNei_BoBao_pingmu_SSSH’父节点");
            return;
        }

        if (Smoke2_SSSH == null)
        {
            Debug.LogError("未找到Smoke2_SSSH节点，请确认是否选中神圣守护Prefab‘Effect_UI_JueNei_BoBao_pingmu_SSSH’父节点");
            return;
        }

        var Smoke1_SSSHdibuPS = Smoke1_SSSHdibu.GetComponent<ParticleSystem>();
        if (Smoke1_SSSHdibuPS == null)
        {
            Debug.LogError("Smoke1_SSSHdibu节点ParticleSystem组件缺失");
            return;
        }

        var Smoke2_SSSHPS = Smoke2_SSSH.GetComponent<ParticleSystem>();
        if (Smoke2_SSSHPS == null)
        {
            Debug.LogError("Smoke2_SSSH节点ParticleSystem组件缺失");
            return;
        }
        
        targetParams.SSSHSmokeSSShdibuColor = Smoke1_SSSHdibuPS.main.startColor.color;
        targetParams.SSSHSmoke2SsshColor = Smoke2_SSSHPS.main.startColor.color;
        
    }
    
    
     private void UpdateZKQCParams()
     {
         var item = FindObjectOfType<AnimationBroadcastImproveZKQC>();
         if (item == null)
         {
             Debug.LogError("请选中ZKQC的父节点!");
             return;
         }
         Transform Smoke1 =      item.transform.Find("Partical/Smoke1");
        Transform Smoke1_zkqc = item.transform.Find("Partical/Smoke1_zkqc");
        if (Smoke1 == null)
        {
            Debug.LogError("未找到Smoke1节点，请确认是否选中掌控全场Prefab‘Effect_UI_JueNei_BoBao_pingmu_ZKQC’的父节点");
            return;
        }

        if (Smoke1_zkqc == null)
        {
            Debug.LogError("未找到Smoke1_zkqc节点，请确认是否选中掌控全场Prefab‘Effect_UI_JueNei_BoBao_pingmu_ZKQC’的父节点");
            return;
        }

        var Smoke1PS = Smoke1.GetComponent<ParticleSystem>();
        if (Smoke1PS == null)
        {
            Debug.LogError("Smoke1_SSSHdibu节点ParticleSystem组件缺失");
            return;
        }

        var Smoke1_zkqcPS = Smoke1_zkqc.GetComponent<ParticleSystem>();
        if (Smoke1_zkqcPS == null)
        {
            Debug.LogError("Smoke2_SSSH节点ParticleSystem组件缺失");
            return;
        }
        
        targetParams.ZKQCSmokeColor = Smoke1PS.main.startColor.color;
        targetParams.ZKQCSmokezkqcColor= Smoke1_zkqcPS.main.startColor.color;
    }
    
    private void UpdateJYBLParams()
    {
        var item = FindObjectOfType<AnimationBroadcastImproveJYBL>();

        if (item == null)
        {
            Debug.LogError("请选中坚毅壁垒prefab的父节点!");
            return;
        }
        Transform targetTransform = item.transform;
        Transform Left1 = targetTransform.Find("JYBL/Left/Left_1");
        Transform Smoke2_SSSH = targetTransform.Find("Partical/Smoke2_SSSH");
        if (Left1 == null)
        {
            Debug.LogError("未找到Smoke2_SSSH节点，请确认是否选中坚毅壁垒Prefab‘Effect_UI_JueNei_BoBao_pingmu_JYBL’的父节点");
            return;
        }

        if (Smoke2_SSSH == null)
        {
            Debug.LogError("未找到Smoke2_SSSH节点，请确认是否选中坚毅壁垒Prefab‘Effect_UI_JueNei_BoBao_pingmu_JYBL’的父节点");
            return;
        }

        var Left1Image = Left1.GetComponent<ImageFast>();
        if (Left1Image == null)
        {
            Debug.LogError("Smoke1_SSSHdibu节点ImageFast组件缺失");
            return;
        }

        var Smoke2_SSSHPS = Smoke2_SSSH.GetComponent<ParticleSystem>();
        if (Smoke2_SSSHPS == null)
        {
            Debug.LogError("Smoke2_SSSH节点ParticleSystem组件缺失");
            return;
        }

        targetParams.JYBLLeftColor = Left1Image.material.GetColor("_GrayColor");
        targetParams.JYBLSSSHEffectColor = Smoke2_SSSHPS.main.startColor.color;
    }


    private void UpdateZZBSParams()
    {
        var item = FindObjectOfType< AnimationBroadcastImproveZZBS>();
        if (item == null)
        {
            Debug.LogError("请选中主宰比赛prefab的父节点!");
            return;
        }
        
        Transform targetTransform = item.transform;
        Transform daoguang_right_01 = targetTransform.Find("Partical/daoguang_right_01");
        Transform Smoke1_SSSHdibu = targetTransform.Find("Partical/Smoke1_SSSHdibu");
        if (daoguang_right_01 == null)
        {
            Debug.LogError("未找到daoguang_right_01节点，请确认是否选中主宰比赛Prefab‘Effect_UI_JueNei_BoBao_pingmu_ZZBS’的父节点");
            return;
        }

        if (Smoke1_SSSHdibu == null)
        {
            Debug.LogError("未找到Smoke2_SSSH节点，请确认是否选中主宰比赛Prefab‘Effect_UI_JueNei_BoBao_pingmu_ZZBS’的父节点");
            return;
        }

        var daoguang_right_01PS = daoguang_right_01.GetComponent<ParticleSystem>();
        if (daoguang_right_01PS == null)
        {
            Debug.LogError("daoguang_right_01节点ParticleSystem组件缺失");
            return;
        }

        var Smoke1_SSSHdibuPS = Smoke1_SSSHdibu.GetComponent<ParticleSystem>();
        if (Smoke1_SSSHdibuPS == null)
        {
            Debug.LogError("Smoke2_SSSH节点ParticleSystem组件缺失");
            return;
        }

        targetParams.ZZBSDaoguangColor = daoguang_right_01PS.main.startColor.color;
        targetParams.ZZBSSmokeColor = Smoke1_SSSHdibuPS.main.startColor.color;
    }
    
}
