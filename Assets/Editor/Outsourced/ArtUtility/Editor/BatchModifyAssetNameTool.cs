using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

public class BatchModifyAssetNameTool : ScriptableWizard
{
    public string 文件名 = "";

    private static BatchModifyAssetNameTool instance = null;

    //显示窗体
    [MenuItem("Assets/批量重命名", false, 2)]
    private static void ShowWindow()
    {
        if (instance == null)
        {
            instance = ScriptableWizard.DisplayWizard<BatchModifyAssetNameTool>("文件批量改名工具", "改名", "取消");
        }
    }

    //显示时调用
    private void OnEnable()
    {
        Debug.Log("OnEnable");
    }

    //更新时调用
    private void OnWizardUpdate()
    {
        Debug.Log("OnWizardUpdate");

        if (string.IsNullOrEmpty(文件名))
        {
            errorString = "请输入文件名";
        }
        else
        {
            StringBuilder sb = new StringBuilder();
            List<Object> selectedObjects = new List<Object>(Selection.objects);
            selectedObjects.Sort((l, r) => l.name.CompareTo(r.name));
            for (int i = 0; i < selectedObjects.Count; ++i)
            {
                var o = selectedObjects[i];
                sb.Append(o.name).Append(" -> ").Append(文件名).Append(i.ToString().PadLeft(3, '0')).AppendLine();
                if (i > 10 && (i + 1) < selectedObjects.Count)
                {
                    sb.AppendLine("more...");
                    break;
                }
            }
            helpString = sb.ToString();
            errorString = "";
        }
    }

    //点击确定按钮时调用
    private void OnWizardCreate()
    {
        List<Object> selectedObjects = new List<Object>(Selection.objects);
        selectedObjects.Sort((l, r) => l.name.CompareTo(r.name));
        for (int i = 0; i < selectedObjects.Count; ++i)
        {
            var o = selectedObjects[i];
            var path = AssetDatabase.GetAssetPath(o);
            var dir = Path.GetDirectoryName(path).Replace("\\", "/");
            var fileName = Path.GetFileNameWithoutExtension(path);
            var ex = Path.GetExtension(path);

            var newPath = dir + "/" + 文件名 + i.ToString().PadLeft(3, '0') + ex;

            Debug.Log(path + " -> " + newPath);
            File.Move(path, newPath);
            File.Move(path + ".meta", newPath + ".meta");
        }
        AssetDatabase.Refresh();
    }

    //点击第二个按钮时调用
    private void OnWizardOtherButton()
    {
        Close();
    }

    //当ScriptableWizard需要更新其GUI时，将调用此函数以绘制内容
    //为GUI绘制提供自定义行为，默认行为是按垂直方向排列绘制所有公共属性字段
    //一般不重写该方法，按照默认绘制方法即可
    protected override bool DrawWizardGUI()
    {
        return base.DrawWizardGUI();
    }

    //隐藏时调用
    private void OnDisable()
    {

    }

    //销毁时调用
    private void OnDestroy()
    {
        instance = null;

    }
}

