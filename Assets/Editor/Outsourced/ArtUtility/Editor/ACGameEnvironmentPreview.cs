/********************************************************************************
 *	创建人:     刘帅
 *	创建时间:   2017-07-20
 *	
 *	功能说明:   全局环境效果的预览工具脚本
 *	
 *	修改记录:   2018-07-20  添加环境变化预览与批量修改的编辑界面
 *	            2018-07-24  添加了unity标记场景为被修改
 *	            2018-08-01  添加了选择使用两个雾效参数的按钮
 *	            2018-08-02  添加了修改天空盒变化的功能
 *	实现思路:   1.申明变量UI使用的Rect信息
 *	            2.在Draw()为前缀的函数下实现数据UI以及其获取数据
 *	            3.通过CopyComponent()与RevokeComponent()实现储存与卸载修改过的数据
 *	            4.通过PasteComponents()实现调用所有OnTriggerEnter()的内容进行环境的渐变
*********************************************************************************/
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

public class ACGameEnvironmentPreview : EditorWindow
{
    #region Properties
    private static ACGameEnvironmentPreview window = null;
    private static Rect rect_window = new Rect(200, 200, 400, 800);
    private static Vector2 size_window = new Vector2(400, 800);

    //获取环境触发器Button
    private Rect rect_collecter = new Rect(240, 10, 150, 50);
    [SerializeField] private ACGameChangeFogColorOnTrigger[] triggers = null;
    private string[] triggerNames = null;
    private bool[] isLinears = null;
    [SerializeField] private Dictionary<int, ACGameEnvironmentContext> copyComponents;
    [SerializeField] private Dictionary<int, float> copyDuration;
    private string[] copBtn = new string[] { "拷贝", "粘贴" };
    private int copIndex = 0;

    //所有触发器
    private Rect rect_content = new Rect(10, 10, 220, 300);
    private Rect rect_contentRegion = new Rect(0, 0, 200, 100);
    private Rect CONTENTREGION
    {
        get
        {
            if (triggers != null && triggers.Length != 0)
            {
                //21是正好一个Button的高度
                rect_contentRegion.height = 21 * triggers.Length;
            }
            else
            {
                rect_contentRegion.height = 21;
            }
            return rect_contentRegion;
        }
    }
    private Vector2 pos_contentSVBar = Vector2.zero;
    private int selectedTriggerIndex = 0;

    //环境效果
    private string[] effects = new string[] { "光照", "雾效", "后处理" };
    private Rect rect_effects = new Rect(240, 70, 150, 240);
    private int selectGrid = -1;
    private bool effectPreview = false;

    //对应效果的处理面板
    private Rect rect_setting = new Rect(10, 320, 380, 470);
    //private Rect rect_settingRegion = new Rect(0, 0, 380, 1000);
    //private Vector2 pos_settingSVBar = Vector2.zero;
    private Rect rect_effectTarget = new Rect(20, 325, 360, 30);
    private GUIStyle style_effectTarget = new GUIStyle();
    private Rect rect_effectHint = new Rect(20, 360, 360, 30);
    private Rect rect_copyComponent = new Rect(260, 760, 40, 20);
    private Rect rect_revokeComponent = new Rect(300, 760, 40, 20);
    private Rect rect_precess = new Rect(340, 760, 40, 20);
    //光照效果设置
    private Rect rect_dirction = new Rect(20, 430, 360, 30);
    private Rect rect_sunColor = new Rect(20, 465, 360, 20);
    private Rect rect_intensity = new Rect(20, 500, 360, 20);
    private Rect rect_indirectMul = new Rect(20, 535, 360, 20);
    private Rect rect_ambientColor = new Rect(20, 570, 360, 20);
    //天空盒效果设置
    private Rect rect_skyboxCol = new Rect(20, 605, 360, 20);
    private Rect rect_skyboxMul = new Rect(20, 640, 360, 20);
    //雾效效果设置
    private Rect rect_startTitle = new Rect(20, 430, 50, 30);
    private Rect rect_middleTitle = new Rect(20, 465, 50, 30);
    private Rect rect_endTitle = new Rect(20, 500, 50, 30);
    private Rect rect_bottomTitle = new Rect(20, 535, 50, 30);
    private Rect rect_upTitle = new Rect(20, 570, 50, 30);
    private Rect rect_fogDurationTime = new Rect(20, 395, 360, 30);
    private Rect rect_fogStart = new Rect(90, 430, 70, 30);
    private Rect rect_fogMiddle = new Rect(90, 465, 70, 30);
    private Rect rect_fogEnd = new Rect(90, 500, 70, 30);
    private Rect rect_fogBottom = new Rect(90, 535, 70, 30);
    private Rect rect_fogUp = new Rect(90, 570, 70, 30);
    private Rect rect_fogStartValue = new Rect(170, 430, 40, 20);
    private Rect rect_fogMiddleValue = new Rect(170, 465, 40, 20);
    private Rect rect_fogEndValue = new Rect(170, 500, 40, 20);
    private Rect rect_fogBottomValue = new Rect(170, 535, 40, 20);
    private Rect rect_fogUpValue = new Rect(170, 570, 40, 20);

    private Rect rect_StartColorTitle = new Rect(220, 430, 50, 30);
    private Rect rect_MiddleColorTitle = new Rect(220, 465, 50, 30);
    private Rect rect_EndColorTitle = new Rect(220, 500, 50, 30);
    private Rect rect_TopColorTitle = new Rect(220, 535, 50, 30);
    private Rect rect_LinearTitle = new Rect(220, 570, 50, 30);
    private Rect rect_StartColor = new Rect(280, 430, 100, 20);
    private Rect rect_MiddleColor = new Rect(280, 465, 100, 20);
    private Rect rect_EndColor = new Rect(280, 500, 100, 20);
    private Rect rect_TopColor = new Rect(280, 535, 100, 20);
    private Rect rect_LinearButton = new Rect(280, 570, 20, 20);

    #endregion

    #region Initialization
    [MenuItem("Window/环境预览工具")]
    public static void ShowWindow()
    {
        if (null == window)
        {
            window = EditorWindow.GetWindow<ACGameEnvironmentPreview>(true, "环境预览工具");
            window.position = rect_window;
            window.minSize = size_window;
            window.maxSize = size_window;
        }
    }
    #endregion

    #region Draw
    private void OnGUI()
    {
        GUI.color = new Color(0.5f, 0.5f, 0.7f);
        GUI.Box(rect_content, string.Empty);
        GUI.color = Color.white;
        GUI.Box(rect_setting, string.Empty);
        GUI.color = Color.white;
        if (GUI.Button(rect_collecter, "获取环境触发器"))
        {
            CollectTriggers();
        }
        DrawContent();
        DrawEffectButton();
        DrawEffects();
    }
    private void OnEnable()
    {
        style_effectTarget.alignment = TextAnchor.MiddleCenter;
        copIndex = 0;
    }
    private void OnDisable()
    {
        effectPreview = false;
        Clear();
    }
    private void DrawContent()
    {
        pos_contentSVBar = GUI.BeginScrollView(rect_content, pos_contentSVBar, CONTENTREGION);
        //循环显示处理的Trigger对象
        if (!SecurityDoor())
        {
            GUI.EndScrollView();
            return;
        }
        for (int i = 0; i < triggerNames.Length; ++i)
        {
            GUILayout.BeginHorizontal();
            if (GUILayout.Button(triggerNames[i], GUILayout.Width(196)))
            {
                selectedTriggerIndex = i;
            }
            GUILayout.EndHorizontal();
        }
        GUI.EndScrollView();
    }
    private void DrawEffectButton()
    {
        EditorGUI.BeginDisabledGroup(!effectPreview);
        selectGrid = GUI.SelectionGrid(rect_effects, selectGrid, effects, 2);
        EditorGUI.EndDisabledGroup();
    }
    private void DrawEffects()
    {
        if (!SecurityDoor())
        {
            return;
        }
        GUI.color = new Color(0.4f, 0.6f, 0.6f);
        GUI.Box(rect_effectTarget, string.Empty);
        GUI.Label(rect_effectTarget, triggerNames[selectedTriggerIndex], style_effectTarget);
        GUI.color = Color.white;
        switch (selectGrid)
        {
            case 0:
                {
                    LightingEffect();
                }
                break;
            case 1:
                {
                    FogEffect();
                }
                break;
            case 2:
                {
                    PostEffect();
                }
                break;
            default:
                {
                    GUI.color = new Color(1f, 0.8f, 0.2f);
                    GUI.Box(rect_effectHint, string.Empty);
                    GUI.Label(rect_effectHint, "未选中效果", style_effectTarget);
                    GUI.color = Color.white;
                }
                break;
        }
        copIndex = Application.isPlaying ? 0 : 1;
        if (GUI.Button(rect_copyComponent, copBtn[copIndex]))
        {
            if (Application.isPlaying)
            {
                CopyComponent();
            }
            else
            {
                PasteComponents();
            }
        }
        if (GUI.Button(rect_revokeComponent, "撤销"))
        {
            RevokeComponent();
        }
        if (GUI.Button(rect_precess, "预览"))
        {
            triggers[selectedTriggerIndex].OnTriggerEnterInEditor();
        }
    }
    private void FogEffect()
    {
        if (!SecurityDoor())
        {
            return;
        }
        GUI.color = new Color(1f, 0.8f, 0.2f);
        GUI.Box(rect_effectHint, string.Empty);
        GUI.Label(rect_effectHint, "雾效环境设置", style_effectTarget);
        GUI.color = Color.white;
        triggers[selectedTriggerIndex].p_durationTime = EditorGUI.Slider(rect_fogDurationTime, "延迟时间", triggers[selectedTriggerIndex].p_durationTime, 1.0f, 10.0f);
        GUI.Label(rect_LinearTitle, "均匀线性");
        isLinears[selectedTriggerIndex] = EditorGUI.Toggle(rect_LinearButton, isLinears[selectedTriggerIndex]);
        EditorGUI.BeginDisabledGroup(isLinears[selectedTriggerIndex]);
        GUI.Label(rect_middleTitle, "雾效中部");
        triggers[selectedTriggerIndex].triggerContext.p_fogMiddle = GUI.HorizontalSlider(rect_fogMiddle, triggers[selectedTriggerIndex].triggerContext.p_fogMiddle, -20.0f, 3000.0f);
        triggers[selectedTriggerIndex].triggerContext.p_fogMiddle = EditorGUI.FloatField(rect_fogMiddleValue, triggers[selectedTriggerIndex].triggerContext.p_fogMiddle);
        GUI.Label(rect_MiddleColorTitle, "中部颜色");
        triggers[selectedTriggerIndex].triggerContext.p_fogMiddleColor = EditorGUI.ColorField(rect_MiddleColor, triggers[selectedTriggerIndex].triggerContext.p_fogMiddleColor);
        EditorGUI.EndDisabledGroup();
        GUI.Label(rect_startTitle, "雾效开始");
        GUI.Label(rect_endTitle, "雾效结束");
        GUI.Label(rect_bottomTitle, "高度起始");
        GUI.Label(rect_upTitle, "高度结束");
        triggers[selectedTriggerIndex].triggerContext.p_fogStart = GUI.HorizontalSlider(rect_fogStart, triggers[selectedTriggerIndex].triggerContext.p_fogStart, -20.0f, 3000.0f);
        triggers[selectedTriggerIndex].triggerContext.p_fogStart = EditorGUI.FloatField(rect_fogStartValue, triggers[selectedTriggerIndex].triggerContext.p_fogStart);
        triggers[selectedTriggerIndex].triggerContext.p_fogEnd = GUI.HorizontalSlider(rect_fogEnd, triggers[selectedTriggerIndex].triggerContext.p_fogEnd, -20.0f, 3000.0f);
        triggers[selectedTriggerIndex].triggerContext.p_fogEnd = EditorGUI.FloatField(rect_fogEndValue, triggers[selectedTriggerIndex].triggerContext.p_fogEnd);
        triggers[selectedTriggerIndex].triggerContext.p_fogBottom = GUI.HorizontalSlider(rect_fogBottom, triggers[selectedTriggerIndex].triggerContext.p_fogBottom, -20.0f, 3000.0f);
        triggers[selectedTriggerIndex].triggerContext.p_fogBottom = EditorGUI.FloatField(rect_fogBottomValue, triggers[selectedTriggerIndex].triggerContext.p_fogBottom);
        triggers[selectedTriggerIndex].triggerContext.p_fogUp = GUI.HorizontalSlider(rect_fogUp, triggers[selectedTriggerIndex].triggerContext.p_fogUp, -20.0f, 3000.0f);
        triggers[selectedTriggerIndex].triggerContext.p_fogUp = EditorGUI.FloatField(rect_fogUpValue, triggers[selectedTriggerIndex].triggerContext.p_fogUp);
        GUI.Label(rect_StartColorTitle, "前部颜色");
        GUI.Label(rect_EndColorTitle, "后部颜色");
        GUI.Label(rect_TopColorTitle, "高度颜色");
        triggers[selectedTriggerIndex].triggerContext.p_fogStartColor = EditorGUI.ColorField(rect_StartColor, triggers[selectedTriggerIndex].triggerContext.p_fogStartColor);
        triggers[selectedTriggerIndex].triggerContext.p_fogEndColor = EditorGUI.ColorField(rect_EndColor, triggers[selectedTriggerIndex].triggerContext.p_fogEndColor);
        triggers[selectedTriggerIndex].triggerContext.p_fogTopColor = EditorGUI.ColorField(rect_TopColor, triggers[selectedTriggerIndex].triggerContext.p_fogTopColor);
        if (isLinears[selectedTriggerIndex])
        {
            triggers[selectedTriggerIndex].triggerContext.p_fogMiddle = (triggers[selectedTriggerIndex].triggerContext.p_fogStart + triggers[selectedTriggerIndex].triggerContext.p_fogEnd) * 0.5f;
            triggers[selectedTriggerIndex].triggerContext.p_fogMiddleColor = (triggers[selectedTriggerIndex].triggerContext.p_fogStartColor + triggers[selectedTriggerIndex].triggerContext.p_fogEndColor) * 0.5f;
        }
    }
    private void LightingEffect()
    {
        if (!SecurityDoor())
        {
            return;
        }
        GUI.color = new Color(1f, 0.8f, 0.2f);
        GUI.Box(rect_effectHint, string.Empty);
        GUI.Label(rect_effectHint, "光照环境设置", style_effectTarget);
        GUI.color = Color.white;
        triggers[selectedTriggerIndex].p_durationTime = EditorGUI.Slider(rect_fogDurationTime, "延迟时间", triggers[selectedTriggerIndex].p_durationTime, 1.0f, 10.0f);
        triggers[selectedTriggerIndex].triggerContext.p_direction = EditorGUI.Vector3Field(rect_dirction, "光照方向", triggers[selectedTriggerIndex].triggerContext.p_direction);
        triggers[selectedTriggerIndex].triggerContext.p_sunColor = EditorGUI.ColorField(rect_sunColor, "光照颜色", triggers[selectedTriggerIndex].triggerContext.p_sunColor);
        triggers[selectedTriggerIndex].triggerContext.p_intensity = EditorGUI.Slider(rect_intensity, "直接光强度", triggers[selectedTriggerIndex].triggerContext.p_intensity, 0.0f, 8.0f);
        triggers[selectedTriggerIndex].triggerContext.p_indirectMul = EditorGUI.Slider(rect_indirectMul, "间接光强度", triggers[selectedTriggerIndex].triggerContext.p_indirectMul, 0.0f, 16.0f);
        triggers[selectedTriggerIndex].triggerContext.p_ambientColor = EditorGUI.ColorField(rect_ambientColor, "环境光颜色", triggers[selectedTriggerIndex].triggerContext.p_ambientColor);
        triggers[selectedTriggerIndex].triggerContext.p_skyboxCol = EditorGUI.ColorField(rect_skyboxCol, "天空盒颜色", triggers[selectedTriggerIndex].triggerContext.p_skyboxCol);
        triggers[selectedTriggerIndex].triggerContext.p_skyboxMul = EditorGUI.Slider(rect_skyboxMul, "天空盒强度", triggers[selectedTriggerIndex].triggerContext.p_skyboxMul, 0.0f, 0.8f);
    }
    private void PostEffect()
    {
        if (!SecurityDoor())
        {
            return;
        }
        GUI.color = new Color(1f, 0.8f, 0.2f);
        GUI.Box(rect_effectHint, string.Empty);
        GUI.Label(rect_effectHint, "开发中", style_effectTarget);
        GUI.color = Color.white;
    }
    #endregion

    #region Functions
    private void CollectTriggers()
    {
        Clear();
        triggers = FindObjectsOfType<ACGameChangeFogColorOnTrigger>();
        if (triggers == null || triggers.Length == 0)
        {
            return;
        }
        triggerNames = new string[triggers.Length];
        isLinears = new bool[triggers.Length];
        for (int i = 0; i < triggers.Length; ++i)
        {
            triggerNames[i] = triggers[i].transform.name;
            isLinears[i] = false;
        }
        //
        for (int i = 0; i < triggers.Length; ++i)
        {
            Debug.Log(triggerNames[i]);
            Debug.Log(triggers[i].gameObject.GetInstanceID());
        }
        //
        copyComponents = new Dictionary<int, ACGameEnvironmentContext>();
        copyDuration = new Dictionary<int, float>();
        effectPreview = true;
    }
    private bool SecurityDoor()
    {
        if (triggers == null || triggerNames == null 
            || triggers.Length == 0 || triggerNames.Length == 0 
            || isLinears == null || isLinears.Length == 0 
            || copyComponents == null || copyDuration == null)
        {
            effectPreview = false;
            selectedTriggerIndex = 0;
            selectGrid = -1;
            return false;
        }
        return true;
    }
    private void Clear()
    {
        if (null != triggers)
        {
            for (int i = 0; i < triggers.Length; ++i)
            {
                triggers[i] = null;
            }
            triggers = null;
        }
        if (null != triggerNames)
        {
            for (int i = 0; i < triggerNames.Length; ++i)
            {
                triggerNames[i] = string.Empty;
            }
            triggerNames = null;
        }
        if (null != copyComponents)
        {
            copyComponents.Clear();
            copyComponents = null;
        }
    }
    private void CopyComponent()
    {
        if (!SecurityDoor())
        {
            return;
        }
        if (selectedTriggerIndex >= 0)
        {
            int key = triggers[selectedTriggerIndex].GetInstanceID();
            if (copyComponents.ContainsKey(key))
            {
                //UnityEditorInternal.ComponentUtility.CopyComponent(triggers[selectedTriggerIndex]);
                //Component oldComponent = triggers[selectedTriggerIndex].GetComponent(triggers[selectedTriggerIndex].GetType());
                copyComponents[key].CopyData(triggers[selectedTriggerIndex].triggerContext);
                copyDuration[key] = triggers[selectedTriggerIndex].p_durationTime;
            }
            else
            {
                copyComponents.Add(key, new ACGameEnvironmentContext(triggers[selectedTriggerIndex].triggerContext));
                copyDuration.Add(key, triggers[selectedTriggerIndex].p_durationTime);
            }
        }
    }
    private void RevokeComponent()
    {
        if (!SecurityDoor())
        {
            return;
        }
        if (selectedTriggerIndex >= 0)
        {
            if (copyComponents.ContainsKey(selectedTriggerIndex))
            {
                copyComponents.Remove(selectedTriggerIndex);
            }
        }
    }
    private void PasteComponents()
    {
        if (!SecurityDoor())
        {
            return;
        }
        if (copyComponents.Count > 0)
        {
            foreach (var it in copyComponents)
            {
                ACGameChangeFogColorOnTrigger trigger = EditorUtility.InstanceIDToObject(it.Key) as ACGameChangeFogColorOnTrigger;
                if (trigger != null)
                {
                    trigger.triggerContext.CopyData(it.Value);
                }
            }
            foreach (var it in copyDuration)
            {
                ACGameChangeFogColorOnTrigger trigger = EditorUtility.InstanceIDToObject(it.Key) as ACGameChangeFogColorOnTrigger;
                if (trigger != null)
                {
                    trigger.p_durationTime = it.Value;
                }
            }
            EditorSceneManager.MarkAllScenesDirty();
            //EditorApplication.MarkSceneDirty();
        }
    }
    #endregion
}
