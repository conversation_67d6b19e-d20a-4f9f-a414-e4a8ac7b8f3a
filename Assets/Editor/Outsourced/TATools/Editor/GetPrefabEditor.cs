//using System;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;
using UnityEditor.IMGUI.Controls;

//UnityEditor.TextureUtil
public class GetPrefabEditor : EditorWindow
{

    enum InspectType
    {
        OutGameScene, Hero, InGameScene
    };
    enum InspectPrefabType
    {
        Mesh, Material, Texture
    };
    enum InspectCharacterType
    {
        InGame,OutGame
    };
    string[] inspectToolbarStrings = { "局外场景", "小小英雄", "局内场景" };
    string[] inspectCharacterStrings = { "局内角色", "局外角色"};
    string[] inspectToolbarPrefabStrings = { "模型", "材质", "贴图" };
    static string[] checkPath = new string[] { "Assets/Art_TFT_Raw/little_legend_res/scene", "Assets/Art_TFT_Raw/little_legend_res/model", "Assets/Art_TFT_Raw/little_legend_res/model", ConstVar.MAP_DIR };
    //static string[] checkPath = new string[] { "Assets/Art_TFT_Raw/little_legend_res/scene"};

    InspectType ActiveInspectType = InspectType.OutGameScene;
    InspectCharacterType ActiveCharacterInspectType = InspectCharacterType.InGame;
    InspectPrefabType ActivePrefabInspectType = InspectPrefabType.Mesh;
    Vector2 ScrollPos = new Vector2(0, 0);
    Vector2 ScrollPrefabPos = new Vector2(0, 0);
    float ThumbnailWidth = 40;
    float ThumbnailHeight = 40;
    bool ctrlPressed = false;
    bool ctrlPrefabPressed = false;
    string searchPrefab;
    bool isSearch = false;
    bool isUporDown = false;
    bool isDebug = false;
    static bool isScan = false;
    int RowHeight = 500;
    public static bool isChanged = true;// 是否需要更新
    public static bool isOutGameScan = false;// 是否需要更新
    public static bool isOutCharScan = false;// 是否需要更新
    public static bool isInCharScan = false;// 是否需要更新
    public static bool isInGameScan = false;// 是否需要更新

    public static bool[] ScanList = { isOutGameScan, isOutCharScan, isInCharScan, isInGameScan };
    private bool initializedGUIStyle = false;
    //工具栏按钮样式
    private GUIStyle toolbarButtonGUIStyle;
    //工具栏样式
    private GUIStyle toolbarGUIStyle;
    private AssetTreeView m_AssetTreeView;
    public static AssetTreeDetailView m_PrefabTreeView;
    public static bool needUpdate = false;
    public bool isFirstJump = false;
    [SerializeField]
    private TreeViewState m_TreeViewState;
    [SerializeField]
    public static TreeViewState m_PrefabViewState;

    public static GetPrefabData data = new GetPrefabData();
    int debug_selectShaderIndex = 0;


    // Start is called before the first frame update
    [MenuItem("Window/美术资产查看工具")]
    static void Init()
    {
        GetPrefabEditor window = (GetPrefabEditor)EditorWindow.GetWindow(typeof(GetPrefabEditor));
        isScan = false;
        data.Init();

    }

    //初始化GUIStyle
    void InitGUIStyleIfNeeded()
    {
        if (!initializedGUIStyle)
        {
            toolbarButtonGUIStyle = new GUIStyle("ToolbarButton");
            toolbarGUIStyle = new GUIStyle("Toolbar");
            initializedGUIStyle = true;

            data.SetLimitedNum();
        }
    }
    private AssetViewItem CreateTree(GetPrefabData.Prefab prefab,int _depth,ref int elementCount)
    {

        GetPrefabData.AssetDescription asset =  data.GetData(prefab);
        ++elementCount;
        var root = new AssetViewItem{  id = elementCount,
                                       data = asset,
                                       depth = _depth};
        return root;
    }
    private AssetViewItem SelectedItem(List<GetPrefabData.PrefabParent> listShow)
    {
        
        int _depth = 0;
        int elementCount = 0;
        var root = new AssetViewItem { id = elementCount ,depth = -1 };

        foreach (GetPrefabData.PrefabParent prefab in listShow)
        {
            ++elementCount;
            var prefab_root = new AssetViewItem { id = elementCount, depth = _depth };
            if (prefab.prefabs.Count>1)
            {
                
                GetPrefabData.AssetDescription asset = data.GetDataParent(prefab);
                prefab_root = new AssetViewItem { id = elementCount, depth = _depth ,data = asset };
                foreach (GetPrefabData.Prefab prefab1 in prefab.prefabs)
                {
                    var child = CreateTree(prefab1, _depth+1, ref elementCount);
                    prefab_root.AddChild(child);
                }
            }
            else
            {
                GetPrefabData.AssetDescription asset = data.GetDataParent(prefab);
                prefab_root = new AssetViewItem { id = elementCount, depth = _depth, data = asset };
                //var child = CreateTree(prefab.prefabs[0], _depth , ref elementCount);
                //prefab_root.AddChild(child);
            }
            root.AddChild(prefab_root);

            //var child = CreateTree(prefab,depth,ref elementCount);
            //root.AddChild(child);
        }
        
        return root;
    }
    private void UpdateAssetTree()
    {
        if(isChanged && data.prefabShowLists.Count != 0)
        {
            var root = SelectedItem(data.prefabShowLists);// 读取资源

            if (m_AssetTreeView == null)
            {
                //初始化TreeView
                if (m_TreeViewState == null)

                    m_TreeViewState = new TreeViewState();

                var headerState = AssetTreeView.CreateDefaultMultiColumnHeaderState(position.width);
                var multiColumnHeader = new MultiColumnHeader(headerState);
                m_AssetTreeView = new AssetTreeView(m_TreeViewState, multiColumnHeader);


            }

            m_AssetTreeView.assetRoot = root;
            //m_AssetTreeView.CollapseAll();
            m_AssetTreeView.Reload();
            isChanged = false;
        }
        
        
    }
    private AssetViewDetailItem CreateDetailTree(GetPrefabData.PrefabDescription prefabDescription, int _depth, ref int elementCount)
    {
        var root = new AssetViewDetailItem
        {
            id = elementCount,
            depth = _depth
        };


        root = new AssetViewDetailItem
        {
            id = elementCount,
            data = prefabDescription,
            depth = _depth
        };
       
        return root;
    }


    private AssetViewDetailItem SelectedPrefabItem(GetPrefabData.Prefab showPrefab)
    {
        int depth = 0;
        int elementCount = 0;
        var root = new AssetViewDetailItem { id = elementCount, depth = -1 };
        switch(data.showDetail)
        {
            case 0:
                
                for (int i = 0; i < showPrefab.prefabDetail.meshDetail.Count; i++)
                {
                    ++elementCount;
                    GetPrefabData.PrefabDescription prefabDescription =  data.ShowPrefab(i,0);
                    var child = CreateDetailTree(prefabDescription,depth, ref elementCount);
                    root.AddChild(child);

                }
                break;
            case 1:
                for (int i = 0; i < showPrefab.prefabDetail.materialDetail.Count; i++)
                {
                    ++elementCount; 
                    GetPrefabData.PrefabDescription prefabDescription = data.ShowPrefab(i, 1);
                    var child = CreateDetailTree(prefabDescription, depth, ref elementCount);
                    root.AddChild(child);
                }
                break;
            case 2:
                
                for (int i = 0; i < showPrefab.prefabDetail.textureDetail.Count; i++)
                {
                    ++elementCount;
                    GetPrefabData.PrefabDescription prefabDescription = data.ShowPrefab(i, 2);
                    var child = CreateDetailTree(prefabDescription, depth, ref elementCount);
                    root.AddChild(child);
                }
                break;
        }
        return root;
    }
    void UpdatePrefabTree()
    {
       
        if (data.showPrefab!=null)
        {
            
            var headerState = AssetTreeDetailView.CreateDefaultMultiColumnHeaderState(position.width);
            var multiColumnHeader = new MultiColumnHeader(headerState);
            
            AssetViewDetailItem root = null;
            root = SelectedPrefabItem(data.showPrefab);
            if (m_PrefabTreeView == null)
            {
                if (m_PrefabViewState == null)
                    m_PrefabViewState = new TreeViewState();

                m_PrefabTreeView = new AssetTreeDetailView(m_PrefabViewState, multiColumnHeader);
            }
            if (root.hasChildren)
            {
                m_PrefabTreeView.assetRoot = root;

            }

            m_PrefabTreeView.CollapseAll();
            m_PrefabTreeView.Reload();
           
        }
        needUpdate = false;
    }
  

    void OnGUI()
    {
        InitGUIStyleIfNeeded();
        UpdateAssetTree();
        if(needUpdate)
        {
            
            UpdatePrefabTree();
        }
        if (m_AssetTreeView != null)
        {
            //绘制Treeview
            m_AssetTreeView.OnGUI(new Rect(0,120, 600, position.height -150  - toolbarGUIStyle.fixedHeight));
            
        }
        ctrlPressed = Event.current.control || Event.current.command;
        //GUILayout.Space(10);
        GUILayout.BeginVertical();
        GUILayout.BeginHorizontal();
        searchPrefab = EditorGUILayout.TextField("搜索Prefab", searchPrefab);
        if (GUILayout.Button("搜索", GUILayout.Width(150)))
        {
            if (searchPrefab != null)
            {
                data.searchPrefab = searchPrefab;
                data.SearchPrefab();
                isSearch = true;
            }
            else
            {
                isSearch = false;
            }

        }

        if (GUILayout.Button("清除", GUILayout.Width(150)))
        {
            searchPrefab = null;
            isSearch = false;
        }
        GUILayout.EndHorizontal();



        GUILayout.BeginHorizontal();
        if (GUILayout.Button("读取资产！", GUILayout.Width(150)))
        {
            data.ScanPrefab();
            data.SetLimitedNum();
            isScan = true;
        }
        if(isScan)
        {
            
            if (GUILayout.Button("检查局外场景(1分钟)", GUILayout.Width(150)))
            {
                data.GetPrefabs(0);
                isChanged = true;
                ScanList[0] = true;
            }
            if (GUILayout.Button("检查角色（3~5分钟）", GUILayout.Width(150)))
            {
                data.GetPrefabs(1);
                data.GetPrefabs(2);
                isChanged = true;
                ScanList[1] = true;
                ScanList[2] = true;
            }
            if (GUILayout.Button("检查局内场景（3~5分钟）", GUILayout.Width(150)))
            {
                data.GetPrefabs(3);
                isChanged = true;
                ScanList[3] = true;
            }
            Color bc = GUI.backgroundColor;
            if(isDebug)
            {
                GUI.backgroundColor = Color.red;
            }
            if (GUILayout.Button("Debug", GUILayout.Width(150)))
            {
                isDebug = !isDebug;
            }
            GUI.backgroundColor = bc;
        }
        GUILayout.EndHorizontal();
        GUILayout.Label("列表左键查看数据，右键展开/收起列表，双击选中项目");
        GUILayout.BeginVertical();
        GUILayout.Space(15);
        ActiveInspectType = (InspectType)GUILayout.Toolbar((int)ActiveInspectType, inspectToolbarStrings);
        GUILayout.EndVertical();
        GUILayout.EndVertical();
        GUILayout.BeginHorizontal();
        if (!isSearch)
        {
            switch (ActiveInspectType)
            {
                case InspectType.OutGameScene:
                    ListPrefab(data.prefabOutGameLists);
                    break;
                case InspectType.Hero:
                    
                    ListCharacterPrefabs(data.prefabInGameCharacterLists, data.prefabOutGameCharacterLists);
                    break;
                case InspectType.InGameScene:
                    //prefabShowLists = prefabInGameLists;
                    ListPrefab(data.prefabInGameLists);
                    break;
            }
        }
        else
        {
            if (GetPrefabData.searchPrefabLists[0].Count != 0 || GetPrefabData.searchPrefabLists[1].Count != 0
            || GetPrefabData.searchPrefabLists[2].Count != 0 || GetPrefabData.searchPrefabLists[3].Count != 0)
            {
                switch (ActiveInspectType)
                {
                    case InspectType.OutGameScene:
                        ListPrefab(GetPrefabData.searchPrefabLists[0]);
                        break;
                    case InspectType.Hero:
                        ListCharacterPrefabs(GetPrefabData.searchPrefabLists[1], GetPrefabData.searchPrefabLists[2]);
                        break;
                    case InspectType.InGameScene:
                        ListPrefab(GetPrefabData.searchPrefabLists[3]);
                        break;
                }
            }
        }

       
        GUILayout.BeginHorizontal();

        if (m_PrefabTreeView != null)
        {
            m_PrefabTreeView.OnGUI(new Rect(600, 120, 800, position.height - 150 - toolbarGUIStyle.fixedHeight));
        }
        switch(ActiveInspectType)
        {
            case InspectType.Hero:
                GUILayout.Space(515);
                break;
            case InspectType.InGameScene:
            case InspectType.OutGameScene:
                GUILayout.Space(535);
                break;
        }
        
        GUILayout.BeginHorizontal();
        ActivePrefabInspectType = (InspectPrefabType)GUILayout.Toolbar((int)ActivePrefabInspectType, inspectToolbarPrefabStrings);
        GUILayout.EndHorizontal();
        switch (ActivePrefabInspectType)
        {
            case InspectPrefabType.Mesh:
                needUpdate = true;
                data.showDetail = 0;
                break;
            case InspectPrefabType.Material:
                needUpdate = true;
                data.showDetail = 1;
                break;
            case InspectPrefabType.Texture:
                needUpdate = true;
                data.showDetail = 2;
                break;

        }
        GUILayout.EndHorizontal();


    }
    void SelectObject(Object selectedObject, bool append)
    {
        if (append)
        {
            List<Object> currentSelection = new List<Object>(Selection.objects);
            // Allow toggle selection
            if (currentSelection.Contains(selectedObject)) currentSelection.Remove(selectedObject);
            else currentSelection.Add(selectedObject);

            Selection.objects = currentSelection.ToArray();
        }
        else Selection.activeObject = selectedObject;
    }

    void SelectObjects(List<Object> selectedObjects, bool append)
    {
        if (append)
        {
            List<Object> currentSelection = new List<Object>(Selection.objects);
            currentSelection.AddRange(selectedObjects);
            Selection.objects = currentSelection.ToArray();
        }
        else Selection.objects = selectedObjects.ToArray();
    }
    
    void ListCharacterPrefabs(List<GetPrefabData.PrefabParent> ingameShow, List<GetPrefabData.PrefabParent> outgameShow)
    {
        if (ingameShow.Count == 0 || outgameShow.Count == 0)
        {
            return;
        }
        GUILayout.BeginHorizontal();
        ActiveCharacterInspectType = (InspectCharacterType)GUILayout.Toolbar((int)ActiveCharacterInspectType, inspectCharacterStrings);
        GUILayout.EndHorizontal();
        switch (ActiveCharacterInspectType)
        {
            case InspectCharacterType.InGame:

                ListPrefab(ingameShow);
                break;
            case InspectCharacterType.OutGame:

                ListPrefab(outgameShow);
                break;
        }

    }
    void ListPrefab(List<GetPrefabData.PrefabParent> listShow)
    {
        if(listShow.Count == 0)
        {
            return;
        }
        isChanged = true;
        data.prefabShowLists = listShow;
        
    }
}
