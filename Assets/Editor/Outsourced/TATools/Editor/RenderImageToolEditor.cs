using System;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using UnityEditor;
using UnityEngine.Rendering;
using System.Reflection;



namespace fengxTools
{
    public class RenderImageToolEditor : EditorWindow
    {
        public static Camera RenderCam;

        public static List<RenderImage> renderImgs = new List<RenderImage>();

        public Vector2Int shotSize = new Vector2Int(1920, 1080);
        public bool HDR = false;
        public static bool transparentEnable = true;
        //public string cacheName = "/screenShot";
        //public string cacheDir = "D:/";

        public static bool toSceneCam = false;
        public static bool explorerTex = true;

        //public static GameObject screenShotCamGo;

        //private static CommandBuffer CB_renderImage = null;
        public Vector4[] postIntensityArray;
        public string[] postTags;
        public const int postTagsCount = 3;
        public static RenderTexture renderImageRT;
        [HideInInspector]
        public Material[] stencilMats;
        private Dictionary<int, Material> postShaders;
        public bool debugPostMask = false;
        //public MaskScale maskScale = MaskScale.Full;
        //[HideInInspector]
        private float maskScaleSize = 1;
        private float lastMaskScaleSize = 1;
        private Material[] testm;
        public static RenderTextureFormat format = RenderTextureFormat.Default;

        public Material mate;


        public static bool showIDMap = false;

        public static float idMapOpacity = 0.5f;

        public static Dictionary<int, Material> highlightColorIDs = new Dictionary<int, Material>();



        [MenuItem("Window/Rendering/【tools】渲染图片 Render Image")]
        static void InvokeRenderImageToolEditor()
        {
            var ww = GetWindow<RenderImageToolEditor>();
            ww.minSize = new Vector2(400, 500);
        }

        RenderImageToolEditor()
        {
            titleContent.text = "渲染图片 Render Image";
        }






        private void OnEnable()
        {
            //screenShotCamGo = new GameObject("ScreenShotCamerea temp");
            //screenShotCam = screenShotCamGo.AddComponent<Camera>();
            LoadSeting();
            iosResList = new Dictionary<string, Vector2Int>();
            iosResList.Add("12.9\" iPad Pro", new Vector2Int(2732, 2048));
            iosResList.Add("11\" iPad Pro", new Vector2Int(2388, 1668));
            iosResList.Add("10.5\" iPad Pro", new Vector2Int(2388, 1668));
            iosResList.Add("9.7\" iPad Pro", new Vector2Int(2048, 1536));
            iosResList.Add("7.9\" iPad mini", new Vector2Int(2048, 1536));
            iosResList.Add("10.5\" iPad Air", new Vector2Int(2224, 1668));
            iosResList.Add("9.7\" iPad Air", new Vector2Int(2048, 1536));
            iosResList.Add("10.2\" iPad", new Vector2Int(2160, 1620));
            iosResList.Add("9.7\" iPad", new Vector2Int(2048, 1536));
            iosResList.Add("iPhone 12 Pro Max", new Vector2Int(2778, 1284));
            iosResList.Add("iPhone 12 Pro", new Vector2Int(2532, 1170));
            iosResList.Add("iPhone 12", new Vector2Int(2532, 1170));
            iosResList.Add("iPhone 12 mini", new Vector2Int(2436, 1125));
            iosResList.Add("iPhone 11 Pro Max", new Vector2Int(2688, 1242));
            iosResList.Add("iPhone 11 Pro", new Vector2Int(2436, 1125));
            iosResList.Add("iPhone 11", new Vector2Int(1792, 828));
            iosResList.Add("iPhone XS Max", new Vector2Int(2688, 1242));
            iosResList.Add("iPhone XS", new Vector2Int(2436, 1125));
            iosResList.Add("iPhone XR", new Vector2Int(1792, 828));
            iosResList.Add("iPhone X", new Vector2Int(2436, 1125));
            iosResList.Add("iPhone 8 Plus", new Vector2Int(1920, 1080));
            iosResList.Add("iPhone 8", new Vector2Int(1334, 750));
            iosResList.Add("iPhone 7 Plus", new Vector2Int(1920, 1080));
            iosResList.Add("iPhone 7", new Vector2Int(1334, 750));
            iosResList.Add("iPhone 6s Plus", new Vector2Int(1920, 1080));
            iosResList.Add("iPhone 6s", new Vector2Int(1334, 750));
            iosResList.Add("iPhone 6 Plus", new Vector2Int(1920, 1080));
            iosResList.Add("iPhone 6", new Vector2Int(1334, 750));
            iosResList.Add("4.7\" iPhone SE", new Vector2Int(1334, 750));
            iosResList.Add("4\" iPhone SE", new Vector2Int(1136, 640));
            iosResList.Add("iPod touch 5th generation and later", new Vector2Int(1136, 640));
            if (RenderCam == null)
            {
                RenderCam = Camera.main;

            }
            //GetGrapyhicSettings();


            if (Directory.Exists(RenderImage.saveDir))
            {
                foreach (var item in Directory.GetFiles(RenderImage.saveDir, "*.png"))
                {
                    //UnityEngine.Debug.Log(item);
                    RenderImage ss = new RenderImage();

                    ss.name = Path.GetFileNameWithoutExtension(item);
                    ss.isHDR = false;
                    ss.filename = item;
                    Texture2D tex = new Texture2D(4, 4);
                    tex.LoadImage(File.ReadAllBytes(ss.filename));
                    ss.tex = tex;
                    ss.size = new Vector2Int(tex.width, tex.height);
                    renderImgs.Add(ss);
                }

                foreach (var item in Directory.GetFiles(RenderImage.saveDir, "*.exr"))
                {
                    ////UnityEngine.Debug.Log(item);
                    //RenderImage ss = new RenderImage();

                    //ss.name = Path.GetFileNameWithoutExtension(item);
                    //ss.isHDR = true;
                    //ss.filename = item;
                    //Texture2D tex = new Texture2D(4, 4,TextureFormat.RGBAFloat,false);
                    //bool suc= tex.LoadImage(File.ReadAllBytes(ss.filename));
                    //if (suc)
                    //{
                    //    ss.tex = tex;
                    //ss.size = new Vector2Int(tex.width, tex.height);
                    //    renderImgs.Add(ss);
                    //    Debug.Log("exr：" + tex.width);

                    //}
                    //else
                    //{
                    //    Debug.Log("不支持的格式："+ss.filename);

                    //}

                }

            }
            else
            {
                Directory.CreateDirectory(RenderImage.saveDir);
            }

        }

       

        private void OnDisable()
        {
            //if (screenShotCamGo != null)
            //{
            //    DestroyImmediate(screenShotCamGo);
            //}
            SaveSeting();
            renderImgs = new List<RenderImage>();
            //UnityEngine.Debug.Log(screenShotCam);
        }

        public static string photosopDir = "";
        public Texture2D psIcon;
        public Texture2D exploreIcon;
        public static string currentDir = "";

        public Vector2 socv = Vector2.zero;


        int toolbarInt = 0;


        Vector2Int gameRes = new Vector2Int(500, 500);
        Vector2Int SceneRes = new Vector2Int(500, 500);

        public Dictionary<string, Vector2Int> iosResList = new Dictionary<string, Vector2Int>();


        public bool ExpertMode = false;
        public bool cacheEnable = true;
        public bool showInfor = false;

        public string lastSavePath = "";

        public class RenderImage
        {
            public string name = "ScreenShot";
            public string dir = "ScreenShot";
            public string filename = "";
            public bool isHDR = false;
            public Texture2D tex = null;

            public Vector2Int size = new Vector2Int(50, 50);


            public static int id = 0;
            public static int index = 0;
            public static string saveDir = "";
            public static string saveName = "Img";


            public RenderImage()
            {
                id += 1;
                //index = (int)Time.time + id;
                saveDir = Application.dataPath + "/../RenderImages/";
                DateTime dt = System.DateTime.Now;

                string dtstr = dt.Month + "-" + dt.Day + "_" + dt.Hour + "-" + dt.Minute + "-" + dt.Second;

                //name = "Img_" + dtstr+"_" + index.ToString();
                name = "Img_" + dt.Ticks.ToString() + "_" + id.ToString();

                filename = saveDir + name;
            }

            public void SaveAsDir(string dir)
            {
                SaveImgToDir(dir);
            }

            public void SaveAs(string fn)
            {
                byte[] bytes;
                if (isHDR)
                {
                    bytes = tex.EncodeToEXR();
                }
                else
                {
                    bytes = tex.EncodeToPNG();

                }
                System.IO.File.WriteAllBytes(fn, bytes);
                tex.Apply();
                //UnityEngine.Debug.Log(string.Format("Redner a image: {0}", filename));
            }

            public void Save()
            {
                SaveImgToDir(saveDir);
            }
            private void SaveImgToDir(string dir)
            {
                if (tex == null || !Directory.Exists(saveDir))
                {
                    UnityEngine.Debug.Log(string.Format("存储位置不存在: {0}", saveDir));
                    return;
                }
                byte[] bytes;
                string exten = ".png";
                if (isHDR)
                {
                    bytes = tex.EncodeToEXR();
                    exten = ".exr";
                }
                else
                {
                    bytes = tex.EncodeToPNG();
                    exten = ".png";

                }
                filename = dir + "/" + name + exten;
                System.IO.File.WriteAllBytes(filename, bytes);
                tex.Apply();
                //UnityEngine.Debug.Log(string.Format("Redner a image: {0}", filename));
            }

            public void deleteTex()
            {
                if (tex != null)
                {
                    DestroyImmediate(tex);
                    string exten = ".png";
                    if (isHDR)
                    {
                        exten = ".exr";
                    }
                    else
                    {
                        exten = ".png";
                    }
                    //Debug.Log(saveDir + "/" + name + exten);
                    File.Delete(saveDir + "/" + name + exten);
                }
            }

        }
        public float renderImgScalefix = 1.0f;




        public void DeleteRes(object id)
        {
            int index = (int)id;
            if (index > -1 && index < resolutions.Count)
            {
                resolutions.Remove(resolutions[index]);
            }
            //UnityEngine.Debug.Log(index);

        }

        public void ReplaceToThisRes(object id)
        {
            int index = (int)id;
            if (index > -1 && index < resolutions.Count)
            {
                resolutions[index] = shotSize;
            }
            //UnityEngine.Debug.Log(index);

        }

        public void SetRes(object res)
        {

            Vector2Int index = (Vector2Int)res;
            shotSize = index;
            //UnityEngine.Debug.Log(index);

        }

        public void CopyScreenShot(object i)
        {

            int index = (int)i;
            if (index < renderImgs.Count)
            {

                RenderImage ss = renderImgs[index];

                if (ss != null)
                {

                }
            }

        }

        public void SaveScreenShot(object i)
        {

            int index = (int)i;
            if (index < renderImgs.Count)
            {

                RenderImage ss = renderImgs[index];

                if (ss != null)
                {
                    string ext = "png";
                    if (ss.isHDR)
                    {
                        ext = "exr";
                    }
                    string path = EditorUtility.SaveFilePanel("Save As", lastSavePath, ss.name, ext);
                    if (path != "")
                    {
                        ss.SaveAs(path);

                    }

                }
            }

        }

        public void OpenScreenShot(object i)
        {

            int index = (int)i;
            if (index < renderImgs.Count)
            {

                RenderImage ss = renderImgs[index];

                if (ss != null)
                {
                    EditorUtility.OpenWithDefaultApp(ss.filename);
                }
            }

        }


        public void ShowScreenShot(object i)
        {

            int index = (int)i;
            if (index < renderImgs.Count)
            {

                RenderImage ss = renderImgs[index];

                if (ss != null)
                {

                }
            }

        }
        public void DeleteScreenShot(object i)
        {

            int index = (int)i;
            if (index < renderImgs.Count)
            {

                RenderImage ss = renderImgs[index];

                renderImgs.RemoveAt(index);
                if (ss != null)
                {
                    if (ss.tex != null)
                    {
                        DestroyImmediate(ss.tex);
                        File.Delete(ss.filename);
                    }
                }
            }

        }

        static string settingKeyName = "TATools_RenderImageTool_";
        public List<Vector2Int> resolutions = new List<Vector2Int>();


        private void SaveSeting()
        {
            string rs = "";
            foreach (var item in resolutions)
            {
                rs += item.x.ToString() + "|" + item.y.ToString() + "|";
            }

            //UnityEngine.Debug.Log(rs);
            EditorPrefs.SetString(settingKeyName + "resolutions", rs);
            EditorPrefs.SetString(settingKeyName + "RenderImage.saveDir", RenderImage.saveDir);
            EditorPrefs.SetString(settingKeyName + "RenderImage.saveName", RenderImage.saveName);
            EditorPrefs.SetBool(settingKeyName + "showInfor", showInfor);
            EditorPrefs.SetString(settingKeyName + "lastSavePath", lastSavePath);

        }

        private void LoadSeting()
        {

            // 载入设置
            //=== IOS 分辨率列表
            string rs = EditorPrefs.GetString(settingKeyName + "resolutions");
            //UnityEngine.Debug.Log(rs);
            resolutions = new List<Vector2Int>();
            string[] rss = rs.Split('|');
            for (int i = 0; i < rss.Length / 2; i++)
            {
                Vector2Int r = new Vector2Int(int.Parse(rss[i * 2]), int.Parse(rss[i * 2 + 1]));
                resolutions.Add(r);
            }
            if (resolutions.Count == 0)
            {
                resolutions.Add(new Vector2Int(1024, 768));
                resolutions.Add(new Vector2Int(2340, 2332));
            }
            //=== 缓存图片地址
            string cdir = EditorPrefs.GetString(settingKeyName + "CacheDir");

            if (cdir != "")
            {
                if (Directory.Exists(cdir))
                {
                    Directory.CreateDirectory(cdir);
                }

                if (Directory.Exists(cdir))
                {
                    cdir = Application.dataPath + "/../RenderImages/";
                }
            }
            else
            {
                cdir = Application.dataPath + "/../RenderImages/";
            }
            RenderImage.saveDir = cdir;

            showInfor = EditorPrefs.GetBool(settingKeyName + "showInfor");

            lastSavePath = EditorPrefs.GetString(settingKeyName + "lastSavePath");

            //UnityEngine.Debug.Log(rs);


        }

        public bool deleteState = false;
        private void OnGUI()
        {

            GUILayout.Label("bug 联系 @fengxzeng");

            GUILayout.BeginHorizontal();

            int btext = GUI.skin.button.fontSize;
            GUI.skin.button.fontSize = 30;
            GUI.skin.button.normal.textColor = Color.red;
            if (GUILayout.Button("渲染", GUILayout.Height(50)))//, GUILayout.Width(100)))
            {
                if (RenderCam == null)
                {
                    //if (screenShotCamGo==null)
                    //{
                    //    screenShotCamGo = new GameObject("ScreenShotCamerea temp");
                    //}
                    //screenShotCam = screenShotCamGo.AddComponent<Camera>();
                    //RenderCam = screenShotCam;
                    ////EditorUtility.DisplayDialog("ScreenShot", "没有摄像机", "确定");
                }
                else
                {
                    RenderImage tex = ScreenShot(RenderCam, HDR, shotSize);
                    if (cacheEnable)
                    {
                        tex.Save();
                    }
                    if (tex != null)
                    {
                        renderImgs.Insert(0, tex);
                    }
                }
            }
            GUI.skin.button.normal.textColor = Color.white;
            GUI.skin.button.fontSize = btext;
            GUI.skin.button.fontSize = 11;

            GUI.color = Color.white;
            ExpertMode = GUILayout.Toggle(ExpertMode, "高级设置", GUI.skin.button, GUILayout.Height(50), GUILayout.Width(100));

            GUILayout.EndHorizontal();


            //EditorGUILayout.LabelField("渲染摄像机 Render Camera", EditorStyles.boldLabel);
            GUILayout.Space(10);
            GUILayout.BeginVertical();

            string[] toolbarStrings = { "自定义摄像机", "场景窗口", "游戏窗口" };

            // (GUIStyle)"ToolbarButtonFlat"
            //toolbarInt = GUILayout.Toolbar(toolbarInt, toolbarStrings,(GUIStyle)"ToolbarButtonFlat", GUILayout.Height(20));


            GUILayout.BeginVertical(GUI.skin.box);
            if (toolbarInt == 0)
            {
                if (RenderCam == null)
                {
                    GUI.color = Color.red;
                }

                RenderCam = EditorGUILayout.ObjectField(new GUIContent("截图摄像机："), RenderCam, typeof(Camera), true) as Camera;
                GUI.color = Color.white;

            }

            if (RenderCam == null)
            {
                GUILayout.EndVertical();
                GUILayout.EndVertical();
                EditorGUILayout.HelpBox("请选择一个摄像机在进行渲染！ ", MessageType.Error);

                return;

            }
            EditorGUILayout.LabelField("分辨率 Reslution", EditorStyles.boldLabel);
            GUILayout.BeginVertical(GUI.skin.box);


            GUILayout.BeginHorizontal(GUI.skin.box);

            if (toolbarInt != 0)
            {
                if (Camera.current != null)
                {
                    // gameRes = new Vector2Int(Camera.current.pixelWidth, Camera.current.pixelHeight);

                    gameRes = EditorGUILayout.Vector2IntField(new GUIContent("分辨率："), gameRes);


                    //if (GUILayout.Button("添加为模板"))
                    if (GUILayout.Button(EditorGUIUtility.IconContent("PrefabOverlayAdded Icon")))
                    {

                        resolutions.Add(gameRes);
                    }
                }

            }
            else
            {
                GUI.enabled = !deleteState;
                //shotSize = EditorGUILayout.Vector2IntField(new GUIContent("分辨率："), shotSize);

                //GUILayout.BeginHorizontal();
                //shotSize.x = EditorGUILayout.IntField(new GUIContent("宽 Width："), shotSize.x);
                //shotSize.y = EditorGUILayout.IntField(new GUIContent("高 Height："), shotSize.y);
                //GUILayout.EndHorizontal();
                GUILayout.BeginVertical();
                shotSize.x = EditorGUILayout.IntField(new GUIContent("宽 Width："), shotSize.x);
                shotSize.y = EditorGUILayout.IntField(new GUIContent("高 Height："), shotSize.y);
                GUILayout.EndVertical();



                GUILayout.BeginVertical();
                GUILayout.BeginHorizontal();
                if (GUILayout.Button(EditorGUIUtility.IconContent("Favorite Icon"), GUILayout.Width(30), GUILayout.Height(20)))
                {
                    if (Event.current.button == 1)
                    {
                        //if (Event.current.button == 1)
                        //{
                        GenericMenu toolsMenu = new GenericMenu();
                        //toolsMenu.AddItem(new GUIContent("Rename"), false, OnTableRename, ode.environments[hdriListTab]);
                        toolsMenu.AddSeparator("");
                        foreach (var item in resolutions)
                        {
                            string n = item.x.ToString() + "×" + item.y.ToString();
                            toolsMenu.AddItem(new GUIContent(n), false, SetRes, item);
                        }
                        toolsMenu.AddSeparator("");
                        // Offset menu from right of editor window
                        toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                        //}
                    }
                    else
                    {
                        bool isExist = false;
                        foreach (var item in resolutions)
                        {
                            if (shotSize == item)
                            {
                                isExist = true;
                            }
                        }
                        if (!isExist)
                        {
                            resolutions.Add(shotSize);

                        }
                    }


                }
                GUI.enabled = true;

                if (GUILayout.Button(EditorGUIUtility.IconContent("Preset.Context"), GUILayout.Width(30)))
                {
                    //if (Event.current.button == 1)
                    //{
                    GenericMenu toolsMenu = new GenericMenu();
                    //toolsMenu.AddItem(new GUIContent("Rename"), false, OnTableRename, ode.environments[hdriListTab]);
                    toolsMenu.AddDisabledItem(new GUIContent("Standard================================="));
                    for (int ir = 0; ir < 9; ir++)
                    {
                        int r = (int)Mathf.Pow(2, ir + 5);
                        Vector2Int v = new Vector2Int(r, r);
                        string n = v.ToString();
                        toolsMenu.AddItem(new GUIContent(n), false, SetRes, v);
                    }
                    toolsMenu.AddDisabledItem(new GUIContent("IOS================================="));
                    foreach (var item in iosResList.Keys)
                    {
                        Vector2Int v = iosResList[item];
                        string n = item + "    " + v.ToString();
                        toolsMenu.AddItem(new GUIContent(n), false, SetRes, v);
                    }
                    toolsMenu.AddSeparator("");
                    // Offset menu from right of editor window
                    toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                    //}
                }
                //if (GUILayout.Button(EditorGUIUtility.IconContent("Preset.Context"), GUILayout.Width(30)))
                //{
                //    //if (Event.current.button == 1)
                //    //{
                //    GenericMenu toolsMenu = new GenericMenu();
                //    //toolsMenu.AddItem(new GUIContent("Rename"), false, OnTableRename, ode.environments[hdriListTab]);
                //    toolsMenu.AddSeparator("");
                //    for (int ir = 0; ir < 9; ir++)
                //    {
                //        int r = (int)Mathf.Pow(2, ir+5);
                //        Vector2Int v = new Vector2Int(r,r);
                //        string n = v.ToString();
                //        toolsMenu.AddItem(new GUIContent(n), false, SetRes, v);
                //    }


                //    toolsMenu.AddSeparator("");
                //    // Offset menu from right of editor window
                //    toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                //    //}
                //}
                GUILayout.EndHorizontal();

                GUILayout.BeginHorizontal();
                GUILayout.Label("比例");
                float aspect = shotSize.x / (float)shotSize.y;
                GUI.enabled = false;
                EditorGUILayout.FloatField(aspect, GUILayout.Width(40));
                GUI.enabled = true;
                GUILayout.EndHorizontal();

                GUILayout.EndVertical();

                //if (showInfor)
                //{
                //    deleteState = GUILayout.Toggle(deleteState, "删除模板", GUI.skin.button);

                //}
            }
            GUILayout.EndHorizontal();


            if (toolbarInt == 0)
            {
                GUILayout.BeginVertical();

                bool isDelete = false;
                int deleteID = -1;
                //int id = 0;



                int maxRX = 6;
                maxRX = (int)(this.position.width / 100);
                int maxRY = resolutions.Count / maxRX + 1;

                for (int y = 0; y < maxRY; y++)
                {

                    GUILayout.BeginHorizontal();
                    for (int x = 0; x < maxRX; x++)
                    {
                        int id = y * maxRX + x;
                        if (id < resolutions.Count)
                        {
                            Vector2Int item = resolutions[id];
                            string n = item.x.ToString() + "×" + item.y.ToString();
                            if (deleteState)
                            {
                                n += "删除";
                            }
                            if (GUILayout.Button(n, GUILayout.Height(25)))
                            {

                                if (Event.current.button == 1)
                                {
                                    GenericMenu toolsMenu = new GenericMenu();
                                    toolsMenu.AddSeparator("");
                                    int indexId = id;
                                    toolsMenu.AddItem(new GUIContent("删除 Delete"), false, DeleteRes, indexId);
                                    toolsMenu.AddItem(new GUIContent("替换 Replace"), false, ReplaceToThisRes, indexId);
                                    toolsMenu.AddSeparator("");
                                    toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                                }
                                else
                                {
                                    if (deleteState)
                                    {
                                        //resolutions.Remove(item);
                                        isDelete = true;
                                        deleteID = id;
                                    }
                                    else
                                    {
                                        shotSize = item;
                                    }
                                }
                            }
                        }
                    }
                    GUILayout.EndVertical();

                }
                GUILayout.EndHorizontal();

                if (isDelete)
                {
                    resolutions.RemoveAt(deleteID);
                }

                if (shotSize.x * shotSize.y > 20000000)
                {
                    EditorGUILayout.HelpBox("分辨率这么大！ 渲染会慢一点", MessageType.Warning);
                }




            }

            GUILayout.EndVertical();



            EditorGUILayout.LabelField("输出 Output", EditorStyles.boldLabel);
            GUILayout.BeginVertical(GUI.skin.box);

            transparentEnable = EditorGUILayout.Toggle("透明", transparentEnable);

            if (transparentEnable&&RenderCam.allowHDR && currentTierSettings.hdr)
            {
                EditorGUILayout.HelpBox("美术同学可以忽略此条消息！\n 激活透明会临时切换HDRMode 为 FP16！，会对 GraphicSettings 产生修改记录！，提交GraphicSettings时请注意 当前激活的tier Use Defaults 会被取消， 等我知道怎么获取这个参数在重置回来。 ", MessageType.Warning);
            }
          
            if (ExpertMode)
            {
                HDR = EditorGUILayout.Toggle("HDR", HDR);
                GUILayout.BeginVertical();// GUI.skin.box);
                GUI.enabled = false;
                HDR = EditorGUILayout.Toggle("抗锯齿", HDR);
                GUI.enabled = true;
                cacheEnable = EditorGUILayout.Toggle("自动缓存", cacheEnable);
                GUILayout.BeginHorizontal();
                RenderImage.saveDir = EditorGUILayout.TextField("存储位置：", RenderImage.saveDir);
                if (GUILayout.Button(EditorGUIUtility.FindTexture("Folder Icon"), GUILayout.Height(20), GUILayout.Width(20)))
                {

                }
                GUILayout.EndHorizontal();
                RenderImage.saveName = EditorGUILayout.TextField("截图名字：", RenderImage.saveName);

                GUILayout.EndVertical();
            }
            GUILayout.EndVertical();


            GUILayout.EndVertical();

            GUILayout.EndVertical();


            //GUILayout.BeginHorizontal();
            //GUI.color = Color.red;
            //if (GUILayout.Button(EditorGUIUtility.FindTexture("RectTool"), GUILayout.Height(50)))



            //if (GUILayout.Button(EditorGUIUtility.FindTexture("FolderEmpty Icon"), GUILayout.Height(50),GUILayout.Width(50)))
            //{
            //    Process.Start(@"explorer", "\"" + texDir.Replace("/", "\\") + "\"");
            //}
            //GUILayout.EndHorizontal();

            //if (GUILayout.Button("摄像机跟随", GUILayout.Height(50)))
            //{
            //    cam.transform.position = SceneView.lastActiveSceneView.camera.transform.position;
            //    cam.transform.rotation = SceneView.lastActiveSceneView.rotation;
            //}


            //            if (GUILayout.Button("x"))
            //            {
            //                UnityEngine.Debug.Log(System.Environment.CurrentDirectory);
            //                UnityEngine.Debug.Log(this.GetType().Assembly.Location);
            //#if UNITY_ANDROID
            //        Debug.Log("安卓设备");
            //#endif

            //#if UNITY_IOS
            //                Debug.Log("苹果设备");
            //                EditorUtility.DisplayDialog("xxx", "xx", "ok", "xxxxxxx!!");
            //                //MessageBox
            //#endif

            //#if UNITY_STANDALONE_WIN
            //        Debug.Log("Windows");
            //#endif
            //            }
            GUILayout.Space(10);

            GUILayout.BeginHorizontal(GUI.skin.box);
            EditorGUILayout.LabelField("渲染图片列表", EditorStyles.boldLabel);

            if (GUILayout.Button(EditorGUIUtility.FindTexture("Folder Icon"), GUILayout.Height(20), GUILayout.Width(50)))
            {
                System.Diagnostics.Process.Start(@"explorer", "\"" + RenderImage.saveDir.Replace("/", "\\") + "\"");

            }

            showInfor = GUILayout.Toggle(showInfor, "详情", GUI.skin.button);//, GUILayout.Width(50));
            if (GUILayout.Button("导出所有"))//, GUILayout.Width(80)))
            {
                string path = EditorUtility.SaveFolderPanel("Save As", lastSavePath, "");
                if (Directory.Exists(path))
                {
                    //Debug.Log(path);
                    lastSavePath = path;
                    foreach (var item in renderImgs)
                    {
                        item.SaveAsDir(path);
                    }
                }

            }
            if (GUILayout.Button("删除所有"))//, GUILayout.Width(80)))
            {
                bool isDelete = EditorUtility.DisplayDialog("RenderImage", "确认要删除所有截图吗？", "确认", "取消");
                if (isDelete)
                {
                    foreach (var item in renderImgs)
                    {
                        item.deleteTex();
                    }

                    renderImgs = new List<RenderImage>();
                }


            }

            GUILayout.EndHorizontal();

            GUILayout.BeginVertical((GUIStyle)"InnerShadowBg");

            socv = GUILayout.BeginScrollView(socv);

            GUILayout.BeginVertical();

            int deletIndex = -1;
            int i = 0;


            Vector2Int texMaxSize = new Vector2Int((int)(200 * renderImgScalefix), (int)(100 * renderImgScalefix));


            int maxX = Mathf.Max(1, (int)(this.position.width / (texMaxSize.x * 1.01f)));
            int maxY = (renderImgs.Count / maxX) + 1;
            bool isCtrl = Event.current.control;
            if (isCtrl)
            {
                Repaint();
            }

            for (int y = 0; y < maxY; y++)
            {
                GUILayout.BeginHorizontal();
                for (int x = 0; x < maxX; x++)
                {
                    if (x + y * maxX < renderImgs.Count && renderImgs[x + y * maxX].tex != null)
                    {
                        int count = x + y * maxX;
                        RenderImage item = renderImgs[count];

                        int w = (int)((80.0f / item.tex.height) * item.tex.width);


                        //EditorGUILayout.BeginHorizontal();
                        //GUILayout.FlexibleSpace();
                        //EditorGUILayout.EndHorizontal();
                        Rect scale = Rect.zero;


                        GUILayout.BeginVertical(GUI.skin.box, GUILayout.Height(texMaxSize.y), GUILayout.Width(texMaxSize.x));





                        //GUILayout.BeginHorizontal();
                        GUI.skin.label.stretchWidth = false;
                        GUI.skin.label.wordWrap = false;
                        bool but = GUILayout.Button(item.tex, (GUIStyle)"TabWindowBackground", GUILayout.Height(texMaxSize.y), GUILayout.Width(texMaxSize.x));

                        if (but)
                        {

                            if (Event.current.button == 1)
                            {
                                GenericMenu toolsMenu = new GenericMenu();
                                //toolsMenu.AddItem(new GUIContent("Rename"), false, OnTableRename, ode.environments[hdriListTab]);
                                deletIndex = count;
                                toolsMenu.AddSeparator("");
                                int a = count;
                                toolsMenu.AddItem(new GUIContent("打开 Open"), false, OpenScreenShot, a);
                                //toolsMenu.AddItem(new GUIContent("Show"), false, ShowScreenShot, a);
                                toolsMenu.AddDisabledItem(new GUIContent("复制 Copy"));//, false, CopyScreenShot, a);
                                toolsMenu.AddItem(new GUIContent("储存 Save As"), false, SaveScreenShot, a);
                                toolsMenu.AddSeparator("");
                                toolsMenu.AddSeparator("");
                                toolsMenu.AddItem(new GUIContent("删除 Delete"), false, DeleteScreenShot, a);
                                // Offset menu from right of editor window
                                toolsMenu.DropDown(new Rect(Event.current.mousePosition.x, Event.current.mousePosition.y, 0, 0));
                                /// EditorGUIUtility.ExitGUI();
                            }

                        }
                        //GUILayout.EndHorizontal();
                        GUILayout.BeginHorizontal(GUILayout.MaxWidth(texMaxSize.x));
                        GUILayout.BeginVertical();
                        if (showInfor || isCtrl)
                        {
                            GUILayout.Label(item.name, (GUIStyle)"AssetLabel Partial", GUILayout.MaxWidth(texMaxSize.x - 50));
                        }
                        else
                        {
                            //GUILayout.Label("", GUILayout.MaxWidth(texMaxSize.x - 50));

                        }


                        //GUILayout.Label(item.tex.width.ToString() + "x" + item.tex.height.ToString(), (GUIStyle)"Label", GUILayout.Width(texMaxSize.x-20));
                        GUILayout.EndVertical();

                        if (showInfor || isCtrl)
                        {
                            //if (GUILayout.Button(EditorGUIUtility.FindTexture("Folder Icon"), GUILayout.Height(18), GUILayout.Width(20)))
                            //{
                            //}
                            if (GUILayout.Button("X", GUILayout.Width(18)))
                            {
                                int aa = count;
                                DeleteScreenShot(aa);
                            }
                            //if (isCtrl)
                            //{
                            //    int aa = count;
                            //    if (GUILayout.Button("X", GUILayout.Width(20)))
                            //    {
                            //        DeleteScreenShot(aa);
                            //    }
                            //}
                        }


                        GUILayout.EndHorizontal();

                        GUILayout.EndVertical();

                        if (Event.current.type == EventType.Repaint)
                        {

                            scale = GUILayoutUtility.GetLastRect();
                        }
                        if (showInfor)
                        {


                        }
                        else
                        {


                        }
                        scale.x += 4;
                        scale.y += -13 + texMaxSize.y;
                        scale.width = 80;
                        scale.height = texMaxSize.y;
                        //EditorGUI.DrawRect(scale, Color.red);
                        int h = 18;
                        EditorGUI.LabelField(scale, item.size.x.ToString() + "x" + item.size.y.ToString(), (GUIStyle)"AssetLabel");
                        if (item.isHDR)
                        {
                            scale.x += scale.width;
                            EditorGUI.LabelField(scale, "HDR", (GUIStyle)"AssetLabel");
                        }
                    }




                }
                GUILayout.EndHorizontal();
                GUILayout.Space(20);

            }

            if (renderImgs.Count == 0)
            {
                GUI.color = Color.grey;
                EditorGUILayout.HelpBox("Empty ", MessageType.Info);
                GUI.color = Color.white;
            }
            GUILayout.EndVertical();
            GUILayout.EndScrollView();
            renderImgScalefix = EditorGUILayout.Slider(renderImgScalefix, 0.7f, 3f);




            GUILayout.EndVertical();

        }



        public static BuildTargetGroup currentBuildTargetGroup = BuildTargetGroup.Android;
        public static  GraphicsTier currentGraphicsTier = Graphics.activeTier;
        public static UnityEditor.Rendering.TierSettings currentTierSettings;
        public static UnityEditor.Rendering.TierSettings currentTierSettingsHDR;
        public static void GetGrapyhicSettings() {

            //CameraClearFlags ccf = RenderCam.clearFlags;
            //Color bc = RenderCam.backgroundColor;
            currentGraphicsTier = Graphics.activeTier;
#if UNITY_ANDROID
            currentBuildTargetGroup = BuildTargetGroup.Android;
#endif

#if UNITY_IOS
                 currentBuildTargetGroup=BuildTargetGroup.iOS;

#endif

#if UNITY_STANDALONE_WIN
                 currentBuildTargetGroup=BuildTargetGroup.Standalone;

#endif
            currentTierSettings = UnityEditor.Rendering.EditorGraphicsSettings.GetTierSettings(currentBuildTargetGroup, currentGraphicsTier);
            currentTierSettingsHDR = UnityEditor.Rendering.EditorGraphicsSettings.GetTierSettings(currentBuildTargetGroup, currentGraphicsTier);
            currentTierSettingsHDR.hdrMode = CameraHDRMode.FP16;

        }


        public static RenderImage ScreenShot(Camera camera, bool isHdr, Vector2Int size)
        {
            if (toSceneCam)
            {
                RenderCam.transform.position = SceneView.lastActiveSceneView.camera.transform.position;
                RenderCam.transform.rotation = SceneView.lastActiveSceneView.rotation;

            }
            RenderTexture RenderImageRT;
            Texture2D renderTex;
            if (isHdr)
            {
                RenderImageRT = new RenderTexture(size.x, size.y, 24, RenderTextureFormat.ARGBFloat);
                renderTex = new Texture2D(size.x, size.y, TextureFormat.RGBAFloat, false);

            }
            else
            {
                RenderImageRT = new RenderTexture(size.x, size.y, 24, RenderTextureFormat.ARGBHalf);
                renderTex = new Texture2D(size.x, size.y, TextureFormat.RGBA32, false);
            }
            RenderCam.targetTexture = RenderImageRT;


            //GetGrapyhicSettings();
            CameraClearFlags ccf = RenderCam.clearFlags;
            if (transparentEnable)
            {

                RenderCam.clearFlags = CameraClearFlags.Nothing;
                //if (RenderCam.allowHDR&&currentTierSettings.hdr)
                //{
                //UnityEditor.Rendering.EditorGraphicsSettings.SetTierSettings(currentBuildTargetGroup, currentGraphicsTier, currentTierSettingsHDR);
                //}
             
            }
            RenderCam.Render();

            RenderTexture lastRT= RenderTexture.active ;
            RenderTexture.active = RenderImageRT;
            renderTex.ReadPixels(new Rect(0, 0, size.x, size.y), 0, 0);
      
            //DestroyImmediate(RenderImageRT);
            if (transparentEnable)
            {
                RenderCam.clearFlags = ccf;
                //if (RenderCam.allowHDR && currentTierSettings.hdr)
                //{
                    //UnityEditor.Rendering.EditorGraphicsSettings.SetTierSettings(currentBuildTargetGroup, currentGraphicsTier, currentTierSettings);
                //}

            }
            RenderImage renderImg = new RenderImage();
            renderImg.isHDR = isHdr;
            renderImg.tex = renderTex;
            renderImg.size = new Vector2Int(renderTex.width, renderTex.height);
            renderTex.Apply();

            RenderCam.targetTexture = null;
            RenderTexture.active = lastRT;

            RenderImageRT.Release();
            return renderImg;
        }




    }



    [CustomEditor(typeof(Camera), true)]
    public class EditCameraInspector : CameraEditor
    {
        Editor defaultEditor;
        Camera cam = null;

        //object[] parOnOverlayGUI = null;
        //MethodInfo MIOnOverlayGUI = null;
        MethodInfo MIOnSceneGUI = null;

        void OnEnable()
        {

            cam = (Camera)target;
            defaultEditor = Editor.CreateEditor(target, Type.GetType("UnityEditor.CameraEditor, UnityEditor"));
            //if (!Application.isPlaying)
            //{

            //    MethodInfo enableMethod = defaultEditor.GetType().GetMethod("OnEnable", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
            //    if (enableMethod != null)
            //        enableMethod.Invoke(defaultEditor, null);
            //}
            //EditorApplication.update += inspectorUpdate;
        }
        void OnDisable()
        {
            MethodInfo disableMethod = defaultEditor.GetType().GetMethod("OnDisable", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
            if (disableMethod != null)
                disableMethod.Invoke(defaultEditor, null);
            DestroyImmediate(defaultEditor);
            // EditorApplication.update -= inspectorUpdate;

        }

        //public void test() {

        //}
        //public void OnSceneGUI()
        //{
        //    //if (!Application.isPlaying)
        //    //{

        //        MIOnSceneGUI = defaultEditor.GetType().GetMethod("OnSceneGUI");//,  BindingFlags.Public | BindingFlags.Instance);
        //        MIOnSceneGUI.Invoke(defaultEditor, null);
        //    //}
        //}

        public override void OnInspectorGUI()
        {
            //defaultEditor.OnOverlayGUI();
            defaultEditor.OnInspectorGUI();
            GUILayout.Space(20);
            EditorGUILayout.LabelField("CameraToolBar", EditorStyles.largeLabel);

            GUIContent cc = EditorGUIUtility.IconContent("Image Icon");
            cc = EditorGUIUtility.IconContent("Texture2D Icon");
  
            if (GUILayout.Button("渲染图片 Render Image", GUILayout.Width(180), GUILayout.Height(50)))
            {
                RenderImageToolEditor.RenderCam = cam;
                EditorApplication.ExecuteMenuItem("Window/Rendering/【tools】渲染图片 Render Image");
            }



            //Debug.Log(methodInfo.Name);
            //Debug.Log(methodInfo.ToString());

        }

    }



}







