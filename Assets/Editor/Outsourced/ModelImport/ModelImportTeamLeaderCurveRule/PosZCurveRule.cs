using System;
using System.Collections.Generic;
using TKPlugins;
using TKPlugins_Extend;
using UnityEditor;
using UnityEngine;

namespace ModelImport
{
    public class PosZCurveRule : BaseRule
    {
        private static HashSet<string> m_blackLists = new HashSet<string>()
        {
            "t_pilot",
        };

        private static string[] rebuildCurveNames =
        {
            RuleToolUtil.rootCurveBName,
            RuleToolUtil.rootCurveBNameOther,
            RuleToolUtil.weaponCurveName,
            RuleToolUtil.weaponLCurveName,
            RuleToolUtil.weaponRCurveName,
        };
        
        private List<KeyValuePair<EditorCurveBinding, AnimationCurve>> m_allRebuildCurves = new List<KeyValuePair<EditorCurveBinding, AnimationCurve>>();
        private string[] m_searchCurveNames = null;
        private int m_searchCurvesCount = 0;

        private DataGroupAsset m_dataGroupAsset = null;
        private string m_animName = string.Empty;
        
        private AnimationCurve m_rootA_pos_z = null;

        private bool m_isHurt02 = false;

        protected override void Init_Impl(ModelResAsset resAsset, AnimationClip clip)
        {
            base.Init_Impl(resAsset, clip);
            m_dataGroupAsset = RuleToolUtil.GetDataGroupAsset(m_resAsset.ModelName);
            m_animName = RuleToolUtil.GetAnimName(m_resAsset);
            m_isHurt02 = m_animName == "hurt_02";

            m_searchCurvesCount = rebuildCurveNames.Length;
            if(m_searchCurveNames == null)
                m_searchCurveNames = new string[m_searchCurvesCount];
            Array.Copy(rebuildCurveNames, m_searchCurveNames, m_searchCurvesCount);
        }

        protected override bool IsEnable()
        {
            return !m_blackLists.Contains(m_resAsset.ModelName) && RuleToolUtil.IsLowModel(m_resAsset) && RuleToolUtil.isMoveAnim(m_animName) && !m_resAsset.resSetting.ApplyRootMotion;
        }

        protected override void CollectTargetCurve_Impl(EditorCurveBinding curveBinding)
        {
            if (m_rootA_pos_z == null)
            {
                if (RuleToolUtil.isTargetCurve(curveBinding, RuleToolUtil.rootCurveAName, RuleToolUtil.posZCurvePropertyName))
                {
                    m_rootA_pos_z = AnimationUtility.GetEditorCurve(m_clip, curveBinding);
                }
            }

            for (int i = 0; i < m_searchCurvesCount; i++)
            {
                string curveName = m_searchCurveNames[i];
                if (RuleToolUtil.isTargetCurve(curveBinding, curveName, RuleToolUtil.posZCurvePropertyName, false))
                {
                    AnimationCurve curve = AnimationUtility.GetEditorCurve(m_clip, curveBinding);
                    KeyValuePair<EditorCurveBinding, AnimationCurve> pair = new KeyValuePair<EditorCurveBinding, AnimationCurve>(curveBinding, curve);
                    m_allRebuildCurves.Add(pair);

                    m_searchCurvesCount--;
                    m_searchCurveNames[i] = m_searchCurveNames[m_searchCurvesCount];
                    break;
                }
            }
        }

        protected override void ChangeCurveData_Impl()
        {
            if (m_rootA_pos_z != null && m_allRebuildCurves.Count > 0)
            {
                foreach (KeyValuePair<EditorCurveBinding, AnimationCurve> pair in m_allRebuildCurves)
                {
                    EditorCurveBinding curveBinding = pair.Key;
                    AnimationCurve curve = pair.Value;
                    RebuildCurve(curveBinding, curve);
                }
                
                UpdateCfgAnim();
            }
        }

        protected override void Clear_Impl()
        {
            base.Clear_Impl();
            
            m_allRebuildCurves.Clear();
            m_searchCurvesCount = 0;
            m_dataGroupAsset = null;
            m_animName = string.Empty;
            m_rootA_pos_z = null;
        }

        private void RebuildCurve(EditorCurveBinding curveBinding, AnimationCurve curve)
        {
            Keyframe[] keyframes = curve.keys;

            for (int i = 0; i < keyframes.Length; i++)
            {
                Keyframe keyframe = keyframes[i];
                    
                float keyframeTime = keyframe.time;
                float value = m_rootA_pos_z.Evaluate(keyframeTime);
                keyframe.value = keyframe.value - value;
                keyframes[i] = keyframe;
            }

            curve.keys = keyframes;

            for (int i = 0; i < keyframes.Length; i++)
            {
                AnimationUtility.SetKeyLeftTangentMode(curve, i, AnimationUtility.TangentMode.ClampedAuto);
            }
                
            AnimationUtility.SetEditorCurve(m_clip, curveBinding, curve);
        }

        private void UpdateCfgAnim()
        {
            if (m_dataGroupAsset == null || m_resAsset.resSetting.ApplyRootMotion)
                return;
            
            DataGroupAsset.DataItem curveItem = m_dataGroupAsset.FindDataItem(m_animName + "_Curve");
            if (curveItem != null && curveItem.type == DataTypeEnum.AnimationCurve)
            {
                AnimationCurve resultCurve = RuleToolUtil.CompressCurve(m_rootA_pos_z);
                if (resultCurve == null)
                    resultCurve = m_rootA_pos_z;
                
                for (int i = 0; i < resultCurve.keys.Length; i++)
                {
                    AnimationUtility.SetKeyLeftTangentMode(resultCurve, i, AnimationUtility.TangentMode.ClampedAuto);
                }

                if (m_isHurt02)
                    resultCurve = RuleToolUtil.ReverseCurve(resultCurve);

                curveItem.curve = resultCurve;
                
                DataGroupAssetEditor.Save(m_dataGroupAsset);
            }
            else
            {
                Debug.LogWarning("not exist " + m_animName + "_Curve");
            }
        }
    }

}