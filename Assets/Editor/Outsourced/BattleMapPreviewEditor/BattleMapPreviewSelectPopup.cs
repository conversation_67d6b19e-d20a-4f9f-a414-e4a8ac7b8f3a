using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using Color = UnityEngine.Color;
using System.Text.RegularExpressions;
using System;
using System.Globalization;
using TKFrame.Asset;
using Path = System.IO.Path;

public class BattleMapPreviewSelectPopup : PopupWindowContent
{
    private List<string> m_prefabs = new List<string>();
    private Vector2 m_scrollPos = Vector2.zero;
    private GUIStyle m_normalItemStyle;
    private bool useLod;
    private string mapDir;

    public BattleMapPreviewSelectPopup(bool useLod)
    {
        this.useLod = useLod;
    }
    
    public string SelectPrefabName { get; set; }

    public override Vector2 GetWindowSize()
    {
        return new Vector2(300, 500);
    }

    public override void OnGUI(Rect rect)
    {
        m_scrollPos = GUILayout.BeginScrollView(m_scrollPos);
        GUILayout.BeginVertical();

        Color oldColor = GUI.backgroundColor;

        string preStr = "";
        bool color1 = false;
        for (int i = m_prefabs.Count - 1; i >= 0; i--)
        {
            string currentPreStr = m_prefabs[i].Substring(0, 6);
            if (!preStr.Equals(currentPreStr))
            {
                color1 = !color1;
                preStr = currentPreStr;
            }
            GUI.backgroundColor = color1 ? Color.green : Color.blue;
            if (GUILayout.Button(m_prefabs[i], m_normalItemStyle, GUILayout.Height(20)))
            {
                SelectPrefabName = m_prefabs[i];
                editorWindow.Close();
            }
            GUILayout.Space(5);
        }
        GUI.backgroundColor = oldColor;

        GUILayout.EndVertical();
        GUILayout.EndScrollView();
    }

    public override void OnOpen()
    {
        m_normalItemStyle = new GUIStyle(GUI.skin.button);
        m_normalItemStyle.alignment = TextAnchor.MiddleLeft;
        m_normalItemStyle.normal.textColor = Color.white;
        m_normalItemStyle.fontSize = 12;
        m_normalItemStyle.hover = m_normalItemStyle.active = m_normalItemStyle.focused = m_normalItemStyle.normal;
        m_normalItemStyle.margin = new RectOffset(0, 0, 0, 0);

        m_prefabs.Clear();

        if (useLod)
        {
            mapDir = TKAssetGroupUtil.NormalPathToLodPath(ConstVar.MAP_DIR, "low");
        }
        else
        {
            mapDir = ConstVar.MAP_DIR;
        }
        
        foreach (string file in Directory.GetFiles(Application.dataPath + "/../" + mapDir))
        {
            if (Path.GetExtension(file) == ".prefab")
            {
                string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(file);
                m_prefabs.Add(fileNameWithoutExtension);
            }
        }

        // 默认排序（按字母顺序）
        m_prefabs.Sort(new NaturalStringComparer(CultureInfo.CurrentCulture));
    }

    public override void OnClose()
    {
    }

    private class NaturalStringComparer : IComparer<string>
    {
        private readonly CultureInfo _culture;

        public NaturalStringComparer(CultureInfo culture)
        {
            _culture = culture;
        }

        public int Compare(string x, string y)
        {
            if (x == y)
            {
                return 0;
            }
            if (x == null)
            {
                return -1;
            }
            if (y == null)
            {
                return 1;
            }

            var regex = new Regex(@"(\d+)|(\D+)", RegexOptions.Compiled);

            var xMatches = regex.Matches(x);
            var yMatches = regex.Matches(y);

            for (int i = 0; i < Math.Min(xMatches.Count, yMatches.Count); i++)
            {
                var xMatch = xMatches[i];
                var yMatch = yMatches[i];

                // Check if the match is a number
                if (int.TryParse(xMatch.Value, out int xNum) && int.TryParse(yMatch.Value, out int yNum))
                {
                    int comparison = xNum.CompareTo(yNum);
                    if (comparison != 0)
                    {
                        return comparison;
                    }
                }
                else
                {
                    int comparison = String.Compare(xMatch.Value, yMatch.Value, _culture, CompareOptions.IgnoreCase);
                    if (comparison != 0)
                    {
                        return comparison;
                    }
                }
            }

            // If one string is a prefix of the other, the shorter string is considered smaller
            return xMatches.Count.CompareTo(yMatches.Count);
        }
    }
}