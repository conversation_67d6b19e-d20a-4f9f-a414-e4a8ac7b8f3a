using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using ZGame.Editor;

public class ZGameLimitedSettingMetaProcessor : ZGameAssetProcessorBase
{
    public override Type ImporterType => typeof(AssetImporter);
    public override Type AssetType => typeof(ZgameLimitedSettingMeta);
    public override bool Enable { get; set; } = true;
    public override bool PostProcess(string assetPath, PostProcessType processType)
    {
        if (assetPath == ZgameLimitedSettingLoader.SettingMetaFilePath)
        {
            ZgameLimitedSettingLoader.ResetSettings();
            return true;
        }
        return false;
    }
}

public class ZgameLimitedSettingMeta : ScriptableObject
{
    [SerializeField]
    public List<ZgameLimitedSetting> SettingList;
}