#if TKF_EDITOR//TKFrame Auto Gen
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace ZGame.Editor
{
    public class ZGameTextureProcessor : ZGameAssetProcessorBase
    {
        public override Type ImporterType => typeof(TextureImporter);
        public override Type AssetType => typeof(Texture);
        public override bool Enable { get; set; } = true;
        public override bool PreProcess(AssetImporter importer)
        {
            if (!ResConfig.ZGameAssetPostProcessorConfig.Enable)
            {
                return false;
            }
            TextureImporter textureImporter = importer as TextureImporter;
            if (textureImporter == null)
            {
                return false;
            }
            if (ZgameLimitedSettingLoader.IsNull)
            {
                //如果Limited setting没有被加载，必须跳过旧的预处理系统。
                return false;
            }
            if (ZgameLimitedTextureImportCheck.FindTextureLimitSet(importer.assetPath) != null)
            {
                //由ZgameLimitedTextureImportCheck接管。
                return new ZgameLimitedTextureImportCheck(textureImporter).CheckLimitedTexture();
            }

            var dirty = CheckSetHeroMaterialTextureAbName(importer, textureImporter.assetPath);
            if (importer.assetPath.Contains("LOL_HeadIcon"))
            {
                //英雄头像资源导入
                var tag = "LOL_HeadIcon_Set" + GetSetVersion(textureImporter.assetPath);
                dirty |= textureImporter.ConfirmTextureType(TextureImporterType.Sprite);
                dirty |= textureImporter.ConfirmPackingTag(tag);
            }
            else if (importer.assetPath.Contains("TFT_Hero_HeadIcon"))
            {
                //英雄头像资源导入
                var tag = "TFT_Hero_HeadIcon_Set" + GetSetVersion(textureImporter.assetPath);
                dirty |= textureImporter.ConfirmTextureType(TextureImporterType.Sprite);
                dirty |= textureImporter.ConfirmPackingTag(tag);
            }

            if (textureImporter.assetPath.StartsWith(ms_uiatlas_dir))
            {
                //Debug.Log(textureImporter.assetPath);
                //UI贴图
                dirty |= ProcessUITexture(textureImporter);
            }
            else if (textureImporter.assetPath.StartsWith(ms_uiatlas_dir_v2))
            {
                //V2版贴图
                dirty |= ProcessUITexture(textureImporter);
            }
            //else if (textureImporter.assetPath.StartsWith(poster_dir)) {
            //    dirty |= ProcessPoster(textureImporter);
            //}
            else if (textureImporter.assetPath.StartsWith(ms_icon_dir))
            {
                //Icon贴图
                foreach (var iconDir in ms_icon_dir_list)
                {
                    if (textureImporter.assetPath.StartsWith(iconDir))
                    {
                        dirty |= ProcessIconTexture(textureImporter);
                        break;
                    }
                }
            }
            else if (textureImporter.assetPath.StartsWith(ms_tiny_dir))
            {
                // 处理小小英雄贴图
                if (IsCustomPath(textureImporter.assetPath))
                {
                    //特殊目录，美术自行设置压缩格式
                    dirty |= SetDefaultASTCCompression(textureImporter);
                }
                else
                {
                    dirty |= ProcessTinyTexture(textureImporter);
                }
            }
            else if (textureImporter.assetPath.StartsWith(ms_hero_dir))
            {
                //Debug.Log(textureImporter.assetPath);
                //英雄贴图
                dirty |= ProcessHeroTexture(textureImporter);
            }
            else if (textureImporter.assetPath.StartsWith(ms_scene_dir))
            {
                //场景贴图
                if (IsCustomPath(textureImporter.assetPath))
                {
                    //美术自己控制
                    dirty |= SetDefaultASTCCompression(textureImporter);
                }
                else
                {
                    dirty |= ProcessSceneTexture(textureImporter);
                }
            }
            else if (textureImporter.assetPath.StartsWith(ms_effect_art_dir))
            {
                //UI特效限制256*256
                if (textureImporter.assetPath.StartsWith(ms_effect_art_ui_dir))
                {
                    dirty |= ProcessEffectTexture(textureImporter, 0);
                }
                //特殊尺寸特效贴图目录让美术设置，程序不干预
                else if (textureImporter.assetPath.StartsWith(ms_effect_art_special_size_dir))
                {
                    //to do: dirty |= 
                }
                else
                {
                    bool isHeroEffect = false;
                    for (int i = 0; i < ms_hero_effect_art_dir.Length; ++i)
                    {
                        if (textureImporter.assetPath.StartsWith(ms_hero_effect_art_dir[i]))
                        {
                            isHeroEffect = true;
                            break;
                        }
                    }
                    //英雄特效限制256*256
                    if (isHeroEffect)
                    {
                        dirty |= ProcessEffectTexture(textureImporter, 256);
                    }
                    //其它特效目录限制128*128
                    else
                    {
                        dirty |= ProcessEffectTexture(textureImporter, 128);
                    }
                }
            }
            else
            {
                //其它UI目录
                foreach (string otherUIDir in ms_other_ui_dir_list)
                {
                    if (textureImporter.assetPath.StartsWith(otherUIDir))
                    {
                        //Debug.Log(textureImporter.assetPath);
                        //设置压缩
                        dirty |= SetCompression(textureImporter, 0, true, true, true);
                    }
                }
            }
            return dirty;
        }

        public static bool CheckSetHeroMaterialTextureAbName(AssetImporter importer, string texturePath)
        {
            if (texturePath.Contains("model_res") && (texturePath.Contains(".tga") ||
                                                      texturePath.Contains(".png") ||
                                                      texturePath.Contains(".jpg")))
            {
                FileInfo fileInfo = new FileInfo(texturePath);
                if (fileInfo.Directory?.Name?.Equals("Materials") == true)
                {
                    var dirty = false;
                    if (texturePath.Contains("LittleLegend"))//小小英雄贴图不需要设置abname
                    {
                        if (importer.assetBundleName != "" || importer.assetBundleVariant != "")
                        {
                            importer.SetAssetBundleNameAndVariant("", "");
                            //importer.assetBundleName = "";
                            dirty = true;
                        }
//#if ENABLE_ASSET_BUNDLE_EXTEND
//                        if (importer.assetBundleVariant != "")
//                        {
//                            importer.assetBundleVariant = "";
//                            dirty = true;
//                        }
//#endif
                        return dirty;
                    }
                    string abname = ChessHeroMaterialController.GetHeroResABPath(fileInfo.Directory.Parent.Parent.Name);
                    //if (!abname.Equals(importer.assetBundleName))
                    {
                        if (importer.assetBundleName != abname)
                        {
                            importer.assetBundleName = abname;
                            dirty = true;
                        }
#if ENABLE_ASSET_BUNDLE_EXTEND
                        if (importer.assetBundleVariant != "unity3d")
                        {
                            importer.assetBundleVariant = "unity3d";
                            dirty = true;
                        }
#endif
                        return dirty;
                    }
                }
            }

            return false;
        }
        static string ms_uiatlas_dir = "Assets/ChessArt/UIAtlas";
        //v2版UI
        static string ms_uiatlas_dir_v2 = "Assets/ChessArt/UIAtlas_V2";
        //v2版UI，不压缩处理。后续根据图集情况，可以选择压缩or不压缩。只要维护这个表即可。
        static string[] ms_uiatlas_dir_v2_argb32_list = new string[]
        {
            "Assets/ChessArt/UIAtlas_V2/v2_Common_Components",
            //"Assets/ChessArt/UIAtlas_V2/v2_Common_PopLayer",
            "Assets/ChessArt/UIAtlas_V2/v2_Common_UnderframeLayer"
        };

        //v2版UI，通用背景ETC2压缩质量损失严重，不压缩，但要限定在1024以内
        static string[] ms_backgrounddir_arba_list = new string[]
        {
            "Assets/ChessArt/UIAtlas_V2/v2_Background/Textures_rgba"
        };

        //static string[] ms_ui_dir = { "Assets/ChessArt/UIAtlas", "Assets" };
        static string ms_icon_dir = "Assets/Art_TFT_Raw/UI";
        static string poster_dir = "Assets/Art_TFT_Raw/UI/TFT_Set2/TFT_Poster";
        static string[] ms_icon_dir_list = new string[] {
            "Assets/Art_TFT_Raw/UI/TFT_FetterIcon",
            "Assets/Art_TFT_Raw/UI/TFT_Float_Text",
            "Assets/Art_TFT_Raw/UI/TFT_Goods",
            "Assets/Art_TFT_Raw/UI/TFT_Hero_HeadIcon",
            "Assets/Art_TFT_Raw/UI/TFT_HeroMin",
            "Assets/Art_TFT_Raw/UI/TFT_ItemIcon",
            "Assets/Art_TFT_Raw/UI/TFT_Level",
            "Assets/Art_TFT_Raw/UI/TFT_Level_Big",
            "Assets/Art_TFT_Raw/UI/TFT_Property",
            "Assets/Art_TFT_Raw/UI/TFT_Skill",
            "Assets/Art_TFT_Raw/UI/TFT_HeadIcon",
            "Assets/Art_TFT_Raw/UI/TFT_Map",
            "Assets/Art_TFT_Raw/UI/TFT_Emote",
            "Assets/Art_TFT_Raw/UI/TFT_TinyActionIcon",
            "Assets/Art_TFT_Raw/UI/TFT_TinyOriginIcon",
            "Assets/Art_TFT_Raw/UI/TFT_Dropbox",
        };

        static string[] ms_backgrounddir_list = new string[] {
            "Assets/ChessArt/UIAtlas/Texture",
            "Assets/ChessArt/UIAtlas/Background",
            "Assets/ChessArt/UIAtlas_V2/v2_Background"
        };

        static string[] ms_other_ui_dir_list = new string[] {
            "Assets/Art_TFT_Raw/UI/TFT_Float_Text",
            "Assets/Art_TFT_Raw/spine_file",
            "Assets/Art/FX/Textures/ui"
        };

        public static Dictionary<string, int> ms_hero_texSize_whitelist = new Dictionary<string, int>()
        {
            {"Assets/Art_TFT_Raw/model_res/hero/set1/h_aatrox", 512},
            {"Assets/Art_TFT_Raw/model_res/hero/set1/h_braum", 512},
            {"Assets/Art_TFT_Raw/model_res/hero/set1/h_gnarbig", 512},
            {"Assets/Art_TFT_Raw/model_res/hero/set1/h_kayle", 512},
        };
        static string ms_hero_dir = "Assets/Art_TFT_Raw/model_res";
        static string ms_tiny_dir = "Assets/Art_TFT_Raw/model_res/LittleLegend";
        //static string ms_effect_dir = "";

        static string ms_scene_dir = "Assets/Art_TFT/scenes";

        //特效贴图总目录
        private static string ms_effect_art_dir = "Assets/Art/FX";
        /// <summary>
        /// 特效贴图中ui贴图不受512限制
        /// </summary>
        private static string ms_effect_art_ui_dir = "Assets/Art/FX/Textures/ui";
        private static string ms_effect_art_special_size_dir = "Assets/Art/FX/Textures/CustomSizeTexture";

        private static string[] ms_hero_effect_art_dir = new string[2] {
            "Assets/Art/FX/Textures/hero_skill",
            "Assets/Art/FX/Textures/set3_hero_skill",
        };

        static string GetSpritePackingTag(string assetPath)
        {
            //取文件所在的子目录名为“packTag”
            string fullDir = Path.GetDirectoryName(assetPath);
            string packingTag = fullDir.Substring(ms_uiatlas_dir.Length + 1);

            if (packingTag.Contains('/'))
                packingTag = packingTag.Replace('/', '_');
            else if (packingTag.Contains('\\'))
                packingTag = packingTag.Replace('\\', '_');

            //Debug.Log("Sprite packing tag is " + packingTag);
            packingTag = "chess_" + packingTag;

            return packingTag;
        }

        /// <summary>
        /// 是否是背景大图
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        static bool IsBackgroundPath(string path)
        {
            foreach (string backgroundDir in ms_backgrounddir_list)
            {
                if (path.StartsWith(backgroundDir, StringComparison.CurrentCultureIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 是否是背景图需要ASTC特别处理
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        static bool IsBackgroundRGBAPath(string path)
        {
            foreach (string backgroundDir in ms_backgrounddir_arba_list)
            {
                if (path.StartsWith(backgroundDir, StringComparison.CurrentCultureIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 是否是UI图集Textures/目录下的大图，不合图集
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        static bool IsUITexturesPath(string path)
        {
            string dir = Path.GetDirectoryName(path);
            if (dir.EndsWith("Textures", StringComparison.CurrentCultureIgnoreCase))
            {
                return true;
            }
            return false;
        }


        static bool IsCustomPath(string path)
        {
            //if (path.Contains("_u."))
            //    return true;

            string dir = Path.GetDirectoryName(path);

            if (dir.EndsWith("custom", StringComparison.CurrentCultureIgnoreCase))
                return true;

            /*foreach (string customDir in ms_backgrounddir_list)
            {
                if (path.StartsWith(customDir, StringComparison.CurrentCultureIgnoreCase))
                {
                    return true;
                }
            }*/

            return false;
        }

        static bool IsCompression(string path)
        {
            foreach (string ignorePath in ms_uiatlas_dir_v2_argb32_list)
            {
                if (path.StartsWith(ignorePath, StringComparison.CurrentCultureIgnoreCase))
                {
                    return false;
                }
            }
            return true;
        }


        private bool ProcessSceneTexture(TextureImporter textureImporter)
        {
            var dirty = false;
            if (textureImporter.textureShape is TextureImporterShape.TextureCube)
            {
                dirty |= textureImporter.ConfirmMipmap(true);
                //开启Mipmap,如果设置为true，英雄边缘内发光会有毛刺感
            }
            else
            {
                dirty |= textureImporter.ConfirmMipmap(false);
                //开启Mipmap,如果设置为true，英雄边缘内发光会有毛刺感
            }
            //关闭可读写
            dirty |= textureImporter.ConfirmReadable(false);
            //设置最大贴图尺寸
            int maxSize = textureImporter.maxTextureSize;
            if (maxSize > 1024) maxSize = 1024;
            dirty |= textureImporter.ConfirmMaxSize(maxSize);
            //设置压缩
            dirty |= SetCompression(textureImporter, maxSize, true, true, false, false, true);
            return dirty;
        }

        private bool ProcessPoster(TextureImporter textureImporter)
        {
            return false;
            //修改packingTag;
            string filename = textureImporter.assetPath.Substring(textureImporter.assetPath.LastIndexOf("/") + 1);
            filename = filename.Substring(0, filename.IndexOf('.'));
            textureImporter.assetBundleName = "art_tft_raw/ui/tft_set2/tft_poster/" + filename;
        }

        /// <summary>
        /// 处理UI纹理
        /// </summary>
        /// <param name="importer"></param>
        private bool ProcessUITexture(TextureImporter textureImporter)
        {
            var dirty = false;
            //UI贴图
            if (IsCustomPath(textureImporter.assetPath))
            {
                //custom目录内的资源可以设置为true color，或者RawTexture，程序不做强制处理
                dirty |= textureImporter.ConfirmCompression(TextureImporterCompression.Uncompressed);

                //是否带有Alpha通道
                bool haveAlpha = textureImporter.DoesSourceTextureHaveAlpha();
                if (textureImporter.alphaSource == TextureImporterAlphaSource.None)
                {
                    haveAlpha = false;
                }

                //windows平台
                var dirty1 = false;
                TextureImporterPlatformSettings settings = textureImporter.GetPlatformTextureSettings("Windows");
                dirty1 |= settings.ConfirmPlatformFormat(haveAlpha
                    ? TextureImporterFormat.RGBA32
                    : TextureImporterFormat.RGB24);
                dirty1 |= settings.ConfirmPlatformOverriden(true);
                if (dirty1)
                {
                    textureImporter.SetPlatformTextureSettings(settings);
                    dirty = true;
                }

                var dirty2 = false;
                //android平台
                settings = textureImporter.GetPlatformTextureSettings("Android");
                dirty2 |= settings.ConfirmPlatformFormat(haveAlpha
                    ? TextureImporterFormat.RGBA32
                    : TextureImporterFormat.RGB24);
                dirty2 |= settings.ConfirmPlatformOverriden(true);
                if (dirty2)
                {
                    textureImporter.SetPlatformTextureSettings(settings);
                    dirty = true;
                }

                var dirty3 = false;
                //iPhone平台
                settings = textureImporter.GetPlatformTextureSettings("iPhone");
                dirty3 |= settings.ConfirmPlatformFormat(haveAlpha
                    ? TextureImporterFormat.RGBA32
                    : TextureImporterFormat.RGB24);
                dirty3 |= settings.ConfirmPlatformOverriden(true);
                if (dirty3)
                {
                    textureImporter.SetPlatformTextureSettings(settings);
                    dirty = true;
                }

            }
            else if (IsBackgroundPath(textureImporter.assetPath))
            {
                if (IsBackgroundRGBAPath(textureImporter.assetPath))
                {
                    dirty |= SetTextureFormat(textureImporter, TextureImporterType.Default, false);
                }
                else
                {
                    //背景类贴图
                    dirty |= SetTextureFormat(textureImporter);
                }
            }
            else if (IsUITexturesPath(textureImporter.assetPath))
            {
                //UI目录下的大图类贴图
                dirty |= SetTextureFormat(textureImporter);
            }
            else
            {
                //其它UI Atlas图集贴图

                string dir = Path.GetDirectoryName(textureImporter.assetPath);
                AssetImporter importer = AssetImporter.GetAtPath(dir);
                if (string.IsNullOrEmpty(importer.assetBundleName))
                {
                    DelegateInterface.ZGameEditorUtility_SetAssetBundleName?.Invoke(importer);
                }

                dirty |= textureImporter.ConfirmPackingTag(GetSpritePackingTag(textureImporter.assetPath));
                dirty |= textureImporter.ConfirmTextureType(TextureImporterType.Sprite);
                dirty |= textureImporter.ConfirmCompression(TextureImporterCompression.Uncompressed);
                dirty |= textureImporter.ConfirmMaxSize(1024);
                dirty |= textureImporter.ConfirmFilterMode(FilterMode.Bilinear);
                //判断下是否需要压缩 白名单：ms_uiatlas_dir_v2_argb32_list
                bool isCompression = IsCompression(textureImporter.assetPath);
                //设置压缩
                dirty |= SetCompression(textureImporter, 1024, isCompression, true, true);
            }

            //UI都不需要开Mipmap
            dirty |= textureImporter.ConfirmMipmap(false);
            //关闭读写
            dirty |= textureImporter.ConfirmReadable(false);
            return dirty;
        }

        /// <summary>
        /// 设置图片格式，主要是背景图一类的大图
        /// </summary>
        private bool SetTextureFormat(TextureImporter textureImporter, TextureImporterType type = TextureImporterType.Default, bool isCompression = true)
        {
            var dirty = false;
            dirty |= textureImporter.ConfirmTextureType(type);
            var hasAlpha = textureImporter.DoesSourceTextureHaveAlpha();//是否含有alpha通道
            if (textureImporter.alphaSource == TextureImporterAlphaSource.None)
            {
                hasAlpha = false;
            }
            dirty |= textureImporter.ConfirmAlpha(hasAlpha);
            textureImporter.wrapMode = TextureWrapMode.Clamp;
            var dirty1 = false;
            //安卓检测
            TextureImporterPlatformSettings setting_Android = textureImporter.GetPlatformTextureSettings("Android");

            if (setting_Android.overridden == false || setting_Android.maxTextureSize < 2048 || (setting_Android.format != TextureImporterFormat.ASTC_RGBA_6x6) == (setting_Android.format != TextureImporterFormat.ASTC_RGB_6x6) ||
                setting_Android.compressionQuality <= 50 || isCompression == false) //100:Best
            {
                dirty1 |= setting_Android.ConfirmPlatformOverriden(true);
                var maxSize = 2048;
                dirty1 |= setting_Android.ConfirmPlatformHighQuality();


                //尺寸决定是否带透明通道
                GetTextureOriginalSize(textureImporter, out int width, out int height);
                var texture = AssetDatabase.LoadAssetAtPath<Texture>(textureImporter.assetPath);

                if (texture != null && width < 1624 && height < 1624 && hasAlpha)
                {
                    if (!isCompression)
                    {//安卓如果不压缩，最大尺寸需要限定在1024.
                        maxSize = 1024;
                        dirty1 |= setting_Android.ConfirmPlatformFormat(TextureImporterFormat.ASTC_RGBA_4x4);
                    }
                    else
                    {
                        dirty1 |= setting_Android.ConfirmPlatformFormat(TextureImporterFormat.ASTC_RGBA_6x6);
                    }
                }
                else
                {
                    if (!isCompression)
                    {//安卓如果不压缩，最大尺寸需要限定在1024.
                        maxSize = 1024;
                        dirty1 |= setting_Android.ConfirmPlatformFormat(hasAlpha ? TextureImporterFormat.ASTC_RGBA_4x4 : TextureImporterFormat.ASTC_RGB_4x4);
                    }
                    else
                    {
                        dirty1 |= setting_Android.ConfirmPlatformFormat(hasAlpha ? TextureImporterFormat.ASTC_RGBA_6x6 : TextureImporterFormat.ASTC_RGB_6x6);
                    }
                }
                dirty1 |= setting_Android.ConfirmPlatformMaxSize(maxSize);
                if (dirty1)
                {
                    textureImporter.SetPlatformTextureSettings(setting_Android);
                    dirty = true;
                }
            }

            var dirty2 = false;
            //IOS检测
            TextureImporterPlatformSettings setting_IOS = textureImporter.GetPlatformTextureSettings("iPhone");

            if (setting_IOS.overridden == false || setting_IOS.maxTextureSize < 2048 ||
                (setting_IOS.format != TextureImporterFormat.ASTC_RGB_6x6 &&
                 setting_IOS.format != TextureImporterFormat.ASTC_RGBA_6x6 &&
                 setting_IOS.format != TextureImporterFormat.ASTC_RGBA_4x4 &&
                 setting_IOS.format != TextureImporterFormat.ASTC_RGB_4x4) ||
                setting_IOS.compressionQuality <= 50) //100:Best 
            {
                dirty2 |= setting_IOS.ConfirmPlatformOverriden(true);
                dirty2 |= setting_IOS.ConfirmPlatformMaxSize(2048);
                dirty2 |= setting_IOS.ConfirmPlatformHighQuality();

                //尺寸决定是否带透明通道
                dirty2 |= setting_IOS.ConfirmPlatformFormat(hasAlpha
                    ? TextureImporterFormat.ASTC_RGBA_6x6
                    : TextureImporterFormat.ASTC_RGB_6x6);
                /*
                GetTextureOriginalSize(textureImporter, out width, out height);
                var texture = AssetDatabase.LoadAssetAtPath<Texture>(assetPath);
                if (width < 1624 && height < 1624 && hasAlpha)
                {
                    setting_IOS.format = TextureImporterFormat.ASTC_RGBA_6x6;
                }
                else
                {
                    setting_IOS.format = TextureImporterFormat.ASTC_RGB_6x6;
                }
*/
                if (dirty2)
                {
                    textureImporter.SetPlatformTextureSettings(setting_IOS);
                    dirty = true;
                }
            }
            return dirty;
        }

        private bool ProcessIconTexture(TextureImporter textureImporter)
        {
            var dirty = false;
            dirty |= textureImporter.ConfirmTextureType(TextureImporterType.Sprite);
            dirty |= textureImporter.ConfirmCompression(TextureImporterCompression.Uncompressed);
            dirty |= textureImporter.ConfirmMaxSize(1024);

            //设置压缩
            dirty |= SetCompression(textureImporter, 1024, true, true, true);

            if (textureImporter.assetPath.Contains("Assets/Art_TFT_Raw/UI/TFT_HeroMin"))
            {
                TextureImporterSettings ts = new TextureImporterSettings();
                textureImporter.ReadTextureSettings(ts);
                if (ts.spriteMeshType != SpriteMeshType.FullRect)
                {
                    Debug.Log("spriteMeshType from " + ts.spriteMeshType + " to : " + SpriteMeshType.FullRect);
                    ts.spriteMeshType = SpriteMeshType.FullRect;
                    textureImporter.SetTextureSettings(ts);
                    dirty = true;
                }
            }

            //UI都不需要开Mipmap
            dirty |= textureImporter.ConfirmMipmap(false);
            //关闭读写
            dirty |= textureImporter.ConfirmReadable(false);
            return dirty;
        }

        /// <summary>
        /// 处理小小影贴图
        /// </summary>
        /// <param name="importer"></param>
        private bool ProcessTinyTexture(TextureImporter importer)
        {
            var dirty = false;
            dirty |= importer.ConfirmMipmap(false);
            dirty |= importer.ConfirmReadable(false);
            // 局内设置为256x256
            int maxTextureSize = 1024;
            if (importer.assetPath.Contains("/low/"))
            {
                maxTextureSize = 256;
                dirty |= importer.ConfirmMaxSize(maxTextureSize);
            }
            if (importer.assetPath.Contains("/model_res/LittleLegend/t_"))
            {
                dirty |= TexturePackTools.SetTinyHeroTexturesAbName(importer);
            }

            dirty |= SetCompression(importer, maxTextureSize, true, true, false, true);
            return dirty;
        }

        /// <summary>
        /// 处理英雄纹理
        /// </summary>
        /// <param name="importer">纹理导入器</param>
        private bool ProcessHeroTexture(TextureImporter importer)
        {
            var dirty = false;
            //Mipmap如果设置为true，英雄边缘内发光会有毛刺感
            dirty |= importer.ConfirmMipmap(false);
            //关闭可读写
            dirty |= importer.ConfirmReadable(false);
            //设置最大贴图尺寸
            GetTextureOriginalSize(importer, out var width, out var height);
            //使用强制贴图边界拉伸
            //importer.wrapMode = TextureWrapMode.Clamp;//部分英雄贴图需要Repeat，不能强制
            //设置压缩
            //1、如果是1024*512的，直接压缩成512的
            int texSize = 256;
            if (width == 1024 && height == 512)
            {
                dirty |= importer.ConfirmMaxSize(512);
                texSize = 512;
            }
            //2、走白名单的，例如本来就是512的，还保持为512的
            else
            {
                int heroTexSize = GetHeroTexSizeFromWhiteList(importer);
                dirty |= importer.ConfirmMaxSize(heroTexSize);
                texSize = heroTexSize;
            }
            dirty |= TexturePackTools.SetHeroTexturesAbName(importer);
            dirty |= SetCompression(importer, texSize, true, true, false, false);
            return dirty;
        }

        private static int GetHeroTexSizeFromWhiteList(TextureImporter importer)
        {
            foreach (var item in ms_hero_texSize_whitelist)
            {
                if (importer.assetPath.StartsWith(item.Key))
                    return item.Value;
            }
            //3、默认压缩成256的
            return 256;
        }

        //获取图片的原始大小
        public static void GetTextureOriginalSize(TextureImporter ti, out int width, out int height)
        {
            if (ti == null)
            {
                width = 0;
                height = 0;
                return;
            }
            object[] args = new object[2] { 0, 0 };
            System.Reflection.MethodInfo mi = typeof(TextureImporter).
                GetMethod("GetWidthAndHeight", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            mi.Invoke(ti, args);
            width = (int)args[0];
            height = (int)args[1];
        }

        /// <summary>
        /// 处理特效纹理
        /// </summary>
        /// <param name="importer"></param>
        private bool ProcessEffectTexture(TextureImporter importer, int maxSize = 256)
        {
            var dirty = importer.ConfirmMipmap(false);
            //关闭读写
            //importer.isReadable = false;
            //importer.maxTextureSize = maxSize;
            //设置压缩
            dirty |= SetCompression(importer, maxSize, true, true);
            return dirty;
        }

        protected bool HaveAlpha(TextureImporter importer)
        {
            //是否带有Alpha通道
            bool haveAlpha = importer.DoesSourceTextureHaveAlpha();
            if (importer.alphaSource == TextureImporterAlphaSource.None)
            {
                haveAlpha = false;
            }
            return haveAlpha;
        }

        /// <summary>
        /// 设置压缩
        /// </summary>
        /// <param name="importer">导入器</param>
        /// <param name="compression">是否压缩</param>
        /// <param name="iosASTC">iOS是否ASTC</param>
        /// <param name="removeAlpha">强制移除Alpha通道</param>
        private bool SetCompression(TextureImporter importer, int texSize, bool compression, bool iOSASTC = false,
            bool isUIAtals = false, bool ingoreDefalutMaxSize = false, bool isSceneTexture = false)
        {
            //是否带有Alpha通道
            bool haveAlpha = HaveAlpha(importer);

            var dirty1 = false;
            //Defalut
            if (!ingoreDefalutMaxSize)
            {
                TextureImporterPlatformSettings defalutSettings = importer.GetDefaultPlatformTextureSettings();
                int defalutSize = defalutSettings.maxTextureSize;
                texSize = (texSize > defalutSize) ? defalutSize : texSize;
                if (texSize > 0)
                {
                    dirty1 |= defalutSettings.ConfirmPlatformMaxSize(texSize);
                    if (dirty1)
                    {
                        importer.SetPlatformTextureSettings(defalutSettings);
                    }
                }
            }

            var dirty2 = false;
            //windows平台
            TextureImporterPlatformSettings settings = importer.GetPlatformTextureSettings("Windows");
            //UI贴图避免带alpha和不带alpha生成两张图集导致不能合批，就不区分alpha
            if (isUIAtals)
            {
                dirty2 |= settings.ConfirmPlatformFormat(TextureImporterFormat.RGBA32);
            }
            else
            {
                dirty2 |= settings.ConfirmPlatformFormat(haveAlpha ? TextureImporterFormat.RGBA32 : TextureImporterFormat.RGB24);
            }
            dirty2 |= settings.ConfirmPlatformHighQuality();
            if (texSize > 0)
            {
                //场景贴图不设置maxTextureSize,继承defalutSettings里面的maxTextureSize，便于美术去override maxTextureSize
                if (!isSceneTexture)
                {
                    dirty2 |= settings.ConfirmPlatformMaxSize(texSize);
                }
            }
            if (dirty2)
            {
                importer.SetPlatformTextureSettings(settings);
            }

            var dirty3 = false;
            //处理Android平台
            settings = importer.GetPlatformTextureSettings("Android");
            dirty3 |= settings.ConfirmPlatformOverriden(true);
            dirty3 |= settings.ConfirmPlatformHighQuality();
            //UI贴图避免带alpha和不带alpha生成两张图集导致不能合批，就不区分alpha
            //安卓图集压缩格式设置成ASTC
            if (isUIAtals)
            {
                if (compression)
                {
                    if (!IsASTC(settings.format))
                        dirty3 |= settings.ConfirmPlatformFormat(TextureImporterFormat.ASTC_RGBA_5x5);
                }
                else
                {
                    dirty3 |= settings.ConfirmPlatformFormat(TextureImporterFormat.RGBA32);
                }
            }
            else
            {
                if (compression)
                {
                    if (!IsASTC(settings.format))
                        dirty3 |= settings.ConfirmPlatformFormat(haveAlpha ? TextureImporterFormat.ASTC_RGBA_6x6 : TextureImporterFormat.ASTC_RGB_6x6);
                }
                else
                {
                    dirty3 |= settings.ConfirmPlatformFormat(haveAlpha ? TextureImporterFormat.RGBA32 : TextureImporterFormat.RGB24);
                }
            }
            if (texSize > 0)
            {
                ////场景贴图不设置maxTextureSize,继承defalutSettings里面的maxTextureSize，便于美术去override maxTextureSize
                if (!isSceneTexture)
                {
                    dirty3 |= settings.ConfirmPlatformMaxSize(texSize);
                }
            }
            if (dirty3)
            {
                importer.SetPlatformTextureSettings(settings);
            }

            var dirty4 = false;
            //处理iPhone平台
            settings = importer.GetPlatformTextureSettings("iPhone");
            dirty4 |= settings.ConfirmPlatformOverriden(true);
            dirty4 |= settings.ConfirmPlatformHighQuality();
            if (texSize > 0)
            {
                ////场景贴图不设置maxTextureSize,继承defalutSettings里面的maxTextureSize，便于美术去override maxTextureSize
                if (!isSceneTexture)
                {
                    dirty4 |= settings.ConfirmPlatformMaxSize(texSize);
                }
            }
            if (!compression)
            {
                // iOS does not support RGB24 natively, and during runtime, the iOS device will convert RGB24 to RGBA32, this is a waste of CPU and RAM
                dirty4 |= settings.ConfirmPlatformFormat(TextureImporterFormat.RGBA32);
            }
            else if (iOSASTC)
            {
                if (isUIAtals)
                {
                    if (!IsASTC(settings.format))
                        dirty4 |= settings.ConfirmPlatformFormat(TextureImporterFormat.ASTC_RGBA_5x5);
                }
                else
                {
                    if (!IsASTC(settings.format))
                        dirty4 |= settings.ConfirmPlatformFormat(haveAlpha ? TextureImporterFormat.ASTC_RGBA_6x6 : TextureImporterFormat.ASTC_RGB_6x6);
                }
            }
            else
            {
                if (isUIAtals)
                {
                    dirty4 |= settings.ConfirmPlatformFormat(TextureImporterFormat.PVRTC_RGBA4);
                }
                else
                {
                    dirty4 |= settings.ConfirmPlatformFormat(haveAlpha ? TextureImporterFormat.PVRTC_RGBA4 : TextureImporterFormat.PVRTC_RGB4);
                }
            }
            if (dirty4)
            {
                importer.SetPlatformTextureSettings(settings);
            }
            return dirty1 | dirty2 | dirty3 | dirty4;
        }

        private bool SetDefaultASTCCompression(TextureImporter importer)
        {
            //是否带有Alpha通道
            bool haveAlpha = HaveAlpha(importer);
            var format = haveAlpha ? TextureImporterFormat.ASTC_RGBA_6x6 : TextureImporterFormat.ASTC_RGB_6x6;

            var dirty1 = false;
            //处理Android平台
            var settings = importer.GetPlatformTextureSettings("Android");
            dirty1 |= settings.ConfirmPlatformOverriden(true);
            dirty1 |= settings.ConfirmPlatformHighQuality();
            //安卓图集压缩格式设置成ASTC
            if (!IsASTC(settings.format))
            {
                dirty1 |= settings.ConfirmPlatformFormat(format);
            }
            if (dirty1)
            {
                importer.SetPlatformTextureSettings(settings);
            }

            var dirty2 = false;
            //处理iPhone平台
            settings = importer.GetPlatformTextureSettings("iPhone");
            dirty2 |= settings.ConfirmPlatformOverriden(true);
            dirty2 |= settings.ConfirmPlatformHighQuality();
            if (!IsASTC(settings.format))
            {
                dirty2 |= settings.ConfirmPlatformFormat(format);
            }
            if (dirty2)
            {
                importer.SetPlatformTextureSettings(settings);
            }
            return dirty1 | dirty2;
        }

        // 在ASTC范围内 美术可以自定义ASTC压缩强度
        private bool IsASTC(TextureImporterFormat format)
        {
            return format == TextureImporterFormat.ASTC_RGBA_10x10
                || format == TextureImporterFormat.ASTC_RGBA_12x12
                || format == TextureImporterFormat.ASTC_RGBA_4x4
                || format == TextureImporterFormat.ASTC_RGBA_5x5
                || format == TextureImporterFormat.ASTC_RGBA_6x6
                || format == TextureImporterFormat.ASTC_RGBA_8x8
                || format == TextureImporterFormat.ASTC_RGB_10x10
                || format == TextureImporterFormat.ASTC_RGB_12x12
                || format == TextureImporterFormat.ASTC_RGB_4x4
                || format == TextureImporterFormat.ASTC_RGB_5x5
                || format == TextureImporterFormat.ASTC_RGB_6x6
                || format == TextureImporterFormat.ASTC_RGB_8x8;
        }
        private string GetSetVersion(string path)
        {
            const string tag = "TFT_Set";
            var strs = path.Split('/');
            foreach (var s in strs)
            {
                if (s.Contains(tag))
                {
                    var version = s.Replace(tag, "").Trim();
                    if(!version.IsNullOrEmpty())
                    {
                        return version;
                    }
                }
                else if(s.Contains("TFT_Boss"))
                {
                    return "Boss";
                }
                else if (s.Contains("TFT_Plus"))
                {
                    return "Plus";
                }
            }
            return "None";
        }
    }

    public static class ZGameTextureImpoertExtension
    {
        public static bool verboseLog = false;
        public static bool ConfirmMipmap(this TextureImporter imp, bool value)
        {
            if (imp.mipmapEnabled != value)
            {
                if (verboseLog)
                {
                    Debug.Log("mipmapEnabled from " + imp.mipmapEnabled + " to : " + value);
                }
                imp.mipmapEnabled = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmReadable(this TextureImporter imp,bool value)
        {
            if (imp.isReadable != value)
            {
                if (verboseLog)
                {
                    Debug.Log("isReadable from " + imp.isReadable + " to : " + value);
                }
                imp.isReadable = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmMaxSize(this TextureImporter imp, int value)
        {
            if (imp.maxTextureSize != value)
            {
                if (verboseLog)
                {
                    Debug.Log("maxTextureSize from " + imp.maxTextureSize + " to : " + value);
                }
                imp.maxTextureSize = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmFilterMode(this TextureImporter imp, FilterMode value)
        {
            if (imp.filterMode != value)
            {
                if (verboseLog)
                {
                    Debug.Log("maxTextureSize from " + imp.filterMode + " to : " + value);
                }
                imp.filterMode = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmTextureType(this TextureImporter imp, TextureImporterType value)
        {
            if (imp.textureType != value)
            {
                if (verboseLog)
                {
                    Debug.Log("textureType from " + imp.textureType + " to : " + value);
                }
                imp.textureType = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmAlpha(this TextureImporter imp, bool value)
        {
            if (imp.alphaIsTransparency != value)
            {
                if (verboseLog)
                {
                    Debug.Log("alphaIsTransparency from " + imp.alphaIsTransparency + " to : " + value);
                }
                imp.alphaIsTransparency = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmCompression(this TextureImporter imp, TextureImporterCompression value)
        {
            if (imp.textureCompression != value)
            {
                if (verboseLog)
                {
                    Debug.Log("textureCompression from " + imp.textureCompression + " to : " + value);
                }
                imp.textureCompression = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmPackingTag(this TextureImporter imp, string value)
        {
            if (imp.spritePackingTag != value)
            {
                if (verboseLog)
                {
                    Debug.Log("spritePackingTag from " + imp.spritePackingTag + " to : " + value);
                }
                imp.spritePackingTag = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmPlatformOverriden(this TextureImporterPlatformSettings setting, bool value)
        {
            if (setting.overridden != value)
            {
                if (verboseLog)
                {
                    Debug.Log("platform overridden from " + setting.overridden + " to : " + value);
                }
                setting.overridden = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmPlatformHighQuality(this TextureImporterPlatformSettings setting)
        {
            if (setting.compressionQuality <= 50)
            {
                if (verboseLog)
                {
                    Debug.Log("platform compressionQuality from " + setting.compressionQuality + " to : 100");
                }
                setting.compressionQuality = 100;
                return true;
            }
            return false;
        }
        public static bool ConfirmPlatformFormat(this TextureImporterPlatformSettings setting, TextureImporterFormat value)
        {
            if (setting.format != value)
            {
                if (verboseLog)
                {
                    Debug.Log("platform format from " + setting.format + " to : " + value);
                }
                setting.format = value;
                return true;
            }
            return false;
        }
        public static bool ConfirmPlatformMaxSize(this TextureImporterPlatformSettings setting, int value)
        {
            if (setting.maxTextureSize != value)
            {
                if (verboseLog)
                {
                    Debug.Log("platform maxTextureSize from " + setting.maxTextureSize + " to : " + value);
                }
                setting.maxTextureSize = value;
                return true;
            }
            return false;
        }
    }
}
#endif //TKFrame Auto Gen
