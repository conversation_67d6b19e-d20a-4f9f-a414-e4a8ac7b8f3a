using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using UnityEngine;
using GameFramework.FMath;
using UnityEditor;
using UnityEngine.SceneManagement;

namespace SDFTool
{
    public class SDFPathTool
    {
        [MenuItem("Assets/场景地图/查看地图")]
        public static void SeeMap()
        {
            string guid = Selection.assetGUIDs[0];
            string path = AssetDatabase.GUIDToAssetPath(guid);
            TextAsset textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
            SDFPathConfig sdfPath = new SDFPathConfig(textAsset);
            string pngName = System.IO.Path.GetFileNameWithoutExtension(path);
            ToMapPNG(sdfPath, pngName);
        }

        /*
        private void DrawCircle(Texture2D texture, Fix64 x, Fix64 y, Fix64 r)
        {
            int indexX = (x / m_runtimeRealMapSize * m_runtimeMapSize).Floor() + m_runtimeHalfMapSize;
            int indexY = (y / m_runtimeRealMapSize * m_runtimeMapSize).Floor() + m_runtimeHalfMapSize;
            int indexR = (r / m_runtimeRealMapSize * m_runtimeMapSize).Floor();

            MidPointCircle.MidpointCircle midPointCircle = new MidPointCircle.MidpointCircle(indexX, indexY, indexR);
            midPointCircle.Calculate();

            foreach (MidPointCircle.CirclePoint point in midPointCircle.allPoints)
            {
                texture.SetPixel(point.x, point.y, Color.red);
            }
        }
        */

        // 生成二值图像
        public static SDFPathConfig Gen(string sceneName, 
            float SDFThreshold, float SDFFloorThreshold, 
            Rect mapArea, 
            List<Rect> whiteMapRectList, 
            List<Vector2> tinyStartPoint, 
            bool needReCalHeight,
            List<ChessMapLogicEvent> eventLogicList)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            SDFPathConfig sdfPathConfig = new SDFPathConfig(sceneName);
            var realMapSize = GenZeroOneMap(ref sdfPathConfig.m_points, ref sdfPathConfig.m_triggerPoints, ref sdfPathConfig.m_heights, ref sdfPathConfig.m_triggers, SDFThreshold, SDFFloorThreshold, mapArea, whiteMapRectList);
            //sdfPathConfig.m_turningPoints = SDFPathCore.GenTurningPoints(sdfPathConfig.m_points);
            UnityEngine.Debug.Log("生成二值数据: " + stopwatch.Elapsed);
            if (needReCalHeight)
                SDFCoreTool.ReCalHeight(ref sdfPathConfig.m_heights, mapArea);
            
            ToMapPNG(sdfPathConfig, "二值图");
            //UnityEngine.Debug.Log("生成二值图: " + stopwatch.Elapsed);
            
            SDFCoreTool.EudlideanDistanceTransfromMap(ref sdfPathConfig.m_points, realMapSize);
            UnityEngine.Debug.Log("欧式距离变换: " + stopwatch.Elapsed);
            
            Save(sdfPathConfig, realMapSize, tinyStartPoint, eventLogicList);
            UnityEngine.Debug.Log("保存数据: " + stopwatch.Elapsed);
        
            ToMapPNG(sdfPathConfig, "有向图0");
            //UnityEngine.Debug.Log("生成有向图: " + stopwatch.Elapsed);
            //sdfPathConfig.ReadDataInEditor();
            //UnityEngine.Debug.Log("读取数据: " + stopwatch.Elapsed);
            //sdfPathConfig.ToMapPNG("有向图_重新读取");
            //UnityEngine.Debug.Log("生成读取后数据的有向图: " + stopwatch.Elapsed);
            return sdfPathConfig;
        }   
        
        private static float GenZeroOneMap(ref Fix64[,] points, ref byte[,] triggerPoints, ref Fix64[,] heights, ref List<string> triggers, float SDFThreshold, float SDFFloorThreshold, Rect mapArea, List<Rect> whiteMapRectList)
        {
            points = new Fix64[SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE];
            triggerPoints = new byte[SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE];
            heights = new Fix64[SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE];
            triggers = new List<string>();

            var scene = SceneManager.GetActiveScene();
            var roots = scene.GetRootGameObjects();
            List<Collider> colliderTriggers = new List<Collider>();
            foreach (var root in roots)
            {
                var cs = root.GetComponentsInChildren<Collider>();
                foreach (var c in cs)
                {
                    if (c.isTrigger)
                    {
                        colliderTriggers.Add(c);
                        c.isTrigger = false;
                    }
                }
            }

            var realMapSize = Math.Max(Math.Max(SDFPathCore.REAL_MAP_SIZE, mapArea.width), mapArea.height);     // 选最大的
            var realMapSizePer = SDFPathCore.MAP_SIZE / realMapSize; // 和真实地图大小的比例

            RaycastHit[] results;
            for (int x = -SDFPathCore.HALF_MAP_SIZE; x < SDFPathCore.HALF_MAP_SIZE; ++x)
            {
                for (int y = -SDFPathCore.HALF_MAP_SIZE; y < SDFPathCore.HALF_MAP_SIZE; ++y)
                {
                    float realX = x / realMapSizePer;
                    float realY = y / realMapSizePer;

                    bool isWhiteRect = false; // 白名单里面的区域 一定是可以走的
                    if (mapArea.Contains(new Vector2(realX, realY)))
                    {
                        for (int i = 0; i < whiteMapRectList.Count; ++i)
                        {
                            if (whiteMapRectList[i].Contains(new Vector2(realX, realY)))
                            {
                                isWhiteRect = true;
                                break;
                            }
                        }

                        // 从高处射下一条射线 判断它是否碰撞到物体
                        float maxHeight = 10;   // 最大高度
                        Ray downRay = new Ray(new Vector3(realX, maxHeight, realY), Vector3.down);
                        results = Physics.RaycastAll(downRay, maxHeight * 2);

                        if (results != null && results.Length > 0)
                        {
                            // 拿到距离最近的点
                            Collider nearNoTriggerCollider = null;
                            float nearNoTriggerDistance = isWhiteRect ? (maxHeight + SDFFloorThreshold) : 9999;
                            Collider nearTriggerCollider = null;
                            float nearTriggerDistance = 9999;

                            for (int i = 0; i < results.Length; ++i)
                            {
                                if (colliderTriggers.Contains(results[i].collider))
                                {
                                    if (nearTriggerCollider == null || nearTriggerDistance > results[i].distance)
                                    {
                                        nearTriggerCollider = results[i].collider;
                                        nearTriggerDistance = results[i].distance;
                                    }
                                }
                                else
                                {
                                    if (nearNoTriggerCollider == null || nearNoTriggerDistance > results[i].distance)
                                    {
                                        if (isWhiteRect)
                                        { // 保证白名单位置的一定是可以走的
                                            if (results[i].distance < (maxHeight - SDFThreshold))
                                                continue;
                                        }

                                        nearNoTriggerCollider = results[i].collider;
                                        nearNoTriggerDistance = results[i].distance;
                                    }
                                }
                            }

                            // 生成不可移动的图
                            {
                                var distance = nearNoTriggerDistance;
                                if (distance > (maxHeight + SDFFloorThreshold))        // 这个碰撞体在地下 不可以下去
                                {
                                    points[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = Fix64.zero;
                                }
                                else if (distance < (maxHeight - SDFThreshold))      // 这个碰撞体太高了 不可以上去
                                {
                                    points[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = Fix64.zero;
                                }
                                else
                                {
                                    points[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = Fix64.one;
                                }
                                heights[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = Fix64.FromSingle(maxHeight - distance);
                            }

                            // 生成触发器图
                            {
                                var collider = nearTriggerCollider;
                                //var distance = nearTriggerDistance;
                                if (colliderTriggers.Contains(collider))
                                {
                                    int triggerIdx = triggers.IndexOf(collider.name);
                                    if (triggerIdx == -1)
                                    {
                                        triggerIdx = triggers.Count;
                                        triggers.Add(collider.name);
                                    }
                                    triggerPoints[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = (byte)triggerIdx;
                                }
                                else
                                {
                                    triggerPoints[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = SDFPathCore.DEFAULT_TRIGGER;
                                }
                            }

                            continue;
                        }
                    }

                    points[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = isWhiteRect ? Fix64.one : Fix64.zero;
                    triggerPoints[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = SDFPathCore.DEFAULT_TRIGGER;
                    heights[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE] = isWhiteRect ? Fix64.FromSingle(0.5f) : Fix64.FromSingle(-1f);
                }
            }

            foreach (var c in colliderTriggers)
            {
                c.isTrigger = true;
            }

            return realMapSize;
        }
        
        public static void ToMapPNG(SDFPathConfig sdfPathConfig, string fileName)
        {
            Texture2D texture = new Texture2D(SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE, TextureFormat.ARGB32, false);
            //Texture2D texture_1 = new Texture2D(SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE);
            //Texture2D texture_h = new Texture2D(SDFPathCore.MAP_SIZE, SDFPathCore.MAP_SIZE);
            
            Fix64 maxR = Fix64.zero;
            Fix64 minR = Fix64.maxValue;
            float maxT = 0;
            //Fix64 maxH = Fix64.minValue;
            //Fix64 minH = Fix64.maxValue;
            
            for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
            {
                for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                {
                    var r = sdfPathConfig.GetR(x, y);
                    maxR = Fix64.Max(maxR, r);
                    minR = Fix64.Min(minR, r);

                    var t = sdfPathConfig.m_triggerPoints[x, y];
                    maxT = Math.Max(maxT, t);

                    //var h = m_heights[x + SDFPathCore.HALF_MAP_SIZE, y + SDFPathCore.HALF_MAP_SIZE];
                    //maxH = Fix64.Max(maxH, h);
                    //minH = Fix64.Min(minH, h);
                }
            }
            for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
            {
                for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                {
                    Fix64 r = sdfPathConfig.GetR(x, y);
                    if (r >= Fix64.zero)
                    {
                        float rf = (r / maxR).ToSingle();
                        texture.SetPixel(x, y, new Color(0, 1, 0, 0.2f));
                    }
                    else
                    {
                        //if (m_turningPoints.Contains(new Vector2Int(x, y)))
                        //{
                        //    texture.SetPixel(x, y, new Color(0, 0, 1, 1));
                        //}
                        //else
                        {
                            float rf = (r / minR).ToSingle();
                            texture.SetPixel(x, y, new Color(1, 0, 0, 0.2f));
                        }
                    }

                    //var t = m_triggerPoints[x, y];
                    //float rt = t / maxT;
                    //texture_1.SetPixel(x, y, new Color(rt, rt, rt, 1));

                    //var h = m_heights[x, y];
                    //h += Fix64.one;
                    //float rh = h.ToSingle() / 2f;
                    //texture_h.SetPixel(x, y, new Color(rh, rh, rh, 1));
                }
            }
            

            var path = SDFCoreTool.GetPathName(sdfPathConfig.SceneName).Replace(".bytes", ".png");
            File.WriteAllBytes(path, texture.EncodeToPNG());

            AssetDatabase.Refresh();

            var importer = TextureImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Sprite;
                importer.alphaIsTransparency = true;
                importer.mipmapEnabled = false;
                importer.SaveAndReimport();
            }
            
        }
        
        private static void Save(SDFPathConfig sdfPathConfig, float realMapSize, List<Vector2> startPoints, List<ChessMapLogicEvent> eventLogicList)
        {
            if (!string.IsNullOrEmpty(sdfPathConfig.SceneName))
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    using (BinaryWriter writer = new BinaryWriter(ms))
                    {
                        //常用的参数写进去 防止以后有改动
                        writer.Write(SDFPathCore.MAP_SIZE);
                        writer.Write(Fix64.FromSingle(realMapSize).rawValue);

                        for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
                        {
                            for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                            {
                                var point = sdfPathConfig.m_points[x, y];
                                writer.Write(point.rawValue);
                            }
                        }

                        for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
                        {
                            for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                            {
                                var height = sdfPathConfig.m_heights[x, y];
                                writer.Write(height.rawValue);
                            }
                        }

                        writer.Write((byte)sdfPathConfig.m_triggers.Count);
                        if (sdfPathConfig.m_triggers.Count > 0)
                        {
                            for (int i = 0; i < sdfPathConfig.m_triggers.Count; ++i)
                            {
                                writer.Write(sdfPathConfig.m_triggers[i]);
                            }

                            for (int x = 0; x < SDFPathCore.MAP_SIZE; ++x)
                            {
                                for (int y = 0; y < SDFPathCore.MAP_SIZE; ++y)
                                {
                                    var point = sdfPathConfig.m_triggerPoints[x, y];
                                    writer.Write(point);
                                }
                            }
                        }

                        writer.Write((byte)startPoints.Count);
                        if (startPoints.Count > 0)
                        {
                            for (int i = 0; i < startPoints.Count; i++)
                            {
                                writer.Write(startPoints[i].x);
                                writer.Write(startPoints[i].y);
                            }
                        }

                        if (eventLogicList != null)
                        {
                            writer.Write((byte)eventLogicList.Count);
                            for (int i = 0; i < eventLogicList.Count; ++i)
                            {
                                eventLogicList[i].WriteTo(writer);
                            }
                        }
                        else
                        {
                            writer.Write((byte)0);
                        }

                        byte[] data = ms.ToArray();
                        //MemoryStream outFile = GZipTools.Compress(data);// 压缩源字符串
                        File.WriteAllBytes(SDFCoreTool.GetPathName(sdfPathConfig.SceneName), data);
                    }
                }
            }
            else
            {
                UnityEngine.Debug.LogError("[SDFPathConfig.Save] path is null!!");
            }
        }
    }
}


