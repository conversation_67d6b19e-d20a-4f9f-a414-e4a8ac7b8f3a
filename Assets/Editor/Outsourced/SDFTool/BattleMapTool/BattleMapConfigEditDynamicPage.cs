using GfxFramework;
using UnityEditor;
using UnityEngine;
using ZGameChess;
using SDFCollider;
using SDFTool;
using System.IO;
using System;
using System.Collections.Generic;

/// <summary>
/// 棋盘配置编辑器——动态物体页面
/// </summary>
public class BattleMapConfigEditDynamicPage : BattleMapConfigEditBasePage
{
    public BattleMapConfigEditDynamicPage(BattleMapConfigEditor parentEditor,
     BattleMapConfig target,
     string title,
     bool initOpen,
     bool isTopBar)
    : base(parentEditor, target, title, initOpen, isTopBar) { }

    private string m_colliderFileName;
    private string m_bigcolliderFileName;
    private string m_colliderFilePath;
    private string m_bigcolliderFilePath;
    private string m_colliderFileConfigPath;
    private string m_bigcolliderFileConfigPath;

    private bool m_isCollider = false;
    private ColliderType m_colliderType = ColliderType.Circle;
    private ColliderWrapper m_collider = null;

    private List<string> m_nodeNameList;
    //序列化对象
    protected SerializedObject m_serializedObject;
    //序列化属性
    protected SerializedProperty m_onlyHomeShowGos;
    protected SerializedProperty m_onlyNotHomeShowGos;

    public override void OnInit()
    {
        base.OnInit();

        m_nodeNameList = new List<string>();
        m_nodeNameList.Add("动态道具");
        m_nodeNameList.Add("动态障碍物");
        m_nodeNameList.Add("主客场显隐");
        InitNodeInfos(m_nodeNameList.Count);
    }

    public override void OnTargetDataChanged()
    {
        base.OnTargetDataChanged();

        InitCollider();

        m_serializedObject = new SerializedObject(m_target);
        m_onlyHomeShowGos = m_serializedObject.FindProperty("m_onlyHomeShowGos");
        m_onlyNotHomeShowGos = m_serializedObject.FindProperty("m_onlyNotHomeShowGos");
    }

    public override void OnShow()
    {
        base.OnShow();

        ShowLeftRightLayout();
    }

    private void DrawDynamicCollider()
    {
        GfxEditorUtility.BeginGroup();

        EditorGUI.BeginChangeCheck();
        m_isCollider = EditorGUILayout.Toggle("是否有障碍物", m_isCollider);
        if (EditorGUI.EndChangeCheck())
        {
            if (m_isCollider)
            {
                m_collider = ColliderFactory.GetColliderWrapper(ColliderType.Circle);
                m_colliderType = ColliderType.Circle;
            }
            else
            {
                DeleteCollider();
                EditorUtility.DisplayDialog("提示", "删除障碍物成功", "确定");
            }
        }

        if (m_isCollider)
        {
            OnDrawCollider();

            if (GUILayout.Button("展示障碍物"))
            {
                ShowCollider();
            }

            if (GUILayout.Button("隐藏障碍物"))
            {
                HideCollider();
            }

            if (GUILayout.Button("导出障碍物"))
            {
                SaveCollider();
                EditorUtility.DisplayDialog("提示", "导出障碍物成功", "确定");
            }

            if (GUILayout.Button("导出障碍物（big）"))
            {
                SaveBigCollider();
                EditorUtility.DisplayDialog("提示", "导出障碍物（big）成功", "确定");
            }
        }

        GfxEditorUtility.EndGroup();
    }

    private void DrawDynamicItem()
    {
        GfxEditorUtility.BeginGroup();

        var dynamicObj = m_target.m_DynamicObj;
        for (int i = 0, n = dynamicObj.Count; i < n; ++i)
        {
            if (DrawDynamicObject(i, dynamicObj[i]))
            {
                dynamicObj.RemoveAt(i);
                break;
            }
        }

        if (GUILayout.Button("添加"))
        {
            dynamicObj.Add(new DynamicObjectConifg());
        }

        GfxEditorUtility.EndGroup();
    }


    #region 动态障碍物配置

    private void InitCollider()
    {
        SDFCoreTool.InitDir();

        m_colliderFileName = GetColliderFileName();
        m_bigcolliderFileName = m_colliderFileName + "_big";
        m_colliderFilePath = SDFCoreTool.GetPathName(m_colliderFileName);
        m_bigcolliderFilePath = SDFCoreTool.GetPathName(m_bigcolliderFileName);
        m_colliderFileConfigPath = SDFCoreTool.GetConfigPathName(m_colliderFileName);
        m_bigcolliderFileConfigPath = SDFCoreTool.GetConfigPathName(m_bigcolliderFileName);

        m_isCollider = File.Exists(m_colliderFilePath) && File.Exists(m_colliderFileConfigPath);
        if (m_isCollider)
        {
            m_collider = SDFColliderTool.ReadColliderConfig(m_colliderFileConfigPath);
            m_colliderType = m_collider.ColliderType;
        }
        else
        {
            UnityEngine.SceneManagement.Scene scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
            if (scene.name.EndsWith("tft_battlefield_center"))
            {
                m_isCollider = true;
                m_collider = ColliderFactory.GetColliderWrapper(ColliderType.Circle);
                m_colliderType = ColliderType.Circle;
                SaveCollider();
            }

        }
    }

    private void DeleteCollider()
    {
        m_collider = null;
        m_target.m_drawGizmos = null;

        if (File.Exists(m_colliderFilePath))
        {
            File.Delete(m_colliderFilePath);
        }

        if (File.Exists(m_colliderFileConfigPath))
        {
            File.Delete(m_colliderFileConfigPath);
        }

        AssetDatabase.Refresh();
    }

    private void SaveCollider()
    {
        BattleMapManagerTool.OutputCollider(m_colliderFileName, m_collider, m_target.MapArea);
        SDFColliderTool.WriteColliderConfig(m_colliderFileConfigPath, m_collider);
    }

    private void SaveBigCollider()
    {
        BattleMapManagerTool.OutputCollider(m_bigcolliderFileName, m_collider, m_target.MapArea);
        SDFColliderTool.WriteColliderConfig(m_bigcolliderFileConfigPath, m_collider);
    }

    private static string GetColliderFileName()
    {
        UnityEngine.SceneManagement.Scene scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
        return SDFColliderTool.GenColliderName(scene.name);
    }

    private void OnDrawCollider()
    {
        EditorGUI.BeginChangeCheck();
        m_colliderType = (ColliderType)EditorGUILayout.EnumPopup("碰撞类型", m_colliderType);
        if (EditorGUI.EndChangeCheck())
        {
            m_collider = ColliderFactory.GetColliderWrapper(m_colliderType);
        }

        m_collider.DrawConfig();
    }

    private void ShowCollider()
    {
        if (m_collider != null)
        {
            float realMapSize = Math.Max(Math.Max(SDFPathCore.REAL_MAP_SIZE, m_target.MapArea.width), m_target.MapArea.height);     // 选最大的
            m_collider.SetConfig(realMapSize.ToFix64(), SDFPathCore.MAP_SIZE);
            m_collider.Calculate();
            m_target.m_drawGizmos = m_collider.Collider;
        }
    }

    private void HideCollider()
    {
        m_target.m_drawGizmos = null;
    }

    #endregion

    private bool DrawDynamicObject(int index, DynamicObjectConifg e)
    {
        BeginColor(Color.cyan);
        EditorGUILayout.BeginHorizontal(m_tabStyle);
        EditorGUILayout.LabelField("索引：" + index, m_labelFontStyle);

        BeginColor(Color.red);
        if (GUILayout.Button("删除", GUILayout.Width(50)))
        {
            EndColor();
            EndColor();
            EditorGUILayout.EndHorizontal();
            return true;
        }
        EndColor();
        EndColor();
        EditorGUILayout.EndHorizontal();

        e.slot = (GameObject)EditorGUILayout.ObjectField("道具插槽: ", e.slot, typeof(GameObject), true);
        e.defaultObj = (GameObject)EditorGUILayout.ObjectField("默认道具: ", e.defaultObj, typeof(GameObject), true);
        return false;
    }

    #region 主客场显隐

    private void DrawHomeVisible()
    {
        GfxEditorUtility.BeginGroup();

        BeginColor(Color.cyan);
        EditorGUILayout.BeginHorizontal(m_tabStyle);
        EditorGUILayout.LabelField("只在主场显示的物件：", m_labelFontStyle);
        EditorGUILayout.EndHorizontal();
        EndColor();

        EditorGUI.BeginChangeCheck();
        EditorGUILayout.PropertyField(m_onlyHomeShowGos, true);
        if (EditorGUI.EndChangeCheck())
        {//提交修改
            m_serializedObject.ApplyModifiedProperties();
        }

        EditorGUILayout.Space();

        BeginColor(Color.cyan);
        EditorGUILayout.BeginHorizontal(m_tabStyle);
        EditorGUILayout.LabelField("只在客场显示的物件：", m_labelFontStyle);
        EditorGUILayout.EndHorizontal();
        EndColor();

        EditorGUI.BeginChangeCheck();
        EditorGUILayout.PropertyField(m_onlyNotHomeShowGos, true);
        if (EditorGUI.EndChangeCheck())
        {//提交修改
            m_serializedObject.ApplyModifiedProperties();
        }

        GfxEditorUtility.EndGroup();
    }

    #endregion

    protected override void OnShowLeftRightLayoutItem(int index)
    {
        base.OnShowLeftRightLayoutItem(index);

        switch (index)
        {
            case 0:
                //动态道具
                DrawDynamicItem();
                break;
            case 1:
                //动态障碍物
                DrawDynamicCollider();
                break;
            case 2:
                //主客场显隐
                DrawHomeVisible();
                break;
        }
    }

    protected override string GetLabelNameByIndex(int index)
    {
        return m_nodeNameList[index];
    }
}