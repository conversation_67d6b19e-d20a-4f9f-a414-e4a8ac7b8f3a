using GfxFramework;
using UnityEngine;
using ZGameChess;

/// <summary>
/// 棋盘配置编辑器——区域触发动画页面
/// </summary>
public class BattleMapConfigEditAreaPage : BattleMapConfigEditBasePage
{
    public BattleMapConfigEditAreaPage(BattleMapConfigEditor parentEditor,
     BattleMapConfig target,
     string title,
     bool initOpen,
     bool isTopBar)
    : base(parentEditor, target, title, initOpen, isTopBar) { }

    public override void OnInit()
    {
        base.OnInit();
    }

    public override void OnTargetDataChanged()
    {
        base.OnTargetDataChanged();
        InitNodeInfos(m_target.m_triggerAnimationList.Count);
    }

    public override void OnShow()
    {
        base.OnShow();
        ShowLeftRightLayout();
    }

    protected override void OnShowLeftRightLayoutItem(int index)
    {
        base.OnShowLeftRightLayoutItem(index);

        var triggerList = m_target.m_triggerAnimationList;
        if (triggerList == null || triggerList.Count <= 0)
        {
            return;
        }
        if (DrawBattleMapTriggerAminationConfig(index, triggerList[index]))
        {
            triggerList.RemoveAt(index);
            m_nodeInfoList.RemoveAt(m_nodeInfoList.Count - 1);
            m_selectedIndex = Mathf.Clamp(m_selectedIndex, 0, Mathf.Max(0, m_nodeInfoList.Count - 1));
        }
    }

    protected override string GetLabelNameByIndex(int index)
    {
        string note = "";
        if (m_target.m_triggerAnimationList != null
            && m_target.m_triggerAnimationList.Count > index
            && index >= 0
           )
        {
            note = m_target.m_triggerAnimationList[index].m_notes;
        }

        if (!string.IsNullOrEmpty(note))
        {
            note = "    [ " + note + " ]";
        }

        return "区域:" + index + note;
    }

    protected override void ShowLeftAddition()
    {
        BeginColor(Color.green);
        if (GUILayout.Button("添加", GUILayout.Height(30)))
        {
            m_target.m_triggerAnimationList.Add(new BattleMapTriggerAminationConfig());
            m_nodeInfoList.Add(new NodeInfo());
        }
        EndColor();
    }
}