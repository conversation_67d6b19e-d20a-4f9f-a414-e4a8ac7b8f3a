#if !LOGIC_THREAD && !ENABLE_TYPE_TREE_IGNORE
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEditor.Experimental.SceneManagement;
using TKFrame;
using System;
using System.IO;
using System.Linq;
using System.Text;
using Lucifer.ActCore;
using SVNTools;
using UnityEditor.Animations;
using ZGame;
using ZGameChess;
using ZGameClient;
using SupportHangPointType = CharacterHangPoint.SupportHangPointType;

[CustomEditor(typeof(CharacterHangPoint))]
[CanEditMultipleObjects]
public class CharacterHangPointEditor : Editor
{

    private Dictionary<int, List<string>> viewAnimationsDic = new Dictionary<int, List<string>>();
    private string playAniName = "idle";
    private static Dictionary<string, int> checkRepeatDic =
        new Dictionary<string, int>();

    private static string GetCharacterHangPointDataKey(CharacterHangPointData chpData)
    {
        if (chpData.isTransformDataFromAnimation)
        {
            return chpData.supportHangPointType + "_" + chpData.dependActionlabel;
        }
        else
        {
            return chpData.supportHangPointType.ToString();
        }
    }

    private bool foldDetailMenus = true;

    //private bool _isShowStarLevelData = false;
    //private bool _isShowSystemScaleData = false;
    //private bool _isShowHangPoint = true;
    //private bool _isShowInitEffectData = false;avatarBoneStrList
    //private bool _isShowActionEffect = false;
    private Avatar avatar = null;
    private bool isShowAvatarBone = false;
    private List<string> avatarBoneStrList = new List<string>();
    private string filterBone = string.Empty;
    private static List<string> littleLegendNameList = new List<string>();
    private static int littleLegendNameSelectIndex = 0;

    public TACG_Hero_Client GetTacgHeroClient(string showName)
    {
        var allHeroCfgs = DataBaseManager.Instance.GetACGHeroTable();
        foreach (var kvp in allHeroCfgs)
        {
            if (kvp.Value.sPrefabShowID.Equals(showName))
            {
                int groupId = kvp.Value.iGroup;
                var heroCfg1Star = DataBaseManager.Instance.SearchACGHeroAndStar(groupId, 1);
                return heroCfg1Star;
            }
        }

        return null;
    }

    private void OnEnable()
    {
        if (littleLegendNameList.Count <= 0)
        {
            DirectoryInfo dirInfo = new DirectoryInfo(Application.dataPath+"/Art_TFT_Raw/model_res/LittleLegend");
            DirectoryInfo[] littleLegendNameDicInfoArr = dirInfo.GetDirectories();
            for (int i = 0, len = littleLegendNameDicInfoArr.Length; i < len; i++)
            {
                if (littleLegendNameDicInfoArr[i].Name.StartsWith("t_"))
                {
                    littleLegendNameList.Add(littleLegendNameDicInfoArr[i].Name);   
                }
            }
        }

        DataBaseManager.Instance.Initialize();
        foreach (CharacterHangPoint hangPoint in targets)
        {
            hangPoint.SetLittleLegendCfg(LittleLegendCfg.GetLegendCfg(hangPoint.name, hangPoint.gameObject.tag));
            if (!string.IsNullOrEmpty(hangPoint.actionEventName)
                && (hangPoint.GetActionEventCfg() == null || hangPoint.GetActionEventCfg().name != hangPoint.actionEventName))
            {
                hangPoint.SetActionEventCfg(AssetDatabase.LoadAssetAtPath<ChessPlayerEffectEventCfg>(
                    "Assets/Art_TFT_Raw/cfg/team_leader_effect_cfg/" + hangPoint.actionEventName + ".asset"));
                EditorUtility.SetDirty(hangPoint);
            }

            if (!string.IsNullOrEmpty(hangPoint.actionMaterialName)
                && (hangPoint.GetMaterialCurveCfg() == null ||
                    hangPoint.GetMaterialCurveCfg().name != hangPoint.actionMaterialName))
            {
                hangPoint.SetMaterialCurveCfg(AssetDatabase.LoadAssetAtPath<ChessPlayerMaterialCurveCfg>(
                    "Assets/Art_TFT_Raw/cfg/team_leader_material_cfg/" + hangPoint.actionMaterialName + ".asset"));
                EditorUtility.SetDirty(hangPoint);
            }
        }

        if (targets.Length == 1)
        {
            CharacterHangPoint hangPoint = targets[0] as CharacterHangPoint;
            CharacterHangPointEditorController.RefreshExtractBoneListEditor(hangPoint);
        }
    }
    
    public GameObject InstantiateShowByShowGo(Transform trans)
    {
        string showGoName = trans.name.Replace("(Clone)", "");
        string[] fileInfoPathArr = Directory.GetFiles(ConstVar.saveShowPrefabPath, "*.prefab",
            SearchOption.AllDirectories);
        for (int i = 0, len = fileInfoPathArr.Length; i < len; i++)
        {
            FileInfo fileInfo = new FileInfo(fileInfoPathArr[i]);
            string fileInfoNameNoExtension = fileInfo.Name.Replace(".prefab", "");
            if (fileInfoNameNoExtension.Equals(showGoName))
            {
                UnityEngine.Object tmpObj =
                    AssetDatabase.LoadAssetAtPath(fileInfoPathArr[i], typeof(UnityEngine.GameObject));
                return PrefabUtility.InstantiatePrefab(tmpObj) as GameObject;
            }
        }

        return null;
    }

    public static IEnumerator ReSampleLogicAnimationHangpoint(CharacterHangPoint hangPoint)
    {
        UnityEngine.Object parentObject = EditorUtility.GetPrefabParent(hangPoint.gameObject);
        string curShowPath = AssetDatabase.GetAssetPath(parentObject);
        //兼容皮肤;
        string convertPureShowPath = curShowPath;
        for (int i = 1; i <= 9; i++)
        {
            convertPureShowPath = convertPureShowPath.Replace("_lv0" + i, "");
        }
        string pureName = convertPureShowPath.Substring(0, convertPureShowPath.LastIndexOf("."));
        pureName = pureName.Replace("_1_show", "");
        string heroPrefabPath = pureName + "_prefab.prefab";
        heroPrefabPath = heroPrefabPath.Replace("hero_show/", "hero_prefab/");
        UnityEngine.Object heroPrefabObj =
            AssetDatabase.LoadAssetAtPath(heroPrefabPath, typeof(UnityEngine.GameObject));
        GameObject heroPrefabInstance = PrefabUtility.InstantiatePrefab(heroPrefabObj) as GameObject;
        CharacterConfig cc = heroPrefabInstance.GetComponent<CharacterConfig>();
        ActionDataConfig actionDataConfig = cc.actionDataConfig;
        GameObject instantiateShow =
            PrefabUtility.InstantiatePrefab(AssetDatabase.LoadAssetAtPath<GameObject>(curShowPath)) as GameObject;
        yield return EditorCoroutineRunner.StartEditorCoroutine(CharacterHangPointBatchEditor.SampleLogicAnimationHangpoint(
            cc,
            actionDataConfig,
            0,
            instantiateShow,
            true,
            true
        ));  
        PrefabUtility.ApplyPrefabInstance(instantiateShow, InteractionMode.UserAction);
        GameObject.DestroyImmediate(instantiateShow);
        GameObject.DestroyImmediate(heroPrefabInstance);
    }

    public override void OnInspectorGUI()
    {
        if (targets.Length == 1)
        {
            CharacterHangPoint hangPoint = (CharacterHangPoint) targets[0];
            // 编辑和保存数据
            GUILayout.Label("(全局)类型相关修改");
            GUILayout.BeginVertical("box");
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("(全局)增改挂点类型"))
            {
                CharacterHangPointTypeEditor.Open();
            }
            if (GUILayout.Button("(全局)刷新挂点的右键菜单"))
            {
                CharacterHangPointEditorController.AutoCreateHangPointMenuItems();
            }

            if (GUILayout.Button("(全局)SVN提交类型修改"))
            {
                SVNTool.CommitAtPaths(new List<string>(){CharacterHangPointEditorController.CharacterHangPointTypeFullPath});
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.LabelField("小小英雄相关");
            EditorGUILayout.BeginHorizontal();
            littleLegendNameSelectIndex =
                EditorGUILayout.Popup("小小英雄列表", littleLegendNameSelectIndex, littleLegendNameList.ToArray());
            if (GUILayout.Button("SVN提交"))
            {
                //获取里面的模型;
                string[] prefabPathArr = Directory.GetFiles("Assets/Art_TFT_Raw/model_res/LittleLegend/" +
                                                               littleLegendNameList[littleLegendNameSelectIndex], "*.prefab", SearchOption.AllDirectories);
                List<string> svnCmitPathList = new List<string>();
                //将当前的show也加进来提交;
                string showName = hangPoint.gameObject.name.Split(' ')[0];
                int findLastIndex = showName.LastIndexOf('(');
                if (findLastIndex > 0)
                {
                    showName = showName.Substring(0, findLastIndex);                    
                }
                string showPath = Application.dataPath.Replace("Assets", "") +
                                  ConstVar.saveMiniHeroShowPrefabPath + "/" + showName;
                string showPath_h = Application.dataPath.Replace("Assets", "") +
                                    ConstVar.saveMiniHeroHightShowPrefabPath + "/" + showName;
                CheckAddToCommitFile(showPath+".prefab", svnCmitPathList);
                CheckAddToCommitFile(showPath+".prefab.meta", svnCmitPathList);
                for (int i = 0, len = prefabPathArr.Length; i < len; i++)
                {
                    if(prefabPathArr[i].EndsWith(".meta", StringComparison.OrdinalIgnoreCase))continue;
                    string[] dependPathArr = AssetDatabase.GetDependencies(prefabPathArr[i], true);
                    for (int k = 0, klen = dependPathArr.Length; k < klen; k++)
                    {
                        if (dependPathArr[k].EndsWith(".prefab"))
                        {
                            svnCmitPathList.Add(dependPathArr[k]);
                            svnCmitPathList.Add(dependPathArr[k]+".meta");
                        }
                    }
                }
                SVNTool.CommitAtPaths(svnCmitPathList);
            }
            EditorGUILayout.EndHorizontal();
            GUILayout.EndVertical();
            GUILayout.Label("快捷操作");
            GUILayout.BeginHorizontal("box");
            if (GUILayout.Button("重新生成逻辑挂点"))
            {
                EditorCoroutineRunner.StartEditorCoroutine(ReSampleLogicAnimationHangpoint(hangPoint));
            }
            if (GUILayout.Button("保存挂点偏移值"))
            {
                RuntimeSaveChpTransform(hangPoint, false);
            }
            if (GUILayout.Button("保存当前位置到PosOffset"))
            {
                RuntimeSaveChpTransform(hangPoint, true);
            }

            if (GUILayout.Button("修复lod位置旋转信息"))
            {
                CharacterHangPointEditorController.ResetLodTransformInfo(hangPoint.gameObject);
            }

            Color bakColor = GUI.color;
            GUI.color = Color.red;
            if (GUILayout.Button("重置静态挂点"))
            {
                //判断下是否是小小英雄，是的话，这里判断下骨骼的创建;
                if (hangPoint.name.StartsWith("t_"))
                {
                    Animator animator = hangPoint.gameObject.GetComponent<Animator>();
                    Avatar avatar = animator.avatar;
                    CharacterHangPointEditorController.CheckExtractAvatarBoneAndSetHangPoints(avatar, hangPoint);
                    EditorUtility.SetDirty(hangPoint);
                    return;
                }
                CharacterHangPointEditor.AdditiveCreateBones(hangPoint, false);
                EditorUtility.SetDirty(hangPoint);
            }
            GUI.color = bakColor;

            GUILayout.EndHorizontal();
            GUILayout.BeginVertical("box");
            // GUILayout.BeginHorizontal("box");
            // if (GUILayout.Button("展开所有骨骼数据"))
            // { 
            //     CharacterHangPointEditorController.UnFoldAllBonesByShow(hangPoint.gameObject);   
            // }
            // if (GUILayout.Button("关闭所有骨骼数据"))
            // {
            //     if (hangPoint != null && !hangPoint.isFoldingBone)
            //     {
            //         CharacterHangPointEditorController.FoldAllBonesByShow(hangPoint.gameObject, false, true);
            //     }
            // }
            // if (GUILayout.Button("只保留挂点数据"))
            // {
            //     CharacterHangPointEditorController.FoldAllBonesByShow(hangPoint.gameObject);
            // }
            // GUILayout.EndHorizontal();
            if (!GUI.enabled)
            {
                GUI.enabled = true;
                EditorGUILayout.HelpBox("要先配置美术资源路径才能使用: Hierarchy视图里，右键->添加挂点->挂点路径配置", MessageType.Warning);
            }
            foldDetailMenus = EditorGUILayout.Foldout(foldDetailMenus, "详细菜单功能");
            if (!foldDetailMenus)
            {
                GUILayout.BeginHorizontal("box");
                if (GUILayout.Button("默认数据", GUILayout.Width(100)))
                {
                    CreateDefaultBones(hangPoint);
                }


                if (GUILayout.Button("重置数据", GUILayout.Width(100)))
                {
                    ResetAllBones(hangPoint);
                }


                GUILayout.EndHorizontal();
                GUILayout.BeginHorizontal("box");
                if (GUILayout.Button("展开数据", GUILayout.Width(100)))
                {
                    ShowBones(hangPoint);
                }

                if (GUILayout.Button("收起挂点展示", GUILayout.Width(100)))
                {
                    //检查下数据是否有重复的先;
                    List<CharacterHangPoint.SupportHangPointType> normalPTList =
                        new List<CharacterHangPoint.SupportHangPointType>();
                    Dictionary<CharacterHangPoint.SupportHangPointType, List<string>>
                        aniPTDic =
                            new Dictionary<CharacterHangPoint.SupportHangPointType,
                                List<string>>();
                    for (int i = 0, len = hangPoint.points.Count; i < len; i++)
                    {
                        if (hangPoint.points[i].isTransformDataFromAnimation)
                        {
                            List<string> aniNameList = null;
                            if (aniPTDic.ContainsKey(hangPoint.points[i].supportHangPointType))
                            {
                                aniNameList = aniPTDic[hangPoint.points[i].supportHangPointType];
                                if (aniNameList.Contains(hangPoint.points[i].dependAnimationName))
                                {
                                    Diagnostic.Error("type类型重复了, 请检查: " +
                                                     Enum.GetName(typeof(CharacterHangPoint.SupportHangPointType),
                                                         hangPoint.points[i].supportHangPointType));
                                    return;
                                }
                            }
                            else
                            {
                                aniPTDic.Add(hangPoint.points[i].supportHangPointType, new List<string>());
                                aniNameList = aniPTDic[hangPoint.points[i].supportHangPointType];
                            }

                            aniNameList.Add(hangPoint.points[i].dependAnimationName);
                        }
                        else
                        {
                            if (normalPTList.Contains(hangPoint.points[i].supportHangPointType))
                            {
                                Diagnostic.Error("type类型重复了, 请检查: " + Enum.GetName(
                                    typeof(CharacterHangPoint.SupportHangPointType),
                                    hangPoint.points[i].supportHangPointType));
                                return;
                            }

                            normalPTList.Add(hangPoint.points[i].supportHangPointType);
                        }
                    }

                    UnFold(hangPoint);
                    //SaveData(hangPoint);
                    ClearCreatedPoints(hangPoint);
                    return;
                }

                //if (GUILayout.Button("收起数据", GUILayout.Width(100)))
                //{
                //    hangPoint.UnFold();
                //}


                GUILayout.EndHorizontal();

                GUILayout.BeginHorizontal("box");
                if (GUILayout.Button("复制", GUILayout.Width(100)))
                {
                    CopyBones(hangPoint);
                }

                if (GUILayout.Button("黏贴", GUILayout.Width(100)))
                {
                    ParseBones(hangPoint);
                }


                GUILayout.EndHorizontal();
                GUILayout.Space(20);
            }

            // 判断是小小英雄
            if (hangPoint.name.StartsWith("t_"))
            {
                GUILayout.BeginHorizontal("box");
                if (GUILayout.Button("打开 动作光效 - 特效 配置", GUILayout.Width(200)))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<ChessPlayerEffectEventCfg>(
                        "Assets/Art_TFT_Raw/cfg/team_leader_effect_cfg/" + hangPoint.actionEventName + ".asset");
                    if (asset != null)
                    {
                        ChessPlayerAniCfgEditorWindow.Open(asset);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("错误", "找不到配置：" + hangPoint.actionEventName, "确定");
                    }
                }

                if (!hangPoint.name.EndsWith("_h_show") && hangPoint.name.EndsWith("_show"))
                {
                    if (GUILayout.Button("打开小小英雄配置", GUILayout.Width(200)))
                    {
                        string cfgName = TeamleaderGenReportModelData.GenerateCfgName(hangPoint.name);
                        string dstPath = TeamLeaderCfgGenTool.CFG_DIR + "/" + cfgName + ".asset";
                        Selection.activeObject = AssetDatabase.LoadAssetAtPath<TKPlugins.DataGroupAsset>(dstPath);
                    }
                }

                bool hasCfg = LittleLegendCfg.HasCfg(hangPoint);
                if (!hasCfg)
                {
                    if (GUILayout.Button("配置转换", GUILayout.Width(200)))
                    {
                        if (EditorUtility.DisplayDialog("提示", "配置转换不可逆，请问是否确认转换？", "确定", "取消"))
                        {
                            if (LittleLegendCfg.Gen(hangPoint))
                            {
                                hangPoint.ClearAllCfg();

                                PrefabUtility.SaveAsPrefabAssetAndConnect(hangPoint.gameObject, UnityEditor.PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(hangPoint.gameObject), InteractionMode.UserAction);
                            }
                        }
                    }
                }
                else
                {
                    if (GUILayout.Button("选中配置", GUILayout.Width(200)))
                    {
                        LittleLegendCfg.SelectCfg(hangPoint);
                    }
                }

                GUILayout.EndHorizontal();
            }

            //ChessPlayerAniCfgEditorWindow

            //if (GUILayout.Button("新建挂点", GUILayout.Width(100)))
            //{
            //    hangPoint.CreateNewBones();

            //}

            hangPoint.defaultScale = hangPoint.transform.localScale;
            GUI.enabled = false;
            EditorGUILayout.Vector3Field("默认缩放值", hangPoint.defaultScale);

            //if (!string.IsNullOrEmpty(hangPoint.actionEventName))
            //{
            //    var asset = AssetDatabase.LoadAssetAtPath<ChessPlayerEffectEventCfg>("Assets/Art_TFT_Raw/cfg/team_leader_effect_cfg/" + hangPoint.actionEventName + ".asset");
            //    if (asset != null)
            //        EditorGUILayout.ObjectField("动作光效-特效配置", asset, typeof(ChessPlayerEffectEventCfg), false);
            //}

            GUI.enabled = true;
            GUILayout.EndVertical();
        }

        if (targets.Length == 1)
        {
            CharacterHangPoint hangPoint = (CharacterHangPoint) targets[0];
            Animator animator = hangPoint.gameObject.GetComponent<Animator>();
            Avatar curAvatar = null;
            if (animator != null)
            {
                curAvatar = animator.avatar;
            }

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("选择骨骼暴露");
            // Color bakColor = GUI.color;
            // GUI.color = Color.green;
            // if (GUILayout.Button("暴露默认骨骼挂点"))
            // {
            //     CharacterHangPointEditorController.CheckExtractAvatarBoneAndSetHangPoints(curAvatar, hangPoint, ref avatarBoneStrList);
            // }
            // GUI.color = bakColor;
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginVertical("box");
            isShowAvatarBone = EditorGUILayout.Foldout(isShowAvatarBone, "显示骨骼");
            if (isShowAvatarBone)
            {
                if (curAvatar != null && curAvatar != avatar)
                {
                    avatar = curAvatar;
                    CharacterHangPointEditorController.CheckExtractAvatarBone(curAvatar, ref avatarBoneStrList);
                    return;
                }

                EditorGUI.indentLevel++;
                EditorGUILayout.BeginHorizontal("box");
                playAniName = EditorGUILayout.TextField(playAniName, GUILayout.Width(80.0f));
                if (GUILayout.Button("播放"+playAniName+"第一帧"))
                {
                    Animator curAni = hangPoint.gameObject.GetComponent<Animator>();
                    curAni.enabled = false;
                    curAni.enabled = true;
                    bool hasIdle = false;
                    if (curAni.runtimeAnimatorController is AnimatorOverrideController)
                    {
                        curAni.runtimeAnimatorController =
                            curAni.runtimeAnimatorController as AnimatorOverrideController;
                        hasIdle = true;
                    }
                    else
                    {
                        AnimatorController ac = curAni.runtimeAnimatorController as AnimatorController;
                        var ssArr = ac.layers[0].stateMachine.states;
                        for (int i = 0, len = ssArr.Length; i < len; i++)
                        {
                            if (ssArr[i].state.name.Equals(playAniName))
                            {
                                hasIdle = true;
                                break;
                            }
                        }
                    }

                    curAni.Play(hasIdle ? playAniName:playAniName+"_UI", 0, 0);
                    curAni.Update(0);
                    curAni.enabled = false;
                    curAni.enabled = true;
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("重新暴露骨骼"))
                {
                    CharacterHangPointEditorController.ReExtractBones(hangPoint);
                }

                if (GUILayout.Button("复制当前挂点信息到其他预制体中"))
                {
                    UnityEngine.Object correspondingObject =
                        PrefabUtility.GetCorrespondingObjectFromOriginalSource(hangPoint.gameObject);
                    string assetPath = AssetDatabase.GetAssetPath(correspondingObject);
                    CharacterHangPointCopyToPanel.Open(assetPath, hangPoint);
                }

                EditorGUILayout.EndHorizontal();
                this.filterBone = EditorGUILayout.TextField("搜索骨骼名:", this.filterBone);
                for (int i = 0, len = avatarBoneStrList.Count; i < len; i++)
                {
                    bool preIsSelectCurItem = hangPoint.extractBoneList_editor.IndexOf(avatarBoneStrList[i]) >= 0;
                    bool newIsSelectCurItem = preIsSelectCurItem;
                    if (string.IsNullOrEmpty(this.filterBone) ||
                        avatarBoneStrList[i].ToLower().Contains(this.filterBone.ToLower()))
                    {
                        EditorGUILayout.BeginHorizontal("box");
                        newIsSelectCurItem = EditorGUILayout.ToggleLeft(avatarBoneStrList[i], newIsSelectCurItem);
                        if (newIsSelectCurItem != preIsSelectCurItem)
                        {
                            if (newIsSelectCurItem)
                            {
                                hangPoint.extractBoneList_editor.Add(avatarBoneStrList[i]);
                            }
                            else
                            {
                                hangPoint.extractBoneList_editor.Remove(avatarBoneStrList[i]);
                            }
                        }

                        EditorGUILayout.EndHorizontal();
                    }
                }

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.EndVertical();
        }

        foreach (CharacterHangPoint hangPoint in targets)
        {
            int hpInstanceId = hangPoint.GetInstanceID();
            if (!viewAnimationsDic.ContainsKey(hpInstanceId))
            {
                viewAnimationsDic.Add(hpInstanceId, new List<string>());
            }

            checkRepeatDic.Clear();
            EditorGUI.BeginChangeCheck();
            base.OnInspectorGUI();
            //绘制pointPosData;
            hangPoint.pointPosDataFoldOutEditor = EditorGUILayout.Foldout(hangPoint.pointPosDataFoldOutEditor, "挂点信息");
            if (hangPoint.pointPosDataFoldOutEditor)
            {
                EditorGUI.indentLevel++;
                if (GUILayout.Button("Add"))
                {
                    CharacterHangPointData chpData = new CharacterHangPointData();
                    string valueName = CommonUtilGlobal.GetEnumNameAttributeValue(chpData.supportHangPointType);
                    if (!string.IsNullOrEmpty(valueName))
                    {
                        GameObject findGo = ToolKit.FindChildDeep(hangPoint.gameObject, valueName);
                        if (findGo != null)
                        {
                            chpData.bindTrans = findGo.transform;
                        }
                    }

                    hangPoint.pointPosData.Add(chpData);
                    EditorUtility.SetDirty(hangPoint);
                }

                for (int i = 0, len = hangPoint.pointPosData.Count; i < len; i++)
                {
                    CharacterHangPointData chpData = hangPoint.pointPosData[i];
                    string chpDataKey = GetCharacterHangPointDataKey(chpData);
                    if (!checkRepeatDic.ContainsKey(chpDataKey))
                    {
                        checkRepeatDic.Add(chpDataKey, 0);
                    }

                    checkRepeatDic[chpDataKey]++;
                    EditorGUILayout.BeginVertical("box");
                    Color bakColor = GUI.color;
                    if (checkRepeatDic[chpDataKey] > 1)
                    {
                        GUI.color = Color.red;
                        EditorGUILayout.TextField("重复了", GUILayout.MaxWidth(100.0f));
                    }

                    GUI.color = bakColor;
                    EditorGUILayout.BeginHorizontal("box");
                    EditorGUI.BeginChangeCheck();
                    EditorGUILayout.LabelField("序号:" + (i + 1), GUILayout.MaxWidth(60.0f));
                    chpData.supportHangPointType =
                        (CharacterHangPoint.SupportHangPointType) EditorGUILayout.EnumPopup("类型",
                            chpData.supportHangPointType);
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (chpData.bindTrans == null)
                        {
                            string valueName = CommonUtilGlobal.GetEnumNameAttributeValue(chpData.supportHangPointType);
                            if (!string.IsNullOrEmpty(valueName))
                            {
                                GameObject findGo = ToolKit.FindChildDeep(hangPoint.gameObject, valueName);
                                if (findGo != null)
                                {
                                    chpData.bindTrans = findGo.transform;
                                }
                            }
                        }
                    }

                    bakColor = GUI.color;
                    GUI.color = Color.red;
                    if (GUILayout.Button("x"))
                    {
                        hangPoint.pointPosData.RemoveAt(i);
                        i--;
                        len--;
                    }

                    GUI.color = bakColor;
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.BeginHorizontal("box");
                    bakColor = GUI.color;
                    if (chpData.isTransformDataFromAnimation)
                    {
                        GUI.color = Color.green;
                    }

                    chpData.IsIgnoreHeight =
                        EditorGUILayout.ToggleLeft("是否忽略高度", chpData.IsIgnoreHeight, GUILayout.Width(150.0f));
                    chpData.IsRelativeParent = EditorGUILayout.ToggleLeft("是否相对于父节点【特指 ground loc之类】",
                        chpData.IsRelativeParent, GUILayout.Width(150.0f));
                    EditorGUI.BeginChangeCheck();
                    chpData.isTransformDataFromAnimation = EditorGUILayout.ToggleLeft("位置信息通过animation获取",
                        chpData.isTransformDataFromAnimation, GUILayout.Width(150.0f));
                    if (EditorGUI.EndChangeCheck() || viewAnimationsDic[hpInstanceId].Count <= 0)
                    {
                        if (chpData.bindTrans == null)
                        {
                            string valueName = CommonUtilGlobal.GetEnumNameAttributeValue(chpData.supportHangPointType);
                            if (!string.IsNullOrEmpty(valueName))
                            {
                                GameObject findGo = ToolKit.FindChildDeep(hangPoint.gameObject, valueName);
                                if (findGo != null)
                                {
                                   
                                }
                            }
                        }

                        //获取动画名列表;
                        List<string> aniNameList = viewAnimationsDic[hpInstanceId];
                        aniNameList.Clear();
              
                        Animator animator = hangPoint.GetComponent<Animator>();
                        AnimatorOverrideController animatorOverrideController =
                            animator.runtimeAnimatorController as AnimatorOverrideController;
                        AnimatorController animatorController;
                        if (animatorOverrideController != null)
                            animatorController = animatorOverrideController.runtimeAnimatorController as AnimatorController;
                        else
                            animatorController = animator.runtimeAnimatorController as AnimatorController;
                        if (animatorController != null)
                        {
                            for (int layerIdx = 0; layerIdx < animatorController.layers.Length; ++layerIdx)
                            {
                                var layer = animatorController.layers[layerIdx];
                                AnimatorStateMachine sm = layer.stateMachine;
                                ChildAnimatorState[] ams = sm.states;
                                for (int k = 0; k < ams.Length; k++)
                                {
                                    string stateName = ams[k].state.name;
                                    if (!aniNameList.Contains(stateName))
                                    {
                                        aniNameList.Add(stateName);
                                    }
                                }
                            }
                        }                        

                        if (!string.IsNullOrEmpty(chpData.dependAnimationName))
                        {
                            chpData.dependAnimationNameIndex_Editor = aniNameList.IndexOf(chpData.dependAnimationName);
                            if (chpData.dependAnimationNameIndex_Editor < 0)
                            {
                                chpData.dependAnimationNameIndex_Editor = 0;
                                chpData.dependAnimationName = aniNameList[chpData.dependAnimationNameIndex_Editor];
                            }
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                    bool preIsFalse = !chpData.isTransformDataFromAnimation;
                    if (chpData.isTransformDataFromAnimation)
                    {
                        EditorGUI.BeginChangeCheck();
                        if (chpData.dependAnimationNameIndex_Editor < 0)
                        {
                            List<string> aniNameList = viewAnimationsDic[hpInstanceId];
                            chpData.dependAnimationNameIndex_Editor = aniNameList.IndexOf(chpData.dependAnimationName);
                            if (chpData.dependAnimationNameIndex_Editor >= 0)
                            {
                                chpData.dependAnimationName = aniNameList[chpData.dependAnimationNameIndex_Editor];
                            }
                        }

                        EditorGUILayout.BeginHorizontal("box");
                        EditorGUILayout.LabelField("参考actioLabel:"+chpData.dependActionlabel);
                        chpData.dependAnimationNameIndex_Editor = EditorGUILayout.Popup("参考动作:",
                            chpData.dependAnimationNameIndex_Editor, viewAnimationsDic[hpInstanceId].ToArray());
                        bool needForceSample = false;
                        if (EditorGUI.EndChangeCheck() || preIsFalse)
                        {
                            chpData.dependAnimationName =
                                viewAnimationsDic[hpInstanceId][chpData.dependAnimationNameIndex_Editor];
                            AnimationClip[] aniClipArr = AnimationUtility.GetAnimationClips(hangPoint.gameObject);
                            AnimationClip curClip = aniClipArr[chpData.dependAnimationNameIndex_Editor];
                            chpData.depengAnimationFrameRateEditor = curClip.frameRate;
                            AnimationClipSettings previewInfo = AnimationUtility.GetAnimationClipSettings(curClip);
                            float normalizedTime = previewInfo.stopTime - previewInfo.startTime;
                            chpData.depengAnimationDurationS = curClip.length;
                            needForceSample = true;
                        }

                        if (chpData.depengAnimationDurationS <= 0.0f)
                        {
                            AnimationClip[] aniClipArr = AnimationUtility.GetAnimationClips(hangPoint.gameObject);
                            AnimationClip curClip = aniClipArr[chpData.dependAnimationNameIndex_Editor];
                            chpData.depengAnimationDurationS = curClip.length;
                        }

                        EditorGUILayout.EndHorizontal();
                        EditorGUILayout.BeginHorizontal();
                        EditorGUI.BeginChangeCheck();
                        chpData.dependAnimationSampleTimeS = EditorGUILayout.Slider(
                            "采样帧(最大秒:" + chpData.depengAnimationDurationS + ")",
                            chpData.dependAnimationSampleTimeS, 0, chpData.depengAnimationDurationS);
                        if (GUILayout.Button("定位"))
                        {
                            needForceSample = true;
                        }

                        if (needForceSample || EditorGUI.EndChangeCheck())
                        {
                            HangPointSampleAnimator(hangPoint, chpData);
                            EditorUtility.SetDirty(hangPoint);
                        }

                        EditorGUILayout.EndHorizontal();
                    }

                    GUI.color = bakColor;
                    bakColor = GUI.color;
                    if (chpData.bindTrans == null)
                    {
                        GUI.color = Color.red;
                    }

                    EditorGUI.BeginChangeCheck();
                    chpData.bindTrans =
                        (Transform) EditorGUILayout.ObjectField("绑骨点", chpData.bindTrans, typeof(Transform), true);
                    // chpData.bindTrans.RotateAround();
                    if (EditorGUI.EndChangeCheck())
                    {
                        //获取对应的绑骨信息;
                        if (chpData.isTransformDataFromAnimation && chpData.bindTrans != null)
                        {
                            chpData.pos = chpData.bindTrans.position - hangPoint.transform.position;
                        }
                        else
                        {
                            chpData.pos = Vector3.zero;
                            chpData.scale = Vector3.one;
                            chpData.eulerAngles = Vector3.zero;
                        }
                    }

                    GUI.color = bakColor;
                    if (chpData.isTransformDataFromAnimation)
                    {
                        GUI.enabled = false;
                    }

                    chpData.pos = EditorGUILayout.Vector3Field("pos", chpData.pos);
                    chpData.eulerAngles = EditorGUILayout.Vector3Field("eulerAngles", chpData.eulerAngles);
                    chpData.scale = EditorGUILayout.Vector3Field("scale", chpData.scale);
                    // EditorGUILayout.BeginHorizontal("box");
                    if (chpData.isTransformDataFromAnimation)
                    {
                        EditorGUILayout.BeginVertical();
                        EditorGUILayout.LabelField("scale=1时的信息");
                        chpData.pointDataTransAtScale1.pos =
                            EditorGUILayout.Vector3Field("pos", chpData.pointDataTransAtScale1.pos);
                        chpData.pointDataTransAtScale1.scale =
                            EditorGUILayout.Vector3Field("scale", chpData.pointDataTransAtScale1.scale);
                        chpData.pointDataTransAtScale1.eulerAngles =
                            EditorGUILayout.Vector3Field("eulerAngles", chpData.pointDataTransAtScale1.eulerAngles);
                        EditorGUILayout.EndVertical();
                    }

                    // EditorGUILayout.EndHorizontal();
                    GUI.enabled = true;
                    EditorGUILayout.EndVertical();
                }

                EditorGUI.indentLevel--;
            }

            if (EditorGUI.EndChangeCheck())
            {
                ZGameChess.ChessBattleUnitServic battleUnitService =
                    ZGameChess.ChessBattleGlobal.Instance.BattleUnitService;
                if (battleUnitService != null)
                {
                    List<ZGameChess.ChessBattleUnit> battleUnitList = battleUnitService.GetAllBattleUnit();
                    ZGameChess.ChessBattleUnit tmpBattleUnit = null;
                    for (int i = 0, len = battleUnitList.Count; i < len; i++)
                    {
                        tmpBattleUnit = battleUnitList[i];
                        CharacterHangPointData chpd =
                            hangPoint.GetPosData(tmpBattleUnit.socketData.supportHangPointType);
                        if (chpd != null && chpd == tmpBattleUnit.socketData)
                        {
                            tmpBattleUnit.SetHeadSocket(chpd);
                            break;
                        }
                    }
                }

                if (hangPoint.GetLittleLegendCfg() != null)
                {
                    if (hangPoint.TryClearOwnerLittleLegendCfgs())
                    {
                        EditorUtility.DisplayDialog("警告", "检测到已配置同名cfg，请勿在模型上继续配置常驻特效、动作光效-特效配置、材质配置、动作声音！", "懂了");
                    }
                }

                //if (!string.IsNullOrEmpty(hangPoint.actionEventName))
                //{
                //    hangPoint.SetActionEventCfg(AssetDatabase.LoadAssetAtPath<ChessPlayerEffectEventCfg>(
                //        "Assets/Art_TFT_Raw/cfg/team_leader_effect_cfg/" + hangPoint.actionEventName + ".asset"));
                //}

                //if (!string.IsNullOrEmpty(hangPoint.actionMaterialName))
                //{
                //    hangPoint.SetMaterialCurveCfg(AssetDatabase.LoadAssetAtPath<ChessPlayerMaterialCurveCfg>(
                //        "Assets/Art_TFT_Raw/cfg/team_leader_material_cfg/" + hangPoint.actionMaterialName + ".asset"));
                //}

                EditorUtility.SetDirty(hangPoint);
            }
        }

        if (targets.Length == 1)
        {
            GUI.enabled = false;
            CharacterHangPoint hangPoint = (CharacterHangPoint)targets[0];
            if (hangPoint.GetLittleLegendCfg() != null)
            {
                EditorGUILayout.ObjectField("小小英雄配置:", hangPoint.GetLittleLegendCfg(), typeof(LittleLegendCfg), false);
            }
            GUI.enabled = true;
        }

    }

    private static void CheckAddToCommitFile(string realPath, List<string> svnCmitPathList)
    {
        if (File.Exists(realPath))
        {
            svnCmitPathList.Add(realPath);
        }
    }

    private void RuntimeSaveChpTransform(CharacterHangPoint hangPoint, bool isPosOffset)
    {
        if (isPosOffset)
        {
            hangPoint.posOffset = hangPoint.transform.localPosition;   
        }
        GameObject instantiateShowGo = InstantiateShowByShowGo(hangPoint.transform);
        CharacterHangPoint instantiateChp = instantiateShowGo.GetComponent<CharacterHangPoint>();
        if (isPosOffset)
        {
            instantiateChp.posOffset = hangPoint.posOffset;   
        }
        List<CharacterHangPointData> dynacChpDataList = hangPoint.pointPosData;
        List<CharacterHangPointData> instantiateChpDataList = instantiateChp.pointPosData;
        for (int i = 0, len = dynacChpDataList.Count; i < len; i++)
        {
            CharacterHangPointData rawChpData = dynacChpDataList[i];
            if (rawChpData.bindTrans != null)
            {
                CharacterHangPointData targetChpData = instantiateChpDataList[i];
                targetChpData.pos = rawChpData.bindTrans.localPosition;
                targetChpData.eulerAngles = rawChpData.bindTrans.localEulerAngles;
                targetChpData.scale = rawChpData.bindTrans.localScale;   
            }
        }

        CharacterHangPointEditorController.IsNeedApplyListenFoldBone = false;
        PrefabUtility.ApplyPrefabInstance(instantiateShowGo, InteractionMode.UserAction);
        GameObject.DestroyImmediate(instantiateShowGo);
        CharacterHangPointEditorController.IsNeedApplyListenFoldBone = true;
    }


    public static void HangPointSampleAnimator(CharacterHangPoint hangPoint, CharacterHangPointData chpData)
    {
        SampleAnimator1(hangPoint, chpData);
        SampleAnimator2(hangPoint, chpData);
    }

    private static void SampleAnimator1(CharacterHangPoint hangPoint, CharacterHangPointData chpData)
    {
        Animator animator = hangPoint.GetComponent<Animator>();

        #region 寻找clip所在layer;

        AnimatorOverrideController animatorOverrideController =
            animator.runtimeAnimatorController as AnimatorOverrideController;
        AnimatorController animatorController;
        if (animatorOverrideController != null)
            animatorController = animatorOverrideController.runtimeAnimatorController as AnimatorController;
        else
            animatorController = animator.runtimeAnimatorController as AnimatorController;
        AnimationClip clip = null;
        int animationLayer = 0;
        if (animatorController != null)
        {
            for (int layerIdx = 0; layerIdx < animatorController.layers.Length; ++layerIdx)
            {
                var layer = animatorController.layers[layerIdx];
                AnimatorStateMachine sm = layer.stateMachine;
                ChildAnimatorState[] ams = sm.states;
                for (int k = 0; k < ams.Length; k++)
                {
                    if (ams[k].state.name == chpData.dependAnimationName)
                    {
                        clip = ams[k].state.motion as AnimationClip;
                        animationLayer = layerIdx;
                        break;
                    }
                }
            }
        }

        #endregion

        for (int k = 0; k < animator.layerCount; ++k)
            animator.SetLayerWeight(k, k == animationLayer ? 1.0f : 0.0f);
        animator.enabled = false;
        animator.enabled = true;
        animator.Play(chpData.dependAnimationName, animationLayer,
            chpData.dependAnimationSampleTimeS / chpData.depengAnimationDurationS);
        animator.Update(0);
        animator.enabled = false;
        animator.enabled = true;
    }

    private static void SampleAnimator2(CharacterHangPoint hangPoint, CharacterHangPointData chpData)
    {
        //获取对应的绑骨信息;
        if (chpData.bindTrans != null)
        {
            Vector3 preWorldPos = hangPoint.transform.position;
            hangPoint.transform.position = Vector3.zero;
            // Transform ground_loc = hangPoint.transform.Find("ground_loc");
            chpData.pos = chpData.bindTrans.position - hangPoint.transform.position;
            chpData.eulerAngles = chpData.bindTrans.eulerAngles;
            Vector3 curScale = hangPoint.transform.localScale;
            //设置scale=1时的信息;
            chpData.pointDataTransAtScale1.pos = chpData.pos / curScale.x;
            hangPoint.transform.position = preWorldPos;
            Debug.Log("记录下 " + chpData.dependAnimationName + " , " + chpData.supportHangPointType + " , " +
                      chpData.pos + "  " + hangPoint.GetInstanceID());
        }
    }

    public void SaveData(CharacterHangPoint hangPoint)
    {
        if (hangPoint == null)
            return;
//         GameObject prefab = PrefabUtility.GetCorrespondingObjectFromSource(hangPoint.gameObject) as GameObject;
//         if (prefab == null)
//         {
//             Diagnostic.Error("请先创建你对应的prefab");
//             return;
//         }
        PrefabStage ps = PrefabStageUtility.GetCurrentPrefabStage();
        if (ps == null)
        {
            Diagnostic.Error("请在prefab Mode下编辑");
            return;
        }

        //string filePath = ps.prefabAssetPath;
        //UnityEngine.Object prefab = PrefabUtility.CreateEmptyPrefab(filePath);
        GameObject
            prefab = ps.prefabContentsRoot; // PrefabUtility.GetCorrespondingObjectFromSource<GameObject>(hangPoint.gameObject);
// 
//         if (prefab == null)
//         {
//             Diagnostic.Error("请先创建你对应的prefab");
//             return;
//         }

        CharacterHangPoint data = prefab.GetComponent<CharacterHangPoint>();
        if (data == null)
        {
            data = prefab.AddComponent<CharacterHangPoint>();
            data.pointPosData = new List<CharacterHangPointData>(hangPoint.pointPosData);
            ClearCreatedPoints(data);
        }
        else
        {
            List<CharacterHangPointData> test = new List<CharacterHangPointData>();
            for (int i = 0; i < hangPoint.pointPosData.Count; i++)
            {
                if (hangPoint.pointPosData[i].bindTrans != null &&
                    hangPoint.pointPosData[i].bindTrans != hangPoint.gameObject.transform)
                {
                    GameObject go = ToolKit.FindChildDeep(prefab, hangPoint.pointPosData[i].bindTrans.name);
                    test.Add(new CharacterHangPointData(go.transform, hangPoint.pointPosData[i].pos,
                        hangPoint.pointPosData[i].scale, hangPoint.pointPosData[i].eulerAngles,
                        hangPoint.pointPosData[i].supportHangPointType));
                }
                else
                {
                    test.Add(new CharacterHangPointData(hangPoint.pointPosData[i]));
                }
            }

            data.pointPosData = test;
            ClearCreatedPoints(data);
        }

        EditorSceneManager.MarkSceneDirty(ps.scene);

        //		data.SaveScars (hangPoint.scars);
        //		prefab = hangPoint.gameObject;
        //		data.CloneScars ();

        //		string fileName = "Assets/ExternalResources/ExternalResources/Character/Avatar/"+ CurrThemeDir + "/prefab/" + formatList[i].name + ".prefab";
        //		if (File.Exists(FullProjectPath + "/" + fileName))
        //		{
        //			AssetDatabase.DeleteAsset(fileName);
        //		}

        //		Object prefab = PrefabUtility.CreateEmptyPrefab(fileName);
        EditorUtility.SetDirty(prefab);

        //PrefabUtility.ReplacePrefab(hangPoint.gameObject,PrefabUtility.GetCorrespondingObjectFromSource(hangPoint.gameObject),ReplacePrefabOptions.ConnectToPrefab);
        //		AssetDatabase.get

        //		AssetDatabase.SaveAssets();
        //		EditorUtility.sav
        AssetDatabase.SaveAssets();
    }

    /// <summary>
    /// 显示骨骼点
    /// </summary>
    public static void ShowBones(CharacterHangPoint chp)
    {
        if (chp.pointPosData.Count == 0)
            CreateDefaultBones(chp);

        // 已经有数据
        if (chp.points.Count != 0)
            return;

        for (int i = 0; i < chp.pointPosData.Count; i++)
        {
            CharacterHangPoint.GameObjectInfo goInfo = new CharacterHangPoint.GameObjectInfo();
            //先找挂点;
            string boneName = CommonUtilGlobal.GetEnumNameAttributeValue(chp.pointPosData[i].supportHangPointType);
            Transform bindTrans = chp.transform.Find(boneName);
            Transform hangTrans = new GameObject().transform;
            hangTrans.name = "挂点" + (i + 1);
            goInfo.isManualCreate = true;
            hangTrans.transform.parent = chp.pointPosData[i].bindTrans;
            hangTrans.transform.localEulerAngles = chp.pointPosData[i].eulerAngles;
            hangTrans.transform.localScale = chp.pointPosData[i].scale;
            hangTrans.transform.localPosition = chp.pointPosData[i].pos;
            goInfo.gameObject = hangTrans.gameObject;
            goInfo.pointDataTransAtScale1 = chp.pointPosData[i].pointDataTransAtScale1;
            goInfo.isTransformDataFromAnimation = chp.pointPosData[i].isTransformDataFromAnimation;
            goInfo.supportHangPointType = chp.pointPosData[i].supportHangPointType;
            goInfo.dependAnimationName = chp.pointPosData[i].dependAnimationName;
            goInfo.dependAnimationNameIndex_Editor = chp.pointPosData[i].dependAnimationNameIndex_Editor;
            goInfo.dependAnimationSampleTimeS = chp.pointPosData[i].dependAnimationSampleTimeS;
            goInfo.depengAnimationDurationS = chp.pointPosData[i].depengAnimationDurationS;
            goInfo.depengAnimationFrameRateEditor = chp.pointPosData[i].depengAnimationFrameRateEditor;
            goInfo.dependActionLabel = chp.pointPosData[i].dependActionlabel;
            chp.points.Add(goInfo);
        }
    }

    public static void ResetAllBones(CharacterHangPoint chp)
    {
        chp.pointPosData.Clear();
        ClearCreatedPoints(chp);
        ShowBones(chp);
    }

    /// <summary>
    /// 创建默认数据
    /// </summary>
    public static void CreateDefaultBones(CharacterHangPoint chp)
    {
        chp.pointPosData.Clear();
        ClearCreatedPoints(chp);
        Array enumValueArr = Enum.GetValues(typeof(SupportHangPointType));
        SupportHangPointType tmpPT;
        for (int i = 0, len = enumValueArr.Length; i < len; i++)
        {
            tmpPT = (SupportHangPointType) enumValueArr.GetValue(i);
            string boneName = CommonUtilGlobal.GetEnumNameAttributeValue(tmpPT);
            GameObject bone = ToolKit.FindChildDeep(chp.gameObject, boneName);
            if (bone != null)
            {
                chp.pointPosData.Add(new CharacterHangPointData(bone.transform,
                    Vector3.zero, Vector3.one, Vector3.zero, tmpPT));
            }
        }
    }

    /// <summary>
    /// 增量添加骨骼挂点;
    /// </summary>
    public static bool AdditiveCreateBones(CharacterHangPoint chp, bool needLog = false)
    {
        bool hasLoadPrefabContent = false;
        string chpAssetPath = null;
        if (AssetDatabase.Contains(chp.gameObject))
        {
            chpAssetPath = AssetDatabase.GetAssetPath(chp.gameObject);
            chp = PrefabUtility.LoadPrefabContents(chpAssetPath).GetComponent<CharacterHangPoint>();
            hasLoadPrefabContent = true;
        }

        bool hasAdditiveData = false;
        StringBuilder logSb = new StringBuilder();
        Array enumValueArr = Enum.GetValues(typeof(SupportHangPointType));
        SupportHangPointType tmpPT;
        for (int i = 0, len = enumValueArr.Length; i < len; i++)
        {
            tmpPT = (SupportHangPointType) enumValueArr.GetValue(i);
            CharacterHangPointData data = chp.__GetReallyPosData(tmpPT, isUseSecond:false);
            HangPointAttribute hpAttr = CommonUtilGlobal.GetEnumAttributes<HangPointAttribute>(tmpPT)[0];
            string[] boneNameArr = hpAttr.GetBoneNameArr();
            if (data == null)
            {
                for (int k = 0, klen = boneNameArr.Length; k < klen; k++)
                {
                    GameObject bone = ToolKit.FindChildDeep(chp.gameObject, boneNameArr[k]);
                    if (bone != null)
                    {
                        hasAdditiveData = true;
                        logSb.Append(CommonUtilGlobal.GetEnumNameAttributeValue(tmpPT));
                        logSb.Append(", ");
                        Transform locTrans = bone.transform;
                        if (!hpAttr.Value.Equals(bone.transform.name))
                        {
                               //获取当前bone下面有没有挂点;
                               locTrans = bone.transform.Find(hpAttr.Value);
                               if (locTrans == null)
                               {
                                   locTrans = new GameObject(hpAttr.Value).transform;
                                   locTrans.SetParent(bone.transform);
                                   locTrans.localPosition = Vector3.zero;
                                   locTrans.localEulerAngles = Vector3.zero;
                                   locTrans.localScale = Vector3.one;
                               }
                        }
                        CharacterHangPointData chpData = new CharacterHangPointData(locTrans.transform,
                            Vector3.zero, Vector3.one, Vector3.zero, tmpPT);
                        HangPointAttribute hangPointAttr =
                            CommonUtilGlobal.GetEnumNameArrAttributeValue(chpData.supportHangPointType)[0] as HangPointAttribute;
                        chpData.IsIgnoreHeight = hangPointAttr.IsIgnoreHeight;
                        chp.pointPosData.Add(chpData);
                        break;
                    }                    
                }
            }
            else
            {
                if (data.bindTrans == null)
                {
                    bool hasFinded = false;
                    for (int k = 0, klen = boneNameArr.Length; k < klen; k++)
                    {
                        GameObject bone = ToolKit.FindChildDeep(chp.gameObject, boneNameArr[k]);
                        if (bone != null)
                        {
                            Transform locTrans = bone.transform;
                            if (!hpAttr.Value.Equals(bone.transform.name))
                            {
                                //获取当前bone下面有没有挂点;
                                locTrans = bone.transform.Find(hpAttr.Value);
                                if (locTrans == null)
                                {
                                    locTrans = new GameObject(hpAttr.Value).transform;
                                    locTrans.SetParent(bone.transform);
                                    locTrans.localPosition = Vector3.zero;
                                    locTrans.localEulerAngles = Vector3.zero;
                                    locTrans.localScale = Vector3.one;
                                }
                            }
                            data.bindTrans = locTrans;
                            hasFinded = true;
                            break;
                        }
                    }

                    if (!hasFinded)
                    {
                        chp.__RemovePosData(tmpPT);
                    }
                }
                else
                {
                    GameObject locTrans = ToolKit.FindChildDeep(chp.gameObject, data.bindTrans.name);
                    if (locTrans != null)
                    {
                        data.bindTrans = locTrans.transform;   
                    }
                    else
                    {
                        chp.__RemovePosData(data);
                    }
                }
            }
        }

        if (needLog)
        {
            if (hasAdditiveData)
            {
                Debug.Log("增量添加了: " + logSb);
            }
        }

        if (hasLoadPrefabContent)
        {
            PrefabUtility.SaveAsPrefabAsset(chp.gameObject, chpAssetPath);
            PrefabUtility.UnloadPrefabContents(chp.gameObject);
        }

        return hasAdditiveData;
    }

    public static void CopyComponentTo(CharacterHangPoint rawChp, GameObject targetGo)
    {
        CharacterHangPoint chp = targetGo.TryGetComponent<CharacterHangPoint>();
        UnityEditor.EditorUtility.CopySerialized(rawChp, chp);
        CharacterHangPointData tmpChp;
        for (int i = 0, len = chp.pointPosData.Count; i < len; i++)
        {
            tmpChp = chp.pointPosData[i];
            string valueName = CommonUtilGlobal.GetEnumNameAttributeValue(tmpChp.supportHangPointType);
            if (string.IsNullOrEmpty(valueName))
                continue;
            GameObject findGo = ToolKit.FindChildDeep(targetGo, valueName);
            if (findGo != null)
            {
                tmpChp.bindTrans = findGo.transform;
            }
        }

        if (chp.GetActionEventCfg() != rawChp.GetActionEventCfg())
            chp.SetActionEventCfg(rawChp.GetActionEventCfg());

        if (chp.GetMaterialCurveCfg() != rawChp.GetMaterialCurveCfg())
            chp.SetMaterialCurveCfg(rawChp.GetMaterialCurveCfg());

        if (chp.GetSoundCfg() != rawChp.GetSoundCfg())
            chp.SetSoundCfg(rawChp.GetSoundCfg());

        if (chp.GetInitEffectDatas() != rawChp.GetInitEffectDatas())
            chp.SetInitEffectDatas(rawChp.GetInitEffectDatas());
    }

    public static void ClearCreatedPoints(CharacterHangPoint chp)
    {
        if (chp.points != null && chp.points.Count > 0)
        {
            for (int i = 0; i < chp.points.Count; i++)
            {
                if (chp.points[i].isManualCreate)
                {
                    DestroyImmediate(chp.points[i].gameObject);
                }
            }

            chp.points.Clear();
        }
    }

    public static void UnFold(CharacterHangPoint chp)
    {
        if (chp.points != null && chp.points.Count > 0)
        {
            chp.pointPosData.Clear();
            chp.pointPosData = new List<CharacterHangPointData>();
            for (int i = 0; i < chp.points.Count; i++)
            {
                GameObject go = chp.points[i].gameObject;
                SupportHangPointType supportHangPointType = chp.points[i].supportHangPointType;
                if (go != null)
                {
                    chp.pointPosData.Add(new CharacterHangPointData(go.transform.parent,
                        go.transform.localPosition, go.transform.localScale, go.transform.localEulerAngles,
                        supportHangPointType));
                }
            }

            ClearCreatedPoints(chp);
        }
    }

    public static List<CharacterHangPointData> copyPointPosData;

    public static void ParseBones(CharacterHangPoint chp)
    {
        if (copyPointPosData != null)
            chp.pointPosData = new List<CharacterHangPointData>(copyPointPosData);
    }

    public void CopyBones(CharacterHangPoint chp)
    {
        copyPointPosData = chp.pointPosData;
    }

    public static void CopyTo(CharacterHangPoint rawChp, CharacterHangPoint otherChp)
    {
        otherChp.initStandEulerY = rawChp.initStandEulerY;
        otherChp.initRightNowStandEulerY = rawChp.initRightNowStandEulerY;
        otherChp.standEulerChangeCurve = rawChp.standEulerChangeCurve;
        otherChp.standEulerChangeDuration = rawChp.standEulerChangeDuration;
        otherChp.defaultScale = rawChp.defaultScale;
        otherChp.starLevelDataList = rawChp.starLevelDataList;
        otherChp.systemScaleDataList = rawChp.systemScaleDataList;
        otherChp.SetInitEffectDatas(rawChp.GetInitEffectDatas());
        otherChp.actionEventName = rawChp.actionEventName;
        otherChp.SetActionEventCfg(rawChp.GetActionEventCfg());
        otherChp.actionMaterialName = rawChp.actionMaterialName;
        otherChp.SetMaterialCurveCfg(rawChp.GetMaterialCurveCfg());
        otherChp.SetSoundCfg(rawChp.GetSoundCfg());
        otherChp.pointPosData = new List<CharacterHangPointData>();
        for (int i = 0, len = rawChp.pointPosData.Count; i < len; i++)
        {
            CharacterHangPointData oldChpData = rawChp.pointPosData[i];
            if (oldChpData.bindTrans != null)
            {
                CharacterHangPointData newChpData = oldChpData.Clone();
                newChpData.bindTrans = null;
                //找不到的话，就创建出来;
                BuildSameBindTransForParent(oldChpData.bindTrans, otherChp);
                GameObject findGo = ToolKit.FindChildDeep(otherChp.gameObject, oldChpData.bindTrans.name);
                if (findGo != null)
                {
                    newChpData.bindTrans = findGo.transform;
                    BuildSameBindTransForChild(oldChpData.bindTrans, newChpData.bindTrans);
                }
                otherChp.pointPosData.Add(newChpData);
            }
        }
        otherChp.pointPosDataFoldOutEditor = rawChp.pointPosDataFoldOutEditor;
        // otherChp.EnableOptimizeTransformHierarchy = rawChp.EnableOptimizeTransformHierarchy;
    }

    /***
     * 创建相同的绑点;
     */
    private static void BuildSameBindTransForParent(Transform oldBindTrans, CharacterHangPoint newChp)
    {
        //先找到oldBindTrans相对于chp的树链, 首元素是bindTrans;
        List<Transform> treeList = new List<Transform>();
        CharacterHangPoint oldChp = null;
        Transform tmpFindTrans = oldBindTrans;
        do
        {
            treeList.Add(tmpFindTrans);
            tmpFindTrans = tmpFindTrans.parent;
            oldChp = tmpFindTrans.GetComponent<CharacterHangPoint>();
        } while (oldChp == null);

        Transform tmpParent = newChp.transform;
        for (int i = treeList.Count - 1; i >= 0; i--)
        {
            Transform refTrans = treeList[i];
            Transform newTrans = tmpParent.Find(refTrans.name);
            if (newTrans == null)
            {
                newTrans = new GameObject(refTrans.name).transform;
                newTrans.SetParent(tmpParent);
                newTrans.localPosition = refTrans.localPosition;
                newTrans.localEulerAngles = refTrans.localEulerAngles;
                newTrans.localScale = refTrans.localScale;
            }
            tmpParent = newTrans;
        }
    }
    
    private static void BuildSameBindTransForChild(Transform oldBindTrans, Transform newBindTrans)
    {
        for (int i = 0; i < oldBindTrans.childCount; i++)
        {
            Transform oldChild = oldBindTrans.GetChild(i);
            Transform newChild = newBindTrans.Find(oldChild.name);
            if (newChild == null)
            {
                newChild = new GameObject(oldChild.name).transform;
                newChild.SetParent(newBindTrans);
            }
            newChild.localPosition = oldChild.localPosition;
            newChild.localEulerAngles = oldChild.localEulerAngles;
            newChild.localScale = oldChild.localScale;
            BuildSameBindTransForChild(oldChild, newChild);
        }
    }
}

#endif