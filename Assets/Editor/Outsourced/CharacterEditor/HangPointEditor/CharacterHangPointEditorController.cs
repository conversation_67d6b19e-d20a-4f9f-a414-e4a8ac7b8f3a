#if !LOGIC_THREAD && !ENABLE_TYPE_TREE_IGNORE
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using ModelImport;
using TKFrame;
using UnityEditor;
using UnityEditor.Experimental.SceneManagement;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;
using Debug = UnityEngine.Debug;
using SupportHangPointType = CharacterHangPoint.SupportHangPointType;

/// <summary>
/// 挂点编辑器控制器;
/// 用于集成相关的操作;
/// created:xfilsonpan
/// date:2020-11-07
/// </summary>
public class CharacterHangPointEditorController : EditorWindow
{
    public static string editorPath = Application.dataPath + "/Editor/CharacterEditor/Editor";
    public static string editorCfgPath = editorPath + "/hangpointCfg.json";
    public static string recordPreLocOffsetInfoPath = editorPath + "/recordPreLocOffsetInfo.json";
    public static string curFileFullPath = editorPath + "/CharacterHangPointEditorController.cs";

    public static string CharacterHangPointTypeFullPath =
        Application.dataPath + "/ChessBattle/BattlePluginCore/_Global/Enums/CharacterHangPointType.cs";

    public enum GameModelType
    {
        Hero,
        LittleLegend,
        Monster,
    }

    public static bool IsNeedApplyListenFoldBone = true;
    // [InitializeOnLoadMethod]
    // static void Start()
    // {
    //     //监听一下apply;
    //     PrefabUtility.prefabInstanceUpdated = delegate
    //     {
    //         if (!IsNeedApplyListenFoldBone) return;
    //         if (hpeCfg == null || string.IsNullOrEmpty(hpeCfg.ArtResPath)) return;
    //         GameObject go = null;
    //         if (Selection.activeTransform)
    //         {
    //             go = Selection.activeGameObject;
    //             CharacterHangPoint chp = go.GetComponent<CharacterHangPoint>();
    //             if (chp != null && !chp.isFoldingBone)
    //             {
    //                 FoldAllBonesByShow(go, false, false);
    //             }
    //         }
    //
    //         AssetDatabase.SaveAssets();
    //         if (go)
    //         {
    //             EditorApplication.delayCall = delegate { Selection.activeGameObject = go; };
    //         }
    //     };
    //
    // }

    private class RecordDataItem
    {
        public string name;
        public string meshAssetPath; //因为资源会变，用的时候, load;
        public string mainObjectFileID = "0";
    }

    private class RecordDataContainer
    {
        public List<RecordDataItem> skinMeshDataItemList = new List<RecordDataItem>();

        public static RecordDataContainer RecordData(GameObject go)
        {
            RecordDataContainer dataContainer = new RecordDataContainer();
            SkinnedMeshRenderer[] srArr = go.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            for (int i = 0, len = srArr.Length; i < len; i++)
            {
                RecordDataItem rdi = new RecordDataItem();
                rdi.name = srArr[i].name;
                rdi.meshAssetPath = AssetDatabase.GetAssetPath(srArr[i].sharedMesh);
                dataContainer.skinMeshDataItemList.Add(rdi);
            }

            return dataContainer;
        }

        public RecordDataItem GetRecordDataItemByMeshAssetPath(string meshAssetPath)
        {
            for (int i = 0, len = this.skinMeshDataItemList.Count; i < len; i++)
            {
                if (this.skinMeshDataItemList[i].meshAssetPath.Equals(meshAssetPath))
                {
                    return this.skinMeshDataItemList[i];
                }
            }

            return null;
        }

        public void RestoreData(GameObject go)
        {
            for (int k = 0, klen = this.skinMeshDataItemList.Count; k < klen; k++)
            {
                RecordDataItem rdi = this.skinMeshDataItemList[k];
                SkinnedMeshRenderer[] srArr = go.GetComponentsInChildren<SkinnedMeshRenderer>();
                for (int i = 0, len = srArr.Length; i < len; i++)
                {
                    if (srArr[i].name.Contains(rdi.name))
                    {
                        srArr[i].sharedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(rdi.meshAssetPath);
                        SetMeshMainObjectFileID(rdi.meshAssetPath, rdi.mainObjectFileID);
                        break;
                    }
                }
            }

            // mesh.Clear();
            //
            // // Do some calculations...
            // mesh.vertices = newVertices;
            // mesh.uv = newUV;
            // mesh.triangles = newTriangles;
        }
    }

    /// <summary>
    /// 挂点的记录信息;
    /// </summary>
    private class HangPointRecordData
    {
        public enum MakeType
        {
            Maya,
            Unity,
        }

        public string bonePath { get; private set; } //骨骼路径;
        public string locName { get; private set; } //挂点名字;
        public SupportHangPointType shpType;
        public Vector3 loc_locPos;
        public Vector3 loc_locEuler;
        public Vector3 loc_locScale;
        public MakeType makeType { get; private set; }


        public void SetData_Unity_MakeLoc(Transform locTrans, string bonePath, SupportHangPointType shpType)
        {
            this.shpType = shpType;
            this.makeType = MakeType.Unity;
            this.bonePath = bonePath;
            this.locName = locTrans.name;
            this.loc_locPos = locTrans.localPosition;
            this.loc_locEuler = locTrans.localEulerAngles;
            this.loc_locScale = locTrans.localScale;
        }

        public void SetData_Maya_MakeLoc(string locTransName, string bonePath, SupportHangPointType shpType)
        {
            this.shpType = shpType;
            this.makeType = MakeType.Maya;
            this.bonePath = bonePath;
            this.locName = locTransName;
            this.loc_locPos = Vector3.zero;
            this.loc_locEuler = Vector3.zero;
            this.loc_locScale = Vector3.one;
        }
    }

    static CharacterHangPointEditorController()
    {
    }

    /*** todo list
     - [ ] 获取创建挂点的菜单项目;
     - [ ] 新增挂点的前置步骤;
     - [ ] 导入fbx,参数是否勾选优化;
     - [ ] show预制体apply后要优化完骨骼数据; 
     */
    // [MenuItem("GameObject/添加挂点", false, 0)]

    private static void MakeFileExist(string fileFullPath)
    {
        if (!File.Exists(fileFullPath))
        {
            FileStream fs = File.Create(fileFullPath);
            if (fs != null)
            {
                fs.Close();
                fs.Dispose();
            }
        }
    }

    // [MenuItem("GameObject/添加挂点/->挂点路径配置<-", false, 0)]
    // public static void OpenWindow()
    // {
    //     CharacterHangPointEditorController window =
    //         EditorWindow.GetWindow(typeof(CharacterHangPointEditorController), false, "挂点路径配置") as
    //             CharacterHangPointEditorController;
    //     window.ShowPopup();
    // }

    #region 刷新show预制体的全部骨骼

    public static IEnumerator RefreshAllBonesByShow(GameObject go)
    {
        if(go  == null)yield break;
        if (!IsHasHangPoint(go)) yield break;
        string pureHeroName = null;
        GameModelType gameModelType;
        FetchGoNameInfo(go, out pureHeroName, out gameModelType);
        GameObject instantiateGo = go;
        bool isHasInstantiated = false;
        if (AssetDatabase.Contains(go))
        {
            //说明存在于in project中，需要实例化一份出来;
            instantiateGo = PrefabUtility.InstantiatePrefab(go) as GameObject;
            isHasInstantiated = true;
        }
        yield return YieldRefreshAllBonesByShow(instantiateGo, isHasInstantiated);
    }

    public static IEnumerator YieldRefreshAllBonesByShow(GameObject instantiateGo, bool isHasInstantiated)
    {
        //必须得加，给个延迟让对象先上到Hierarchy，这样才能正确采样动画;
        yield return new WaitForSeconds(0.1f);
        SetDisplayProgressBar("刷新中", "刷新show预制体的全部骨骼", 0);
        //记录下之前的数据;
        CharacterHangPoint chp = instantiateGo.GetComponent<CharacterHangPoint>();
        List<CharacterHangPointData> preChpDataList = new List<CharacterHangPointData>(chp.pointPosData);
        CharacterHangPointEditor.AdditiveCreateBones(chp, false);
        EditorCoroutineRunner.StartEditorCoroutine(
            AutoRefreshLogicToViewAnimationHangPoint(instantiateGo, isHasInstantiated, preChpDataList, true));
    }


    public static IEnumerator AutoRefreshLogicToViewAnimationHangPoint(GameObject go, bool isHasInstantiated,
        List<CharacterHangPointData> preChpDataList, bool needResetHangPointData = false)
    {
        SetDisplayProgressBar("同步逻辑到显示层的动画挂点", "自动设置逻辑挂点中", 0);
#if LOGIC_THREAD
        Debug.LogError("LOGIC_THREAD is Open! Can not do this Please close LOGIC_THREAD micro AutoRefreshLogicToViewAnimationHangPoint");
        yield break;
#else
        yield return CharacterHangPointBatchEditor.SyncLogicToViewAnimationHangPoint(go, 0, needResetHangPointData,
            false, 1.0f);
#endif
        CharacterHangPoint chp = go.GetComponent<CharacterHangPoint>();
        SetDisplayProgressBar("同步逻辑到显示层的动画挂点", "再次执行apply操作，保存挂点", 0);
        // //旋转归0,防止因为播放动画导致的lod旋转变化了;
        // for (int i = 0, len = AnimationAutoCreateEditor.lodName.Length; i < len; i++)
        // {
        //     Transform lodTrans = go.transform.Find(AnimationAutoCreateEditor.lodName[i]);
        //     if (lodTrans != null)
        //     {
        //         lodTrans.localRotation = Quaternion.identity;
        //     }
        // }
        ResetLodTransformInfo(go);
        // CharacterHangPoint ccc1 = go.GetComponent<CharacterHangPoint>();
        // Debug.Log("准备应用了11111");
        // List<CharacterHangPointData> posDataList1 = chp.pointPosData;
        // for (int i = 0, len = posDataList1.Count; i<len; i++) {
        //     if (posDataList1[i].isTransformDataFromAnimation)
        //     {
        //         Debug.Log("再次确认下1111 "+posDataList1[i].dependAnimationName+" , "+posDataList1[i].supportHangPointType+" , "+posDataList1[i].pos+"  "+ccc1.GetInstanceID());
        //     }
        // }
        //查看下之前记录的绑骨数据offset, 要设置到新的挂点数据中;
        if (preChpDataList != null)
        {
            for (int i = 0, len = preChpDataList.Count; i < len; i++)
            {
                CharacterHangPointData preChpData = preChpDataList[i];
                if (preChpData != null)
                {
                    //查看是否有有对应的挂点项;
                    CharacterHangPointData chpData = chp.__GetReallyPosData(preChpData.supportHangPointType,
                        preChpData.dependActionlabel, preChpData.starHeroModelScaleRate, false, isUseSecond:false);
                    if (chpData != null && !chpData.isTransformDataFromAnimation &&
                        (!chpData.pos.Equals(preChpData.pos) || !chpData.eulerAngles.Equals(preChpData.eulerAngles) ||
                         !chpData.scale.Equals(preChpData.scale)))
                    {
                        chpData.pos = preChpData.pos;
                        chpData.eulerAngles = preChpData.eulerAngles;
                        chpData.scale = preChpData.scale;
                    }
                }
            }
        }

        // CharacterHangPoint ccc = go.GetComponent<CharacterHangPoint>();
        // Debug.Log("准备应用了");
        // List<CharacterHangPointData> posDataList = chp.pointPosData;
        // for (int i = 0, len = posDataList.Count; i<len; i++) {
        //     if (posDataList[i].isTransformDataFromAnimation)
        //     {
        //         Debug.Log("再次确认下 "+posDataList[i].dependAnimationName+" , "+posDataList[i].supportHangPointType+" , "+posDataList[i].pos+"  "+ccc.GetInstanceID());
        //     }
        // }
        PrefabUtility.ApplyPrefabInstance(go, InteractionMode.UserAction);

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        if (isHasInstantiated)
        {
            GameObject.DestroyImmediate(go);
        }
        StopDisplayProgressBar();
    }

    public static bool IsHasHangPoint(GameObject go)
    {
        if (go.GetComponent<CharacterHangPoint>() != null)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 通过show gameobject获取fbx的完整路径;
    /// </summary>
    /// <param name="go"></param>
    /// <returns></returns>
    public static void FetchGoNameInfo(GameObject go, out string pureHeroName, out GameModelType gameModelType)
    {
        gameModelType = GameModelType.Hero;
        pureHeroName = null;
        //获取show本身所在的目录名;
        string goPath = null;
        if (AssetDatabase.Contains(go))
        {
            goPath = AssetDatabase.GetAssetPath(go);
        }
        else
        {
            UnityEngine.Object parentObject = EditorUtility.GetPrefabParent(go);
            goPath = AssetDatabase.GetAssetPath(parentObject);
        }

        FileInfo goFileInfo = new FileInfo(goPath);
        string goSetName = goFileInfo.Directory.Name;
        string heroName = go.name;
        heroName = heroName.Split(' ')[0].Trim();
        heroName = heroName.Replace("_show", "");
        pureHeroName = heroName;
        if (pureHeroName.StartsWith("h_"))
        {
            gameModelType = GameModelType.Hero;
            return;
        }

        if (pureHeroName.StartsWith("m_"))
        {
            gameModelType = GameModelType.Monster;
            return;
        }

        if (pureHeroName.StartsWith("t_"))
        {
            gameModelType = GameModelType.LittleLegend;
            return;
        }
    }

    private static string GetLittleLegendFbxPath(string dirFullName, string pureLittleName)
    {
        //小小英雄分h,low，
        string pureLittleName_fbx = pureLittleName + ".fbx";
        string[] fiNameArr = Directory.GetFiles(dirFullName, pureLittleName_fbx, SearchOption.AllDirectories);
        if (fiNameArr.Length > 0)
        {
            return fiNameArr[0];
        }

        return null;
    }

    private static string GetChildPath(Transform childTrans, Transform parentTrans)
    {
        List<string> recordNameList = new List<string>();
        Transform curTrans = childTrans;
        while (!curTrans.Equals(parentTrans))
        {
            recordNameList.Add(curTrans.name);
            curTrans = curTrans.parent;
        }

        recordNameList.Reverse();
        string rtnStr = string.Join("/", recordNameList.ToArray());
        return rtnStr;
    }

    /// <summary>
    /// 获取挂点信息进行记录;
    /// </summary>
    /// <param name="go"></param>
    /// <returns></returns>
    private static List<HangPointRecordData> GetHangPointRecordData(GameObject go, ModelImporter mi,
        bool isFoldAllBones)
    {
        if (!IsHasHangPoint(go)) return null;
        List<HangPointRecordData> recordDataList = new List<HangPointRecordData>();
        List<Transform> locTransList = new List<Transform>();
        RecurseFindLoc(go.transform, ref locTransList);
        //模型的所有骨骼点;
        List<string> allTransformPathList = mi.transformPaths.ToList();
        for (int i = 0, len = locTransList.Count; i < len; i++)
        {
            Transform locTrans = locTransList[i];
            List<SupportHangPointType> shpTypeList =
                CharacterHangPoint.GetSupportHangPointTypeByName(locTrans.name, false);
            for (int k = 0, klen = shpTypeList.Count; k < klen; k++)
            {
                SupportHangPointType shpType = shpTypeList[k];
                HangPointAttribute hangPointAttr =
                    CommonUtilGlobal.GetEnumNameArrAttributeValue(shpType)[0] as HangPointAttribute;
                if (hangPointAttr.ForceFrameIndexSample < 0)
                {
                    AddToRecordDataList(allTransformPathList, shpTypeList[k], locTrans, recordDataList);
                }
            }
        }

        return recordDataList;
    }

    private static void AddToRecordDataList(List<string> allTransformPathList, SupportHangPointType shpType,
        Transform locTrans, List<HangPointRecordData> recordDataList)
    {
        if (locTrans == null) return;
        string locName = locTrans.name;
        //测试下挂点名字是否存在与骨骼中;
        string boneForLocPath = allTransformPathList.Find(delegate(string item) { return item.Contains(locName); });
        bool isLocExistInAllBone = !string.IsNullOrEmpty(boneForLocPath);
        HangPointRecordData hprData = new HangPointRecordData();
        //存在表明是以前的在maya中直接处理骨骼的方案;
        if (isLocExistInAllBone)
        {
            hprData.SetData_Maya_MakeLoc(locTrans.name, boneForLocPath, shpType);
        }
        //不存在表明是直接在unity里创建挂点的方案;
        else
        {
            string parentBoneName = locTrans.parent.name;
            boneForLocPath = allTransformPathList.Find(delegate(string item)
            {
                string[] boneTagArr = item.Split('/');
                if (boneTagArr.Length > 0 && boneTagArr[boneTagArr.Length - 1].Equals(parentBoneName))
                {
                    return true;
                }

                return false;
            });
            hprData.SetData_Unity_MakeLoc(locTrans, boneForLocPath, shpType);
        }

        recordDataList.Add(hprData);
    }

    private static void RecurseFindLoc(Transform rootTrans, ref List<Transform> findedList)
    {
        for (int i = 0, len = rootTrans.childCount; i < len; i++)
        {
            Transform curTrans = rootTrans.GetChild(i);
            if (IsLoc(curTrans))
            {
                findedList.Add(curTrans);
            }

            if (curTrans.childCount > 0)
            {
                RecurseFindLoc(curTrans, ref findedList);
            }
        }
    }

    private static bool IsLoc(Transform trans)
    {
        return IsLoc(trans.name);
    }

    private static bool IsLoc(string transName)
    {
        if (transName.Contains("loc"))
        {
            return true;
        }

        return false;
    }

    private static Stopwatch progressbarSW = new Stopwatch();

    private static void StartDisplayProgressBar(string title)
    {
        Debug.Log(string.Format("开始计算耗时-----------------------------------【{0}】", title));
        progressbarSW.Start();
    }

    private static void StopDisplayProgressBar()
    {
        Debug.Log("计算耗时结束-----------------------------------");
        progressbarSW.Stop();
        EditorUtility.ClearProgressBar();
    }

    private static void SetDisplayProgressBar(string title, string content, float progress)
    {
        Debug.Log(string.Format("花费时间: 【{0}】: {1}   进度:{2}  花费时间: {3}ms", title, content, progress,
            progressbarSW.ElapsedMilliseconds));
        progressbarSW.Restart();
        EditorUtility.DisplayProgressBar(title, content, progress);
    }

    public static void ResetLodTransformInfo(GameObject showGo)
    {
        Animator showAni = showGo.GetComponent<Animator>();
        if (showAni != null)
        {
            Avatar showAvatar = showAni.avatar;
            if (showAvatar != null)
            {
                string showAvatarPath = AssetDatabase.GetAssetPath(showAvatar);
                if (!string.IsNullOrEmpty(showAvatarPath))
                {
                    string modelPath = showAvatarPath.Replace(".asset", ".prefab");
                    GameObject showGoModel = AssetDatabase.LoadAssetAtPath<GameObject>(modelPath);
                    if (showGoModel != null)
                    {
                        for (int i = 0, len = ConstVar.lodName.Length; i < len; i++)
                        {
                            string lodName = ConstVar.lodName[i];
                            Transform lodTrans = showGoModel.transform.Find(lodName);
                            if (lodTrans != null)
                            {
                                Transform showLodTrans = showGo.transform.Find(lodName);
                                if (showLodTrans != null)
                                {
                                    showLodTrans.localPosition = lodTrans.localPosition;
                                    showLodTrans.localRotation = lodTrans.localRotation;
                                    showLodTrans.localScale = lodTrans.localScale;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 导入Fbx文件;
    /// </summary>
    /// <param name="fbxFullPath"></param>
    /// <param name="targetDirPath"></param>
    /// <param name="showGo"></param>
    /// <param name="needFoldAllBones"></param>
    private static void ImportFbxFileToRefreshShow(string pureHeroName, string fbxFullPath, string targetDirPath,
        GameObject showGo, bool needFoldAllBones, GameModelType gameModelType)
    {
        do
        {
            //文件不存在;
            if (!File.Exists(fbxFullPath)) break;
            //从美术资源目录里赋值资源到项目中;
            if (!Directory.Exists(targetDirPath))
            {
                Directory.CreateDirectory(targetDirPath);
            }

            FileInfo fileInfo = new FileInfo(fbxFullPath);
            DirectoryInfo dirInfo = new DirectoryInfo(targetDirPath);
            string targetPath = Path.Combine(dirInfo.FullName, fileInfo.Name);
            if (File.Exists(targetPath))
            {
                File.Delete(targetPath);
            }

            SetDisplayProgressBar("fbx处理中", "fbx文件拷贝中", 0);
            File.Copy(fbxFullPath, targetPath, true);
            // AssetDatabase.Refresh();
            //已导入fbx到工程中;
            string dataPathConvert = Application.dataPath.Replace("/", "\\");
            string assetTargetPath = "Assets" + targetPath.Replace(dataPathConvert, "");
            SetDisplayProgressBar("fbx处理中", "fbx导入unity中，并编译", 0);
            AssetDatabase.ImportAsset(assetTargetPath, ImportAssetOptions.DontDownloadFromCacheServer);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            // return;
            ModelImporter mi = AssetImporter.GetAtPath(assetTargetPath) as ModelImporter;
            SetDisplayProgressBar("fbx处理中", "fbx设置骨骼是否优化选项中", 0);
            //获取对应的绑骨挂点;
            List<string> allTransformPathList = mi.transformPaths.ToList();
            List<HangPointRecordData> hprDataList = GetHangPointRecordData(showGo, mi, needFoldAllBones);
            if (needFoldAllBones)
            {
                mi.optimizeGameObjects = true;
                //是否是只展示extrace的骨骼数据;
                List<string> hprStrList = new List<string>();
                for (int i = 0, len = hprDataList.Count; i < len; i++)
                {
                    HangPointRecordData hprData = hprDataList[i];
                    hprStrList.Add(hprData.bonePath);
                }

                hprStrList = hprStrList.FindAll(delegate(string item)
                {
                    return allTransformPathList.IndexOf(item) >= 0;
                });
                //这里还要兼容下中途maya新增挂点的情况;
                for (int i = 0, len = allTransformPathList.Count; i < len; i++)
                {
                    string transPath = allTransformPathList[i];
                    string[] splitNameArr = transPath.Split('/');
                    string lastName = transPath;
                    if (splitNameArr.Length > 0)
                    {
                        lastName = splitNameArr[splitNameArr.Length - 1];
                    }

                    if (!string.IsNullOrEmpty(lastName) && IsLoc(lastName))
                    {
                        int findedIndex = hprStrList.FindIndex(delegate(string item)
                        {
                            return item.Equals(transPath);
                        });
                        if (findedIndex < 0)
                        {
                            List<SupportHangPointType> shpTypeList =
                                CharacterHangPoint.GetSupportHangPointTypeByName(lastName, false);
                            for (int k = 0, klen = shpTypeList.Count; k < klen; k++)
                            {
                                HangPointRecordData hprData = new HangPointRecordData();
                                hprData.SetData_Maya_MakeLoc(lastName, transPath, shpTypeList[k]);
                                hprDataList.Add(hprData);
                                hprStrList.Add(hprData.bonePath);
                            }
                        }
                    }
                }

                mi.extraExposedTransformPaths = hprStrList.ToArray();
            }
            else
            {
                //展开所有骨骼;
                mi.optimizeGameObjects = false;
            }

            EditorUtility.SetDirty(mi);
            mi.SaveAndReimport();
            GameObject newModelObj = AssetDatabase.LoadAssetAtPath<GameObject>(assetTargetPath);
            var modelAssets = AssetDatabase.LoadAllAssetsAtPath(assetTargetPath);
            SetDisplayProgressBar("fbx处理中", "fbx mesh拆分实例化", 0);
            AssetDatabase.Refresh();
            //mesh 在优化骨骼和非优化骨骼间会不一样，这里记录下mesh数据;
            RecordDataContainer rdContainer = RecordDataContainer.RecordData(showGo);
            List<string> newMesPathList = new List<string>(2);
            for (int i = 0, len = modelAssets.Length; i < len; i++)
            {
                var modelAsset = modelAssets[i];
                if (modelAsset is Mesh)
                {
                    string newMeshPath =
                        ModelImportCore.CreateModelAssetMesh(pureHeroName, ".FBX", targetPath, modelAsset);
                    newMesPathList.Add(newMeshPath);
                    //记录mainObjectFileID, 否则切换骨骼优化，会mesh显示错乱;
                    RecordDataItem recordDataItem = rdContainer.GetRecordDataItemByMeshAssetPath(newMeshPath);
                    //为了兼容部分模型原来没有制作lod_l的情况;
                    if (recordDataItem == null)
                    {
                        recordDataItem = new RecordDataItem();
                        recordDataItem.name = modelAsset.name;
                        recordDataItem.meshAssetPath = newMeshPath;
                        rdContainer.skinMeshDataItemList.Add(recordDataItem);
                    }

                    recordDataItem.mainObjectFileID = GetMeshMainObjectFileID(targetPath);
                }
            }

            //刷新写入到show gameobject中;
            // newModelObj.name = newModelObj.name+"__newModelObj";
            // showGo.name = showGo.name+"__showGo";
            switch (gameModelType)
            {
                case GameModelType.Hero:
                case GameModelType.Monster:
                {
                    SetDisplayProgressBar("fbx处理中", "使用RefreshModelToShow", 0);
                    OverrideShowGameObject(newModelObj, showGo, gameModelType);
                }
                    break;
                case GameModelType.LittleLegend:
                {
                    SetDisplayProgressBar("fbx处理中", "将新的骨骼数据应用到小小英雄本体上", 0);
                    GameObject rawGo = PrefabUtility.GetCorrespondingObjectFromOriginalSource<GameObject>(showGo);
                    if (rawGo != null)
                    {
                        OverrideShowGameObject(newModelObj, rawGo, GameModelType.LittleLegend);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("错误", "没有找到小小英雄的本体:" + showGo.name, "确定");
                    }
                }
                    break;
            }

            SetDisplayProgressBar("fbx处理中", "修正skin mesh的引用", 0);
            rdContainer.RestoreData(showGo);
            CharacterHangPoint chp = showGo.GetComponent<CharacterHangPoint>();
            //清空挂点;
            chp.pointPosData.Clear();
            //unity挂点填充;
            for (int i = 0, len = hprDataList.Count; i < len; i++)
            {
                HangPointRecordData hprData = hprDataList[i];
                Transform newBoneTrans = null;
                if (hprData.makeType == HangPointRecordData.MakeType.Maya ||
                    allTransformPathList.IndexOf(hprData.bonePath) < 0)
                {
                    newBoneTrans = showGo.transform;
                }
                else
                {
                    if (needFoldAllBones)
                    {
                        string[] boneTagArr = hprData.bonePath.Split('/');
                        newBoneTrans = ToolKit.FindChildDeep(showGo, boneTagArr[boneTagArr.Length - 1]).transform;
                    }
                    else
                    {
                        newBoneTrans = showGo.transform.Find(hprData.bonePath);
                    }
                }

                Transform newLocTrans = newBoneTrans.Find(hprData.locName);
                if (newLocTrans == null)
                {
                    newLocTrans = new GameObject(hprData.locName).transform;
                    newLocTrans.SetParent(newBoneTrans);
                    newLocTrans.localPosition = hprData.loc_locPos;
                    newLocTrans.localEulerAngles = hprData.loc_locEuler;
                    newLocTrans.localScale = hprData.loc_locPos;
                }

                SupportHangPointType shpType = hprData.shpType;
                CharacterHangPointData chpData = new CharacterHangPointData(newLocTrans, Vector3.zero,
                    Vector3.one, Vector3.zero, shpType);
                HangPointAttribute hangPointAttr =
                    CommonUtilGlobal.GetEnumNameArrAttributeValue(shpType)[0] as HangPointAttribute;
                chpData.IsIgnoreHeight = hangPointAttr.IsIgnoreHeight;
                chp.pointPosData.Add(chpData);
            }

            //删除;
            if (dirInfo.Exists)
            {
                dirInfo.Delete(true);
            }

            AssetDatabase.Refresh();
        } while (false);
    }

    /// <summary>
    /// 重写show gameobject, 不采用delete, create是因为避免meta guid变了造成不必要的麻烦;
    /// </summary>
    /// <param name="modelObj"></param>
    /// <param name="prefabShowGo"></param>
    private static void OverrideShowGameObject(GameObject modelObj, GameObject showGo, GameModelType gameModelType)
    {
        DelegateInterface.AnimationAutoCreateEditor_RefreshModelToShow?.Invoke(modelObj, showGo, gameModelType);
    }

    private const string matchKey_MainObjectFileID = "  mainObjectFileID: ";

    private static bool IsContainsMetaFile(string assetPath, out string fullPath)
    {
        string metaFullPath = assetPath + ".meta";
        fullPath = null;
        if (!File.Exists(metaFullPath))
        {
            EditorUtility.DisplayDialog("错误", "不存在对应的meta文件:" + metaFullPath, "确定");
            return false;
        }

        fullPath = metaFullPath;
        return true;
    }

    private static string GetMeshMainObjectFileID(string meshAssetPath)
    {
        IsContainsMetaFile(meshAssetPath, out string metaFullPath);
        string[] lineArr = File.ReadAllLines(metaFullPath);
        for (int i = 0, len = lineArr.Length; i < len; i++)
        {
            string tmpLine = lineArr[i];
            if (tmpLine.Contains(matchKey_MainObjectFileID))
            {
                return tmpLine.Replace(matchKey_MainObjectFileID, string.Empty);
            }
        }

        return null;
    }

    private static void SetMeshMainObjectFileID(string meshAssetPath, string mainObjectFileID)
    {
        IsContainsMetaFile(meshAssetPath, out string metaFullPath);
        List<string> lineList = File.ReadAllLines(metaFullPath).ToList();
        for (int i = 0, len = lineList.Count; i < len; i++)
        {
            string tmpLine = lineList[i];
            if (tmpLine.Contains(matchKey_MainObjectFileID))
            {
                lineList[i] = string.Format("{0}{1}", matchKey_MainObjectFileID, mainObjectFileID);
                break;
            }
        }

        File.WriteAllLines(metaFullPath, lineList);
    }

    #endregion

    [Serializable]
    private class RecordPreLocHero
    {
        public string path;
        public List<RecordPreLocOffsetItem> itemList = new List<RecordPreLocOffsetItem>();
    }

    [Serializable]
    public class RecordPreLocOffsetItem
    {
        public SupportHangPointType shpType;
        public bool isTransformDataFromAnimation;
        public string dependAnimationName;
        public Vector3 pos;
        public Vector3 scale1Pos;
        public Vector3 eulerAngles;
        public Vector3 scale;
        public string dependActionlabel;
    }

    [Serializable]
    private class RecordPreLocOffsetInfo
    {
        public List<RecordPreLocHero> heroList = new List<RecordPreLocHero>();
    }

    /// <summary>
    /// 记录所有骨骼的偏移信息;
    /// </summary>
    public static void RecordAllLocOffsetInfo(string showPrefabDirPath)
    {
        RecordPreLocOffsetInfo recordPreLocOffsetInfo = new RecordPreLocOffsetInfo();
        //写入新的配置;
        string[] fileInfoPathArr = Directory.GetFiles(showPrefabDirPath, "*.prefab", SearchOption.AllDirectories);
        for (int i = 0, len = fileInfoPathArr.Length; i < len; i++)
        {
            UnityEngine.GameObject tmpGo =
                (GameObject) AssetDatabase.LoadAssetAtPath(fileInfoPathArr[i], typeof(UnityEngine.GameObject));
            CharacterHangPoint chp = tmpGo.GetComponent<CharacterHangPoint>();
            EditorUtility.DisplayProgressBar("提示", "读取配置:" + tmpGo.name, (float) i / len);
            if (chp != null)
            {
                RecordPreLocHero recordHero = new RecordPreLocHero();
                recordPreLocOffsetInfo.heroList.Add(recordHero);
                //hero;
                recordHero.path = AssetDatabase.GetAssetPath(tmpGo);
                for (int k = 0, klen = chp.pointPosData.Count; k < klen; k++)
                {
                    CharacterHangPointData chpData = chp.pointPosData[k];
                    // if (string.IsNullOrEmpty(chpData.dependAnimationName))
                    // {
                    RecordPreLocOffsetItem offsetItem = new RecordPreLocOffsetItem();
                    offsetItem.pos = chpData.pos;
                    offsetItem.scale1Pos = chpData.pointDataTransAtScale1.pos;
                    // offsetItem.eulerAngles = chpData.eulerAngles;
                    // offsetItem.scale = chpData.scale;
                    offsetItem.shpType = chpData.supportHangPointType;
                    offsetItem.isTransformDataFromAnimation = chpData.isTransformDataFromAnimation;
                    offsetItem.dependAnimationName = chpData.dependAnimationName;
                    offsetItem.dependActionlabel = chpData.dependActionlabel;
                    //chpData;
                    recordHero.itemList.Add(offsetItem);
                    // }
                }
            }
        }

        EditorUtility.DisplayProgressBar("提示", "写入配置中", 1);
        MakeFileExist(recordPreLocOffsetInfoPath);
        string jsonStr = JsonUtility.ToJson(recordPreLocOffsetInfo);
        File.WriteAllText(recordPreLocOffsetInfoPath, string.Empty);
        using (StreamWriter sw = new StreamWriter(recordPreLocOffsetInfoPath))
        {
            sw.Write(jsonStr);
        }

        EditorUtility.ClearProgressBar();
    }

    public static void RestoreAllLocOffsetInfo()
    {
        if (!File.Exists(recordPreLocOffsetInfoPath))
        {
            Debug.LogError("请先记录源文件的挂点信息");
            return;
        }

        RecordPreLocOffsetInfo recordPreLocOffsetInfo = null;
        using (StreamReader sr = new StreamReader(recordPreLocOffsetInfoPath))
        {
            string jsonStr = sr.ReadToEnd();
            if (!string.IsNullOrEmpty(jsonStr))
            {
                recordPreLocOffsetInfo = JsonUtility.FromJson<RecordPreLocOffsetInfo>(jsonStr);
            }
        }

        if (recordPreLocOffsetInfo != null)
        {
            bool hasChanged = false;
            for (int i = 0, len = recordPreLocOffsetInfo.heroList.Count; i < len; i++)
            {
                RecordPreLocHero recordHero = recordPreLocOffsetInfo.heroList[i];
                if (recordHero != null)
                {
                    //获取对应的gameObject;
                    UnityEngine.GameObject tmpGo =
                        (GameObject) AssetDatabase.LoadAssetAtPath(recordHero.path, typeof(UnityEngine.GameObject));
                    if (tmpGo != null)
                    {
                        GameObject instantiateGo = PrefabUtility.InstantiatePrefab(tmpGo) as GameObject;
                        EditorUtility.DisplayProgressBar("提示", "正在还原" + instantiateGo.name, (float) i / len);
                        CharacterHangPoint chp = instantiateGo.GetComponent<CharacterHangPoint>();
                        bool chpIsChanged = false;
                        if (chp != null)
                        {
                            for (int k = 0, klen = recordHero.itemList.Count; k < klen; k++)
                            {
                                RecordPreLocOffsetItem offsetItem = recordHero.itemList[k];
                                if (offsetItem != null)
                                {
                                    //查看是否有有对应的挂点项;
                                    // for (int m = 0, mlen = chp.pointPosData.Count; m < mlen; m++)
                                    // {
                                    //     CharacterHangPointData chpData = chp.pointPosData[m];
                                    //     if(chpData)
                                    // }

                                    CharacterHangPointData chpData = chp.__GetReallyPosData(offsetItem.shpType,
                                        offsetItem.dependActionlabel, 1.0f, false, isUseSecond:false);
                                    if (chpData != null &&
                                        (!chpData.pos.Equals(offsetItem.pos) ||
                                         !chpData.pointDataTransAtScale1.pos.Equals(offsetItem.scale1Pos))
                                    ) // || !chpData.eulerAngles.Equals(offsetItem.eulerAngles) || !chpData.scale.Equals(offsetItem.scale)
                                    {
                                        chpData.pos = offsetItem.pos;
                                        if (!string.IsNullOrEmpty(offsetItem.dependActionlabel))
                                        {
                                            chpData.pointDataTransAtScale1.pos = offsetItem.scale1Pos;
                                        }

                                        // chpData.eulerAngles = offsetItem.eulerAngles;
                                        // chpData.scale = offsetItem.scale;
                                        hasChanged = true;
                                        chpIsChanged = true;
                                    }
                                }
                            }

                            if (chpIsChanged)
                            {
                                var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                                if (prefabStage != null)
                                {
                                    EditorSceneManager.OpenScene(SceneManager.GetSceneAt(0).path);
                                }

                                PrefabUtility.ApplyPrefabInstance(chp.gameObject,
                                    InteractionMode.UserAction);
                            }
                        }

                        GameObject.DestroyImmediate(instantiateGo);
                    }
                }
            }

            EditorUtility.ClearProgressBar();
            if (hasChanged)
            {
                AssetDatabase.Refresh();
                AssetDatabase.SaveAssets();
            }
        }
    }

    public static void CheckExtractAvatarBoneAndSetHangPoints(Avatar curAvatar, CharacterHangPoint hangPoint)
    {
        List<string> avatarBoneStrList = new List<string>();
        CheckExtractAvatarBoneAndSetHangPoints(curAvatar, hangPoint, ref avatarBoneStrList);
    }

    /// <summary>
    /// 设置静态挂点;
    /// </summary>
    /// <param name="curAvatar"></param>
    /// <param name="hangPoint"></param>
    /// <param name="avatarBoneStrList"></param>
    public static void CheckExtractAvatarBoneAndSetHangPoints(Avatar curAvatar, CharacterHangPoint hangPoint,
        ref List<string> avatarBoneStrList)
    {
        if (curAvatar == null)
        {
            return;
        }

        CharacterHangPointEditorController.CheckExtractAvatarBone(curAvatar, ref avatarBoneStrList);
        if (avatarBoneStrList.Count > 0)
        {
            Dictionary<string, List<string>> boneLocDic = new Dictionary<string, List<string>>();
            List<SupportHangPointType> typeList = new List<SupportHangPointType>();
            Array enumValueArr = Enum.GetValues(typeof(SupportHangPointType));
            SupportHangPointType tmpPT;
            List<string> extractBoneList = new List<string>();
            for (int i = 0, len = enumValueArr.Length; i < len; i++)
            {
                tmpPT = (SupportHangPointType) enumValueArr.GetValue(i);
                HangPointAttribute hangPointAttr =
                    CommonUtilGlobal.GetEnumNameArrAttributeValue(tmpPT)[0] as HangPointAttribute;
                if (hangPointAttr.isNeedDefaultCreateInEditor)
                {
                    string[] boneNameArr = hangPointAttr.GetBoneNameArr();
                    bool hasAddTypeList = false;
                    for (int k = 0, klen = boneNameArr.Length; k < klen; k++)
                    {
                        string boneName = boneNameArr[k];
                        if (!string.IsNullOrEmpty(boneName))
                        {
                            if (avatarBoneStrList.Contains(boneName))
                            {
                                if (!extractBoneList.Contains(boneName))
                                {
                                    extractBoneList.Add(boneName);   
                                }
                                hasAddTypeList = true;
                                typeList.Add(tmpPT);
                                List<string> locList;
                                if (!boneLocDic.TryGetValue(boneName, out locList))
                                {
                                    locList = new List<string>();
                                    boneLocDic.Add(boneName, locList);
                                }
                                locList.Add(hangPointAttr.Value);
                            }                                
                        }
                    }

                    if (!hasAddTypeList && boneNameArr.Length == 1)
                    {
                        //没写骨骼名字的话，loc就是骨骼;
                        boneLocDic.Add(hangPointAttr.Value, new List<string>());
                        typeList.Add(tmpPT);
                    }
                }
            }

            if (typeList.Count > 0)
            {
                for (int i = 0, len = enumValueArr.Length; i < len; i++)
                {
                    tmpPT = (SupportHangPointType) enumValueArr.GetValue(i);
                    HangPointAttribute hangPointAttr =
                        CommonUtilGlobal.GetEnumNameArrAttributeValue(tmpPT)[0] as HangPointAttribute;
                    string[] boneNameArr = hangPointAttr.GetBoneNameArr();
                    for (int k = 0, klen = boneNameArr.Length; k < klen; k++)
                    {
                        string boneName = boneNameArr[k];
                        if (!string.IsNullOrEmpty(boneName))
                        {
                            if (avatarBoneStrList.Contains(boneName))
                            {
                                if (!extractBoneList.Contains(boneName))
                                {
                                    extractBoneList.Add(boneName);   
                                }
                            }   
                        }
                    }
                }
                hangPoint.extractBoneList_editor.Clear();
                hangPoint.extractBoneList_editor.AddRange(extractBoneList);
                ReExtractBones(hangPoint);
                CharacterHangPoint rawChp = GetRawChp(hangPoint);
                if (rawChp != null)
                {
                    bool isSameChp = rawChp.GetInstanceID() == hangPoint.GetInstanceID();
                    //因为如果是GetCorrespondingObjectFromSource获取出来的对象设置parent无效,这里实例化再保存进去;
                    GameObject rawInstGo = null;
                    CharacterHangPoint instRawChp = rawChp;
                    if (!isSameChp)
                    {
                        rawInstGo = PrefabUtility.InstantiatePrefab(rawChp.gameObject) as GameObject;
                        instRawChp = rawInstGo.GetComponent<CharacterHangPoint>();   
                    }
                    for (int i = 0, len = typeList.Count; i < len; i++)
                    {
                        SupportHangPointType shpType = typeList[i];
                        CharacterHangPointData chpData = instRawChp.__GetReallyPosData(shpType, isUseSecond:false);
                        if (chpData == null)
                        {
                            chpData = new CharacterHangPointData(null, Vector3.zero,
                                Vector3.one, Vector3.zero, shpType);
                            HangPointAttribute hangPointAttr =
                                CommonUtilGlobal.GetEnumNameArrAttributeValue(shpType)[0] as HangPointAttribute;
                            chpData.IsIgnoreHeight = hangPointAttr.IsIgnoreHeight;
                            instRawChp.pointPosData.Add(chpData);
                        }
                    }

                    foreach (var kvp in boneLocDic)
                    {
                        GameObject bone = ToolKit.FindChildDeep(instRawChp.gameObject, kvp.Key);
                        if (bone == null)
                        {
                            bone = new GameObject(kvp.Key);
                            bone.SetParent(instRawChp.transform);
                            bone.transform.localPosition = Vector3.zero;
                            bone.transform.localEulerAngles = Vector3.zero;
                            bone.transform.localScale = Vector3.one;                            
                        }
                        if (bone != null)
                        {
                            for (int i = 0, len = kvp.Value.Count; i < len; i++)
                            {
                                GameObject locBone = ToolKit.FindChildDeep(instRawChp.gameObject, kvp.Value[i]);
                                if (locBone == null)
                                {
                                    locBone = new GameObject(kvp.Value[i]);
                                    locBone.SetParent(bone.transform);
                                    locBone.transform.localPosition = Vector3.zero;
                                    locBone.transform.localEulerAngles = Vector3.zero;
                                    locBone.transform.localScale = Vector3.one;
                                }                                
                            }
                        }
                    }

                    if (!isSameChp)
                    {
                        PrefabUtility.ApplyPrefabInstance(rawInstGo.gameObject, InteractionMode.UserAction);
                        GameObject.DestroyImmediate(rawInstGo);   
                        CharacterHangPointEditor.AdditiveCreateBones(hangPoint, false);
                    }
                    else
                    {
                        CharacterHangPointEditor.AdditiveCreateBones(rawChp, false);
                    }
                }

                EditorUtility.SetDirty(hangPoint);
            }
        }
    }

    /// <summary>
    /// 暴露骨骼到根节点的直接子节点;
    /// </summary>
    /// <param name="hangPoint"></param>
    public static void ReExtractBones(CharacterHangPoint hangPoint)
    {
        //这里要获取原生对象进行操作，这样进行直接影响变体;
        string assetPath = null;
        UnityEngine.Object correspondingObject =
            PrefabUtility.GetCorrespondingObjectFromOriginalSource(hangPoint.gameObject);
        GameObject contentRoot = hangPoint.gameObject;
        bool isTempObj = false;
        if (correspondingObject == null)
        {
            isTempObj = true;
        }
        else
        {
            assetPath = AssetDatabase.GetAssetPath(correspondingObject);
            contentRoot = PrefabUtility.LoadPrefabContents(assetPath);   
        }
        List<SupportHangPointType> typeList = null;
        if (contentRoot != null)
        {
            List<string> curSettingBoneList = new List<string>(hangPoint.extractBoneList_editor);
            List<string> cacheExtractBoneList = new List<string>();
            //记录下当前缓存的骨骼点;
            for (int i = 0, len = contentRoot.transform.childCount; i < len; i++)
            {
                cacheExtractBoneList.Add(contentRoot.transform.GetChild(i).name);
                //要去脚本来找下有没有对应的类型, 有的话要删除掉;
                Transform curFindBoneTrans = hangPoint.transform.Find(cacheExtractBoneList[i]);
                for (int k = 0, klen = curFindBoneTrans.childCount; k < klen; k++)
                {
                    Transform locTrans = curFindBoneTrans.GetChild(k);
                    typeList = CharacterHangPoint.GetSupportHangPointTypeByName(locTrans.name, false);
                    for (int m = 0, mlen = typeList.Count; m < mlen; m++)
                    {
                        //当前的变体下的没有进prefab的话，要手动删除;
                        for (int pi = hangPoint.pointPosData.Count - 1; pi >= 0; pi--)
                        {
                            if (hangPoint.pointPosData[pi].supportHangPointType == typeList[m])
                            {
                                Transform bindTrans = hangPoint.pointPosData[pi].bindTrans;
                                if (bindTrans != null &&
                                    !PrefabUtility.IsPartOfAnyPrefab(bindTrans.gameObject))
                                {
                                    GameObject.DestroyImmediate(bindTrans.gameObject);
                                }
                            }
                        }

                        hangPoint.__RemovePosData(typeList[m]);
                    }
                }
            }

            //因为小小英雄的原始物体时没有挂点脚本的;
            CharacterHangPoint contentRootHangPoint = contentRoot.GetComponent<CharacterHangPoint>();
            //使用当前设置的暴露骨骼进行计算, 先移除之前暴露的骨骼;
            for (int i = 0, len = cacheExtractBoneList.Count; i < len; i++)
            {
                if (!curSettingBoneList.Contains(cacheExtractBoneList[i]))
                {
                    //移除对应的挂点信息;
                    Transform findBoneTrans = contentRoot.transform.Find(cacheExtractBoneList[i]);
                    if (findBoneTrans != null)
                    {
                        if (contentRootHangPoint != null)
                        {
                            //可能本身就是一个挂点;
                            typeList = CharacterHangPoint.GetSupportHangPointTypeByName(findBoneTrans.name,
                                false);
                            for (int m = 0, mlen = typeList.Count; m < mlen; m++)
                            {
                                contentRootHangPoint.__RemovePosData(typeList[m]);
                            }

                            //看下原始的prefab;
                            for (int k = 0, klen = findBoneTrans.childCount; k < klen; k++)
                            {
                                Transform locTrans = findBoneTrans.GetChild(k);
                                //要去脚本来找下有没有对应的类型, 有的话要删除掉;
                                typeList = CharacterHangPoint.GetSupportHangPointTypeByName(locTrans.name,
                                    false);
                                for (int m = 0, mlen = typeList.Count; m < mlen; m++)
                                {
                                    contentRootHangPoint.__RemovePosData(typeList[m]);
                                }
                            }
                        }

                        if (CharacterHangPoint.GetSupportHangPointTypeByName(findBoneTrans.name, false).Count > 0)
                        {
                            GameObject.DestroyImmediate(findBoneTrans.gameObject);   
                        }
                    }
                }
            }

            for (int i = 0, len = curSettingBoneList.Count; i < len; i++)
            {
                //判断下是否没有对应的骨骼点,没有的话，新建;
                Transform findBoneTrans = contentRoot.transform.Find(curSettingBoneList[i]);
                if (findBoneTrans == null)
                {
                    GameObject newBoneGo = new GameObject(curSettingBoneList[i]);
                    newBoneGo.transform.SetParent(contentRoot.transform);
                    newBoneGo.transform.localPosition = Vector3.zero;
                    newBoneGo.transform.localEulerAngles = Vector3.zero;
                    newBoneGo.transform.localScale = Vector3.one;
                }
            }

            if (contentRootHangPoint != null)
            {
                RefreshExtractBoneListEditor(contentRootHangPoint);
            }

            if (!isTempObj)
            {
                PrefabUtility.SaveAsPrefabAsset(contentRoot, assetPath);
                PrefabUtility.UnloadPrefabContents(contentRoot);   
            }
            RefreshExtractBoneListEditor(hangPoint);
        }
    }

    public static void RefreshExtractBoneListEditor(CharacterHangPoint hangPoint)
    {
        hangPoint.extractBoneList_editor.Clear();
        for (int i = 0, len = hangPoint.transform.childCount; i < len; i++)
        {
            hangPoint.extractBoneList_editor.Add(hangPoint.transform.GetChild(i).name);
        }
    }

    public static CharacterHangPoint GetRawChp(CharacterHangPoint chp)
    {
        if (chp != null)
        {
            GameObject correspondingObject =
                PrefabUtility.GetCorrespondingObjectFromSource(chp.gameObject);
            if (correspondingObject != null)
            {
                CharacterHangPoint newChp = correspondingObject.GetComponent<CharacterHangPoint>();
                if (newChp != null)
                {
                    return GetRawChp(newChp);
                }
            }

            return chp;
        }

        return null;
    }

    public static void CheckExtractAvatarBone(Avatar curAvatar, ref List<string> avatarBoneStrList)
    {
        avatarBoneStrList.Clear();
        string assetPath = AssetDatabase.GetAssetPath(curAvatar);
        string[] allLineArr = File.ReadAllLines(assetPath);
        for (int i = 0, len = allLineArr.Length; i < len; i++)
        {
            string tmpLine = allLineArr[i];
            if (tmpLine.Contains("m_TOS:"))
            {
                for (int k = i + 1; k < len; k++)
                {
                    string[] idPath = allLineArr[k].Split(':');
                    //看下是否找到一条骨骼的开始，如果后面的没有包含冒号的话，说明是骨骼的链接字符串，要改为一个空格才行;
                    string curBonePath = idPath[1].Trim();
                    if (string.IsNullOrEmpty(curBonePath)) continue;
                    //继续看下有没有没找干净的;
                    for (int nextI = k + 1; nextI < len; nextI++)
                    {
                        idPath = allLineArr[nextI].Split(':');
                        if (idPath.Length == 1)
                        {
                            curBonePath += " " + idPath[0].Trim();
                            k++;
                        }
                        else
                        {
                            break;
                        }
                    }

                    string[] boneArr = curBonePath.Split('/');
                    for (int m = 0, mLen = boneArr.Length; m < mLen; m++)
                    {
                        if (avatarBoneStrList.IndexOf(boneArr[m]) < 0)
                        {
                            avatarBoneStrList.Add(boneArr[m]);
                        }
                    }
                }

                avatarBoneStrList.Sort();
                break;
            }
        }
    }                                                                                                                                                                                                                                                                             

    #region 获取创建挂点的菜单项目

[MenuItem("GameObject/添加挂点/->刷新挂点菜单<-", false, 0)]
    public static void AutoCreateHangPointMenuItems()
    {
        //获取要替换的开始和结束行数;
        List<string> lineList = new List<string>(128);
        int menuItemAutoCreate_startIndex = 0;
        int menuItemAutoCreate_endIndex = 0;
        bool hasFindStartTag = false;
        StringBuilder resultSb = new StringBuilder();
        using (StreamReader sr = new StreamReader(CharacterHangPointTypeFullPath))
        {
            //存储原有的内容;
            string curLine = null;
            while ((curLine = sr.ReadLine()) != null)
            {
                if (curLine.Contains("#region menuItemAutoCreate") && !curLine.Contains("Contains"))
                {
                    menuItemAutoCreate_startIndex = lineList.Count;
                    hasFindStartTag = true;
                    resultSb.AppendLine(curLine);
                }

                if (curLine.Contains("#endregion menuItemAutoCreate") && !curLine.Contains("Contains"))
                {
                    menuItemAutoCreate_endIndex = lineList.Count;
                }

                if (!hasFindStartTag)
                {
                    resultSb.AppendLine(curLine);
                }

                lineList.Add(curLine);
            }
        }

        if (hasFindStartTag)
        {
            //先清除原有的自动化生成的内容;
            lineList.RemoveRange(menuItemAutoCreate_startIndex + 1,
                menuItemAutoCreate_endIndex - menuItemAutoCreate_startIndex - 1);
            //开始填充新的自动化生成的内容;
            Array shTypeArr = Enum.GetValues(typeof(SupportHangPointType));
            for (int i = 0, len = shTypeArr.Length; i < len; i++)
            {
                FillCheckCanCreateMethodByHangPointType((SupportHangPointType) shTypeArr.GetValue(i), resultSb);
                FillCreateMethodByHangPointType((SupportHangPointType) shTypeArr.GetValue(i), resultSb);
            }

            //填充剩下的内容;
            for (int i = menuItemAutoCreate_startIndex + 1, len = lineList.Count; i < len; i++)
            {
                resultSb.AppendLine(lineList[i]);
            }

            //写入新内容;
            // return;
            File.WriteAllText(CharacterHangPointTypeFullPath, resultSb.ToString());
            AssetDatabase.Refresh();
        }
    }

    private static void FillCheckCanCreateMethodByHangPointType(SupportHangPointType shpType, StringBuilder sb)
    {
        string methodTab = "    ";
        string methodContentTab = "        ";
        HangPointAttribute hpAttr = CommonUtilGlobal.GetEnumNameArrAttributeValue(shpType)[0] as HangPointAttribute;
        string locName = hpAttr.Value;
        sb.AppendLine(methodTab + string.Format("[MenuItem(\"GameObject/添加挂点/{0}({1})\", true, 0)]", locName,
            hpAttr.Desc));
        sb.AppendLine(methodTab + string.Format("static bool CheckCreateHangPointType_{0}_{1}()", locName,
            (int) shpType));
        sb.AppendLine(methodTab + "{");
        //函数内容;
        sb.AppendLine(methodContentTab + "if(Selection.activeGameObject != null){");
        sb.AppendLine(methodContentTab + methodContentTab + string.Format(
            "return CheckCanCreateSupportHangPointGo({0}, {1});",
            "(SupportHangPointType)" + (int) shpType, "Selection.activeGameObject.transform"));
        sb.AppendLine(methodContentTab + "}");
        sb.AppendLine(methodContentTab + "return false;");
        sb.AppendLine(methodTab + "}");
    }

    private static void FillCreateMethodByHangPointType(SupportHangPointType shpType, StringBuilder sb)
    {
        string methodTab = "    ";
        string methodContentTab = "        ";
        HangPointAttribute hpAttr = CommonUtilGlobal.GetEnumNameArrAttributeValue(shpType)[0] as HangPointAttribute;
        string locName = hpAttr.Value;
        sb.AppendLine(methodTab + string.Format("[MenuItem(\"GameObject/添加挂点/{0}({1})\", false, 0)]", locName,
            hpAttr.Desc));
        sb.AppendLine(methodTab + string.Format("static void CreateHangPointType_{0}_{1}()", locName, (int) shpType));
        sb.AppendLine(methodTab + "{");
        //函数内容;ss
        sb.AppendLine(methodContentTab + "if(Selection.activeGameObject != null){");
        sb.AppendLine(methodContentTab + methodContentTab + string.Format("CreateSupportHangPointGo({0}, {1});",
            "(SupportHangPointType)" + (int) shpType, "Selection.activeGameObject.transform"));
        sb.AppendLine(methodContentTab + "}");
        sb.AppendLine(methodTab + "}");
    }

    #endregion
}
#endif