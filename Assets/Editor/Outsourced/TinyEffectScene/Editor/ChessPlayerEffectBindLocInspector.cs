using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace ZGameChess
{
    [CustomEditor(typeof(ChessPlayerEffectBindLoc))]
    public class ChessPlayerEffectBindLocInspector : Editor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            EditorGUILayout.HelpBox("制作的时候需要【注入】才能看到效果，保存的时候需要【还原】才能保证timeline的链接不丢失", MessageType.Info);

            if (GUILayout.Button("注入"))
            {
                var c = target as ChessPlayerEffectBindLoc;

                if (PrefabUtility.IsAnyPrefabInstanceRoot(c.gameObject))
                    PrefabUtility.UnpackPrefabInstance(c.gameObject, PrefabUnpackMode.Completely, InteractionMode.UserAction);

                c.Inject();
            }

            if (GUILayout.But<PERSON>("还原"))
            {
                var c = target as ChessPlayerEffectBindLoc;
                c.Revert();
            }
        }
    }
}
