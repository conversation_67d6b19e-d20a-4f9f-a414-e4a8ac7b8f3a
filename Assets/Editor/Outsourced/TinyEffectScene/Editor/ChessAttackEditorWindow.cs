#if !LOGIC_THREAD 
using FxEffect;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEditor;
using UnityEngine;

namespace TinyTool
{
    public class ChessAttackEditorWindow : EditorWindowBaseEx
    {
        public TinyAttackSceneController controller;

        public SerializedObject serializedObject;
        public SerializedProperty attackConfigSerializedProperty;

        public static void Open(TinyAttackSceneController controller)
        {
            var window = GetWindow<ChessAttackEditorWindow>(EditorLanguageControl.GetStr("攻击特效编辑器"));
            window.Init(controller);
            window.Show();
        }

        protected void Init(TinyAttackSceneController controller)
        {
            this.controller = controller;
            serializedObject = new SerializedObject(controller);
            attackConfigSerializedProperty = serializedObject.FindProperty("m_attackConfig");

            m_SubPanelList.Clear();
            CreateSubPanel<ChessAttackCtrlSubPanel>(EditorLanguageControl.GetStr("控制面板"), new Vector2(400, 400), E_SubPanelAnchor.Left, false);
            CreateSubPanel<ChessAttackCommonPropertySubPanel>(EditorLanguageControl.GetStr("通用属性"), new Vector2(400, 400), E_SubPanelAnchor.Left, new Vector2(350, 200), new Vector2(350, 800));
            CreateSubPanel<ChessAttackEffectListSubPanel>(EditorLanguageControl.GetStr("攻击效果选择"), new Vector2(350, 700), E_SubPanelAnchor.Center, new Vector2(350, 500), new Vector2(350, 1000));
            CreateSubPanel<ChessAttackEffectParamSubPanel>(EditorLanguageControl.GetStr("攻击效果参数"), new Vector2(350, 700), E_SubPanelAnchor.Right, new Vector2(350, 500), new Vector2(500, 1000));

            for (int i = 0; i < m_SubPanelList.Count; ++i)
            {
                var subPanel = m_SubPanelList[i] as ChessAttackSubPanel;
                if (subPanel != null)
                    subPanel.SetController(this);
            }

            ReLayout();
        }

        protected override void OnPreDraw()
        {
            base.OnPreDraw();

            if (controller == null || serializedObject == null || attackConfigSerializedProperty == null)
            {
                var ctrl = TinyAttackSceneController.getInstance() as TinyAttackSceneController;
                if (ctrl != null)
                {
                    Init(ctrl);
                    return;
                }

                var sceneCount = UnityEngine.SceneManagement.SceneManager.sceneCount;
                for (int i = 0; i < sceneCount; ++i)
                {
                    var scene = UnityEngine.SceneManagement.SceneManager.GetSceneAt(i);
                    var roots = scene.GetRootGameObjects();
                    for (int j = 0; j < roots.Length; ++j)
                    {
                        var root = roots[j];
                        ctrl = root.GetComponentInChildren<TinyAttackSceneController>();
                        if (ctrl != null)
                        {
                            Init(ctrl);
                            return;
                        }
                    }
                }
            }
  
        }

        protected override void OnPostDraw()
        {
            base.OnPostDraw();

            if (serializedObject != null)
            {
                serializedObject.ApplyModifiedProperties();
            }
        }
    }
}
#endif