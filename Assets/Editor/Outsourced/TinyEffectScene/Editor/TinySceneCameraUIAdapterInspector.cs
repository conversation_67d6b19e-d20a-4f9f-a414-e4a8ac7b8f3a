using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using static GameViewUtils;

namespace ZGameChess
{
    [CustomEditor(typeof(TinySceneCameraUIAdapter))]
    public class TinySceneCameraUIAdapterInspector : Editor
    {
        public GameObject prefab = null;

        private void OnEnable()
        {
            prefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art_TFT/scenes/LLReview/Review_scenes/SencePrefabs/" + GetPrefabName() + ".prefab");
        }

        private string GetPrefabName()
        {
            var adapter = target as TinySceneCameraUIAdapter;
            if (adapter != null)
                return adapter.m_prefabName;
            return string.Empty;
        }

        public override void OnInspectorGUI()
        {
            //base.OnInspectorGUI();

            var adapter = target as TinySceneCameraUIAdapter;

            GUILayout.BeginVertical("box");
            var oPrefab = EditorGUILayout.ObjectField("原始prefab", prefab, typeof(GameObject), false) as GameObject;
            if (prefab != oPrefab)
            {
                prefab = oPrefab;

                adapter.m_prefabName = prefab != null ? prefab.name : string.Empty;
            }

            if (GUILayout.Button("保存到原始Prefab"))
            {
                if (prefab == null)
                {
                    EditorUtility.DisplayDialog("提示", "请将原始prefab拖上来", "OK");
                    return;
                }

                var cameras = prefab.GetComponentsInChildren<Camera>();
                Camera camera = null;
                foreach (var item in cameras)
                {
                    if (item.name == adapter.name)
                    {
                        camera = item;
                        break;
                    }
                }

                if (camera != null)
                {
                    var prefabAdapter = camera.gameObject.TryGetComponent<TinySceneCameraUIAdapter>();
                    UnityEditorInternal.ComponentUtility.CopyComponent(adapter);
                    UnityEditorInternal.ComponentUtility.PasteComponentValues(prefabAdapter);
                    EditorUtility.SetDirty(prefabAdapter);
                    PrefabUtility.SavePrefabAsset(prefab);
                    EditorUtility.DisplayDialog("提示", "保存成功！", "OK");
                }
                else
                {
                    EditorUtility.DisplayDialog("提示", "保存失败，没找到名字叫: " + adapter.name + " 的相机！", "OK");
                }
            }
            GUILayout.EndVertical();

            GfxFramework.GfxEditorUtility.ContentsHeader("Maya中相机属性:");
            GUILayout.BeginVertical("box");
            var aa = adapter.GetArtCameraSyncLittleLegend();
            if (aa == null)
            {
                GUILayout.Label("要像查看Maya的效果，请进入游戏中");
            }
            else
            {
                var camPos = aa.LocPosTransNode;
                if (camPos != null)
                {
                    var camPosition = camPos.parent.InverseTransformPoint(adapter.transform.position);
                    var scale = camPos.parent.localScale;
                    camPosition.x *= scale.x;
                    camPosition.y *= scale.y;
                    camPosition.z *= scale.z;
                    var camPositionInvX = new Vector3(-camPosition.x, camPosition.y, camPosition.z);
                    Quaternion camRotation = Quaternion.Inverse(camPos.parent.rotation) * adapter.transform.rotation;
                    var eulerAngles = aa.RecoveryUnityRotationToMayaRotation(camRotation);

                    EditorGUILayout.Vector3Field("相机位置: (米)", camPositionInvX);
                    EditorGUILayout.Vector3Field("相机位置: (厘米)", camPositionInvX * 100);
                    EditorGUILayout.Vector3Field("相机旋转: ", eulerAngles);
                    // 实际是maya中相机的焦距属性，便于美术理解称作相机FOV
                    EditorGUILayout.FloatField("相机FOV:", aa.RecoveryFocalLength(adapter.GetCamera().fieldOfView));
                }
                else
                {
                    GUILayout.Label("找不到Cam_loc");
                }
            }

            //EditorGUILayout.Vector3Field("相机坐标: ", )
            GUILayout.EndVertical();

            GfxFramework.GfxEditorUtility.ContentsHeader("自适配算法参数:");
            GUILayout.BeginVertical("box");
            adapter.m_autoAdapter = EditorGUILayout.Toggle("是否启动自动适配: ", adapter.m_autoAdapter);
            adapter.m_adapterDistance = EditorGUILayout.Toggle("是否调整相机距离: ", adapter.m_adapterDistance);
            EditorGUILayout.HelpBox("勾上以后保证小小英雄不会超框", MessageType.Info);
            adapter.m_adapterOffset = EditorGUILayout.Vector3Field("相机偏移校正: ", adapter.m_adapterOffset);
            adapter.m_cameraFadeOutTime = EditorGUILayout.FloatField("恢复默认机位时间:(s) ", adapter.m_cameraFadeOutTime);
            adapter.m_ipadFovOffset = EditorGUILayout.FloatField("Ipad屏幕下FOV缩放偏移: ", adapter.m_ipadFovOffset);
            GUILayout.EndVertical();

            GUILayout.BeginVertical("box");
            adapter.m_useRTCustomXOffset = EditorGUILayout.Toggle("是否自定义RT模式的偏移值", adapter.m_useRTCustomXOffset);
            adapter.m_rtXOffset = EditorGUILayout.FloatField("RT模式下的左右偏移值: ", adapter.m_rtXOffset);
            GUILayout.EndVertical();

            GfxFramework.GfxEditorUtility.ContentsHeader("相机机位:");
            GUILayout.BeginVertical("box");
            if (GUILayout.Button("添加"))
            {
                adapter.m_cameraDefaultPos.Add(new TinySceneCameraUIAdapter.CameraDefaultPos());
            }

            for (int i = 0; i < adapter.m_cameraDefaultPos.Count; ++i)
            {
                var cameraInfo = adapter.m_cameraDefaultPos[i];

                bool delete = DrawCameraPosItem(adapter, cameraInfo);
                if (delete)
                {
                    adapter.m_cameraDefaultPos.RemoveAt(i);
                    --i;
                }
            }

            GUILayout.EndVertical();

            GfxFramework.GfxEditorUtility.ContentsHeader("自定义相机适配:");
            GUILayout.BeginVertical("box");

            if (GUILayout.Button("添加"))
            {
                adapter.m_cameraInfo.Add(new TinySceneCameraUIAdapter.CameraInfo());
            }

            for (int i = 0; i < adapter.m_cameraInfo.Count; ++i)
            {
                var cameraInfo = adapter.m_cameraInfo[i];

                bool delete = DrawItem(adapter, cameraInfo);
                if (delete)
                {
                    adapter.m_cameraInfo.RemoveAt(i);
                    --i;
                }
            }

            //GUILayout.Label("当前分辨率: " + Screen.width + "x" + Screen.height);

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("切换为平板"))
            {
                TkEditorScreenController.SetIpad(adapter.gameObject, () =>
                {
                    adapter.AdapterCameraPosInEditor();
                });
            }

            if (GUILayout.Button("切换为全面屏手机"))
            {
                TkEditorScreenController.SetFullScreenPhone(adapter.gameObject, () =>
                {
                    adapter.AdapterCameraPosInEditor();
                });
            }

            if (GUILayout.Button("切换为普通手机"))
            {
                TkEditorScreenController.SetNormalPhone(adapter.gameObject, () =>
                {
                    adapter.AdapterCameraPosInEditor();
                });
            }

            GUILayout.EndHorizontal();

            GUILayout.EndVertical();
        }

        private static TinySceneCameraUIAdapter.CameraInfo ms_info;

        private static void CopyCameraInfo(TinySceneCameraUIAdapter.CameraInfo info)
        {
            ms_info = info;
            Debug.Log("复制成功");
        }

        private static void PasteCameraInfo(TinySceneCameraUIAdapter.CameraInfo info)
        {
            if (ms_info != null)
            {
                info.position = ms_info.position;
                info.rotation = ms_info.rotation;
                Debug.Log("粘贴成功");
            }
            else
            {
                Debug.Log("粘贴失败");
            }
        }

        private bool DrawCameraPosItem(TinySceneCameraUIAdapter owner, TinySceneCameraUIAdapter.CameraDefaultPos info)
        {
            bool remove = false;
            GUILayout.BeginVertical("box");

            info.condition = EditorGUILayout.TextField("条件", info.condition);
            info.position = EditorGUILayout.Vector3Field("相机位置", info.position);
            info.rotation = EditorGUILayout.Vector3Field("相机角度", info.rotation);
            info.fov = EditorGUILayout.FloatField("FOV", info.fov);
            info.fadeInTime = EditorGUILayout.FloatField("进入机位时间", info.fadeInTime);
            info.fadeOutTime = EditorGUILayout.FloatField("离开机位时间", info.fadeOutTime);
            info.autoAdapter = EditorGUILayout.Toggle("是否启动自动适配: ", info.autoAdapter);

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("应用"))
            {
                var camera = owner.GetComponent<Camera>();
                camera.transform.localPosition = info.position;
                camera.transform.localRotation = Quaternion.Euler(info.rotation);
                camera.fieldOfView = info.fov;
            }

            if (GUILayout.Button("记录"))
            {
                owner.RecordCurrentCameraDefaultPosTo(info);
            }

            if (GUILayout.Button("删除"))
            {
                if (EditorUtility.DisplayDialog("提示", "是否确认删除: " + info.condition, "确定", "取消"))
                    remove = true;
            }

            GUILayout.EndHorizontal();

            GUILayout.EndVertical();

            return remove;
        }

        private bool DrawItem(TinySceneCameraUIAdapter owner, TinySceneCameraUIAdapter.CameraInfo info)
        {
            bool remove = false;
            GUILayout.BeginVertical("box");

            info.uiType = (TinySceneCameraUIAdapter.UIType)EditorGUILayout.EnumPopup("UI面板", info.uiType);
            info.condition = EditorGUILayout.TextField("条件", info.condition);
            info.screenType = (TinySceneCameraUIAdapter.ScreenType)EditorGUILayout.EnumPopup("屏幕类型", info.screenType);
            info.position = EditorGUILayout.Vector3Field("相机位置", info.position);
            info.rotation = EditorGUILayout.Vector3Field("相机角度", info.rotation);

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("应用"))
            {
                owner.AdapterCameraPos(info, false);
            }

            if (GUILayout.Button("记录"))
            {
                owner.RecordCurrentCameraInfoTo(info);
            }

            if (GUILayout.Button("删除"))
            {
                if (EditorUtility.DisplayDialog("提示", "是否确认删除: " + info.uiType, "确定", "取消"))
                    remove = true;
            }

            if (GUILayout.Button("复制"))
            {
                CopyCameraInfo(info);
            }

            if (GUILayout.Button("粘贴"))
            {
                PasteCameraInfo(info);
            }

            GUILayout.EndHorizontal();

            GUILayout.EndVertical();

            return remove;
        }
    }
}
