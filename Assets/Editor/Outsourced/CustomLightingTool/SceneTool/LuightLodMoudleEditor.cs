using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEditor;
using UnityEngine;

namespace PBRTools
{
    public class LuightLodMoudleEditor
    {
        public static void Draw(LightLodModule module, SerializedProperty moduleProperty, GameObject root)
        {
            EditorGUI.BeginChangeCheck();
            module.enable = EditorGUILayout.Toggle("开启", module.enable);
            if (EditorGUI.EndChangeCheck())
            {
                EditorUtility.SetDirty(root);
            }

            EditorGUILayout.BeginVertical("box");
            for (int i = 0; i < module.m_lightLodList.Count; ++i)
            {
                var lod = module.m_lightLodList[i];

                EditorGUILayout.BeginHorizontal();

                EditorGUILayout.ObjectField(lod.light, typeof(Light), false);
                EditorGUI.BeginChangeCheck();
                lod.lod = (EDevicePower)EditorGUILayout.EnumPopup(lod.lod);
                if (EditorGUI.EndChangeCheck())
                {
                    EditorUtility.SetDirty(root);
                }

                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndVertical();

            if (GUILayout.Button("收集"))
            {
                module.ClearEmptyLight();

                var lights = GameObject.FindObjectsOfType<Light>();// root.GetComponentsInChildren<Light>();
                for (int i = 0; i < lights.Length; ++i)
                {
                    if (lights[i].lightmapBakeType == LightmapBakeType.Baked)
                        continue;
                    if (!module.HasLight(lights[i]))
                        module.m_lightLodList.Add(new LightLodModule.LightLod() { light = lights[i], lod = EDevicePower.EDP_Low });
                }

                EditorUtility.SetDirty(root);
            }

            if (GUILayout.Button("清理"))
            {
                module.m_lightLodList.Clear();
                EditorUtility.SetDirty(root);
            }

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("预览" + DeviceAssessment.TranslateLODEnumToChinese(EDevicePower.EDP_Ultra) + "画质"))
            {
                module.Apply(EDevicePower.EDP_Ultra);
            }
            if (GUILayout.Button("预览" + DeviceAssessment.TranslateLODEnumToChinese(EDevicePower.EDP_High) + "画质"))
            {
                module.Apply(EDevicePower.EDP_High);
            }
            if (GUILayout.Button("预览" + DeviceAssessment.TranslateLODEnumToChinese(EDevicePower.EDP_Middle) + "画质"))
            {
                module.Apply(EDevicePower.EDP_Middle);
            }
            if (GUILayout.Button("预览" + DeviceAssessment.TranslateLODEnumToChinese(EDevicePower.EDP_Low) + "画质"))
            {
                module.Apply(EDevicePower.EDP_Low);
            }
            EditorGUILayout.EndHorizontal();
        }
    }
}
