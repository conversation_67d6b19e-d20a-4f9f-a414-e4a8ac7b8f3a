using System.Collections;
using System.Collections.Generic;
using UnityEngine;


namespace PBRTools
{
    public class CubeBuffer
    {
        public enum FilterMode
        {
            NEAREST,
            BILINEAR,
            BICUBIC
        };

        public delegate void SampleFunction(ref Color dst, float u, float v, int face);
        public SampleFunction sample = null;

        private FilterMode _filterMode;
        public FilterMode filterMode
        {
            set
            {
                _filterMode = value;
                switch(_filterMode)
                {
                    case FilterMode.NEAREST:
                        sample = sampleNearest;
                        break;

                    case FilterMode.BILINEAR:
                        sample = sampleBilinear;
                        break;

                    case FilterMode.BICUBIC:
                        sample = sampleBicubic;
                        break;
                }
            }

            get
            {
                return _filterMode;
            }
        }

        public CubeBuffer()
        {
            filterMode = FilterMode.BILINEAR;
            clear();
        }

        private class CubeEdge
        {
            public int other;
            public bool flipped;
            public bool swizzled;
            public bool mirrored;
            public bool minEdge;

            public CubeEdge(int _other, bool _flipped, bool _swizzled)
            {
                other = _other;
                flipped = _flipped;
                swizzled = _swizzled;
                mirrored = false;
                minEdge = false;
            }

            public CubeEdge(int _other, bool _flipped, bool _swizzled, bool _mirrorred)
            {
                other = _other;
                flipped = _flipped;
                swizzled = _swizzled;
                mirrored = _mirrorred;
                minEdge = false;
            }


            //project u, v, face to new u, v, face for a neighbour face
            public void transmogrify(ref int u, ref int v, ref int face, int faceSize)
            {
                bool changed = false;
                if( minEdge && u < 0)
                {
                    u = faceSize + u;
                    changed = true;
                }
                else if(!minEdge && u >= faceSize)
                {
                    u %= faceSize;
                    changed = true;
                }

                if(changed)
                {
                    if (mirrored)
                        u = faceSize - u - 1;

                    if (flipped)
                        v = faceSize - v - 1;

                    if(swizzled)
                    {
                        int tmp = v;
                        v = u;
                        u = tmp;
                    }

                    face = other;
                }
            }
        }


        public int faceSize = 0;
        public Color[] pixels = null;

        public int width {

            get
            {
                return faceSize;
            }
        }

        public int height
        {
            get
            {
                return faceSize * 6;
            }
        }

        public void clear()
        {
            pixels = null;
            faceSize = 0;
        }

        public bool empty()
        {
            if (pixels == null)
                return true;
            if (pixels.Length == 0)
                return true;

            return false;
        }

        public void resize(int newFaceSize)
        {
            if (newFaceSize == faceSize)
                return;

            faceSize = newFaceSize;
            pixels = null;
            pixels = new Color[faceSize * faceSize * 6];
            Util.clearTo(ref pixels, Color.black);
        }

        public static void pixelCopy(ref Color[] dst, int dst_offset, Color[] src, int src_offset, int count)
        {
            for (int i = 0; i < count; i++)
                dst[dst_offset + i] = src[src_offset + i];
        }



        public static void encode(ref Color[] dst, Color[] src, ColorMode outMode, bool useGamma)
        {
            if (outMode == ColorMode.RGBM8)
            {
                for (int i = 0; i < src.Length; i++)
                {
                    RGB.toRGBM(ref dst[i], src[i], useGamma);
                }
            }
            else
            {
                if (useGamma)
                    Util.applyGamma(ref dst, src, Gamma.toSRGB);
                else
                    pixelCopy(ref dst, 0, src, 0, src.Length);
            }
        }

        public static void decode(ref Color[] dst, Color[] src, ColorMode inMode, bool useGamma)
        {
            if(inMode == ColorMode.RGBM8)
            {
                for (int i = 0; i < src.Length; i++)
                    RGB.fromRGBM(ref dst[i], src[i], useGamma);
            }
            else
            {
                if (useGamma)
                {
                    Util.applyGamma(ref dst, src, Gamma.toLinear);
                }
                else
                {
                    pixelCopy(ref dst, 0, src, 0, src.Length);
                }

                Util.clearAlpha(ref dst);
            }
        }


        //read buffer from the input cube
        public void fromCube(Cubemap cube, int mip, ColorMode cubeColorMode, bool useGamma)
        {
            int mipSize = cube.width >> mip;
            if (pixels == null || faceSize != mipSize)
                resize(mipSize);

            for(int face = 0; face < 6; face++)
            {
                Color[] src = cube.GetPixels((CubemapFace)face, mip);
                pixelCopy(ref pixels, face * faceSize * faceSize, src, 0, src.Length);
            }
            decode(ref pixels, pixels, cubeColorMode, useGamma);
        }

        //read buffer from the input panorama
        public void fromPanoTexture(Texture2D tex, int _faceSize, ColorMode texColorMode, bool useGamma)
        {
            resize(_faceSize);
            for(int face = 0; face < 6; face++)
            {
                for(int y = 0; y < faceSize; y++)
                {
                    for(int x = 0; x < faceSize; x++)
                    {
                        float u = 0f;
                        float v = 0f;
                        Util.cubeToLatLongLookup(ref u, ref v, face, x, y, faceSize);

                        float theClamps = 1f / (float)faceSize;
                        v = Mathf.Clamp(v, theClamps, 1f - theClamps);
                        pixels[face * faceSize * faceSize + y * faceSize + x] = tex.GetPixelBilinear(u, v);
                    }
                }
            }

            decode(ref pixels, pixels, texColorMode, useGamma);
        }




        //sample functions
        private static CubeEdge[] _leftEdges = null;
        private static CubeEdge[] _rightEdges = null;

        private static CubeEdge[] _upEdges = null;
        private static CubeEdge[] _downEdges = null;

        private static void linkEdges()
        {
            if (_leftEdges == null)
            {
                //Allocate edge mappings between faces, with flip and rotate flags for some weird cases
                _leftEdges = new CubeEdge[6];
                _leftEdges[(int)CubemapFace.NegativeX] = new CubeEdge((int)CubemapFace.NegativeZ, false, false);
                _leftEdges[(int)CubemapFace.PositiveX] = new CubeEdge((int)CubemapFace.PositiveZ, false, false);
                _leftEdges[(int)CubemapFace.NegativeY] = new CubeEdge((int)CubemapFace.NegativeX, true, true);
                _leftEdges[(int)CubemapFace.PositiveY] = new CubeEdge((int)CubemapFace.NegativeX, false, true, true);
                _leftEdges[(int)CubemapFace.NegativeZ] = new CubeEdge((int)CubemapFace.PositiveX, false, false);
                _leftEdges[(int)CubemapFace.PositiveZ] = new CubeEdge((int)CubemapFace.NegativeX, false, false);

                _rightEdges = new CubeEdge[6];
                _rightEdges[(int)CubemapFace.NegativeX] = new CubeEdge((int)CubemapFace.PositiveZ, false, false);
                _rightEdges[(int)CubemapFace.PositiveX] = new CubeEdge((int)CubemapFace.NegativeZ, false, false);
                _rightEdges[(int)CubemapFace.NegativeY] = new CubeEdge((int)CubemapFace.PositiveX, false, true, true);
                _rightEdges[(int)CubemapFace.PositiveY] = new CubeEdge((int)CubemapFace.PositiveX, true, true);
                _rightEdges[(int)CubemapFace.NegativeZ] = new CubeEdge((int)CubemapFace.NegativeX, false, false);
                _rightEdges[(int)CubemapFace.PositiveZ] = new CubeEdge((int)CubemapFace.PositiveX, false, false);

                _upEdges = new CubeEdge[6];
                _upEdges[(int)CubemapFace.NegativeX] = new CubeEdge((int)CubemapFace.PositiveY, false, true, true);
                _upEdges[(int)CubemapFace.PositiveX] = new CubeEdge((int)CubemapFace.PositiveY, true, true);
                _upEdges[(int)CubemapFace.NegativeY] = new CubeEdge((int)CubemapFace.PositiveZ, false, false);
                _upEdges[(int)CubemapFace.PositiveY] = new CubeEdge((int)CubemapFace.NegativeZ, true, false, true);
                _upEdges[(int)CubemapFace.NegativeZ] = new CubeEdge((int)CubemapFace.PositiveY, true, false, true);
                _upEdges[(int)CubemapFace.PositiveZ] = new CubeEdge((int)CubemapFace.PositiveY, false, false);

                _downEdges = new CubeEdge[6];
                _downEdges[(int)CubemapFace.NegativeX] = new CubeEdge((int)CubemapFace.NegativeY, true, true);
                _downEdges[(int)CubemapFace.PositiveX] = new CubeEdge((int)CubemapFace.NegativeY, false, true, true);
                _downEdges[(int)CubemapFace.NegativeY] = new CubeEdge((int)CubemapFace.NegativeZ, true, false, true);
                _downEdges[(int)CubemapFace.PositiveY] = new CubeEdge((int)CubemapFace.PositiveZ, false, false);
                _downEdges[(int)CubemapFace.NegativeZ] = new CubeEdge((int)CubemapFace.NegativeY, true, false, true);
                _downEdges[(int)CubemapFace.PositiveZ] = new CubeEdge((int)CubemapFace.NegativeY, false, false);

                for (int i = 0; i < 6; ++i)
                {
                    _leftEdges[i].minEdge = _upEdges[i].minEdge = true; // coord < 0 edges
                    _rightEdges[i].minEdge = _downEdges[i].minEdge = false; // coord >= faceSize edges
                }

                /*    ___
				     |+y |
				  ___|___|___ ___
				 |-x |+z |+x |-z |
				 |___|___|___|___|
				     |-y |
				     |___|
				 */
            }
        }

        public int toIndex(int face, int x, int y)
        {
            x = Mathf.Clamp(x, 0, faceSize - 1);
            y = Mathf.Clamp(y, 0, faceSize - 1);
            return faceSize * faceSize * face + faceSize * y + x;
        }

        public int toIndexLinked(int face, int u, int v)
        {
            linkEdges();
            int currFace = face;
            _leftEdges[currFace].transmogrify(ref u, ref v, ref currFace, faceSize);
            _upEdges[currFace].transmogrify(ref u, ref v, ref currFace, faceSize);
            _rightEdges[currFace].transmogrify(ref u, ref v, ref currFace, faceSize);
            _downEdges[currFace].transmogrify(ref u, ref v, ref currFace, faceSize);

            u = Mathf.Clamp(u, 0, faceSize - 1);
            v = Mathf.Clamp(v, 0, faceSize - 1);

            return toIndex(currFace, u, v);
        }


        public void sampleNearest(ref Color dst, float u, float v, int face)
        {
            int ui = Mathf.FloorToInt(faceSize * u);
            int vi = Mathf.FloorToInt(faceSize * v);
            dst = pixels[faceSize * faceSize * face + faceSize * vi + ui];
        }

        public void sampleBilinear(ref Color dst, float u, float v, int face)
        {
            u = faceSize * u + 0.5f;
            int ui = Mathf.FloorToInt(u) - 1;
            u = Mathf.Repeat(u, 1f);

            v = faceSize * v + 0.5f;
            int vi = Mathf.FloorToInt(v) - 1;
            v = Mathf.Repeat(v, 1f);

            int i00 = toIndexLinked(face, ui, vi);
            int i10 = toIndexLinked(face, ui + 1, vi);
            int i11 = toIndexLinked(face, ui + 1, vi + 1);
            int i01 = toIndexLinked(face, ui, vi + 1);

            Color c0, c1;
            c0 = Color.Lerp(pixels[i00], pixels[i10], u);
            c1 = Color.Lerp(pixels[i01], pixels[i11], u);
            dst = Color.Lerp(c0, c1, v);
        }

        private static Color[,] cubicKernel = new Color[4, 4];
        public void sampleBicubic(ref Color dst, float u, float v, int face)
        {
            u = faceSize * u + 0.5f;
            int ui = Mathf.FloorToInt(u) - 1;
            u = Mathf.Repeat(u, 1f);

            v = faceSize * v + 0.5f;
            int vi = Mathf.FloorToInt(v) - 1;
            v = Mathf.Repeat(v, 1f);

            for (int x = 0; x < 4; ++x)
            {
                for (int y = 0; y < 4; ++y)
                {
                    int index = toIndexLinked(face, ui - 1 + x, vi - 1 + y);
                    cubicKernel[x, y] = pixels[index];
                }
            }

            float weight = 0.85f;
            float anchor = 0.333f;
            Color c0, c1, w0, w1, l0, l1, l2;
            for (int y = 0; y < 4; ++y)
            {
                w0 = cubicKernel[0, y];
                c0 = cubicKernel[1, y];
                c1 = cubicKernel[2, y];
                w1 = cubicKernel[3, y];
                w0 = Color.Lerp(c0, w0, weight);
                w1 = Color.Lerp(c1, w1, weight);
                w0 = c0 + anchor * (c0 - w0);
                w1 = c1 + anchor * (c1 - w1);

                l0 = Color.Lerp(c0, w0, u);
                l1 = Color.Lerp(w0, w1, u);
                l2 = Color.Lerp(w1, c1, u);

                l0 = Color.Lerp(l0, l1, u);
                l1 = Color.Lerp(l1, l2, u);

                cubicKernel[0, y] = Color.Lerp(l0, l1, u);
            }

            w0 = cubicKernel[0, 0];
            c0 = cubicKernel[0, 1];
            c1 = cubicKernel[0, 2];
            w1 = cubicKernel[0, 3];
            w0 = Color.Lerp(c0, w0, weight);
            w1 = Color.Lerp(c1, w1, weight);
            w0 = c0 + anchor * (c0 - w0);
            w1 = c1 + anchor * (c1 - w1);

            l0 = Color.Lerp(c0, w0, v);
            l1 = Color.Lerp(w0, w1, v);
            l2 = Color.Lerp(w1, c1, v);

            l0 = Color.Lerp(l0, l1, v);
            l1 = Color.Lerp(l1, l2, v);

            dst = Color.Lerp(l0, l1, v);
        }



        //sample a cube face to another face with a different size
        public void resampleFace(ref Color[] dst, int face, int newSize, bool flipY)
        {
            resampleFace(ref dst, 0, face, newSize, flipY);
        }

        public void resampleFace(ref Color[] dst, int dstOffset, int face, int newSize, bool flipY)
        {
            if(newSize == faceSize)
            {
                //copy directly
                pixelCopy(ref dst, dstOffset, pixels, face * faceSize * faceSize, faceSize * faceSize);
                return;
            }

            float inv_newSize = 1f / (newSize);
            if (flipY)
            {
                for (int y = 0; y < newSize; y++)
                {
                    float v = 1f - ((float)y + 0.5f) * inv_newSize;
                    for (int x = 0; x < newSize; x++)
                    {
                        float u = ((float)x + 0.5f) * inv_newSize;
                        int i = y * newSize + x + dstOffset;
                        sample(ref dst[i], u, v, face);
                    }
                }
            }
            else
            {
                for (int y = 0; y < newSize; y++)
                {
                    float v = ((float)y + 0.5f) * inv_newSize;
                    for (int x = 0; x < newSize; x++)
                    {
                        float u = ((float)x + 0.5f) * inv_newSize;
                        int i = y * newSize + x + dstOffset;
                        sample(ref dst[i], u, v, face);
                    }
                }
            }
        }



        public void resampleToBuffer(ref CubeBuffer dst)
        {
            int squareSize = dst.faceSize * dst.faceSize;
            for(int face = 0; face < 6; face++)
            {
                resampleFace(ref dst.pixels, face * squareSize, face, dst.faceSize, false);
            }
        }

        public void resampleToCube(ref Cubemap cube, int mip, ColorMode colorMode, bool useGamma, float exposure)
        {
            int mipSize = cube.width >> mip;
            int mipLength = mipSize * mipSize * 6;
            Color[] mipPixels = new Color[mipLength];

            for(int face = 0; face < 6; face++)
            {
                resampleFace(ref mipPixels, face, mipSize, false);
                if (exposure != 1f)
                    applyExposure(ref mipPixels, exposure);
                encode(ref mipPixels, mipPixels, colorMode, useGamma);
                cube.SetPixels(mipPixels, (CubemapFace)face, mip);
            }
            cube.Apply(false);
        }

        public static void applyExposure(ref Color[] pixels, float mult)
        {
            //exposure paramters always set in gamma space 
            mult *= Gamma.toLinear;
            for (int i = 0; i < pixels.Length; ++i)
            {
                pixels[i].r *= mult;
                pixels[i].g *= mult;
                pixels[i].b *= mult;
                //NOTE: alpha is not touched because it is treated as a multiplier
                //even in non-RGBM images. It must remain 1f when writing out LDR
                //images.
            }
        }

        public void applyExposure(float multiplier)
        {
            //exposure paramters always set in gamma space
            multiplier *= Gamma.toLinear;
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i].r *= multiplier;
                pixels[i].g *= multiplier;
                pixels[i].b *= multiplier;
                //NOTE: alpha is not touched because it is treated as a multiplier
                //even in non-RGBM images. It must remain 1f when writing out LDR
                //images.
            }
        }
    }
}
