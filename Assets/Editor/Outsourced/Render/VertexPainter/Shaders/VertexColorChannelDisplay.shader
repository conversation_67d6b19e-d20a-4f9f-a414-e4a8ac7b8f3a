Shader "Hidden/TATools/VertexColorChannelDisplay"
{
    Properties
    {
        _Channel ("Display Channel (0=All, 1=R, 2=G, 3=B, 4=A)", Int) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float4 color : COLOR;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float4 color : COLOR;
            };

            int _Channel;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.color = v.color;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = i.color;
                
                // 根据选择的通道显示不同的颜色
                if (_Channel == 1) // R
                    return fixed4(col.r, 0, 0, 1);
                else if (_Channel == 2) // G
                    return fixed4(0, col.g, 0, 1);
                else if (_Channel == 3) // B
                    return fixed4(0, 0, col.b, 1);
                else if (_Channel == 4) // A
                    return fixed4(col.a, col.a, col.a, 1);
                else // All
                    return col;
            }
            ENDCG
        }
    }
}
