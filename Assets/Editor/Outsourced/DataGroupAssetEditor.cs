
using TKPlugins;
#if ACGGAME_CLIENT
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEditorInternal;
using System;
using System.IO;
using Newtonsoft.Json;

namespace TKPlugins_Extend
{

    [CustomEditor(typeof(DataGroupAsset), true)]
    public class DataGroupAssetEditor : Editor
    {
        public class GroupShowInfo
        {
            public string groupName;
            public ReorderableList paramList;
            public bool isShow = false;
        }
        
        public static int fps = 30;
        
        private static float pro = 30;
        private static int gap = 4;

        private DataGroupAsset m_dataAsset;
        private List<GroupShowInfo> m_groupList;
        private bool m_isChanged = false;

        protected void OnEnable()
        {
            if (serializedObject.targetObject == null)
                return;

            m_dataAsset = (DataGroupAsset)serializedObject.targetObject;

            if (m_dataAsset == null)
                return;

            if (m_dataAsset.dataGroupList == null)
            {
                m_dataAsset.dataGroupList = new List<DataGroupAsset.DataGroup>();
            }

            DrawAllList();
        }

        private void OnDisable()
        {
            if(m_isChanged)
            {
                Save(m_dataAsset);
                m_isChanged = false;
            }

        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            GUILayout.BeginVertical();
            
            for (int i = 0; i < m_groupList.Count; i++)
            {
                GroupShowInfo info = m_groupList[i];
                info.isShow = EditorGUILayout.Foldout(info.isShow, info.groupName);
                if (info.isShow)
                {
                    EditorGUI.BeginChangeCheck();
                    info.paramList.DoLayoutList();
                    if(EditorGUI.EndChangeCheck())
                    {
                        m_isChanged = true;
                    }
                }
            }

            if (GUILayout.Button("保存参数"))
            {
                Save(m_dataAsset);
                m_isChanged = false;
            }

            GUILayout.EndVertical();

            serializedObject.ApplyModifiedProperties();
        }

        public static void Save(DataGroupAsset dataGroupAsset)
        {
            EditorUtility.SetDirty(dataGroupAsset);
            AssetDatabase.SaveAssets();
            ConvertToJson(dataGroupAsset);
            
            if(Application.isPlaying)
                ZGameChess.GlobalConfig.Instance.ReloadInEditor();
        }

        
        [MenuItem("Assets/小小英雄/导出Config->JSON")]
        public static void ExportTinyConfig()
        {
            string guid = Selection.assetGUIDs[0];
            string path = AssetDatabase.GUIDToAssetPath(guid);
            string dir = Path.GetDirectoryName(path);

            string[] allFiles = Directory.GetFiles(dir);
            foreach (string file in allFiles)
            {
                if (file.EndsWith(".asset"))
                {
                    DataGroupAsset dataAsset = AssetDatabase.LoadAssetAtPath<DataGroupAsset>(file);
                    ConvertToJson(dataAsset);
                }
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        
#region ChessPlayerLogicConfig 这个没办法，暂时这样，和这个深度结合了，下面有更好的实现

        public static void ConvertToJson(DataGroupAsset dataAsset)
        {
            string filePath = AssetDatabase.GetAssetPath(dataAsset);

            float totalTime = 0f;
            float totalDis = 0f;
            AnimationCurve curve = null;
            float speed = 0f;

            ChessPlayerLogicConfig logicConfig = new ChessPlayerLogicConfig();

#region 走路

            List<DataGroupAsset.DataItem> dataList = dataAsset.GetDataGroup("Run").dataList; 
            logicConfig.walkSpeed = dataAsset.GetVal(dataList, "run_Speed", speed);

            curve = dataAsset.GetVal(dataList, "run_start_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.walkStartDis = totalDis;
            logicConfig.walkStart = Analyse(totalTime, totalDis, curve);

            curve = dataAsset.GetVal(dataList, "run_stop_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.walkStopDis = totalDis;
            logicConfig.walkStop = Analyse(totalTime, totalDis, curve);

            curve = dataAsset.GetVal(dataList, "run_stop_02_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.walk02StopDis = totalDis;
            logicConfig.walk02Stop = Analyse(totalTime, totalDis, curve);

            logicConfig.walkStopPre = dataAsset.GetVal(dataList, "run_stop_Pre", 0);
            logicConfig.walk02StopPre = dataAsset.GetVal(dataList, "run_stop_02_Pre", 0);
            logicConfig.totalWalkPre = logicConfig.walkStopPre + logicConfig.walk02StopPre;
#endregion

#region 跑步

            dataList = dataAsset.GetDataGroup("RunFast").dataList; 
            logicConfig.runSpeed = dataAsset.GetVal(dataList, "run_fast_Speed", speed);

            curve = dataAsset.GetVal(dataList, "run_fast_start_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.runStartDis = totalDis;
            logicConfig.runStart = Analyse(totalTime, totalDis, curve); 

            curve = dataAsset.GetVal(dataList, "run_fast_stop_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.runStopDis = totalDis;
            logicConfig.runStop = Analyse(totalTime, totalDis, curve);
            
            curve = dataAsset.GetVal(dataList, "run_fast_stop_02_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.run02StopDis = totalDis;
            logicConfig.run02Stop = Analyse(totalTime, totalDis, curve);

            logicConfig.runStopPre = dataAsset.GetVal(dataList, "run_fast_stop_Pre", 0);
            logicConfig.run02StopPre = dataAsset.GetVal(dataList, "run_fast_stop_02_Pre", 0);
            logicConfig.totalRunPre = logicConfig.runStopPre + logicConfig.run02StopPre;
#endregion

#region 冲刺

            dataList = dataAsset.GetDataGroup("Rush").dataList;
            curve = dataAsset.GetVal(dataList, "rush_Curve", curve);
            GetCurveData(curve, ref totalTime, ref totalDis);

            logicConfig.rushDis = totalDis;
            logicConfig.rush = Analyse(totalTime, totalDis, curve);

#endregion

#region Click

            DataGroupAsset.DataGroup dataGroup = dataAsset.GetDataGroup("Click");
            if (dataGroup != null)
            {
                AnimationCurve defaultCurve = new AnimationCurve();
                dataList = dataGroup.dataList;
                for (int i = 0; i < dataList.Count; i++)
                {
                    string animName = "click_0" + (i + 1);
                    string curveName = animName + "_Curve";
                    curve = dataAsset.GetVal(dataList, curveName, defaultCurve);
                    GetCurveData(curve, ref totalTime, ref totalDis);
                    if (totalDis > 0 && !Mathf.Approximately(totalDis, 0))
                    {
                        ChessPlayerLogicCurve logicCurve = new ChessPlayerLogicCurve();
                        logicCurve.id = i;
                        logicCurve.dis = totalDis;
                        logicCurve.curve = Analyse(totalTime, totalDis, curve);
                        logicConfig.rootMotions.Add(logicCurve);
                    }
                }
            }


#endregion

#region 蓄力

            int castFrame = 0;
            string castAbName = "";
            dataList = dataAsset.GetDataGroup("Cast").dataList;
            castFrame = dataAsset.GetVal(dataList, "CastFrame", castFrame);
            castAbName = dataAsset.GetVal(dataList, "CastAbName", castAbName);

            logicConfig.CastFrame = castFrame;
            logicConfig.CastAbName = castAbName;
            
#endregion

#region 受击相关配置

            int BodyScaleChangePlan = 0;
            dataList = dataAsset.GetDataGroup("BeatedCfg").dataList;
            BodyScaleChangePlan = dataAsset.GetVal(dataList, "BodyScaleChangePlan", BodyScaleChangePlan);

            logicConfig.BodyScaleChangePlan = BodyScaleChangePlan;

            #endregion

            WriteJson(filePath, logicConfig);
        }

        private static void GetCurveData(AnimationCurve curve, ref float time, ref float dis)
        {
            Keyframe[] keys = curve.keys;
            int len = keys.Length;

            Keyframe lastFrame = keys[len - 1];

            time = lastFrame.time;
            dis = lastFrame.value;
        }


        private static List<float> Analyse(float totalTime, float totalDis, AnimationCurve curve)
        {
            List<float> disList = new List<float>();
            if (totalTime > 0 && totalDis > 0)
            {
                int totalCount = Mathf.FloorToInt(totalTime * fps);
                float offset = totalTime * fps - totalCount;
                for (int i = 1; i <= totalCount; i++)
                {
                    float dis = curve.Evaluate((i + offset) / fps);
                    disList.Add(dis);
                }
            }
            return disList;
        }
#endregion

#region 转为json
        private static void WriteJson(string path, object data)
        {
            string jsonString = JsonConvert.SerializeObject(data);

            string dir = Path.GetDirectoryName(path);
            string fileName = Path.GetFileNameWithoutExtension(path);

            string file = dir + Path.DirectorySeparatorChar + fileName + "_logic.json";
            File.WriteAllText(file, jsonString);
            AssetDatabase.Refresh();
        }
#endregion

#region 转化为逻辑参数

        /*
        private void ConvertToLogicFile()
        {
            string filePath = AssetDatabase.GetAssetPath(m_dataAsset);
            LogicDataAsset logicDataAsset = ConvertToLogicDataAsset(m_dataAsset);
            WriteJson(filePath, logicDataAsset);
        }

        private LogicDataAsset ConvertToLogicDataAsset(DataGroupAsset dataAsset)
        {
            LogicDataAsset logicConfig = new LogicDataAsset();
            foreach(DataGroupAsset.DataGroup dataItem in dataAsset.dataGroupList)
            {
                LogicDataAsset.LogicDataItem logicDataItem = ConvertToLogicDataItem(dataItem);
                logicConfig.m_dataList.Add(logicDataItem);
            }
            return logicConfig;
        }

        private LogicDataAsset.LogicDataItem ConvertToLogicDataItem(DataAsset.DataItem dataItem)
        {
            LogicDataAsset.LogicDataItem logicData = new LogicDataAsset.LogicDataItem();
            logicData.name = dataItem.name;
            logicData.intVal = dataItem.intVal;
            logicData.floatVal = dataItem.floatVal;
            logicData.strVal = dataItem.strVal;
            logicData.type = dataItem.type;
            logicData.minMaxVal = dataItem.minMaxVal;
            logicData.curve = AnalyseCurve(dataItem.curve);
            return logicData;
        }

        private List<float> AnalyseCurve(AnimationCurve curve)
        {
            List<float> disList = new List<float>();
            if(curve != null && curve.keys.Length > 0)
            {
                float totalTime = FindTotalTime(curve.keys);
                int totalCount = Mathf.FloorToInt(totalTime * fps);
                float offset = totalTime * fps - totalCount;
                for (int i = 1; i <= totalCount; i++)
                {
                    float dis = curve.Evaluate(i + offset);
                    disList.Add(dis);
                }
            }
            return disList;
        }

        private float FindTotalTime(Keyframe[] keys)
        {
            float minTime = keys[0].time;
            float maxTime = keys[keys.Length - 1].time;
            return maxTime - minTime;
        }
        */

#endregion

#region item操作

/*
private void CopyDataItem(object data)
{
    DataAsset.DataItem item = data as DataAsset.DataItem;
    if(item != null)
        CopyManager.Copy<DataAsset.DataItem>(item);
}

private void PasteDataItem(object data)
{
    DataAsset.DataItem dst = data as DataAsset.DataItem;
    if (dst != null)
    {
        DataAsset.DataItem src = CopyManager.Paste<DataAsset.DataItem>();
        src.CopyTo(dst);
    }
    else
    {
        DataAsset.DataItem item = CopyManager.Paste<DataAsset.DataItem>();
        m_dataAsset.dataList.Add(item);
    }
    EditorUtility.SetDirty(m_dataAsset);
    AssetDatabase.SaveAssets();
}
*/
#endregion

#region 点击操作
        /*
        private void OnHeadRightClick(object data)
        {
            GenericMenu gm = new GenericMenu();
            if (CopyManager.isCopy<DataAsset.DataItem>())
            {
                gm.AddItem(new GUIContent("Paste"), false, PasteDataItem, data);
            }
            else
            {
                gm.AddDisabledItem(new GUIContent("Paste"));
            }
            gm.ShowAsContext();
            
        }

        private void OnDataTimeRightClick(object data)
        {
            GenericMenu gm = new GenericMenu();
            gm.AddItem(new GUIContent("Copy"), false, CopyDataItem, data);
            if (CopyManager.isCopy<DataAsset.DataItem>())
            {
                gm.AddItem(new GUIContent("Paste"), false, PasteDataItem, data);
            }
            else
            {
                gm.AddDisabledItem(new GUIContent("Paste"));
            }
            gm.ShowAsContext();
        }

        private void RightClick(Rect rect, int index, object data, Action<object> rightClick)
        {
            int itemControlID = index + 20000000;

            Event evt = Event.current;

            switch (evt.GetTypeForControl(itemControlID))
            {
                case EventType.MouseDown:
                    if (Event.current.button == 1 && rect.Contains(Event.current.mousePosition))
                    {
                        GUIUtility.hotControl = itemControlID;
                        if (rightClick != null)
                            rightClick(data);
                        Event.current.Use();
                    }
                    break;
                case EventType.MouseUp:
                    if (GUIUtility.hotControl == itemControlID)
                    {
                        GUIUtility.hotControl = 0;
                        Event.current.Use();
                    }
                    break;
                default:
                    break;
            }
        }
        */
#endregion

#region 绘制

        private void DrawAllList()
        {
            m_groupList = new List<GroupShowInfo>();
            foreach (DataGroupAsset.DataGroup dataGroup in m_dataAsset.dataGroupList)
            {
                GroupShowInfo groupShowInfo = new GroupShowInfo();
                groupShowInfo.groupName = dataGroup.groupName;
                groupShowInfo.paramList = DrawList(dataGroup);
                m_groupList.Add(groupShowInfo);
            }
        }

        private UnityEditorInternal.ReorderableList DrawList(DataGroupAsset.DataGroup dataGroup)
        {
            UnityEditorInternal.ReorderableList paramList;
            
            if(dataGroup.groupName == "AnimationList")
            {
                paramList = new UnityEditorInternal.ReorderableList(dataGroup.dataList, typeof(DataGroupAsset.DataItem), false, true, false, false);
            }
            else
            {
                paramList = new UnityEditorInternal.ReorderableList(dataGroup.dataList, typeof(DataGroupAsset.DataItem), true, true, true, true);
            }
            
            paramList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
            {

                DataGroupAsset.DataItem bp = (DataGroupAsset.DataItem)paramList.list[index];
                DrawDataItem(m_dataAsset, bp, rect);
            };
            
            paramList.onAddCallback = delegate (UnityEditorInternal.ReorderableList list)
            {

                GenericMenu gm = new GenericMenu();
                foreach (DataTypeEnum e in Enum.GetValues(typeof(DataTypeEnum)))
                {
                    gm.AddItem(new GUIContent(e.ToString()), false, delegate(object data)
                    {
                        DataTypeEnum typeEnum = (DataTypeEnum)data;
                        DataGroupAsset.DataItem dataItem = new DataGroupAsset.DataItem();
                        dataItem.type = typeEnum;
                        dataGroup.dataList.Add(dataItem);
                    }, e);
                }
                gm.ShowAsContext();
            };
            
            paramList.onRemoveCallback = delegate (UnityEditorInternal.ReorderableList list)
            {
                if (list.index < 0)
                    return;

                dataGroup.dataList.RemoveAt(list.index);
            };
            
            paramList.drawHeaderCallback = (Rect rect) =>
            {
                GUI.Label(rect, "参数列表");
            };
            
            paramList.elementHeightCallback  = delegate(int index)
            {
                if (dataGroup.dataList.Count > index)
                {
                    DataGroupAsset.DataItem dataItem = dataGroup.dataList[index];
                    if (dataItem.type == DataTypeEnum.AnimationCurve)
                    {
                        return paramList.elementHeight * 3;
                    }
                }
                return paramList.elementHeight;
            };
            
            return paramList;
        }

        public static void DrawDataItem(DataGroupAsset dataGroupAsset, DataGroupAsset.DataItem dataItem, Rect realRect, bool onlyShowValue = false)
        {
            Rect paramRect = realRect;
            paramRect.height = EditorGUIUtility.singleLineHeight;
            paramRect.y += 2;
            float left = paramRect.width - 30 - 20 - 30 - 5 * gap;
            float each = left / 4;

            switch (dataItem.type)
            {
                case DataTypeEnum.Int:
                    DrawRawValue(dataItem, paramRect, each, onlyShowValue);
                    break;
                case DataTypeEnum.Float:
                    DrawRawValue(dataItem, paramRect, each, onlyShowValue);
                    break;
                case DataTypeEnum.String:
                    DrawRawValue(dataItem, paramRect, each, onlyShowValue);
                    break;   
                case DataTypeEnum.Bool:
                    DrawRawValue(dataItem, paramRect, each, onlyShowValue);
                    break;
                case DataTypeEnum.Color:
                    DrawColorValue(dataItem, paramRect, each, onlyShowValue);
                    break;    
                case DataTypeEnum.Object:
                    DrawObjectValue<UnityEngine.Object>(dataItem, paramRect, each, onlyShowValue);
                    break;    
                case DataTypeEnum.Sprite:
                    DrawObjectValue<Sprite>(dataItem, paramRect, each, onlyShowValue);
                    break;   
                case DataTypeEnum.Texture:
                    DrawObjectValue<Texture>(dataItem, paramRect, each, onlyShowValue);
                    break;    
                case DataTypeEnum.GameObject:
                    DrawObjectValue<GameObject>(dataItem, paramRect, each, onlyShowValue);
                    break;
                case DataTypeEnum.Transform:
                    DrawObjectValue<Transform>(dataItem, paramRect, each, onlyShowValue);
                    break;    
                case DataTypeEnum.AnimationCurve:
                    DrawAnimationCurve(dataItem, paramRect, realRect, each, onlyShowValue);
                    break;
                case DataTypeEnum.MinMax:
                    DrawMinMaxValue(dataItem, paramRect, each, onlyShowValue);
                    break;
                case DataTypeEnum.Animation:
                    DrawAnimValue(dataItem, paramRect);
                    break;
                case DataTypeEnum.AnimSprintSwtich:
                    DrawSprintSwitchValue(dataGroupAsset, dataItem, paramRect);
                    break;
                case DataTypeEnum.AnimCrossTime:
                    DrawAnimCrossTimeValue(dataGroupAsset, dataItem, paramRect);
                    break;
                default:
                    EditorGUI.LabelField(paramRect, "Unsupported Type " + dataItem.type);
                    break;   
            }
        }

        #region Draw Object Value

        private static void DrawObjectValue<T>(DataGroupAsset.DataItem dataItem, Rect paramRect, float each, bool onlyShowValue) where T : UnityEngine.Object
        {
            paramRect = DrawName(dataItem, paramRect, each, onlyShowValue);
            
            UnityEngine.Object val = dataItem.unityObj;
            var obj = EditorGUI.ObjectField(paramRect, val, typeof(T), true) as T;
            dataItem.unityObj = obj;

            if (dataItem.unityObj)
            {
                if (string.IsNullOrEmpty(dataItem.name))
                {
                    dataItem.name = val.name;
                }
            }

            DrawDescription(dataItem, paramRect, each, onlyShowValue);
        }

        #endregion

        #region Draw Raw Value

        private static void DrawRawValue(DataGroupAsset.DataItem dataItem, Rect paramRect, float each, bool onlyShowValue)
        {
            paramRect = DrawName(dataItem, paramRect, each, onlyShowValue);
            
            if(!onlyShowValue)
                paramRect.width -= pro;

            switch (dataItem.type)
            {
                case DataTypeEnum.Int:
                    dataItem.intVal = EditorGUI.IntField(paramRect, dataItem.intVal);
                    break;
                case DataTypeEnum.Float:
                    dataItem.floatVal = EditorGUI.FloatField(paramRect, dataItem.floatVal);
                    break;
                case DataTypeEnum.String:
                    dataItem.strVal = EditorGUI.TextField(paramRect, dataItem.strVal);
                    break;
                case DataTypeEnum.Color:
                    dataItem.colorVal = EditorGUI.ColorField(paramRect, dataItem.colorVal);
                    break;
                case DataTypeEnum.Bool:
                    dataItem.boolVal = EditorGUI.Toggle(paramRect, dataItem.boolVal);
                    break;
                default:
                    EditorGUI.LabelField(paramRect, "Unsupported type");
                    break;
            }

            if (!onlyShowValue)
            {
                paramRect.x = paramRect.xMax;
                paramRect.width = pro;
                EditorGUI.LabelField(paramRect, dataItem.type.ToString());
            }

            DrawDescription(dataItem, paramRect, each, onlyShowValue);
        }

        #endregion
        
        #region Draw Color Value
        
        private static void DrawColorValue(DataGroupAsset.DataItem dataItem, Rect paramRect, float each, bool onlyShowValue)
        {
            paramRect = DrawName(dataItem, paramRect, each, onlyShowValue);
            dataItem.colorVal = EditorGUI.ColorField(paramRect, dataItem.colorVal);
            DrawDescription(dataItem, paramRect, each, onlyShowValue);
        }
        
        #endregion
        
        #region Draw MinMax Value

        private static void DrawMinMaxValue(DataGroupAsset.DataItem dataItem, Rect paramRect, float each, bool onlyShowValue)
        {
            paramRect = DrawName(dataItem, paramRect, each, onlyShowValue);
            
            if (dataItem.minMaxVal == null)
                dataItem.minMaxVal = new MinMaxData();

            {
                Rect tmp = paramRect;
                tmp.width = 30;
                dataItem.minMaxVal.min = EditorGUI.FloatField(tmp, dataItem.minMaxVal.min);

                tmp.x = tmp.xMax;
                tmp.width = paramRect.width - 60;
                EditorGUI.MinMaxSlider(tmp, ref dataItem.minMaxVal.min, ref dataItem.minMaxVal.max, 0, 1);

                tmp.x = tmp.xMax;
                tmp.width = 30;
                dataItem.minMaxVal.max = EditorGUI.FloatField(tmp, dataItem.minMaxVal.max);
            }
            
            DrawDescription(dataItem, paramRect, each, onlyShowValue);
        }
        
        #endregion

        #region Draw Curve Value
        
        private static void DrawAnimationCurve(DataGroupAsset.DataItem dataItem, Rect paramRect, Rect realRect, float each, bool onlyShowValue)
        {
            paramRect = DrawName(dataItem, paramRect, each, onlyShowValue);
            
            if (dataItem.curve == null)
                dataItem.curve = new AnimationCurve();

            dataItem.curve = EditorGUI.CurveField(paramRect, dataItem.curve);
            
            DrawDescription(dataItem, paramRect, each, onlyShowValue);

            Rect tmpRect = realRect;
            tmpRect.y += realRect.height / 3;
            tmpRect.y += 2;
            tmpRect.height = EditorGUIUtility.singleLineHeight;
            DrawCurveTimeAndValue(dataItem, tmpRect, each);
            
            tmpRect = realRect;
            tmpRect.y += realRect.height / 3 * 2;
            tmpRect.y += 2;
            tmpRect.height = EditorGUIUtility.singleLineHeight;
            DrawCurveTangent(dataItem, tmpRect, each);
        }

        private static void DrawCurveTimeAndValue(DataGroupAsset.DataItem dataItem, Rect realRect, float each)
        {
            Keyframe[] keyframes = dataItem.curve.keys;
            
            float time = 0f;
            float value = 0f;
            if (keyframes.Length > 1)
            {
                int last = keyframes.Length - 1;
                Keyframe lastFrame = keyframes[last];
                time = lastFrame.time;
                value = lastFrame.value;
                
                float tmpTime = time;
                float tmpValue = value;

                // 时间
                realRect.width = 30;
                EditorGUI.LabelField(realRect, "时间:");

                // 时间数值
                realRect.x = realRect.xMax + gap;
                realRect.width = each;
				
                EditorGUI.BeginChangeCheck();
                tmpTime = EditorGUI.FloatField(realRect, tmpTime);
                if (EditorGUI.EndChangeCheck())
                {
                    TeamLeaderCfgChange.UpdateCurveTime(dataItem, tmpTime, time, value);
                }

                // 值
                realRect.x = realRect.xMax + gap;
                realRect.width = 30;
                EditorGUI.LabelField(realRect, "距离:");
				
                // 值的数值
                realRect.x = realRect.xMax + gap;
                realRect.width = each;
                EditorGUI.BeginChangeCheck();
                tmpValue = EditorGUI.FloatField(realRect, tmpValue);
                if (EditorGUI.EndChangeCheck())
                {
                    TeamLeaderCfgChange.UpdateCurveValue(dataItem, tmpValue, value, tmpTime);
                }
            }
            else
            {
                EditorGUI.LabelField(realRect, "错误, 曲线关键帧小于2");
            }
        }

        static void DrawCurveTangent(DataGroupAsset.DataItem dataItem, Rect realRect, float each)
        {
            Keyframe[] keyframes = dataItem.curve.keys;
            if (keyframes.Length > 1)
            {
                // 起始速度
                realRect.width = 60;
                EditorGUI.LabelField(realRect, "起始速度:");
                
                // 速度数值：
                realRect.x = realRect.xMax + gap;
                realRect.width = each;
                
                EditorGUI.LabelField(realRect, keyframes[0].inTangent.ToString("f2"));
                
                // 结束速度
                realRect.x = realRect.xMax + gap;
                realRect.width = 60;
                EditorGUI.LabelField(realRect, "结束速度:");
                
                // 速度数值：
                realRect.x = realRect.xMax + gap;
                realRect.width = each;
                
                int last = keyframes.Length - 1;
                EditorGUI.LabelField(realRect, keyframes[last].outTangent.ToString("f2"));
            }
            else
            {
                EditorGUI.LabelField(realRect, "错误, 曲线关键帧小于2");
            }
        }

        #endregion

        #region Draw Item Message
        
        private static Rect DrawName(DataGroupAsset.DataItem dataItem, Rect paramRect, float each, bool onlyShowValue)
        {
            if (!onlyShowValue)
            {
                paramRect.width = 30;
                EditorGUI.LabelField(paramRect, "参数:");

                paramRect.x = paramRect.xMax + gap;
                paramRect.width = each * 1.8f;
                dataItem.name = EditorGUI.TextField(paramRect, dataItem.name);    

                paramRect.x = paramRect.xMax + gap;
                paramRect.width = 20;
                EditorGUI.LabelField(paramRect, "值:");

                paramRect.x = paramRect.xMax + gap;
                paramRect.width = each * 1.2f;
            }

            return paramRect;
        }

        private static Rect DrawDescription(DataGroupAsset.DataItem dataItem, Rect paramRect, float each, bool onlyShowValue)
        {
            if (!onlyShowValue)
            {
                paramRect.x = paramRect.xMax + gap;
                paramRect.width = 30;
                EditorGUI.LabelField(paramRect, "描述:");

                paramRect.x = paramRect.xMax + gap;
                paramRect.width = each;
                dataItem.desc = EditorGUI.TextField(paramRect, dataItem.desc);
            }

            return paramRect;
        }
                
        #endregion
        
        #region Draw AnimCrossTime Value

        private static void DrawAnimCrossTimeValue(DataGroupAsset dataAsset, DataGroupAsset.DataItem dataItem, Rect paramRect)
        {
            if (GUI.Button(paramRect, "编辑"))
            {
                AnimSheetConfigEditor.OpenDataGroupAsset(dataAsset);
            }
        }
        
        #endregion
        
        #region Draw Anim Value

        private static void DrawAnimValue(DataGroupAsset.DataItem dataItem, Rect paramRect)
        {
            EditorGUI.LabelField(paramRect, dataItem.strVal);
        }
        
        #endregion
        
        #region Draw SprintSwitch Value
        
        private static void DrawSprintSwitchValue(DataGroupAsset dataAsset, DataGroupAsset.DataItem dataItem, Rect paramRect)
        {
            if (GUI.Button(paramRect, "编辑"))
            {
                AnimSpringManagerConfigEditor.OpenDataGroupAsset(dataAsset);
            }
        }

        #endregion

        #endregion
    }
}  
#endif