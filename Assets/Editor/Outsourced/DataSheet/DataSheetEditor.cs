using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace TKPlugins
{
    public class DataSheetEditor : EditorWindow
    {
        public static void Initialzie(string titleName, DataSheetModel sheetModel)
        {
            DataSheetEditor window = CreateInstance<DataSheetEditor>();
            window.wantsMouseMove = true;
            window.titleContent = new GUIContent(titleName);
            window.sheetModel = sheetModel;
            window.Show();
        }

        private DataSheetTableView m_tableView;
        private DataSheetModel m_sheetModel;
        private Vector2 m_pos = Vector2.zero;
        private bool m_isReload = false;

        public DataSheetModel sheetModel
        {
            set
            {
                m_sheetModel = value;
                m_isReload = true;
            }
        }

        private void OnGUI()
        {
            if (m_isReload)
            {
                TreeViewState treeViewState = new TreeViewState();
                MultiColumnHeaderState multiColumnHeaderState = DataSheetTableView.CreateDefaultMultiColumnHeaderState(m_sheetModel);
                MultiColumnHeader multiColumnHeader = new MultiColumnHeader(multiColumnHeaderState);
                m_tableView = new DataSheetTableView(m_sheetModel, treeViewState, multiColumnHeader);
                m_isReload = false;
            }

            if (m_tableView != null)
            {
                if (GUILayout.Button("保存"))
                {
                    m_sheetModel.Save();
                }
                
                m_pos = EditorGUILayout.BeginScrollView(m_pos);
                m_tableView.OnLayoutGUI();
                EditorGUILayout.EndScrollView();
            }
               
        }
    }

}
