using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using ZGame.Editor;
using ZGameChess;

[CustomEditor(typeof(ACGameMaterialCurve))]
public class ACGameMaterialCurveEditor : Editor
{
    public ACGameMaterialCurve m_target = null;
    public static string[] m_enumProperties = null;

    private MaterialAnimationInfo.EffectCurveType filterCurveType =
        MaterialAnimationInfo.EffectCurveType.TEXTURE_STRING;
    private bool needFilterSee = false;
    private void OnEnable()
    {
        InitEnumProperties();
    }

    public static MaterialAnimationInfo copiedInfo = null;

    public static void InitEnumProperties()
    {
        if (null == m_enumProperties)
        {
            m_enumProperties = new string[]
                {
                    ACGameMaterialCurve.other,
                    "_MainTex",
                    "_Alpha",
                    "_DissolveTex",
                    "_DissolveValue",
                    "_EdgeRange",
                    "_EdgeColor",
                    "_AlphaValue",
                    "_FresnelExponent",
                    "_FresnelColor",
                    "_FresnelScale",
                    "_FresnelAlpha",
                    "_ShadowPlane",
                    "_ShadowProjDir",
                    "_ShadowIntensity",
                    "_Debut",
                    "_DebutColor",
                    "_DebutColorIntensity",
                    "_LevelUp",
                    "_LevelUpTex",
                    "_ReadyColor",
                    "_ReadyColorLerp",
                    "_ReadyAlpha",
                    "_SpeedU",
                    "_SpeedV",
                    "_FlowingTex",
                    "_OutlineColor",
                    "_OutlineWidth",
                    "_OutlineFactor",
                    "_OutlineExponent",
                    "_OutlineRimAlpha",
                    "_MainCol",
                    "_UVRotate",
                    "_SpeedX",
                    "_SpeedY",
                    "_SpeedToggle",
                    "_NormalFactor",
                    "_GalaxyStars",
                    "_GalaxyColor",
                    "_GalaxyUSpeed",
                    "_GalaxyVSpeed",
                };
        }
    }

    public override void OnInspectorGUI()
    {
        m_target = targets[0] as ACGameMaterialCurve;
        EditorGUI.BeginChangeCheck();
        m_target.delayTime = EditorGUILayout.FloatField(new GUIContent("Delay time:", "延迟多长时间后，开始播放效果"), m_target.delayTime);
        m_target.duration = EditorGUILayout.Slider(new GUIContent("Duration:", "效果持续多长时间，单位为秒，设置范围是"
            + ChessHeroMaterialController.MinimumDurationTime.ToString() + "-" + ChessHeroMaterialController.MaximumDurationTime.ToString()),
            m_target.duration, ChessHeroMaterialController.MinimumDurationTime, ChessHeroMaterialController.MaximumDurationTime);
        m_target.canOverrideDuration = EditorGUILayout.Toggle(new GUIContent("Can override duration:", "一般设为true, 如果设为false, 则特效持续时间固定为预设值，否则，持续时间可以被其他方式改写，例如程序逻辑"), m_target.canOverrideDuration);
        m_target.priority = EditorGUILayout.IntField(new GUIContent("Priority:", "优先级, 默认是0, 数字大的会覆盖数字小的"), m_target.priority);
        m_target.shaderType = (ACGameMaterialCurve.ShaderType)EditorGUILayout.EnumPopup(new GUIContent("Shader type:", "使用什么shader去渲染这个效果。如有疑问，可咨询lvanchen。另外，如果你要用的shader这里面的选项都没有，那么你可以选Other，允许你在下面填写shader名（不带.shader扩展名）。"), m_target.shaderType);
        if (m_target.shaderType == ACGameMaterialCurve.ShaderType.Other)
        {
            m_target.shaderName = EditorGUILayout.TextField(new GUIContent("Shader Name", "将自定义shader的文件名（不带.shader扩展名）放入这里"), m_target.shaderName);
        }
        m_target.relationType = (ACGameMaterialCurve.RelationType)EditorGUILayout.EnumPopup(new GUIContent("RelationType:", "选择关系类型，覆盖/叠加\n"+
        "1、对于cfg增加一个参数Relation叠加关系，默认为0（覆盖），1为叠加\n"+
        "2、对于2个关系为0的cfg同时存在时，保留两者的全部数据，外在表现为显示高优先级的cfg，如果优先级相同则显示后加上的cfg\n"+
        "eg：对一个角色同时加上持续3s的2优先cfgA和持续5s的1优先cfgB，那么会表现3sA，之后从B的第3s开始持续2s\n"+
        "3、一个关系为1的cfg与一个关系为0的cfg同时存在，那么同时表现A两者，冲突的参数以高优先的为准\n"+
        "eg：对于一个角色同时加上头上绿色关系为1优先为2的cfgA和全身变红的关系为1优先为1的cfgB，那么表现为头绿其他红"), m_target.relationType);
        m_target.resetMaterialAfterFinishingPlay = EditorGUILayout.Toggle(new GUIContent("Reset after finish curve play:", "如果设为true，则播放完这个效果之后，将英雄材质恢复成默认材质效果，" +
            "但贴图会根据英雄当前等级设定对应等级的贴图。你可以选择不勾选此项，这样播放完效果后，将会持续保持最后一帧的效果状态，" +
            "开发侧随后可以调用CurveType=None来恢复成默认材质效果（同根据当前英雄等级设置贴图），这样就不必费心考虑Duration该设置多长时间了"), m_target.resetMaterialAfterFinishingPlay);
        m_target.isDoCurveForAllMaterials = EditorGUILayout.Toggle(new GUIContent("Do curve for all materials:", "如果设为true，则对英雄的所有材质都进行曲线播放无视命名，否则只对符合命名要求的材质进行曲线播放"), m_target.isDoCurveForAllMaterials);
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(new GUIContent("忽略的transform名字列表", "如果某个棋子的子物体，你不想要控制它的特效播放，那么可以将这个子物体的名字添加进去，则播放配置的时候，自动忽略之"));
        if (GUILayout.Button("添加忽略名字"))
        {
            if (m_target.ignoreTransformNameList == null)
            {
                m_target.ignoreTransformNameList = new List<string>();
            }
            m_target.ignoreTransformNameList.Add("");
        }
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();
        {
            SerializedProperty stringsProperty = serializedObject.FindProperty("mainTextureNoChangeNameArr");
            EditorGUILayout.PropertyField(stringsProperty, new GUIContent("mainTexture不改变的材质名字组"), true); // True means show children
            serializedObject.ApplyModifiedProperties(); // Remember to apply modified properties
            EditorGUILayout.EndHorizontal();
        }
        if (m_target.ignoreTransformNameList != null)
        {
            for (int i = m_target.ignoreTransformNameList.Count - 1; i >= 0; i--)
            {
                EditorGUILayout.BeginHorizontal();
                m_target.ignoreTransformNameList[i] = EditorGUILayout.TextField(m_target.ignoreTransformNameList[i]);
                if (GUILayout.Button("x", GUILayout.Width(50.0f)))
                {
                    m_target.ignoreTransformNameList.RemoveAt(i);   
                }
                EditorGUILayout.EndHorizontal();
            }   
        }

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(new GUIContent("忽略的material名字列表", "如果棋子的某个材质，你不想要它受到材质曲线的影响，那么可以将这个材质的名字添加进去，则播放配置的时候，自动忽略之"));
        if (GUILayout.Button("添加忽略名字"))
        {
            if (m_target.ignoreMaterialNameList == null)
            {
                m_target.ignoreMaterialNameList = new List<string>();
            }
            m_target.ignoreMaterialNameList.Add("");
        }
        EditorGUILayout.EndHorizontal();

        if (m_target.ignoreMaterialNameList != null)
        {
            for (int i = m_target.ignoreMaterialNameList.Count - 1; i >= 0; i--)
            {
                EditorGUILayout.BeginHorizontal();
                m_target.ignoreMaterialNameList[i] = EditorGUILayout.TextField(m_target.ignoreMaterialNameList[i]);
                if (GUILayout.Button("x", GUILayout.Width(50.0f)))
                {
                    m_target.ignoreMaterialNameList.RemoveAt(i);
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        EditorGUILayout.BeginHorizontal();
        // 必须包括的material名字列表
        EditorGUILayout.LabelField(new GUIContent("必须包括的material名字列表", "如果棋子的某个材质并没有按照标准格式命名，你想要它受到这个材质曲线的影响，那么可以将这个属性的名字添加进去，则播放配置的时候，自动添加之。\n" +
            "但是注意，由于这些材质无法从名字获取其index，只有index为-1的属性才会影响它。"));
        if (GUILayout.Button("添加必须包括名字"))
        {
            if (m_target.mustIncludeMaterialNameList == null)
            {
                m_target.mustIncludeMaterialNameList = new List<string>();
            }
            m_target.mustIncludeMaterialNameList.Add("");
        }
        EditorGUILayout.EndHorizontal();

        if (m_target.mustIncludeMaterialNameList != null)
        {
            for (int i = m_target.mustIncludeMaterialNameList.Count - 1; i >= 0; i--)
            {
                EditorGUILayout.BeginHorizontal();
                m_target.mustIncludeMaterialNameList[i] = EditorGUILayout.TextField(m_target.mustIncludeMaterialNameList[i]);
                if (GUILayout.Button("x", GUILayout.Width(50.0f)))
                {
                    m_target.mustIncludeMaterialNameList.RemoveAt(i);
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        EditorGUILayout.BeginVertical("box");
        if (GUILayout.Button("Add"))
        {
            if (m_target.curveList == null)
                m_target.curveList = new MaterialAnimationInfo[0];
            Array.Resize(ref m_target.curveList, m_target.curveList.Length + 1);
            m_target.curveList[m_target.curveList.Length - 1] = new MaterialAnimationInfo();
        }
        if (GUILayout.Button("Paste"))
        {
            if (copiedInfo != null)
            {
                Array.Resize(ref m_target.curveList, m_target.curveList.Length + 1);
                copiedInfo.CopyTo(ref m_target.curveList[m_target.curveList.Length - 1]);
                // This will cause memory leak to object AnimationCurve inside, but so be it.
                //copiedInfo = null;
            }
        }
        if (GUILayout.Button("Clear"))
        {
            if (EditorUtility.DisplayDialog(
                "Warning",
                "Are you sure you want to clear?",
                "Clear!",
                "Cancel"))
            {
                Array.Clear(m_target.curveList, 0, m_target.curveList.Length);
                Array.Resize(ref m_target.curveList, 0);
            }
        }
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
        needFilterSee = EditorGUILayout.ToggleLeft(new GUIContent("过滤查看", "你可以使用过滤查看功能，筛选查看你想要的变量类型，这对于变量数量特别多的情况下，非常有效。"), needFilterSee);
        if (needFilterSee)
        {
            filterCurveType = (MaterialAnimationInfo.EffectCurveType)EditorGUILayout.EnumPopup("过滤类型", filterCurveType);
        }

        EditorGUILayout.Space();
        if (null != m_target && null != m_target.curveList && m_target.curveList.Length > 0)
        {
            GUILayout.BeginVertical();
            for (int i = 0; i < m_target.curveList.Length; ++i)
            {
                if (!needFilterSee || (m_target.curveList[i].type == filterCurveType))
                {
                    EditorGUILayout.Space();
                    EditorGUI.indentLevel = 1;
                    GUILayout.Label("index : " + i);
                    EditorGUI.indentLevel = 2;
                    GUILayout.BeginVertical("box");
                    EditorGUI.indentLevel = 2;
                    InspectorInfo(m_target.curveList[i], ref m_target.curveList, i);
                    GUILayout.EndVertical();
                }
            }
            GUILayout.EndVertical();
        }

        EditorGUILayout.Space();
        EditorGUILayout.Space();
        EditorGUILayout.Space();
        m_target.masterCfg = EditorGUILayout.ObjectField(
            new GUIContent("Master Curve: ", "Master即被继承的Curve，在当前配置生效的基础上，余下的参数由Master生效，太多的继承关系可能造成游戏崩溃，通常几个，十几个和几十个问题不大（但也要考虑性能）\n    注意基础配置（DelayTime/Duration/Shader/Priority）会覆盖掉Master，且如果因为更换Shader造成Master某些参数无法生效，则那些参数将无效\n    当前Curve同名参数会覆盖掉Master的同名参数，但如果Master当中的同名参数控制的材质index不同则不受影响。\n    例如，4个材质，Master控制-1（全部4个），而当前配置同名参数只控制2，\n    那么1，3，4号材质同种参数将由Master来控制，当前配置同名参数将只覆盖控制掉2的对应Master的同名参数\n    或者Master控制1，当前配置的同名参数只控制2，那么两者都会正常生效且互不干扰。"),
            m_target.masterCfg, typeof(ACGameMaterialCurve), false) as ACGameMaterialCurve;

        if (EditorGUI.EndChangeCheck())
        {
            EditorUtility.SetDirty(m_target);
        }
    }

    public static void InspectorInfo(MaterialAnimationInfo item, ref MaterialAnimationInfo[] curveList, int index, bool needIndent = true)
    {
        if (item == null)
            return;
        GUILayout.Label("Property Name:");
        item.type = (MaterialAnimationInfo.EffectCurveType)EditorGUILayout.EnumPopup(new GUIContent("Type:", "数据种类，对应shader对应变量的种类。如有不清楚的地方，请咨询lvanchen。"), item.type/*, GUILayout.Width(500f)*/);
        if (item.m_PropertyName == null)
            item.m_PropertyName = "";
        int m_enumFlagIndex = Array.IndexOf(m_enumProperties, item.m_PropertyName);
        if (m_enumFlagIndex == -1)
            m_enumFlagIndex = 0;
        if (item.type != MaterialAnimationInfo.EffectCurveType.RENDER_QUEUE)
            m_enumFlagIndex = EditorGUILayout.Popup(new GUIContent("Internal Variable Name: ", "Shader的变量名，如果不知道该选什么，可以咨询lvanchen。如果这里没有你想要的变量名，那么，选择Other，这时候将会允许在Custom Internal Variable Name自己填写变量名。"), m_enumFlagIndex, m_enumProperties/*, GUILayout.Width(400f)*/);
        item.m_PropertyName = m_enumProperties[m_enumFlagIndex];
        if (m_enumFlagIndex == Array.IndexOf(m_enumProperties, ACGameMaterialCurve.other))
        {
            if (item.type != MaterialAnimationInfo.EffectCurveType.RENDER_QUEUE)
                item.m_CustomPropertyName = EditorGUILayout.TextField(new GUIContent("Custom Internal Variable Name: ", "自定义Shader变量名，如果不知道该填写什么，可以咨询lvanchen"), item.m_CustomPropertyName);
        }
        item.materialIndex = EditorGUILayout.IntField(new GUIContent("Material Index:", "材质Index，为-1是影响这个英雄的所有材质，否则影响特定index的材质。注意，如果你设定的index没有找到对应的材质，那么将不会有任何效果。\n所谓index指的是材质顺序，当你命名这个棋子的材质名称的时候，例如h_garen_1_2.mat，则其中的2为材质Index。同时兼容简便写法，例如h_garen_1.mat，自动认为材质index为1"), item.materialIndex);

        switch (item.type)
        {
            case MaterialAnimationInfo.EffectCurveType.FLOAT:
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Float = EditorGUILayout.FloatField("From:", item.m_From_Float);
                item.m_To_Float = EditorGUILayout.FloatField("To:", item.m_To_Float);
                break;
            case MaterialAnimationInfo.EffectCurveType.KEYWORD:
                item.m_Uses_KeyWord = EditorGUILayout.Toggle(new GUIContent("Use KeyWord:", "是否启用宏"), item.m_Uses_KeyWord);
                break;
            case MaterialAnimationInfo.EffectCurveType.COLOR:
                item.m_Uses_HDR = EditorGUILayout.Toggle(new GUIContent("Use HDR:", "是否使用HDR颜色"), item.m_Uses_HDR);
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Color = EditorGUILayout.ColorField(new GUIContent("From:"), item.m_From_Color, true, true, item.m_Uses_HDR);
                item.m_To_Color = EditorGUILayout.ColorField(new GUIContent("To:"), item.m_To_Color, true, true, item.m_Uses_HDR);
                break;
            case MaterialAnimationInfo.EffectCurveType.VECTOR4:
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Vec4 = EditorGUILayout.Vector4Field("From:", item.m_From_Vec4);
                item.m_To_Vec4 = EditorGUILayout.Vector4Field("To:", item.m_To_Vec4);
                break;
            case MaterialAnimationInfo.EffectCurveType.TEXTURE:
                item.m_Texture = EditorGUILayout.ObjectField("Texture:", item.m_Texture, typeof(Texture), false) as Texture;
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Vec4 = EditorGUILayout.Vector4Field("From:", item.m_From_Vec4);
                item.m_To_Vec4 = EditorGUILayout.Vector4Field("To:", item.m_To_Vec4);
                break;
            case MaterialAnimationInfo.EffectCurveType.TEXTURE_STRING:
                item.m_Texture_name = EditorGUILayout.TextField("Texture Name:", item.m_Texture_name);
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Vec4 = EditorGUILayout.Vector4Field("From:", item.m_From_Vec4);
                item.m_To_Vec4 = EditorGUILayout.Vector4Field("To:", item.m_To_Vec4);
                break;
            case MaterialAnimationInfo.EffectCurveType.BOOL:
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线，高于0.5算true，低于0.5算false"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                break;
            case MaterialAnimationInfo.EffectCurveType.SWITCHABLETEXTURE:
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线，高于0.5使用To贴图，低于0.5使用From贴图，如果二者有一个为空，则哪怕满足高于0.5/低于0.5条件，也不会作任何修改"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Tex = EditorGUILayout.ObjectField("From:", item.m_From_Tex, typeof(Texture), false) as Texture;
                item.m_To_Tex = EditorGUILayout.ObjectField("To:", item.m_To_Tex, typeof(Texture), false) as Texture;
                break;
            case MaterialAnimationInfo.EffectCurveType.RENDER_QUEUE:
                item.curve = EditorGUILayout.CurveField(new GUIContent("Curve:", "编辑曲线，高于0.5使用To的值，反之使用From的值"), item.curve/*, Color.white, new Rect(0, 0, 1, 1)*/);
                item.wrapMode = (WrapMode)EditorGUILayout.EnumPopup(new GUIContent("Wrap Mode:", "曲线的Warp Mode，当前其他方案无效，暂时先设为once"), item.wrapMode);
                item.m_From_Int = EditorGUILayout.IntField("From:", item.m_From_Int);
                item.m_To_Int = EditorGUILayout.IntField("To:", item.m_To_Int);
                break;
            default:
                break;
        }

        if (needIndent)
        {
            EditorGUI.indentLevel = 3;   
        }
        if (GUILayout.Button("Remove", GUILayout.Width(200f)))
        {
            List<MaterialAnimationInfo> tmp = new List<MaterialAnimationInfo>(curveList);
            tmp.RemoveAt(index);
            curveList = tmp.ToArray();
        }
        if (GUILayout.Button("Copy", GUILayout.Width(200f)))
        {
            curveList[index].CopyTo(ref copiedInfo);
        }
    }
}

public class ACGameMaterialCurvePostProcessor : ZGameAssetProcessorBase
{
    public override Type ImporterType => typeof(AssetImporter);
    public override Type AssetType => typeof(ACGameMaterialCurve);
    public override bool Enable { get; set; } = true;

    //public ACGameMaterialCurvePostProcessor()
    //{
    //    if (ChessBattleGlobal.Instance.HeroMatCfg == null)
    //        ChessBattleGlobal.Instance.HeroMatCfg = new HeroMaterialConfig();
    //    ChessHeroMaterialController.WarmUp();
    //}

    public override bool PostProcess(string assetPath, PostProcessType processType)
    {
        if (processType != PostProcessType.Deleted
            && (
                assetPath.Contains("hero_material_cfg")
                || assetPath.Contains("hero_material_cfg_overrides")
                || assetPath.Contains("per_hero_mat_cfg")
            ))
        {
            // make shader name to shader asset object and save it.
            ACGameMaterialCurve curve = AssetDatabase.LoadAssetAtPath<ACGameMaterialCurve>(assetPath);
            if (curve != null
                // Set dirty & save will trigger PostProcess again. We use this to avoid stack overflow.
                && curve.shaderObject == null)
            {
                if (curve.shaderType == ACGameMaterialCurve.ShaderType.Other)
                {
                    //var shader = ChessBattleGlobal.Instance.HeroMatCfg.LoadShader(curve.shaderName);
                    var shader = AssetDatabase.LoadAssetAtPath<Shader>($"Assets/Shaders/Moba/{curve.shaderName}.shader");
                    curve.shaderObject = shader;
                }
                else
                {
                    // Todo:...
                    GetShaderByEnumTypeEditor(out var shader, curve);
                    curve.shaderObject = shader;
                }

                // It could happen when the shader is not found. However, we don't want to trigger OnPostProcess again.
                if (curve.shaderObject != null)
                {
                    EditorUtility.SetDirty(curve);
                    AssetDatabase.SaveAssets();
                }
                else
                {
                    Debug.LogError("ACGameMaterialCurvePostProcessor: Shader could not found for: " + curve.shaderName);
                }
            }
        }

        return true;
    }

    private static void GetShaderByEnumTypeEditor(out Shader shader, ACGameMaterialCurve curveInfo)
    {
        var type = curveInfo.shaderType;
        switch (type)
        {
            case ACGameMaterialCurve.ShaderType.DeathDissolve:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Hero_DeathDissolve.shader");
                break;
            case ACGameMaterialCurve.ShaderType.HeroUplevel:
                {
                    //判断下原有的材质名字是否是带Transparent;
                    //if (matShaderName.Equals(m_TransparentShader.name) || matShaderName.Equals(m_SelectionOutlineTransparentShader.name))
                    //    shader = m_HerouplevelTransparentShader;
                    //else if (matShaderName.Contains(m_HeroBaseShader.name) || matShaderName.Equals(m_SelectionOutlineShader.name))
                    //    shader = m_HerouplevelShader;
                    //else if (matShaderName.Equals("Mt/Effect/FluxayOnline"))
                    //    shader = m_HerouplevelShader;
                    //else
                        //其他类型Shader不播放升级特效
                        shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Hero_DebutAndLevelUP.shader");
                }
                break;
            case ACGameMaterialCurve.ShaderType.Transparent:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Hero_Base_Transparent.shader");
                break;
            case ACGameMaterialCurve.ShaderType.MonsterBase:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Monster_Base.shader");
                break;
            case ACGameMaterialCurve.ShaderType.MonsterReadyTime:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Monster_ReadyTime.shader");
                break;
            case ACGameMaterialCurve.ShaderType.SelectionOutline:
                {
                    //判断下原有的材质名字是否是带Transparent;
                    //if (matShaderName.Equals(m_TransparentShader.name) || matShaderName.Equals(m_SelectionOutlineTransparentShader.name))
                    //    shader = m_SelectionOutlineTransparentShader;
                    //else if (matShaderName.Equals(m_EffectAdditiveUVAnimationBaseShader.name) || matShaderName.Equals(m_SelectionOutlineAdditiveUVAnimationShader.name))
                    //    shader = m_SelectionOutlineAdditiveUVAnimationShader;
                    //else if (matShaderName.Equals(m_EffectAlphaBlendUVAnimationBaseShader.name) || matShaderName.Equals(m_SelectionOutlineAlphaBlendUVAnimationShader.name) || matShaderName.Equals("Mt/Effect/Alpha Blend UVAnimation Mask"))
                    //    shader = m_SelectionOutlineAlphaBlendUVAnimationShader;
                    //else if (matShaderName.Equals(m_HeroBase_VexShadowShader.name) || matShaderName.Equals(m_SelectionOutline_VexShadowShader.name))
                    //    shader = m_SelectionOutline_VexShadowShader;
                    //else
                        shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Hero_Base_Outline.shader");
                }
                break;
            case ACGameMaterialCurve.ShaderType.EffectAdditiveUVAnimBase:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Hero_Base_Outline.shader");
                break;
            case ACGameMaterialCurve.ShaderType.EffectAlphaBlendUVAnimBase:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Effect_Alpha Blend_UVAnimation_Base.shader");
                break;
            case ACGameMaterialCurve.ShaderType.EffectAlphaBlendUVAnimMask:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Effect_Alpha Blend_UVAnimation_Mask.shader");
                break;
            default:
                shader = AssetDatabase.LoadAssetAtPath<Shader>("Assets/Shaders/Moba/Mt_Hero_Base.shader");
                break;
        }
    }
}
