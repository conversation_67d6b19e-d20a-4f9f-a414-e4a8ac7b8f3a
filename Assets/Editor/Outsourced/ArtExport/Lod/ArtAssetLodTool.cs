using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

public class ArtAssetLodTool
{

    public static string EffectPath = "Assets/Art_TFT_Raw/effects";
    public static string EffectPath_SeaLow = "Assets/Art_TFT_Raw/effects_sealow";       // 极低版本
    public static string EffectPath_Low = "Assets/Art_TFT_Raw/effects_low";

    public static string ModelPath = "Assets/Art_TFT_Raw/hero/hero_show";
    public static string ModelPath_Low = "Assets/Art_TFT_Raw/hero/hero_show_low";
    public static string ModelPath_High = "Assets/Art_TFT_Raw/hero/hero_show_high";
    public static string ModelPath_Pad = "Assets/Art_TFT_Raw/hero/hero_show_pad";
    public static string ModelResPath = "Assets/Art_TFT_Raw/model_res";

    public static string TinyModelPath = "Assets/Art_TFT_Raw/little_legend_res/model/";
    public static string TinyModelPath_Low = "Assets/Art_TFT_Raw/little_legend_res/model_low/";
    public static string TinyModelPath_High = "Assets/Art_TFT_Raw/little_legend_res/model_high/";
    public static string TinyModelPath_Pad = "Assets/Art_TFT_Raw/little_legend_res/model_pad/";

    public static List<string> GenLodPrefab(string path)
    {
        if (path.EndsWith(".prefab") && (path.StartsWith(ModelPath) || path.StartsWith(TinyModelPath)))
        {
            List<string> lodPaths = new List<string>();
            if (!path.StartsWith(ModelPath))
            {
                ArtAssetModelLod lod = new ArtAssetModelLod(path, ArtAssetModelType.Low);
                var lodPath = lod.DoLod();
                if (!string.IsNullOrEmpty(lodPath))
                    lodPaths.Add(lodPath);
                lod = new ArtAssetModelLod(path, ArtAssetModelType.High);
                lodPath = lod.DoLod(); 
                if (!string.IsNullOrEmpty(lodPath))
                    lodPaths.Add(lodPath);
                lod = new ArtAssetModelLod(path, ArtAssetModelType.Pad);
                lodPath = lod.DoLod();
                if (!string.IsNullOrEmpty(lodPath))
                    lodPaths.Add(lodPath);   
            }
            if (lodPaths.Count == 0)
                lodPaths.Add(path);

            return lodPaths;
        }
        else
        {
            return new List<string>() { path };
        }
    }

    //递归查找子节点
    public static void FindTransform(Transform root, string startName, ref List<Transform> list)
    {
        if (root.name.Contains(startName))
        {
            list.Add(root);
            return;
        }
        foreach (Transform child in root)
        {
            FindTransform(child, startName, ref list);
        }
    }
}

