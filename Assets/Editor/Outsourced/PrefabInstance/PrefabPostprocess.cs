using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using TKFrame;
using TKFramework;
using UnityEditor;
using UnityEditor.Experimental.SceneManagement;
using UnityEngine;
using UnityEngine.Playables;
using static GetPrefabData;

namespace TKPlugins
{
    public static class PrefabPostprocess
    {
        private const string LastSvnFileName = "last_prefab_svn_info.txt";
        private const string ROBOT_KEY = "942f5e02-9df6-4261-af7e-0625f765de13";

        #region 判断是否Prefab

        public static bool isUIPrefab(string item)
        {
            if (item.EndsWith(".prefab") && item.StartsWith("Assets/ChessArt/UI"))
            {
                return true;
            }

            return false;
        }

        public static bool isEffectPrefab(string item)
        {
            if (item.EndsWith(".prefab") && item.StartsWith("Assets/Art_TFT_Raw/effects"))
            {
                return true;
            }

            return false;
        }

        public static bool isScenePrefab(string item)
        {
            if (item.EndsWith(".prefab") && 
                (item.StartsWith("Assets/Art_TFT_Raw/scenes") || item.StartsWith("Assets/Art_TFT_Raw/little_legend_res/scene")))
            {
                return true;
            }

            return false;
        }

        #endregion

        public static string GetCommandLineArg(string name)
        {
            string[] args = System.Environment.GetCommandLineArgs();
            for (int i = 0; i < args.Length; i++)
            {
                if (args[i] == name)
                {
                    if (args.Length > i + 1)
                    {
                        return args[i + 1];
                    }
                }
            }

            return "";
        }

        // 对指定目录所有有变化的prefab进行处理

        [MenuItem("Assets/xiaobai/PrefabPostprocess")]
        public static void Postprocess()
        {
            Handle(Application.dataPath);
        }

        [MenuItem("Assets/xiaobai/PrefabPostprocessSelectAsset")]
        public static void PrefabPostprocessSelectAsset()
        {
            foreach (var item in Selection.objects)
            {
                var path = AssetDatabase.GetAssetPath(item);
                if (isEffectPrefab(path) || isUIPrefab(path) || isScenePrefab(path))
                {
                    CheckAndModifyPrefab(path);
                }
            }
        }

        public static void Handle(string rootPath)
        {
            List<TKSvnLogInfo> logs;
            if (File.Exists(LastSvnFileName) && int.TryParse(File.ReadAllText(LastSvnFileName), out int lastSvn))
            {
                var info = TKSvnCore.GetInfo(rootPath);
                int curSvn = info.svnNo;
                logs = TKSvnCore.ShowLog(rootPath, lastSvn, curSvn, true);

                // 上次已检查过的不用管了
                for (int i = logs.Count - 1; i >= 0; --i)
                {
                    if (logs[i].svnNo == lastSvn)
                    {
                        logs.RemoveAt(i);
                        break;
                    }
                }
            }
            else
            {
                logs = TKSvnCore.ShowLog(rootPath, 100, true);
            }

            logs.Sort((a, b) => a.svnNo.CompareTo(b.svnNo));

            int lastSvnNo = 0;
            try
            {
                string submitInfo = "--story=865809373 自动处理prefab [xiaobai]";
                for (int i = 0; i < logs.Count; ++i)
                {
                    var log = logs[i];
                    if (log.submitDesc.StartsWith(submitInfo))
                        continue;
                    lastSvnNo = log.svnNo;
                    // 打印处理日志
                    TKFrame.Diagnostic.Log("svnNo: {0} svnAuthor: {1} submitDesc: {2} time: {3}", log.svnNo, log.author, log.submitDesc, log.submitTime);
                    HandleLog(log, submitInfo);

                    File.WriteAllText(LastSvnFileName, log.svnNo.ToString());
                }
            }
            catch (Exception ex)
            {
                TKFrame.Diagnostic.Log("svn号：" + lastSvnNo);
#if !OUTSOURCE
                TKFrame.Diagnostic.Log(ex.ToString());
                string piplineName = GetCommandLineArg("-pipline");
                WeChatTool.SendTxt(ROBOT_KEY, "## 自动处理prefab执行异常\n> <@jiaiebai>\n> 最后处理SVN号:" + lastSvnNo + "\n>流水线: " + piplineName + "\n异常内容:\n" + ex.ToString(), true);
#endif
            }
        }

        private static void HandleLog(TKSvnLogInfo log, string submitInfo)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();

            List<string> logList = new List<string>();
            List<string> errorList = new List<string>();
            List<string> assetPaths = new List<string>();
            try
            {
                foreach (var item in log.modifyList)
                {
                    // 美术的本地文件或者目录
                    string path = item.path;
                    path = path.Substring(path.IndexOf("/Assets/") + 1);
                    if (path.EndsWith(".prefab") && File.Exists(path))
                    {
                        if (isEffectPrefab(path) || isUIPrefab(path) || isScenePrefab(path))
                        {
                            AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
                            if (CheckAndModifyPrefab(path))
                            {
                                assetPaths.Add(path);
                                logList.Add(path);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorList.Add("处理异常: " + ex.ToString());
            }

            if (assetPaths.Count != 0)
            {
                EditorUtility.UnloadUnusedAssetsImmediate(true);
                AssetDatabase.Refresh();
                AssetDatabase.SaveAssets();

                if (TKSvnCore.AutoCommitSync(submitInfo + " [" + log.author + "]" + log.submitDesc + "[xiaobai][自动导入工程]", assetPaths.ToList()))
                {
                    logList.Add("Svn提交：成功!");
                }
                else
                {
                    errorList.Add("Svn提交：失败!");
                }

                string piplineName = GetCommandLineArg("-pipline");
                string simpleContent = string.Format("> ## Svn: {0}\n> 提交人:<@{1}>\n> 提交时间:{2}\n> 提交信息:{3}\n>###  状态:{4}\n> 耗时: {5}\n>流水线:{6}",
                            log.svnNo, log.author, log.submitTime, log.submitDesc,
                            errorList.Count == 0 ? "<font color='info'>处理完成</font>" : "<font color='warning'>处理出错</font>",
                            sw.Elapsed,
                            piplineName);

                ReportToWechat(simpleContent, logList, errorList);
            }
            //else
            //{
            //    logList.Add("Svn提交：无资源可提交!");
            //}

            sw.Stop();
        }

        private static void ReportToWechat(string simpleContent, List<string> logList, List<string> errorList)
        {
#if !OUTSOURCE
            string logstr = string.Join("\n", logList);
            string errorstr = string.Join("\n", errorList);

            if (logstr.Length + simpleContent.Length + errorstr.Length < 1980)
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine(simpleContent);
                if (logstr.Length != 0)
                {
                    sb.AppendLine("\n\n## 日志:\n" + logstr);
                }
                if (errorstr.Length != 0)
                {
                    sb.AppendLine("\n\n## <font color='warning'>错误日志:</font>\n" + errorstr);
                }
                WeChatTool.SendTxt(ROBOT_KEY, sb.ToString(), true);
            }
            else
            {
                WeChatTool.SendTxt(ROBOT_KEY, simpleContent, true);
                WeChatTool.SendFile(ROBOT_KEY, "日志.txt", logstr);

                if (errorstr.Length > 0)
                {
                    WeChatTool.SendFile(ROBOT_KEY, "错误日志.txt", errorstr);
                }
            }
#endif
        }

        public static void DisplayHierarchy(Transform t)
        {
            StringBuilder sb = new StringBuilder();
            DisplayHierarchy(sb, t);
            Diagnostic.Log(sb.ToString());
        }

        private static void DisplayHierarchy(StringBuilder sb, Transform t, int depth = 0)
        {
            for (int i = 0; i < depth; ++i)
                sb.Append(" ");
            sb.Append(t.name);
            var components = t.GetComponents<Component>();
            foreach (var item in components)
            {
                if (item == null) continue;
                sb.Append("|").Append(item.GetType().Name);
                if (item.GetType() == typeof(Canvas))
                    sb.Append("_rm:").Append((item as Canvas).renderMode);
            }
            sb.AppendLine();

            foreach (Transform child in t)
            {
                DisplayHierarchy(sb, child, ++depth);
            }
        }

        public static bool CheckAndModifyPrefab(string assetPath)
        {
            PrefabInstance.EditorEnable = false;
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            Diagnostic.Log("[CheckAndModifyPrefab] show prefab asset");
            DisplayHierarchy(prefab.transform);
            var stage = OpenPrefab(assetPath);
            if (stage == null)
                return false;
            Diagnostic.Log("[CheckAndModifyPrefab] show start prefab");
            DisplayHierarchy(stage.prefabContentsRoot.transform);
            bool hasChanged = AutoFixPlayableDirector(stage.prefabContentsRoot);

            // UI的话 需要复杂一些
            if (isUIPrefab(assetPath))
            {
                hasChanged |= ApplyPrefabInstance(stage.prefabContentsRoot, assetPath);
            }

            if (hasChanged)
            {
                SavePrefab(stage);
            }
            Diagnostic.Log("[CheckAndModifyPrefab] show end prefab");
            DisplayHierarchy(stage.prefabContentsRoot.transform);
            ClosePrefab();
            PrefabInstance.EditorEnable = true;
            return hasChanged;
        }

        private static bool ApplyPrefabInstance_Impl(GameObject prefab, bool testMode)
        {
            bool hasChanged = false;
            var gfxRoots = prefab.GetComponentsInChildren<GfxFramework.GfxRoot_Unity>(true);
            foreach (var gfxRoot in gfxRoots)
            {
                if (gfxRoot == null)
                    continue;
                if (!gfxRoot.EnableOptimize)
                {
                    var parent = gfxRoot.transform.parent;
                    if (parent != null)
                    {
                        if (PrefabInstanceEditor.DestoryPrefabInstance(parent, testMode))
                        {
                            Diagnostic.Log("has changed: " + gfxRoot.name + " need revert optimize");
                            hasChanged = true;
                        }
                    }
                    continue;
                }
                if (PrefabInstanceEditor.ConvertToPrefabInstance(gfxRoot.gameObject, testMode, out bool valueHasChanged) == PrefabInstanceEditor.ConvertResult.Success || valueHasChanged)
                {
                    hasChanged = true;
                }
            }
            return hasChanged;
        }

        private static bool FixCanvas(GameObject prefab)
        {
            // 不准设置为ScreenSpaceOverlay 这种方式会导致这个节点的scale被设置为0
            var canvas = prefab.GetComponentsInChildren<Canvas>(true);
            bool hasChanged = false;
            foreach (var item in canvas)
            {
                if (item.renderMode == RenderMode.ScreenSpaceOverlay)
                {
                    item.renderMode = RenderMode.WorldSpace;
                    EditorUtility.SetDirty(item);
                    hasChanged = true;
                    Diagnostic.Log("has changed: " + item.name + " has error render mode");
                }
            }
            return hasChanged;
        }

        public static bool ApplyPrefabInstance(GameObject prefab, string assetPath)
        {
            bool hasChanged = false;
            //var parentcanvas = prefab.GetComponentsInParent<Canvas>();
            //if (parentcanvas != null)
            //{
            //    foreach (var c in parentcanvas)
            //    {
            //        Diagnostic.Log("parent canvas: " + c.name);
            //    }
            //}
            //hasChanged |= FixCanvas(prefab);
            hasChanged |= ApplyPrefabInstance_Impl(prefab, false);

            //if (hasChanged)
            //{
            //    GameObjectList[] gameObjectLists = prefab.GetComponentsInChildren<GameObjectList>(true);
            //    if (gameObjectLists != null && gameObjectLists.Length > 0)
            //    {
            //        if (UpdatePrefab(gameObjectLists, assetPath))
            //        {
            //            Diagnostic.Log("[ApplyPrefabInstance] update prefab");
            //            hasChanged = true;
            //        }
            //    }
            //}

            return hasChanged;
        }

        public static bool AutoFixPlayableDirector(GameObject prefab)
        {
            var playables = prefab.GetComponentsInChildren<PlayableDirector>(true);
            bool hasChanged = false;
            foreach (var item in playables)
            {
                hasChanged |= AutoFixPlayableDirector(item);
            }
            if (hasChanged)
                UnityEngine.Debug.LogWarning(prefab.name + " fix PlayableDirector.");
            return hasChanged;
        }

        public static bool AutoFixPlayableDirector(PlayableDirector playable)
        {
            Dictionary<UnityEngine.Object, UnityEngine.Object> bindings = new Dictionary<UnityEngine.Object, UnityEngine.Object>();
            if (playable.playableAsset != null)
            {
                foreach (var pb in playable.playableAsset.outputs)
                {
                    var key = pb.sourceObject;
                    var value = playable.GetGenericBinding(key);
                    if (!bindings.ContainsKey(key) && key != null)
                    {
                        bindings.Add(key, value);
                    }
                }
            }

            bool hasChanged = false;
            var dirSO = new SerializedObject(playable);
            var sceneBindings = dirSO.FindProperty("m_SceneBindings");
            for (var i = sceneBindings.arraySize - 1; i >= 0; i--)
            {
                var binding = sceneBindings.GetArrayElementAtIndex(i);
                var key = binding.FindPropertyRelative("key");
                if (key.objectReferenceValue == null || !bindings.ContainsKey(key.objectReferenceValue))
                {
                    Diagnostic.Log("has changed: " + playable.name + " has error key");
                    hasChanged = true;
                    sceneBindings.DeleteArrayElementAtIndex(i);
                }
            }
            if (hasChanged)
                dirSO.ApplyModifiedProperties();

            return hasChanged;
        }

        #region 收缩prefab的effect以及检测一些问题

        [ContextMenu("GameObject/美术资源优化/自动修复Timeline问题")]
        public static void AutoFixPlayableDirector()
        {
            var assetPath = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(assetPath))
            {
                Diagnostic.Error(" asset path error");
                return;
            }

            var prefab = PrefabUtility.LoadPrefabContents(assetPath);
            bool hasChanged = AutoFixPlayableDirector(prefab);
            if (hasChanged)
            {
                PrefabUtility.SaveAsPrefabAsset(prefab, assetPath);
            }
            PrefabUtility.UnloadPrefabContents(prefab);
        }

        private static MethodInfo OpenPrefabMethod;
        private static MethodInfo SavePrefabMethod;

        /// <summary>
        /// 使用PrefabUtility.LoadPrefabContents去打开UGUI的界面，然后保存的话 可能会造成一些以外的数据被更改，例如Canvas(RenderMode=ScreenSpace-overlay)的scale会设置为0
        /// 而Unity的OpenPrefab规避了这个bug，他会将UI设置为preview模式。整个过程与我们双击打开prefab，然后操作+保存的流程是一致的
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static PrefabStage OpenPrefab(string path)
        {
            if (OpenPrefabMethod == null)
            {
                OpenPrefabMethod = typeof(PrefabStageUtility).GetMethods(BindingFlags.Static | BindingFlags.NonPublic).FirstOrDefault(m => m.Name == "OpenPrefab" && m.GetParameters().Length == 1);
            }
            if (OpenPrefabMethod != null)
            {
                PrefabStage stage = (PrefabStage)OpenPrefabMethod.Invoke(null, new object[] { path });
                return stage;
            }
            else
            {
                Diagnostic.Error("OpenPrefabMethod is null. OpenPrefab faild.");
            }
            return null;
        }

        public static void SavePrefab(PrefabStage stage)
        {
            if (SavePrefabMethod == null)
                SavePrefabMethod = typeof(PrefabStage).GetMethod("SavePrefabWithVersionControlDialogAndRenameDialog", BindingFlags.Instance | BindingFlags.NonPublic);
            if (SavePrefabMethod != null)
            {
                SavePrefabMethod.Invoke(stage, null);
            }
            else
            {
                Diagnostic.Error("SavePrefabMethod is null. SavePrefab faild");
            }
        }

        public static void ClosePrefab()
        {
            UnityEditor.SceneManagement.StageUtility.GoToMainStage();
        }

        #endregion
    }
}
