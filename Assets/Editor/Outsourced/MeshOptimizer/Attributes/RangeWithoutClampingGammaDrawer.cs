using UnityEngine;
using UnityEditor;

namespace MeshOptimizer
{
    /// <summary>
    /// 自定义PropertyDrawer：实现带gamma映射的不限制范围的滑动条
    /// </summary>
    [CustomPropertyDrawer(typeof(RangeWithoutClampingGammaAttribute))]
    public class RangeWithoutClampingGammaDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            // 获取自定义属性
            RangeWithoutClampingGammaAttribute rangeAttr = attribute as RangeWithoutClampingGammaAttribute;

            // 只支持浮点数
            if (property.propertyType != SerializedPropertyType.Float)
            {
                EditorGUI.LabelField(position, label, new GUIContent("只支持浮点数"));
                return;
            }

            // 分割区域 - 输入框放在滑杆右侧
            Rect labelRect = new Rect(position.x, position.y, EditorGUIUtility.labelWidth, position.height);
            // 滑杆放在标签后面
            Rect sliderRect = new Rect(position.x + EditorGUIUtility.labelWidth, position.y,
                                      position.width - EditorGUIUtility.labelWidth - 55, position.height);
            // 输入框放在最右侧
            Rect fieldRect = new Rect(position.x + position.width - 50, position.y, 50, position.height);

            // 绘制标签
            EditorGUI.LabelField(labelRect, label);

            // 记录初始值
            float originalValue = property.floatValue;

            // 绘制输入框（不限制范围）
            EditorGUI.BeginChangeCheck();
            float inputValue = EditorGUI.DelayedFloatField(fieldRect, GUIContent.none, originalValue);
            bool inputChanged = EditorGUI.EndChangeCheck();

            // 将原始值转换为gamma空间用于滑动条
            float normalizedValue = Mathf.InverseLerp(rangeAttr.min, rangeAttr.max, Mathf.Clamp(originalValue, rangeAttr.min, rangeAttr.max));
            float gammaValue = Mathf.Pow(normalizedValue, 1.0f / rangeAttr.gamma);

            // 绘制滑动条（在gamma空间）- 不显示当前值
            EditorGUI.BeginChangeCheck();
            // 使用GUILayout.HorizontalSlider代替EditorGUI.Slider，不显示当前值
            float newGammaValue = GUI.HorizontalSlider(sliderRect, gammaValue, 0f, 1f);
            bool sliderChanged = EditorGUI.EndChangeCheck();

            // 更新属性值
            if (sliderChanged)
            {
                // 将gamma空间的值转换回原始空间
                float newNormalizedValue = Mathf.Pow(newGammaValue, rangeAttr.gamma);
                float newValue = Mathf.Lerp(rangeAttr.min, rangeAttr.max, newNormalizedValue);
                property.floatValue = newValue;
            }
            else if (inputChanged)
            {
                // 直接使用输入值，不限制范围
                property.floatValue = inputValue;
            }
        }
    }
}
