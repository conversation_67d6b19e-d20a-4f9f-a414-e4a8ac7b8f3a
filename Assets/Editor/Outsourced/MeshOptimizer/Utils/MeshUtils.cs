using UnityEngine;
using System.Collections.Generic;

namespace MeshOptimizer.Utils
{
    /// <summary>
    /// 网格处理工具类
    /// </summary>
    public static class MeshUtils
    {
        /// <summary>
        /// 创建一个断开三角形的网格，每个三角形使用自己独立的3个顶点
        /// </summary>
        /// <param name="sourceMesh">源网格</param>
        /// <returns>断开三角形的新网格</returns>
        public static Mesh CreateDisconnectedTrianglesMesh(Mesh sourceMesh)
        {
            if (sourceMesh == null)
                return null;

            // 获取源网格数据
            Vector3[] sourceVertices = sourceMesh.vertices;
            Vector2[] sourceUVs = sourceMesh.uv;
            Vector3[] sourceNormals = sourceMesh.normals;
            Color[] sourceColors = sourceMesh.colors;
            int[] sourceTriangles = sourceMesh.triangles;

            // 计算新网格的顶点数量（每个三角形3个顶点）
            int triangleCount = sourceTriangles.Length / 3;
            int newVertexCount = triangleCount * 3;

            // 创建新的数据数组
            Vector3[] newVertices = new Vector3[newVertexCount];
            Vector2[] newUVs = new Vector2[newVertexCount];
            Vector3[] newNormals = sourceNormals.Length > 0 ? new Vector3[newVertexCount] : null;
            Color[] newColors = sourceColors.Length > 0 ? new Color[newVertexCount] : null;
            int[] newTriangles = new int[sourceTriangles.Length];

            // 填充新数据
            for (int i = 0; i < triangleCount; i++)
            {
                int sourceIndex1 = sourceTriangles[i * 3];
                int sourceIndex2 = sourceTriangles[i * 3 + 1];
                int sourceIndex3 = sourceTriangles[i * 3 + 2];

                int newIndex1 = i * 3;
                int newIndex2 = i * 3 + 1;
                int newIndex3 = i * 3 + 2;

                // 复制顶点
                newVertices[newIndex1] = sourceVertices[sourceIndex1];
                newVertices[newIndex2] = sourceVertices[sourceIndex2];
                newVertices[newIndex3] = sourceVertices[sourceIndex3];

                // 复制UV（如果有）
                if (sourceUVs.Length > 0)
                {
                    newUVs[newIndex1] = sourceUVs[sourceIndex1];
                    newUVs[newIndex2] = sourceUVs[sourceIndex2];
                    newUVs[newIndex3] = sourceUVs[sourceIndex3];
                }

                // 复制法线（如果有）
                if (newNormals != null)
                {
                    newNormals[newIndex1] = sourceNormals[sourceIndex1];
                    newNormals[newIndex2] = sourceNormals[sourceIndex2];
                    newNormals[newIndex3] = sourceNormals[sourceIndex3];
                }

                // 复制顶点颜色（如果有）
                if (newColors != null)
                {
                    newColors[newIndex1] = sourceColors[sourceIndex1];
                    newColors[newIndex2] = sourceColors[sourceIndex2];
                    newColors[newIndex3] = sourceColors[sourceIndex3];
                }

                // 设置新的三角形索引
                newTriangles[i * 3] = newIndex1;
                newTriangles[i * 3 + 1] = newIndex2;
                newTriangles[i * 3 + 2] = newIndex3;
            }

            // 创建新网格
            Mesh newMesh = new Mesh();
            newMesh.name = sourceMesh.name + "_Disconnected";
            newMesh.vertices = newVertices;
            newMesh.triangles = newTriangles;
            
            // 设置UV（如果有）
            if (sourceUVs.Length > 0)
                newMesh.uv = newUVs;
                
            // 设置法线（如果有）
            if (newNormals != null)
                newMesh.normals = newNormals;
                
            // 设置顶点颜色（如果有）
            if (newColors != null)
                newMesh.colors = newColors;
                
            // 重新计算边界
            newMesh.RecalculateBounds();
            
            // 如果没有法线，重新计算法线
            if (newNormals == null)
                newMesh.RecalculateNormals();

            return newMesh;
        }
    }
}
