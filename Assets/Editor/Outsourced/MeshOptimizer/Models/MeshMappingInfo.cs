using UnityEngine;
using System.Collections.Generic;
using MeshOptimizer.Models;

namespace MeshOptimizer.Models
{
    /// <summary>
    /// 网格映射信息类，存储原始Mesh与合并Mesh的对应关系
    /// </summary>
    [System.Serializable]
    public class MeshMappingInfo
    {
        // 原始处理数据引用
        private MeshProcessingData _originalData;

        // 顶点索引映射
        private int _startVertexIndex; // 在合并Mesh中的起始顶点索引
        private int _vertexCount;      // 顶点数量

        // 三角形索引映射
        private int _startTriangleIndex; // 在合并Mesh中的起始三角形索引
        private int _triangleCount;      // 三角形数量

        // 子网格索引
        private int _submeshIndex;     // 在合并Mesh中的子网格索引

        // 变换矩阵
        private Matrix4x4 _localToWorldMatrix; // 原始对象的本地到世界空间变换矩阵
        private Matrix4x4 _worldToLocalMatrix; // 原始对象的世界到本地空间变换矩阵

        // UV映射信息
        private Rect _uvRect;          // UV在Atlas中的矩形区域

        // 三角形映射
        private Dictionary<int, int[]> _triangleToVerticesMap; // 三角形索引到顶点索引的映射
        private int[] _originalTriangles; // 原始三角形索引数组

        /// <summary>
        /// 原始处理数据引用
        /// </summary>
        public MeshProcessingData OriginalData
        {
            get => _originalData;
            set => _originalData = value;
        }

        /// <summary>
        /// 在合并Mesh中的起始顶点索引
        /// </summary>
        public int StartVertexIndex
        {
            get => _startVertexIndex;
            set => _startVertexIndex = value;
        }

        /// <summary>
        /// 顶点数量
        /// </summary>
        public int VertexCount
        {
            get => _vertexCount;
            set => _vertexCount = value;
        }

        /// <summary>
        /// 在合并Mesh中的起始三角形索引
        /// </summary>
        public int StartTriangleIndex
        {
            get => _startTriangleIndex;
            set => _startTriangleIndex = value;
        }

        /// <summary>
        /// 三角形数量
        /// </summary>
        public int TriangleCount
        {
            get => _triangleCount;
            set => _triangleCount = value;
        }

        /// <summary>
        /// 在合并Mesh中的子网格索引
        /// </summary>
        public int SubmeshIndex
        {
            get => _submeshIndex;
            set => _submeshIndex = value;
        }

        /// <summary>
        /// 原始对象的本地到世界空间变换矩阵
        /// </summary>
        public Matrix4x4 LocalToWorldMatrix
        {
            get => _localToWorldMatrix;
            set => _localToWorldMatrix = value;
        }

        /// <summary>
        /// 原始对象的世界到本地空间变换矩阵
        /// </summary>
        public Matrix4x4 WorldToLocalMatrix
        {
            get => _worldToLocalMatrix;
            set => _worldToLocalMatrix = value;
        }

        /// <summary>
        /// UV在Atlas中的矩形区域
        /// </summary>
        public Rect UVRect
        {
            get => _uvRect;
            set => _uvRect = value;
        }

        /// <summary>
        /// 三角形索引到顶点索引的映射
        /// </summary>
        public Dictionary<int, int[]> TriangleToVerticesMap
        {
            get => _triangleToVerticesMap;
            set => _triangleToVerticesMap = value;
        }

        /// <summary>
        /// 原始三角形索引数组
        /// </summary>
        public int[] OriginalTriangles
        {
            get => _originalTriangles;
            set => _originalTriangles = value;
        }

        /// <summary>
        /// 创建一个新的网格映射信息实例
        /// </summary>
        public MeshMappingInfo()
        {
            // 使用默认值
            _triangleToVerticesMap = new Dictionary<int, int[]>();
        }

        /// <summary>
        /// 使用原始处理数据创建一个新的网格映射信息实例
        /// </summary>
        /// <param name="originalData">原始处理数据</param>
        public MeshMappingInfo(MeshProcessingData originalData)
        {
            _originalData = originalData;
            _triangleToVerticesMap = new Dictionary<int, int[]>();

            // 如果原始数据有GameObject，获取其变换矩阵
            if (originalData != null && originalData.OriginalGameObject != null)
            {
                Transform transform = originalData.OriginalGameObject.transform;
                _localToWorldMatrix = transform.localToWorldMatrix;
                _worldToLocalMatrix = transform.worldToLocalMatrix;
            }
            else
            {
                // 如果没有GameObject，使用单位矩阵
                _localToWorldMatrix = Matrix4x4.identity;
                _worldToLocalMatrix = Matrix4x4.identity;
            }

            // 保存原始三角形数据
            if (originalData != null && originalData.OptimizedMesh != null)
            {
                _originalTriangles = originalData.OptimizedMesh.triangles;
            }
        }

        /// <summary>
        /// 获取顶点索引范围
        /// </summary>
        /// <returns>顶点索引范围（起始索引，结束索引）</returns>
        public (int start, int end) GetVertexIndexRange()
        {
            return (_startVertexIndex, _startVertexIndex + _vertexCount - 1);
        }

        /// <summary>
        /// 获取三角形索引范围
        /// </summary>
        /// <returns>三角形索引范围（起始索引，结束索引）</returns>
        public (int start, int end) GetTriangleIndexRange()
        {
            return (_startTriangleIndex, _startTriangleIndex + _triangleCount - 1);
        }

        /// <summary>
        /// 检查顶点索引是否属于此映射
        /// </summary>
        /// <param name="vertexIndex">要检查的顶点索引</param>
        /// <returns>是否属于此映射</returns>
        public bool ContainsVertexIndex(int vertexIndex)
        {
            return vertexIndex >= _startVertexIndex && vertexIndex < _startVertexIndex + _vertexCount;
        }

        /// <summary>
        /// 检查三角形索引是否属于此映射
        /// </summary>
        /// <param name="triangleIndex">要检查的三角形索引</param>
        /// <returns>是否属于此映射</returns>
        public bool ContainsTriangleIndex(int triangleIndex)
        {
            return triangleIndex >= _startTriangleIndex && triangleIndex < _startTriangleIndex + _triangleCount;
        }

        /// <summary>
        /// 将全局顶点索引转换为局部顶点索引
        /// </summary>
        /// <param name="globalIndex">全局顶点索引</param>
        /// <returns>局部顶点索引</returns>
        public int GlobalToLocalVertexIndex(int globalIndex)
        {
            if (!ContainsVertexIndex(globalIndex))
                return -1;

            return globalIndex - _startVertexIndex;
        }

        /// <summary>
        /// 将局部顶点索引转换为全局顶点索引
        /// </summary>
        /// <param name="localIndex">局部顶点索引</param>
        /// <returns>全局顶点索引</returns>
        public int LocalToGlobalVertexIndex(int localIndex)
        {
            if (localIndex < 0 || localIndex >= _vertexCount)
                return -1;

            return _startVertexIndex + localIndex;
        }

        /// <summary>
        /// 存储三角形到顶点的映射关系
        /// </summary>
        /// <param name="combinedMesh">合并后的网格</param>
        public void StoreTriangleToVertexMapping(Mesh combinedMesh)
        {
            if (combinedMesh == null)
                return;

            _triangleToVerticesMap.Clear();

            // 获取子网格的三角形
            int[] submeshTriangles = combinedMesh.GetTriangles(_submeshIndex);

            // 遍历所有三角形，找出属于当前映射的三角形
            for (int i = 0; i < submeshTriangles.Length; i += 3)
            {
                int v1 = submeshTriangles[i];
                int v2 = submeshTriangles[i + 1];
                int v3 = submeshTriangles[i + 2];

                // 检查三角形的所有顶点是否属于当前映射
                if (ContainsVertexIndex(v1) && ContainsVertexIndex(v2) && ContainsVertexIndex(v3))
                {
                    // 计算三角形索引（相对于子网格）
                    int triangleIndex = i / 3;

                    // 存储三角形到顶点的映射
                    _triangleToVerticesMap[triangleIndex] = new int[] { v1, v2, v3 };
                }
            }

            // 存储完成
        }

        /// <summary>
        /// 更新顶点索引映射（在UV展开后调用）
        /// </summary>
        /// <param name="combinedMesh">合并后的网格（已展开UV）</param>
        public void UpdateVertexMapping(Mesh combinedMesh)
        {
            if (combinedMesh == null)
                return;

            // 获取子网格的三角形
            int[] submeshTriangles = combinedMesh.GetTriangles(_submeshIndex);

            // 创建新的三角形到顶点的映射
            Dictionary<int, int[]> newMapping = new Dictionary<int, int[]>();

            // 遍历所有三角形
            for (int i = 0; i < submeshTriangles.Length; i += 3)
            {
                int v1 = submeshTriangles[i];
                int v2 = submeshTriangles[i + 1];
                int v3 = submeshTriangles[i + 2];

                // 计算三角形索引（相对于子网格）
                int triangleIndex = i / 3;

                // 存储新的映射
                newMapping[triangleIndex] = new int[] { v1, v2, v3 };
            }

            // 更新映射
            _triangleToVerticesMap = newMapping;

            // 更新顶点数量（可能已经改变）
            _vertexCount = combinedMesh.vertexCount - _startVertexIndex;

            // UV展开后更新完成
        }
    }
}
