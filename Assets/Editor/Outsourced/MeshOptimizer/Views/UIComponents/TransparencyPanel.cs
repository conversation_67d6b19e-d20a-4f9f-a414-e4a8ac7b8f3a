using UnityEngine;
using UnityEditor;
using MeshOptimizer.Controllers;
using MeshOptimizer.Models;
using MeshOptimizer.Utilities;
using MeshOptimizer.Services;
using MeshOptimizer.Events;
using MeshOptimizer.Repository;
using System.Collections.Generic;
using System;

namespace MeshOptimizer.Views.UIComponents
{
    /// <summary>
    /// 透明度分析面板，用于分析和显示网格的透明度
    /// </summary>
    public class TransparencyPanel : PanelBase
    {
        // 控制器和仓库
        private WorkflowController _workflowController;
        private TransparencyController _transparencyController;
        private PreviewController _previewController;
        private IEventSystem _eventSystem;
        private IProcessingDataRepository _repository;

        // 处理参数
        private ProcessingParameters _parameters;

        // 预览相关
        private bool _showTriangleTransparency = false; // 是否显示三角面透明度

        // 预览旋转控制
        private Vector2 _previewRotation = new Vector2(20f, 30f);
        private float _previewZoom = 1.0f;

        // 透明度阈值 (0-1)
        private float _alphaThreshold = 0.5f;

        // Alpha显示强度 (0-1)
        private float _alphaVisStrength = 0.7f;

        // 选中对象索引
        private int _selectedIndex = -1;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="title">面板标题</param>
        public TransparencyPanel(string title) : base(title)
        {
        }

        /// <summary>
        /// 初始化面板
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            // 获取控制器和仓库
            _workflowController = WorkflowController.Instance;
            _workflowController.Initialize(); // 确保WorkflowController已初始化

            _eventSystem = EventSystem.Instance;

            // 使用WorkflowController中的Repository，而不是创建新的
            _repository = _workflowController.GetRepository();

            _transparencyController = TransparencyController.Instance;
            _transparencyController.Initialize();

            _previewController = PreviewController.Instance;
            _previewController.Initialize();

            // 获取处理参数
            _parameters = _repository.GetParameters();

            // 获取当前选中的对象索引
            _selectedIndex = _repository.GetSelectedObjectIndex();

            // 不再需要创建临时透明度材质和预览渲染工具，这些已移至PreviewService

            // 订阅事件
            _eventSystem.Subscribe<AlphaThresholdChangedEvent>(OnAlphaThresholdChanged);
            _eventSystem.Subscribe<MeshCulledEvent>(OnMeshCulled);
            _eventSystem.Subscribe<SelectedObjectChangedEvent>(OnSelectedObjectChanged);

            // 注册全局事件处理
            EditorApplication.update += OnEditorUpdate;

            base.Initialize();
        }

        /// <summary>
        /// 编辑器更新事件
        /// </summary>
        private void OnEditorUpdate()
        {
            // 检查并清理无效对象
            CheckAndCleanInvalidObjects();
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            // 取消注册全局事件处理
            EditorApplication.update -= OnEditorUpdate;

            // 取消订阅事件
            if (_eventSystem != null)
            {
                _eventSystem.Unsubscribe<AlphaThresholdChangedEvent>(OnAlphaThresholdChanged);
                _eventSystem.Unsubscribe<MeshCulledEvent>(OnMeshCulled);
                _eventSystem.Unsubscribe<SelectedObjectChangedEvent>(OnSelectedObjectChanged);
            }

            // 清理控制器
            if (_transparencyController != null)
            {
                _transparencyController.Cleanup();
            }

            if (_previewController != null)
            {
                _previewController.Cleanup();
            }

            base.Cleanup();
        }

        /// <summary>
        /// 绘制面板内容
        /// </summary>
        protected override void DrawContent()
        {
            // 检查参数是否有效
            if (_parameters == null)
            {
                EditorGUILayout.HelpBox("处理参数未初始化", MessageType.Error);
                return;
            }

            // 检查是否有选中的对象
            List<MeshProcessingData> processingDataList = _repository.GetAllProcessingData();
            if (processingDataList == null || processingDataList.Count == 0)
            {
                EditorGUILayout.HelpBox("请先选择要处理的对象", MessageType.Info);
                return;
            }

            // 绘制透明度分析参数
            DrawTransparencyParameters();

            // 预览内容
            DrawPreviewContent();

            // 绘制处理按钮
            DrawProcessButtons();
        }

        /// <summary>
        /// 直接绘制面板，不使用折叠栏
        /// </summary>
        public void DrawPanelDirect()
        {
            // 检查参数是否有效
            if (_parameters == null)
            {
                EditorGUILayout.HelpBox("处理参数未初始化", MessageType.Error);
                return;
            }

            // 检查是否有选中的对象
            List<MeshProcessingData> processingDataList = _repository.GetAllProcessingData();
            if (processingDataList == null || processingDataList.Count == 0)
            {
                EditorGUILayout.HelpBox("请先选择要处理的对象", MessageType.Info);
                return;
            }

            // 绘制透明度分析参数
            DrawTransparencyParameters();

            // 预览内容
            DrawPreviewContent();

            // 绘制处理按钮
            DrawProcessButtons();
        }

        /// <summary>
        /// 绘制透明度分析参数
        /// </summary>
        private void DrawTransparencyParameters()
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.LabelField("透明度分析参数", EditorStyles.boldLabel);

            // 透明度阈值 - 使用对数尺度实现更精确的极小值控制
            float currentAlphaThreshold = _transparencyController.GetAlphaThreshold();

            // 获取矩形区域 - 输入框放在滑杆右侧
            Rect rect = EditorGUILayout.GetControlRect();
            Rect labelRect = new Rect(rect.x, rect.y, EditorGUIUtility.labelWidth, rect.height);
            // 滑杆放在标签后面
            Rect sliderRect = new Rect(rect.x + EditorGUIUtility.labelWidth, rect.y,
                                      rect.width - EditorGUIUtility.labelWidth - 55, rect.height);
            // 输入框放在最右侧
            Rect fieldRect = new Rect(rect.x + rect.width - 50, rect.y, 50, rect.height);

            // 绘制标签
            EditorGUI.LabelField(labelRect, "透明度阈值");

            // 绘制输入框（不限制范围）
            EditorGUI.BeginChangeCheck();
            float inputValue = EditorGUI.DelayedFloatField(fieldRect, GUIContent.none, currentAlphaThreshold);
            bool inputChanged = EditorGUI.EndChangeCheck();

            // 定义阈值范围
            float minThreshold = 1e-10f; // 非常小的最小值，接近0但不为0
            float maxThreshold = 0.3f;   // 最大阈值保持不变

            // 将当前阈值转换为滑动条值 (0-1)，但颠倒方向
            // 高阈值(裁剪更多)对应滑动条左侧(0)，低阈值(保留更多)对应滑动条右侧(1)
            float sliderValue = 1.0f - TransparencyPrecisionUtility.ThresholdToSlider(currentAlphaThreshold, minThreshold, maxThreshold);

            // 绘制滑动条 - 不显示当前值
            EditorGUI.BeginChangeCheck();
            float newSliderValue = GUI.HorizontalSlider(sliderRect, sliderValue, 0f, 1f);
            bool sliderChanged = EditorGUI.EndChangeCheck();

            // 更新属性值
            float newAlphaThreshold = currentAlphaThreshold;
            if (sliderChanged)
            {
                // 将滑动条值转换回实际阈值，注意颠倒方向
                newAlphaThreshold = TransparencyPrecisionUtility.SliderToThreshold(1.0f - newSliderValue, minThreshold, maxThreshold);
            }
            else if (inputChanged)
            {
                // 直接使用输入值，不限制范围
                newAlphaThreshold = inputValue;
            }

            // 如果值发生变化，更新控制器
            if (newAlphaThreshold != currentAlphaThreshold)
            {
                _alphaThreshold = newAlphaThreshold;

                // 调用控制器处理阈值变更和剔除逻辑
                _transparencyController.SetAlphaThreshold(newAlphaThreshold);

                // 更新着色器参数
                _previewController.SetVisualizationParameters(
                    TransparencyVisualizerUtility.VisualizationMode.Heatmap,
                    _alphaThreshold,
                    _showTriangleTransparency,
                    _alphaVisStrength);

                // 实时更新预览
                EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
            }

            // 采样密度
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PrefixLabel(new GUIContent("采样密度", "低=1点, 中=~100点, 高=~500点"));
            string[] samplingDensityOptions = new string[] { "低 (1点)", "中 (~100点)", "高 (~500点)" };
            int currentSamplingDensity = _parameters.SamplingDensity;
            int newSamplingDensity = EditorGUILayout.Popup(currentSamplingDensity, samplingDensityOptions);
            EditorGUILayout.EndHorizontal();

            if (newSamplingDensity != currentSamplingDensity)
            {
                _parameters.SamplingDensity = newSamplingDensity;
            }

            // 使用Compute Shader
            bool currentUseComputeShader = _parameters.UseComputeShader;
            bool newUseComputeShader = EditorGUILayout.Toggle("使用GPU加速", currentUseComputeShader);

            if (newUseComputeShader != currentUseComputeShader)
            {
                // 更新参数
                _parameters.UseComputeShader = newUseComputeShader;

                // 重置预览状态

                // 如果当前有选中对象且已分析透明度，立即更新预览
                MeshProcessingData dataToUpdate = _repository.GetProcessingData(_selectedIndex);
                if (dataToUpdate != null && dataToUpdate.IsTransparencyAnalyzed)
                {
                    // 强制重新分析当前对象，以更新透明度数据
                    if (newUseComputeShader)
                    {
                        // 切换到GPU模式，需要重新生成ComputeBuffer
                        _transparencyController.RefreshTransparencyData(dataToUpdate);
                    }
                }

                // 刷新预览窗口，确保使用最新的数据
                RefreshPreview();
            }



            EditorGUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制预览内容
        /// </summary>
        private void DrawPreviewContent()
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // 预览标题和显示模式切换
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("透明度预览", EditorStyles.boldLabel, GUILayout.ExpandWidth(true));

            // 显示三角面透明度切换
            bool prevShowTriangleTransparency = _showTriangleTransparency;
            _showTriangleTransparency = EditorGUILayout.Toggle(
                new GUIContent("显示三角面透明度", "显示逐三角面的透明度，即compute shader的计算结果"),
                _showTriangleTransparency);

            // 如果开关状态变化，确保使用正确的mesh并更新着色器参数
            if (prevShowTriangleTransparency != _showTriangleTransparency)
            {
                // 更新着色器参数
                _previewController.SetVisualizationParameters(
                    TransparencyVisualizerUtility.VisualizationMode.Heatmap,
                    _alphaThreshold,
                    _showTriangleTransparency,
                    _alphaVisStrength);

                // 如果关闭了显示三角面透明度，重置预览状态
                if (!_showTriangleTransparency)
                {
                    // 不需要特殊处理
                }
                // 获取当前选中的对象和mesh信息
                MeshProcessingData dataForPreview = _repository.GetProcessingData(_selectedIndex);
                if (dataForPreview != null)
                {
                    // 如果开启了显示三角面透明度
                    if (_showTriangleTransparency)
                    {
                        // 检查当前选中对象是否已经分析过透明度
                        if (!dataForPreview.IsTransparencyAnalyzed)
                        {
                            // 如果未分析过透明度，自动调用"分析选中对象"按钮的逻辑
                            try
                            {
                                // 确保透明度控制器已初始化
                                if (_transparencyController == null)
                                {
                                    _transparencyController = TransparencyController.Instance;
                                }

                                // 初始化透明度控制器
                                _transparencyController.Initialize();

                                // 调用透明度分析控制器分析选中对象
                                ProcessingResult result = _transparencyController.AnalyzeSelectedObject();

                                // 获取当前选中对象的信息
                                MeshProcessingData dataAfterAnalysis = _repository.GetProcessingData(_selectedIndex);

                                if (dataAfterAnalysis != null && dataAfterAnalysis.IsTransparencyAnalyzed)
                                {
                                    // 添加触发逻辑：分析完成后立即执行剔除操作
                                    float currentThreshold = _transparencyController.GetAlphaThreshold();

                                    // 获取剔除控制器并确保初始化
                                    var cullingController = CullingController.Instance;
                                    cullingController.Initialize();

                                    // 执行剔除操作
                                    bool cullResult = cullingController.CullMesh(_selectedIndex, currentThreshold, false);

                                    if (cullResult)
                                    {
                                        // 更新处理数据
                                        _repository.UpdateProcessingData(_selectedIndex, dataAfterAnalysis);
                                    }
                                }
                                // 自动分析透明度失败，无法显示三角面透明度
                            }
                            catch (System.Exception)
                            {
                                // 自动分析透明度时出错
                            }
                        }

                        // 使用PreviewController获取预览网格
                        _previewController.GetPreviewMesh(dataForPreview, true);
                    }

                    // 切换显示模式
                }

                // 刷新预览窗口，确保使用最新的数据
                RefreshPreview();
            }
            EditorGUILayout.EndHorizontal();

            // 仅当显示三角面透明度时显示Alpha显示强度滑块
            if (_showTriangleTransparency)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.PrefixLabel("Alpha显示强度");
                float newAlphaVisStrength = EditorGUILayout.Slider(_alphaVisStrength, 0f, 1f);
                EditorGUILayout.EndHorizontal();

                if (newAlphaVisStrength != _alphaVisStrength)
                {
                    _alphaVisStrength = newAlphaVisStrength;

                    // 更新着色器参数
                    _previewController.SetVisualizationParameters(
                        TransparencyVisualizerUtility.VisualizationMode.Heatmap,
                        _alphaThreshold,
                        _showTriangleTransparency,
                        _alphaVisStrength);

                    // 刷新预览窗口，确保使用最新的数据
                    RefreshPreview();
                }
            }

            // 使用"已选对象列表"当前选中的对象
            List<MeshProcessingData> processingDataList = _repository.GetAllProcessingData();
            if (processingDataList != null && processingDataList.Count > 0)
            {
                // 获取SelectionPanel中选中的对象索引
                int selectionPanelSelectedIndex = _repository.GetSelectedObjectIndex();

                // 如果SelectionPanel有选中对象，使用它
                if (selectionPanelSelectedIndex >= 0 && selectionPanelSelectedIndex < processingDataList.Count)
                {
                    _selectedIndex = selectionPanelSelectedIndex;
                }

                // 检查选中的对象是否有效
                MeshProcessingData dataToCheck = _repository.GetProcessingData(_selectedIndex);
                if (dataToCheck == null || dataToCheck.OriginalGameObject == null)
                {
                    // 当前选中的对象无效，显示提示信息
                    EditorGUILayout.HelpBox("选中的对象已被删除", MessageType.Warning);
                    EditorGUILayout.EndVertical();
                    return;
                }

                // 预览渲染
                if (_selectedIndex >= 0)
                {
                    // 获取预览区域，使用自适应宽度
                    Rect previewRect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(200));

                    // 绘制背景
                    GUI.Box(previewRect, GUIContent.none, EditorStyles.helpBox);

                    // 处理预览区域的鼠标输入
                    MeshPreviewUtility.HandlePreviewInput(previewRect, ref _previewRotation, ref _previewZoom);

                    // 调用控制器渲染预览
                    try
                    {
                        // 确保选中的对象索引与仓库中的选中索引一致
                        int repositorySelectedIndex = _repository.GetSelectedObjectIndex();
                        if (_selectedIndex != repositorySelectedIndex)
                        {
                            // 更新仓库中的选中索引
                            _repository.SetSelectedObjectIndex(_selectedIndex);
                        }

                        // 再次检查选中的对象是否有效
                        MeshProcessingData dataToRender = _repository.GetProcessingData(_selectedIndex);
                        if (dataToRender == null)
                        {
                            MeshPreviewUtility.DrawCenteredText(previewRect, "无法获取选中对象的处理数据");
                            return;
                        }

                        // 调用控制器渲染预览
                        _transparencyController.RenderPreview(
                            previewRect,
                            _selectedIndex,
                            _showTriangleTransparency,
                            _previewRotation,
                            _previewZoom,
                            _alphaVisStrength);
                    }
                    catch (System.Exception)
                    {
                        // 渲染预览时出错
                        MeshPreviewUtility.DrawCenteredText(previewRect, "渲染预览时出错");
                    }

                    // 显示网格信息
                    MeshProcessingData dataToPreview = _repository.GetProcessingData(_selectedIndex);
                    Mesh meshToPreview = dataToPreview != null && dataToPreview.IsCulled && dataToPreview.CulledMesh != null
                        ? dataToPreview.CulledMesh
                        : _previewController.GetPreviewMesh(dataToPreview, _showTriangleTransparency);

                    if (meshToPreview != null)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField($"顶点数：{meshToPreview.vertexCount}", EditorStyles.miniLabel);
                        EditorGUILayout.LabelField($"三角面数：{meshToPreview.triangles.Length / 3}", EditorStyles.miniLabel);
                        EditorGUILayout.EndHorizontal();
                    }
                    else
                    {
                        MeshPreviewUtility.DrawCenteredText(previewRect, "无法预览网格");
                    }
                }
                else
                {
                    // 没有选中对象，显示提示信息
                    EditorGUILayout.LabelField("请选择要预览的对象", EditorStyles.centeredGreyMiniLabel);
                }
            }
            else
            {
                // 没有可用对象，显示提示信息
                EditorGUILayout.LabelField("请添加要处理的对象", EditorStyles.centeredGreyMiniLabel);
            }

            EditorGUILayout.EndVertical();
        }













        /// <summary>
        /// 绘制处理按钮
        /// </summary>
        private void DrawProcessButtons()
        {
            try
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                // 处理按钮
                EditorGUILayout.BeginHorizontal();

                // 显示分析状态
                if (_transparencyController.IsAnalyzing)
                {
                    EditorGUI.ProgressBar(
                        EditorGUILayout.GetControlRect(false, 20),
                        _transparencyController.AnalyzingProgress,
                        _transparencyController.CurrentStep);
                }
                else
                {
                    if (GUILayout.Button("分析选中对象"))
                    {
                        try
                        {
                            // 检查选中索引是否有效
                            if (_selectedIndex < 0)
                            {
                                EditorUtility.DisplayDialog("错误", "请先选择要处理的对象", "确定");
                                return;
                            }

                            // 确保透明度控制器已初始化
                            if (_transparencyController == null)
                            {
                                _transparencyController = TransparencyController.Instance;
                            }

                            // 初始化透明度控制器
                            _transparencyController.Initialize();

                            // 调用透明度分析控制器分析选中对象
                            ProcessingResult result = _transparencyController.AnalyzeSelectedObject();

                            // 获取当前选中对象的信息
                            MeshProcessingData dataAfterAnalysis = _repository.GetProcessingData(_selectedIndex);

                            if (dataAfterAnalysis != null && dataAfterAnalysis.IsTransparencyAnalyzed)
                            {
                                // 添加触发逻辑：分析完成后立即执行剔除操作
                                float currentThreshold = _transparencyController.GetAlphaThreshold();

                                // 获取剔除控制器并确保初始化
                                var cullingController = CullingController.Instance;
                                cullingController.Initialize();

                                // 检查TargetMesh是否为null
                                if (dataAfterAnalysis.TargetMesh == null)
                                {
                                    // 对象的TargetMesh为null，无法剔除
                                }

                                // 检查TriangleAlpha是否为null
                                if (dataAfterAnalysis.TriangleAlpha == null)
                                {
                                    // 对象的TriangleAlpha为null，无法剔除
                                }

                                // 执行剔除操作
                                bool cullResult = cullingController.CullMesh(_selectedIndex, currentThreshold, false);

                                if (cullResult)
                                {
                                    // 更新处理数据
                                    _repository.UpdateProcessingData(_selectedIndex, dataAfterAnalysis);
                                }
                            }
                            // 对象未分析过透明度或处理数据为null，无法执行剔除操作

                            // 静默导出调试文件，不显示对话框
                            EditorApplication.delayCall += () =>
                            {
                                // 分析完成，无需显示对话框

                                // 刷新预览窗口，确保使用最新的数据
                                RefreshPreview();
                            };
                        }
                        catch (System.Exception)
                        {
                            // 分析选中对象时出错，无需显示对话框
                        }
                    }

                    if (GUILayout.Button("分析所有对象"))
                    {
                        try
                        {
                            // 不再尝试结束预览，避免"Matrix stack empty"错误

                            // 获取所有处理数据，检查是否有有效的对象
                            var dataListToCheck = _repository.GetAllProcessingData();
                            if (dataListToCheck == null || dataListToCheck.Count == 0)
                            {
                                return;
                            }

                            // 检查每个对象是否有有效的网格
                            bool hasValidMesh = false;
                            for (int i = 0; i < dataListToCheck.Count; i++)
                            {
                                var data = dataListToCheck[i];
                                if (data != null && data.OriginalMesh != null)
                                {
                                    hasValidMesh = true;
                                    break;
                                }
                            }

                            if (!hasValidMesh)
                            {
                                return;
                            }

                            // 调用透明度分析控制器分析所有对象
                            List<ProcessingResult> results = _transparencyController.AnalyzeAllObjects();

                            // 获取当前透明度阈值
                            float currentThreshold = _transparencyController.GetAlphaThreshold();

                            // 获取剔除控制器并确保初始化
                            var cullingController = CullingController.Instance;
                            cullingController.Initialize();

                            // 获取所有处理数据
                            List<MeshProcessingData> processingDataList = _repository.GetAllProcessingData();

                            // 对所有已分析的对象执行剔除操作
                            if (processingDataList != null && processingDataList.Count > 0)
                            {
                                for (int i = 0; i < processingDataList.Count; i++)
                                {
                                    var data = processingDataList[i];
                                    if (data != null && data.IsTransparencyAnalyzed)
                                    {
                                        try
                                        {
                                            // 检查剔除控制器是否为null
                                            if (cullingController == null)
                                            {
                                                continue;
                                            }

                                            // 检查TargetMesh是否为null
                                            if (data.TargetMesh == null)
                                            {
                                                continue;
                                            }

                                            // 检查TriangleAlpha是否为null
                                            if (data.TriangleAlpha == null)
                                            {
                                                continue;
                                            }

                                            // 执行剔除操作
                                            bool cullResult = cullingController.CullMesh(i, currentThreshold, false);

                                            if (cullResult)
                                            {
                                                // 更新处理数据
                                                _repository.UpdateProcessingData(i, data);
                                            }
                                            // 剔除失败不做特殊处理
                                        }
                                        catch (System.Exception)
                                        {
                                            // 剔除对象时出错
                                        }
                                    }
                                }
                            }

                            // 静默导出调试文件，不显示对话框
                            EditorApplication.delayCall += () =>
                            {
                                // 分析完成，无需显示对话框

                                // 刷新预览窗口，确保使用最新的数据
                                RefreshPreview();
                            };
                        }
                        catch (System.Exception)
                        {
                            // 分析所有对象时出错，无需显示对话框
                        }
                    }
                }

                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
            }
            catch (System.Exception)
            {
                // 确保布局组正确结束
                try { EditorGUILayout.EndHorizontal(); } catch { }
                try { EditorGUILayout.EndVertical(); } catch { }

                // 绘制处理按钮时出错，无需显示对话框
            }
        }

        /// <summary>
        /// 更新面板
        /// </summary>
        public override void UpdateView()
        {
            // 检查并清理无效对象
            CheckAndCleanInvalidObjects();
        }

        /// <summary>
        /// 刷新预览窗口，确保使用最新的数据
        /// </summary>
        private void RefreshPreview()
        {
            // 强制重绘窗口
            EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
        }

        /// <summary>
        /// 检查并清理无效对象
        /// </summary>
        private void CheckAndCleanInvalidObjects()
        {
            List<MeshProcessingData> processingDataList = _repository.GetAllProcessingData();
            if (processingDataList == null || processingDataList.Count == 0)
                return;

            bool hasInvalidObjects = false;
            List<int> invalidIndices = new List<int>();

            // 检查无效对象
            for (int i = 0; i < processingDataList.Count; i++)
            {
                var data = processingDataList[i];
                if (data == null || data.OriginalGameObject == null)
                {
                    invalidIndices.Add(i);
                    hasInvalidObjects = true;
                }
            }

            // 如果有无效对象，从后向前移除（避免索引变化影响）
            if (hasInvalidObjects)
            {
                // 从后向前移除
                for (int i = invalidIndices.Count - 1; i >= 0; i--)
                {
                    int indexToRemove = invalidIndices[i];
                    _repository.RemoveProcessingData(indexToRemove);

                    // 更新选中索引
                    if (_selectedIndex == indexToRemove)
                    {
                        _selectedIndex = -1;
                    }
                    else if (_selectedIndex > indexToRemove)
                    {
                        _selectedIndex--;
                    }
                }

                // 如果还有对象且没有选中对象，选中第一个
                processingDataList = _repository.GetAllProcessingData();
                if (processingDataList != null && processingDataList.Count > 0 && _selectedIndex == -1)
                {
                    _selectedIndex = 0;
                }

                // 同步更新Repository中的选中索引
                _repository.SetSelectedObjectIndex(_selectedIndex);

                // 强制重绘窗口
                EditorWindow.GetWindow<MeshOptimizerWindow>().Repaint();
            }
        }

        /// <summary>
        /// 处理透明度阈值变更事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnAlphaThresholdChanged(AlphaThresholdChangedEvent eventData)
        {
            // 更新UI中的阈值
            _alphaThreshold = eventData.NewThreshold;

            // 刷新预览
            RefreshPreview();
        }

        /// <summary>
        /// 处理网格剔除事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnMeshCulled(MeshCulledEvent eventData)
        {
            // 刷新预览
            RefreshPreview();
        }

        /// <summary>
        /// 处理选中对象变更事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSelectedObjectChanged(SelectedObjectChangedEvent eventData)
        {
            // 更新选中对象索引
            _selectedIndex = eventData.Index;

            // 如果显示三角面透明度开关打开，检查新选中的对象是否已分析过透明度
            if (_showTriangleTransparency && _selectedIndex >= 0)
            {
                // 获取当前选中的对象
                MeshProcessingData dataForPreview = _repository.GetProcessingData(_selectedIndex);
                if (dataForPreview != null && !dataForPreview.IsTransparencyAnalyzed)
                {
                    // 如果未分析过透明度，自动调用"分析选中对象"按钮的逻辑
                    try
                    {
                        // 确保透明度控制器已初始化
                        if (_transparencyController == null)
                        {
                            _transparencyController = TransparencyController.Instance;
                        }

                        // 初始化透明度控制器
                        _transparencyController.Initialize();

                        // 调用透明度分析控制器分析选中对象
                        ProcessingResult result = _transparencyController.AnalyzeSelectedObject();

                        // 获取当前选中对象的信息
                        MeshProcessingData dataAfterAnalysis = _repository.GetProcessingData(_selectedIndex);

                        if (dataAfterAnalysis != null && dataAfterAnalysis.IsTransparencyAnalyzed)
                        {
                            // 添加触发逻辑：分析完成后立即执行剔除操作
                            float currentThreshold = _transparencyController.GetAlphaThreshold();

                            // 获取剔除控制器并确保初始化
                            var cullingController = CullingController.Instance;
                            cullingController.Initialize();

                            // 执行剔除操作
                            bool cullResult = cullingController.CullMesh(_selectedIndex, currentThreshold, false);

                            if (cullResult)
                            {
                                // 更新处理数据
                                _repository.UpdateProcessingData(_selectedIndex, dataAfterAnalysis);
                            }
                        }
                        // 切换对象时自动分析透明度失败
                    }
                    catch (System.Exception)
                    {
                        // 切换对象时自动分析透明度出错
                    }
                }
            }

            // 刷新预览
            RefreshPreview();
        }
    }
}
