using UnityEngine;

namespace MeshOptimizer.Utilities
{
    /// <summary>
    /// 材质资源
    /// </summary>
    public class MaterialResource : ResourceBase
    {
        /// <summary>
        /// 材质
        /// </summary>
        public Material Material { get; private set; }

        /// <summary>
        /// 着色器名称
        /// </summary>
        public string ShaderName { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">资源ID</param>
        /// <param name="material">材质</param>
        /// <param name="shaderName">着色器名称</param>
        public MaterialResource(string id, Material material, string shaderName)
            : base(id, "Material")
        {
            Material = material;
            ShaderName = shaderName;
            SizeInBytes = 1024 * 1024; // 估计值，实际大小难以精确计算
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Release()
        {
            if (Material != null)
            {
                UnityEngine.Object.DestroyImmediate(Material);
                Material = null;
            }
        }
    }
}
