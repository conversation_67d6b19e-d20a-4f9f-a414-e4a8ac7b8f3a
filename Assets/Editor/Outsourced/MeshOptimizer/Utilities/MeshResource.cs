using UnityEngine;

namespace MeshOptimizer.Utilities
{
    /// <summary>
    /// Mesh资源
    /// </summary>
    public class MeshResource : ResourceBase
    {
        /// <summary>
        /// Mesh
        /// </summary>
        public Mesh Mesh { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">资源ID</param>
        /// <param name="mesh">Mesh</param>
        public MeshResource(string id, Mesh mesh)
            : base(id, "Mesh")
        {
            Mesh = mesh;
            
            // 计算网格大小（估计值）
            if (mesh != null)
            {
                // 顶点数据大小
                long vertexSize = mesh.vertexCount * (sizeof(float) * 3); // 位置
                
                // UV数据大小
                if (mesh.uv != null && mesh.uv.Length > 0)
                {
                    vertexSize += mesh.uv.Length * (sizeof(float) * 2);
                }
                
                // 法线数据大小
                if (mesh.normals != null && mesh.normals.Length > 0)
                {
                    vertexSize += mesh.normals.Length * (sizeof(float) * 3);
                }
                
                // 颜色数据大小
                if (mesh.colors != null && mesh.colors.Length > 0)
                {
                    vertexSize += mesh.colors.Length * (sizeof(float) * 4);
                }
                
                // 三角形索引数据大小
                long triangleSize = mesh.triangles.Length * sizeof(int);
                
                // 总大小
                SizeInBytes = vertexSize + triangleSize;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Release()
        {
            if (Mesh != null)
            {
                UnityEngine.Object.DestroyImmediate(Mesh);
                Mesh = null;
            }
        }
    }
}
