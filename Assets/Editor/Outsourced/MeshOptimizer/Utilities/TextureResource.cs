using UnityEngine;

namespace MeshOptimizer.Utilities
{
    /// <summary>
    /// RenderTexture资源
    /// </summary>
    public class RenderTextureResource : ResourceBase
    {
        /// <summary>
        /// RenderTexture
        /// </summary>
        public RenderTexture Texture { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">资源ID</param>
        /// <param name="texture">RenderTexture</param>
        public RenderTextureResource(string id, RenderTexture texture)
            : base(id, "RenderTexture")
        {
            Texture = texture;
            SizeInBytes = (long)texture.width * texture.height * GetBytesPerPixel(texture.format);
        }

        /// <summary>
        /// 获取每像素字节数
        /// </summary>
        /// <param name="format">纹理格式</param>
        /// <returns>每像素字节数</returns>
        private int GetBytesPerPixel(RenderTextureFormat format)
        {
            switch (format)
            {
                case RenderTextureFormat.ARGB32:
                case RenderTextureFormat.BGRA32:
                case RenderTextureFormat.ARGBFloat:
                    return 4;
                case RenderTextureFormat.RGFloat:
                    return 8;
                case RenderTextureFormat.ARGBHalf:
                    return 8;
                default:
                    return 4;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Release()
        {
            if (Texture != null)
            {
                Texture.Release();
                UnityEngine.Object.DestroyImmediate(Texture);
                Texture = null;
            }
        }
    }
}
