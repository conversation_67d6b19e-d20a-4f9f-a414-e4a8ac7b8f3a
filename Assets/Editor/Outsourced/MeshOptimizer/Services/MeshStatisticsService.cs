using UnityEngine;
using System.Collections.Generic;
using MeshOptimizer.Models;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// 网格统计服务，提供网格统计信息的计算和管理功能
    /// </summary>
    public class MeshStatisticsService : ServiceBase
    {
        // 单例实例
        private static MeshStatisticsService _instance;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static MeshStatisticsService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new MeshStatisticsService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MeshStatisticsService()
        {
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            base.Initialize();
        }

        /// <summary>
        /// 计算网格统计信息
        /// </summary>
        /// <param name="mesh">要分析的网格</param>
        /// <returns>网格统计信息</returns>
        public MeshStatistics CalculateMeshStatistics(Mesh mesh)
        {
            if (mesh == null)
                return null;

            MeshStatistics statistics = new MeshStatistics();
            statistics.CalculateFromMesh(mesh);
            return statistics;
        }

        /// <summary>
        /// 计算网格处理前后的统计差异
        /// </summary>
        /// <param name="originalMesh">原始网格</param>
        /// <param name="processedMesh">处理后的网格</param>
        /// <returns>处理结果，包含统计差异</returns>
        public ProcessingResult CalculateProcessingResult(Mesh originalMesh, Mesh processedMesh)
        {
            if (originalMesh == null || processedMesh == null)
                return null;

            MeshStatistics originalStats = CalculateMeshStatistics(originalMesh);
            MeshStatistics processedStats = CalculateMeshStatistics(processedMesh);

            ProcessingResult result = new ProcessingResult();

            // 设置基本统计信息
            int originalVertexCount = originalStats.VertexCount;
            int originalTriangleCount = originalStats.TriangleCount;
            int processedVertexCount = processedStats.VertexCount;
            int processedTriangleCount = processedStats.TriangleCount;
            int culledTriangleCount = originalTriangleCount - processedTriangleCount;

            result.SetBasicStatistics(
                originalVertexCount,
                originalTriangleCount,
                processedVertexCount,
                processedTriangleCount,
                culledTriangleCount
            );

            // 设置优化信息
            int reducedVertexCount = originalVertexCount - processedVertexCount;
            int reducedTriangleCount = originalTriangleCount - processedTriangleCount;
            float optimizationStrength = 0.5f; // 默认值，实际应该从参数中获取

            result.SetOptimizationInfo(
                optimizationStrength,
                reducedVertexCount,
                reducedTriangleCount
            );

            // 设置处理成功
            result.SetSuccess(0.0f); // 处理时间，实际应该测量

            return result;
        }

        /// <summary>
        /// 计算多个网格的总体统计信息
        /// </summary>
        /// <param name="meshes">网格列表</param>
        /// <returns>总体统计信息</returns>
        public MeshStatistics CalculateAggregateStatistics(List<Mesh> meshes)
        {
            if (meshes == null || meshes.Count == 0)
                return null;

            // 创建一个合并的网格，用于计算统计信息
            Mesh combinedMesh = new Mesh();
            CombineInstance[] combineInstances = new CombineInstance[meshes.Count];

            int validMeshCount = 0;
            for (int i = 0; i < meshes.Count; i++)
            {
                if (meshes[i] != null)
                {
                    combineInstances[validMeshCount].mesh = meshes[i];
                    combineInstances[validMeshCount].transform = Matrix4x4.identity;
                    validMeshCount++;
                }
            }

            // 如果没有有效的网格，返回null
            if (validMeshCount == 0)
                return null;

            // 调整数组大小
            if (validMeshCount < meshes.Count)
            {
                System.Array.Resize(ref combineInstances, validMeshCount);
            }

            // 合并网格
            combinedMesh.CombineMeshes(combineInstances, true, true);

            // 计算合并网格的统计信息
            MeshStatistics aggregateStats = CalculateMeshStatistics(combinedMesh);

            // 清理临时网格
            Object.DestroyImmediate(combinedMesh);

            return aggregateStats;
        }

        /// <summary>
        /// 获取网格统计信息的摘要
        /// </summary>
        /// <param name="mesh">要分析的网格</param>
        /// <returns>统计信息摘要</returns>
        public string GetMeshStatisticsSummary(Mesh mesh)
        {
            if (mesh == null)
                return "无效网格";

            MeshStatistics stats = CalculateMeshStatistics(mesh);
            return stats.GetSummary();
        }

        /// <summary>
        /// 获取网格统计信息的详细报告
        /// </summary>
        /// <param name="mesh">要分析的网格</param>
        /// <returns>详细统计信息</returns>
        public string GetDetailedMeshStatistics(Mesh mesh)
        {
            if (mesh == null)
                return "无效网格";

            MeshStatistics stats = CalculateMeshStatistics(mesh);
            return stats.GetDetailedStatistics();
        }

        /// <summary>
        /// 计算网格优化的潜在收益
        /// </summary>
        /// <param name="mesh">要分析的网格</param>
        /// <param name="optimizationStrength">优化强度，0-1之间</param>
        /// <returns>预估的优化结果</returns>
        public ProcessingResult EstimateOptimizationResult(Mesh mesh, float optimizationStrength)
        {
            if (mesh == null)
                return null;

            MeshStatistics originalStats = CalculateMeshStatistics(mesh);

            // 基于优化强度估算优化后的顶点和三角形数量
            int estimatedVertexCount = Mathf.RoundToInt(originalStats.VertexCount * (1f - optimizationStrength * 0.5f));
            int estimatedTriangleCount = Mathf.RoundToInt(originalStats.TriangleCount * (1f - optimizationStrength * 0.5f));

            ProcessingResult result = new ProcessingResult();

            // 设置基本统计信息
            int originalVertexCount = originalStats.VertexCount;
            int originalTriangleCount = originalStats.TriangleCount;
            int culledTriangleCount = 0; // 估算时没有剔除

            result.SetBasicStatistics(
                originalVertexCount,
                originalTriangleCount,
                estimatedVertexCount,
                estimatedTriangleCount,
                culledTriangleCount
            );

            // 设置优化信息
            int reducedVertexCount = originalVertexCount - estimatedVertexCount;
            int reducedTriangleCount = originalTriangleCount - estimatedTriangleCount;

            result.SetOptimizationInfo(
                optimizationStrength,
                reducedVertexCount,
                reducedTriangleCount
            );

            // 设置处理成功
            result.SetSuccess(0.0f); // 估算不需要处理时间

            return result;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}
