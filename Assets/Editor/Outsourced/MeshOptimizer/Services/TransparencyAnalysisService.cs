using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Diagnostics;
using MeshOptimizer.Models;
using MeshOptimizer.Utilities;
using Debug = UnityEngine.Debug;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// 透明度分析服务，用于分析网格的透明度
    /// </summary>
    public class TransparencyAnalysisService : ServiceBase
    {
        private static TransparencyAnalysisService _instance;

        /// <summary>
        /// 透明度分析服务单例
        /// </summary>
        public static TransparencyAnalysisService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new TransparencyAnalysisService();
                }
                return _instance;
            }
        }

        // Compute Shader相关
        private ComputeShader _transparencyAnalysisShader;
        private int _analyzeTriangleAlphaKernel;
        private bool _isComputeShaderInitialized = false;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public TransparencyAnalysisService()
        {
            // 公共默认构造函数，用于依赖注入
            InitializeComputeShader();
        }

        /// <summary>
        /// 初始化Compute Shader
        /// </summary>
        private void InitializeComputeShader()
        {
            if (_isComputeShaderInitialized)
                return;

            // 使用路径常量类加载Compute Shader
            string path = PathConstants.GetResourceLoadPath(PathConstants.GetComputeShaderPath("TransparencyAnalysis"));

            // 尝试通过Resources.Load加载
            _transparencyAnalysisShader = Resources.Load<ComputeShader>(path);

            // 如果加载失败，尝试通过AssetDatabase加载
            if (_transparencyAnalysisShader == null)
            {
                string fullPath = PathConstants.GetComputeShaderPath("TransparencyAnalysis") + ".compute";
                _transparencyAnalysisShader = AssetDatabase.LoadAssetAtPath<ComputeShader>(fullPath);
            }

            if (_transparencyAnalysisShader != null)
            {
                try
                {
                    _analyzeTriangleAlphaKernel = _transparencyAnalysisShader.FindKernel("AnalyzeTriangleAlpha");
                    _isComputeShaderInitialized = true;
                    // 成功加载透明度分析Compute Shader
                }
                catch (System.Exception)
                {
                    // 初始化Compute Shader内核失败
                    _transparencyAnalysisShader = null;
                    _isComputeShaderInitialized = false;
                }
            }
            else
            {
                // 无法加载透明度分析Compute Shader，将使用CPU实现
            }
        }

        /// <summary>
        /// 分析网格透明度
        /// </summary>
        /// <param name="meshData">网格处理数据</param>
        /// <param name="parameters">处理参数</param>
        /// <returns>处理结果</returns>
        public ProcessingResult AnalyzeTransparency(MeshProcessingData meshData, ProcessingParameters parameters)
        {
            ProcessingResult result = new ProcessingResult();

            if (meshData == null || meshData.OriginalMesh == null)
            {
                result.SetFailure("无效的网格数据", 0);
                return result;
            }

            try
            {
                // 获取处理后的网格（细分后的网格）
                // 如果没有细分过，则使用原始网格
                Mesh meshToAnalyze = meshData.IsSubdivided && meshData.ProcessedMesh != null
                    ? meshData.ProcessedMesh
                    : meshData.OriginalMesh;

                // 记录使用的是哪个网格
                bool usingProcessedMesh = meshData.IsSubdivided && meshData.ProcessedMesh != null;

                // 获取透明度阈值
                float alphaThreshold = parameters.AlphaThreshold;

                // 计算三角面数量
                int triangleCount = meshToAnalyze.triangles.Length / 3;

                // 创建透明度数据数组
                float[] triangleAlphaValues = new float[triangleCount];

                // 获取材质和贴图
                Texture2D mainTexture = null;
                if (meshData.OriginalGameObject != null)
                {
                    Renderer renderer = meshData.OriginalGameObject.GetComponent<Renderer>();
                    if (renderer != null && renderer.sharedMaterial != null)
                    {
                        mainTexture = renderer.sharedMaterial.mainTexture as Texture2D;
                    }
                }

                // 记录开始时间
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();

                // 根据参数选择使用GPU或CPU进行分析
                if (parameters.UseComputeShader && _isComputeShaderInitialized)
                {
                    // 使用Compute Shader分析透明度
                    AnalyzeTriangleAlphaGPU(meshToAnalyze, mainTexture, triangleAlphaValues, parameters.SamplingDensity);

                    // 标记已经使用GPU分析
                    meshData.IsGpuAnalyzed = true;
                }
                else
                {
                    // 使用CPU分析透明度
                    AnalyzeTriangleAlphaCPU(meshToAnalyze, mainTexture, triangleAlphaValues, parameters.SamplingDensity);

                    // 标记已经使用CPU分析
                    meshData.IsCpuAnalyzed = true;
                }

                // 透明度分析完成

                // 记录结束时间
                stopwatch.Stop();
                float processingTime = stopwatch.ElapsedMilliseconds / 1000.0f;

                // 估算采样点数量
                int estimatedSamplePoints = 0;
                if (parameters.SamplingDensity == 0) // 低密度
                {
                    estimatedSamplePoints = 1 * triangleCount;
                }
                else if (parameters.SamplingDensity == 1) // 中密度
                {
                    estimatedSamplePoints = (1 + 30 + 10) * triangleCount; // 中心点 + 边缘点 + 内部网格点
                }
                else if (parameters.SamplingDensity == 2) // 高密度
                {
                    estimatedSamplePoints = (1 + 30 + 10 + 3 + 45 + 60) * triangleCount; // 中心点 + 边缘点 + 内部网格点 + 顶点 + 更密集网格点 + 额外边缘点
                }

                // 统计透明和不透明三角形数量
                int transparentTriangleCount = 0;
                int opaqueTriangleCount = 0;

                for (int i = 0; i < triangleCount; i++)
                {
                    if (triangleAlphaValues[i] < alphaThreshold)
                    {
                        transparentTriangleCount++;
                    }
                    else
                    {
                        opaqueTriangleCount++;
                    }
                }

                // 保存透明度数据到网格处理数据
                meshData.TriangleAlpha = triangleAlphaValues == null ? null : new List<float>(triangleAlphaValues);
                meshData.IsTransparencyAnalyzed = true;

                // 设置结果
                result.SetSuccess(processingTime);
                result.SetTransparencyInfo(alphaThreshold, transparentTriangleCount, opaqueTriangleCount);

                // 释放临时创建的可读写贴图
                if (mainTexture != null)
                {
                    TextureReadableUtility.ReleaseReadableTexture(mainTexture);
                }

                return result;
            }
            catch (System.Exception)
            {
                // 释放临时创建的可读写贴图
                if (meshData.OriginalGameObject != null)
                {
                    Renderer renderer = meshData.OriginalGameObject.GetComponent<Renderer>();
                    if (renderer != null && renderer.sharedMaterial != null)
                    {
                        Texture2D mainTexture = renderer.sharedMaterial.mainTexture as Texture2D;
                        if (mainTexture != null)
                        {
                            TextureReadableUtility.ReleaseReadableTexture(mainTexture);
                        }
                    }
                }

                result.SetFailure("透明度分析失败", 0);
                return result;
            }
        }

        /// <summary>
        /// 使用CPU分析三角面透明度
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <param name="texture">贴图</param>
        /// <param name="triangleAlphaValues">输出的三角面透明度值</param>
        /// <param name="samplingDensity">采样密度 (0=低, 1=中, 2=高)</param>
        private void AnalyzeTriangleAlphaCPU(Mesh mesh, Texture2D texture, float[] triangleAlphaValues, int samplingDensity)
        {
            // 获取网格数据
            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = mesh.uv;
            int[] triangles = mesh.triangles;

            // 检查UV是否有效
            if (uvs == null || uvs.Length == 0)
            {
                // 如果没有UV，所有三角面都设为不透明
                for (int i = 0; i < triangleAlphaValues.Length; i++)
                {
                    triangleAlphaValues[i] = 1.0f;
                }
                return;
            }

            // 检查贴图是否有效
            if (texture == null)
            {
                // 如果没有贴图，所有三角面都设为不透明
                for (int i = 0; i < triangleAlphaValues.Length; i++)
                {
                    triangleAlphaValues[i] = 1.0f;
                }
                return;
            }

            // CPU实现：根据采样密度生成采样点
            for (int i = 0; i < triangles.Length; i += 3)
            {
                int triangleIndex = i / 3;

                // 获取三角形的三个顶点
                int vertIndex1 = triangles[i];
                int vertIndex2 = triangles[i + 1];
                int vertIndex3 = triangles[i + 2];

                // 获取三角形的三个UV坐标
                Vector2 uv1 = uvs[vertIndex1];
                Vector2 uv2 = uvs[vertIndex2];
                Vector2 uv3 = uvs[vertIndex3];

                // 根据采样密度生成采样点
                List<Vector2> samplingPoints = new List<Vector2>();

                // 三角形中心点（所有密度都采样）
                samplingPoints.Add((uv1 + uv2 + uv3) / 3f);

                if (samplingDensity >= 1) // 中密度和高密度
                {
                    // 三条边的多个点
                    for (int j = 1; j <= 10; j++)
                    {
                        float t = j / 11f; // 将边分为11等份，取中间10个点
                        samplingPoints.Add(Vector2.Lerp(uv1, uv2, t)); // uv1-uv2边
                        samplingPoints.Add(Vector2.Lerp(uv2, uv3, t)); // uv2-uv3边
                        samplingPoints.Add(Vector2.Lerp(uv3, uv1, t)); // uv3-uv1边
                    }

                    // 三角形内部的网格点
                    for (int u = 1; u < 5; u++)
                    {
                        for (int v = 1; v < 5 - u; v++)
                        {
                            float w1 = u / 5f;
                            float w2 = v / 5f;
                            float w3 = 1f - w1 - w2;
                            samplingPoints.Add(uv1 * w1 + uv2 * w2 + uv3 * w3);
                        }
                    }
                }

                if (samplingDensity >= 2) // 高密度
                {
                    // 三个顶点
                    samplingPoints.Add(uv1);
                    samplingPoints.Add(uv2);
                    samplingPoints.Add(uv3);

                    // 更密集的三角形内部网格点
                    for (int u = 1; u < 10; u++)
                    {
                        for (int v = 1; v < 10 - u; v++)
                        {
                            float w1 = u / 10f;
                            float w2 = v / 10f;
                            float w3 = 1f - w1 - w2;
                            samplingPoints.Add(uv1 * w1 + uv2 * w2 + uv3 * w3);
                        }
                    }

                    // 额外在边缘附近添加更多采样点
                    for (int j = 1; j <= 20; j++)
                    {
                        float t = j / 21f; // 将边分为21等份，取中间20个点

                        // 边缘点
                        Vector2 edgePoint1 = Vector2.Lerp(uv1, uv2, t);
                        Vector2 edgePoint2 = Vector2.Lerp(uv2, uv3, t);
                        Vector2 edgePoint3 = Vector2.Lerp(uv3, uv1, t);

                        // 从边缘向内部的点
                        samplingPoints.Add(Vector2.Lerp(edgePoint1, (uv1 + uv2 + uv3) / 3f, 0.1f));
                        samplingPoints.Add(Vector2.Lerp(edgePoint2, (uv1 + uv2 + uv3) / 3f, 0.1f));
                        samplingPoints.Add(Vector2.Lerp(edgePoint3, (uv1 + uv2 + uv3) / 3f, 0.1f));
                    }
                }

                // 采样所有点并计算平均透明度
                float totalAlpha = 0f;

                foreach (var uv in samplingPoints)
                {
                    Color pixelColor = SampleTexture(texture, uv);
                    totalAlpha += pixelColor.a;
                }

                float averageAlpha = totalAlpha / samplingPoints.Count;

                // 存储三角面的透明度值
                triangleAlphaValues[triangleIndex] = averageAlpha;
            }
        }

        // 存储最近一次GPU分析的结果缓冲区
        private ComputeBuffer _lastAlphaValuesBuffer;
        private int _lastTriangleCount;
        private bool _hasValidBuffer = false;

        /// <summary>
        /// 获取最近一次GPU分析的结果缓冲区
        /// </summary>
        /// <returns>ComputeBuffer，如果没有有效的缓冲区则返回null</returns>
        public ComputeBuffer GetLastAlphaValuesBuffer()
        {
            return _hasValidBuffer ? _lastAlphaValuesBuffer : null;
        }

        /// <summary>
        /// 获取最近一次分析的三角形数量
        /// </summary>
        /// <returns>三角形数量</returns>
        public int GetLastTriangleCount()
        {
            return _lastTriangleCount;
        }

        /// <summary>
        /// 使用GPU分析三角面透明度
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <param name="texture">贴图</param>
        /// <param name="triangleAlphaValues">输出的三角面透明度值</param>
        /// <param name="samplingDensity">采样密度 (0=低, 1=中, 2=高)</param>
        private void AnalyzeTriangleAlphaGPU(Mesh mesh, Texture2D texture, float[] triangleAlphaValues, int samplingDensity)
        {
            // 检查Compute Shader是否已初始化
            if (!_isComputeShaderInitialized || _transparencyAnalysisShader == null)
            {
                // 如果Compute Shader未初始化，回退到CPU实现
                AnalyzeTriangleAlphaCPU(mesh, texture, triangleAlphaValues, samplingDensity);
                return;
            }

            // 检查UV是否有效
            Vector2[] uvs = mesh.uv;
            if (uvs == null || uvs.Length == 0)
            {
                // 如果没有UV，所有三角面都设为不透明
                for (int i = 0; i < triangleAlphaValues.Length; i++)
                {
                    triangleAlphaValues[i] = 1.0f;
                }
                return;
            }

            // 检查贴图是否有效
            if (texture == null)
            {
                // 如果没有贴图，所有三角面都设为不透明
                for (int i = 0; i < triangleAlphaValues.Length; i++)
                {
                    triangleAlphaValues[i] = 1.0f;
                }
                return;
            }

            try
            {
                // 获取网格数据
                Vector3[] vertices = mesh.vertices;
                int[] triangles = mesh.triangles;
                int triangleCount = triangles.Length / 3;

                // 使用ResourceManager创建ComputeBuffer
                ComputeBuffer verticesBuffer = ResourceManager.Instance.CreateComputeBuffer(vertices.Length, sizeof(float) * 3);
                ComputeBuffer uvsBuffer = ResourceManager.Instance.CreateComputeBuffer(uvs.Length, sizeof(float) * 2);
                ComputeBuffer trianglesBuffer = ResourceManager.Instance.CreateComputeBuffer(triangles.Length, sizeof(int));
                ComputeBuffer alphaValuesBuffer = ResourceManager.Instance.CreateComputeBuffer(triangleCount, sizeof(float));

                // 设置数据
                verticesBuffer.SetData(vertices);
                uvsBuffer.SetData(uvs);
                trianglesBuffer.SetData(triangles);

                // 设置Shader参数
                _transparencyAnalysisShader.SetBuffer(_analyzeTriangleAlphaKernel, "_Vertices", verticesBuffer);
                _transparencyAnalysisShader.SetBuffer(_analyzeTriangleAlphaKernel, "_UVs", uvsBuffer);
                _transparencyAnalysisShader.SetBuffer(_analyzeTriangleAlphaKernel, "_Triangles", trianglesBuffer);
                _transparencyAnalysisShader.SetBuffer(_analyzeTriangleAlphaKernel, "_TriangleAlphaValues", alphaValuesBuffer);
                _transparencyAnalysisShader.SetInt("_TriangleCount", triangleCount);
                _transparencyAnalysisShader.SetInt("_SamplingDensity", samplingDensity);
                _transparencyAnalysisShader.SetTexture(_analyzeTriangleAlphaKernel, "_MainTex", texture);
                _transparencyAnalysisShader.SetVector("_TextureSize", new Vector4(
                    texture.width, texture.height, 1.0f / texture.width, 1.0f / texture.height));

                // 计算线程组数量
                int threadGroupSize = 64; // 与Compute Shader中的numthreads保持一致
                int threadGroups = Mathf.CeilToInt((float)triangleCount / threadGroupSize);

                // 执行Compute Shader
                try
                {
                    _transparencyAnalysisShader.Dispatch(_analyzeTriangleAlphaKernel, threadGroups, 1, 1);
                    // GPU透明度分析完成
                }
                catch (System.Exception)
                {
                    // 执行Compute Shader失败，重新抛出异常，让外层catch捕获
                    throw;
                }

                // 获取结果
                alphaValuesBuffer.GetData(triangleAlphaValues);

                // 释放不再需要的缓冲区
                ResourceManager.Instance.ReleaseComputeBuffer(verticesBuffer);
                ResourceManager.Instance.ReleaseComputeBuffer(uvsBuffer);
                ResourceManager.Instance.ReleaseComputeBuffer(trianglesBuffer);

                // 保存最近一次的结果缓冲区
                if (_hasValidBuffer && _lastAlphaValuesBuffer != null)
                {
                    ResourceManager.Instance.ReleaseComputeBuffer(_lastAlphaValuesBuffer);
                    _lastAlphaValuesBuffer = null;
                }

                _lastAlphaValuesBuffer = alphaValuesBuffer;
                _lastTriangleCount = triangleCount;
                _hasValidBuffer = true;
            }
            catch (System.Exception)
            {
                // GPU透明度分析失败，回退到CPU实现
                AnalyzeTriangleAlphaCPU(mesh, texture, triangleAlphaValues, samplingDensity);
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            // 释放ComputeBuffer
            if (_hasValidBuffer && _lastAlphaValuesBuffer != null)
            {
                ResourceManager.Instance.ReleaseComputeBuffer(_lastAlphaValuesBuffer);
                _lastAlphaValuesBuffer = null;
                _hasValidBuffer = false;
            }

            // 释放所有临时创建的可读写贴图
            TextureReadableUtility.ReleaseAllReadableTextures();

            base.Cleanup();
        }

        /// <summary>
        /// 采样贴图
        /// </summary>
        /// <param name="texture">贴图</param>
        /// <param name="uv">UV坐标</param>
        /// <returns>采样的颜色</returns>
        private Color SampleTexture(Texture2D texture, Vector2 uv)
        {
            if (texture == null)
                return Color.white; // 如果贴图为空，返回白色（不透明）

            // 获取可读写的贴图
            Texture2D readableTexture = TextureReadableUtility.GetReadableTexture(texture);
            if (readableTexture == null)
                return Color.white; // 如果无法获取可读写贴图，返回白色（不透明）

            // 确保UV在0-1范围内
            uv.x = Mathf.Repeat(uv.x, 1.0f);
            uv.y = Mathf.Repeat(uv.y, 1.0f);

            // 计算像素坐标
            int x = Mathf.FloorToInt(uv.x * readableTexture.width);
            int y = Mathf.FloorToInt(uv.y * readableTexture.height);

            // 确保坐标在贴图范围内
            x = Mathf.Clamp(x, 0, readableTexture.width - 1);
            y = Mathf.Clamp(y, 0, readableTexture.height - 1);

            // 读取像素颜色
            Color pixelColor = readableTexture.GetPixel(x, y);

            // 如果使用的是临时创建的可读写贴图，不需要在这里释放
            // 贴图会在处理完成后统一释放

            return pixelColor;
        }
    }
}
