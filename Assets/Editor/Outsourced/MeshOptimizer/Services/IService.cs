using UnityEngine;

namespace MeshOptimizer.Services
{
    /// <summary>
    /// 服务层基础接口，所有服务都应实现此接口
    /// </summary>
    public interface IService
    {
        /// <summary>
        /// 初始化服务
        /// </summary>
        void Initialize();

        /// <summary>
        /// 清理服务资源
        /// </summary>
        void Cleanup();

        /// <summary>
        /// 设置依赖
        /// </summary>
        /// <param name="dependencies">依赖对象数组</param>
        void SetDependencies(params object[] dependencies);
    }
}
