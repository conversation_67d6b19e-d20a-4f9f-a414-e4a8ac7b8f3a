using System.Collections.Generic;
using MeshOptimizer.Models;

namespace MeshOptimizer.Repository
{
    /// <summary>
    /// 处理数据仓库接口，用于访问和修改处理数据
    /// </summary>
    public interface IProcessingDataRepository
    {
        /// <summary>
        /// 获取所有处理数据
        /// </summary>
        /// <returns>处理数据列表</returns>
        List<MeshProcessingData> GetAllProcessingData();

        /// <summary>
        /// 获取指定索引的处理数据
        /// </summary>
        /// <param name="index">索引</param>
        /// <returns>处理数据，如果索引无效则返回null</returns>
        MeshProcessingData GetProcessingData(int index);

        /// <summary>
        /// 添加处理数据
        /// </summary>
        /// <param name="data">处理数据</param>
        void AddProcessingData(MeshProcessingData data);

        /// <summary>
        /// 移除指定索引的处理数据
        /// </summary>
        /// <param name="index">索引</param>
        /// <returns>是否成功移除</returns>
        bool RemoveProcessingData(int index);

        /// <summary>
        /// 清除所有处理数据
        /// </summary>
        void ClearProcessingData();

        /// <summary>
        /// 更新指定索引的处理数据
        /// </summary>
        /// <param name="index">索引</param>
        /// <param name="data">处理数据</param>
        /// <returns>是否成功更新</returns>
        bool UpdateProcessingData(int index, MeshProcessingData data);

        /// <summary>
        /// 获取处理参数
        /// </summary>
        /// <returns>处理参数</returns>
        ProcessingParameters GetParameters();

        /// <summary>
        /// 更新处理参数
        /// </summary>
        /// <param name="parameters">处理参数</param>
        void UpdateParameters(ProcessingParameters parameters);

        /// <summary>
        /// 获取选中对象索引
        /// </summary>
        /// <returns>选中对象索引</returns>
        int GetSelectedObjectIndex();

        /// <summary>
        /// 设置选中对象索引
        /// </summary>
        /// <param name="index">索引</param>
        void SetSelectedObjectIndex(int index);

        /// <summary>
        /// 获取当前会话
        /// </summary>
        /// <returns>当前会话</returns>
        WorkSession GetCurrentSession();

        /// <summary>
        /// 创建新会话
        /// </summary>
        void CreateNewSession();

        /// <summary>
        /// 保存会话
        /// </summary>
        /// <param name="sessionName">会话名称</param>
        /// <returns>是否成功保存</returns>
        bool SaveSession(string sessionName);

        /// <summary>
        /// 加载会话
        /// </summary>
        /// <param name="sessionName">会话名称</param>
        /// <returns>是否成功加载</returns>
        bool LoadSession(string sessionName);
    }
}
