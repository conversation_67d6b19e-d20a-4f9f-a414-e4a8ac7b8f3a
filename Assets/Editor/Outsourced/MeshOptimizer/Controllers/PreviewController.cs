using UnityEngine;
using UnityEditor;
using MeshOptimizer.Models;
using MeshOptimizer.Services;
using MeshOptimizer.Utilities;

namespace MeshOptimizer.Controllers
{
    /// <summary>
    /// 预览控制器，负责协调预览相关功能
    /// </summary>
    public class PreviewController : ControllerBase
    {
        private static PreviewController _instance;

        /// <summary>
        /// 预览控制器单例
        /// </summary>
        public static PreviewController Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new PreviewController();
                }
                return _instance;
            }
        }

        private WorkflowController _workflowController;
        private PreviewService _previewService;
        private TransparencyController _transparencyController;

        // 预览渲染器
        private Views.PreviewRenderers.MeshPreviewRenderer _meshPreviewRenderer;
        private Views.PreviewRenderers.OptimizationPreviewRenderer _optimizationPreviewRenderer;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PreviewController()
        {
            // 公共默认构造函数，用于依赖注入
        }

        /// <summary>
        /// 带依赖注入的构造函数
        /// </summary>
        /// <param name="workflowController">工作流控制器</param>
        /// <param name="previewService">预览服务</param>
        /// <param name="transparencyController">透明度控制器</param>
        public PreviewController(
            WorkflowController workflowController,
            PreviewService previewService,
            TransparencyController transparencyController)
        {
            _workflowController = workflowController;
            _previewService = previewService;
            _transparencyController = transparencyController;
        }

        /// <summary>
        /// 设置依赖
        /// </summary>
        /// <param name="dependencies">依赖对象数组</param>
        public override void SetDependencies(params object[] dependencies)
        {
            base.SetDependencies(dependencies);

            foreach (var dependency in dependencies)
            {
                if (dependency is WorkflowController workflowController)
                {
                    _workflowController = workflowController;
                }
                else if (dependency is PreviewService previewService)
                {
                    _previewService = previewService;
                }
                else if (dependency is TransparencyController transparencyController)
                {
                    _transparencyController = transparencyController;
                }
            }
        }

        /// <summary>
        /// 初始化控制器
        /// </summary>
        public override void Initialize()
        {
            if (_isInitialized)
                return;

            base.Initialize();

            // 从依赖中获取服务
            _workflowController = _workflowController ?? GetDependency<WorkflowController>();
            _previewService = _previewService ?? GetDependency<PreviewService>();
            _transparencyController = _transparencyController ?? GetDependency<TransparencyController>();

            // 如果依赖中没有服务，则使用单例
            if (_workflowController == null)
            {
                _workflowController = WorkflowController.Instance;
            }

            if (_previewService == null)
            {
                _previewService = PreviewService.Instance;
            }

            if (_transparencyController == null)
            {
                _transparencyController = TransparencyController.Instance;
            }

            // 确保服务已初始化
            if (_previewService != null)
            {
                _previewService.Initialize();
            }

            // 初始化预览渲染器
            _meshPreviewRenderer = new Views.PreviewRenderers.MeshPreviewRenderer();
            _meshPreviewRenderer.Initialize();

            _optimizationPreviewRenderer = new Views.PreviewRenderers.OptimizationPreviewRenderer();
            _optimizationPreviewRenderer.Initialize();
        }

        /// <summary>
        /// 设置预览渲染工具
        /// </summary>
        /// <param name="previewUtility">预览渲染工具</param>
        /// <param name="meshBounds">网格边界</param>
        public void SetupPreviewUtility(PreviewRenderUtility previewUtility, Vector3 meshBounds)
        {
            _previewService.SetupPreviewUtility(previewUtility, meshBounds);
        }

        /// <summary>
        /// 创建透明度可视化材质
        /// </summary>
        /// <param name="alphaValues">透明度值数组</param>
        /// <param name="threshold">透明度阈值</param>
        /// <param name="strength">可视化强度</param>
        /// <returns>可视化材质</returns>
        public Material CreateTransparencyVisualizationMaterial(float[] alphaValues, float threshold, float strength)
        {
            return _previewService.CreateTransparencyVisualizationMaterial(alphaValues, threshold, strength);
        }

        /// <summary>
        /// 生成临时透明度数据
        /// </summary>
        /// <param name="mesh">网格</param>
        /// <returns>临时透明度数据</returns>
        public float[] GenerateTempAlphaValues(Mesh mesh)
        {
            int triangleCount = mesh.triangles.Length / 3;
            float[] tempTriangleAlphaValues = new float[triangleCount];

            // 生成随机透明度值（模拟真实数据）
            System.Random random = new System.Random(mesh.GetInstanceID());
            for (int i = 0; i < triangleCount; i++)
            {
                // 生成0-1之间的随机值，但偏向于两端（更多的完全透明或完全不透明）
                float value = (float)random.NextDouble();
                if (value < 0.3f)
                    value *= 0.3f; // 更多的低透明度值
                else if (value > 0.7f)
                    value = 0.7f + (value - 0.7f) * 0.3f; // 更多的高透明度值

                tempTriangleAlphaValues[i] = value;
            }

            return tempTriangleAlphaValues;
        }

        /// <summary>
        /// 获取预览网格
        /// </summary>
        /// <param name="data">处理数据</param>
        /// <param name="showTriangleTransparency">是否显示三角面透明度</param>
        /// <returns>预览网格</returns>
        public Mesh GetPreviewMesh(MeshProcessingData data, bool showTriangleTransparency)
        {
            if (data == null)
                return null;

            // 根据显示模式选择不同的mesh
            if (showTriangleTransparency)
            {
                // 显示三角面透明度时，必须使用预览专用mesh
                // 如果预览专用mesh不存在，尝试创建它
                if (data.PreviewMesh == null && data.IsTransparencyAnalyzed)
                {
                    // 调用透明度控制器创建预览专用mesh
                    _transparencyController.CreatePreviewMesh(data);
                }

                if (data.PreviewMesh != null)
                {
                    return data.PreviewMesh;
                }
                else
                {
                    // 如果无法创建预览专用mesh，回退到使用处理后的mesh或原始mesh
                    return data.ProcessedMesh != null ? data.ProcessedMesh : data.OriginalMesh;
                }
            }
            else
            {
                // 否则使用处理后的mesh或原始mesh
                return data.ProcessedMesh != null ? data.ProcessedMesh : data.OriginalMesh;
            }
        }

        /// <summary>
        /// 设置透明度可视化参数
        /// </summary>
        /// <param name="mode">可视化模式</param>
        /// <param name="threshold">透明度阈值</param>
        /// <param name="showTriangleTransparency">是否显示三角面透明度</param>
        /// <param name="strength">可视化强度</param>
        public void SetVisualizationParameters(TransparencyVisualizerUtility.VisualizationMode mode, float threshold, bool showTriangleTransparency, float strength)
        {
            TransparencyVisualizerUtility.SetVisualizationParameters(mode, threshold, showTriangleTransparency, strength);
        }

        /// <summary>
        /// 设置主纹理
        /// </summary>
        /// <param name="texture">纹理</param>
        public void SetMainTexture(Texture texture)
        {
            TransparencyVisualizerUtility.SetMainTexture(texture);
        }

        /// <summary>
        /// 渲染网格预览
        /// </summary>
        /// <param name="previewRect">预览区域</param>
        /// <param name="selectedIndex">选中对象索引</param>
        /// <param name="previewRotation">预览旋转</param>
        /// <param name="previewZoom">预览缩放</param>
        public void RenderMeshPreview(Rect previewRect, int selectedIndex, Vector2 previewRotation, float previewZoom)
        {
            if (selectedIndex < 0)
                return;

            var processingDataList = _workflowController.SelectionController.ProcessingDataList;
            if (processingDataList == null || selectedIndex >= processingDataList.Count)
                return;

            var selectedData = processingDataList[selectedIndex];
            if (selectedData == null || selectedData.OriginalGameObject == null)
                return;

            // 获取网格和材质
            MeshFilter meshFilter = selectedData.OriginalGameObject.GetComponent<MeshFilter>();
            Renderer renderer = selectedData.OriginalGameObject.GetComponent<Renderer>();

            if (meshFilter == null || meshFilter.sharedMesh == null)
                return;

            try
            {
                // 获取纹理
                Texture mainTexture = null;
                if (renderer != null && renderer.sharedMaterial != null)
                {
                    mainTexture = renderer.sharedMaterial.mainTexture;
                }

                // 创建半透明材质
                Material material = MeshPreviewUtility.CreateTransparentMaterial(mainTexture, 0.7f);

                if (material != null)
                {
                    // 渲染网格预览
                    PreviewRenderUtility previewUtility = MeshPreviewUtility.CreatePreviewUtility();

                    try
                    {
                        MeshPreviewUtility.RenderMeshPreview(
                            previewUtility,
                            previewRect,
                            meshFilter.sharedMesh,
                            material,
                            previewRotation,
                            previewZoom,
                            true);
                    }
                    finally
                    {
                        // 清理资源，使用MeshPreviewUtility.CleanupPreviewUtility确保它被正确从跟踪列表中移除
                        MeshPreviewUtility.CleanupPreviewUtility(previewUtility);

                        // 减少材质引用计数
                        ResourceManager.Instance.ReleaseMaterial(material);
                    }
                }
            }
            catch (System.Exception)
            {
                // 忽略异常
            }
        }

        /// <summary>
        /// 渲染细分网格预览
        /// </summary>
        /// <param name="previewRect">预览区域</param>
        /// <param name="selectedIndex">选中对象索引</param>
        /// <param name="showSubdivisionVertices">是否显示细分顶点</param>
        /// <param name="previewRotation">预览旋转</param>
        /// <param name="previewZoom">预览缩放</param>
        public void RenderSubdivisionPreview(Rect previewRect, int selectedIndex, bool showSubdivisionVertices,
                                           Vector2 previewRotation, float previewZoom)
        {
            if (selectedIndex < 0)
                return;

            var processingDataList = _workflowController.SelectionController.ProcessingDataList;
            if (processingDataList == null || selectedIndex >= processingDataList.Count)
                return;

            var selectedData = processingDataList[selectedIndex];
            if (selectedData == null)
                return;

            // 获取网格
            Mesh meshToPreview = selectedData.ProcessedMesh != null ?
                selectedData.ProcessedMesh : selectedData.OriginalMesh;

            if (meshToPreview == null)
                return;

            // 创建预览渲染工具，使用MeshPreviewUtility确保它被正确跟踪和清理
            PreviewRenderUtility previewUtility = MeshOptimizer.Utilities.MeshPreviewUtility.CreatePreviewUtility();
            GameObject previewObject = null;
            Material material = null;
            Material wireframeMaterial = null;

            try
            {
                previewUtility.camera.transform.position = new Vector3(0, 0, -6);
                previewUtility.camera.transform.rotation = Quaternion.identity;
                previewUtility.camera.nearClipPlane = 0.3f;
                previewUtility.camera.farClipPlane = 1000f;
                previewUtility.lights[0].intensity = 1.4f;
                previewUtility.lights[0].transform.rotation = Quaternion.Euler(40f, 40f, 0);
                previewUtility.lights[1].intensity = 1.4f;

                // 计算合适的缩放比例 - 统一所有预览视图的显示尺寸，放大2倍
                float objectSize = meshToPreview.bounds.size.magnitude;
                float scaleFactor = 2.0f / objectSize; // 放大为原来的2倍

                // 创建临时游戏对象
                previewObject = new GameObject("PreviewObject");
                MeshFilter meshFilter = previewObject.AddComponent<MeshFilter>();
                meshFilter.sharedMesh = meshToPreview;

                // 设置对象变换 - 旋转180度使正面朝向摄像机
                previewObject.transform.position = Vector3.zero;
                previewObject.transform.rotation = Quaternion.Euler(previewRotation.x, previewRotation.y + 180f, 0f);
                previewObject.transform.localScale = Vector3.one * scaleFactor * previewZoom;

                if (showSubdivisionVertices)
                {
                    try
                    {
                        // 使用顶点颜色材质
                        material = ResourceManager.Instance.GetMaterial("Hidden/MeshOptimizer/VertexColorShader", "VertexColorMaterial", mat => {
                            mat.SetFloat("_ShowSubdivision", 1);
                        });

                        // 如果找不到自定义着色器，尝试从Resources加载
                        if (material == null)
                        {
                            Shader vertexColorShader = Resources.Load<Shader>("VertexColorShader");
                            if (vertexColorShader != null)
                            {
                                material = ResourceManager.Instance.GetMaterial(vertexColorShader.name, "VertexColorMaterial", mat => {
                                    mat.SetFloat("_ShowSubdivision", 1);
                                });
                            }
                        }

                        // 如果仍然找不到，使用Unlit/Color着色器
                        if (material == null)
                        {
                            material = ResourceManager.Instance.GetMaterial("Unlit/Color", "VertexColorMaterial", mat => {
                                mat.color = Color.white;
                            });
                        }
                    }
                    catch (System.Exception)
                    {
                        // 忽略异常
                    }
                }
                else
                {
                    try
                    {
                        // 使用半透明材质，从MeshProcessingData中获取贴图
                        Texture mainTexture = MeshPreviewUtility.GetTextureFromMeshProcessingData(selectedData);

                        // 创建半透明材质
                        material = MeshPreviewUtility.CreateTransparentMaterial(mainTexture, 0.7f);

                        // 如果无法创建材质或没有原始对象，创建一个默认的半透明材质
                        if (material == null)
                        {
                            material = ResourceManager.Instance.GetMaterial("Standard", "DefaultTransparentMaterial", mat => {
                                mat.SetFloat("_Mode", 3); // 设置为Transparent模式
                                mat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                                mat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                                mat.SetInt("_ZWrite", 0);
                                mat.DisableKeyword("_ALPHATEST_ON");
                                mat.EnableKeyword("_ALPHABLEND_ON");
                                mat.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                                mat.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
                                mat.color = new Color(1f, 1f, 1f, 0.7f);
                            });
                        }
                    }
                    catch (System.Exception)
                    {
                        // 忽略异常
                    }
                }

                // 添加渲染器组件
                MeshRenderer meshRenderer = previewObject.AddComponent<MeshRenderer>();
                meshRenderer.sharedMaterial = material;

                // 渲染模型
                previewUtility.BeginPreview(previewRect, GUIStyle.none);
                previewUtility.camera.clearFlags = CameraClearFlags.SolidColor;
                previewUtility.camera.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
                previewUtility.AddSingleGO(previewObject);
                previewUtility.camera.Render();
                Texture resultRender = previewUtility.EndPreview();
                GUI.DrawTexture(previewRect, resultRender, ScaleMode.StretchToFill, false);

                // 渲染线框 - 使用基于重心坐标的线框渲染
                try
                {
                    // 创建带有重心坐标的网格
                    Mesh wireframeMesh = BarycentricWireframeUtility.AddBarycentricCoordinates(meshToPreview);

                    // 更新网格过滤器
                    meshFilter.sharedMesh = wireframeMesh;

                    // 创建线框材质
                    Material barycentricWireframeMaterial = BarycentricWireframeUtility.CreateWireframeMaterial(
                        Color.black,           // 线框颜色
                        new Color(0, 0, 0, 0), // 填充颜色（透明）
                        0.05f,                 // 线框粗细
                        true                   // 使用屏幕空间线宽
                    );

                    // 开始预览
                    previewUtility.BeginPreview(previewRect, GUIStyle.none);
                    previewUtility.camera.clearFlags = CameraClearFlags.SolidColor;
                    previewUtility.camera.backgroundColor = new Color(0, 0, 0, 0);

                    // 设置线框材质
                    meshRenderer.sharedMaterial = barycentricWireframeMaterial;

                    // 添加对象到预览
                    previewUtility.AddSingleGO(previewObject);

                    // 渲染线框
                    previewUtility.camera.Render();
                    Texture wireframeRender = previewUtility.EndPreview();

                    // 绘制结果
                    GUI.DrawTexture(previewRect, wireframeRender, ScaleMode.StretchToFill, true);

                    // 清理资源
                    if (wireframeMesh != null)
                    {
                        Object.DestroyImmediate(wireframeMesh);
                    }

                    if (barycentricWireframeMaterial != null)
                    {
                        Object.DestroyImmediate(barycentricWireframeMaterial);
                    }

                    // 恢复原始网格
                    meshFilter.sharedMesh = meshToPreview;
                }
                catch (System.Exception e)
                {
                    // 渲染线框时出错
                    Debug.LogError("渲染线框时出错: " + e.Message);
                    try { previewUtility.EndPreview(); } catch { }
                }
            }
            catch (System.Exception)
            {
                // 渲染细分预览时出错
            }
            finally
            {
                // 确保在任何情况下都清理资源
                if (previewObject != null)
                {
                    Object.DestroyImmediate(previewObject);
                }

                if (previewUtility != null)
                {
                    // 使用MeshPreviewUtility.CleanupPreviewUtility确保它被正确从跟踪列表中移除
                    MeshPreviewUtility.CleanupPreviewUtility(previewUtility);
                }

                // 减少材质引用计数
                if (material != null)
                {
                    ResourceManager.Instance.ReleaseMaterial(material);
                }

                // 减少线框材质引用计数
                if (wireframeMaterial != null)
                {
                    ResourceManager.Instance.ReleaseMaterial(wireframeMaterial);
                }
            }
        }

        /// <summary>
        /// 渲染优化预览
        /// </summary>
        /// <param name="previewRect">预览区域</param>
        /// <param name="selectedIndex">选中对象索引</param>
        /// <param name="previewRotation">预览旋转</param>
        /// <param name="previewZoom">预览缩放</param>
        public void RenderOptimizationPreview(Rect previewRect, int selectedIndex, Vector2 previewRotation, float previewZoom)
        {
            if (selectedIndex < 0)
                return;

            var processingDataList = _workflowController.SelectionController.ProcessingDataList;
            if (processingDataList == null || selectedIndex >= processingDataList.Count)
                return;

            var selectedData = processingDataList[selectedIndex];
            if (selectedData == null)
                return;

            // 确保预览渲染器已初始化
            if (_optimizationPreviewRenderer == null)
            {
                _optimizationPreviewRenderer = new Views.PreviewRenderers.OptimizationPreviewRenderer();
                _optimizationPreviewRenderer.Initialize();
            }

            // 设置预览数据
            _optimizationPreviewRenderer.SetMeshData(selectedData);

            // 设置旋转和缩放
            _optimizationPreviewRenderer.SetRotationAndZoom(previewRotation, previewZoom);

            // 绘制预览 - 使用传入的预览区域
            if (previewRect != Rect.zero)
            {
                // 如果传入了有效的预览区域，使用它
                _optimizationPreviewRenderer.DrawGUI(previewRect);
            }
            else
            {
                // 否则使用默认方法创建预览区域
                _optimizationPreviewRenderer.DrawGUI();
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public override void Cleanup()
        {
            if (_previewService != null)
            {
                _previewService.Cleanup();
            }

            // 清理预览渲染器
            if (_meshPreviewRenderer != null)
            {
                _meshPreviewRenderer.Cleanup();
                _meshPreviewRenderer = null;
            }

            if (_optimizationPreviewRenderer != null)
            {
                _optimizationPreviewRenderer.Cleanup();
                _optimizationPreviewRenderer = null;
            }

            base.Cleanup();
        }
    }
}
