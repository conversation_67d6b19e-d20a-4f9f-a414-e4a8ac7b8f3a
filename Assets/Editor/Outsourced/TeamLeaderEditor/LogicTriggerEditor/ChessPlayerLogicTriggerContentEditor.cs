using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class ChessPlayerLogicTriggerContentEditor : EditorSubPanelBase
{
    protected ChessPlayerLogicTriggerEditor window;
    protected ChessPlayerTriggerInfo m_info = null;

    private UnityEditorInternal.ReorderableList m_conditionList;
    private UnityEditorInternal.ReorderableList m_actionList;

    private string[] m_varDisplayList = null;

    public void SetController(ChessPlayerLogicTriggerEditor window)
    {
        this.window = window;

        InitConditionList();
        InitActionList();
    }

    public void OnVarChanged(List<CSoTriggerVar> vars)
    {
        m_varDisplayList = new string[vars.Count];
        for (int i = 0; i < vars.Count; i++)
        {
            m_varDisplayList[i] = vars[i].name;
        }
    }

    public void OnTriggerSelected(ChessPlayerTriggerInfo info)
    {
        m_info = info;
        if (info != null)
        {
            m_conditionList.list = info.m_condition;
            m_actionList.list = info.m_actions;
        }
        else
        {
            m_conditionList.list = null;
            m_actionList.list = null;
        }
    }

    public override void OnDraw()
    {
        if (m_info != null)
        {
            EditorGUI.BeginChangeCheck();
            m_info.name = EditorGUILayout.TextField("触发器名称: ", m_info.name);
            if (EditorGUI.EndChangeCheck())
            {
                window.OnTriggerNameChanged();
            }

            m_conditionList?.DoLayoutList();
            m_actionList?.DoLayoutList();
        }
    }

    private void InitConditionList()
    {
        m_conditionList = new UnityEditorInternal.ReorderableList(null, typeof(CSoTriggerConditionInfo), false, true, true, true);
        m_conditionList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            var condition = (m_conditionList.list as List<CSoTriggerConditionInfo>)[index];
            DrawCondition(rect, condition, m_conditionList.elementHeight);
        };

        m_conditionList.onAddCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            (list.list as List<CSoTriggerConditionInfo>).Add(new CSoTriggerConditionInfo());
        };

        m_conditionList.onRemoveCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            if (list.index < 0)
                return;

            list.list.RemoveAt(list.index);
        };

        m_conditionList.drawHeaderCallback = (Rect rect) =>
        {
            GUI.Label(rect, "条件列表");
        };

        m_conditionList.elementHeightCallback = delegate (int index)
        {
            var condition = (m_conditionList.list as List<CSoTriggerConditionInfo>)[index];
            if (condition.type == E_SO_TRIGGER_TYPE.VarChanged
                || condition.type == E_SO_TRIGGER_TYPE.CheckVar)
                return m_conditionList.elementHeight * 4;
            return m_conditionList.elementHeight * 3;
        };
    }

    private void InitActionList()
    {
        m_actionList = new UnityEditorInternal.ReorderableList(null, typeof(CSoTriggerConditionInfo), false, true, true, true);
        m_actionList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            var action = (m_actionList.list as List<CSoAction>)[index];
            DrawAction(rect, action, m_actionList.elementHeight);
        };

        m_actionList.onAddCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            (list.list as List<CSoAction>).Add(new CSoAction());
        };

        m_actionList.onRemoveCallback = delegate (UnityEditorInternal.ReorderableList list)
        {
            if (list.index < 0)
                return;

            list.list.RemoveAt(list.index);
        };

        m_actionList.drawHeaderCallback = (Rect rect) =>
        {
            GUI.Label(rect, "行为列表");
        };

        m_actionList.elementHeightCallback = delegate (int index)
        {
            var action = (m_actionList.list as List<CSoAction>)[index];
            //if (action.action == E_SO_TRIGGER_ACTION.Transform)
            //    return m_actionList.elementHeight * 5;
            if (action.action == E_SO_TRIGGER_ACTION.SetVar
            || action.action == E_SO_TRIGGER_ACTION.AddVar
            || action.action == E_SO_TRIGGER_ACTION.SubVar)
                return m_actionList.elementHeight * 4;
            return m_actionList.elementHeight * 3;
        };
    }

    private string DrawVar(Rect rect, string propertyName, string varName)
    {
        if (m_varDisplayList == null || m_varDisplayList.Length == 0)
        {
            EditorGUI.LabelField(rect, "请添加变量");
            return string.Empty;
        }
        else
        {
            var varIndex = Array.IndexOf(m_varDisplayList, varName);
            if (varIndex == -1)
            {
                varIndex = 0;
                varName = m_varDisplayList[0];
            }
            var newVarIndex = EditorGUI.Popup(rect, propertyName, varIndex, m_varDisplayList);
            if (newVarIndex != varIndex)
            {
                varName = m_varDisplayList[newVarIndex];
            }
            return varName;
        }
    }

    private void DrawConditionValue(Rect rect, CSoTriggerConditionInfo condition)
    {
        string conditionValueName = "";
        switch (condition.type)
        {
            case E_SO_TRIGGER_TYPE.ChangeTurnState:
                conditionValueName = "回合状态";
                break;
            case E_SO_TRIGGER_TYPE.TurnStart:
                conditionValueName = "回合数";
                break;
            case E_SO_TRIGGER_TYPE.PlayerOut:
                conditionValueName = "剩余玩家数";
                break;
            case E_SO_TRIGGER_TYPE.ConWin:
                conditionValueName = "连胜次数";
                break;
            case E_SO_TRIGGER_TYPE.ConLose:
                conditionValueName = "连败次数";
                break;
            case E_SO_TRIGGER_TYPE.ActiveFetter:
                conditionValueName = "激活羁绊";
                break;
            case E_SO_TRIGGER_TYPE.UnactiveFetter:
                conditionValueName = "移除羁绊";
                break;
            case E_SO_TRIGGER_TYPE.LevelUp:
                conditionValueName = "等级";
                break;
            case E_SO_TRIGGER_TYPE.MeetTiny:
                conditionValueName = "对方小小英雄Id";
                break;
            case E_SO_TRIGGER_TYPE.WinTiny:
                conditionValueName = "对方小小英雄Id";
                break;
            case E_SO_TRIGGER_TYPE.PickDropbox:
                conditionValueName = "拾取方式";
                break;
            case E_SO_TRIGGER_TYPE.WillPickDropbox:
                break;
            case E_SO_TRIGGER_TYPE.PickAllDropbox:
                conditionValueName = "拾取方式";
                break;
            case E_SO_TRIGGER_TYPE.BuyHero:
            case E_SO_TRIGGER_TYPE.HeroStarUp:
            case E_SO_TRIGGER_TYPE.HeroUp:
            case E_SO_TRIGGER_TYPE.HeroSelect:
                conditionValueName = "英雄ID";
                break;
            case E_SO_TRIGGER_TYPE.ChangeModel:
                conditionValueName = "按钮ID";
                break;
            case E_SO_TRIGGER_TYPE.VarChanged:
            case E_SO_TRIGGER_TYPE.CheckVar:
                conditionValueName = "变量值";
                break;
            default:
                break;
        }

        if (!string.IsNullOrEmpty(conditionValueName))
            condition.value = EditorGUI.IntField(rect, conditionValueName, condition.value);
    }

    private Rect DrawCondition(Rect rect, CSoTriggerConditionInfo condition, float lineHeight)
    {
        Rect itemRect = rect;
        itemRect.height = lineHeight;
        condition.type = (E_SO_TRIGGER_TYPE)EditorGUI.EnumPopup(itemRect, "触发条件", condition.type);

        if (condition.type == E_SO_TRIGGER_TYPE.VarChanged
            || condition.type == E_SO_TRIGGER_TYPE.CheckVar)
        {
            itemRect.y += lineHeight;
            condition.varName = DrawVar(itemRect, "变量名", condition.varName);
        }

        itemRect.y += lineHeight;
        condition.compare = (E_SO_TRIGGER_COMPARE)EditorGUI.EnumPopup(new Rect(itemRect.x, itemRect.y, itemRect.width / 2, itemRect.height), "比较方式", condition.compare);
        if (condition.compare != E_SO_TRIGGER_COMPARE.AnyValue)
        {
            DrawConditionValue(new Rect(itemRect.x + itemRect.width / 2, itemRect.y, itemRect.width / 2, itemRect.height), condition);
        }

        itemRect.y += lineHeight;
        condition.onlyBattleCheck = EditorGUI.Toggle(itemRect, "仅战斗触发", condition.onlyBattleCheck);

        return rect;
    }

    private Rect DrawAction(Rect rect, CSoAction actionInfo, float lineHeight)
    {
        float height = rect.y;
        var r = new Rect(rect.x, height, rect.width, lineHeight);
        height += r.height;
        actionInfo.action = (E_SO_TRIGGER_ACTION)EditorGUI.EnumPopup(r, "行为", actionInfo.action);
        r = new Rect(rect.x, height, rect.width, lineHeight);
        height += r.height;
        actionInfo.actionMode = (E_SO_TRIGGER_ACTION_MODE)EditorGUI.EnumPopup(r, "触发模式", actionInfo.actionMode);

        switch (actionInfo.action)
        {
            case E_SO_TRIGGER_ACTION.PlayAnimation:
                actionInfo.strParam = EditorGUI.TextField(new Rect(rect.x, height, rect.width, lineHeight), "动作名称", actionInfo.strParam);
                break;
            case E_SO_TRIGGER_ACTION.PlayTinyEffect:
                actionInfo.strParam = EditorGUI.TextField(new Rect(rect.x, height, rect.width / 2, lineHeight), "特效名称", actionInfo.strParam);
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x + rect.width / 2, height, rect.width / 2, lineHeight), "挂点", actionInfo.intParam);
                break;
            case E_SO_TRIGGER_ACTION.PlayExpression:
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height, rect.width, lineHeight), "表情ID", actionInfo.intParam);
                break;
            //case E_SO_TRIGGER_ACTION.PlayRandomExpression:
            //    actionInfo.strParam = EditorGUI.TextField(new Rect(rect.x, rect.y + lineHeight, rect.width, lineHeight), "表情ID(用;隔开)", actionInfo.strParam);
            //    break;
            //case E_SO_TRIGGER_ACTION.Teleport:
            //    break;
            case E_SO_TRIGGER_ACTION.Transform:
                float singleWidth = rect.width;
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height, singleWidth, lineHeight), "目标小小英雄ID", actionInfo.intParam, EditorStyles.numberField);
                //actionInfo.strParam = EditorGUI.TextField(new Rect(rect.x, height + lineHeight, singleWidth, lineHeight), "变身动作", actionInfo.strParam);
                //actionInfo.floatParam = EditorGUI.FloatField(new Rect(rect.x, height + lineHeight * 2, singleWidth, lineHeight), "变身时间(s)", actionInfo.floatParam);
                break;
            case E_SO_TRIGGER_ACTION.Summon:
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height, rect.width, lineHeight), "召唤小小英雄ID", actionInfo.intParam);
                break;
            case E_SO_TRIGGER_ACTION.SetVar:
                actionInfo.strParam = DrawVar(new Rect(rect.x, height, rect.width, lineHeight), "变量名", actionInfo.strParam);
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height + lineHeight, rect.width, lineHeight), "变量值", actionInfo.intParam);
                break;
            case E_SO_TRIGGER_ACTION.AddVar:
                actionInfo.strParam = DrawVar(new Rect(rect.x, height, rect.width, lineHeight), "变量名", actionInfo.strParam);
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height + lineHeight, rect.width, lineHeight), "增加值", actionInfo.intParam);
                break;
            case E_SO_TRIGGER_ACTION.SubVar:
                actionInfo.strParam = DrawVar(new Rect(rect.x, height, rect.width, lineHeight), "变量名", actionInfo.strParam);
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height + lineHeight, rect.width, lineHeight), "减少值", actionInfo.intParam);
                break;
            case E_SO_TRIGGER_ACTION.LockMove:
                actionInfo.intParam = EditorGUI.IntField(new Rect(rect.x, height, rect.width, lineHeight), "锁定移动时间(ms)", actionInfo.intParam);
                break;
            default:
                break;
        }

        //rect.height = 50;
        return rect;
    }
}
