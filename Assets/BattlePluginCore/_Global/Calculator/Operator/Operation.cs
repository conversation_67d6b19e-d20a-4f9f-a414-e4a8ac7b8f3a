using GameFramework.FMath;
using ProtoBuf;
using System;
using System.Collections;

[Serializable]
[ProtoContract]
public class Operation : IClear
{
    [ProtoMember(1)]
    public OperationType type;

    [ProtoMember(2)]
    public string strValue;

    [NonSerialized]
    private Fix64 numValueFix64;
    //是否从池子创建
    [NonSerialized]
    public bool isCreateFromPool = false;
    [NonSerialized]
    private bool isInit = false;

    private void Init()
    {
        if (!isInit)
        {
            if (type == OperationType.VALUE)
            {
                Double num = 0;
                Double.TryParse(strValue, out num);
                this.numValueFix64 = Fix64.FromDouble(num);
                strValue = string.Empty;
            }
#if (UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE) || !ACGGAME_CLIENT
            else
            {
                strValue = string.Intern(strValue);
            }
#endif

            isInit = true;
        }
    }

    public void Clear()
    {
        type = OperationType.VALUE;
        strValue = string.Empty;
        numValueFix64 = Fix64.zero;
        isInit = false;
    }

    public Operation()
    {

    }

    public Operation(OperationType type, string value)
    {
        this.type = type;
        if (type == OperationType.VALUE)
        {
            Double num = 0;
            Double.TryParse(value, out num);
            this.numValueFix64 = Fix64.FromDouble(num);
#if (UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE) || !ACGGAME_CLIENT
            strValue = value;
#else
            strValue = string.Empty;
#endif
        }
        else
        {
            strValue = string.Intern(value);
        }

        isInit = true;
    }


    public OperationType GetOperationType()
    {
        return type;
    }

    public string GetValue()
    {
#if (UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE) || !ACGGAME_CLIENT
        Init();
#endif
        return strValue;
    }

    public Fix64 GetNumValue()
    {
        Init();
        return numValueFix64;
    }

    public override string ToString()
    {
#if (UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE) || !ACGGAME_CLIENT
        Init();
#endif
        return strValue;
    }

    public void CopyData(Operation copyData)
    {
        copyData.Init();
        this.type = copyData.type;
        this.strValue = copyData.strValue;
        this.numValueFix64 = copyData.numValueFix64;
        this.isInit = true;
    }

    public void SetData(OperationType type, Fix64 value)
    {
        this.type = type;
        this.strValue = string.Empty;
        this.numValueFix64 = value;
        this.isInit = true;
    }

    public void CopyFrom(Operation data)
    {
        data.Init();
        this.type = data.type;
        this.strValue = data.strValue;
        this.isInit = true;
    }
}
