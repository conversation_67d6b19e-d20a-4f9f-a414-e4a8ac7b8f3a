using UnityEngine;
using System.Collections;
using GameFramework.FMath;

public class RightBracket : Symbol
{

    public override string Flag()
    {
        return SymbolName.Base_RightBracket;
    }

    public override SymbolPriority Priority()
    {
        return SymbolPriority.USELESS;
    }

    public override int NumOfSymbol()
    {
        return 0;
    }

    public override Fix64 Cal(Fix64[] values)
    {
        return Fix64.zero;
    }
}
