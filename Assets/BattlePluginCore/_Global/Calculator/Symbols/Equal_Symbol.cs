using UnityEngine;
using System.Collections;
using GameFramework.FMath;

public class Equal_Symbol : Symbol
{

    public override string Flag()
    {
        return SymbolName.Base_Equal;
    }

    public override SymbolPriority Priority()
    {
        return SymbolPriority.EQUAL;
    }

    public override int NumOfSymbol()
    {
        return 2;
    }

    public override Fix64 Cal(Fix64[] values)
    {
        return values[0] == values[1] ? Fix64.one : Fix64.zero;
    }
}
