using UnityEngine;
using System.Collections;
using GameFramework.FMath;

public class Floor_Symbol : Symbol
{

    public override string Flag()
    {
        return SymbolName.Base_Floor;
    }

    public override SymbolPriority Priority()
    {
        return SymbolPriority.CUSTOM;
    }

    public override int NumOfSymbol()
    {
        return 1;
    }

    public override Fix64 Cal(Fix64[] values)
    {
        if (values.Length == 0) return Fix64.zero;
        return values[0].Floor().ToFix64();
    }
}
