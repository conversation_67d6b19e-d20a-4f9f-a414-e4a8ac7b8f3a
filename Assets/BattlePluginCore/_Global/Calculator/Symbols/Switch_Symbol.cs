using UnityEngine;
using System.Collections;
using GameFramework.FMath;

/// <summary>
/// 应对3层不同效果
/// </summary>
[EnumName("switch条件判断")]
[ST_Param("输入值", defaultValue = "")]
[ST_Param("条件1", defaultValue = "")]
[ST_Param("条件2", defaultValue = "")]
[ST_Param("条件3", defaultValue = "")]
[ST_Param("条件4", defaultValue = "")]
[ST_Param("条件5", defaultValue = "")]
[ST_Param("条件6", defaultValue = "")]
[ST_Param("结果1", defaultValue = "")]
[ST_Param("结果2", defaultValue = "")]
[ST_Param("结果3", defaultValue = "")]
[ST_Param("结果4", defaultValue = "")]
[ST_Param("结果5", defaultValue = "")]
[ST_Param("结果6", defaultValue = "")]
public class Switch_Symbol : Symbol
{

    public override string Flag()
    {
        return SymbolName.Base_Switch;
    }

    public override SymbolPriority Priority()
    {
        return SymbolPriority.CUSTOM;
    }

    public override int NumOfSymbol()
    {
        return 13;
    }

    public override Fix64 Cal(Fix64[] values)
    {
        if (values[0] == values[1]) return values[7];
        else if (values[0] == values[2]) return values[8];
        else if (values[0] == values[3]) return values[9];
        else if (values[0] == values[4]) return values[10];
        else if (values[0] == values[5]) return values[11];
        else if (values[0] == values[6]) return values[12];
        return values[7];
    }
}
