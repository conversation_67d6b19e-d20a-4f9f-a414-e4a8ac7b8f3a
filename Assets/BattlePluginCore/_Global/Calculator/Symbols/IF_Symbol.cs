using UnityEngine;
using System.Collections;
using GameFramework.FMath;

[EnumName("if条件判断")]
[ST_Param("条件", defaultValue = "条件")]
[ST_Param("成功", defaultValue = "成功")]
[ST_Param("失败", defaultValue = "失败")]
public class IF_Symbol : Symbol
{

    public override string Flag()
    {
        return SymbolName.Base_IF;
    }

    public override SymbolPriority Priority()
    {
        return SymbolPriority.CUSTOM;
    }

    public override int NumOfSymbol()
    {
        return 3;
    }

    public override Fix64 Cal(Fix64[] values)
    {
        return values[0] > Fix64.zero ? values[1] : values[2];
    }
}
