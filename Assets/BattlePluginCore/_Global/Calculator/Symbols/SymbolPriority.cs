using UnityEngine;
using System.Collections;

public enum SymbolPriority
{
    /**
	 * 没用，类似左右括号
	 */
    USELESS = 0,
    /**
     * 最低优先级
     */
    MIN = 1,
    /**
* 逻辑或
*/
    LOGIC_OR = 8,
    /**
* 逻辑与
*/
    LOGIC_AND = 9,
    /**
 * 判断相等
 */
    EQUAL = 13,
    /**
   * 判断
   */
    JUDGE = 14,
    /**
     * 加
     */
    PLUS = 16,
    /**
     * 减
     */
    RDUCE = 16,
    /**
     * 乘
     */
    MULT = 17,
    /**
     * 除
     */
    DIVIDE = 17,
    /**
     * 
     */
    CUSTOM = 19,
    /**
     * 最高
     */
    MAX = 20,
}
