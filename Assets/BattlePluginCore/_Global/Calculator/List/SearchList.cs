using System.Collections;

public class SearchList<T>
{

    private OperatorNode<T> first;
    private OperatorNode<T> last;

    public SearchList()
    {
    }

    /**
	 * 添加元素
	 * 
	 * @param e
	 */
    public void Add(T e)
    {
        if (first == null)
        {
            first = new OperatorNode<T>(e, null);
        }
        else
        {
            if (last == null)
            {
                last = new OperatorNode<T>(e, null);
                first.next = last;// 因为first和last还没建立关系，所以在这里要将他们的关系建立起来
            }
            else
            {
                OperatorNode<T> n = new OperatorNode<T>(e, null);// 一个临时的引用n
                last.next = n;// 将last的next赋值为n的引用
                last = n;// 然后再将last重新赋值为n的引用
            }
        }
    }

    public OperatorNode<T> GetFirst()
    {
        return first;
    }
}
