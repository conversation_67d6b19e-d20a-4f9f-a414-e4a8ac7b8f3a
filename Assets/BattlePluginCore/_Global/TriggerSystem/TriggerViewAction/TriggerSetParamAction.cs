#if ACGGAME_CLIENT

using System;
using System.Collections.Generic;
using System.IO;
using ZGameChess;

namespace TriggerSystem
{
    [System.Serializable]
    public class TriggerSetParamActionWrapper : TriggerViewAction
    {
        public TriggerSetParamBaseAction setParamAction;
        public byte[] m_data;
        
        public static void Init(Dictionary<TriggerEnum.E_TRIGGER_ACTION, Func<TriggerViewAction>> dic)
        {
            TriggerViewAction.Init<TriggerSetParamActionWrapper>(dic, TriggerEnum.E_TRIGGER_ACTION.SetParam);
        }

        public override TriggerEnum.E_TRIGGER_ACTION GetActionType()
        {
            return TriggerEnum.E_TRIGGER_ACTION.SetParam;
        }

        public override void Execute(TriggerViewSystem system)
        {
            setParamAction.Execute(system);
        }

        public override bool isCorrect(LinkedHashMap<string, BaseTriggerParam> paramDict)
        {
            return setParamAction.isCorrect(paramDict);
        }

        public void Read(BinaryReader br)
        {
            TriggerEnum.E_TRIGGER_PARAM param = (TriggerEnum.E_TRIGGER_PARAM) br.ReadByte();

            ParamCondtion paramCondtion = null;
            if(TriggerSystemConfig.defaultParamDic.TryGetValue(param, out paramCondtion))
            {
                setParamAction = paramCondtion.createAction();
                setParamAction.Read(br);
            }
        }

#if UNITY_EDITOR
        
        public void Write(BinaryWriter bw)
        {
            bw.Write((byte)setParamAction.GetParamType());
            setParamAction.Write(bw);
        }
        
#endif
    }
    
    public abstract class TriggerSetParamBaseAction : TriggerViewAction
    {
        public abstract TriggerEnum.E_TRIGGER_PARAM GetParamType();

        public abstract void Read(BinaryReader br);
        
#if UNITY_EDITOR
        public abstract void Write(BinaryWriter br);
#endif
    }
    
    public class TriggerSetParamAction<T> : TriggerSetParamBaseAction
    {
        public string m_paramName;
        public T m_value;

        public TriggerSetParamAction()
        {
            m_value = Specializer<T>.GetDefaultValue();
        }

        public override TriggerEnum.E_TRIGGER_ACTION GetActionType()
        {
            return TriggerEnum.E_TRIGGER_ACTION.SetParam;
        }

        public override TriggerEnum.E_TRIGGER_PARAM GetParamType()
        {
            return Specializer<T>.GetDataType();
        }

        public override void Execute(TriggerViewSystem system)
        {
            ChessPlayerUnit playerUnit = system.data as ChessPlayerUnit;
            if (playerUnit != null)
            {
                playerUnit.SetTriggerParam<T>(m_paramName, m_value);
            }
        }

        public override bool isCorrect(LinkedHashMap<string, BaseTriggerParam> paramDict)
        {
            return !string.IsNullOrEmpty(m_paramName) && paramDict.ContainsKey(m_paramName);
        }

        public override void Read(BinaryReader br)
        {
            m_paramName = br.ReadString();
            m_value = Specializer<T>.Read(br);
        }

#if UNITY_EDITOR
        
        public override void Write(BinaryWriter bw)
        {
            bw.Write(m_paramName);
            Specializer<T>.Write(bw, m_value);
        }
        
#endif
    }
}

#endif
