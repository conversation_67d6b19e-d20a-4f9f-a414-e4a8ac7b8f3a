#if ACGGAME_CLIENT

using System.Collections.Generic;
using System.IO;
using GameFramework.FMath;

#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
#endif

namespace TriggerSystem
{
    public static class TriggerFix64Type
    {
        public static void Init(Dictionary<TriggerEnum.E_TRIGGER_PARAM,ParamCondtion> dic)
        {
            Specializer<Fix64>.ReadFunc = Read;
            Specializer<Fix64>.WriteFunc = Write;
            Specializer<Fix64>.CompareFunc = Compare;
            Specializer<Fix64>.DataTypeFunc = GetDataType;
            Specializer<Fix64>.DefaultValueFunc = DefaultValue;
            
            Specializer<Fix64>.Init(dic);
            
#if UNITY_EDITOR
            InitEditor();
#endif
        }
        
        private static Fix64 DefaultValue()
        {
            return Fix64.zero;
        }
        
        private static TriggerEnum.E_TRIGGER_PARAM GetDataType()
        {
            return TriggerEnum.E_TRIGGER_PARAM.FIX64;
        }

        private static Fix64 Read(BinaryReader br)
        {
            long rawValue = br.ReadInt64();
            return Fix64.FromRawValue(rawValue);
        }
        
        private static void Write(BinaryWriter bw, Fix64 value)
        {
            bw.Write(value.rawValue);
        }
        
        private static bool Compare(Fix64 a, Fix64 b, TriggerEnum.E_TRIGGER_CONDITION compareFunc)
        {
            switch (compareFunc)
            {
                case TriggerEnum.E_TRIGGER_CONDITION.Equal:
                    return a == b;
                case TriggerEnum.E_TRIGGER_CONDITION.NotEqual:
                    return a != b;
                case TriggerEnum.E_TRIGGER_CONDITION.Greater:
                    return a > b;
                case TriggerEnum.E_TRIGGER_CONDITION.Less:
                    return a < b;
                case TriggerEnum.E_TRIGGER_CONDITION.GreaterOrEqual:
                    return a >= b;
                case TriggerEnum.E_TRIGGER_CONDITION.LessOrEqual:
                    return a <= b;
                case TriggerEnum.E_TRIGGER_CONDITION.AnyValue:
                    return true;
                default:
                    return false;
            }
        }
        
#if UNITY_EDITOR
        
        private static void InitEditor()
        {
            Specializer<Fix64>.DrawValueFunc = DrawValue;
        }
        
        private static Fix64 DrawValue(Rect rect, string label, Fix64 oldValue)
        {
            float tmpValue = oldValue.ToSingle();
            tmpValue =  EditorGUI.FloatField(rect, label, tmpValue);
            return tmpValue.ToFix64();
        }
        
#endif
    }
}

#endif
