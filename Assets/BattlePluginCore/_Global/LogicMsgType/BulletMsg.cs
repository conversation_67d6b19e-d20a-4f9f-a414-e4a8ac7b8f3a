using System.Collections;
using System.Collections.Generic;
using GameFramework.FMath;
using LogicFrameWork;
using UnityEngine;

public class BulletMsg : LogicMsg
{
    public FVector3 Position;
    public Fix64 Speed;
    public FVector3 Dir;
    public FQuaternion FQuaternion;
    public BulletType BulletType;
    public int AttackerID;
    public int HitterID;
    public bool isNewTarget = false;
    public bool Kill = false;   // 是否击杀对方

    public void  Set(FVector3 Position, Fix64 Speed, FVector3 Dir, BulletType BulletType, FQuaternion FQuaternion, int AttackerID, int HitterID, bool kill = false)
    {
        this.Position = Position;
        this.Speed = Speed;
        this.Dir = Dir;
        this.BulletType = BulletType;
        this.FQuaternion = FQuaternion;
        this.isNewTarget = true;
        this.AttackerID = AttackerID;
        this.HitterID = HitterID;
        this.Kill = kill;
    }
    
    public override int dataType()
    {
        return (int) BulletMoveMessege.StartFly;
    }
}

public class BullectDirMsg : LogicMsg
{
    public FVector3 Dir;
    
    public override int dataType()
    {
        return (int) BulletMoveMessege.Fly;
    }
}

public class SimpleBulletMsg : SimpleMsg
{
    public int HitterID;
}

public class BulletFVector3Msg : FVector3Msg
{
    public int HitterID;
}
