using System.Collections.Generic;

namespace LogicFrameWork
{
    public class LogicQueuePool
    {
        private int m_cacheSize = 10;
        private LogicQueue<LogicQueue<LogicMsg>> m_bufferPool;

        private int m_queueSize = 16;

        private int queueIndex = 0;

        private int GetQueueIndex()
        {
            return queueIndex++;
        }

        public LogicQueuePool(int size)
        {
            m_cacheSize = size;
            m_bufferPool = LogicQueueUtil.CreateNewLogicQueue<LogicQueue<LogicMsg>>(m_cacheSize);
            for (int i = 0; i < m_cacheSize; i++)
            {
                LogicQueue<LogicMsg> queue = LogicQueueUtil.CreateNewLogicQueue<LogicMsg>(m_queueSize);
                queue.index = GetQueueIndex();
                m_bufferPool.WriteData(queue);
            }
        }

        public LogicQueue<LogicMsg> GetLogicQueue()
        {
            LogicQueue<LogicMsg> result = null;

            if (m_bufferPool.size > 0)
            {
                result = m_bufferPool.ReadData();
            }
            else
            {
                result = LogicQueueUtil.CreateNewLogicQueue<LogicMsg>(m_queueSize);
                result.index = GetQueueIndex();
            }

            return result;
        }

        public void RecycleLogicQueue(LogicQueue<LogicMsg> buffer)
        {
            if (m_bufferPool.size < m_cacheSize - 1)
            {
                buffer.Reset();
                m_bufferPool.WriteData(buffer);
            }
        }
    }
}


