using System.Collections.Generic;
using System.Threading;

namespace LogicFrameWork
{
    public class LogicMsgTypePool
    {
        private int m_cacheSize = 8;
        private LogicQueue<LogicMsg> m_logicQueue;

        public LogicMsgTypePool()
        {
            m_logicQueue = LogicQueueUtil.CreateNewLogicQueue<LogicMsg>(m_cacheSize);
        }

        public LogicMsgTypePool(int size)
        {
            m_cacheSize = size;
            m_logicQueue = LogicQueueUtil.CreateNewLogicQueue<LogicMsg>(m_cacheSize);
        }

        public void Init<T>() where T: LogicMsg, new()
        {
            for (int i = 0; i < m_cacheSize; i++)
            {
                RecycleMsg(new T());
            }
        }

        public LogicMsg GetMsg<T>() where T: LogicMsg, new()
        {
            LogicMsg result = null;

            if (m_logicQueue.size > 0)
            {
                result = m_logicQueue.ReadData();
            }
            else
            {
                result = new T();
            }
            
            return result;
        }

        public void RecycleMsg(LogicMsg logicMsg)
        {
            if (m_logicQueue.size < m_cacheSize)
            {
                logicMsg.Reset();
                m_logicQueue.WriteData(logicMsg);
            }
        }
    }
}

