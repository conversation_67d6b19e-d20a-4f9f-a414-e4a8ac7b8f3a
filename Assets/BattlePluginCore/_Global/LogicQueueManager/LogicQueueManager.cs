using System.Collections.Generic;
using System.Threading;
using TKFrame;

namespace LogicFrameWork
{
    public class LogicQueueManager : ReleaseSingleton<LogicQueueManager>
    {
        private int m_cacheSize = 10;
        private Dictionary<int, LogicQueue<LogicMsg>> m_logicQueueDic = new Dictionary<int, LogicQueue<LogicMsg>>();
        private LogicQueuePool m_queuePool;
        
        private object m_lock = new object();

        private int m_logicQueueId = 0;

        public LogicQueueManager()
        {
            m_queuePool = new LogicQueuePool(m_cacheSize);
        }
        
        public LogicQueueManager(int size)
        {
            m_queuePool = new LogicQueuePool(size);
        }

        public LogicQueue<LogicMsg> NewQueue()
        {
            LogicQueue<LogicMsg> result = null;
            result = m_queuePool.GetLogicQueue();
            result.RefCountAdd();
            result.id = m_logicQueueId;

            lock (m_lock)
            {
                m_logicQueueDic.Add(result.id, result);
            }

            m_logicQueueId++;
            return result;
        }

        public LogicQueue<LogicMsg> GetQueue(int id)
        {
            LogicQueue<LogicMsg> result = null;
            lock (m_lock)
            {
                if (m_logicQueueDic.TryGetValue(id, out result))
                {
                    result.SetSendingStatus(1);
                    result.RefCountAdd();
                }
            }
            return result;
        }

        private List<LogicMsg> m_recycleMsg = new List<LogicMsg>();

        public void RecycleQueue(LogicQueue<LogicMsg> queue, bool isView)
        {
            lock (m_lock)
            {
                if (isView)
                    queue.SetSendingStatus(2);
                queue.RefCountDec();
                if (queue.refCount == 0)
                {
                    if (queue.size > 0)
                    {
                        m_recycleMsg.Clear();
                        queue.ReadData(m_recycleMsg, queue.size);
                        foreach (LogicMsg msg in m_recycleMsg)
                        {
                            LogicMsgPool.Instance.RecycleMsg(msg);
                        }
                    }
                
                    LogicQueue<LogicMsg> result = null;
                    if (m_logicQueueDic.TryGetValue(queue.id, out result))
                    {
                        m_logicQueueDic.Remove(queue.id);
                    }
                
                    m_queuePool.RecycleLogicQueue(queue);
                }
            }
        }

        public void RecycleAllQueue()
        {
            foreach (LogicQueue<LogicMsg> queue in m_logicQueueDic.Values)
            {
                if (queue.size > 0)
                {
                    m_recycleMsg.Clear();
                    queue.ReadData(m_recycleMsg, queue.size);
                    foreach (LogicMsg msg in m_recycleMsg)
                    {
                        LogicMsgPool.Instance.RecycleMsg(msg);
                    }
                }
                
                m_queuePool.RecycleLogicQueue(queue);
            }
            
            m_logicQueueDic.Clear();
        }
    }
}

