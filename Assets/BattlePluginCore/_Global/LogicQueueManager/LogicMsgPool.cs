using System;
using System.Collections.Generic;
using TKFrame;

namespace LogicFrameWork
{
    public class LogicMsgPool : ReleaseSingleton<LogicMsgPool>
    {
        private static byte msgType = 0;
        private Dictionary<Type, LogicMsgTypePool> m_allPool = new Dictionary<Type, LogicMsgTypePool>();
        
        public LogicMsgPool()
        {
            Init();
        }

        private void Init()
        {
            // 轮抽信息
            RegisterMsg<RoundSelectInitMsg>(1);
            RegisterMsg<RoundSelectAddHero>(4);
            RegisterMsg<RoundSelectReleasePlayers>(2);
            RegisterMsg<RoundSelectReleasePre3s>(8);
            RegisterMsg<RoundSelectChooseHero>(8);
            RegisterMsg<RoundSelectShowPlayer>(8);
            
            RegisterMsg<SimpleMsg>(16);
            RegisterMsg<SimpleBulletMsg>(16);
            RegisterMsg<BulletMsg>(20);
            RegisterMsg<BulletFVector3Msg>(20);
            
            RegisterMsg<SimpleCSoPlayAnimationMsg>(8);
            RegisterMsg<SimpleCSoPlayEffectMsg>(8);
            RegisterMsg<FVector3Msg>(20);
            RegisterMsg<FQuaternionMsg>(8);
            RegisterMsg<FPositionAndQuaternionMsg>(4);
            RegisterMsg<Fix64Msg>(20);
#if ACGGAME_CLIENT
            RegisterMsg<TriggerActionMsg>(8);
#endif
            
            RegisterMsg<PlayExpressionMsg>(8);
            RegisterMsg<InitPlayerMsg>(8);
            RegisterMsg<PlayActionMsg>(8);

            RegisterMsg<PlayerConfigMsg>(1);

            RegisterMsg<AttackMsg>(4);
            RegisterMsg<WillHitMsg>(4);
            RegisterMsg<HitMsg>(4);
        }

        public void RegisterMsg<T>(int size = 1) where T: LogicMsg, new()
        {
            Type type = typeof(T);
            LogicMsgTypePool result = null;
            if (!m_allPool.TryGetValue(type, out result))
            {
                result = new LogicMsgTypePool(size);
                result.Init<T>();
                m_allPool.Add(type, result);
            }
        }

        public T GetMsg<T>() where T: LogicMsg, new()
        {
            Type type = typeof(T);
            return (T)m_allPool[type].GetMsg<T>();
        }

        public void RecycleMsg<T>(T msg) where T : LogicMsg
        {
            Type type = msg.GetType();
            m_allPool[type].RecycleMsg(msg);
        }
    }
}


