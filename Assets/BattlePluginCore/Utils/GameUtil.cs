using System.Collections.Generic;
using ZGameClient;
using TKFrame;

using MiniGameClientProto;

using System.Text.RegularExpressions;
#if ACGGAME_CLIENT
using TKPlugins;
using UnityEngine;
using UnityEngine.UI;
#endif
using System.Text;
using System.Globalization;
using System;
using System.IO;
using System.Threading;
using ZGameChess;

using GameFramework.FMath;

namespace ZGame
{

    //-------------------------------
    //游戏相关的公共对象函数集，都是static
    //较为通用的功能函数
    //-------------------------------
    public class GameUtil
    {
#if ACGGAME_CLIENT
		 private static long DAYSECONDS = 3600 * 24;
        private static long HOURSECONDS = 3600;
        private static long MONTHSECONDS = 30 * 3600 * 24;

        //递归查找子节点
        public static Transform FindTransform(Transform root, string name)
        {
            Transform dt = root.Find(name);
            if (null != dt)
            {
                return dt;
            }
            else
            {
                foreach (Transform child in root)
                {
                    dt = FindTransform(child, name);
                    if (dt)
                    {
                        return dt;
                    }
                }
            }
            return null;
        }
        public static T FindComponentInParent<T>(Transform node, Transform maxParent) where T : MonoBehaviour
        {
            while (node != null)
            {
                T result = node.GetComponent<T>();
                if (result != null)
                    return result;

                node = node.parent;
                if (node == maxParent)
                    return null;
            }
            return null;
        }
        //递归设置物体的layer
        public static void SetLayerRecursive(GameObject obj, int layer)
        {
            obj.layer = layer;
            foreach (Transform child in obj.transform)
            {
                SetLayerRecursive(child.gameObject, layer);
            }
        }

        //递归设置物体的layer,当物体layer不为exceptLayer时才修改
        public static void SetLayerRecursiveExceptSpecific(GameObject obj, int layer, int exceptLayer)
        {
            obj.layer = obj.layer != exceptLayer ? layer : exceptLayer;
            foreach (Transform child in obj.transform)
            {
                SetLayerRecursiveExceptSpecific(child.gameObject, layer, exceptLayer);
            }
        }

        //删除所有的子节点
        public static void DestroyChildren(Transform root)
        {
            foreach (Transform child in root)
            {
                if (child.gameObject)
                    GameObject.Destroy(child.gameObject);
            }
        }

        static ConfigFile ms_dev_configFile;

        //开发在移动设备上，调试BUG时，可能需要到一些特殊的设置开关
        public static ConfigFile DevConfig
        {
            get
            {
                if (ms_dev_configFile == null)
                {
                    ms_dev_configFile = new ConfigFile();
                    ms_dev_configFile.Load(Application.persistentDataPath + "/zgame_dev.txt");
                }
                return ms_dev_configFile;
            }
        }


        private static long synchServiceTime = 0;//同步时的服务器时间撮
        private static long synchLocalTime = 0;  //同步时本地realtimeSinceStartup
        public static long timeOffset = 0;  //时间偏移，供测试使用
        /// <summary>
        /// 获取当前服务器时间(毫秒ms)时间戳
        /// </summary>
        public static long CurrServiceTime
        {
            get
            {
                long currServiceTime = synchServiceTime + (long)(Time.realtimeSinceStartup * 1000L - synchLocalTime) + timeOffset * 1000;
                return currServiceTime;
            }
            set
            {
                synchServiceTime = value;
                synchLocalTime = (long)(Time.realtimeSinceStartup * 1000L);
            }
        }

        /// <summary>
        /// 获取当前服务器时间(秒s)时间戳
        /// </summary>
        public static int ServerTimeSec
        {
            get { return (int)(CurrServiceTime / 1000L); }
        }

#if LOGIC_THREAD
        public static float RealtimeSinceStartupForLogicThread
        {
            get;
            set;
        }
#endif

        /// <summary>
        /// 判断时间范围
        /// <returns></returns>
        public static bool IsBetweenTime(long time, long start_time, long end_time)
        {
            return time >= start_time && time <= end_time;
        }


        /// <summary>
        /// 缓存音频路径对应文件名
        /// </summary>
        private static Dictionary<string, string> wwwisePathToAssetName = new Dictionary<string, string>();
        private static Dictionary<string, string> wwwisePathToAssetPath = new Dictionary<string, string>();
        /// <summary>
        /// 判定assetbundle路径的合法性
        /// </summary>
        public static bool SplitPath(string path, out string assetPath, out string assetName)
        {
            assetPath = string.Empty;
            assetName = string.Empty;

            if (string.IsNullOrEmpty(path))
            {
                return false;
            }

            if (!wwwisePathToAssetName.TryGetValue(path, out assetName))
            {
                FastStringSplit split = path.BeginSplit('|');
                if (split.Length == 2)
                {
                    assetPath = split[0];
                    assetName = split[1];
                    wwwisePathToAssetName[path] = assetName;
                    wwwisePathToAssetPath[path] = assetPath;
                    split.EndSplit();
                }
                else
                {
                    split.EndSplit();
                    return false;
                }
            }
            else
            {//AssetName存在，asstPasth也存在
                assetPath = wwwisePathToAssetPath[path];
            }
            return true;
        }

        //恢复UI原来的颜色
        public static void ResetUIFromGray(Graphic g, bool gray)
        {
            if (g == null)
                return;
            g.material = null;
        }
        

        /// <summary>
        /// 包括各类版本信息在内的字符串，用于设置界面展示与日志输出
        /// </summary>
        public static string GetVersion()
        {
            string str = VersionInfo.BundleVersion;
            // if (VersionInfo.VersionMode == "debug")
            {
                str += VersionInfo.VersionMode;
            }
            return str + "(" + VersionInfo.PackageVersion.ToString() + "-" +
                (TKAssetDatabase.Instance.ResourceVersion % 1000).ToString("#000") +
                "-" + VersionInfo.SodaBuildNumber.ToString() + "-r" + VersionInfo.SVNRevNumber + ")";
        }

        /// <summary>
        /// IOS平台过滤掉输入法自己加的特殊字符
        /// </summary>
        static byte[] ms_unicode_buff = null;
        static byte[] ms_unicode_buff2 = null;
        public static string EscapeInvalidChar(string text)
        {
#if UNITY_IOS
            int need_count = Encoding.UTF32.GetByteCount(text);
            if (ms_unicode_buff.Length < need_count)
            {
                ms_unicode_buff = new byte[need_count];
                ms_unicode_buff2 = new byte[need_count];
            }

            need_count = Encoding.UTF32.GetBytes(text, 0, text.Length, ms_unicode_buff, 0);

            int result_count = 0;
            for (int i = 0; i < need_count; )
            {
                int code = ms_unicode_buff[i + 0];
                code |= ms_unicode_buff[i + 1] << 8;
                code |= ms_unicode_buff[i + 2] << 16;
                code |= ms_unicode_buff[i + 3] << 24;

                if (code != 0x2006)
                {
                    ms_unicode_buff2[result_count++] = ms_unicode_buff[i + 0];
                    ms_unicode_buff2[result_count++] = ms_unicode_buff[i + 1];
                    ms_unicode_buff2[result_count++] = ms_unicode_buff[i + 2];
                    ms_unicode_buff2[result_count++] = ms_unicode_buff[i + 3];
                }

                i += 4;
            }

            if (result_count == need_count)
            {
                //如果没有非法字符，则直接返回，不用转换
                return text;
            }

            string ret = Encoding.UTF32.GetString(ms_unicode_buff2, 0, result_count);
            return ret;
#else
            return text;
#endif
        }

        /// emoji表情过滤
        /// </summary>
        /// <param name="txt"></param>
        /// <returns></returns>
        public static string RemoveEmoji(string txt)
        {
            byte[] src = System.Text.Encoding.Unicode.GetBytes(txt);
            string result = System.Text.Encoding.Unicode.GetString(src);
            string str = Regex.Replace(result, @"\p{Cs}", "");
            result = str;
            str = Regex.Replace(result, @"([\uD800-\uDBFF][\uDC00-\uDFFF])+", "");
            //byte[] mystc = System.Text.Encoding.Unicode.GetBytes(str);
            //string str = Regex.Replace(result, "[\ud800 - \udfff]", "");
            return str;
        }

        public static void OpenURL(string url, bool isLandscape = false, bool isUseURLEcode = true, bool isFullScreen = false, string ExtraJson = "")
        {
#if UNITY_EDITOR
            Application.OpenURL(url);
#else
            /*if (isLandscape)
            {
                MSDKWebView.OpenUrl (url, MSDKWebViewOrientation.Landscape, isUseURLEcode:isUseURLEcode, isFullScreen:isFullScreen, extraJson:ExtraJson);
            }
            else
            {
                MSDKWebView.OpenUrl (url, MSDKWebViewOrientation.Auto, isUseURLEcode:isUseURLEcode, isFullScreen:isFullScreen, extraJson:ExtraJson);
            }*/
#endif

        }

        /// <summary>
        /// 设置输入框为过滤输入框的Emoji表情
        /// </summary>
        public static void SetMaskRemoji(InputField input)
        {
            if (input is InputFieldEx == false)
                input.onValidateInput += InputFieldEx.ValidateRemojiInput;
        }

        //public static char ValidateRemojiInput(string text, int charIndex, char addedChar)
        //{
        //    if (GetStringLen(text) + GetCharLen(addedChar) > 6)
        //        return '\0';

        //    return addedChar;
        //}

        public static void SetLimitedInput(InputField input, int max_english_add = 0)
        {
            int maxLen = input.characterLimit * 2;
            if (maxLen == 0)
                return;

            input.characterLimit = 0;
            input.onValidateInput += (text, charIndex, addedChar) =>
            {
                if (max_english_add > 0)
                {
                    if (text.Length > maxLen / 2 + max_english_add)
                        return '\0';
                }

                if (GetStringLen(text) + GetCharLen(addedChar) > maxLen)
                    return '\0';
                return addedChar;
            };
        }
#endif
        //返回字符长度（汉字两个，英文字母数字算1个）
        static int GetStringLen(string text)
        {
            int len = 0;
            foreach (var ch in text)
            {
                len += GetCharLen(ch);
            }
            return len;
        }

        static int GetCharLen(char ch)
        {
            if (ch <= 127)
                return 1;
            else
                return 2;
        }

#if ACGGAME_CLIENT
#if !OUTSOURCE
        //判断本地时间是否在活动时间范围内
        //-------活动时间形式： 15点——17点   相对当天的时间
        public static bool IsBetweenActivityTimes(int beginTime, int endTime)
        {
            bool isBetween = false;
            DateTime dateTime = TimeUtil.GetDataTimeByTimestamp(GameUtil.ServerTimeSec);
            int beginHour = beginTime / 60 / 60;
            //int endHour = endTime / 60 / 60;
            if (beginHour <= dateTime.Hour && endTime > dateTime.Hour)
            {
                isBetween = true;
            }

            return isBetween;
        }
#endif

        //根据Text组件的宽度，对输入的字符串进行截断，并在其后面加上"..." ，然后给text组件赋值
        public static void CutStringByTextWidth(string des, Text text, string appendString = "")
        {
            if (text != null)
            {
                float textWidht = text.gameObject.GetComponent<RectTransform>().rect.width;
                text.text = des;
                int i = des.Length - 1;
                while (text.preferredWidth > textWidht)
                {
                    if (i > 0)
                    {
                        string subDes = des.Substring(0, i);
                        text.text = subDes + "..." + appendString;
                    }
                    else
                    {
                        break;
                    }
                    i--;
                }
            }
        }

        public static void SetScaleToZero(GameObject obj, bool zero, bool autoEnable = false)
        {
            
            if (obj == null)
                return;
            if (zero)
            {
                obj.transform.localScale = Vector3.zero;
            }
            else
            {
                if (autoEnable && !obj.activeSelf)
                {
                    obj.SetActive(true);
                }
                obj.transform.localScale = Vector3.one;
            }
        }



#endif

        public static int PosToNum(int x, int y, AreaType areaType)
        {
            int num = -1;
            if (areaType == AreaType.None)
            {
                num = 0;
            }
            else if (areaType == AreaType.Battle)
            {
                num = x + y * ChessConst.FORMATION_COL_COUNT;
            }
            else if (areaType == AreaType.Wait)
            {
                num = x + y * ChessConst.WAIT_COL_COUNT;
            }
            else if (areaType == AreaType.ShareWait)
            {
                num = x + y * ChessConst.SHARE_WAIT_COL_COUNT;
            }
            return num;
        }

        public static void NumToPos(int num, AreaType areaType, out int x, out int y)
        {
            x = -1;
            y = -1;
            if (areaType == AreaType.Battle)
            {
                x = num % ChessConst.FORMATION_COL_COUNT;
                y = num / ChessConst.FORMATION_COL_COUNT;
            }
            else if (areaType == AreaType.Wait || areaType == AreaType.EnemyWait)
            {
                x = num % ChessConst.WAIT_COL_COUNT;
                y = num / ChessConst.WAIT_COL_COUNT;
            }
            else if (areaType == AreaType.ShareWait)
            {
                x = num % ChessConst.SHARE_WAIT_COL_COUNT;
                y = num / ChessConst.SHARE_WAIT_COL_COUNT;
            }
        }

        public static int PosToNetWorkNum(int x, int y, AreaType areaType)
        {
            int num = 0;
            if (areaType == AreaType.None)
            {
                num = 0;
            }
            else if (areaType == AreaType.Battle)
            {
                num = x + y * ChessConst.FORMATION_COL_COUNT + 1;
            }
            else if (areaType == AreaType.Wait)
            {
                num = x + y * ChessConst.WAIT_COL_COUNT + ChessConst.FORMATION_GRID_COUNT + 1;
            }
            else if (areaType == AreaType.ShareWait)
            {
                num = x + y * ChessConst.SHARE_WAIT_COL_COUNT + ChessConst.FORMATION_GRID_COUNT + ChessConst.WAIT_GRID_COUNT + 1;
            }
            return num;
        }

        public static void NetWorkNumToBattlePos(int num, out int x, out int y)
        {
            x = -1;
            y = -1;

        }

        public static int NetWorkNumToPos(int num)
        {
            int pos = num;
            return pos;
        }

        #region FQuaternion char[] 转换

        public static char[] FQuaternionToCharArray(FQuaternion value)
        {
            char[] tempchar = new char[16];

            Fix64ToCharArray(value.x, tempchar, 0);
            Fix64ToCharArray(value.y, tempchar, 4);
            Fix64ToCharArray(value.z, tempchar, 8);
            Fix64ToCharArray(value.w, tempchar, 12);

            return tempchar;
        }

        public static FQuaternion CharArrayToFQuaternion(char[] array, int start = 0)
        {
            Fix64 x = CharArrayToFix64(array, start + 0);
            Fix64 y = CharArrayToFix64(array, start + 4);
            Fix64 z = CharArrayToFix64(array, start + 8);
            Fix64 w = CharArrayToFix64(array, start + 12);
            return new FQuaternion(x, y, z, w);
        }
        #endregion

        #region FVector3 char[] 转换

        public static char[] FVector3ToCharArray(FVector3 value)
        {
            char[] tempchar = new char[12];

            Fix64ToCharArray(value.x, tempchar, 0);
            Fix64ToCharArray(value.y, tempchar, 4);
            Fix64ToCharArray(value.z, tempchar, 8);

            return tempchar;
        }

        public static FVector3 CharArrayToFVector3(char[] array, int start = 0)
        {
            Fix64 x = CharArrayToFix64(array, start + 0);
            Fix64 y = CharArrayToFix64(array, start + 4);
            Fix64 z = CharArrayToFix64(array, start + 8);
            return new FVector3(x, y, z);
        }
        
        #endregion
        
        #region FVector2 char[] 转换

        public static char[] FVector2ToCharArray(FVector2 value)
        {
            char[] tempchar = new char[8];

            Fix64ToCharArray(value.x, tempchar, 0);
            Fix64ToCharArray(value.y, tempchar, 4);

            return tempchar;
        }

        public static FVector2 CharArrayToFVector2(char[] array, int start = 0)
        {
            Fix64 x = CharArrayToFix64(array, start + 0);
            Fix64 y = CharArrayToFix64(array, start + 4);
            return new FVector2(x, y);
        }
        
        #endregion
        
        #region Fix64 char[] 转换

        private static unsafe void Fix64ToCharArray(Fix64 value, char[] array, int start = 0)
        {
            fixed (char* numPtr = array)
            {
                char* newPtr = numPtr + start;
                *(double*) newPtr = value.rawValue;
            }
        }

        public static char[] Fix64ToCharArray(Fix64 value)
        {
            char[] tempchar = new char[4];
            Fix64ToCharArray(value, tempchar);
            return tempchar;
        }

        public static Fix64 CharArrayToFix64(char[] array, int start = 0)
        {
            byte[] bytes = new byte[8];
            for (int i = 0; i < 4; i++)
            {
                char c = array[i + start];
                bytes[2 * i] = (byte)(c & 0x00FF);
                bytes[2 * i + 1] = (byte)((c & 0xFF00) >> 8);
            }
            long value = BitConverter.ToInt64(bytes, 0);
            return Fix64.FromRawValue(value);
        }
        
        #endregion

        #region FVector2 byte[] 转换

        public static byte[] FVector2ToMinCharArray(FVector2 value)
        {
            byte[] tempchar = new byte[4];
            Fix64ToMinChar(value.x, tempchar, 0);
            Fix64ToMinChar(value.y, tempchar, 2);
            return tempchar;
        }

        public static FVector2 MinCharArrayToFVector2(byte[] array, int start = 0)
        {
            Fix64 x = MinCharToFix64(array, start);
            Fix64 y = MinCharToFix64(array, start + 2);
            return new FVector2(x, y);
        }
        
        #endregion
        
        #region Fix64 byte[] 转换
        
        private static void Fix64ToMinChar(Fix64 value, byte[] array, int start = 0)
        {
            int x = (value * 100).Ceiling();
            if (x > short.MaxValue)
                x = short.MaxValue;
            if (x < short.MinValue)
                x = short.MinValue;

            short x16 = (short)x;
            
            array[start] = (byte) x16;
            array[start + 1] = (byte) (x16 >> 8);
        }

        private static byte[] Fix64ToMinChar(Fix64 value)
        {
            int x = (value * 100).Ceiling();
            if (x > short.MaxValue)
                x = short.MaxValue;
            if (x < short.MinValue)
                x = short.MinValue;

            short x16 = (short)x;

            byte[] moveData = new byte[2];
            moveData[0] = (byte) x16;
            moveData[1] = (byte) (x16 >> 8);
            
            return moveData;
        }

        private static Fix64 MinCharToFix64(byte[] value, int start)
        {
            short x16 = BitConverter.ToInt16(value, start);
            int x = (int)x16;
            Fix64 tmp = new Fix64(x);
            return tmp/Fix64._100;
        }
        
        #endregion

        #region degree byte[] 转换

        public static void DegreeToByteArray(float degree, byte[] data)
        {
            degree *= 100;
            ushort sendDegree = (ushort)degree;
            
            data[0] = (byte)sendDegree;
            data[1] = (byte)(sendDegree >> 8);
        }

        public static void ByteArrayToDegree(byte[] data, ref Fix64 degree)
        {
            ushort sendDegree = (ushort) ((data[1] << 8) | data[0]);
            Fix64.SetIntValue(ref degree, sendDegree);
            degree /= Fix64._100;
        }

        #endregion
        
        public static int GetValueByParaType(int valueOriginal, int paramNum, TDRConfig.PARA_TYPE paraType)
        {
            int changeValue = paramNum;

            switch (paraType)
            {
                case TDRConfig.PARA_TYPE.PARA_TYPE_PER100:
                    changeValue = valueOriginal * paramNum / 100;
                    break;
                case TDRConfig.PARA_TYPE.PARA_TYPE_PER1000:
                    changeValue = valueOriginal * paramNum / 1000;
                    break;
                case TDRConfig.PARA_TYPE.PARA_TYPE_PER10000:
                    changeValue = (int)((long)valueOriginal * paramNum / 10000);
                    break;
            }
            return changeValue;
        }

#if ACGGAME_CLIENT
		  private static Vector3[] RectOverLapCornerArr = new Vector3[4];
        public static bool IsRectOverlap(RectTransform rect1, RectTransform rect2)
        {
            Vector2 rect1Pos = RectTransformUtility.WorldToScreenPoint(Services.GetService<TKCameraManager>().UICamera, rect1.position);
            rect1.GetWorldCorners(RectOverLapCornerArr);
            Vector2 rect1HalfSize = rect1Pos - RectTransformUtility.WorldToScreenPoint(Services.GetService<TKCameraManager>().UICamera, RectOverLapCornerArr[0]);

            Vector2 rect2Pos = RectTransformUtility.WorldToScreenPoint(Services.GetService<TKCameraManager>().UICamera, rect2.position);
            rect2.GetWorldCorners(RectOverLapCornerArr);
            Vector2 rect2HalfSize = rect2Pos - RectTransformUtility.WorldToScreenPoint(Services.GetService<TKCameraManager>().UICamera, RectOverLapCornerArr[0]);


            float rect1MinX = rect1Pos.x - rect1HalfSize.x;
            float rect1MaxX = rect1Pos.x + rect1HalfSize.x;
            float rect1MinY = rect1Pos.y - rect1HalfSize.y;
            float rect1MaxY = rect1Pos.y + rect1HalfSize.y;

            float rect2MinX = rect2Pos.x - rect2HalfSize.x;
            float rect2MaxX = rect2Pos.x + rect2HalfSize.x;
            float rect2MinY = rect2Pos.y - rect2HalfSize.y;
            float rect2MaxY = rect2Pos.y + rect2HalfSize.y;

            bool isOverlap = rect1MaxX > rect2MinX && rect1MinX < rect2MaxX && rect1MaxY > rect2MinY && rect1MinY < rect2MaxY;

            //UnityEngine.Debug.LogFormat("neison rect1Pos:{0}, rect1MinX:{1}, rect1MinX:{2}, rect1MinY:{3}, rect1MaxY:{4}|rect2Pos:{5}, rect2MinX:{6}, rect2MaxX:{7}, rect2MinY:{8}, rect2MaxY:{9}|{10}",
            //  rect1Pos, rect1MinX, rect1MaxX, rect1MinY, rect1MaxY, rect2Pos, rect2MinX, rect2MaxX, rect2MinY, rect2MaxY, isOverlap);

            return isOverlap;
        }

        public static bool GetHierarchyPath(Transform node, Transform maxParent, out string path)
        {
            TKStack<string> stack = new TKStack<string>();
            while (node != null && node != maxParent)
            {
                stack.Push(node.name);
                node = node.parent;
            }
            path = string.Join("/", stack);
            return node == maxParent;
        }

        public static string GetHierarchyPath(Transform node, Transform maxParent)
        {
            Stack<string> stack = new Stack<string>();
            while (node != null && node != maxParent)
            {
                stack.Push(node.name);
                node = node.parent;
            }
            return string.Join("/", stack);
        }

        public static string GetHierarchyPath(Transform transform)
        {
            StringBuilder sb = new StringBuilder();
            Stack<string> paths = new Stack<string>();
            Transform t = transform;
            while (t != null)
            {
                paths.Push(t.name);
                t = t.parent;
            }
            foreach (var item in paths)
            {
                sb.Append(item).Append("/");
            }
            return sb.ToString();
        }


        public static Vector2 LimitScreenPos(RectTransform rect, Vector2 pos)
        {
            var maxw = Screen.width;
            var maxh = Screen.height;
            float x = pos.x;
            float y = pos.y;
            if (pos.x + rect.rect.width > maxw)
                x = maxw - rect.rect.width - 10;
            if (maxh < rect.rect.height - pos.y)
                y = rect.rect.height - maxh + 10;

            return new Vector2(x, y);
        }
        public static Vector2 LimitScreenPos(float width, float height, Vector2 pos)
        {
            var maxw = Screen.width;
            var maxh = Screen.height;
            float x = pos.x;
            float y = pos.y;
            if (pos.x + width > (maxw/2f))
                x = maxw/2f - width - 10;
            if ((maxh/ 2f) < height - pos.y)
                y = height - maxh/2 + 10;

            return new Vector2(x, y);
        }
                
#endif
        public static List<int> GetSplitList(string plan)
        {
            List<int> temp = new List<int>();
            FastStringSplit plans = plan.BeginSplit('|');
            for (int i = 0; i < plans.Length; i++)
            {
                if (plans.TryParse(i, out int k))
                {
                    temp.Add(k);
                }
            }
            plans.EndSplit();
            return temp;
        }
    }
}
