Shader "InGame/Scene/WaterColor" 
{
    Properties 
    {    
        _Brightness("Brightness", Range(1, 2.5)) = 1
        _MainTex ("Base (RGB)", 2D) = "white" {}
        
        _NoiseTex ("NoiseTex", 2D) = "white" {}
        _NoiseScale ("NoiseScale", Range(0,20)) = 0
        _NoiseStrength ("NoiseStrength", Range(0, 1)) = 0.6

        [HideInInspector]_SceneVignetteColor("SceneVignetteColor",Color) = (0,0,0,1.0)
        [HideInInspector]_SceneVignetteRadius("SceneVignetteRadius", float) = 1
        [HideInInspector] _SceneVignettePos("SceneVignettePos", vector) = (0.5,0.5,0,0)
        [HideInInspector]_SceneVignette("SceneVignettePos", float) = 0

        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
    }

    SubShader 
    {
        Tags { "RenderType"="Opaque" }

        Pass 
        {
			// 遮挡描边的场景, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

			ColorMask RGB
            CGPROGRAM
                #pragma vertex vert
                #pragma fragment frag

                #include "UnityCG.cginc"
                #include "AutoLight.cginc"
                #define _IN_GAME_SCENE 1
                #include "Assets/Shaders/InGame/Common/InGame.cginc"
                struct appdata_t {
                    float4 vertex : POSITION;
                    float2 uv : TEXCOORD0;
                    float3 normal : NORMAL;

                    float4 color : COLOR;
                };

                struct v2f {
                    float4 vertex : SV_POSITION;
                    float2 uv : TEXCOORD0;

                    half3 worldNormal : TEXCOORD1;
                    float3 worldPos : TEXCOORD2;
                    float3 viewDir : TEXCOORD3;
                    float4 screenPos: TEXCOORD4;

                    float4 vertColor : COLOR0;
                };

                sampler2D _MainTex;
                sampler2D _NoiseTex;

                fixed _Brightness;
                half _NoiseScale;
                half _NoiseStrength;

                v2f vert (appdata_t v)
                {
                    v2f o;
                    UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                    o.vertex = UnityObjectToClipPos(v.vertex);
                    o.uv = v.uv;
                    o.screenPos = ComputeScreenPos(o.vertex);

                    o.vertColor = v.color;

                    o.worldNormal = UnityObjectToWorldNormal(v.normal);
                    o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;

                    o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos );

                    return o;
                }

                fixed4 frag (v2f i) : SV_Target
                {
                    float2 ScreenUV = i.screenPos.xy/i.screenPos.w;
                    half4 col = tex2D(_MainTex, i.uv);
                    half4 finalColor = col;
                    finalColor *= _Brightness;
                    half4 Noise = tex2D(_NoiseTex, ScreenUV*_NoiseScale);
                    Noise = Noise*1;                   
                    finalColor.a = 1;
                    //finalColor = lerp(finalColor*_NoiseStrength,finalColor,Noise.r);
                    finalColor -= (1-Noise)*(1-_NoiseStrength);
                    APPLY_EFFECT_DARK_SCENE(finalColor);
                    return finalColor;
                }
            ENDCG
        }

    }

}
