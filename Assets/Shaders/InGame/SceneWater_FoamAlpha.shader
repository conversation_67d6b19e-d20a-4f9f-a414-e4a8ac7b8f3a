Shader "InGame/Scene/Water_FoamAlpha(CullOff)"
{
    Properties
    {
        _MainColor ("Main Color", COLOR)  = ( 0, 0, 0, 1)
        _MainTex ("Texture", 2D) = "white" {}

        [Space(5)]
        [Header(Water)]
        [Space(5)]
        _TargetWaterHeight("Target Water Height", Float) = 0
        _WaterDeep("Water Deep", Range(0,2)) = 1
        _UnderColor ("Water Under Color", COLOR)  = ( 0, 0, 0, 1)
        _FoamWidth("Foam Width", Range(0,10)) = 5
        _FoamColor ("Foam Color", COLOR)  = ( 0, 0, 0, 0)

        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }

        Pass
        {
            Stencil
            {
                Ref 2
                Comp Equal
            }
            
            //	ColorMask RGB
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite off
            ZTest Greater
            Cull Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _IN_GAME_SCENE 1
            #include "Common/InGame.cginc"

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;

                float4 worldPos : TEXCOORD1;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            half4 _MainColor, _UnderColor, _FoamColor;
            fixed _FoamWidth;
            fixed _WaterDeep;
            half _TargetWaterHeight;

            v2f vert (appdata v)
            {
                v2f o;

                o.vertex = UnityObjectToClipPos(v.vertex);

                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                o.worldPos = mul(unity_ObjectToWorld, v.vertex);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv) * _MainColor;

                fixed targetPosY = _TargetWaterHeight;

                fixed waterDeep = (targetPosY - i.worldPos.y) * _WaterDeep;

                col.rgb = lerp(col.rgb,col.rgb * _UnderColor.rgb, waterDeep);

                col.a = saturate(col.a - waterDeep);

                half4 foamLine = (1 - saturate(_FoamWidth * waterDeep )) * _FoamColor ;

                col += foamLine;

                APPLY_EFFECT_DARK_SCENE(col);

                return col ;
            }
            ENDCG
        }

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            
            Cull Off
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            #define _IN_GAME_SCENE 1
            #include "Common/InGame.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            half4 _MainColor;

            v2f vert (appdata v)
            {
                v2f o;

                o.vertex = UnityObjectToClipPos(v.vertex);

                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                half4 col = tex2D(_MainTex, i.uv) * _MainColor;

                APPLY_EFFECT_DARK_SCENE(col);

                return col ;
            }
            ENDCG
        }

        
    }
}
