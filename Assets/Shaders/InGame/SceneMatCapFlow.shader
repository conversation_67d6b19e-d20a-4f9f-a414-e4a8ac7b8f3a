Shader "InGame/Scene/MatcapFlowOpaque"
{
	Properties
	{
		[Header(Matcap)]
		[Space(5)]
		_Brightness("Brightness", Range(1,2)) = 1
		_Color("MainColor", Color) = (1, 1, 1, 1)
		[NoScaleOffset]_MainTex("Main Matcap Texture", 2D) = "black" {}

		[Space(5)]
		[Header(Rim Lighting)]
		[Space(5)]
		_EdgeColor("RimColor", Color) = (1, 1, 1, 1)
		_RimSmoothMin("MinSmooth",Range(0,1)) = 0
		_RimSmoothMax("MaxSmooth",Range(0,1)) = 1
		
		[Space(5)]
		[Header(Pattern)]
		[Space(5)]
		_patternCol01("Color01", Color) = (1,1,1,1)
		_pattern01("Pattern01",2D) = "black"{} 
		[Space(5)]
		_patternCol02("Color02", Color) = (1,1,1,1)
		_pattern02("Pattern02",2D) = "black"{} 
		[Space(5)]
		_patternCol03("Color03", Color) = (1,1,1,1)
		_pattern03("Pattern03",2D) = "black"{}
		//【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
		[HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" "Queue"="Geometry" }

		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag 

			#define _IN_GAME_SCENE 1
			
			#include "Common/InGame.cginc"

			#include "UnityCG.cginc"
			
			sampler2D		_MainTex;
			uniform float4	_Color;
			uniform float4	_EdgeColor;
			fixed _Brightness, _RimSmoothMin, _RimSmoothMax;
			
			half4 _patternCol01, _patternCol02, _patternCol03;
			sampler2D _pattern01, _pattern02, _pattern03;
			float4 _pattern01_ST, _pattern02_ST, _pattern03_ST;

			struct a2v
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL; 
				float4 texcoord : TEXCOORD0;
			};



			struct v2f
			{
				float4 pos : SV_POSITION;
				float3 normal : NORMAL; 
				float3 viewDir : TEXCOORD1;
				float3 worldPos : TEXCOORD2; 
				float2 uv : TEXCOORD4;
				
			};

			v2f vert(a2v v)
			{
				v2f o; 
				UNITY_INITIALIZE_OUTPUT(v2f, o); 
				o.pos = UnityObjectToClipPos(v.vertex);
				float4 normal4 = float4(v.normal, 0.0);
				o.normal = normalize(mul(normal4, unity_WorldToObject).xyz);
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos );

				o.uv = v.texcoord; 
				return o;
			}

			float4 frag(v2f i) : COLOR 
			{ 
				half3 viewNormal = mul(UNITY_MATRIX_V,i.normal);
				half3 viewPos = UnityWorldToViewPos(i.worldPos);
				half3 r = normalize(reflect(viewPos, viewNormal));
				half m = 2 * sqrt(r.x * r.x + r.y * r.y + (r.z + 1) * (r.z + 1));
				half2 matcapUV = r.xy/m + 0.5 ;
				
				
				half4 texColor = tex2D(_MainTex, matcapUV ) * _Brightness;

				half edgeFactor = abs(dot(i.viewDir, i.normal));
				edgeFactor = smoothstep(_RimSmoothMin, _RimSmoothMax,edgeFactor); 
				half oneMinusEdge = 1.0 - edgeFactor; 
				float3 rgb = (_Color.rgb * edgeFactor) + (_EdgeColor * oneMinusEdge);
				rgb = rgb * texColor.rgb;

				float2 uv = i.uv; 
				fixed4 col01 =tex2D( _pattern01, (uv * _pattern01_ST.xy + frac(_Time.y *  _pattern01_ST.zw))  )  * _patternCol01; 
				fixed4 col02 = tex2D( _pattern02, (uv * _pattern02_ST.xy + frac(_Time.y *  _pattern02_ST.zw))  )  * _patternCol02; 
				fixed4 col03 = tex2D( _pattern03, (uv * _pattern03_ST.xy + frac(_Time.y  * _pattern03_ST.zw))  ) * _patternCol03; 
				col01 = col01 + col02 + col03; 
				rgb += col01.rgb;

				float4 col = float4(rgb, 1); 
				APPLY_EFFECT_DARK_SCENE(col);

				return col;
			}

			ENDCG
		} 
		
	}

}