Shader "InGame/Scene/Water/Water_Dissolve"
{
    Properties
    {
        _WaterColor("MainRGB", 2D)= "white" {}
        _Foam("WaterDepth(R),FoamMask(G),DetailDistortTex(B)", 2D) = "white" {}
        [HDR]_DeepColor("DeepColor", Color) = (0,0,0,0)
        [HDR]_ShalowColor("ShalowColor", Color) = (1,1,1,0)

        [Space(10)]
        _WaterNormal("WaveNormal", 2D) = "bump" {}
        _NormalScale("NormalIntensity", Range(0,1)) = 0.3
        _WaveParams("WaveSpeed:Panner1(xy),Panner2(zw)", vector) = (-0.04,-0.02,-0.02,-0.04)
        [Space(10)]
        _DetailColor("DetailColor", Color) = (1,1,1,1)
        _WaterWave("DetailDistort",Range(0,0.1)) = 0.02 
        [Space(10)]
        _WaterSpecular("SpecularIntensity", Range(0,1)) = 0.8
        _WaterSmoothness("SpecularFactor", Range(0,10)) = 8
        _LightColor("SpecularColor", color) = (1,1,1,1)
        _LightDir("SpecularDirection", vector) = (0, 0, 0, 0)
        _RimPower("Fresnel", Range(0,20)) = 8

        [Space(10)] 
        _FoamColor("FoamColor", Color) = (1,1,1,1)
        _FoamDepth("FoamRange", Range(-2,20)) = 0.5
        _FoamFactor("FoamFactor",Range(0,10)) = 0.2
        _FoamOffset("Foam:Speed(xy),Intensity(z)Distort(w)", vector) = (-0.01,0.01, 2, 0.01) 
        
        //Dissolve 
        _DissolveTex("Dissolve Texture", 2D) = "white" {}
        _Dissolve("Dissolve Value", Range(-1,1)) = 0
        
        [HDR]_CeilColorA("Edge First Color", Color) = (1,1,1,1)
        [HDR]_CeilColorB("Edge Second Color", Color) = (1,1,1,1)
        _Mask("Edge Range",Range(0.0,1.0)) = 0.2
        _Cutoff("Sharpen Toggle",Range(0.0,1.0)) = 0.0 
        [Header(Stencil)]
        [IntRange] _Stencil ("Stencil ID", Range(0,255)) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)] _StencilComp ("Stencil Comparison", Float) = 8
        [Enum(UnityEngine.Rendering.StencilOp)] _StencilPass ("Stencil Pass", Float) = 0
        [Enum(UnityEngine.Rendering.StencilOp)] _StencilFail ("Stencil Fail", Float) = 0
        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0 
    }

    SubShader
    {
       Tags { "RenderType"="Opaque" "Queue"="Geometry" } 
        
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilPass]
            Fail [_StencilFail]
        } 
        Pass
        {
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag  
            #define _IN_GAME_SCENE 1
            
            #include "Common/InGame.cginc"
            #include "UnityCG.cginc" 
            struct appdata
            {
                float4 vertex : POSITION; 
                float4 uv_Tex : TEXCOORD0; 
                float4 uv : TEXCOORD1;  
            }; 
            struct v2f
            {
                float4 vertex : SV_POSITION; 
                float4 uv_Tex : TEXCOORD0; 
                float4 uv : TEXCOORD1;  
            };
            sampler2D _WaterColor,_Foam,_WaterNormal; 
            float4 _Foam_ST,_WaterNormal_ST;  
            half4 _WaveParams; 
            half _WaterWave;  
            //溶解
            sampler2D _DissolveTex;
            float4 _DissolveTex_ST; 
            half _Mask,_Dissolve;
            fixed4 _CeilColorA,_CeilColorB;
            fixed _Cutoff;
            

            v2f vert(appdata v)
            {
                v2f o; 
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv_Tex.xy = TRANSFORM_TEX(v.uv,_Foam);
                o.uv_Tex.zw = TRANSFORM_TEX(v.uv, _WaterNormal); 
                UNITY_TRANSFER_FOG(o,o.vertex);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {  
                //Dissolve
                half2 uv = i.uv_Tex.xy; 
                fixed ClipTex = tex2D(_DissolveTex, uv.xy).r;
                fixed ClipValue = ClipTex + ( _Dissolve * (-1.2)) + 0.1; 
                float degree = saturate(ClipValue / _Mask);
                float4 edgeColor = lerp(_CeilColorA, _CeilColorB, degree); 
                degree = _Cutoff > 0.5 ? degree == 1 : degree;
                //waterWave 
                half2 panner1 = (_Time.y * _WaveParams.xy + i.uv_Tex.zw);
                half2 panner2 = (_Time.y * _WaveParams.zw + i.uv_Tex.zw);
                half3 worldNormal = UnpackNormal(tex2D(_WaterNormal, panner1))+ UnpackNormal(tex2D(_WaterNormal, panner2)); 
                half2 detailpanner = (i.uv_Tex.xy / _Foam_ST.xy + worldNormal.xy * _WaterWave);  
                half4 detail01 = tex2D(_WaterColor,i.uv_Tex.xy);
                half4 detail02 = tex2D(_WaterColor,detailpanner);  
                float4 col =float4(detail02.rgb,1);
                col =  lerp(  detail01 ,detail02,degree); 
                APPLY_EFFECT_DARK_SCENE(col);
                return col;
            }
            ENDCG
        }
        Pass
        {
            
            Blend SrcAlpha OneMinusSrcAlpha 
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define _IN_GAME_SCENE 1 
            #include "Common/InGame.cginc" 
            #include "UnityCG.cginc" 

            struct appdata
            {
                float4 vertex : POSITION;
                float4 tangent : TANGENT;
                float4 uv_Tex : TEXCOORD0;
                float4 worldPos : TEXCOORD1;   
                float4 uv : TEXCOORD7; 
                float3 normal : NORMAL;  
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float4 tangent : TANGENT;
                float4 uv_Tex : TEXCOORD0;
                float4 worldPos : TEXCOORD1;
                float4 TW0:TEXCOORD2;
                float4 TW1:TEXCOORD3;
                float4 TW2:TEXCOORD4;  
                float4 uv : TEXCOORD7; 
                float3 normal : NORMAL;  
            };
            
            sampler2D _Foam;
            float4 _Foam_ST;
            half4 _DeepColor;
            half4 _ShalowColor;

            sampler2D _WaterNormal;
            float4 _WaterNormal_ST;
            half _NormalScale;
            half4 _WaveParams;

            half _WaterSpecular;
            half _WaterSmoothness;
            half4 _LightDir;
            half4 _LightColor;

            half _RimPower;

            sampler2D _FormDisort;
            float4 _FormDisort_ST;

            half4 _FoamColor;
            half _FoamDepth;
            half _FoamFactor;
            half4 _FoamOffset; 

            half _WaterWave;
            half4 _DetailColor; 
            
            sampler2D _DissolveTex;
            float4 _DissolveTex_ST;
            half _Dissolve; 
            half _Mask;
            fixed4 _CeilColorA;
            fixed4 _CeilColorB;
            fixed _Cutoff;

            half3 BlendNormals(half3 n1,half3 n2)
            {
                return normalize(half3(n1.xy+n2.xy,n1.z*n2.z));
            } 
            v2f vert(appdata v)
            {
                v2f o;

                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv_Tex.xy = TRANSFORM_TEX(v.uv,_Foam);
                o.uv_Tex.zw = TRANSFORM_TEX(v.uv, _WaterNormal);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex);

                fixed3 worldNormal = UnityObjectToWorldNormal(v.normal);
                fixed3 worldTangent = UnityObjectToWorldDir(v.tangent.xyz);
                fixed tangentSign = v.tangent.w * unity_WorldTransformParams.w;
                fixed3 worldBinormal = cross(worldNormal, worldTangent) * tangentSign;

                o.TW0 = float4(worldTangent.x, worldBinormal.x, worldNormal.x, o.worldPos.x);
                o.TW1 = float4(worldTangent.y, worldBinormal.y, worldNormal.y, o.worldPos.y);
                o.TW2 = float4(worldTangent.z, worldBinormal.z, worldNormal.z, o.worldPos.z);
                
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {    
                half2 panner1 = (_Time.y * _WaveParams.xy + i.uv_Tex.zw);
                half2 panner2 = (_Time.y * _WaveParams.zw + i.uv_Tex.zw);
                half3 worldNormal = BlendNormals(UnpackNormal(tex2D(_WaterNormal, panner1)) , UnpackNormal(tex2D(_WaterNormal, panner2)));
                
                half4 water = tex2D(_Foam,i.uv_Tex.xy / _Foam_ST.xy);//
                //half3 foam1 = tex2D(_Foam,i.uv_Tex.xy + worldNormal.xy * _FoamOffset.w);
                //half3 foam2 = tex2D(_Foam, _Time.y * _FoamOffset.xy + i.uv_Tex.xy + worldNormal.xy * _FoamOffset.w);
                half2 detailpanner = (i.uv_Tex.xy / _Foam_ST.xy + worldNormal.xy * _WaterWave);

                worldNormal = lerp(half3(0, 0, 1), worldNormal, _NormalScale);
                worldNormal = normalize(fixed3(dot(i.TW0.xyz, worldNormal), dot(i.TW1.xyz, worldNormal), dot(i.TW2.xyz, worldNormal)));
                
                fixed3 viewDir = normalize(UnityWorldSpaceViewDir(i.worldPos));
                float NdotV = saturate(dot(worldNormal,viewDir));
                fixed3 worldLightDir = _LightDir.xyz;
                fixed3 halfDir = normalize(worldLightDir + viewDir);

                half4 diffuse = lerp(_ShalowColor, _DeepColor, water.r);
                fixed3 specular = _LightColor.rgb * _WaterSpecular * pow(max(0, dot(worldNormal, halfDir)), _WaterSmoothness * 256.0);
                fixed3 rim = pow(1 - saturate(NdotV),_RimPower) * _LightColor;
                half4 detail = tex2D(_Foam,detailpanner).b * _DetailColor;
               
                //溶解
                half2 uv = i.uv_Tex.xy;
                half2 uv2 = i.uv_Tex.xy;
                fixed ClipTex = tex2D(_DissolveTex, uv2.xy).r;
                fixed ClipValue = ClipTex + ( _Dissolve  * (-1.2)) + 0.1;//-0.1~1.1 & -1.1~0.1
                clip(ClipValue);
                float degree = saturate(ClipValue / _Mask);
                fixed4 edgeColor = lerp(_CeilColorA, _CeilColorB, degree); 
                degree = _Cutoff > 0.5 ? degree == 1 : degree;
                
                half alpha =  water.a;
                fixed4 col = fixed4(diffuse * NdotV * 0.5 + specular  + rim * 0.2  + diffuse.rgb  * detail.rgb * 0.5   ,alpha); 
                col = lerp(edgeColor , col, degree); 
                APPLY_EFFECT_DARK_SCENE(col);
                return  col;
            }
            ENDCG
        }

    } 
}