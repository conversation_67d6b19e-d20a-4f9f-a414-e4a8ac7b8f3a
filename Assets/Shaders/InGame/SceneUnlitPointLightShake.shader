Shader "InGame/Scene/PointLight_Shake" 
{
    Properties 
    {    
        _dark("Dark", Range(0.0, 1.0)) = 0
        _BrightnessPower("Brightness", Range(1, 2.5)) = 1
        _MainTex ("Base (RGB)", 2D) = "white" {}
        _PointLightMin("Point Light Min Edge", Range(0, 1)) = 0
        _PointLightMax("Point Light Max Edge", Range(0, 1)) = 1
        _PointPower("Point Light Power", Range(0, 1)) = 1
        _NLPower("Light Smooth", Range(0, 1)) = 0.5

        [Space(10)]
        [Header(Shake)]
        _Scale("变形强度", Range(0,1)) = 0
        _YScale("Y轴缩放强度", Range(0.1,1)) = 0.8
        _XZScale("X轴缩放强度", Range(0.5,2)) = 1.2
        _CenterY("中心偏移值", Float) = 0

        [HideInInspector]_SceneVignetteColor("SceneVignetteColor",Color) = (0,0,0,1.0)
        [HideInInspector]_SceneVignetteRadius("SceneVignetteRadius", float) = 1
        [HideInInspector] _SceneVignettePos("SceneVignettePos", vector) = (0.5,0.5,0,0)
        [HideInInspector]_SceneVignette("SceneVignettePos", float) = 0

        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
    }

    SubShader 
    {
        Tags { "RenderType"="Opaque" }
        LOD 100

        Pass 
        {
			// 遮挡描边的场景, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

			ColorMask RGB
            Tags { "LightMode"="ForwardBase" }
            CGPROGRAM
                #pragma vertex vert
                #pragma fragment frag
                #pragma multi_compile _ VERTEXLIGHT_ON

                #include "UnityCG.cginc"
                #include "AutoLight.cginc"
                #define _IN_GAME_SCENE 1
                #include "Common/InGame.cginc"

                struct appdata_t {
                    float4 vertex : POSITION;
                    float2 texcoord : TEXCOORD0;
                    float3 normal : NORMAL;
                    UNITY_VERTEX_INPUT_INSTANCE_ID
                };

                struct v2f {
                    float4 vertex : SV_POSITION;
                    float2 texcoord : TEXCOORD0;
                    UNITY_VERTEX_OUTPUT_STEREO

                    half3 worldNormal : TEXCOORD2;
                    float3 worldPos : TEXCOORD3;
                    float4 screenPos : TEXCOORD5;
                    
                    half3 vertexLightColor : TEXCOORD4;
                };

                sampler2D _MainTex;
                float4 _MainTex_ST;

                sampler2D _LightTex;
                half4 _LightTexColor;
                fixed _PointLightMin,_PointLightMax, _PointPower, _NLPower, _BrightnessPower;

                fixed _CenterY, _Scale, _XZScale, _YScale;

                half3 ComputeVertexLightColor(float3 worldPos, half3 worldNormal, half edgeMin, half edgeMax, out half3 lightPos)
                {
                    lightPos = float3(unity_4LightPosX0.x, unity_4LightPosY0.x, unity_4LightPosZ0.x);
                    float3 lightVec = lightPos - worldPos;
                    float3 lightDir = normalize(lightVec);
                    
                    float ndotl = saturate(dot(worldNormal, lightDir));
                    ndotl = lerp(0.5, ndotl, _NLPower);
                    
                    float attenuation = 1 / (1 + dot(lightVec, lightVec) * unity_4LightAtten0.x);
                    attenuation = smoothstep(edgeMin, edgeMax, attenuation);

                    half3 vertexLightColor = unity_LightColor[0].rgb * ndotl * attenuation ;

                    return vertexLightColor;
                }

                v2f vert (appdata_t v)
                {
                    v2f o;
                    UNITY_SETUP_INSTANCE_ID(v);
                    UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                    half verOff = abs(v.vertex.y - _CenterY);
                    fixed shakePower = _Scale;
                    fixed yScale = lerp(1, _YScale, shakePower);
                    v.vertex.y = (v.vertex.y - _CenterY) * yScale + _CenterY;
                    v.vertex.x = v.vertex.x  * lerp( 1, lerp(_XZScale, 1, pow(verOff, 2)), shakePower) ;


                    o.vertex = UnityObjectToClipPos(v.vertex);
                    o.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);

                    o.worldNormal = UnityObjectToWorldNormal(v.normal);
                    o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                    o.screenPos = ComputeScreenPos(o.vertex);

                    return o;
                }

                fixed4 frag (v2f i) : SV_Target
                {
                    fixed4 col = tex2D(_MainTex, i.texcoord) * _BrightnessPower;

                    #if defined(VERTEXLIGHT_ON)
                        half3 lightPos ;
					    half3 vertexLightColor = ComputeVertexLightColor(i.worldPos, normalize(i.worldNormal) , _PointLightMin, _PointLightMax, lightPos);
                        col.rgb += vertexLightColor * col.rgb * _PointPower;
                    #endif
                    
                        APPLY_HEIGHT_FOG(col, i.worldPos.y);
                        APPLY_VIGNETTE(col, i.screenPos.xy/ i.screenPos.w);
                    
                    APPLY_EFFECT_DARK_SCENE(col);
                    return col;
                }
            ENDCG
        }
    }

}
