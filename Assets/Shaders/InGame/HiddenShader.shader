Shader "InGame/Scene/Hidden"
{
    Properties
    {
        _Color("Color",Color) = (1.0,1.0,1.0,1.0)
        _MainTex("Texture", 2D) = "white" {}
    /*  _SceneFog("Fog",float)=0
        _SceneFogColor("Fog Color",Color)=(1,1,1,1)
        _SceneFogStart("Fog Start",float) = 0
        _SceneFogEnd("Fog End",float)=5*/
        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", Float) = 2
        //blending state 
        [HideInInspector] _Mode("Blend Mode", float) = 0
        [HideInInspector] _SrcBlend("Source Blend", float) = 1.0
        [HideInInspector] _DstBlend("Destination Blend", float) = 0.0
        [HideInInspector] _ZWrite("Z Write", float) = 1.0
         _Cutoff("Alpha Cutoff", Range(0.0, 0.95)) = 0.8

        [HideInInspector] _RenderQueueOffset("_Render Queue Offset", int) = 0
         //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0

    

        [HideInInspector]_SceneVignetteColor("SceneVignetteColor",Color) = (0,0,0,1.0)
        [HideInInspector]_SceneVignetteRadius("SceneVignetteRadius", float) = 1
        [HideInInspector] _SceneVignettePos("SceneVignettePos", vector) = (0.5,0.5,0,0)
        [HideInInspector]_SceneVignette("SceneVignettePos", float) = 0


    }
    SubShader
    {
        Tags { "RenderType"="Opaque"/* "DisableBatching"="True" */}
        LOD 100
        Pass
        {
			// 遮挡描边的场景, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

            Blend[_SrcBlend][_DstBlend]
            Zwrite[_ZWrite]
            Cull[_Cull]

            CGPROGRAM
            #pragma multi_compile_instancing
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ LIGHTMAP_ON        
            #pragma multi_compile _ LIGHTPROBE_SH
            #pragma shader_feature _ _ALPHATEST_ON _ALPHABLEND_ON // _ALPHAPREMULTIPLY_ON

            #define _IN_GAME_SCENE 1
            
            #include "Common/InGame.cginc"
            #include "../TKCGFast.cginc"
            struct appdata
            {
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float4 vertex       : POSITION;
                half2 uv            : TEXCOORD0;
#ifdef LIGHTMAP_ON
                half2 uv1           : TEXCOORD1;
#endif
            };

            struct v2f
            {
                half4 vertex        : SV_POSITION;
#ifdef LIGHTMAP_ON
                half4 uv            : TEXCOORD0;        //xy for uv0, zw for uv1
#else
                half2 uv            : TEXCOORD0;
#endif
                half4 fragPos     : TEXCOORD1;        //worldPos.Y, screenPos.XZW
            };

            fixed4 _Color;
            sampler2D _MainTex;
            float4 _MainTex_ST;

#ifdef _ALPHATEST_ON
            half _Cutoff;
#endif
            v2f vert (appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                float3 posWorld = ObjectToWorldFast(v.vertex);
                o.vertex = WorldToClipFast(posWorld);
                o.fragPos.x = posWorld.y;
                o.fragPos.yzw = ComputeScreenPos(o.vertex).xzw;
#ifdef LIGHTMAP_ON
                o.uv = half4(v.uv, v.uv1) * half4(_MainTex_ST.xy, unity_LightmapST.xy) + half4(_MainTex_ST.zw, unity_LightmapST.zw);
#else
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
#endif	
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				discard;
                fixed4 col = tex2D(_MainTex, i.uv.xy);
                return col; 
            }
            ENDCG
        }
    }
        FallBack "Unlit/Texture"
}
