Shader "InGame/Scene/Transition" 
{
    Properties 
    {    
        _Brightness("Brightness", Range(1, 2.5)) = 1
        _MainTex ("Base01 (RGB)", 2D) = "white" {}
        _MainTex01 ("Base02 (RGB)", 2D) = "white" {}
        
        _LinePatternTex ("Mask Texture", 2D) = "white" {}
        _LineDistortTex("Distort Texture", 2D) = "white" {}

      // _Test("Distort Texture", 2D) = "white" {}
        // [HideInInspector]_Distort("Distort", float) =1 
        // [HideInInspector][HDR]_LineColor("Line Color", Color) = (1,1,1,1)
        
        [HideInInspector]_SceneVignetteColor("SceneVignetteColor",Color) = (0,0,0,1.0)
        [HideInInspector]_SceneVignetteRadius("SceneVignetteRadius", float) = 1
        [HideInInspector] _SceneVignettePos("SceneVignettePos", vector) = (0.5,0.5,0,0)
        [HideInInspector]_SceneVignette("SceneVignettePos", float) = 0

        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
        
        [Header(Stencil)]
        [IntRange] _Stencil ("Stencil ID", Range(0,255)) = 5
        [Enum(UnityEngine.Rendering.CompareFunction)] _StencilComp ("Stencil Comparison", Float) = 3
        [Enum(UnityEngine.Rendering.StencilOp)] _StencilPass ("Stencil Pass", Float) = 0
        [Enum(Off, 0, On, 1)]_ZWriteMode ("ZWriteMode", float) = 1 
        [Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 4
        
    }

    SubShader 
    {
        Tags { "RenderType"="Opaque" }

        Pass 
        {
            
            // Stencil
            // { 
            //     Ref [_Stencil]
            //     Comp [_StencilComp]
            //     Pass[_StencilPass]
            // }  
            // ZWrite[_ZWriteMode]
            // ZTest[_ZTestMode]
            
            // /ColorMask RGB
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _IN_GAME_SCENE 1
            #include "Common/InGame.cginc"
            #include "Common/InGameSceneTransitions.cginc"
            struct appdata_t 
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;

                half3 worldNormal : TEXCOORD1;
                float3 worldPos : TEXCOORD2;
                float4 screenPos : TEXCOORD3;

            };

            sampler2D _MainTex,_MainTex01;
        
            
            v2f vert (appdata_t v)
            {
                v2f o;
                UNITY_INITIALIZE_OUTPUT(v2f, o);
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.screenPos = ComputeScreenPos(o.vertex);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            { 
                half4 col01 = tex2D(_MainTex, i.uv);
                half4 col02 = tex2D(_MainTex01, i.uv); 
                half4 finalColor =   GetMaskRadius(col01,col02,i.worldPos, i.uv);
                return finalColor; 
            }
            ENDCG
        }

    }

}
