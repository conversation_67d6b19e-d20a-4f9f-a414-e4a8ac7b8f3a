Shader "InGame/Scene/Sparkle"
{
    Properties
    {
        _MainTex("Main Tex", 2D) = "white" {}
        _MainCol("Main Color", Color) = (1,1,1,1)
        _SparkleTex ("Sparkle Tex", 2D) = "black" {} 
        _Sparkle ("Sparkle", Range(0, 5)) = 1 
        _SprkleSpeed("SparkleSpeed", Range(0, 5)) = 1

        [HDR]_SparkleColor ("Sparkle Color", Color) = (1,1,1,1)
        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0

    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        // 遮挡描边的场景, 参考值5
        Stencil
        {
            Ref 5
            Comp Always
            Pass Replace
        }
        

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _IN_GAME_SCENE 1
            
            #include "Common/InGame.cginc"

            #include "UnityCG.cginc"

            sampler2D _MainTex,_SparkleTex;
            float4 _SparkleTex_ST, _MainTex_ST; 

            float _Sparkle,_SprkleSpeed;
            half4 _SparkleColor, _MainCol;

            struct appdata
            {
                float4 vertex	: POSITION; 
                float2 uv : TEXCOORD0;
                float2 uvmain : TEXCOORD1;
            };

            struct v2f
            {
                float4 pos		: SV_POSITION; 
                float2 uv		: TEXCOORD2;
                float2 uvmain		: TEXCOORD3;
            };

            v2f vert (appdata v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex); 
                o.uv =  TRANSFORM_TEX(v.uv, _SparkleTex);
                o.uvmain = TRANSFORM_TEX(v.uvmain, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                float2 uv = i.uv; 
                fixed4 col_1 = tex2D(_SparkleTex, uv+  frac(0.01*sin(i.uv*3.14 * 9 + _Time.y *_SprkleSpeed)));  
                fixed4 col_2 = tex2D(_SparkleTex, uv+ frac(0.01*sin(i.uv*3.14*5+ _Time.y *_SprkleSpeed))); 

                float sparkle = pow(col_1.r * col_2.r, _Sparkle);  
                half3 sparkleColor = sparkle * _SparkleColor.rgb;
                half4 maincol = tex2D(_MainTex, i.uvmain)* _MainCol;
                maincol= float4(lerp(maincol, maincol + sparkleColor, maincol.a),1);
                APPLY_EFFECT_DARK_SCENE(maincol); 
                return maincol;
            }
            ENDCG
        }
    }
}
