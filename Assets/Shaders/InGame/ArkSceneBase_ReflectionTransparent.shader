Shader "InGame/Scene/Ark/Base_ReflectionTransparent"
{
    Properties
    { 
        _MainTex ("主贴图", 2D) = "white" {}
        _EmissiveTex ("自发光 贴图", 2D) = "black" {}

        [Header(Cloud Shadow)]
        _PatternTex ("云投影 图案", 2D) = "white" {}

        [Header(Wet Density)]
        _RainMaskTex ("雨水扩散遮罩贴图", 2D) = "white" {}
        _RippleTex("涟漪贴图", 2D) = "white" {}

        _Reflectionmap ("反射贴图", 2D) = "" {}
    }

    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent"}
        Blend SrcAlpha OneMinusSrcAlpha
        Pass
        {
            	// 遮挡描边的场景, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile _ _RIPPLE_ON
            #pragma multi_compile _ _CLOUD_ON
            #pragma multi_compile _ LIGHTMAP_ON

            #include "Common/InGame.cginc"
            #include "Common/InGameArk.cginc"
       
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                #ifdef LIGHTMAP_ON
                    float2 uv1: TEXCOORD1;
                #endif

                half3 normal: NORMAL;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;

                #ifdef LIGHTMAP_ON
                    half4 uv            : TEXCOORD0;        //xy for uv0, zw for uv1
                #else
                    half2 uv            : TEXCOORD0;
                #endif

                float3 worldPos: TEXCOORD2;
                float3 worldNormal: TEXCOORD3;

            };


            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);

                #ifdef LIGHTMAP_ON
                    o.uv = half4(v.uv, v.uv1) * half4(_MainTex_ST.xy, unity_LightmapST.xy) + half4(_MainTex_ST.zw, unity_LightmapST.zw);
                #else
                    o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                #endif	

                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;  
                o.worldNormal = UnityObjectToWorldNormal(v.normal);  

                return o;
            }


             fixed4 frag (v2f i) : SV_Target
            {
               
                float3 worldNormal = normalize(i.worldNormal);

                #ifdef _RIPPLE_ON
                    float2 rippleUV =worldUV(i.worldPos) * _RippleTex_ST.xy + _RippleTex_ST.zw;
                    float3 ripple = ComputeRipple(rippleUV / ark_rippleScale, _Time.x * ark_rippleSpeed, ark_ripplePower, _RippleTex) ;
                    i.uv.x += lerp(0, lerp(0, ripple.r, saturate(ark_WetPower)), worldNormal.y) ;
                #endif

                // sample the texture
                half4 albedo = tex2D(_MainTex, i.uv.xy);

                #ifdef LIGHTMAP_ON
                    fixed3 lm = DecodeLightmap(UNITY_SAMPLE_TEX2D(unity_Lightmap,i.uv.zw));
                    albedo.rgb *= lm;
                #endif

                //Emission Color
                half4 emission = tex2D(_EmissiveTex, i.uv.xy) * ark_EmissionColor;
                //Final Color
                half4 col = albedo;
                col.rgb = albedo.rgb  * ark_GlobalColor.rgb + emission.rgb;
 
                //云投影动画
                #ifdef _CLOUD_ON
                    half2 rollSpeed = half2(ark_CloudSpeedX, ark_CloudSpeedY);
                    half cloudTex = tex2D(_PatternTex, worldUV(i.worldPos) * ark_cloudTilling.xy + ark_cloudTilling.zw + frac(_Time.y * rollSpeed)).r;
                    col.rgb *= lerp(1, lerp(ark_CloudColor.rgb, 1, cloudTex), ark_CloudPower);
                #endif


                //地面湿润
                #ifdef _RIPPLE_ON
                    half rainMask = tex2D(_RainMaskTex, worldUV(i.worldPos) * _RainMaskTex_ST.xy).r;
                    col.rgb = lerp(col.rgb, col.rgb * 0.8 , saturate(rainMask + ark_WetPower));
                #endif

                //Reflection
                fixed3 worldViewDir = normalize(_WorldSpaceCameraPos.xyz - i.worldPos.xyz);
                float3 reflectDir = reflect(-worldViewDir, worldNormal); 
                fixed4 reflection = tex2D(_Reflectionmap, reflectDir * ark_ReflectivityTilling.xy + ark_ReflectivityTilling.zw);

                float fresnel = pow(1 - saturate(dot(worldViewDir, worldNormal)), ark_FresnelFactor);
                fresnel = smoothstep(ark_FresnelMin,ark_FresnelMax,fresnel);

                col.rgb += ark_FresnelColor.rgb*fresnel; 
                col.rgb = lerp(col.rgb,reflection.rgb,  ark_Reflectivity*fresnel);

                col.a = max(col.a,fresnel);
                col.a *= ark_ReflectivityAlpha;

                APPLY_HEIGHT_FOG(col,i.worldPos.y);
                return col;
            }
            ENDCG
        }

        Pass
        {
            Name "Meta"
            Tags
            {
                "LightMode" = "Meta"
            }
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            #include "UnityMetaPass.cginc"
            fixed4 _Color;
            sampler2D _MainTex;
            float4 _MainTex_ST;
            
            struct v2f
            {
                half4 vertex        : SV_POSITION;
                half2 uv            : TEXCOORD0;
            };
            
            v2f vert (appdata_full v)
            {
                v2f o;
                o.vertex = UnityMetaVertexPosition(v.vertex, v.texcoord1.xy, v.texcoord2.xy, unity_LightmapST, unity_DynamicLightmapST);
                o.uv = TRANSFORM_TEX(v.texcoord, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                UnityMetaInput metaIN;
                UNITY_INITIALIZE_OUTPUT(UnityMetaInput,metaIN);
                fixed4 col = tex2D(_MainTex, i.uv.xy);
                col *= _Color;
                metaIN.Emission = col;
                metaIN.Albedo = col;
                return UnityMetaFragment(metaIN);
            }
            ENDCG
        }
        
    }
}
