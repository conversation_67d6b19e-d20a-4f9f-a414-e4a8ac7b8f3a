Shader "InGame/Scene/SceneTransform"
{
	Properties
	{

		_MainTex01("Tex01", 2D) = "white" {}
		_Color01("Color01", Color) = (1,1,1,1)
		_MainTex02("Tex02", 2D) = "white" {}
		_Color02("Color02", Color) = (1,1,1,1)

		_StarTex("StarTex",2D) = "white" {}
		_MaskTex("MaskTex",2D) = "white" {}
		[HDR]_EdgeEmission("Edge Emission", Color) = (1,1,1,1)
		_DissolveNoise("Dissolve Noise", 2D) = "white" {}
		[Toggle]_InvertDissolve("Invert Dissolve", Float) = 0
		[Space(5)]
		[Header(Mask)]
		[Space(5)]
		_Position("Position",Vector) = (0,0,0,0)
		_Radius("Radius", Range(-10 , 80)) = 0
		_Intensity("Intensity", Range(1 , 10)) = 0
		_NoiseStrength("NoiseStrength", Range(0 , 10)) = 0
		_EdgeSize("RingSize", Range(0 , 10)) = 0
		_EdgeOffset("EdgeOffset", Range(0 , 10)) = 0
		_Smooth("Smooth", Range(0 , 10)) = 0
		_OutSmooth("OutSmooth",Range(0,10)) = 0


	}
	SubShader
	{
		Tags
		{ "RenderType" = "TransparentCutout" }
		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag


			#define _IN_GAME_SCENE 1

			#include "Common/InGame.cginc"
			#include "../TKCGFast.cginc"
			#include "UnityCG.cginc"
			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
			};

			struct v2f
			{
				float4 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
				float4 uv2 : TEXCOORD1;
				float3 worldPos : TEXCOORD3;
			};

			fixed4 _Color01,_Color02;
			sampler2D _MainTex01,_MainTex02;
			float4 _MainTex01_ST,_MainTex02_ST;
			sampler2D _StarTex,_DissolveNoise,_MaskTex;
			float4 _StarTex_ST,_DissolveNoise_ST,_MaskTex_ST;

			fixed4 _EdgeEmission;
			float _InvertDissolve;

			half _Radius,
			_Intensity,
			_NoiseStrength,
			_EdgeSize,
			_Smooth;
			half _OutSmooth,_EdgeOffset;


			float4 _Position;

			inline float3 RadialMask(float3 vertexWorldPosition, float noise, float3 maskPosition, float radius, float intensity, float noiseStrength, float edgeSize, float smooth)
			{
				//Distance
				float d = distance(maskPosition, vertexWorldPosition);
				//Noise
				radius += noise * noiseStrength;
				float shape = 1 - saturate(max(0, d -radius  + edgeSize) / edgeSize);
				float ring = saturate(max(0, d  - radius - _EdgeOffset ) /  _EdgeOffset );

				//Smooth
				shape = pow(shape, smooth + 0.01);
				ring = pow(ring, _OutSmooth + 0.01);
				shape *= intensity;
				ring +=shape;
				ring = saturate(1-ring);
				//float edge =  saturate(pow(ring, 0.01)) -pow(ring, 10)- saturate(pow(shape,  0.01));
				float edge =  pow(ring, 0.01) -pow(ring, 10)- pow(shape,  0.01);
				//float edge =  ring- pow(shape,  0.01);

				return float3(shape,ring,edge);
			}


			float3 RadialMaskNormalized(float3 positionWS, float noise,float3 center)
			{
				float3 retValue = 1;

				retValue *= 1 - RadialMask(positionWS,
				noise,
				center + _Position.xyz,
				_Radius,
				_Intensity,
				_NoiseStrength,
				_EdgeSize,
				_Smooth);
				return 1 - retValue;
			}

			v2f vert(appdata v)
			{
				v2f o;
				o.worldPos = ObjectToWorldFast(v.vertex);
				o.vertex = WorldToClipFast(o.worldPos);
				o.uv.xy = v.uv;
				o.uv.zw =TRANSFORM_TEX(v.uv,_MaskTex);
				o.uv2.xy = TRANSFORM_TEX(v.uv,_DissolveNoise);
				o.uv2.zw = TRANSFORM_TEX(v.uv,_StarTex);
				return o;
			}

			fixed4 frag(v2f i, fixed facing : VFACE) : SV_Target
			{

				if(_Radius > 49.9 )//0 - 50 Tex02 to Tex01 Trans end
				{
					float4 col = tex2D(_MainTex01,  i.uv.xy)*_Color01;
					APPLY_EFFECT_DARK_SCENE(col);
					return col;
				}
				else if(_Radius < 0.01)//50 -1 Tex01 to Tex02 Trans end
				{
					float4 col = tex2D(_MainTex02,  i.uv.xy)*_Color02;
					APPLY_EFFECT_DARK_SCENE(col);
					return col;
				}
				else
				{
					float3 center = transpose(unity_ObjectToWorld)[3].xyz;
					float noise = tex2D(_DissolveNoise, i.uv2.xy).r;
					float4 star = tex2D(_StarTex, i.uv2.zw);

					float3 mask =RadialMaskNormalized(i.worldPos, noise,center);
					float starmask =tex2D(_MaskTex, i.uv.zw).r;

					float shape = mask.x;
					float ring = mask.y;
					float edge =mask.z;

					shape = lerp(shape, 1 - shape, _InvertDissolve);
					float4 col01 = tex2D(_MainTex01,  i.uv.xy)*_Color01;
					float4 col02 = tex2D(_MainTex02,  i.uv.xy)*_Color02;

					col01 = float4(lerp(col01.rgb ,col02.rgb,saturate(shape)),1);
					col01 = float4(lerp(col01.rgb ,star.rgb,saturate(ring*starmask.r) ),1);
					col01  = float4(lerp(col01.rgb,_EdgeEmission.rgb,saturate(edge)*_EdgeEmission.a),1);
					// float masktest = saturate(ring)*starmask.r;
					// col01 = float4(masktest,masktest,masktest,1);
					APPLY_EFFECT_DARK_SCENE(col01);
					return col01;
				}
			}
			ENDCG
		}

	}

}
