// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Z_UI/RotateShader" {       
       Properties
       {
           _TintColor ("Tint Color", Color) = (0.5,0.5,0.5,0.5)
           _MainTex("Main Tex",2D)="Write"{}
           //给unity3d提供一个滑动条来控制旋转速度
           _RotateSpeed("Rotate Speed",Range(0,10))=5
       }
       SubShader
       {
         Tags{"Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent"}
         Blend SrcAlpha One
         Cull Off Lighting Off ZWrite Off Fog { Color (0,0,0,0) }
         
         Pass
         {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MainTex;
            fixed4 _TintColor;
            float _RotateSpeed;
            struct v2f
            {
               float4 pos:POSITION;
               float4 uv:TEXCOORD;
            };
            v2f vert(appdata_base v)
            {
               v2f o;
               o.pos=UnityObjectToClipPos(v.vertex);
               o.uv=v.texcoord;
               return o;
                }
            half4 frag(v2f i):COLOR
            {  //定义一个float2来存储顶点的UV的XY,减去0.5是因为uv旋转的起始是1,1为中心，XY都减去0.5是把中心点移到中心。
               float2 uv=i.uv.xy -float2(0.5,0.5);
              //        _Time   是一个内置的float4时间，X是1/20，Y是1倍，Z是2倍，W是3倍           
              //        旋转矩阵的公式是： COS() -  sin() , sin() + cos()     顺时针
              //                                             COS() +  sin() , sin() - cos()     逆时针     
               uv = float2(    uv.x*cos(_RotateSpeed * _Time.y) - uv.y*sin(_RotateSpeed*_Time.y),
                                      uv.x*sin(_RotateSpeed * _Time.y) + uv.y*cos(_RotateSpeed*_Time.y) );
               //再加回来
               uv += float2(0.5,0.5);
               half4 c=tex2D(_MainTex,uv);
               return c;
            }
            ENDCG        
         }                  
     }
}
