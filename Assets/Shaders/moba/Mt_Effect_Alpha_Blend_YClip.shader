Shader "Mt/Effect/YClip"
{
	Properties
	{
		_MainTex("Tex", 2D) = "white" {}
	    _Color("Color", Color) = (1,1,1,1)
		_YTopOffset("Y-TopOffset",float) = 0
		_YGroundOffset("Y-GroundOffset",float) = 0 
		_YSmoothnessTop("Y-TopEdgeWidth",Range(0,5)) = 0 
		_YSmoothnessGround("Y-GroundEdgeWidth",Range(0,5)) = 0 
		[ToggleOff]_Invert("Invert?",float) = 0 
		[Enum(Off, 0, Front, 1, Back,2)]_Cull ("Cull", Float) = 2
		[Enum(Off, 0, On, 1)]_ZWrite ("ZWrite", Float) = 0
		[Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 4

	}
	Category
	{
		
		SubShader
		{ 
			Tags
			{
				"Queue"="Transparent" 
				"RenderType"="Transparent"
			}
			Blend SrcAlpha OneMinusSrcAl<PERSON>  
			Cull [_Cull]
			ZWrite [_ZWrite]
			ZTest [_ZTestMode]
			Pass
			{
				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag

				#include "UnityCG.cginc" 
				struct appdata
				{
					float4 vertex : POSITION;
					float2 uv : TEXCOORD0; 
				};

				struct v2f
				{
					float2 uv : TEXCOORD0;
					float4 vertex : SV_POSITION; 
					float3 worldPos : TEXCOORD1;
				};
 
				sampler2D _MainTex;
				float4 _MainTex_ST;
				half4 _Color;
				half _YSmoothnessTop,_YSmoothnessGround,_YTopOffset,_YGroundOffset;
				half _Invert;

				v2f vert(appdata v)
				{
					v2f o;
					o.vertex = UnityObjectToClipPos(v.vertex);
					o.uv = v.uv; 
					o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
					return o;
				}

				fixed4 frag(v2f i, fixed facing : VFACE) : SV_Target
				{ 
					float4 col = tex2D(_MainTex,i.uv.xy)*_Color;
					float worldPosYTop = _YTopOffset-i.worldPos.y;
					float worldPosYGround = _YGroundOffset+i.worldPos.y;

					worldPosYTop = pow(saturate(worldPosYTop), max(0.0001, _YSmoothnessTop));
				    worldPosYGround = pow(saturate(worldPosYGround), max(0.0001, _YSmoothnessGround));

					float worldPosClipRange = worldPosYTop * worldPosYGround;
					worldPosClipRange = _Invert*(1-worldPosClipRange) + worldPosClipRange*step(_Invert,0);
					 
					col.a *= worldPosClipRange;
					
					return col; 
				}
				ENDCG
			}

		}

	}
}
