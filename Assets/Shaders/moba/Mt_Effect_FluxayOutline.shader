Shader "Mt/Effect/FluxayOnline"
{
	Properties
	{
		[Toggle] _GPU_SKINNING("GPU Skinning", float) = 0
		[Toggle]_Void("Is Void?",float) = 0
		_VoidTexture("Void Flowing Texture",2D) = "black"{}
		[HDR]_VoidColor("Void Color",Color) = (1.0,1.0,1.0,1.0)
		_VoidScreenSpeedX("Void Screen Speed X",Range(-5,5)) = 0
		_VoidScreenSpeedY("Void Screen Speed Y",Range(-5,5)) = 0
		[HDR]_VoidFlowingColor("Void Flowing Color",Color) = (0.0,0.0,0.0,0.0)

		_MainTex("Color", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularTex("Flowing Mask", 2D) = "black" {}
		_ScreenSpeedX("Screen Speed X",Range(-5,5)) = 0
		_ScreenSpeedY("Screen Speed Y",Range(-5,5)) = 0
		[HDR]_FlowingColor("Flowing Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		_SpeedU("Speed X",Range(-10,10)) = 0
		_SpeedV("Speed Y",Range(-10,10)) = 0
		_FluxayColor("Fluxay Color", Color) = (1.0,1.0,1.0,1.0)
		_FresnelFluxay("Fresnel Fluxay", 2D) = "black" {}
		[HDR]_FluxayFresnelColor("Fluxay Fresnel Color", Color) = (1.0,1.0,1.0,1.0)

		[Toggle]_CorrectMatcap("Use Matcap",float) = 0
		//_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelFromValue("Fresnel From Value",Range(0,1)) = 0
		_FresnelToValue("Fresnel To Value",Range(0,1)) = 1
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		_DissolveTex("Dissolve Texture", 2D) = "white" {}
		_Dissolve ("Dissolve Value", Range(0,1)) = 0

		_OutlineWidth("Outline Width", Range(0.0,1.0)) = 0.2
		[HDR]_OutlineColor("Outline Color", Color) = (0.0,0.73,1.0,1.0)
		_OutlineFactor("Outline Factor", Range(0.0,1.0)) = 1
		_OutlineExponent("Outline Exponent", Range(0.1,10)) = 1.5
		_OutlineRimAlpha("Outline Alpha", Range(0.0,1)) = 0.15

		_AvatarTransform("XYZ:Avatar Offset,W:Scale",Vector) = (0,0,0,1)

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	SubShader
	{
		LOD 300
		Tags
		{
			"RenderType" = "Opaque"
		}
		Tags
		{
			"RenderType" = "Opaque"
		}
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}

			// 参考值1作为描边Mask
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
			}

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragOpaqueFluxay
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON
			#pragma shader_feature _GPU_SKINNING_ON


			ENDCG
		}
		Pass
		{
			Tags
			{
				"RenderType" = "Transparent"
			}
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
			}
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			struct VertexInput
			{
				float4 vertex : POSITION;
			};
			struct VertexOutput
			{
				float4 pos : SV_POSITION;
			};

			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;
				o.pos = UnityObjectToClipPos(v.vertex);
				return o;
			}

			fixed4 frag(VertexOutput v) : COLOR
			{
				return 0;
			}
			ENDCG
		}
		Pass
		{
			Tags
			{
				"RenderType" = "Opaque"
			}
			Stencil
			{
				Ref 1
				Comp NotEqual
				Pass Keep
				//Fail keep
				//ZFail keep
			}
			Name "Outline"
			ZTest Off
			Cull Off

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			#pragma fragmentoption ARB_precision_hint_fastest
			//#pragma multi_compile_shadowcaster

			uniform half _OutlineWidth;
			uniform fixed4 _OutlineColor;
			uniform fixed _OutlineFactor;
			uniform float _OutlineExponent;

			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
			};
			struct VertexOutput
			{
				float4 pos : SV_POSITION;
			};
			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;

				float4 worldPos = mul(unity_ObjectToWorld,v.vertex);
				float3 dir1 = normalize(v.vertex.xyz);
				float3 dir2 = normalize(v.normal);
				float3 dir = lerp(dir1, dir2, _OutlineFactor);
				float3 offset = dir * _OutlineWidth;
				offset.xyz = v.vertex.xyz + offset.xyz;
				o.pos = UnityObjectToClipPos(float4(offset,1));
				return o;
			}
			float4 frag(VertexOutput i) : COLOR
			{
				fixed4 finalColor = fixed4(0, 0, 0, 1);

				finalColor.rgb = _OutlineColor.rgb;

				return finalColor;
			}
			ENDCG
		}
		/*Pass
		{
			Name "HeroFakeShadow"
			Stencil
			{
				Ref 0
				Comp equal
				Pass incrWrap
				Fail keep
				ZFail keep
			}
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
			ZWrite off
			ZTest Off

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert_fakeshadow
			#pragma fragment frag_fakeshadowOpaque

			ENDCG
		}*/
	}
	SubShader
	{
		LOD 150
		Tags
		{
			"RenderType" = "Opaque"
		}
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
				//Fail keep
				//ZFail Invert
			}
			//ZTest Off

			CGPROGRAM
			#include "UnityCG.cginc"
			#pragma vertex vert
			#pragma fragment fragOpaque

			uniform sampler2D _MainTex;
			uniform fixed4 _MainCol;
			uniform float _SpeedX;
			uniform float _SpeedY;
			uniform fixed4 _FluxayColor;
			uniform sampler2D _FresnelFluxay;
			uniform float _FresnelExponent;
			uniform fixed4 _FresnelColor;
			uniform fixed4 _FluxayFresnelColor;
			uniform float _FresnelScale;
			uniform sampler2D _DissolveTex;
			uniform fixed _Dissolve;
			uniform float _SpecularExponent;
			uniform fixed4 _SpecularColor;
			uniform float _SpecularScale;

			struct VertexInput
			{
				float4 vertex : POSITION;
				float2 texcoord : TEXCOORD0;
				float3 normal : NORMAL;
			};

			struct VertexOutput
			{
				float4 pos : SV_POSITION;
				half2 uv : TEXCOORD0;
				fixed3 normalDir : TEXCOORD1;
				float4 posWorld : TEXCOORD2;
			};

			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;
				o.uv.xy = v.texcoord.xy;
				o.normalDir = UnityObjectToWorldNormal(v.normal);
				o.posWorld = mul(unity_ObjectToWorld, v.vertex);
				o.pos = UnityObjectToClipPos(v.vertex);
				return o;
			}

			fixed4 fragOpaque(VertexOutput i) : COLOR
			{
				fixed4 finalColor = fixed4(0,0,0,1);
				fixed4 source1 = tex2D(_MainTex, i.uv.xy);
				fixed dissolve = tex2D(_DissolveTex, i.uv.xy);
				fixed dissolveArea = step(dissolve, _Dissolve);
				fixed3 albedo = source1.rgb;
				fixed specMask = source1.a;
				fixed rimMask = step(0.01, specMask);

				half2 uv2 = i.uv.xy + frac(_Time.yy * half2(_SpeedX, _SpeedY));
				fixed fluxay = tex2D(_FresnelFluxay, uv2).r;

				fixed3 normalDir = i.normalDir;
				fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);

				fixed3 reflectDir = normalize(reflect(-viewDir, normalDir));
				fixed RdotV = max(0.0, dot(reflectDir, viewDir)) * specMask;
				float specularIntensity = pow(RdotV, _SpecularExponent) * _SpecularScale;
				fixed3 specularLighting = _SpecularColor.rgb * specularIntensity;

				//rim lighting.
				fixed rim = max(0, dot(normalDir, viewDir));
				float fresnelStrength = pow((1.0 - rim), _FresnelExponent) * _FresnelScale;
				fixed threshold = saturate(fresnelStrength) * rimMask;
				fixed3 rimLighting = _FresnelColor.rgb;
				//fixed3 rimLighting = fresnelStrength * _FresnelColor.rgb * rimMask;

				float fluxayFresnelStrength = fresnelStrength + fluxay;
				fixed3 fluxayRimLighting = fluxayFresnelStrength * _FluxayFresnelColor.rgb;

				finalColor.rgb = albedo.rgb + specularLighting.rgb;
				finalColor.rgb = lerp(finalColor.rgb, rimLighting.rgb, threshold);
				finalColor.rgb *= _MainCol.rgb;

				fixed3 fluxayColor = albedo.rgb;
				//fluxayColor.rgb *= _FluxayColor.rgb;
				fluxayColor.rgb += fluxayRimLighting.rgb;
				fluxayColor.rgb *= _FluxayColor.rgb;

				finalColor.rgb = lerp(fluxayColor.rgb, finalColor.rgb, dissolveArea);

				return finalColor;
			}

			ENDCG
		}
		Pass
		{
			Tags
			{
				"RenderType" = "Opaque"
			}
			Stencil
			{
				Ref 1
				Comp NotEqual
				Pass Keep
				//Fail keep
				//ZFail keep
			}
			Name "Outline"
			ZTest Off
			Cull Off

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			#pragma fragmentoption ARB_precision_hint_fastest
			//#pragma multi_compile_shadowcaster

			uniform half _OutlineWidth;
			uniform fixed4 _OutlineColor;
			uniform fixed _OutlineFactor;
			uniform float _OutlineExponent;

			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
			};
			struct VertexOutput
			{
				float4 pos : SV_POSITION;
			};
			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;

				float4 worldPos = mul(unity_ObjectToWorld,v.vertex);
				float3 dir1 = normalize(v.vertex.xyz);
				float3 dir2 = normalize(v.normal);
				float3 dir = lerp(dir1, dir2, _OutlineFactor);
				float3 offset = dir * _OutlineWidth;
				offset.xyz = v.vertex.xyz + offset.xyz;
				o.pos = UnityObjectToClipPos(float4(offset,1));
				return o;
			}
			float4 frag(VertexOutput i) : COLOR
			{
				fixed4 finalColor = fixed4(0, 0, 0, 1);

				finalColor.rgb = _OutlineColor.rgb;

				return finalColor;
			}
			ENDCG
		}
	}
}
