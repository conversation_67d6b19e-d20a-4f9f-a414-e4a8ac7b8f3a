Shader "Mt/Effect/Tree_Dissolve"
{
    Properties
    {
        _MainColor("MainRGB", 2D)= "white" {} 
        _DissolveTex("Dissolve Texture", 2D) = "white" {}
        _Dissolve("Dissolve Value", Range(-1,1)) = 0 
        [HDR]_CeilColorA("Edge First Color", Color) = (1,1,1,1)
        [HDR]_CeilColorB("Edge Second Color", Color) = (1,1,1,1)
        _Mask("Edge Range",Range(0.0,1.0)) = 0.2
        _Alpha("Alpha",Range(0.0,1.0)) = 1
        
    }

    SubShader
    {
        //Dissolve(Soft) Outline   
        Tags
        {
            "Queue"="Transparent" 
            "RenderType"="Transparent"
        }
		Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
        Lighting Off 
        
        Pass
        { 
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag 

            #include "UnityCG.cginc" 
            struct appdata
            {
                float4 vertex : POSITION; 
                float2 uv : TEXCOORD0; 
                
            }; 
            struct v2f
            {
                float4 vertex : SV_POSITION; 
                float2 uv : TEXCOORD0;  
            };
            sampler2D _MainColor;
            float4 _MainColor_ST;

            
            sampler2D _DissolveTex;
            float4 _DissolveTex_ST;
            half _Dissolve; 
            half _Mask,_Alpha;
            fixed4 _CeilColorA;
            fixed4 _CeilColorB; 

            v2f vert(appdata v)
            {
                v2f o; 
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv.xy = TRANSFORM_TEX(v.uv,_MainColor); 
                UNITY_TRANSFER_FOG(o,o.vertex);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {  
                half2 uv = i.uv.xy; 
                fixed4 col = tex2D(_MainColor,uv);
                
                fixed ClipTex = tex2D(_DissolveTex, uv).r;
                fixed ClipValue = ClipTex + ( _Dissolve  * (-1.2)) + 0.1; 
                float degree = saturate(ClipValue / _Mask);
                fixed4 edgeColor = lerp(_CeilColorA, _CeilColorB, degree); 
                
                col = lerp(edgeColor , col, degree);  
                col = fixed4(col.rgb,degree*_Alpha);  
                return  col;
            }
            ENDCG
        }
        

    }



}