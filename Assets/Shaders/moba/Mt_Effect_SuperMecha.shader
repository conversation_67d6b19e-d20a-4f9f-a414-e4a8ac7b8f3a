Shader "Mt/Effect/SuperMecha"
{
	Properties
	{
		[HDR]_Color ("Tint Color", Color) = (0.5,0.5,0.5,0.5)
		_MainTex ("Particle Texture", 2D) = "white" {}
		[ToggleOff]_ScreenSpaceUV("Screen Space UV", float) = 0.0
		//_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		[ToggleOff]_SpeedToggle("Speed Toggle",float) = 0.0
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		//_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	Category
	{
		Tags
		{
			"Queue"="Transparent"
			"IgnoreProjector"="True"
			"RenderType"="Transparent"
		}
		Blend SrcAlpha One,Zero One
		Cull Off
		Lighting Off
		//ZWrite Off
		SubShader
		{
			Pass
			{
				Fog{Mode Off}
				COLORMASK 0
			}
			Pass
			{
				CGPROGRAM
				#pragma vertex effectVertexBase
				#pragma fragment frag
				#pragma fragmentoption ARB_precision_hint_fastest
				#include "UnityCG.cginc"
				#include "ACGameCG.cginc"

				uniform float _FresnelExponent;
				uniform fixed4 _FresnelColor;
				uniform float _FresnelScale;
				
				struct effect_in_full
				{
					float4 vertex : POSITION;
					float4 texcoord : TEXCOORD0;
					float4 control : TEXCOORD1;
					float3 normal : NORMAL;
					half4 color : COLOR;
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				struct effect_v2f_custom
				{
					float4 vertex : SV_POSITION;
					half4 color : COLOR;
					half2 uv : TEXCOORD0;
					float4 control : TEXCOORD1;
					fixed3 normalDir : TEXCOORD3;
					float4 posWorld : TEXCOORD4;
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				effect_v2f_custom effectVertexBase(effect_in_full v)
				{
					effect_v2f_custom o = (effect_v2f_custom)0;

					UNITY_SETUP_INSTANCE_ID(v);
					UNITY_TRANSFER_INSTANCE_ID(v,o);

					o.vertex = UnityObjectToClipPos(v.vertex);
					half2 uv1 = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
					o.normalDir = UnityObjectToWorldNormal(v.normal);
					o.posWorld = mul(unity_ObjectToWorld, v.vertex);;
					o.uv.xy = uv1;
					o.color.rgba = v.color.rgba;
					o.control.xyzw = v.control.xyzw;
					return o;
				}

				fixed4 frag(effect_v2f_custom i) : SV_Target
				{
					half2 uv1 = i.uv.xy;
					half2 uv2 = GetNdcPos(i.vertex);
					uv2 = TRANSFORM_TEX(uv2, _MainTex);
					half2 uv = lerp(uv1, uv2, _ScreenSpaceUV);
					uv = GetAnimationUV(uv.xy,i.control.xy,half2(_SpeedX,_SpeedY));
					fixed4 color = tex2D (_MainTex, uv);

					fixed3 normalDir = i.normalDir;
					fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
					fixed rim = max(0, dot(normalDir, viewDir));
					float fresnelStrength = pow((1.0 - rim), _FresnelExponent) * _FresnelScale;
					fixed threshold = saturate(fresnelStrength);

					color.rgb = 1;// color.rgb* (_Color.rgb + _FresnelColor.rgb * threshold)* i.color.rgb * 2;
					color.a = saturate((color.a + threshold) * i.color.a * _Color.a);
					//color.a = threshold;
					return color;
				}
				ENDCG
			}
		}
	}
}
