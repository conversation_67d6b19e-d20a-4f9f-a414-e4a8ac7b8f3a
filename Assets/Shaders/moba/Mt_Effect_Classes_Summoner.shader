Shader "Mt/Effect/Classed/Summoner" 
{
    Properties 
	{
		_MainTex("Color", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)
		_Alpha("Alpha", Range(0,1)) = 1

		_DissolveTex("Dissolve Texture", 2D) = "white" {}
		_Dissolve("Dissolve Value", Range(0,1)) = 0

		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 8
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 1.867199

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)
		
		// ��Ч������
		[HideInInspector]_MainTexUVRotation("��Ч������: MainTex UV��תֵ(x:sin��, y:cos��)(��Ҫ�޸�!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("��Ч������: MainTex UV�Ƿ�������ĵ�����(��Ҫ�޸�!)", Float) = 0.0
    }
    SubShader 
	{
        Tags 
		{
            "RenderType"="Transparent"
			"Queue"="Transparent"
        }
        Pass 
		{  
            Name "FORWARD"
            Tags 
			{
                "LightMode"="ForwardBase"
            }
			Blend SrcAlpha OneMinusSrcAlpha,Zero OneMinusSrcAlpha
            
            CGPROGRAM
			#include "UnityCG.cginc"
			#pragma vertex vert
			#pragma fragment fragTransparent

			struct VertexInput
			{
				float4 vertex : POSITION;
				float2 texcoord : TEXCOORD0;
				float3 normal : NORMAL;
			};
			struct VertexOutput
			{
				float4 pos : SV_POSITION;
				half2 uv : TEXCOORD0;
				fixed3 normalDir : TEXCOORD1;
				float4 posWorld : TEXCOORD2;
			};

			uniform sampler2D _MainTex;
			uniform fixed4 _MainCol;
			uniform fixed _Alpha;
			uniform float _FresnelExponent;
			uniform fixed4 _FresnelColor;
			uniform float _FresnelScale;
			uniform sampler2D _DissolveTex;
			uniform fixed _Dissolve;

			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;
				o.uv.xy = v.texcoord.xy;
				o.normalDir = UnityObjectToWorldNormal(v.normal);
				o.posWorld = mul(unity_ObjectToWorld, v.vertex);
				o.pos = UnityObjectToClipPos(v.vertex);
				return o;
			}

			float4 fragTransparent(VertexOutput i) : COLOR
			{
				fixed4 finalColor = fixed4(0,0,0,1);
				fixed4 source1 = tex2D(_MainTex, i.uv.xy);

				fixed dissolve = tex2D(_DissolveTex, i.uv.xy);
				fixed threshold = saturate((1-_Dissolve) * 2 - 1 + dissolve);

				fixed3 albedo = source1.rgb;
				fixed alpha = source1.a;

				fixed3 normalDir = i.normalDir;
				fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);

				//rim lighting.
				fixed rim = max(0, dot(normalDir, viewDir));
				float fresnelStrength = pow((1.0 - rim), _FresnelExponent) * _FresnelScale;
				fixed3 rimLighting = fresnelStrength * _FresnelColor.rgb;

				finalColor.rgb = albedo.rgb;
				finalColor.rgb += rimLighting.rgb;
				finalColor.rgb *= _MainCol.rgb;

				finalColor.a = _Alpha * saturate(alpha + fresnelStrength);
				finalColor.a *= _MainCol.a;

				finalColor.a *= threshold;

				return finalColor;
			}

			ENDCG
		}
    }
}
