Shader "LOD/Mt/Effect/Alpha Blend UVAnimation Base_ETC1"
{
	Properties
	{
		[HDR]_Color ("Tint Color", Color) = (1,1,1,1)
		_MainTex ("Particle Texture(RGB)", 2D) = "white" {}
		_MainAlphaTex ("Particle Texture(Alpha)", 2D) = "white" {}
		[ToggleOff]_ScreenSpaceUV("Screen Space UV", float) = 0.0
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		_SpeedToggle("Speed Toggle",float) = 0.0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
		//_GradientValue("不要修改！Effect Fade Value",Float) = 1
		[Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 4

		[Toggle]_SoftVFX("Soft Particle",Int) = 0.0
		_InvFade("Soft Particles Factor", Range(0.01,20.0)) = 1.0

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}

	Category
	{
		Tags
		{
			"Queue"="Transparent"
			"IgnoreProjector"="True"
			"RenderType"="Transparent"
		}
		Blend SrcAlpha OneMinusSrcAlpha,Zero OneMinusSrcAlpha
		Cull Off
		Lighting Off
		ZWrite Off
		ZTest [_ZTestMode]
		SubShader
		{
			CGINCLUDE
			#pragma multi_compile _ _OFFSCREEN_RENDER
			#include "UnityCG.cginc"
			#include "ACGameCG.cginc"
			ENDCG
			Pass
			{
				CGPROGRAM
				#pragma vertex effectVertexBaseUVAni
				#pragma fragment frag
				#pragma fragmentoption ARB_precision_hint_fastest
				uniform float _InvFade;

				sampler2D _MainAlphaTex;

				fixed4 frag (effect_v2f_base_uvani i) : SV_Target
				{
#ifdef _SOFTVFX_ON
					float sceneZ = GetSceneDepth(i.vertex);
					float partZZ = i.vertex.z;
					float diff = sceneZ - partZZ;
					float fade = _InvFade * diff;
					i.color.a *= saturate(fade);
#endif

					half2 uv = i.uv.xy;
					fixed4 color = tex2D (_MainTex, uv);
					color.rgb = color.rgb * _Color.rgb * i.color.rgb ;

					fixed alphaEtc = tex2D(_MainAlphaTex, uv ) ;
					color.a = saturate(alphaEtc * i.color.a  * _Color.a);
					
					OffScreenLateZ(color.a, i.vertex);
					return color;
				}
				ENDCG
			}
		}

	}
}
