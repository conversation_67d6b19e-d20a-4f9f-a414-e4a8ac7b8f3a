Shader "Mt/Effect/Alpha Test UVAnimation Base"
{
    Properties
    {
        [HDR]_Color ("Tint Color", Color) = (1,1,1,1)
		_MainTex ("Particle Texture", 2D) = "white" {}
		[ToggleOff]_ScreenSpaceUV("Screen Space UV", float) = 0.0
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		_SpeedToggle("Speed Toggle",float) = 0.0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
		//_GradientValue("不要修改！Effect Fade Value",Float) = 1

		_Cutoff("Alpha cutout", Range(0, 1)) = 0.01

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
    }
    SubShader
    {
        Tags {  
			"RenderType" = "TransparentCutout"
			"Queue" = "AlphaTest"
			//"DisableBatching"="True"
			"IgnoreProjector" = "True" 
		}

        Pass
        {
			Blend SrcAlpha OneMinusSrcAlpha,Zero OneMinusSrcAlpha
			Cull Off
			Lighting Off

			// 遮挡描边的特效, 参考值5
			Stencil
			{
				Ref 5
				Comp Always
				Pass Replace
			}

            CGPROGRAM
			#pragma shader_feature _OFFSCREEN_RENDER
			#pragma vertex effectVertexBaseUVAni
			#pragma fragment frag
			#pragma fragmentoption ARB_precision_hint_fastest

			#include "UnityCG.cginc"
			#include "ACGameCG.cginc"

            fixed4 frag(effect_v2f_base_uvani i) : SV_Target
			{
				half2 uv = i.uv.xy;
				fixed4 color = tex2D (_MainTex, uv);
				color.rgb = color.rgb * _Color.rgb * i.color.rgb ;
				color.a = saturate(color.a * i.color.a  * _Color.a);
				OffScreenLateZ(color.a, i.vertex);
				clip(color.a - _Cutoff);
				return color;
			}

            ENDCG
        }
    }
}
