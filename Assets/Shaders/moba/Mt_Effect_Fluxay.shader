Shader "Mt/Effect/Fluxay"
{
	Properties
	{
		[Toggle] _GPU_SKINNING("GPU Skinning", float) = 0
		[Toggle]_Void("Is Void?",float) = 0
		_VoidTexture("Void Flowing Texture",2D) = "black"{}
		[HDR]_VoidColor("Void Color",Color) = (1.0,1.0,1.0,1.0)
		_VoidScreenSpeedX("Void Screen Speed X",Range(-5,5)) = 0
		_VoidScreenSpeedY("Void Screen Speed Y",Range(-5,5)) = 0
		[HDR]_VoidFlowingColor("Void Flowing Color",Color) = (0.0,0.0,0.0,0.0)

		_MainTex("Color", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularTex("Flowing Mask", 2D) = "black" {}
		_ScreenSpeedX("Screen Speed X",Range(-5,5)) = 0
		_ScreenSpeedY("Screen Speed Y",Range(-5,5)) = 0
		[HDR]_FlowingColor("Flowing Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		_SpeedU("Speed X",Range(-10,10)) = 0
		_SpeedV("Speed Y",Range(-10,10)) = 0
		_FluxayColor("Fluxay Color", Color) = (1.0,1.0,1.0,1.0)
		_FresnelFluxay("Fresnel Fluxay", 2D) = "black" {}
		[HDR]_FluxayFresnelColor("Fluxay Fresnel Color", Color) = (1.0,1.0,1.0,1.0)

		[Toggle]_CorrectMatcap("Use Matcap",float) = 0
		//_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelFromValue("Fresnel From Value",Range(0,1)) = 0
		_FresnelToValue("Fresnel To Value",Range(0,1)) = 1
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		_DissolveTex("Dissolve Texture", 2D) = "white" {}
		_Dissolve ("Dissolve Value", Range(0,1)) = 0

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	SubShader
	{
		Tags
		{
			"RenderType" = "Opaque"
		}
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}

			// 参考值1作为描边Mask
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
			}

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragOpaqueFluxay
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON
			#pragma shader_feature _GPU_SKINNING_ON


			ENDCG
		}
		/*Pass
		{
			Name "HeroFakeShadow"
			Stencil
			{
				Ref 0
				Comp equal
				Pass incrWrap
				Fail keep
				ZFail keep
			}
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
			ZWrite off

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert_fakeshadow
			#pragma fragment frag_fakeshadowOpaque

			ENDCG
		}*/
	}
}
