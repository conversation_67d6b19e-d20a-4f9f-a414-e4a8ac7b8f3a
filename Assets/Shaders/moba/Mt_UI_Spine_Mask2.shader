Shader "Mt/UI/Spine_Mask2"
{
    Properties
    {
        _Cutoff ("Shadow alpha cutoff", Range(0,1)) = 0.1
	    _MaskTex ("Mask Texture", 2D) = "black" {}
    	_UVOffsetX("Horizontal Offset",Range(-1,1)) = 0.0
    	_UVOffsetY("Vertical Offset",Range(-1,1)) = 0.0
	    [Toggle(_Reverse)]_Reverse("Enable Reverse Flow",float)=1
    	
    	
    	_StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 1
        _StencilOp ("Stencil Operation", Float) = 2
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 0
    	
    	 

       
    }
    SubShader
    {
        Tags 
		{
			"Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
		}
        
		Fog { Mode Off }
        Cull Off
        ZWrite Off
		ZTest [unity_GUIZTestMode]
        Blend SrcAlpha One,SrcColor One
        ColorMask [_ColorMask]
    	
    	
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }
        
      
    	
    	
        Pass
        {
        	
        	
        	
            CGPROGRAM
			
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
            #include "UnityUI.cginc"
			sampler2D _MaskTex;

            float _UVOffsetX;
            float _UVOffsetY;
			float _Reverse;

            bool _UseClipRect;
            float4 _ClipRect;

            
            #pragma multi_compile __ UNITY_UI_CLIP_RECT
            
			struct VertexInput {
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
				float4 vertexColor : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct VertexOutput {
				float4 pos : SV_POSITION;
				float2 uv : TEXCOORD0;
				float4 vertexColor : COLOR;
				float4 worldPosition : TEXCOORD1;
				UNITY_VERTEX_OUTPUT_STEREO
			};

			VertexOutput vert (VertexInput v) {
				VertexOutput o;
				UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                o.worldPosition = v.vertex;
				o.pos = UnityObjectToClipPos(v.vertex);
				o.uv = v.uv;
				o.vertexColor = v.vertexColor;
				return o;
			}

			float4 frag (VertexOutput i) : SV_Target {
				
				float4 mask = tex2D(_MaskTex,i.uv + float2(_UVOffsetX,_UVOffsetY));
				mask.a = lerp(mask.r,1-mask.r,_Reverse);

				#ifdef UNITY_UI_CLIP_RECT
					color.a *= UnityGet2DClipping(i.worldPosition.xy, _ClipRect);
				#endif

				
				float4 color = mask *  i.vertexColor  ;
				
				clip(color.a - 0.001);
				
				return color;
			}
			ENDCG
        }
    }
}
