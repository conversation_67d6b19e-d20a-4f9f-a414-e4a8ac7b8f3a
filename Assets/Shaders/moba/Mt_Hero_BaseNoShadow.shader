Shader "Mt/Hero/Base(NoShadow)" 
{
    Properties 
	{
		_MainTex("Color", 2D) = "white" {}
		_MainCol ("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		//_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)
    }
	SubShader
	{
		Tags
		{
			"RenderType" = "Opaque"
		}
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
				//Fail keep
				//ZFail Invert
			}


			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragOpaque
			
			ENDCG
		}
		
	}
}
