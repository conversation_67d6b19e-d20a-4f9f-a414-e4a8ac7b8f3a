Shader "Mt/Effect/Stencil/Base_BackGround"
{
    Properties
    {
        _MainTex ("贴图", 2D) = "white" {} 
        
        [Header(Stencil)]
        [IntRange] _Stencil ("Stencil ID", Range(0,255)) = 2
        [Enum(UnityEngine.Rendering.CompareFunction)] _StencilComp ("Stencil Comparison", Float) = 3
        [Enum(UnityEngine.Rendering.StencilOp)] _StencilPass ("Stencil Pass", Float) = 0
        [Enum(Off, 0, On, 1)]_ZWriteMode ("ZWriteMode", float) = 1 
        [Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 4

    }

    
    SubShader
    {
        Tags { "RenderType" = "Transparent" "Queue" = "Transparent" }
        
        Pass
        {
            Stencil
            { 
                Ref [_Stencil]
                Comp [_StencilComp]
                Pass[_StencilPass] 
            }
            ZWrite[_ZWriteMode]
            ZTest[_ZTestMode]
            
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc" 

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                
            };

            sampler2D _MainTex;
            float4 _MainTex_ST; 

            fixed _TransparentPower;
            

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex); 
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            { 
                
                // sample the texture
                fixed4 col = tex2D(_MainTex, i.uv) ; 
                
                return col ;
            }
            ENDCG
        }
        
    }
}
