Shader "Mt/Effect/AutoSequenceFrame"
{
	Properties
	{
		_MainTex("Particle Texture", 2D) = "white" {}
		[HDR]_MainColor("Main Color", Color) = (1, 1, 1, 1) 
		
		_FlowTex("Flow Texture", 2D) = "white" {}
		
		[HDR]_FlowColor("Flow Color", Color) = (1, 1, 1, 1) 
		
		_NoiseTex("Mask Texture", 2D) = "white" {}
		
		_SequenceFrameSpeed("SequenceFrameSpeed", Range(1, 100)) = 30
		
		_SequenceFrame("HVCount(xy)FlowHVSpeed(zw)", Vector )=(1,1,1,1)
		
		_UVRotate("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
		
		
		// 特效裁切用
		[Enum(UnityEngine.Rendering.CullMode)] _Cull ("Cull Mode", Float) = 0

		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	Category
	{
		Tags
		{
			"Queue" = "Transparent"
			"IgnoreProjector" = "True"
			"RenderType" = "Transparent"
		}
		Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
		Cull [_Cull]
		Lighting Off
		ZWrite Off
		SubShader
		{

			CGINCLUDE
			#include "UnityCG.cginc"
			#include "ACGameCG.cginc"
			ENDCG
			Pass
			{
				CGPROGRAM
				#pragma vertex effectVertexBase
				#pragma fragment frag
				#pragma fragmentoption ARB_precision_hint_fastest
				
				half _SequenceFrameSpeed;
				
				sampler2D _FlowTex;

				sampler2D _NoiseTex;
				half4 _NoiseTex_ST;
				half4 _FlowColor ;
				half4 _MainColor ;
				half4 _FlowTex_ST;
				half4 _SequenceFrame;

				

				fixed4 frag(effect_v2f_base i) : SV_Target
				{
					
					fixed4 flowCol = tex2D(_FlowTex, TRANSFORM_TEX(float2 (i.uv.x + _SequenceFrame.z*frac(_Time.x),i.uv.y+ _SequenceFrame.w*frac(_Time.x)), _FlowTex));
					
					fixed mask =tex2D(_NoiseTex,TRANSFORM_TEX(i.uv,_NoiseTex));
					
					float time = floor(frac(_Time.y)* _SequenceFrameSpeed);
					float horizontal = floor(time / _SequenceFrame.x);
					float vertical = time - horizontal * _SequenceFrame.x;

					half2 uv = float2((i.uv.x  + vertical )/ _SequenceFrame.x,( i.uv.y  -horizontal )/ _SequenceFrame.y);
					

					fixed4 col = tex2D(_MainTex, uv)*_MainColor;
					flowCol.rgb = lerp(col.rgb ,flowCol.rgb*_FlowColor.rgb,mask);	
					col.rgb = flowCol.rgb*col.rgb ;
					return col;
				}
				ENDCG
			}
		}
	}
}
