Shader "Mt/UI/Alpha Blend UVAnimation Dissolve"
{
	Properties
	{
		_MainTex ("Main Texture", 2D) = "white" {}
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		[HDR]_Color("Main Color", Color) = (1.0,1.0,1.0,1.0)
		_DissolveTex("Dissolve Texture", 2D) = "white" {}
		_Dissolve("Dissolve Value", Range(-1,1)) = 0
		_DissolveToggle("Dissolve Toggle(0 : Value; 1 : Custom)",float) = 0.0
		_SpeedToggle("Speed Toggle",float) = 0.0
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		_SpeedX2("Noise Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY2("Noise Vertical Speed",Range(-5.0, 5.0)) = 0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
		//_GradientValue("不要修改！Effect Fade Value",Float) = 1

		[Toggle]_UIEffect("UI Effect",float) = 0.0
		_FadeRange("Fade Range", Vector) = (0.1, 0.1, 0.1, 0.1)

        _StencilComp ("Stencil Comp", Float) = 8
        _Stencil ("Stencil Ref", Float) = 0
        _StencilOp ("Stencil Pass", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
        _ColorMask ("Color Mask", Float) = 15
        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
		_XMin("X Min",float) = -1
		_XMax("X Max",float) = 1
		_YMin("Y Min",float) = -1
		_YMax("Y Max",float) = 1
	}
	SubShader
	{
		Tags
		{
			"RenderType"="Transparent"
            "IgnoreProjector"="True"
			"Queue" = "Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
		}
		LOD 100
		Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
		Cull Off
		Lighting Off
		ZWrite Off

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        ZTest [unity_GUIZTestMode]
        ColorMask [_ColorMask]
		Pass
		{
			CGPROGRAM
			#pragma vertex effectVertexDiss
			#pragma fragment frag
			#pragma shader_feature _UIEFFECT_ON
			#include "UnityCG.cginc"
			#include "UnityUI.cginc"
			#include "ACGameCG.cginc"
			fixed4 frag (effect_v2f_diss i) : SV_Target
			{
				fixed4 color = fixed4(0,0,0,1);
				half2 uv = i.uv.xy;
				half2 uv2 = i.uv1.xy;
				fixed4 main_var = tex2D(_MainTex, uv);
				fixed4 diss_var = tex2D(_DissolveTex,uv2);
				half dissValue = lerp(_Dissolve,i.uv.z,_DissolveToggle) * (-1.2) + 0.1;//-0.1 ~ 1.1
				float ClipAmount = diss_var.r + dissValue;
				color = main_var;
				color.rgb = color.rgb * i.color.rgb * _Color.rgb;
				color.a = saturate(color.a * i.color.a * _Color.a * step(0,ClipAmount));
				#ifdef UNITY_UI_CLIP_RECT
				color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
				#endif

				#ifdef UNITY_UI_ALPHACLIP
				clip (color.a - 0.001);
				#endif

				#ifdef _UIEFFECT_ON
				float2 ndcPos = GetNdcPos(i.vertex);
				float xRange = clamp((ndcPos.x - _XMin) / _FadeRange.x, 0, 1) * clamp((_XMax - ndcPos.x) / _FadeRange.y, 0, 1);
				float yRange = clamp((ndcPos.y - _YMin) / _FadeRange.z, 0, 1) * clamp((_YMax - ndcPos.y) / _FadeRange.w, 0, 1);
				float alphaFactor = xRange * yRange;
				color.a = color.a * alphaFactor;
				#endif
				return color;
			}
			ENDCG
		}
	}
}
