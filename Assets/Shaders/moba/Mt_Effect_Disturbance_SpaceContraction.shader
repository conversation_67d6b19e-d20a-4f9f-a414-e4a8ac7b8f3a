Shader "Mt/Effect/Disturbance/SpaceContraction"
{
    Properties
    {
        _NoiseTex ("Noise Texture", 2D) = "white" {}
		_DistortScale("Distort Scale",float) = 1
		_DistortFactor("Distort Factor", Range(0,1)) = 0.0
		_DistortCenter("Distort Center", Vector) = (0.0,0.0,0.0,0.0)
    }
    SubShader
    {
        Tags 
		{ 
			"RenderType"="Transparent" 
			"Queue" = "Transparent+500"
			"IgnoreProjector" = "True"
		}
        LOD 1000
		GrabPass
		{
			"_EffectGrabTexture3"
		}
        Pass
        {
			ZTest Always
			Cull Off
			ZWrite Off
			Fog
			{
				Mode off
			}

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			#pragma fragmentoption ARB_precision_hint_fastest 

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
				fixed4 color : COLOR;
            };

            struct v2f
            {
                half2 uv : TEXCOORD0;
				float4 centralPoint : TEXCOORD1;
                float4 pos : SV_POSITION;
				float4 clipPos : TEXCOORD2;
				fixed4 color : TEXCOORD3;
            };

			uniform sampler2D _EffectGrabTexture3;
            uniform sampler2D _NoiseTex;
            uniform float4 _NoiseTex_ST;
			uniform float _DistortScale;
			uniform float _DistortFactor;
			uniform float4 _DistortCenter;


            v2f vert (appdata v)
            {
                v2f o = (v2f)0;
                o.pos = UnityObjectToClipPos(v.vertex);
				o.clipPos = o.pos;
                o.uv = TRANSFORM_TEX(v.uv, _NoiseTex);
				o.centralPoint = UnityObjectToClipPos(_DistortCenter);
				o.color = v.color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				fixed noise = tex2D(_NoiseTex,i.uv).r;
				float2 dir = i.clipPos.xy - i.centralPoint.xy;
				float2 offset = normalize(dir) * _DistortFactor * i.color.a * _DistortScale;
				i.clipPos.xy += offset * noise;
				float4 uvGrab = ComputeGrabScreenPos(i.clipPos);
				fixed4 col = tex2Dproj(_EffectGrabTexture3, uvGrab);
                return col;
            }
            ENDCG
        }
    }
	FallBack Off
}
