// by arts<PERSON><PERSON>r "Hidden/Alpha Blend"
{
    Properties
    {
        _Color ("Main Color", Color) = (1, 1, 1, 1)
        _Brightness ("Brightness", Float) = 1.0
        _MainTex ("Base (RGB)", 2D) = "white" {}
        _MaskTex ("Mask Texture (R)", 2D) = "white" {}
        _UVParam ("UV Param", Vector) = (0, 0, 1, 1)
        _UVTile ("UV Tile", Vector) = (0, 0, 1, 1)
        _Rotate ("UV Rotate", Range (0, 360.0)) = 0.0
        _MaskUVParam ("Mask UV Param", Vector) = (0, 0, 1, 1)
        _MaskRotate ("Mask UV Rotate", Range (0, 360.0)) = 0.0
        [Toggle] _Flow ("_Flow", Float) = 0
        [HideInInspector] _FlowUVParam ("_Flow UV Param", Vector) = (0, 0, 0, 1)
        _FlowTex ("Flow Texture (RG)", 2D) = "black" {}
        _FlowRotate ("FlowRotate", Range (0, 360.0)) = 0.0
        [KeywordEnum(Off,On)] _SeparateAlpha("_Separate Alpha(Do not edit)", Float) = 0
        _AlphaCtrl("Alpha control ***Do not edit***", Float) = 1
        [HideInInspector] _RotateSin("", Float) = 0
        [HideInInspector] _RotateCos("", Float) = 1
        [HideInInspector] _MaskRotateSin("", Float) = 0
        [HideInInspector] _MaskRotateCos("", Float) = 1
        [HideInInspector] _FlowRotateSin("", Float) = 0
        [HideInInspector] _FlowRotateCos("", Float) = 1

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0

		[HideInInspector]_MaskTexUVRotation("特效裁切用: MaskTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MaskTexUVScaleOnCenter("特效裁切用: MaskTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
    }
    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
        }
        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha,Zero OneMinusSrcAlpha
            Cull Off
            Lighting Off
            Fog { Mode Off }
            ZWrite Off
//	        ZTest Off
            Name "Effect"
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest // Only takes effect in ogl, has no effect in vk, mt, dx... etc
 
            #pragma multi_compile __ MASK_ON
            #pragma multi_compile __ OPTIMIZE_ON
            //#pragma multi_compile __ _SCALE_ON
            #pragma multi_compile __ _FLOW_ON
            #pragma multi_compile_instancing

            #pragma target 2.0
            #include "EffectShader1 Common.cginc"

            fixed4 frag(VS_OUTPUT In) : COLOR
            {
            #ifdef _FLOW_ON
            half4 flow = tex2D(_FlowTex, In.flowUV);
            flow += _FlowUVParam.w;
            fixed4 color = tex2D(_MainTex, In.uv.xy + flow.rg *_FlowUVParam.z);
            #else
            fixed4 color = tex2D(_MainTex, In.uv.xy);
            #endif

                color *= In.color;
            #ifdef MASK_ON
                color.a *= tex2D(_MaskTex, In.uv.zw).r;
            #endif
                return color;
            }

            ENDCG
        }
    }
    Fallback off
    CustomEditor "EffectMaterialEd"
}
