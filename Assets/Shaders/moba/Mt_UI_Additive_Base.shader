// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "Mt/UI/Additive Base"
{
    Properties
    {
        /*[PerRendererData]*/ _MainTex ("Sprite Texture", 2D) = "white" {}
        [HDR] _Color ("Tint", Color) = (1,1,1,1)

        _StencilComp ("Stencil Comp", Float) = 8
        _Stencil ("Stencil Ref", Float) = 0
        _StencilOp ("Stencil Pass", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
        _SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
        _SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
        _ColorMask ("Color Mask", Float) = 15

        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0

		[Toggle]_UIEffect("UI Effect",float) = 0.0
		_FadeRange("Fade Range", Vector) = (0.1, 0.1, 0.1, 0.1)

		_XMin("X Min",float) = -1
		_XMax("X Max",float) = 1
		_YMin("Y Min",float) = -1
		_YMax("Y Max",float) = 1
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha One,Zero One
        ColorMask [_ColorMask]

        Pass
        {
            Name "Default"
			CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            #include "UnityCG.cginc"
            #include "UnityUI.cginc"
			#include "ACGameCG.cginc"

            #pragma multi_compile_local _ UNITY_UI_CLIP_RECT
            #pragma multi_compile_local _ UNITY_UI_ALPHACLIP
			#pragma shader_feature _UIEFFECT_ON

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float4 worldPosition : TEXCOORD1;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            fixed4 _TextureSampleAdd;
            float4 _ClipRect;

            v2f vert(appdata_t v)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);
                OUT.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);

                OUT.color = v.color * _Color;
                return OUT;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                half2 uv = IN.texcoord;
                uv.x += frac(_SpeedX * _Time.y);
                uv.y += frac(_SpeedY * _Time.y);

                half4 color = (tex2D(_MainTex, uv) + _TextureSampleAdd);
                color.rgb = color.rgb * _Color.rgb * IN.color.rgb * 2;

                #ifdef UNITY_UI_CLIP_RECT
                color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                #endif

                #ifdef UNITY_UI_ALPHACLIP
                clip (color.a - 0.001);
                #endif

#ifdef _UIEFFECT_ON
                float2 ndcPos = GetNdcPos(IN.vertex);
				float xRange = clamp((ndcPos.x - _XMin) / _FadeRange.x, 0, 1) * clamp((_XMax - ndcPos.x) / _FadeRange.y, 0, 1);
				float yRange = clamp((ndcPos.y - _YMin) / _FadeRange.z, 0, 1) * clamp((_YMax - ndcPos.y) / _FadeRange.w, 0, 1);
				float alphaFactor = xRange * yRange;
				color.a = color.a * alphaFactor;
#endif

                return color;
            }
        ENDCG
        }
    }
}
