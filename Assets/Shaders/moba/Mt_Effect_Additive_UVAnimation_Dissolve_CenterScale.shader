Shader "Mt/Effect/Additive UVAnimation Dissolve(CenterScale)"
{
	Properties
	{
		_MainTex ("Main Texture", 2D) = "white" {} 
		_CenterScale("CenterScale",float) = 1.0
		_ScaleToggle("ScaleToggle(0 : Value; 1 : Custom)",float) = 0.0
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		[HDR]_Color("Main Color", Color) = (1.0,1.0,1.0,1.0)
		_DissolveTex("Dissolve Texture", 2D) = "white" {}
		_Dissolve("Dissolve Value", Range(-1,1)) = 0
		_DissolveToggle("Dissolve Toggle(0 : Value; 1 : Custom)",float) = 0.0
		_SpeedToggle("Speed Toggle",float) = 0.0
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		_SpeedX2("Noise Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY2("Noise Vertical Speed",Range(-5.0, 5.0)) = 0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0
		//_GradientValue("不要修改！Effect Fade Value",Float) = 1
		[Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 4

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	SubShader
	{
		Tags
		{
			"RenderType"="Transparent"
			"Queue" = "Transparent"
		}
		LOD 100
		Blend SrcAlpha One,Zero One
		Cull Off
		Lighting Off
		ZWrite Off
		ZTest [_ZTestMode]
		Pass
		{
			CGPROGRAM
			#pragma shader_feature _OFFSCREEN_RENDER
			#pragma vertex effectVertexDiss
			#pragma fragment frag
			#include "UnityCG.cginc"
			#include "ACGameCG.cginc"
			float _CenterScale,_ScaleToggle;
			
			fixed4 frag (effect_v2f_diss i) : SV_Target
			{
				fixed4 color = fixed4(0,0,0,1);
				half2 uv = (i.uv.xy-0.5)*lerp(_CenterScale,i.uv.w,_ScaleToggle);
				uv +=0.5;
				half2 uv2 = i.uv1.xy;
				fixed4 main_var = tex2D(_MainTex, uv);
				fixed4 diss_var = tex2D(_DissolveTex,uv2);
				half dissValue = lerp(_Dissolve,i.uv.z,_DissolveToggle) * (-1.2) + 0.1;//-0.1 ~ 1.1
				float ClipAmount = diss_var.r + dissValue;
				color = main_var;
				color.rgb = color.rgb * i.color.rgb * _Color.rgb * 2;
				color.a = saturate(color.a * i.color.a * _Color.a * step(0,ClipAmount));
				
				OffScreenLateZ(color.a, i.vertex);
				return color;
			}
			ENDCG
		}
	}
	FallBack "Mt/Effect/Additive UVAnimation Base"
}
