Shader "Mt/Hero/Base(Aquaticdragon)" 
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
		_MainCol("Main Color", Color) = (0.25,0.25,0.25,1.0)


		_CloudTex ("效果贴图(MaskTex)", 2D) = "black" {}
        _Speed("移动速度", Float) = 1
        _MoveAxis("移动 x , y 方向", vector) =(1,0,0,0)


		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)

		[Space(30)][Toggle]_Gem("Is Gem?",float) = 0
		[NoScaleOffset]_Matcap_Tex("Reflection MatCap", 2D) = "black" {}
		_MatcapCol("Matcap Color", Color) = (1.0,1.0,1.0,1.0)

		[Space(30)][Toggle]_FLOWINGMASK("Is Flowing?",float) = 0
		_FlowingTex("Flowing Texture",2D) = "black" {}
		_FlowingColor("Flowing Color",Color) = (0.0,0.0,0.0,0.0)
		_FlowingSpeedU("Flowing Speed U",Range(-5,5)) = 0
		_FlowingSpeedV("Flowing Speed V",Range(-5,5)) = 0
	}
	SubShader
	{
		LOD 150
		Tags
		{
			"RenderType" = "Opaque"
			"Target" = "Hero"
			"Queue" = "Geometry+200"
		}
		Pass
		{
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
		//Fail keep
		//ZFail Invert
	}

	CGPROGRAM
	#pragma vertex vert
	#pragma fragment frag
	#include "UnityCG.cginc"
	#include "ColorBleeding.cginc"
	#pragma shader_feature _GEM_ON
	#pragma shader_feature _FLOWINGMASK_ON

            half2 _MoveAxis;
            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _CloudTex;
            float4 _CloudTex_ST;
            float _Speed;

	sampler2D _Matcap_Tex;
	uniform fixed4 _MainCol;
	uniform fixed4 _MatcapCol;
#ifdef _FLOWINGMASK_ON
			uniform sampler2D _FlowingTex;
			uniform float _FlowingSpeedU;
			uniform float _FlowingSpeedV;
			uniform fixed4 _FlowingColor;
#endif

			uniform float _SpecularExponent;
			uniform fixed4 _SpecularColor;
			uniform float _SpecularScale;

			uniform float4 _SideLightDirection;
			uniform fixed4 _SideLightColor;

			uniform float _SpeedFresnel;
			uniform fixed _FXFresnel;
			uniform float _FresnelExponent;
			uniform fixed4 _FresnelColor;
			uniform float _FresnelScale;
			uniform fixed _SinAmplitude;

			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float2 texcoord : TEXCOORD0;
				float2 texcoord1 : TEXCOORD1;
			};

			struct VertexOutput
			{
				float4 pos : SV_POSITION;

				float2 texcoord : TEXCOORD0;
                float2 texcoord1 : TEXCOORD1;

				half3 normalDir : NORMAL;
				half2 uv : TEXCOORD2;
				float3 posWorld : TEXCOORD3;

			};

			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;

				o.pos = UnityObjectToClipPos(v.vertex);
                o.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
                o.texcoord1 = TRANSFORM_TEX(v.texcoord, _CloudTex);
                half offset1 = frac(_Time.x * _Speed) *_CloudTex_ST.x;
                _MoveAxis = ceil(_MoveAxis);
                o.texcoord1.xy += offset1*_MoveAxis.xy;

				o.uv.xy = v.texcoord.xy;
				o.normalDir = UnityObjectToWorldNormal(v.normal);
				o.posWorld = mul(unity_ObjectToWorld, v.vertex).xyz;
				//float4 worldLightDir = mul(unity_ObjectToWorld, _SideLightDirection);
				//o.lightDir = normalize(worldLightDir.xyz);
				o.pos = UnityObjectToClipPos(v.vertex);

#if _GEM_ON
				o.matcap_uv.x = dot(normalize(UNITY_MATRIX_IT_MV[0].xyz),normalize(v.normal));
				o.matcap_uv.y = dot(normalize(UNITY_MATRIX_IT_MV[1].xyz),normalize(v.normal));
#endif

				return o;
			}

			half4 frag(VertexOutput i) : COLOR
			{
				fixed4 col = tex2D(_MainTex, i.texcoord);
                fixed4 ccol = tex2D(_CloudTex, i.texcoord1);
                half cloudMask = ccol.a;

                col.rgb = lerp(col.rgb, ccol.rgb, cloudMask);

				fixed4 finalColor = fixed4(0,0,0,1);
				fixed4 source1 = tex2D(_MainTex, i.uv.xy);
				fixed3 albedo = source1.rgb;

#ifdef _FLOWINGMASK_ON
				fixed flowingMask = tex2D(_FlowingTex, i.uv.xy).r;//the red channel is mask area.
				half2 flowingUV = half2(0, 0);
				flowingUV.x = i.uv.x + frac(_FlowingSpeedU * _Time.y);
				flowingUV.y = i.uv.y + frac(_FlowingSpeedV * _Time.y);
				fixed flowing = tex2D(_FlowingTex, flowingUV.xy).g;
				fixed3 flowing_var = flowing.rrr * _FlowingColor.rgb * flowingMask.rrr;
				albedo.rgb = albedo.rgb + flowing_var.rgb;
#endif

				fixed specMask = source1.a;
				fixed sinTime = (sin(_Time.y*_SpeedFresnel)*_SinAmplitude + 2 - _SinAmplitude) * 0.5;
				fixed specMaskStep = step(0.01, specMask);
				fixed rimMask = specMaskStep * lerp(sinTime, 1.0, _FXFresnel);

				fixed3 normalDir = normalize(i.normalDir);
				fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);

				fixed3 reflectDir = normalize(reflect(-viewDir, normalDir));
				fixed RdotV = max(0.0, dot(reflectDir, viewDir)) * specMask;
				float specularIntensity = pow(RdotV, _SpecularExponent) * _SpecularScale;
				fixed3 specularLighting = _SpecularColor.rgb * specularIntensity;


				//rim lighting.
				fixed rim = max(0, dot(normalDir, viewDir));
				float fresnelStrength = pow((1.0 - rim), _FresnelExponent) * _FresnelScale;
				fixed threshold = saturate(fresnelStrength);
				threshold = threshold * saturate(rimMask + _FXFresnel);
				fixed3 rimLighting = _FresnelColor.rgb;

				//fixed3 lightDir = i.lightDir;
				fixed3 lightDir = normalize(_SideLightDirection.xyz);//viewDir
				normalDir = mul(UNITY_MATRIX_V,normalDir);
				fixed ndotl = max(dot(normalDir, lightDir),0);
				fixed3 sideLightColor = ndotl.xxx * _SideLightColor.rgb;

				finalColor.rgb = albedo.rgb + specularLighting.rgb + sideLightColor.rgb;
				finalColor.rgb = lerp(finalColor.rgb, rimLighting.rgb, threshold);
				finalColor.rgb *= _MainCol.rgb;

				#if _GEM_ON
					half3 Reflection = tex2D(_Matcap_Tex, i.matcap_uv);
					finalColor.rgb += Reflection * _MatcapCol.rgb;
				#endif
				float3 positionWS = i.posWorld.xyz;
				OUTPUT_FINALCOLOR(finalColor)
				ADDITIONAL_LIGHTS(finalColor.rgb, albedo, normalDir, positionWS)
				// 描边Mask为0
				finalColor.a = 0;

				finalColor.rgb = finalColor.rgb + col.rgb;

				return finalColor;
                //return col;

			}
			ENDCG
		}
	}
	CustomEditor "DefaultShaderGUI"
}
