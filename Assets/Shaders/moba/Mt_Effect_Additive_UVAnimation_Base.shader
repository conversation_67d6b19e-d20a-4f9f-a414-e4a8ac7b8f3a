Shader "Mt/Effect/Additive UVAnimation Base"
{
	Properties
	{
		// 【ID863885247】局外升星
		_dark("Dark", Range(0.0, 1.0)) = 0

		[HDR]_Color ("Tint Color", Color) = (0.5,0.5,0.5,0.5)
		_MainTex ("Particle Texture", 2D) = "white" {}
		[ToggleOff]_ScreenSpaceUV("Screen Space UV", float) = 0.0
		_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		_SpeedX("Horizontal Speed", Range(-5.0, 5.0)) = 0
		_SpeedY("Vertical Speed",Range(-5.0, 5.0)) = 0
		[ToggleOff]_SpeedToggle("Speed Toggle",float) = 0.0
		_NormalFactor("Z Depth Offset",Range(-1.0,1.0)) = 0

		[Toggle]_SoftVFX("Soft Particle",Int) = 0.0
		_InvFade("Soft Particles Factor", Range(0.01,20.0)) = 1.0

		//_GradientValue("不要修改！Effect Fade Value",Float) = 1
		[Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 4

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	Category
	{
		Tags
		{
			"Queue"="Transparent"
			"IgnoreProjector"="True"
			"RenderType"="Transparent"
		}
		//Stencil
		//{
		//	Ref 13
		//	Comp NotEqual
		//	Pass Replace
		////Fail keep
		////ZFail Invert
		//}
		Blend SrcAlpha One,Zero One
		Cull Off
		Lighting Off
		ZWrite Off
		ZTest [_ZTestMode]
		SubShader
		{
			Pass
			{
				CGPROGRAM
				#pragma shader_feature _OFFSCREEN_RENDER
				#include "UnityCG.cginc"
				#include "ACGameCG.cginc"
				#pragma multi_compile _ _SOFTVFX_ON
				#pragma multi_compile __ _TK_INCLUDE_NORMAL
				#pragma vertex effectVertexBaseUVAni
				#pragma fragment frag
				uniform half _dark;
				uniform float _InvFade;

				fixed4 frag (effect_v2f_base_uvani i) : SV_Target
				{
#ifdef _SOFTVFX_ON
					float sceneZ = GetSceneDepth(i.vertex);
					float partZZ = i.vertex.z;
					float diff = sceneZ - partZZ;
					float fade = _InvFade * diff;
					i.color.a *= saturate(fade);
#endif
					fixed4 color = tex2D (_MainTex, i.uv);
					color = color * _Color * i.color;
					color.rgb = color.rgb * 2;
					color.rgb = color.rgb - color.rgb * _dark;
					color.a = saturate(color.a);
					
					OffScreenLateZ(color.a, i.vertex);

					return color;
				}
				ENDCG
			}
		}
	}
	//CustomEditor "ACGameEffectBaseGUI"
	//FallBack "Legacy Shaders/Transparent/Diffuse"
}
