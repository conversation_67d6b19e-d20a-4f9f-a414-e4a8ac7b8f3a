Shader "Mt/Hero/SettShadow(Stencil)" 
{
	Properties 
	{
		[Toggle]_Void("Is Void?",float) = 0
		_VoidTexture("Void Flowing Texture",2D) = "black"{}
		[HDR]_VoidColor("Void Color",Color) = (1.0,1.0,1.0,1.0)
		_VoidScreenSpeedX("Void Screen Speed X",Range(-5,5)) = 0
		_VoidScreenSpeedY("Void Screen Speed Y",Range(-5,5)) = 0
		[HDR]_VoidFlowingColor("Void Flowing Color",Color) = (0.0,0.0,0.0,0.0)

		[Toggle]_CutoutAlpha("Cutout Alpha",float) = 0 
		_MainTex("Texture", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularTex("Specular&Flowing Mask", 2D) = "black" {}
		_ScreenSpeedX("Screen Speed X",Range(-5,5)) = 0
		_ScreenSpeedY("Screen Speed Y",Range(-5,5)) = 0
		[HDR]_FlowingColor("Flowing Color", Color) = (1.0,1.0,1.0,1.0)

		_Alpha("Alpha", Range(0,1)) = 1

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		[Toggle]_CorrectMatcap("Use Matcap",float) = 0
		//_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelFromValue("Fresnel From Value",Range(0,1)) = 0
		_FresnelToValue("Fresnel To Value",Range(0,1)) = 1
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0
		
		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)
		
		[Header(Shadow)] 
		//倒影部分
		_AvatarTransform("XYZ:Avatar Offset,W:Scale",Vector) = (0,0,0,1)
		_AvatarScale("XYZ:Avatar Scale,W:Scale",Vector) = (0,0,0,1) 
		_ShadowCol("Shadow Color", Color) = (1.0,1.0,1.0,1.0) 
		_FresnelFromValueShadow("Fresnel From Value(Shadow)",Range(0,1)) = 0 
		_FresnelExponentShadow("Fresnel Exponent(Shadow)", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColorShadow("Fresnel Color(Shadow)", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScaleShadow("Fresnel Scale(Shadow)", Range(0.1, 10)) = 5.0 

		_TransFinish("TransFinish",float) = 1
		
		[Space(10)]
		[Header(Stencil)]
		[IntRange] _Stencil ("Stencil ID", Range(0,255)) = 2
		[Enum(UnityEngine.Rendering.CompareFunction)] _StencilComp ("Stencil Comparison", Float) = 3
		[Enum(UnityEngine.Rendering.StencilOp)] _StencilPass ("Stencil Pass", Float) = 2
		[Enum(Off, 0, On, 1)]_ZWriteMode ("ZWriteMode", float) = 0 
		[Enum(UnityEngine.Rendering.CompareFunction)]_ZTestMode ("ZTestMode", Float) = 0
	}
	SubShader
	{
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}
			Blend SrcAlpha OneMinusSrcAlpha,Zero OneMinusSrcAlpha
			
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
			}

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragTransparentShadow
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON
			#pragma shader_feature _CUTOUTALPHA_ON
			#pragma multi_compile __ _GPU_SKINNING_ON
			fixed _TransFinish;
			half4 fragTransparentShadow(VertexOutput i) : COLOR
			{
				fixed4 finalColor = fixed4(0,0,0,1);
				fixed4 source1 = tex2D(_MainTex, i.uv.xy);
				fixed3 albedo = source1.rgb;
				fixed alpha = source1.a;
				fixed specMask = source1.a;;
				fixed sinTime = (sin(_Time.y*_SpeedFresnel)*_SinAmplitude + 2 - _SinAmplitude) * 0.5;
				fixed specMaskStep = step(0.01, specMask);
				fixed rimMask = specMaskStep *lerp(sinTime, 1.0, _FXFresnel);

				fixed3 normalDir = i.normalDir;
				fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);

				fixed3 reflectDir = normalize(reflect(-viewDir, normalDir));
				fixed RdotV = max(0.0, dot(reflectDir, viewDir)) * specMask;
				float specularIntensity = pow(RdotV, _SpecularExponent) * _SpecularScale;
				fixed3 specularLighting = _SpecularColor.rgb * specularIntensity;

				//rim intensity.
				half2 uv2 = i.pos.xy * _ScreenParams.zw - i.pos.xy;		//screenPos
				uv2 = TRANSFORM_TEX(uv2, _SpecularTex);
				half2 rimUV = uv2 + frac(_Time.y * half2(_ScreenSpeedX, _ScreenSpeedY));
				fixed rimIntensity = tex2D(_SpecularTex, rimUV.xy).b;

				//rim lighting.
				fixed rim = max(0, dot(normalDir, viewDir));
				fixed rimK = _FresnelToValue - _FresnelFromValue;
				float fresnelStrength = pow((1.0 - rim)*rimK, _FresnelExponent) * _FresnelScale + _FresnelFromValue;
				fixed threshold = saturate(fresnelStrength);
				threshold = threshold * saturate(rimMask + _FXFresnel);
				fixed3 rimLighting = _FresnelColor.rgb;

				#ifdef _CORRECTMATCAP_ON
					//camera up vector
					float3 upVector = mul(UNITY_MATRIX_I_V, float4(0, 1, 0, 0)).xyz;
					//reconstruct view matrix
					float3 matrixV_ZAxis = -viewDir;
					float3 matrixV_XAxis = normalize(cross(upVector, matrixV_ZAxis));
					float3 matrixV_YAxis = normalize(cross(matrixV_ZAxis, matrixV_XAxis));
					float3x3 reMatrixV = float3x3(matrixV_XAxis.x, matrixV_YAxis.x, matrixV_ZAxis.x,
					matrixV_XAxis.y, matrixV_YAxis.y, matrixV_ZAxis.y,
					matrixV_XAxis.z, matrixV_YAxis.z, matrixV_ZAxis.z);
					half2 matCapUV = mul(normalDir, reMatrixV).xy *0.5 + 0.5;
					fixed sideLight = tex2D(_SpecularTex, matCapUV).g;
					fixed3 sideLightColor = sideLight * _SideLightColor.rgb;
				#else
					//fixed3 lightDir = i.lightDir;
					//fixed3 lightDir = normalize(_SideLightDirection.xyz);//viewDir
					//normalDir = normalize(mul(UNITY_MATRIX_V,normalDir));
					//fixed ndotl = max(dot(normalDir, lightDir),0);
					//fixed3 sideLightColor = ndotl.xxx * _SideLightColor.rgb;
					fixed3 sideLightColor = fixed3(0, 0, 0);
				#endif

				finalColor.rgb = albedo.rgb + specularLighting.rgb + sideLightColor.rgb;
				//finalColor.rgb = lerp(finalColor.rgb, rimLighting.rgb, threshold);
				finalColor.rgb = finalColor.rgb + rimLighting * threshold;
				finalColor.rgb = finalColor.rgb + _FlowingColor.rgb * rimIntensity *0.5*specMaskStep;
				finalColor.rgb *= _MainCol.rgb;

				finalColor.a = _Alpha * saturate(alpha + fresnelStrength);
				// 必须保证英雄的alpha都是小于1的, 以作为描边的mask
				finalColor.a *= 0.96;

				#ifdef _VOID_ON
					half2 voidUV = TRANSFORM_TEX(i.uv.xy, _VoidTexture);
					voidUV = voidUV + frac(_Time.y*half2(_VoidScreenSpeedX, _VoidScreenSpeedY));
					fixed voidIntensity = tex2D(_VoidTexture, voidUV).r;
					fixed3 voidFlowing = voidIntensity * _VoidFlowingColor.rgb;
					finalColor.rgb = lerp(finalColor.rgb* _VoidColor.rgb + voidFlowing.rgb, finalColor.rgb, specMaskStep);
				#endif

				float3 positionWS = i.posWorld.xyz;
				finalColor.a *= _MainCol.a;

				#ifdef _CUTOUTALPHA_ON
					clip(finalColor.a - 0.01);
				#endif
				finalColor.a = lerp(finalColor.a,_MainCol.a,_TransFinish);

				OUTPUT_FINALCOLOR(finalColor)
				ADDITIONAL_LIGHTS(finalColor.rgb, albedo, normalDir, positionWS)
				
				return finalColor;
			}

			ENDCG
		}
		Pass
		{ 
			Tags
			{
				"RenderType" = "Opaque"
				"Queue" = "Opaque+100"
				"Target" = "Hero"
			}
			
			Stencil
			{
				Ref [_Stencil]
				Comp [_StencilComp]
				Pass [_StencilPass]
			} 
			//Blend SrcAlpha OneMinusSrcAlpha
			ZWrite [_ZWriteMode]
			ZTest [_ZTestMode]
			Cull Front
			ColorMask RGB
			CGPROGRAM
			#pragma vertex vertMul
			#pragma fragment fragMul
			#include "FKHEROCGINCLUDE.cginc"

			uniform float4 _AvatarScale,_AvatarTransform,_ShadowCol;  

			uniform fixed _FresnelFromValueShadow;
			uniform fixed _FresnelToValueShadow; 
			uniform float _FresnelExponentShadow;
			uniform fixed4 _FresnelColorShadow;
			uniform float _FresnelScaleShadow;
			//uniform float _AvatarExtent;
			float4x4 MatrixScale(float3 offsetxyz,float3 scalexyz)
			{
				return float4x4(scalexyz.x,0,0,offsetxyz.x,
				0,scalexyz.y,0,offsetxyz.y,
				0,0,scalexyz.z,offsetxyz.z,
				0,0,0,1);
			}
			VertexOutput vertMul(VertexInput v)
			{
				VertexOutput o; 
				o.uv.xy = v.texcoord.xy;
				o.normalDir = UnityObjectToWorldNormal(v.normal.xyz);
				o.posWorld = ObjectToWorldFast(v.vertex); 
				float4 vertex = mul(MatrixScale(_AvatarTransform.xyz,_AvatarScale.xyz),v.vertex);
				
				//vertex.xyz = o.posWorld;
				o.pos = UnityObjectToClipPos(vertex);
				return o;
			} 
			
			float4 fragMul(VertexOutput i) : COLOR
			{ 
				fixed4 finalColor = fixed4(0,0,0,1);
				fixed4 source1 = tex2D(_MainTex, i.uv.xy); 
				fixed3 albedo = source1.rgb;
				fixed3 normalDir = i.normalDir;
				fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz); 
				//rim lighting.
				fixed rim = max(0.01, dot(normalDir, viewDir));  
				float fresnelStrength = pow(rim, _FresnelExponentShadow) * _FresnelScaleShadow + _FresnelFromValueShadow; 
				fixed3 rimLighting = _FresnelColorShadow.rgb;

				finalColor.rgb = albedo.rgb;  
				finalColor.rgb *= _ShadowCol.rgb; 
				finalColor.rgb += fresnelStrength*rimLighting;
				
				OUTPUT_FINALCOLOR(finalColor)
				
				return finalColor;
			} 
			ENDCG
		}
		
	}
	
}
