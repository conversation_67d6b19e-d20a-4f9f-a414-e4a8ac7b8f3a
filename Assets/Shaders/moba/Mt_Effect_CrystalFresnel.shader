Shader "Mt/Effect/CrystalFresnel"
{
	Properties
	{
		_MainTex ("Main Texture", 2D) = "white" {}
		//_UVRotate ("UV Rotate(1:90 2:180 3:270)",Range(0,4)) = 0
		[HDR]_Color ("Main Color", Color) = (1,1,1,1)
		_DissolveTex ("Dissolve Texture", 2D) = "white" {}
		_Dissolve ("Dissolve Value", Range (-2, 2)) = 0
		//_DissolveToggle("Dissolve Toggle(0 : Value; 1 : Custom)",float) = 0.0
		_MaskTex ("Mask Texture", 2D) = "white" {}
		_Mask ("Mask Value",Range(-1,1)) = 0
		//_MaskToggle ("Mask Toggle(0 : Value; 1 : Custom)",float) = 0.0
		_SpeedX("U Speed",Range(-5,5)) = 0
		_SpeedY("V Speed",Range(-5,5)) = 0
		//_SpeedX2("Dissolve U Speed",Range(-5,5)) = 0
		//_SpeedY2("Dissolve V Spedd",Range(-5,5)) = 0
		//_SpeedToggle("Speed Toggle",float) = 0.0
		//[Toggle]_SamplerToggle("Sampler Toggle",Int) = 0.0

		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5.0
		//_GradientValue("不要修改！Effect Fade Value",Float) = 1

		// 特效裁切用
		[HideInInspector]_MainTexUVRotation("特效裁切用: MainTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MainTexUVScaleOnCenter("特效裁切用: MainTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0

		[HideInInspector]_MaskTexUVRotation("特效裁切用: MaskTex UV旋转值(x:sinθ, y:cosθ)(不要修改!)", Vector) = (0, 1, 0, 0)
		[HideInInspector]_MaskTexUVScaleOnCenter("特效裁切用: MaskTex UV是否基于中心点缩放(不要修改!)", Float) = 0.0
	}
	Category
	{
		Tags
		{
			"Queue"="Transparent"
			"IgnoreProjector"="True"
			"RenderType"="Transparent"
		}
		Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
		SubShader
		{
			Pass
			{
				CGPROGRAM
				#pragma shader_feature _OFFSCREEN_RENDER
				#pragma vertex vert
				#pragma fragment frag
				#pragma fragmentoption ARB_precision_hint_fastest
				//#pragma shader_feature _SAMPLERTOGGLE_ON
				#include "UnityCG.cginc"
				#include "ACGameCG.cginc"

				uniform float _FresnelExponent;	
				uniform fixed4 _FresnelColor;
				uniform float _FresnelScale;

				struct appdata
				{
					float4 vertex : POSITION;
					float4 texcoord : TEXCOORD0;
					float4 control : TEXCOORD1;
					fixed4 color : COLOR;
					float3 normal : NORMAL;
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				struct v2f
				{
					float4 vertex : SV_POSITION;
					fixed4 color : COLOR;
					float4 uv : TEXCOORD0;//xy:uv;  zw:dissolve or mask
					float4 control : TEXCOORD1;//xy:main's uv curve;  zw:dissolve's or mask's uv curve
					half4 uv1 : TEXCOORD2;
					fixed3 normalDir : TEXCOORD3;
					float4 posWorld : TEXCOORD4;
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				v2f vert(appdata v)
				{
					v2f o = (v2f)0;
					o.vertex = UnityObjectToClipPos(v.vertex);

					o.uv.xy = TRANSFORM_TEX(v.texcoord.xy, _MainTex);
					//o.uv.xy = FlipUV(o.uv.xy);
					o.uv.zw = v.texcoord.zw;
					o.uv1.xy = TRANSFORM_TEX(v.texcoord.xy, _DissolveTex);
					o.uv1.zw = TRANSFORM_TEX(v.texcoord.xy, _MaskTex);
					o.color.rgba = v.color.rgba;
					o.control.xyzw = v.control.xyzw;

					o.normalDir = UnityObjectToWorldNormal(v.normal);
					o.posWorld = mul(unity_ObjectToWorld, v.vertex);
					o.uv.xy = GetAnimationUV(o.uv.xy,o.control.xy,half2(_SpeedX,_SpeedY));
					return o;
				}

				fixed4 frag (v2f i) : SV_Target
				{
					fixed4 color = fixed4(0,0,0,1);
					half2 uv = i.uv.xy;

					fixed clipValue = tex2D(_DissolveTex, i.uv1.xy).r;
					fixed mask = tex2D(_MaskTex, i.uv1.zw).r;

					half dissValue = _Dissolve * (-1.2) + 0.1;//-0.1 ~ 1.1
					float ClipAmount = clipValue + dissValue;
					color = tex2D(_MainTex, uv);
					color.rgb = color.rgb * 2.0 * _Color.rgb * i.color.rgb;
					
					fixed3 normalDir = i.normalDir;
					fixed3 viewDir = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
					fixed rim = max(0, dot(normalDir, viewDir));
					float fresnelStrength = pow((1.0 - rim), _FresnelExponent) * _FresnelScale;
					fixed3 rimLighting = _FresnelColor.rgb * fresnelStrength;

					color.rgb += rimLighting.rgb;

					color.a = color.a * saturate( fresnelStrength) * i.color.a * saturate(mask - _Mask) * _Color.a * step(0, ClipAmount);
					color.a = saturate(color.a);
					OffScreenLateZ(color.a, i.vertex);
					return color;
				}
				ENDCG
			}
		}
		/*SubShader
		{
			Pass
			{
				SetTexture [_MainTex]
				{
					constantColor [_TintColor]
					combine constant * primary
				}
				SetTexture [_MainTex]
				{
					combine texture * previous DOUBLE
				}
			}
		}
		SubShader
		{
			Pass
			{
				SetTexture [_MainTex]
				{
					combine texture * primary
				}
			}
		}*/
	}
}
