Shader "Custom/Crystal.shader"
{
    Properties
    {
        _MainTex("Main Texture", 2D) = "white" {}
    }
    SubShader
    {
        Tags
        {
            "RenderType"="Transparent"
            "Queue"="Transparent"
            "IgnoreProjector"="True"
        }
        CGINCLUDE
        float3 faceNormalInViewSpace(float3 viewPos)
        {
            return normalize(cross(ddx(viewPos), ddy(viewPos)));
        }
        ENDCG

        Pass
        {
            ColorMask 0
        }
    }
}