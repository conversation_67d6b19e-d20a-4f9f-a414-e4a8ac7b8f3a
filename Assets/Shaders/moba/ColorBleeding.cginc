//不同场景对局内角色染色
uniform fixed3 _BleedingColor;

uniform uint   _PointLightsCount;
uniform float4 _PointLightPosition[4];
uniform half4  _PointLightAtten[4];
uniform fixed3  _PointLightColor[4];

/////////////////////////////////////////////////
// OUTPUT_FINALCOLOR /////////////////////////////////
/////////////////////////////////////////////////
#define OUTPUT_FINALCOLOR(color)  											        \
    color.rgb += _BleedingColor;												


/////////////////////////////////////////////////
// OUTPUT_ADDIYIONAL_LIGHT /////////////////////////////////
/////////////////////////////////////////////////
#define ADDITIONAL_LIGHTS(color, albedo, normalWS, positionWS)                                              \
        int pixelLightCount = _PointLightsCount;                                                            \
        for(int i = 0; i < pixelLightCount; ++i)                                                            \
        {                                                                                                   \
            float4 lightPositionWS = _PointLightPosition[i];                                                \
            half4 distanceAttenuation = _PointLightAtten[i];                                                \
            fixed3 lightColor = _PointLightColor[i];                                                        \
            float3 lightVector = lightPositionWS.xyz - positionWS;                                          \
            float distanceSqr = max(dot(lightVector, lightVector), 6.103515625e-5);                         \
            half3 lightDirection = half3(lightVector * rsqrt(distanceSqr));                                 \
            half factor = distanceSqr * distanceAttenuation.x;                                              \
            half smoothFactor = saturate(1.0h - factor * factor);                                           \
            half disAttenuation = smoothFactor * smoothFactor;                                              \
            color += albedo * lightColor * disAttenuation * saturate(dot(normalWS, lightDirection));        \
        }