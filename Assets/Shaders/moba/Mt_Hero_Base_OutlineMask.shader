Shader "Mt/Hero/Base(OutlineMaskVertexNoise)"
{
	Properties
	{
		[Toggle]_Void("Is Void?",float) = 0
		_VoidTexture("Void Flowing Texture",2D) = "black"{}
		[HDR]_VoidColor("Void Color",Color) = (1.0,1.0,1.0,1.0)
		_VoidScreenSpeedX("Void Screen Speed X",Range(-5,5)) = 0
		_VoidScreenSpeedY("Void Screen Speed Y",Range(-5,5)) = 0
		[HDR]_VoidFlowingColor("Void Flowing Color",Color) = (0.0,0.0,0.0,0.0)

		_MainTex("Texture", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularTex("Flowing Mask", 2D) = "black" {}
		_ScreenSpeedX("Screen Speed X",Range(-5,5)) = 0
		_ScreenSpeedY("Screen Speed Y",Range(-5,5)) = 0
		[HDR]_FlowingColor("Flowing Color", Color) = (1.0,1.0,1.0,1.0)

		_Alpha("Alpha", Range(0,1)) = 1

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		[Toggle]_CorrectMatcap("Use Matcap",float) = 0
		//_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelFromValue("Fresnel From Value",Range(0,1)) = 0
		_FresnelToValue("Fresnel To Value",Range(0,1)) = 1
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		// Noise properties
		[Toggle(_VERTEX_NOISE_ON)] _EnableVertexNoise("Enable VertexNoise?", Float) = 0
		_NoiseStrength("Noise Strength", Range(0.0, 1.0)) = 0.1
		_NoiseSpeed("Noise Speed", Range(-5, 50)) = 0
		_NoiseTex("Noise Texture", 2D) = "white" {}
		
		_OutlineWidth("Outline Width", Range(0.0,1.0)) = 0.2
		[HDR]_OutlineColor("Outline Color", Color) = (0.0,0.73,1.0,1.0)
		_OutlineFactor("Outline Factor", Range(0.0,1.0)) = 1
		_OutlineColorTex("OutlineColorTexture", 2D) = "white" {}
		_OutlineOffset("Outline Offset", vector) = (0,1,0,0) 

		_HighlightOffset("Highlight Offset", float) = 0

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)
	}
	
	SubShader
	{ 
		Tags
		{
			"Queue" = "Geometry+200"
			"RenderType" = "Opaque"
			"Target" = "Hero" 
		}
		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}
			Stencil
			{
				Ref 3
				Comp Always
				Pass Replace
			} 
			
			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragOpaque
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON
			#pragma multi_compile  __ _GPU_SKINNING_ON
			
			ENDCG
		}

		Pass
		{
			Stencil
			{
				Ref 3
				Comp NotEqual
				Pass Replace	// 防止描边部分再绘制平面阴影
				//Fail keep
				//ZFail keep
			}
			Name "Outline"
			Cull Off
			Blend SrcAlpha OneMinusSrcAlpha
			CGPROGRAM
			#pragma multi_compile  __ _GPU_SKINNING_ON
			#pragma shader_feature _VERTEX_NOISE_ON
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			#include "ColorBleeding.cginc"
			#pragma fragmentoption ARB_precision_hint_fastest
			//#pragma multi_compile_shadowcaster
			sampler2D _OutlineColorTex;
			sampler2D _NoiseTex; // 噪声纹理
			float4 _OutlineColorTex_ST, _OutlineOffset, _NoiseTex_ST;
			uniform half _OutlineWidth;
			uniform fixed4 _OutlineColor;
			uniform fixed _OutlineFactor;
			uniform half _NoiseStrength; // 噪声强度
			uniform half _NoiseSpeed; // 噪声动画速度 
			#ifdef _GPU_SKINNING_ON
				half4 _uvBoundData;
				half3 _posBoundMin;
				half3 _posBoundSize;
				half4 _GPUSkinMatrices[216];
				inline half4x4 GetMatrix(uint index)
				{
					return half4x4(_GPUSkinMatrices[index]
					, _GPUSkinMatrices[index + 1]
					, _GPUSkinMatrices[index + 2]
					, half4(0.0f, 0.0f, 0.0f, 1.0f));
				}
				struct VertexInput
				{
					half4 vertex		: POSITION;			//UNorm16	-> FP16
					half4 normal		: NORMAL;			//Float32	-> FP16
					//half2 texcoord		: TEXCOORD0;		//UNorm16	-> FP16
					
					uint4 skinIndices	: TEXCOORD6;
					half4 skinWeights	: TEXCOORD7;		//Color8	-> FP16
				};
			#else
				struct VertexInput
				{
					float4 vertex		: POSITION;			//Float32	-> FP32
					half3 normal		: NORMAL;			//Float32	-> FP16
					half2 texcoord		: TEXCOORD0;		//UVs for noise texture
				};
			#endif
			struct VertexOutput
			{
				float4 pos : SV_POSITION;
				half2 noiseUV : TEXCOORD0;
			};
			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;
				 
				
				
				#ifdef _GPU_SKINNING_ON
					half4x4 blendMatrix = GetMatrix(v.skinIndices.x) * v.skinWeights.x +
					GetMatrix(v.skinIndices.y) * v.skinWeights.y +
					GetMatrix(v.skinIndices.z) * v.skinWeights.z +
					GetMatrix(v.skinIndices.w) * v.skinWeights.w;
					v.vertex.xyz = mul(blendMatrix, half4(v.vertex.xyz , 1)).xyz;
					v.normal.xyz = normalize(mul((half3x3)blendMatrix, v.normal));
				#endif 
				float3 worldPos = mul(unity_ObjectToWorld, float4(v.vertex.xyz, 1)).xyz;
				float3 worldNormal = mul((float3x3)unity_ObjectToWorld, v.normal.xyz);
				 
				#ifdef _VERTEX_NOISE_ON
					o.noiseUV = TRANSFORM_TEX(v.texcoord, _NoiseTex) + frac(_Time.xx) * _NoiseSpeed ;
					half3 noise = tex2Dlod(_NoiseTex, float4(o.noiseUV, 0, 0)).rgb * 2.0 - 1.0;
					worldPos += noise * _NoiseStrength;
				#endif
				
				float3 dir1 = normalize(worldPos.xyz);
				float3 dir2 = normalize(worldNormal.xyz);
				float3 dir = lerp(dir1, dir2, _OutlineFactor);
				float3 offset = dir * _OutlineWidth;
				worldPos.xyz += offset+_OutlineOffset;
				o.pos = mul(UNITY_MATRIX_VP, float4(worldPos.xyz, 1));
				return o;
			}
			float4 frag(VertexOutput i) : COLOR
			{
				half2 uv = i.pos.xy * _ScreenParams.zw - i.pos.xy;		//screenPos
				uv = uv * _OutlineColorTex_ST.xy +_OutlineColorTex_ST.zw; 
		 
				fixed4 finalColor = fixed4(0, 0, 0, 1); 
				finalColor.rgb = tex2D(_OutlineColorTex,uv)*_OutlineColor.rgb; 
				OUTPUT_FINALCOLOR(finalColor)
				
				return finalColor;
			}
			ENDCG
		}
	}
}
