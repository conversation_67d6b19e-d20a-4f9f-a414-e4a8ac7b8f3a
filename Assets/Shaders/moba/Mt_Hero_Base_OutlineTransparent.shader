Shader "Mt/Hero/Base(OutlineTransparent)" 
{
    Properties 
	{
		[Toggle]_Void("Is Void?",float) = 0
		_VoidTexture("Void Flowing Texture",2D) = "black"{}
		[HDR]_VoidColor("Void Color",Color) = (1.0,1.0,1.0,1.0)
		_VoidScreenSpeedX("Void Screen Speed X",Range(-5,5)) = 0
		_VoidScreenSpeedY("Void Screen Speed Y",Range(-5,5)) = 0
		[HDR]_VoidFlowingColor("Void Flowing Color",Color) = (0.0,0.0,0.0,0.0)

		_MainTex("Color", 2D) = "white" {}
		_MainCol("Main Color", Color) = (1.0,1.0,1.0,1.0)

		_SpecularTex("Specular&Flowing Mask", 2D) = "black" {}
		_ScreenSpeedX("Screen Speed X",Range(-5,5)) = 0
		_ScreenSpeedY("Screen Speed Y",Range(-5,5)) = 0
		[HDR]_FlowingColor("Flowing Color", Color) = (1.0,1.0,1.0,1.0)

		_Alpha("Alpha", Range(0,1)) = 1

		_SpecularExponent("Specular Exponent", Range(0.001,16.0)) = 3.0
		_SpecularColor("Specular Color", Color) = (1.0,1.0,1.0,1.0)
		_SpecularScale("Specular Scale", Range(0.0,10.0)) = 8.2

		[Toggle]_CorrectMatcap("Use Matcap",float) = 0
		//_SideLightDirection("SideLight Direction",vector) = (1,0.3,-0.5,0.0)
		[HDR]_SideLightColor("Character SideLight Color",Color) = (.75,.75,.75,1.0)

		_SpeedFresnel("Fresnel Speed",float) = 1
		[ToggleOff]_FXFresnel("Is Effect Used",float) = 0
		_FresnelFromValue("Fresnel From Value",Range(0,1)) = 0
		_FresnelToValue("Fresnel To Value",Range(0,1)) = 1
		_FresnelExponent("Fresnel Exponent", Range(0.01, 8)) = 3.6
		[HDR]_FresnelColor("Fresnel Color", Color) = (0.08499137,0.3454357,0.9632353,1)
		_FresnelScale("Fresnel Scale", Range(0.1, 10)) = 5
		_SinAmplitude("Fresnel Amplitude", Range(0.0,1.0)) = 1.0

		_OutlineWidth("Outline Width", Range(0.0,1.0)) = 0.2
		[HDR]_OutlineColor("Outline Color", Color) = (0.0,0.73,1.0,1.0)
		_OutlineFactor("Outline Factor", Range(0.0,1.0)) = 1

		_HighlightOffset("Highlight Offset", float) = 0

		_ShadowPlane("Shadow Plane", vector) = (0,1,0,0)
    }

	SubShader 
	{
		// 极高设置下描边通过后处理实现
		LOD 900
		Tags 
		{
			"Queue" = "Transparent"
			"RenderType" = "Transparent"
			"Target" = "Hero"
		}

		/*Pass
		{
			Name "HeroFakeShadow"
			Stencil
			{
				Ref 0
				Comp equal
				Pass incrWrap
				Fail keep
				ZFail keep
			}
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
			ZWrite Off
			Offset -120, 0

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert_fakeshadow
			#pragma fragment frag_fakeshadowTransparent

			ENDCG
		}*/

		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}

			
		Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragTransparent
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON

			ENDCG
		}

		Pass
		{
			Name "HighlightMask"
			Stencil
			{
				Ref 3
				Comp Always
				Pass Replace
			}
			Blend Zero One	// 该Pass不显示模型, 只写模板
			Offset [_HighlightOffset], 0	// 防止售卖时描边被场景遮挡
			ZWrite Off

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragHide

			ENDCG
		}
    }
	SubShader
	{
		LOD 150
		Tags
		{
			"Queue" = "Transparent+100"
			"RenderType" = "Transparent"
			"Target" = "Hero"
			//"HideType" = "HeroTransparent150"
		}


		Pass
		{
			Name "FORWARD"
			Tags
			{
				"LightMode" = "ForwardBase"
			}
			Stencil
			{
				Ref 3
				Comp Always
				Pass Replace
			}
			
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragTransparent
			#pragma shader_feature _VOID_ON
			#pragma shader_feature _CORRECTMATCAP_ON

			ENDCG
		}

		Pass
		{
			Stencil
			{
				Ref 3
				Comp NotEqual
				Pass Keep
				//Fail keep
				//ZFail keep
			}
			Name "Outline"
			//Cull Off
			Cull Off

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			#include "ColorBleeding.cginc"
			#pragma fragmentoption ARB_precision_hint_fastest
			//#pragma multi_compile_shadowcaster

			uniform half _OutlineWidth;
			uniform fixed4 _OutlineColor;
			uniform fixed _OutlineFactor;
			//uniform float _OutlineExponent;

			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
			};
			struct VertexOutput
			{
				float4 pos : SV_POSITION;
			};
			VertexOutput vert(VertexInput v)
			{
				VertexOutput o = (VertexOutput)0;

				float3 dir1 = normalize(v.vertex.xyz);
				float3 dir2 = normalize(v.normal);
				float3 dir = lerp(dir1, dir2, _OutlineFactor);
				float3 offset = dir * _OutlineWidth;
				offset.xyz = v.vertex.xyz + offset.xyz;
				o.pos = UnityObjectToClipPos(float4(offset,1));
				return o;
			}
			float4 frag(VertexOutput i) : COLOR
			{
				fixed4 finalColor = fixed4(0, 0, 0, 1);

				finalColor.rgb = _OutlineColor.rgb;
				OUTPUT_FINALCOLOR(finalColor)
				return finalColor;
			}
			ENDCG
		}

		Pass
		{
			// 该Pass用于在选中时覆盖Alpha通道描边Mask, 以遮挡其他单位的描边
			ColorMask A
			ZWrite Off

			CGPROGRAM
			#include "FKHEROCGINCLUDE.cginc"
			#pragma vertex vert
			#pragma fragment fragHide

			ENDCG
		}
	}
}
