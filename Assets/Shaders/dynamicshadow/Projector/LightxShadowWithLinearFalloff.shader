Shader "DynamicShadowProjector/Projector/Light x Shadow With Linear Falloff" {
	Properties {
		_LightTex ("Light Cookie", 2D) = "gray" {}
		_ShadowTex ("Shadow Cookie", 2D) = "gray" {}
		_ClipScale ("Near Clip Sharpness", Float) = 100
		_Alpha ("Light Intensity", Range (0, 1)) = 1.0
		_Offset ("Offset", Range (-1, -10)) = -1.0
		_MaxHeight("Max Height", Float) = 1000	// _WorldPos.y高于_MaxHeight的位置不显示阴影
		_MinHeight("Min Height", Float) = -0.3	// _WorldPos.y低于_MinHeight的位置不显示阴影
	}
	Subshader {
		Tags {"Queue"="Transparent-1"}
		Pass {
			Name "PASS"
			ZWrite Off
			ColorMask RGB
			Blend DstColor One
			Offset -1, [_Offset]

			CGPROGRAM
			#pragma vertex DSPProjectorVertLightLinearFalloff
			#pragma fragment DSPProjectorFragLightWithShadow
			//#pragma multi_compile_fog
			#include "DSPProjector.cginc"
			ENDCG
		}
	}
}
