//Input And Output Layer
#ifndef OUTGAME_V2_FRAGINPUT_INCLUDE
#define OUTGAME_V2_FRAGINPUT_INCLUDE

#include "OutGameV2_VertexFactory.cginc"

// Struct define
typedef VertexOutput FragInput;
typedef VertexOutputPositionOnly FragInputPositionOnly;

void InitFragmentInput(inout FragInput input)
{
    input.Normal = normalize(input.Normal);
    input.UV0 = input.UV0;
#ifdef TANGENT_SPACE
    input.Tangent = normalize(input.Tangent);
    input.BiNormal = normalize(input.BiNormal);
#endif
}

// Fragment Attribute Getter
half3 GetVertexNormal(in FragInput input)
{
    return input.Normal;
}
half2 GetVertexUV0(in FragInput input)
{
    return input.UV0.xy;
}
#ifdef WORLD_POS
float3 GetWorldPos(in FragInput input)
{
    return input.WorldPos;
}
#endif
#ifdef LIGHTMAP_ON
half2 GetLightmapUV(in FragInput input)
{
    return input.UV0.zw;
}
#endif
#ifdef VERTEX_COLOR
half4 GetVertexColor(in FragInput input)
{
    return input.Color;
}
#endif
#ifdef TANGENT_SPACE
half3 GetVertexTangent(in FragInput input)
{
    return input.Tangent;
}
half3 GetVertexBiNormal(in FragInput input)
{
    return input.BiNormal;
}
#endif

// Varying Getter

#define VARYING_GETTER(i)                           \
v##i##_type GetVarying##i (in FragInput input)      \
{                                                   \
     return GetMappedVarying##i (input.pack);       \
}
#ifdef VARYING1
VARYING_GETTER(1)
#endif
#ifdef VARYING2
VARYING_GETTER(2)
#endif
#ifdef VARYING3
VARYING_GETTER(3)
#endif
#ifdef VARYING4
VARYING_GETTER(4)
#endif
#ifdef VARYING5
VARYING_GETTER(5)
#endif
#ifdef VARYING6
VARYING_GETTER(6)
#endif
#ifdef VARYING7
VARYING_GETTER(7)
#endif
#ifdef VARYING8
VARYING_GETTER(8)
#endif
#undef VARYING_GETTER

// BuiltIn Varying Getter
#ifdef WORLD_POS
float3 GetWorldPosition(in FragInput input)
{
    return input.WorldPos;
}
#endif
#ifdef VIEW_DIR
half3 GetViewDir(in FragInput input)
{
    EXPAND_GET_VARYINGPOS(VIEW_DIR_VERYINGPOS)
}
#endif

// BuiltIn Function
half2 GetScreenUV(in FragInput input)
{
    half2 screenPosition = input.Position.xy;
    half2 screenUV = screenPosition * _ScreenParams.zw - screenPosition;      //Lower  precision ~ 1e-5
    //half2 screenUV = screenPosition / _ScreenParams.xy;                     //Higher precision ~ 1e-7
    return screenUV;
}

half2 GetNdcPosition(in FragInput input)
{
    float2 screenUV = GetScreenUV(input);
    float2 reNdc = screenUV * 2 - 1;
#if UNITY_UV_STARTS_AT_TOP
    reNdc.y = -reNdc.y;
#endif
    return reNdc;
}
#endif