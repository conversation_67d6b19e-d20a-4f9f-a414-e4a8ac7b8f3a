// Input And Output Layer
#ifndef OUTGAME_V2_VERTEXFACTORY_INCLUDE
#define OUTGAME_V2_VERTEXFACTORY_INCLUDE

#include "OutGameV2_VaryingPack.cginc"

#define HELPER_TYPEDEF
#define TYPE_DIM VERTEX_UV2
#define TYPE_NAME vuv2_type
#include "OutGameV2_CGIncHelper.cginc"
#define TYPE_DIM VERTEX_UV3
#define TYPE_NAME vuv3_type
#include "OutGameV2_CGIncHelper.cginc"
#define TYPE_DIM VERTEX_UV4
#define TYPE_NAME vuv4_type
#include "OutGameV2_CGIncHelper.cginc"
#define TYPE_DIM VERTEX_UV5
#define TYPE_NAME vuv5_type
#include "OutGameV2_CGIncHelper.cginc"
#define TYPE_DIM VERTEX_UV6
#define TYPE_NAME vuv6_type
#include "OutGameV2_CGIncHelper.cginc"
#define TYPE_DIM VERTEX_UV7
#define TYPE_NAME vuv7_type
#undef HELPER_TYPEDEF

// Struct define
struct VertexInputPositionOnly
{
    float3 Position : POSITION;
};
struct VertexInputBase
{
    float3 Position : POSITION;
    half3 Normal : NORMAL;
#ifdef TANGENT_SPACE
    half4 Tangent : TANGENT;
#endif
// #ifdef VERTEX_COLOR
    half4 Color : COLOR;
// #endif
    half2 UV0 : TEXCOORD0;
#ifdef LIGHTMAP_ON
    half2 UV1 : TEXCOORD1;
#endif
#ifdef VERTEX_UV2
    vuv2_type UV2 : TEXCOORD2;
#endif
#ifdef VERTEX_UV3
    vuv3_type UV3 : TEXCOORD3;
#endif
#ifdef VERTEX_UV4
    vuv4_type UV4 : TEXCOORD4;
#endif
#ifdef VERTEX_UV5
    vuv5_type UV5 : TEXCOORD5;
#endif
#ifdef VERTEX_UV6
    vuv6_type UV6 : TEXCOORD6;
#endif
#ifdef VERTEX_UV7
    vuv7_type UV7 : TEXCOORD7;
#endif
};

struct VertexOutputPositionOnly 
{
    float4 Position : SV_Position;
};
struct VertexOutput
{
    float4 Position : SV_Position;
    half3 Normal : NORMAL;
#ifdef LIGHTMAP_ON
    half4 UV0 : TEXCOORD0;
#else
    half2 UV0 : TEXCOORD0;
#endif
#ifdef TANGENT_SPACE
    half3 Tangent : TANGENT;
    half3 BiNormal : BINORMAL;
#endif
#ifdef VERTEX_COLOR
    half4 Color : COLOR;
#endif
#ifdef WORLD_POS
    float3 WorldPos : TEXCOORD1;
#endif
    // SHADOW
    //UNITY_SHADOW_COORDS(7)
    VaryingPack pack;
};

// Vertex Attribute Getter
float3 GetVertexPosition(in VertexInputPositionOnly input)
{
    return input.Position;
}
float3 GetVertexPosition(in VertexInputBase input)
{
    return input.Position;
}
half3 GetVertexNormal(in VertexInputBase input)
{
    return input.Normal;
}
half2 GetVertexUV0(in VertexInputBase input)
{
    return input.UV0.xy;
}

#ifdef TANGENT_SPACE
half4 GetVertexTangent(in VertexInputBase input)
{
    return input.Tangent;
}
#endif
#ifdef VERTEX_COLOR
half4 GetVertexColor(in VertexInputBase input)
{
    return input.Color;
}
#endif
#ifdef LIGHTMAP_ON
half2 GetVertexLightmapUV(in VertexInputBase input)
{
    return input.UV1.xy;
}
#endif
#define VERTEX_UV_GETTER(i)                             \
vuv##i##_type GetVertexUV##i (in VertexInputBase input)     \
{                                                       \
    return input.UV##i;                                 \
}
#ifdef VERTEX_UV2
VERTEX_UV_GETTER(2)
#endif
#ifdef VERTEX_UV3
VERTEX_UV_GETTER(3)
#endif
#ifdef VERTEX_UV4
VERTEX_UV_GETTER(4)
#endif
#ifdef VERTEX_UV5
VERTEX_UV_GETTER(5)
#endif
#ifdef VERTEX_UV6
VERTEX_UV_GETTER(6)
#endif
#ifdef VERTEX_UV7
VERTEX_UV_GETTER(7)
#endif
#undef VERTEX_UV_GETTER

// Varying Setter
#define VARYING_SETTER(i)                                               \
void SetVarying##i (inout VertexOutput output, v##i##_type value)       \
{                                                                       \
    SetMappedVarying##i (output.pack, value);                           \
}
#ifdef VARYING1
VARYING_SETTER(1)
#endif
#ifdef VARYING2
VARYING_SETTER(2)
#endif
#ifdef VARYING3
VARYING_SETTER(3)
#endif
#ifdef VARYING4
VARYING_SETTER(4)
#endif
#ifdef VARYING5
VARYING_SETTER(5)
#endif
#ifdef VARYING6
VARYING_SETTER(6)
#endif
#ifdef VARYING7
VARYING_SETTER(7)
#endif
#ifdef VARYING8
VARYING_SETTER(8)
#endif
#undef VARYING_SETTER

// BuiltIn Varying Setter
#ifdef WORLD_POS
void SetWorldPos(inout VertexOutput output, float3 value)
{
    output.WorldPos = value;
}
#endif
#ifdef VIEW_DIR
void SetViewDir(inout VertexOutput output, half3 value)
{
    EXPAND_SET_VARYINGPOS(VIEW_DIR_VERYINGPOS)
}
#endif

// BuiltIn Function
float3 GetWorldPosition(in VertexInputBase input)
{
    return mul(unity_ObjectToWorld, float4(GetVertexPosition(input), 1.0f)).xyz;
}
float4 GetNdcPosition(in VertexInputPositionOnly input)
{
    return UnityObjectToClipPos(GetVertexPosition(input));
}
float4 GetNdcPosition(in VertexInputBase input)
{
    return UnityObjectToClipPos(GetVertexPosition(input));
}
half3 GetWorldNormal(in VertexInputBase input)
{
    return UnityObjectToWorldNormal(GetVertexNormal(input));
}
half3 GetViewDir(in VertexInputBase input)
{
    // return normalize(WorldSpaceViewDir(float4(GetVertexPosition(input), 1.0f)).xyz);
    return normalize(GetWorldPosition(input).xyz - _WorldSpaceCameraPos);
}

// Init Function
VertexOutputPositionOnly InitVertexOutput(in VertexInputPositionOnly input)
{
    VertexOutputPositionOnly output;
    output.Position = GetNdcPosition(input);
    return output;
}

VertexOutput InitVertexOutput(in VertexInputBase input)
{
    VertexOutput output = (VertexOutput)0;
    output.Position = GetNdcPosition(input);
    output.Normal = GetWorldNormal(input);
    output.UV0.xy = GetVertexUV0(input);
#ifdef TANGENT_SPACE
    output.Tangent = UnityObjectToWorldDir(GetVertexTangent(input).xyz);
    float sign = GetVertexTangent(input).w * unity_WorldTransformParams.w;
    output.BiNormal = cross(output.Normal, output.Tangent) * sign;
#endif
#ifdef LIGHTMAP_ON
    output.UV0.zw = GetVertexLightmapUV(input) * unity_LightmapST.xy + unity_LightmapST.zw;
#endif
#ifdef VERTEX_COLOR 
    output.Color = GetVertexColor(input);
#endif
#ifdef WORLD_POS
    SetWorldPos(output, GetWorldPosition(input));
#endif
#ifdef VIEW_DIR
    SetViewDir(output, GetViewDir(input));
#endif

    return output;
}

#endif