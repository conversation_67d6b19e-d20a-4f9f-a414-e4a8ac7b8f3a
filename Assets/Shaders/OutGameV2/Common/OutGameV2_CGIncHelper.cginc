// C<PERSON>nclude Helper
// Helper : varying getter and setter
// input macro : HELPER_VARYINGLOCATION, VARYING_I, VARYING_M, VARYING_GETTER, VARYING_SETTER, SORTED_VARYING1 ~ SORTED_VARYING12, SORTED_VARYING_POS1 ~ SORTED_VARYING_POS12
#if defined(HELPER_VARYINGLOCATION) && defined(VARYING_I) && defined(VARYING_M) && (defined(VARYING_GETTER) || defined(VARYING_SETTER))

    #define VARYING_I_DIM_CAT(i)  SORTED_VARYING##i
    #define EXPAND_VARYING_DIM(i) VARYING_I_DIM_CAT(i)
    #define VARYING_I_DIM_TMP EXPAND_VARYING_DIM(VARYING_M)     //for gcc -E Debug
    #define VARYING_I_DIM VARYING_I_DIM_TMP                     //for vs code lens

    #define VARYING_I_POS_CAT(i)  SORTED_VARYING_POS##i
    #define EXPAND_VARYING_POS(i) VARYING_I_POS_CAT(i)
    #define VARYING_I_POS_TMP EXPAND_VARYING_POS(VARYING_M)     //for gcc -E Debug
    #define VARYING_I_POS VARYING_I_POS_TMP                     //for vs code lens

    #if VARYING_I_DIM > 0
        #if defined(VARYING_GETTER)
            #define GETTERFUNC(i,j) sv##i##_type GetMappedVarying##j (in VaryingPack pack)
            #define EXPAND_GETTERFUNC(i,j) GETTERFUNC(i, j)
            EXPAND_GETTERFUNC(VARYING_M, VARYING_I)
            {
                return pack.
            #undef EXPAND_GETTERFUNC
            #undef GETTERFUNC
        #elif defined(VARYING_SETTER)
            #define SETTERFUNC(i,j) void SetMappedVarying##j (inout VaryingPack pack, sv##i##_type value)
            #define EXPAND_SETTERFUNC(i,j) SETTERFUNC(i, j)
            EXPAND_SETTERFUNC(VARYING_M, VARYING_I)
            {
                pack.
            #undef EXPAND_SETTERFUNC
            #undef SETTERFUNC
        #endif

        #if VARYING_I_DIM == 1
            #if VARYING_I_POS < 0
                #error "VARYING_I_POS must be >= 0"
            #elif VARYING_I_POS == 0
                pack1.x
            #elif VARYING_I_POS == 1
                pack1.y
            #elif VARYING_I_POS == 2
                pack1.z
            #elif VARYING_I_POS == 3
                pack1.w
            #elif VARYING_I_POS == 4
                pack2.x
            #elif VARYING_I_POS == 5
                pack2.y
            #elif VARYING_I_POS == 6
                pack2.z
            #elif VARYING_I_POS == 7
                pack2.w
            #elif VARYING_I_POS == 8
                pack3.x
            #elif VARYING_I_POS == 9
                pack3.y
            #elif VARYING_I_POS == 10
                pack3.z
            #elif VARYING_I_POS == 11
                pack3.w
            #elif VARYING_I_POS == 12
                pack4.x
            #elif VARYING_I_POS == 13
                pack4.y
            #elif VARYING_I_POS == 14
                pack4.z
            #elif VARYING_I_POS == 15
                pack4.w
            #elif VARYING_I_POS == 16
                pack5.x
            #elif VARYING_I_POS == 17
                pack5.y
            #elif VARYING_I_POS == 18
                pack5.z
            #elif VARYING_I_POS == 19
                pack5.w
            #elif VARYING_I_POS == 20
                pack6.x
            #elif VARYING_I_POS == 21
                pack6.y
            #elif VARYING_I_POS == 22
                pack6.z
            #elif VARYING_I_POS == 23
                pack6.w
            #elif VARYING_I_POS == 24
                pack7.x
            #elif VARYING_I_POS == 25
                pack7.y
            #elif VARYING_I_POS == 26
                pack7.z
            #elif VARYING_I_POS == 27
                pack7.w
            #else
                #error "VARYING_I_POS must be <= 27"
            #endif
        #elif VARYING_I_DIM == 2
            #if VARYING_I_POS < 0
                #error "VARYING_I_POS must be >= 0"
            #elif VARYING_I_POS == 0
                pack1.xy
            #elif VARYING_I_POS == 1
                pack1.yz
            #elif VARYING_I_POS == 2
                pack1.zw
            #elif VARYING_I_POS == 4
                pack2.xy
            #elif VARYING_I_POS == 5
                pack2.yz
            #elif VARYING_I_POS == 6
                pack2.zw
            #elif VARYING_I_POS == 8
                pack3.xy
            #elif VARYING_I_POS == 9
                pack3.yz
            #elif VARYING_I_POS == 10
                pack3.zw
            #elif VARYING_I_POS == 12
                pack4.xy
            #elif VARYING_I_POS == 13
                pack4.yz
            #elif VARYING_I_POS == 14
                pack4.zw
            #elif VARYING_I_POS == 16
                pack5.xy
            #elif VARYING_I_POS == 17
                pack5.yz
            #elif VARYING_I_POS == 18
                pack5.zw
            #elif VARYING_I_POS == 20
                pack6.xy
            #elif VARYING_I_POS == 21
                pack6.yz
            #elif VARYING_I_POS == 22
                pack6.zw
            #elif VARYING_I_POS == 24
                pack7.xy
            #elif VARYING_I_POS == 25
                pack7.yz
            #elif VARYING_I_POS == 26
                pack7.zw
            #else
                #error "VARYING_I_POS must be <= 26"
            #endif
        #elif VARYING_I_DIM == 3
            #if VARYING_I_POS < 0
                #error "VARYING_I_POS must be >= 0"
            #elif VARYING_I_POS == 0
                pack1.xyz
            #elif VARYING_I_POS == 1
                pack1.yzw
            #elif VARYING_I_POS == 4
                pack2.xyz
            #elif VARYING_I_POS == 5
                pack2.yzw
            #elif VARYING_I_POS == 8
                pack3.xyz
            #elif VARYING_I_POS == 9
                pack3.yzw
            #elif VARYING_I_POS == 12
                pack4.xyz
            #elif VARYING_I_POS == 13
                pack4.yzw
            #elif VARYING_I_POS == 16
                pack5.xyz
            #elif VARYING_I_POS == 17
                pack5.yzw
            #elif VARYING_I_POS == 20
                pack6.xyz
            #elif VARYING_I_POS == 21
                pack6.yzw
            #elif VARYING_I_POS == 24
                pack7.xyz
            #elif VARYING_I_POS == 25
                pack7.yzw
            #else
                #error "VARYING_I_POS must be <= 25"
            #endif
        #elif VARYING_I_DIM == 4
            #if VARYING_I_POS < 0
                #error "VARYING_I_POS must be >= 0"
            #elif VARYING_I_POS == 0
                pack1.xyzw
            #elif VARYING_I_POS == 4
                pack2.xyzw
            #elif VARYING_I_POS == 8
                pack3.xyzw
            #elif VARYING_I_POS == 12
                pack4.xyzw
            #elif VARYING_I_POS == 16
                pack5.xyzw
            #elif VARYING_I_POS == 20
                pack6.xyzw
            #elif VARYING_I_POS == 24
                pack7.xyzw
            #else
                #error "VARYING_I_POS must be <= 24"
            #endif
        #else
            #error "VARYING_I_DIM is wrong"
        #endif

        #if defined(VARYING_GETTER)
                ;
            }
            #undef VARYING_GETTER
        #elif defined(VARYING_SETTER)
            = value;
            }
            #undef VARYING_SETTER
        #endif
    #endif

    #undef VARYING_I_POS
    #undef VARYING_I_POS_TMP
    #undef VARYING_I_DIM
    #undef VARYING_I_DIM_TMP
    #undef EXPAND_VARYING_DIM
    #undef EXPAND_VARYING_POS
    #undef VARYING_I_DIM_CAT
    #undef VARYING_I_POS_CAT
#endif

// Helper : type def
// input macro : HELPPER_TYPEDEF, TYPE_DIM, TYPE_NAME
// undef macro : TYPE_DIM, TYPE_NAME
#if defined(HELPER_TYPEDEF) && defined(TYPE_DIM) && defined(TYPE_NAME)
    #if TYPE_DIM == 1
    typedef half1 TYPE_NAME;
    #elif TYPE_DIM == 2
    typedef half2 TYPE_NAME;
    #elif TYPE_DIM == 3
    typedef half3 TYPE_NAME;
    #elif TYPE_DIM == 4
    typedef half4 TYPE_NAME;
    #endif
#undef TYPE_DIM
#undef TYPE_NAME
#endif