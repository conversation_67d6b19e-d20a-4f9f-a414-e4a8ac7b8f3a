Shader "OutGameV2/VaryingExample"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
    }
    SubShader
    {
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define VARYING1 3          //worldPos * 0.1
            #define VARYING2 4          //ndcPos * 0.2
            #define VARYING3 1          //depth
            #define VARYING4 2          //custom1
            #define VARYING5 2          //custom2

            #include "../Common/OutGameV2.cginc"

            VertexOutput vert (VertexInputBase vertex)
            {
                VertexOutput output = InitVertexOutput(vertex);

                float3 worldPos  = GetWorldPosition(vertex);
                float4 ndcPos = GetNdcPosition(vertex);
                float depth = ndcPos.z / ndcPos.w;

                SetVarying1(output, worldPos * 0.1);
                SetVarying2(output, ndcPos * 0.2);
                SetVarying3(output, depth);
                SetVarying4(output, half2(0.1,0.4));
                SetVarying5(output, half2(1.0,2.0));

                return output;
            }

            fixed4 frag (FragInput frag) : SV_Target
            {
                InitFragmentInput(frag);
                half3 worldPos = GetVarying1(frag);
                half4 ndcPos = GetVarying2(frag);
                half depth = GetVarying3(frag);
                half2 custom1 = GetVarying4(frag);
                half2 custom2 = GetVarying5(frag);

                half3 baseColor = SampleMainTex(frag);

                fixed4 color = fixed4(0,0,0,1);
                color.xyz = baseColor * abs(worldPos.yzx) * 5.0f;
                return color;
            }
            ENDCG
        }
    }
}