Shader "OutGameV2/MultiCompileExample"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
    }
    SubShader
    {
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ LIGHTMAP_ON
            #pragma multi_compile _ BUMP_MAP

            #if BUMP_MAP
                #define TANGENT_SPACE
            #endif

            #include "../Common/OutGameV2.cginc"

            VertexOutput vert (VertexInputBase vertex)
            {
                VertexOutput output = InitVertexOutput(vertex);
                return output;
            }

            fixed4 frag (FragInput frag) : SV_Target
            {
                InitFragmentInput(frag);

                #if LIGHTMAP_ON
                    half3 lm = SampleLightmap(frag);
                #else
                    half3 lm = half3(1,1,1);
                #endif

				#if BUMP_MAP
			        half3 normal = GetDetailNormal(frag);
                #else
                    half3 normal = GetVertexNormal(frag);
				#endif
                
                half3 baseColor = SampleMainTex(frag);
                fixed4 color = fixed4(0,0,0,1);
                color.xyz = baseColor * lm * normal;
                return color;
            }
            ENDCG
        }
    }
}