// It only support one directional light, lightmap
// no specular and no emission.

Shader "Mt/Wind Tree" {
    Properties {
        /*[PerRendererData]*/_Color ("Color", Color) = (1,1,1,1)
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        [HideInInspector]_RendererColor("RendererColor", Color) = (1,1,1,1)
        [HideInInspector]_Flip("Flip", Vector) = (1,1,1,1)
        
        /*[PerRendererData]*/_WindDirAndBendScale("Wind direction (xy) and Bend Scale (zw)", Vector) = (0, 0, 0, 0)
        /*[PerRendererData]*/_RandOffsetAndSwayFreq("Random Offset (xy) and sway freq (zw)", Vector) = (0, 0, 0, 0)
        /*[PerRendererData]*/_YStart("Y axis start position", Range(-1, 1)) = 0
        /*[PerRendererData]*/_Cutoff("Alpha cutout", Range(0, 1)) = 0.01
        //【【LL需求】小小英雄攻击特效编辑器内添加屏幕（黑屏）表现功能及效果实现】 http://tapd.oa.com/cchess/prong/stories/view/1020417564865672619  (fengxzeng)
        [HideInInspector]_dark("Dark", Range(0.0, 1.0)) = 0
    }
    SubShader {
        Tags {
            "RenderType"="TransparentCutout"
            "Queue"="AlphaTest"
            //"DisableBatching"="True"
            "IgnoreProjector"="True"
        }
        Cull Off
        // Lighting Off
        // ZWrite Off
        Pass{
            Blend SrcAlpha OneMinusSrcAlpha
			ColorMask RGB

            CGPROGRAM
            sampler2D _MainTex;
            sampler2D _AlphaTex;
            // fixed4 _Color;

            //fixed _WindDir;
            //fixed _RandOffset;
            //fixed _BendScale;
            //fixed _SwayFreq;
            
     
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #pragma multi_compile_fwdbase
            #pragma multi_compile_fog
            #define UNITY_INSTANCED_LOD_FADE
            #define UNITY_INSTANCED_SH
            #define UNITY_INSTANCED_LIGHTMAPSTS
            #if defined(SHADER_API_MOBILE) && !defined(SHADER_API_METAL)
            #include "TriFuncApproximate.cginc"
            #endif
            #include "HLSLSupport.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"
            #include "UnityCG.cginc"

           
            #define _IN_GAME_SCENE 1
            #include "../InGame/Common/InGame.cginc"
            struct appdata_my{
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
                float2 texcoord1 : TEXCOORD1;
                fixed4 color : COLOR;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f_my{
                float4 pos : SV_POSITION;
                float2 uv_MainTex : TEXCOORD0;
                float3 worldPos : TEXCOORD1;
                float4 screenPos : TEXCOORD2;
                fixed4 color : TEXCOORD3;
            #ifdef LIGHTMAP_ON
                float2 lmap : TEXCOORD4;
            #endif
                LIGHTING_COORDS(5,6)
                UNITY_FOG_COORDS(7)
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(fixed4, _MainTex_ST)
                UNITY_DEFINE_INSTANCED_PROP(fixed4, _Color)
                UNITY_DEFINE_INSTANCED_PROP(fixed4, _WindDirAndBendScale)
                UNITY_DEFINE_INSTANCED_PROP(fixed4, _RandOffsetAndSwayFreq)
                UNITY_DEFINE_INSTANCED_PROP(fixed, _YStart)
                UNITY_DEFINE_INSTANCED_PROP(fixed, _Cutoff)
            UNITY_INSTANCING_BUFFER_END(Props)

            v2f_my vert(appdata_my v) {
                v2f_my o = (v2f_my)0;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_TRANSFER_INSTANCE_ID(v, o);
                // Transform tex
                o.uv_MainTex = v.texcoord.xy
                    * UNITY_ACCESS_INSTANCED_PROP(Props, _MainTex_ST).xy
                    + UNITY_ACCESS_INSTANCED_PROP(Props, _MainTex_ST).zw;

                float3 vPos = v.vertex;
                float2 fBF = (vPos.y + 0.5)
                    * (UNITY_ACCESS_INSTANCED_PROP(Props, _WindDirAndBendScale.zw))
                    * ((
                    // On mobile platforms (such as arm), the trigonometric operation is expensive. But for PowerVR series(used by iOS devices), and PC gpus, it's okay.
                    // Using approximate value instead of calculate trigonometric itself.
                #if defined(SHADER_API_MOBILE) && !defined(SHADER_API_METAL)
                    SinApproximate
                #else
                    sin
                #endif
                    (_Time.z * (UNITY_ACCESS_INSTANCED_PROP(Props, _RandOffsetAndSwayFreq.zw))
                         + UNITY_ACCESS_INSTANCED_PROP(Props, _RandOffsetAndSwayFreq.xy)) + 0.5) * 0.5);
                vPos.xz += (UNITY_ACCESS_INSTANCED_PROP(Props, _WindDirAndBendScale.xy) + fBF.xy)
                    * saturate(vPos.y - UNITY_ACCESS_INSTANCED_PROP(Props, _YStart));
                v.vertex.xz = vPos.xz;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex);
            #ifdef LIGHTMAP_ON
                o.lmap.xy = v.texcoord1.xy * unity_LightmapST.xy + unity_LightmapST.zw;
            #endif

                // Transfer light info
                TRANSFER_VERTEX_TO_FRAGMENT(o);
                UNITY_TRANSFER_FOG(o, o.pos);
                // The following code is for test purpose
                //o.color = fixed4(vPos.y + 0.5, vPos.y + 0.5, vPos.y + 0.5, 1);
                return o;
            }

            fixed4 frag(v2f_my i) : SV_Target {
                UNITY_SETUP_INSTANCE_ID(i);
                fixed4 c = tex2D(_MainTex, i.uv_MainTex) * UNITY_ACCESS_INSTANCED_PROP(Props, _Color);
                clip(c.a - UNITY_ACCESS_INSTANCED_PROP(Props, _Cutoff));
                fixed atten = LIGHT_ATTENUATION(i);
                c.rgb *= atten;
            #ifdef LIGHTMAP_ON
                // copied from internal method UnityGI_Base
                fixed3 lm = DecodeLightmap(UNITY_SAMPLE_TEX2D(unity_Lightmap, i.lmap.xy));
            // #ifdef SHADOWS_SCREEN
            //     c.rgb = c.rgb * min(lm, atten*2);
            // #else
                c.rgb = c.rgb * lm;
            // #endif
            #endif // LIGHTMAP_ON

                //UNITY_APPLY_FOG(i.fogCoord, c);
                APPLY_HEIGHT_FOG(c, i.worldPos.y);
                APPLY_EFFECT_DARK_SCENE(c);
                return fixed4(c.rgb, 1);
            }
            ENDCG
        }
    }
    CustomEditor "DefaultShaderGUI"
}
