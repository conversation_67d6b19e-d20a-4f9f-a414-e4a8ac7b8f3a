Shader "OutGame/Scene/Unlit"
{
    Properties
    {
        [HDR] _Color("Color",Color) = (1.0,1.0,1.0,1.0)
        _MainTex("Texture", 2D) = "white" {}

        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", Float) = 2

        //blending state
        [HideInInspector] _Mode("Blend Mode", float) = 0
        [HideInInspector] _SrcBlend("Source Blend", float) = 1.0
        [HideInInspector] _DstBlend("Destination Blend", float) = 0.0
        [HideInInspector] _ZWrite("Z Write", float) = 1.0
            _Cutoff("Alpha Cutoff", Range(0.0, 0.95)) = 0.8

        // 【ID863885247】局外升星，场景变黑			--ho<PERSON><PERSON><PERSON>
        [HideInInspector] _dark("Dark", Range(0.0, 1.0)) = 0
        [HideInInspector] _RenderQueueOffset("_Render Queue Offset", int) = 0

        // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
        [Toggle(_GRANDIENT_BG)]_GrandientBG("_GrandientBG ",Float) = 0
        _Color0("Color0",Color) = (1.0,1.0,1.0,1.0)
        _Color1("Color0",Color) = (1.0,1.0,1.0,1.0)
        _Color2("Color0",Color) = (1.0,1.0,1.0,1.0)
        _Color3("Color0",Color) = (1.0,1.0,1.0,1.0)
        _ColorPos("colorpos", Vector) = (0,0.33,0.66,1.0)
        _GrandientAxis("grandient axis ", Vector) = (1,0,0,0)
        _GrandientAxisIndex("grandient axis ", Range(0,3)) = 0
        _RotAngle("_RotAngle", Range(0.0,360)) = 0

        // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)  //Noise 减弱色带精度问题
        [Toggle(_GRANDIENT_NOISE)]_GrandientNoise("_GRANDIENT_NOISE",Float) = 0
        _NoiseTex("Noise Texture", 2D) = "white" {}
        _NoiseStrength("Noise Strength", Range(10, 500)) = 90
        _NoiseScale("Noise Scale", Vector) = (25, 25, 0, 0) // [10-60]
    }
    SubShader
    {
        Tags { "RenderType" = "Opaque" }
        LOD 200
        Pass
        {
            Blend[_SrcBlend][_DstBlend]
            Zwrite[_ZWrite]
            Cull[_Cull]
            // 没必要写Alpha, 避免和非极高下存于Alpha通道的描边Mask冲突
            ColorMask RGB

            CGPROGRAM
            #pragma multi_compile_instancing
            #pragma vertex vert
            #pragma fragment frag
            #pragma shader_feature _ _ALPHATEST_ON _ALPHABLEND_ON  //_ALPHAPREMULTIPLY_ON
            #pragma shader_feature _GRANDIENT_BG
            #pragma shader_feature _GRANDIENT_NOISE
            #define _SCENE 1
            #define _OUTGAME_SCENE 1
            #include "./Common/OutGame.cginc"

            struct appdata
            {
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
#if defined (_GRANDIENT_BG)   // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
                float2 axis: TEXCOORD1;
#endif
                float4 screenUv: TEXCOORD3;
            };

            float _RotAngle;
            fixed4 _GrandientAxis;
            fixed _GrandientAxisIndex;
            fixed4 _Color;
            fixed4 _Color0;
            fixed4 _Color1;
            fixed4 _Color2;
            fixed4 _Color3;
            float4 _ColorPos;
            sampler2D _ReflectionTex;
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float _Cutoff;

            v2f vert(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
#if defined(_GRANDIENT_NOISE) // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)  //Noise 减弱色带精度问题
                o.screenUv = ComputeGrabScreenPos(o.vertex);
#endif
#if defined (_GRANDIENT_BG)   // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
                o.axis.x = frac(_GrandientAxis.x * o.uv.x + _GrandientAxis.y * (1 - o.uv.x) + _GrandientAxis.z * o.uv.y + _GrandientAxis.w * (1 - o.uv.y) + saturate(_GrandientAxisIndex - 3) * distance(float2(0.5, 0.5), o.uv));
                o.axis.y = (o.axis.x - _ColorPos.x) / (_ColorPos.w - _ColorPos.x);
#endif
                return o;
            }

            fixed3 selectRange(float3 a, float3 b, inout float3 x)
            {
                // x = smoothstep(0, 1, saturate((x - a) / (b - a)));
                // 上式等同于下面的写法
                x = smoothstep(a, b, x);
                return ceil(x) * floor(x); //重新计算 【【#267】【局外】【小小英雄】小小英雄界面有条不明显的黑线】http://tapd.oa.com/cchess/bugtrace/bugs/view?bug_id=1020417564090737021
            }

            // 部分标量计算优化成矢量计算，减少计算次数 --bug=********* 【客户端性能】【局外】【藏品】太空律动 咚咚、炸鸡咚咚 藏品GPU高
            fixed4 frag(v2f i) : SV_Target
            {
                fixed4 col;
#if defined (_GRANDIENT_BG)   // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
                // 片源计算distance性能开销比较大（因为需要计算sqrt, frac, saturate），使用顶点着色器计算代替。
                // i.axis.x = frac(_GrandientAxis.x * i.uv.x + _GrandientAxis.y * (1 - i.uv.x) + _GrandientAxis.z * i.uv.y + _GrandientAxis.w * (1 - i.uv.y) + saturate(_GrandientAxisIndex - 3) * distance(float2(0.5, 0.5), i.uv));
                float4 xVec = i.axis.yxxx;
                //小于最小 和 大于最大 两个遮罩
                // 扔到顶点着色器，以节省性能（除法还是比较消耗的）
                // xVec.x = saturate((xVec.x - _ColorPos.x) / (_ColorPos.w - _ColorPos.x));
                xVec.x = saturate(xVec.x);
                //小于最小 和 大于最大 区域染色
                fixed4 colBG = (1 - ceil(xVec.x)) * _Color0 + (1 - ceil(1 - xVec.x)) * _Color3;
                //分为三段 分别的遮罩区域
                fixed3 mVec = selectRange(_ColorPos.xyz, _ColorPos.yzw, xVec.yzw);
                //重新计算 【【#267】【局外】【小小英雄】小小英雄界面有条不明显的黑线】http://tapd.oa.com/cchess/bugtrace/bugs/view?bug_id=1020417564090737021
                //分为三段 分别染色
                fixed3 col1 = lerp(_Color0, _Color1, xVec.y).rgb * (1 - mVec.x);
                fixed3 col2 = lerp(_Color1, _Color2, xVec.z).rgb * (mVec.x - mVec.y);
                fixed3 col3 = lerp(_Color2, _Color3, xVec.w).rgb * (mVec.y - mVec.z);

                col = fixed4(saturate(colBG.rgb + col3 + col2 + col1) * _Color.rgb, _Color.a);
#else
                col = tex2D(_MainTex, i.uv) * _Color;
#endif
#if defined(_ALPHATEST_ON)
                clip(col.a - _Cutoff);
#endif
#if defined(_GRANDIENT_NOISE) // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)  //Noise 减弱色带精度问题
                APPLY_GRADIENT_NOISE_FRAG(col);
#endif
#if defined (_OUTGAME_SCENE)  // 【ID863885247】局外升星，场景变黑		--horacezhao
                APPLY_EFFECT_DARK_SCENE(col);
#endif
                return col;
            }
            ENDCG
        }
    }

    SubShader
    {
        Tags { "RenderType" = "Opaque" }
        Pass
        {
            Blend[_SrcBlend][_DstBlend]
            Zwrite[_ZWrite]
            Cull[_Cull]
            // 没必要写Alpha, 避免和非极高下存于Alpha通道的描边Mask冲突
            ColorMask RGB

            CGPROGRAM
            #pragma multi_compile_instancing
            #pragma vertex vert
            #pragma fragment frag
            #pragma shader_feature _ _ALPHATEST_ON _ALPHABLEND_ON  //_ALPHAPREMULTIPLY_ON
            #pragma shader_feature _GRANDIENT_BG
            // #pragma shader_feature _GRANDIENT_NOISE
            #define _SCENE 1
            #define _OUTGAME_SCENE 1
            #include "./Common/OutGame.cginc"

            struct appdata
            {
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
#if defined (_GRANDIENT_BG)   // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
                float2 axis: TEXCOORD1;
#endif
                float4 screenUv: TEXCOORD3;
            };

            float _RotAngle;
            fixed4 _GrandientAxis;
            fixed _GrandientAxisIndex;
            fixed4 _Color;
            fixed4 _Color0;
            fixed4 _Color1;
            fixed4 _Color2;
            fixed4 _Color3;
            float4 _ColorPos;
            sampler2D _ReflectionTex;
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float _Cutoff;

            v2f vert(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
// #if defined(_GRANDIENT_NOISE) // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)  //Noise 减弱色带精度问题
//                 o.screenUv = ComputeGrabScreenPos(o.vertex);
// #endif
#if defined (_GRANDIENT_BG)   // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
                o.axis.x = frac(_GrandientAxis.x * o.uv.x + _GrandientAxis.y * (1 - o.uv.x) + _GrandientAxis.z * o.uv.y + _GrandientAxis.w * (1 - o.uv.y) + saturate(_GrandientAxisIndex - 3) * distance(float2(0.5, 0.5), o.uv));
                o.axis.y = (o.axis.x - _ColorPos.x) / (_ColorPos.w - _ColorPos.x);
#endif
                return o;
            }

            fixed3 selectRange(float3 a, float3 b, inout float3 x)
            {
                // x = smoothstep(0, 1, saturate((x - a) / (b - a)));
                // 上式等同于下面的写法
                x = smoothstep(a, b, x);
                return ceil(x) * floor(x); //重新计算 【【#267】【局外】【小小英雄】小小英雄界面有条不明显的黑线】http://tapd.oa.com/cchess/bugtrace/bugs/view?bug_id=1020417564090737021
            }

            // 部分标量计算优化成矢量计算，减少计算次数 --bug=********* 【客户端性能】【局外】【藏品】太空律动 咚咚、炸鸡咚咚 藏品GPU高
            fixed4 frag(v2f i) : SV_Target
            {
                fixed4 col;
#if defined (_GRANDIENT_BG)   // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)
                // 片源计算distance性能开销比较大（因为需要计算sqrt, frac, saturate），使用顶点着色器计算代替。
                // i.axis.x = frac(_GrandientAxis.x * i.uv.x + _GrandientAxis.y * (1 - i.uv.x) + _GrandientAxis.z * i.uv.y + _GrandientAxis.w * (1 - i.uv.y) + saturate(_GrandientAxisIndex - 3) * distance(float2(0.5, 0.5), i.uv));
                float4 xVec = i.axis.yxxx;
                //小于最小 和 大于最大 两个遮罩
                // 扔到顶点着色器，以节省性能（除法还是比较消耗的）
                // xVec.x = saturate((xVec.x - _ColorPos.x) / (_ColorPos.w - _ColorPos.x));
                xVec.x = saturate(xVec.x);
                //小于最小 和 大于最大 区域染色
                fixed4 colBG = (1 - ceil(xVec.x)) * _Color0 + (1 - ceil(1 - xVec.x)) * _Color3;
                //分为三段 分别的遮罩区域
                fixed3 mVec = selectRange(_ColorPos.xyz, _ColorPos.yzw, xVec.yzw);
                //重新计算 【【#267】【局外】【小小英雄】小小英雄界面有条不明显的黑线】http://tapd.oa.com/cchess/bugtrace/bugs/view?bug_id=1020417564090737021
                //分为三段 分别染色
                fixed3 col1 = lerp(_Color0, _Color1, xVec.y).rgb * (1 - mVec.x);
                fixed3 col2 = lerp(_Color1, _Color2, xVec.z).rgb * (mVec.x - mVec.y);
                fixed3 col3 = lerp(_Color2, _Color3, xVec.w).rgb * (mVec.y - mVec.z);

                col = fixed4(saturate(colBG.rgb + col3 + col2 + col1) * _Color.rgb, _Color.a);
#else
                col = tex2D(_MainTex, i.uv) * _Color;
#endif
#if defined(_ALPHATEST_ON)
                clip(col.a - _Cutoff);
#endif
// #if defined(_GRANDIENT_NOISE) // 【ID865632473】【【TA需求】渐变shader效果需求】 (fengxzeng)  //Noise 减弱色带精度问题
//                 APPLY_GRADIENT_NOISE_FRAG(col);
// #endif
#if defined (_OUTGAME_SCENE)  // 【ID863885247】局外升星，场景变黑		--horacezhao
                APPLY_EFFECT_DARK_SCENE(col);
#endif
                return col;
            }
            ENDCG
        }
    }
    FallBack "Unlit/Texture"
    CustomEditor "SceneShaderGUI"
}
