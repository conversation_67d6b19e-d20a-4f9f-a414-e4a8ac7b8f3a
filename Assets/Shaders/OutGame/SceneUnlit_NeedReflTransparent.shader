Shader "OutGame/Scene/Z_TransparentUnlit"
{
    Properties
    {
        [HDR] _Color("Color",Color) = (1.0,1.0,1.0,1.0)
        _MainTex("Texture", 2D) = "white" {}

        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", Float) = 2

        // 【ID863885247】局外升星，场景变黑			--<PERSON><PERSON><PERSON><PERSON>
        [HideInInspector] _dark("Dark", Range(0.0, 1.0)) = 0
        [HideInInspector] _RenderQueueOffset("_Render Queue Offset", int) = 0

    }


    SubShader
    {
        Tags { "RenderType" = "NeedReflTransparent"  "Queue"="Transparent"}
        Pass
        {
            ZWrite Off
            Blend SrcAlpha OneMinusSrcAlpha
            Cull[_Cull]

            // 没必要写Alpha, 避免和非极高下存于Alpha通道的描边Mask冲突
            ColorMask RGB

            CGPROGRAM
            #pragma multi_compile_instancing
            #pragma vertex vert
            #pragma fragment frag
            #define _SCENE 1
            #define _OUTGAME_SCENE 1
            #include "./Common/OutGame.cginc"

            struct appdata
            {
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            fixed4 _Color;
            sampler2D _MainTex;
            float4 _MainTex_ST;


            v2f vert(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            // 部分标量计算优化成矢量计算，减少计算次数 --bug=109106221 【客户端性能】【局外】【藏品】太空律动 咚咚、炸鸡咚咚 藏品GPU高
            fixed4 frag(v2f i) : SV_Target
            {
                half4 col;

                col = tex2D(_MainTex, i.uv) * _Color;

#if defined (_OUTGAME_SCENE)  // 【ID863885247】局外升星，场景变黑		--horacezhao
                APPLY_EFFECT_DARK_SCENE(col);
#endif
                return col;
            }
            ENDCG
        }
    }
}
