// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'

Shader "OutGame/Scene/CloudLit"
{
    Properties
    {
        _MainTex("Texture", 2D) = "white" {}

        _CloudLightDir1("1", 2D) = "white" {}
        _CloudLightDir2("2", 2D) = "white" {}

        _CloudLightDirU("u", 2D) = "white" {}
        _CloudLightDirD("d", 2D) = "white" {}
        _CloudLightDirR("r", 2D) = "white" {}
        _CloudLightDirL("l", 2D) = "white" {}
        _CloudLightDirB("b", 2D) = "white" {}
        _CloudLightDirF("f", 2D) = "white" {}
        [HDR]_SceneCloudLightColor("Main Color", Color) = (1, 1, 1, 1)
        [HDR]_SceneCloudAmbientColor("_AmbientColor", Color) = (1, 1, 1, 1)
        //[LightDir] _SceneCloudLightDir("LightDir", Vector) = (0, 0, 0)
        _Range("Screen Speed X",float) = 1
        [Toggle]_UseDirection("Use dir",float) = 0

    }
    SubShader
    {
        Tags { "Queue" = "Transparent" "RenderType" = "Transparent" "IgnoreProjector" = "True" }
        LOD 100

        Pass
        {

            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite off
            Cull back

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float2 normal : NORMAL;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 normalW : TEXCOORD2;
                float4 worldPos:TEXCOORD1;
            };

            float4 _SceneCloudLightColor;
            float4 _SceneCloudAmbientColor;
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _CloudLightDir;
            sampler2D _CloudLightDir1;
            sampler2D _CloudLightDir2;
            sampler2D _CloudLightDirU;
            sampler2D _CloudLightDirD;
            sampler2D _CloudLightDirR;
            sampler2D _CloudLightDirL;
            sampler2D _CloudLightDirB;
            sampler2D _CloudLightDirF;
            
                
            float3 _SceneCloudLightDir;
            sampler2D _SceneCloudEmissiveRamp;
            sampler2D _SceneCloudEmissivePreMultiply;
            float _SceneCloudMoveSpeed;
            float _Range;
            float _UseDirection;

            
            
            //float4 _MainTex_ST;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex);
                o.normalW = mul((float3x3)unity_ObjectToWorld, v.normal);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {

                //_SceneCloudLightDir = normalize(_SceneCloudLightDir);// -i.worldPos);
                _SceneCloudLightDir = -normalize(_SceneCloudLightDir.xyz -i.worldPos);
                
                float dis=distance(_SceneCloudLightDir.xyz,  i.worldPos.xyz);
                dis = max(1-(dis / _Range),0);

                fixed4 col = tex2D(_MainTex, i.uv);
                fixed4 dir = tex2D(_CloudLightDir, i.uv);

           

                half4 dirU = 0;
                half4 dirD = 0;
                half4 dirR = 0;
                half4 dirL = 0;

                half4 front = 0;
                half4 back = 0;
                half alpha = 0;


                if (_UseDirection==0)
                {
                    half4 dir1 = tex2D(_CloudLightDir1, i.uv);
                    half4 dir2 = tex2D(_CloudLightDir2, i.uv);

                    dirU = dir1.x;
                    dirD = dir1.y;
                    dirR = dir1.z;
                    dirL = dir1.a;

                    back = dir2.x;
                    front = dir2.y;

                    alpha = dir2.z;
                    alpha = col.a;
                }
                else {
                

                     dirU = tex2D(_CloudLightDirU, i.uv);
                     dirD = tex2D(_CloudLightDirD, i.uv);
                     dirR = tex2D(_CloudLightDirR, i.uv);
                     dirL = tex2D(_CloudLightDirL, i.uv);

                     front = tex2D(_CloudLightDirF, i.uv);
                     back = tex2D(_CloudLightDirB, i.uv);
                     alpha = col.a;

                }



                //half front = pow((dir.r + dir.g + dir.b + dir.a) * 0.25, 0.625);

            



                //half back = front * 0.4;
                //half back = 1.0 - front;
                //back = saturate(0.25 * (1.0 - i.normalW.x) + 0.5 * (back * back * back * back));


                float3 hMap = (_SceneCloudLightDir.x > 0.0f) ? (dirR) : (dirL);   // Picks the correct horizontal side.
                float3 vMap = (_SceneCloudLightDir.y > 0.0f) ? ( dirD) : (dirU);   // Pickws the correct Vertical side.
                float3 dMap = (_SceneCloudLightDir.z > 0.0f) ? (  back) : (front);              // Picks the correct Front/back Pseudo Map
                float3 lightMap = hMap * _SceneCloudLightDir.x * _SceneCloudLightDir.x + vMap * _SceneCloudLightDir.y * _SceneCloudLightDir.y + dMap * _SceneCloudLightDir.z * _SceneCloudLightDir.z; // Pythagoras!

                if (_UseDirection == 0)
                {
                    lightMap = lerp(_SceneCloudAmbientColor, _SceneCloudLightColor, lightMap.r);
                }

                //fixed4 EmissivePreTex = tex2D(_SceneCloudEmissivePreMultiply, float2(1 - lightMap+ _SinTime.z* _SceneCloudMoveSpeed, 0));

                //fixed4 EmissiveTex = tex2D(_SceneCloudEmissiveRamp, float2(1 - EmissivePreTex.r, 0));

                //lightMap += _SinTime.z* _SceneCloudMoveSpeed;


                //col.rgb = lerp(_SceneCloudAmbientColor.rgb, _SceneCloudLightColor.rgb, lightMap);
                col.rgb = lightMap* dis;
                //col.rgb *= _SceneCloudLightColor.rgb * EmissiveTex.rgb + _SceneCloudAmbientColor.rgb;
                //col.rgb *= _SceneCloudLightColor.rgb* lightMap + _SceneCloudAmbientColor.rgb;
                //col.a *= _SceneCloudLightColor.a;
                col.a = alpha;
         
                return col;
            }
            ENDCG
        }
            Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #pragma multi_compile_shadowcaster
            #pragma shader_feature INSTANCING_ON
            #include "UnityCG.cginc"

            struct v2f
            {
                V2F_SHADOW_CASTER;
                UNITY_VERTEX_OUTPUT_STEREO
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            v2f vert(appdata_base v)
            {
                v2f o;
                TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
                return o;
            }

            float4 frag(v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                SHADOW_CASTER_FRAGMENT(i)
            }
            ENDCG

        }
    }
        FallBack "Unlit/Texture"

}
