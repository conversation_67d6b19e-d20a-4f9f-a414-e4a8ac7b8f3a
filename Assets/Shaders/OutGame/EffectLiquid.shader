Shader "OutGame/Effect/Liquid"
{
    Properties
    {
		_Tint ("颜色", Color) = (1,1,1,1)
		_MainTex ("基础贴图", 2D) = "white" {}

        [Space(10)]
        [Header(Liquid)]
        [Space(5)]
        _FillAmount ("液体 填充度", Range(-2,2)) = 0
        _WobbleSpeed ("液体 摇晃 速度", Range(0,8)) = 5
        _WobbleFreq ("液体 摇晃 频率", Range(0,1)) = 0.1
        _TopColor ("液体 顶部 颜色", Color) = (0,0,0,1)
		// _FoamColor ("泡沫 颜色", Color) = (1,0,1,1)
        // _Rim ("泡沫 宽度", Range(0,0.1)) = 0.02    
		_RimColor ("边缘光 颜色", Color) = (0,0,1,1)
	    _RimPower ("边缘光 强度", Range(0,3)) = 1
    }
 
    SubShader
    {
        Tags {"Queue"="Geometry"  "DisableBatching" = "True" }
 
        Pass
        {
            Cull Off
    
            CGPROGRAM
    
            #pragma vertex vert
            #pragma fragment frag
    
            #include "UnityCG.cginc"
    
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;	
            };
    
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 viewDir : TEXCOORD1;
                float3 normal : TEXCOORD2;		
                float fillEdge : TEXCOORD3;
            };
    
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float _FillAmount , _WobbleSpeed, _WobbleFreq;
            float4 _TopColor, _RimColor, _FoamColor, _Tint;
            float _Rim, _RimPower;
            float3 _ChangeWorldPos;
            float _WobbleX, _WobbleZ;
    
            float4 RotateAroundYInDegrees (float4 vertex, float degrees)
            {
                float alpha = degrees * UNITY_PI / 180;
                float sina, cosa;
                sincos(alpha, sina, cosa);
                float2x2 m = float2x2(cosa, sina, -sina, cosa);
                return float4(vertex.yz , mul(m, vertex.xz)).xzyw ;				
            }

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                // get model position change of the vertex
                float3 bipPos = _ChangeWorldPos ;   
                float3 modelPos = v.vertex.xyz - mul (unity_WorldToObject, bipPos);

                half WobbleX = sin(_Time.z * _WobbleSpeed - 1 ) * _WobbleFreq ;
                half WobbleZ = WobbleX * 0.5  ;
                // rotate it around XY
                float3 worldPosX= RotateAroundYInDegrees(float4(modelPos,0),360);
                // rotate around XZ
                float3 worldPosZ = float3 (worldPosX.y, worldPosX.z, worldPosX.x);		
                // combine rotations with worldPos, based on sine wave from script
                float3 worldPosAdjusted = modelPos + (worldPosX  * WobbleX) + (worldPosZ * WobbleZ); 
                //float3 worldPosAdjusted = worldPos + (worldPosX  * _WobbleX) + (worldPosZ * _WobbleZ); 


                // how high up the liquid is
                o.fillEdge =  worldPosAdjusted.y + _FillAmount;
    
                o.viewDir = normalize(ObjSpaceViewDir(v.vertex));
                o.normal = v.normal;

                return o;
            }
    
            fixed4 frag (v2f i, fixed facing : VFACE) : SV_Target
            {
                // sample the texture
                half4 albedo = tex2D(_MainTex, i.uv);
                half4 col = lerp(_Tint, albedo * _Tint, _Tint.a);

                clip(0.5 - i.fillEdge);   //暂时注销功能

                float dotProduct = 1 - pow(dot(i.normal, i.viewDir), _RimPower);
                float4 RimResult = smoothstep(0.5, 1.0, dotProduct);
                RimResult *= _RimColor;
        
                // foam edge
                // float4 foam = ( step(i.fillEdge, 0.5) - step(i.fillEdge, (0.5 - _Rim)))  ;
                // float4 foamColored = foam * (_FoamColor * 0.9);
                float4 foam = 0 ;
                float4 foamColored = 0 ;

                // rest of the liquid
                float4 result = step(i.fillEdge, 0.5) - foam;
                float4 resultColored = result * col;
                // both together, with the texture
                float4 finalResult = resultColored + foamColored;				
                finalResult.rgb += RimResult;
        
                // color of backfaces/ top
                //half4 topAlbedo = lerp(topAlbedo00 * _TopColor, _TopColor,  _TopColor.a);
                half4 topAlbedo = _TopColor;
                // float4 topColor = foam + topAlbedo;
                float4 topColor = _TopColor * (foam + result * topAlbedo);
                float4 topColor02 = lerp(finalResult, _TopColor*1.5, _TopColor.a);
                //VFACE returns positive for front facing, negative for backfacing
                return facing > 0 ? finalResult: topColor02;
    
            }
            ENDCG
        }
 
    }
}