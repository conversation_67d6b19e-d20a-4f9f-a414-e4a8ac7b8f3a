Shader "OutGame/Effect/Crystal(Dissolve)"
{
	Properties
	{
		[Header(Main Color)]
		[Space(5)]
		_Brightness("亮度", Range(1,2)) = 1
		_Color("主纹理颜色", Color) = (1, 1, 1, 1)
		[NoScaleOffset]_MainTex("Main Matcap Texture", 2D) = "black" {}

		[Space(10)]
		[Header(Rim Lighting)]
		[Space(5)]
		_EdgeColor("边缘光颜色", Color) = (1, 1, 1, 1)
		_RimSmoothMin("最小平滑度",Range(0,1)) = 0
		_RimSmoothMax("最大平滑度",Range(0,1)) = 1
		
		[Space(10)]
		[Header(Parallax Effect)]
		[Space(5)]
		_starcol("颜色 01", Color) = (1,1,1,1)
		_starmap("视差纹理 01 ",2D) = "black"{}
		_Parallax("视差深度 01", Range(0,3)) = 0.0
		[Space(5)]
		_starcol02("颜色 02", Color) = (1,1,1,1)
		_starmap02("视差纹理 02",2D) = "black"{}
		_Parallax02("视差深度 02", Range(0,3)) = 0.0
		[Space(5)]
		_starcol03("颜色 03", Color) = (1,1,1,1)
		_starmap03("视差纹理 03",2D) = "black"{}
		_Parallax03("视差深度 03", Range(0,3)) = 0.0

 
		_DisAmount("Dissolve Amount", Range(-2,2)) = 0.0
		_DirectionTex("Direction", 2D) = "white" {} 
		_DissolveNoiseScale("Noise Texture Scale", Range(0,5)) = 1
		_DissolveNoise("Noise Texture (RGB)", 2D) = "white" {} 
		[HDR]_DisColor("Dissolve Color", Color) = (1,1,1,1)
		_DisplacementWidth("Displacement Segment Width", Range(0,1)) = 0.3
		_HeightScale("Height Displacement Amount", Range(0,33)) = 0.0
		_DissolveCutoff("Noise Cutoff", Range(0,1)) = 0.5
		_Smoothness("Cutoff Smoothness", Range(0,2)) = 0.05

		//[HideInInspector][Toggle]_UniqueShadow("软阴影 UniqueShadow", float) = 0

	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" "Queue"="Geometry" }

		Pass
		{
			Tags { "LightMode"="ForwardBase" }
			Stencil
			{
				Ref 1
				Comp Always
				Pass Replace
			}
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			#pragma multi_compile_fwdbase  nolightmap nodirlightmap nodynlightmap novertexlight 
			#pragma skip_variants POINT POINT_COOKIE SHADOWS_CUBE SHADOWS_DEPTH

			#include "UnityCG.cginc"
			#include "AutoLight.cginc"
			//#include "UniqueShadow/UniqueShadow_ShadowSample.cginc"

			// Properties
			sampler2D		_MainTex;
			uniform float4	_Color;
			uniform float4	_EdgeColor;
			fixed _Brightness, _RimSmoothMin, _RimSmoothMax;

			float _Parallax, _Parallax02, _Parallax03;
			half4 _starcol, _starcol02, _starcol03;
			sampler2D _starmap, _starmap02, _starmap03;
			float4 _starmap_ST, _starmap02_ST, _starmap03_ST;

			//_DISSOLVEEFFECT
			sampler2D _DirectionTex;
			sampler2D _DissolveNoise;
			float _DisAmount, _DissolveNoiseScale;
			float _HeightScale; 
			float _DisplacementWidth;
			float _DissolveCutoff, _Smoothness;  
			float _DissolveColorWidth;
			half4  _DisColor; 
			
			struct a2v
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float4 tangent : TANGENT;
				float4 texcoord : TEXCOORD0;
			};



			struct v2f
			{
				float4 pos : SV_POSITION;
				float3 normal : NORMAL;

				float3 viewDir : TEXCOORD1;
				float3 worldPos : TEXCOORD2;
				float3 tangentViewDir : TEXCOORD3;
				float2 uv : TEXCOORD4;

				//UNITY_SHADOW_COORDS(5)
			};

			v2f vert(a2v v)
			{
				v2f o;

				UNITY_INITIALIZE_OUTPUT(v2f, o)	;

				float4 direction = tex2Dlod(_DirectionTex, float4(o.uv.xy,0.0,0.0));
				direction = direction + ( _DisAmount* (-1.2)) + 0.1; 
				float4 dispPosClamped = smoothstep(0, 0.15, direction) * smoothstep(direction, direction + 0.15, _DisplacementWidth);
				dispPosClamped *=  tex2Dlod(_DissolveNoise, float4(o.uv.xy,0.0,0.0)*_DissolveNoiseScale); 
				
				v.vertex.xyz +=  dispPosClamped.xyz * v.normal.xyz*_HeightScale;  
				o.pos = UnityObjectToClipPos(v.vertex);

				float4 normal4 = float4(v.normal, 0.0);
				o.normal = normalize(mul(normal4, unity_WorldToObject).xyz);
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos );

				o.uv = v.texcoord;

				TANGENT_SPACE_ROTATION;
				o.tangentViewDir = mul(rotation,ObjSpaceViewDir(v.vertex)).xyz;

				//UNITY_TRANSFER_SHADOW(o, v.texcoord.xy);//no use for v.uv1
				return o;
			}

			float4 frag(v2f i) : COLOR 
			{
				
				half3 viewNormal = mul(UNITY_MATRIX_V,i.normal);
				half3 viewPos = UnityWorldToViewPos(i.worldPos);
				half3 r = normalize(reflect(viewPos, viewNormal));
				half m = 2 * sqrt(r.x * r.x + r.y * r.y + (r.z + 1) * (r.z + 1));
				half2 matcapUV = r.xy/m + 0.5 ;
				
				half4 texColor = tex2D(_MainTex, matcapUV ) * _Brightness;

				half edgeFactor = abs(dot(i.viewDir, i.normal));
				edgeFactor = smoothstep(_RimSmoothMin, _RimSmoothMax,edgeFactor);

				half oneMinusEdge = 1.0 - edgeFactor;

				float3 rgb = (_Color.rgb * edgeFactor) + (_EdgeColor * oneMinusEdge);
				rgb = rgb * texColor.rgb;

				float2 baseUV = i.uv;
				float2 parallax = ParallaxOffset(0, _Parallax, i.tangentViewDir);
				fixed4 startex01 = tex2D(_starmap, baseUV * _starmap_ST.xy + parallax) * _starcol;

				float2 parallax02 = ParallaxOffset(0.15, _Parallax02, i.tangentViewDir);
				fixed4 startex02 = tex2D(_starmap02, (baseUV * _starmap02_ST.xy + _starmap02_ST.zw ) + parallax02) * _starcol02;

				float2 parallax03 = ParallaxOffset(0.25, _Parallax03, i.tangentViewDir);
				fixed4 startex03 = tex2D(_starmap03, (baseUV * _starmap03_ST.xy + frac(_Time.y  * _starmap03_ST.zw)) + parallax03) * _starcol03;

				startex01 = startex01 + startex02 + startex03;

				rgb += startex01.rgb;
				
				float3 noisetexture = tex2D(_DissolveNoise,baseUV* _DissolveNoiseScale); 
				float4 direction = tex2D(_DirectionTex,baseUV);
				direction = direction + (_DisAmount* (-1.2)) + 0.1;  
				float MovingPosOnModel =direction ;
				MovingPosOnModel *= noisetexture; 
				float maintexturePart = smoothstep(0, _Smoothness, MovingPosOnModel ); 
				float glowingPart = smoothstep(0, _Smoothness, MovingPosOnModel);
				
				glowingPart = step(_DissolveCutoff, maintexturePart); 
				clip( glowingPart  -0.01);
				glowingPart *= (1 - maintexturePart);  
				float4 glowingColored = glowingPart * _DisColor;
				
				rgb +=(glowingColored * noisetexture)*glowingPart;
				
				float4 output = float4(rgb, 1);

				return output;
			}

			ENDCG
		}

		Pass 
		{
			Tags{ "LightMode" = "ShadowCaster" }

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_shadowcaster
			#pragma fragmentoption ARB_precision_hint_fastest
			#include "UnityCG.cginc"
			
			struct v2f
			{
				V2F_SHADOW_CASTER;

			};

			v2f vert(appdata_base v) 
			{
				v2f o;
				TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
				return o;
			}

			float4 frag(v2f i) :SV_Target
			{
				SHADOW_CASTER_FRAGMENT(i)
			}

			ENDCG
		}


	}

}