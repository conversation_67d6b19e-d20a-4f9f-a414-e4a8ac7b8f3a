#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 3.0.12
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class AkMIDIPost : AkMIDIEvent {
  private global::System.IntPtr swigCPtr;

  internal AkMIDIPost(global::System.IntPtr cPtr, bool cMemoryOwn) : base(AkSoundEnginePINVOKE.CSharp_AkMIDIPost_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = cPtr;
  }

  internal static global::System.IntPtr getCPtr(AkMIDIPost obj) {
    return (obj == null) ? global::System.IntPtr.Zero : obj.swigCPtr;
  }

  internal override void setCPtr(global::System.IntPtr cPtr) {
    base.setCPtr(AkSoundEnginePINVOKE.CSharp_AkMIDIPost_SWIGUpcast(cPtr));
    swigCPtr = cPtr;
  }

  ~AkMIDIPost() {
    Dispose();
  }

  public override void Dispose() {
    lock(this) {
      if (swigCPtr != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          AkSoundEnginePINVOKE.CSharp_delete_AkMIDIPost(swigCPtr);
        }
        swigCPtr = global::System.IntPtr.Zero;
      }
      global::System.GC.SuppressFinalize(this);
      base.Dispose();
    }
  }

  public uint uOffset { set { AkSoundEnginePINVOKE.CSharp_AkMIDIPost_uOffset_set(swigCPtr, value); }  get { return AkSoundEnginePINVOKE.CSharp_AkMIDIPost_uOffset_get(swigCPtr); } 
  }

  public AKRESULT PostOnEvent(uint in_eventID, UnityEngine.GameObject in_gameObjectID, uint in_uNumPosts) {

	var in_gameObjectID_id = AkSoundEngine.GetAkGameObjectID(in_gameObjectID);
	AkSoundEngine.PreGameObjectAPICall(in_gameObjectID, in_gameObjectID_id);

    { return (AKRESULT)AkSoundEnginePINVOKE.CSharp_AkMIDIPost_PostOnEvent(swigCPtr, in_eventID, in_gameObjectID_id, in_uNumPosts); }
  }

  public void Clone(AkMIDIPost other) { AkSoundEnginePINVOKE.CSharp_AkMIDIPost_Clone(swigCPtr, AkMIDIPost.getCPtr(other)); }

  public static int GetSizeOf() { return AkSoundEnginePINVOKE.CSharp_AkMIDIPost_GetSizeOf(); }

  public AkMIDIPost() : this(AkSoundEnginePINVOKE.CSharp_new_AkMIDIPost(), true) {
  }

}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.