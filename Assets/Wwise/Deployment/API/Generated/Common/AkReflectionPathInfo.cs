#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 3.0.12
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class AkReflectionPathInfo : global::System.IDisposable {
  private global::System.IntPtr swigCPtr;
  protected bool swigCMemOwn;

  internal AkReflectionPathInfo(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  internal static global::System.IntPtr getCPtr(AkReflectionPathInfo obj) {
    return (obj == null) ? global::System.IntPtr.Zero : obj.swigCPtr;
  }

  internal virtual void setCPtr(global::System.IntPtr cPtr) {
    Dispose();
    swigCPtr = cPtr;
  }

  ~AkReflectionPathInfo() {
    Dispose();
  }

  public virtual void Dispose() {
    lock(this) {
      if (swigCPtr != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          AkSoundEnginePINVOKE.CSharp_delete_AkReflectionPathInfo(swigCPtr);
        }
        swigCPtr = global::System.IntPtr.Zero;
      }
      global::System.GC.SuppressFinalize(this);
    }
  }

  public AkVector imageSource { set { AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_imageSource_set(swigCPtr, AkVector.getCPtr(value)); } 
    get {
      global::System.IntPtr cPtr = AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_imageSource_get(swigCPtr);
      AkVector ret = (cPtr == global::System.IntPtr.Zero) ? null : new AkVector(cPtr, false);
      return ret;
    } 
  }

  public uint numPathPoints { set { AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_numPathPoints_set(swigCPtr, value); }  get { return AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_numPathPoints_get(swigCPtr); } 
  }

  public uint numReflections { set { AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_numReflections_set(swigCPtr, value); }  get { return AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_numReflections_get(swigCPtr); } 
  }

  public float level { set { AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_level_set(swigCPtr, value); }  get { return AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_level_get(swigCPtr); } 
  }

  public bool isOccluded { set { AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_isOccluded_set(swigCPtr, value); }  get { return AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_isOccluded_get(swigCPtr); } 
  }

  public static int GetSizeOf() { return AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_GetSizeOf(); }

  public AkVector GetPathPoint(uint idx) {
    global::System.IntPtr cPtr = AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_GetPathPoint(swigCPtr, idx);
    AkVector ret = (cPtr == global::System.IntPtr.Zero) ? null : new AkVector(cPtr, false);
    return ret;
  }

  public AkAcousticSurface GetAcousticSurface(uint idx) {
    AkAcousticSurface ret = new AkAcousticSurface(AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_GetAcousticSurface(swigCPtr, idx), false);
    return ret;
  }

  public float GetDiffraction(uint idx) { return AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_GetDiffraction(swigCPtr, idx); }

  public void Clone(AkReflectionPathInfo other) { AkSoundEnginePINVOKE.CSharp_AkReflectionPathInfo_Clone(swigCPtr, AkReflectionPathInfo.getCPtr(other)); }

  public AkReflectionPathInfo() : this(AkSoundEnginePINVOKE.CSharp_new_AkReflectionPathInfo(), true) {
  }

}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.