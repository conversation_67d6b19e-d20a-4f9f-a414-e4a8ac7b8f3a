using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ZGameChess
{
    public enum PlayModeType
    {
        Normal = 0, // 正常
        Turbo = 1, // turbo
        Dual_Normal = 2, //普通 双人
        Dual_TFT = 3, // tft 双人
        Team_4V4 = 4,
    }
    
    public enum PlayModeData
    {
        Normal = 0, // 正常
        Turbo = 1, // turbo
        Dual_Normal = 2, //普通 双人
        Dual_TFT = 3, // tft 双人
        ExtraType4 = 4, // 预留
        ExtraType5 = 5, // 预留
        ExtraType6 = 6, // 预留
    }

    
    /// <summary>
    /// 比较运算符
    /// </summary>
    public enum CompareMethod
    {
        EqualTo = 1, // ==
        GreaterThan = 2, // >
        LessThan = 3, // <
        GreaterOrEqualTo = 4, // >=
        LessOrEqualTo = 5, // <=
        NotEqual = 6, // !=
        RemainDer = 7, //  Left % Right == Right1
        Contains = 8, //  包含
        NotContains = 9, // 不包含
    }

    /// <summary>
    /// 前置条件的判断属性
    /// </summary>
    public enum CompareParmaType
    {
        TurnCount = 1, // 总回合数
        PlayerLevel = 2, // 玩家等级
        HeroAttr = 3, //属性
        Fetter = 4,  //羁绊
        RealWaitHeroCount = 5, //备战区真实单位
        ConWinCount = 6, // 连胜
        ConLoseCount = 7, //连败
        PlayerMoney = 8, // 玩家金币
        // 配置为9则会初始化到重回数据中，
        // 在添加配置9的condition时会初始化需要统计的类型,之后才会计算对应达成羁绊回合数
        // 不连续达成羁绊次数
        ActiveFetter_NotContinue = 9,
        AllWaitHeroCount = 10, // 备战区所有单位
        RefreshCount = 11, // 本回合刷新次数
        UpdateLevelCount = 12, // 本回合升级经验次数
        FetterLevel = 13, // 羁绊等级
        DeductedLife = 14, // 扣血总量
        ForeverRecord = 15, //全局永久变量数据
        PlayerLife = 16, //玩家血量
        HADataCount = 17, //玩家海克斯数量
        EquipCount = 18, //某装备的数量 
        StageCount = 19, //阶段数
        StageTurnCount = 20, //阶段回合数
        FetterCountMaxNum = 21, //最大羁绊英雄
        BattleHeroCount = 22, //战场上英雄数量
        PlayerEquipmentsCount = 23, //玩家装备数   []
        TriggerParam = 24, //触发器附带参数
        AllPlayerDeductLife = 25, //所有玩家记录的扣血量
        PlayerPropNoChangeCount = 26, //玩家某种属性未变化的回合数
        LivePlayerCount = 27, //存活玩家数
        PreConWinCount = 28, //上回合的连胜场数
        PreConLoseCount = 29, //上回合的连败场数
        BreakWinLose = 30, //打断连败或连胜
        Rank = 31, //玩家rank
        PlayerFetterHeroCount = 32, //玩家某羁绊英雄数

        TargetQualityHeroCount = 1000, 
        BuffLayers = 1001, // buff层数   buffid
    }

    /// <summary>
    /// HexJobTypeEnum
    /// </summary>
    public enum BattleRulesJobType
    {
        RecastPlayerEquips = 1, // 某种时机是否需要重铸装备
        AddPlayerFreeRefreshCount = 2,// 某种时机赠送的免费刷新次数
        AddMoney = 3, // 赠送金币
        AddExp = 4 ,// 增加经验
        AddLife = 5, // 增加生命
        DoHexDrop = 6, // 掉落
        AddMoneyWithoutMonster = 7, // 野怪回合不生效的增加金币   [todo:  待删除]  对应改成3 加野怪回合限制
        RecastWaitHero = 8, // 将备战区最右侧的若干棋子变为同费用棋子
        AddHeroAttrs = 9, // 永久增加英雄属性  
        AddMoneyWithDropBox = 10, // 用球掉钱  
        BuffLayersJob = 11, // buff层数在做一些事情  
        OneTimeBuffJob = 12, //一回合buff
        AllGameBuffJob = 13, //全局buff
        AddLifeAllCount = 14, // 增加生命，野怪回合以及龙之秘宝都可以 [todo : 待删除] 对应改成5
        FakeSummon = 15, // 准备阶段的一些特殊召唤
        UpGradeEquip = 16, // 升级普通装备为光明装备
        DoHexDropByDeductedLife = 17, // DoHexDropByDeductedLife
        CreateTerrainGrid = 18, // 创建地形格子
        OpenGraveyardEquipStore = 19, // 坟场
        ChangePlayerRefreshMoney = 20, // 改变玩家的刷新金钱
        NoLimitedRefresh = 21, // 填快速思考参数
        FreeGetBuyHeroViewHero = 22, //获取购买面板上英雄质量最高的那一个
        AddAnEmblem = 23, //获得一个纹章
        OpenEquipStore = 24, //SpecialRule 打开装备商店
        FixedAddFakeHero = 25, //   添加假人
        DropSameHero = 26, //   掉落固定品质的英雄
        HeroCountChangeJob = 27, //   場上英雄变化时的通知, S9.5大头和波比彩蛋
        SetMoneyJob = 28, //   设置金币，超出变为经验， 可扩展
        DoHexDropAllPlayer = 29, // 所有活着的玩家一起掉落
        SetForeverRecordDataJob = 30, //   设置全局变量数据
        CashOutJob = 31, //   存活玩家瓜分金币
        SellAllHeroJob = 32, //售卖所有英雄  
        OpenTimedEquipStore = 33, //打开计时性质的商店  
        PlayOutfieldViewEff = 34, //场外单位播放特效  
        HologramCopyJob = 35, //Hologram  3772  赛季之星会有一个复制体  
        AddDragonAggJob = 36, //龙蛋
        
        RandomDropFetterHero = 40, //随机掉落某个英雄的同羁绊英雄  
        RandomDropEnemyHero = 41, //随机掉落敌方一个棋子 
        
        ChangeHeroProp = 42, //改变英雄永久属性 
        ChangeHeroRecord = 43, //改变英雄永久变量 

        
        
        Eight_bit_FinalReward = 100, //8_bit  最终奖励

        #region s11 job

        CopyBuyHeroJob = 200, //购买棋子后获得相同的棋子
        UpLvlJob = 201, //提升玩家等级
        CopyNewEquipment = 202, //获得新装备时额外掉落一件一样的
        RecastAllHeroJob = 203, //重新随机所有英雄，并且拆掉装备
        AddOneTimeBuff = 204, //添加临时buff
        ExtraInterestToExpJob = 205, //利息超过的N部分转换为N*M的经验
        BanWaitHeroPosJob = 206, //选择后的下一阶段禁用备战区格子
        ReuseEquipmentJob = 207, // N回合不用的装备会被随机为L 等级的装备
        AndEnemyDropJob = 208, //你和你的对手都掉落N
        CalFetterHeroCountToGiveReward = 209, //统计对应位置的英雄数量来给予奖励
        AddHeroWithSameFetterJob = 210, //掉落一个主英雄，并掉落该英雄对应羁绊的随机费用英雄
        DeductedEnemyLifeDropJob = 211, //
        GetExaltedHeroJob = 212, // s11至高天英雄掉落
        AddPlayerRefreshMoneyJob = 213, // 增加玩家刷新金币的任务
        SetHeroForeverRecordDataJob = 214, // 改变英雄永久变量【适用于参数是heroentity的任务】

        #endregion
        SoulEquipStore = 1001, //   灵魂装备商店   参数1 2 3 4， id 1 id2 id3 id4
    }

    
    /// <summary>
    /// 特殊规则种类
    /// </summary>
    public enum SpecialRuleType
    {
        EquipmentStore = 1,
        StarUpAddRadCombatEffect = 2,//升星时随机添加效果 参数:minStar|maxStar|SpecClassType|classId|effId1;effId2;effId3
        AddTerrainGridNum = 3,//增加地形格子数量 参数:num|TerrainGridCfgGroupId
        RandomHighLevelHero = 4,// 战场英雄随机成不同的英雄 参数:higherLevel
        AddFreeRefreshCount = 5,// 添加免费刷新次数
        SetTerrainGridShareRange = 6,// 设置地形格子分享范围 参数:范围|TerrainGridCfgGroupId
        AddHP = 7,// 直接添加生命值
        AddDragonEgg = 8, //添加龙蛋 level|turn|龙蛋confid
        YordleExtraEquip = 9, //约德尔召唤带装备
        FetterNoCntLimit = 10, //解除羁绊数量限制 羁绊对应key
        NoLimitedRefresh = 11, //本轮免费刷新
        AddTerrainGrid = 12,    //添加地形
        UnstableRift = 13,//不稳定裂隙
        CorruptedRoundSelect = 14,//故障轮抽
        AddExp = 15,//添加经验
        RoundSelectRelease = 16, //轮抽施放
        ImpromptuInventions = 17, //极客（战斗开始时英雄身上的散件变成成装）
        HolyDropCtl = 18, //神圣法球相关

        AstraCtrl_Ctrl = 20, //星系控制
        CommonSummon_Ctrl = 21 , //召唤效果
        DragonTrainer_Ctrl = 22, //驯龙师
        LuckyGloves = 23, //幸运手套
        HeavenSelect_Ctrl = 24, //天选机制相关
        WaitHero_Ctrl = 25, //备战区效果

        PlayerCtrl = 26, //控制器交互
        ForeverRecordCounter = 27, //永久变量是否需要记录
        CastAllPlayerEquip = 28, //将玩家所有的装备重铸为其他装备   1:2:3:4 | poolid     1 2 3 4为装备等级
        ReplaceDropPool = 29, //替换掉落池
        AddFakeHeroWithEquipment = 30, //加个带装备的假人 召唤id|掉落池子|装备件数|combateffect
        LimitHeroEquipment = 31, //限制英雄装备数量
        //促销星系
        BuyPriceReduce = 32,
        CastAllPlayerEquipWithDrop = 33, // 将玩家所有的装备重铸为其他装备 1:2:3:4 | poolid     1 2 3 4为装备等级， 包含后续新增的装备
        FutureInvest = 34, // s3.5未来战士特殊羁绊hex
        WishList = 35, //s3.5愿望商店
        BilgeWaterDrop = 36, //s9.5比尔吉沃特宝箱掉落
        RatTownStore = 37, //老鼠城幸运商店
        HeartCollector = 38, //s10钢之心特殊效果
        SellAllHeroJob = 39, //售卖所有英雄  
        FateCtrl = 40,//天命控制器
        FortuneCtrlS11 = 41, //S11福星
        EncounterRoundselect = 42, //奇遇轮抽
        SetPlayerLevel = 44,//设置玩家等级
        DropTurnLimitEquipment = 45, //掉落有回合次数的装备
        SetConWinLoseCount = 46,//设置玩家连胜连败数
        
        TimedEquipmentStore = 50,
        SetForeverValueFromRandom = 60, // 从填的所有羁绊id中随机一个写入指定永久变量中
        
        InitPlayerNotChangeProp = 100, //开始记录玩家的某些没有变化的属性的回合
        
        ChangePlayerHATurn = 500, //改变玩家海克斯轮次   [是否是相同随机] | [轮次1] | [轮次2] 
        ChangePlayerHARefreshTimes = 501 ,//改变玩家海克斯刷新次数   [刷新次数] | [生效回合数] 
        GetPlayerNexturnHA = 502 ,//立即获得下一轮海克斯 [随意参数]
        FixedHexCfg = 503 ,//某一轮海克斯配置
        
        ChooseHexStore = 601 ,//打开一个可以选择两个海克斯效果的商店

        /**********S6羁绊海克斯**********/
        S6_GeekHexRule = 6001,
        S6_MercenaryHexRule = 6002,//赏金猎人
        
        
        

        MixEff = 10000, //复合效果
			
    }
    
    public enum PlayerExtraFlow
    {
        LockLife = 10001,
        HeroCountChange = 10002,
        LifeDeduct = 10003,
        ChooseHex = 10004,
        ComposeEquip = 10005,
        ComposeEquipOnHero = 10006,
        ComposeEquipOnPanel = 10007,
        PlayerDead = 10008,
        LevelUp2StarHero = 10009,
        LevelUp3StarHero = 10010,
        PlayerLevelUp = 10011,
        Get2StarHero = 10012, //获得2星
        Get3StarHero = 10013, //获得3星
        PlayerCostRefresh = 10014, // 玩家使用非免费刷新时
        PlayerFreeRefresh = 10015, // 玩家使用免费刷新时
        PlayerClickRefresh = 10016, // 玩家点击刷新时  不区分是否免费
        AutoRefresh = 10017, // 自动刷新时
        LevelUpHero = 10018, // 英雄升星时，不区分2 3 星
        OnAddHero = 10019, // 备战席或者战场区域 添加了棋子会触发
        LifeDeductDelay = 10020, // 扣血后用的触发器
        DeductEnemyLife = 10021, // 扣除敌人血量触发
        OPT_BUY_POST = 10022, //购买英雄后
        PlayerAddNewEquip = 20000, // 玩家获得一件新的装备
    }

    public enum DelayGetType
    {
        HeroEquip = 0,//直接装备在假人身上
        FakeHeroWithEquip = 1, //生成带装备的假人
        TurnLimitFakeHero = 2, //生成带回合数限制的假人
    }
    
    
    /// <summary>
    /// 回合类型  海克斯配置用
    /// </summary>
    public enum HexQuestType
    {
        PVP = 0, // 与玩家对战
        PVE = 1, // 野怪回合
        RoundSelect = 2, //轮抽回合
        Dragon = 3, // 龙之秘宝 //todo:
    }

    public enum JobExecutorType
    {
        Self = 0,
        All = 1,
        WithEnemy = 2,
    }
    
    /// <summary>
    /// 海克斯触发器枚举类型（大枚举）
    /// </summary>
    public enum BattleRulesTriggerJobEnum
    {
        PlayerOPT = 0,
        TurnState = 1,
        BattleResult = 2,
    }
    /// <summary>
    /// 回合类型枚举，用于回合限制
    /// </summary>
    public enum SpecialTurnType
    {
        NoneType = 0, // 不care
        Monster = 1, // 野怪
        Dragon = 2, //龙之密宝
    }
    /// <summary>
    ///  回合数限制枚举\
    /// </summary>
    public enum CheckTurnType
    {
        Before = 0, // 前一回合
        Current = 1, // 当前回合
        After = 2, // 下一回合
    }
    
    public enum JobParamType
    {
        Int = 0,
        ListInt = 1,
        Format = 2,
    }


    public enum ParamFormatType
    {
        ForeverRecord = 0,
        ConditionParam = 1,
        ConditionID = 2,
    }

    #region 选秀轮抽的投票枚举

    public enum RoundSelectVoteType
    {
        None = 0, // 
        Galaxy = 1, // 星系投票
        Effect = 2, // 特殊效果投票
    }

    /// <summary>
    /// 触发器对应的参数枚举
    /// </summary>
    public enum TriggerConditionEnum
    {
        // 英雄相关  0 - 1000
        HeroSpec  = 0,
        HeroClass = 1,
        SameHeroCount = 2, // 玩家相同英雄数
        HeroQuality = 3, // 英雄品质
                
        // 刷新相关 1001 - 2000
        RefreshKey = 1001,
    }


    /// <summary>
    /// 玩家一些基础属性枚举
    /// </summary>
    public enum PlayerBaseProp
    {
        Level = 0, //等级
    }
    #endregion



    #region 奇遇事件

    public enum EncounterEventMsg
    {
        AllGameData = 0,
        InserdTurnData = 1,
        PlayAnim = 2,
        StopAnim = 3,
        EncounterEff = 4,
        RemoveEncounterEff = 5,
        RemoveEncounterEffAll = 6,
        PlayerAlwaysDance = 7,
        KillingGame = 8,
        UI = 9,
        TurnEff = 10,
        Banner = 11,
        HideMainActor = 12,
        HideEff = 13,
    }


    public enum EncounterSpecialRule
    {
        ChooseHex = 1,
        GameEndTurn = 2,
        EncounterDraft = 3,
    }

    public enum EncounterRoundSelectType
    {
        Hero = 1,
        ExtraEquipment = 2,
        Equipment = 3,
        Irelia = 4,
    }

    public enum EncounterHeroBiome
    {
        None = 0,
        Moutain = 1,
        Water = 2,
        Sky = 3,
        Cave = 4,
        
        Special1 = 9999,
    }

    public enum JKSegmentEnum
    {
        CommonRoundselectLeave = -10,
        CommonRoundselectEnd = -5,
        CommonRoundselectEndAutoSelect = -4,
        CommonRoundselectStart = -1,
        
        
        VoteRoundSelect = 1,
        
        EC_Declaration = 1000,
        EC_FishingGame = 1001,
        EC_DancingGame = 1002,
        EC_KillingGame = 1003,
        
        
    }
    #endregion
}


