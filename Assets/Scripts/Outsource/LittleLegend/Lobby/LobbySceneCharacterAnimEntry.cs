using UnityEngine;
using TKFrame;
public class LobbySceneCharacterAnimEntry : TKBehaviour
{
    public enum EmCharacterAnimatorObj2System
    {
        em_CharacterAnimatorObj2Sys_None = 0,
        em_CharacterAnimatorObj2Sys_Role_Hero = 1,
        em_CharacterAnimatorObj2Sys_Role_Chanzi = 2,
        em_CharacterAnimatorObj2Sys_Role_ExhibitHero = 3,

        em_CharacterAnimatorObj2Sys_Role_Hero_LongPressed = 15,
        em_CharacterAnimatorObj2Sys_Role_Hero_Mall_LongPressed = 16,

        em_CharacterAnimatorObj2Sys_Max
    }
    //

    public EmCharacterAnimatorObj2System emSceneObj2SysVal;
    private Animation _aniSceneBall;

    protected override void Awake()
    {
        base.Awake();
    }

    protected override void Start()
    {
        base.Start();


    }

    protected override void OnDestroy()
    {


        base.OnDestroy();
    }

}
