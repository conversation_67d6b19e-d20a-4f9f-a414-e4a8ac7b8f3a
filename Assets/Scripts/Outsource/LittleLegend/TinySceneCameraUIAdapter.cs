using Cinemachine;
using NTween;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

namespace ZGameChess
{
    public class TinySceneCameraUIAdapter : MonoBehaviour
    {
        public enum UIType
        {
            None = 0,
            排行榜 = 1,
            商城 = 2,
            藏品 = 3,
            BattlePass = 4,
            个人信息 = 5,
            结算_第一名 = 6,
            结算_BattlePass = 7,
            获得新英雄 = 8,
            联盟抽奖 = 9,
            J科技 = 10,
            奖池 = 11,
            赛事兑换商店 = 12,
            赛事宝典 = 13,
            推荐页 = 15,
            魔典赛季奖励 = 16,

            镜头居中 = 1000,
            镜头靠右 = 1001,
            镜头靠左 = 1002,
        }

        public enum ScreenType
        {
            兼容所有屏幕,
            平板电脑,
            普通手机,
            全面屏手机,
        }

        [Serializable]
        public class CameraInfo
        {
            public string condition;
            public UIType uiType;
            public ScreenType screenType = ScreenType.兼容所有屏幕;
            public Vector3 position;
            public Vector3 rotation;
        }

        [Serializable]
        public class CameraDefaultPos       // 相机默认机位
        {
            // 条件名称
            public string condition;
            public Vector3 position;
            public Vector3 rotation;
            public float fov;
            public float fadeInTime;
            public float fadeOutTime;
            public bool autoAdapter = true;     // 机位是否启用自动适配
        }

        public string m_prefabName; // 原始prefabName

        // 相机默认机位
        public List<CameraDefaultPos> m_cameraDefaultPos = new List<CameraDefaultPos>();

        public List<CameraInfo> m_cameraInfo = new List<CameraInfo>();

        // 自适应算法参数
        public bool m_autoAdapter = true;           // 是否开启自动适配
        public bool m_adapterDistance = true;      // 是否调整相机距离（有些有雾效的不能调整）
        public Vector3 m_adapterOffset = Vector3.zero;  // 相机偏移校正 （针对那些动作里面带偏移的）
        public float m_cameraFadeOutTime = 1.0f;    // 从特殊镜头恢复到默认镜头的时间
        public float m_ipadFovOffset = 6f;          // ipad屏幕下 fov缩放

        public bool m_useRTCustomXOffset = false;       // RT模式下是否需要自定义偏移值
        public float m_rtXOffset = 0f;               // RT模式下的左右偏移值

        protected TinyModelPreviewSceneInst m_inst;
        protected TinySceneUIType m_curType = TinySceneUIType.None;
        protected TinySceneCameraType m_cameraType = TinySceneCameraType.Center;
        float m_axes;
        protected bool m_inited = false;
        protected Vector3 m_defaultCameraPos;
        protected Quaternion m_defaultCameraRot;
        protected float m_defaultFov = 35;
        protected bool m_needAdapterCameraPos = false;
        protected int m_screenWidth;
        protected int m_screenHeight;

        protected HashSet<string> m_conditions = new HashSet<string>();

        protected CameraDefaultPos m_lastDefaultCameraPos = null;

        public Camera GetCamera()
        {
            if (m_inst != null && m_inst.SceneCamera != null)
                return m_inst.SceneCamera;
            else
                return GetComponent<Camera>();
        }

        public ArtCameraSyncLittleLegend GetArtCameraSyncLittleLegend()
        {
#if UNITY_EDITOR
            if (m_inst != null && m_inst.Animator != null)
                return m_inst.Animator.GetComponent<ArtCameraSyncLittleLegend>();
            else
#endif
                return null;
        }

        public void InitDefaultCameraParam(TinyModelPreviewSceneInst inst)
        {
            if (m_inited)
                return;
            m_inited = true;

            m_inst = inst;
            var camera = GetCamera();
            m_defaultCameraPos = camera.transform.localPosition;
            m_defaultCameraRot = camera.transform.localRotation;
            if (camera != null)
                m_defaultFov = camera.fieldOfView;

            Diagnostic.Log($"[Tiny InitCamera] m_defaultCameraPos: {m_defaultCameraPos} m_defaultCameraRot: {m_defaultCameraRot} m_defaultFov: {m_defaultFov}");
            ACGEventManager.Instance.AddEventListener("OnAndroidScreenSizeChange", AdapterListener);
        }

        public void OnDisable()
        {
            if (m_inited)
            {
                m_lastDefaultCameraPos = null;
                m_conditions.Clear();
                ResetCameraPos(false);
            }
        }

        public void UpdateDefaultCameraParam(Vector3 cameraPosition, Quaternion cameraRotation, float fov, bool withAnim)
        {
            m_defaultCameraPos = cameraPosition;
            m_defaultCameraRot = cameraRotation;
            m_defaultFov = fov;

            Diagnostic.Log($"[Tiny UpdateCamera] m_defaultCameraPos: {m_defaultCameraPos} m_defaultCameraRot: {m_defaultCameraRot} m_defaultFov: {m_defaultFov}");

            AdapterCameraPosImpl(withAnim);
        }

#if UNITY_EDITOR
        public void AdapterCameraPosInEditor()
        {
            if (!AdapterCameraPos((UIType)m_curType, false))
                ResetCameraPos(false);
        }
#endif


        private void AdapterListener(GEvent e)
        {
            if (gameObject != null && gameObject.activeSelf)
                AdapterCameraPosByCurType();
        }

        public void AdapterCameraPosByCurType()
        {
            AdapterCameraPosImpl(true);
        }

        #region 自适应适配方案

        public void AutoAdapterCameraPos(TinySceneCameraType cameraType, bool adapterDistance, float axes, Vector3 offset, bool withAnim)
        {
            if (m_curType == TinySceneUIType.JReward)
            {
                adapterDistance = false;
            }

            var sceneCamera = GetCamera();
            Vector3 oldPosition = sceneCamera.transform.localPosition;
            Quaternion oldRot = sceneCamera.transform.localRotation;
            float oldFov = sceneCamera.fieldOfView;

            ResetCameraPos(false);

            //0.优先拿里面挂点的位置 这样算的准确点
            Vector3 pos = m_inst.SpawnPoint.position;
            if (m_inst.HangPoint != null)
            {
                var loc = m_inst.HangPoint.__GetReallyPosData(CharacterHangPoint.SupportHangPointType.GROUND_LOC);
                if (loc == null || loc.bindTrans == null)
                {
                    if (loc == null || loc.bindTrans == null)
                    {
                        loc = m_inst.HangPoint.GetPosData(CharacterHangPoint.SupportHangPointType.SPINE_LOC);
                    }
                }

                if (loc != null && loc.bindTrans != null)
                {
                    pos = loc.bindTrans.position;
                }
            }
            //1.将【出生点-世界坐标】转化到【相机-坐标系】位置
            Vector3 vec3PosCameraSpace = sceneCamera.transform.InverseTransformPoint(pos);
            //2.计算目标位置，即映射到屏幕宽度的3/4位置
            float cameraDistance = vec3PosCameraSpace.z;
            float fValuePos = CalculatePos(cameraDistance, axes, sceneCamera);
            float xOffset = fValuePos - vec3PosCameraSpace.x;
            //3.转换成世界坐标
            vec3PosCameraSpace = sceneCamera.transform.TransformPoint(new Vector3(-xOffset, 0, 0));
            //4.将偏移加回到相机上
            sceneCamera.transform.position = vec3PosCameraSpace;

            if (cameraType != TinySceneCameraType.Center && adapterDistance && m_inst != null && m_inst.TinyGo != null)
            {
                //5.计算模型大小
                var skinMeshs = m_inst.TinyGo.GetComponentsInChildren<SkinnedMeshRenderer>();
                if (skinMeshs != null)
                {
                    Bounds b = new Bounds();
                    foreach (var skinMesh in skinMeshs)
                    {
                        if (b.size.Equals(Vector3.zero))
                            b = skinMesh.bounds;
                        else
                            b.Encapsulate(skinMesh.bounds);
                    }

                    //6.将模型体积转换成相机坐标系
                    Vector3 meshMin = sceneCamera.transform.InverseTransformPoint(b.min);
                    Vector3 meshMax = sceneCamera.transform.InverseTransformPoint(b.max);

                    float meshMinX = Mathf.Min(meshMin.x, meshMax.x);
                    float meshMaxX = Mathf.Max(meshMin.x, meshMax.x);
                    //7.计算相机最大最小显示范围
                    float cameraMin = 0;
                    float cameraMax = CalculatePos(cameraDistance, 1, sceneCamera);
                    //8.计算超框部分
                    float detla = 0;
                    if (meshMinX < cameraMin)
                        detla = cameraMin - meshMinX;

                    if (meshMaxX > cameraMax)
                        detla = meshMaxX - cameraMax;
                    //9.将超框部分转换成相机远近的偏移量
                    if (detla > 0)
                    {
                        float fFovHalf = sceneCamera.fieldOfView / 2;
                        float fTanValue = Mathf.Tan(fFovHalf * Mathf.Deg2Rad);
                        float distance = detla / fTanValue;
                        //10.转换成世界坐标
                        vec3PosCameraSpace = sceneCamera.transform.TransformPoint(new Vector3(0, 0, -distance));
                        //11.将偏移加回到相机上
                        sceneCamera.transform.position = vec3PosCameraSpace;
                        //12.重新再匹配一下3/4的位置
                        vec3PosCameraSpace = sceneCamera.transform.InverseTransformPoint(pos);
                        cameraDistance = vec3PosCameraSpace.z;
                        fValuePos = CalculatePos(cameraDistance, axes, sceneCamera);
                        xOffset = fValuePos - vec3PosCameraSpace.x;
                        vec3PosCameraSpace = sceneCamera.transform.TransformPoint(new Vector3(-xOffset, 0, 0));
                        sceneCamera.transform.position = vec3PosCameraSpace;
                    }
                }
            }

            if (offset != Vector3.zero)
                sceneCamera.transform.position += offset;

            if (withAnim)
            {
                Vector3 newPosition = sceneCamera.transform.localPosition;
                Quaternion newRot = sceneCamera.transform.localRotation;
                float newFov = sceneCamera.fieldOfView;

                sceneCamera.transform.localPosition = oldPosition;
                sceneCamera.transform.localRotation = oldRot;
                sceneCamera.fieldOfView = oldFov;

                float fadeOutTimeS = m_cameraFadeOutTime;
                var tw = GameTween.DOLocalMove(sceneCamera.gameObject, oldPosition, newPosition, 0.0f,
                    fadeOutTimeS, 1, UITweener.Style.Once);
                tw.ignoreTimeScale = false;
                tw = GameTween.DOLocalRotate(sceneCamera.gameObject, oldRot, newRot, 0.0f,
                    fadeOutTimeS, 1, UITweener.Style.Once, UITweener.Method.Linear, null);
                tw.ignoreTimeScale = false;
                tw = GameTween.DOFov(sceneCamera.gameObject, oldFov, newFov, 0.0f, fadeOutTimeS);
                tw.ignoreTimeScale = false;
            }
        }

        //由【张角-FOV】、【横纵比-Aspect】、【距摄像机距离-fDistance】反推【摄像机视椎体宽度插值】
        public float CalculatePos(float fDistance, float posPercent, Camera sceneCamera)
        {
            float fFovHalf = sceneCamera.fieldOfView / 2;
            float fWSlashH = sceneCamera.aspect;
            float fTanValue = Mathf.Tan(fFovHalf * Mathf.Deg2Rad);
            float fHeightHalf = fDistance * fTanValue;
            float fWidthHalf = fWSlashH * fHeightHalf;

            return (fWidthHalf * 2) * posPercent - fWidthHalf;
        }

        #endregion

        public void AdapterCameraPosImpl(bool withAnim)
        {
            if (!AdapterCameraPos((UIType)m_curType, withAnim))
            {
                if (!AdapterCameraPos((UIType)((int)m_cameraType + 1000), withAnim))
                {
                    if (m_cameraType != TinySceneCameraType.Center && NeedAutoAdapter())
                    {
                        AutoAdapterCameraPos(m_cameraType, m_adapterDistance, m_axes, m_adapterOffset, withAnim);
                    }
                    else
                    {
                        ResetCameraPos(withAnim);
                    }
                }
            }
        }

        public void AdapterCameraPos(TinySceneUIType type, TinySceneCameraType cameraType, float axes, bool withAnim)
        {
            SetAdapterCameraParam(type, cameraType, axes);

            AdapterCameraPosImpl(withAnim);
        }

        public void SetAdapterCameraParam(TinySceneUIType type, TinySceneCameraType cameraType, float axes)
        {
            m_curType = type;
            m_cameraType = cameraType;
            m_axes = axes;
        }

        //public Vector3 GetCameraOffset()
        //{
        //    return transform.localPosition - m_defaultCameraPos;
        //}

        //public float GetFOVOffset()
        //{
        //    var oprCamera = GetCamera();
        //    if (oprCamera != null)
        //        return oprCamera.fieldOfView - m_defaultFov;
        //    return 0;
        //}

        public void SetCondition(string condition, bool active)
        {
            if (active && !m_conditions.Contains(condition))
                m_conditions.Add(condition);
            if (!active && m_conditions.Contains(condition))
                m_conditions.Remove(condition);
        }

        public bool NeedAutoAdapter()
        {
            for (int i = 0; i < m_cameraDefaultPos.Count; ++i)
            {
                var defaultPos = m_cameraDefaultPos[i];
                if (m_conditions.Contains(defaultPos.condition))
                {
                    return defaultPos.autoAdapter;
                }
            }
            return m_autoAdapter;
        }

        public void GetDefaultCameraPos(out Vector3 pos, out Quaternion rot, out float fov, out float fadeInTime)
        {
            pos = m_defaultCameraPos;
            rot = m_defaultCameraRot;
            fov = m_defaultFov;
            fadeInTime = m_lastDefaultCameraPos != null && m_lastDefaultCameraPos.fadeOutTime > 0 ? m_lastDefaultCameraPos.fadeOutTime : m_cameraFadeOutTime;
            m_lastDefaultCameraPos = null;
            for (int i = 0; i < m_cameraDefaultPos.Count; ++i)
            {
                var defaultPos = m_cameraDefaultPos[i];
                if (m_conditions.Contains(defaultPos.condition))
                {
                    pos = defaultPos.position;
                    rot = Quaternion.Euler(defaultPos.rotation);
                    fov = defaultPos.fov;
                    if (defaultPos.fadeInTime > 0)
                        fadeInTime = defaultPos.fadeInTime;
                    m_lastDefaultCameraPos = defaultPos;
                    break;
                }
            }

            bool isIpad = NotchSizeImp.IsPad || NotchSizeImp.IsIpadSpecialScreen();
            if (isIpad)
                fov = fov + m_ipadFovOffset;
        }

        public void DoCameraFadeOut()
        {
            var oprCamera = GetCamera();
            if (oprCamera == null)
                return;

            GetDefaultCameraPos(out Vector3 pos, out Quaternion rot, out float fov, out float fadeOutTimeS);

            //float fadeOutTimeS = m_cameraFadeOutTime;
            var tw = GameTween.DOLocalMove(oprCamera.gameObject, oprCamera.transform.localPosition, pos, 0.0f,
                fadeOutTimeS, 1, UITweener.Style.Once);
            tw.ignoreTimeScale = false;
            tw = GameTween.DOLocalRotate(oprCamera.gameObject, oprCamera.transform.localRotation, rot, 0.0f,
                fadeOutTimeS, 1, UITweener.Style.Once, UITweener.Method.Linear, null);
            tw.ignoreTimeScale = false;
            tw = GameTween.DOFov(oprCamera.gameObject, oprCamera.fieldOfView, fov, 0.0f, fadeOutTimeS);
            tw.ignoreTimeScale = false;
        }

        public void ResetCameraPos(bool withAnim)
        {
            var camera = GetCamera();
            if (camera != null)
            {
                if (withAnim)
                {
                    DoCameraFadeOut();
                }
                else
                {
                    GetDefaultCameraPos(out Vector3 pos, out Quaternion rot, out float fov, out float fadeOutTimeS);

                    camera.transform.localPosition = pos;
                    camera.transform.localRotation = rot;
                    camera.fieldOfView = fov;

                    Diagnostic.Log($"[Tiny ResetCameraPos] camera.transform.localPosition: {camera.transform.localPosition} camera.transform.localRotation: {camera.transform.localRotation}  camera.fieldOfView: {camera.fieldOfView}");
                }
            }
        }

        protected bool AdapterCameraPos(UIType uiType, bool withAnim)
        {
            bool isIpad = NotchSizeImp.IsPad || NotchSizeImp.IsIpadSpecialScreen();
            ScreenType screenType = ScreenType.兼容所有屏幕;
            if (isIpad)
            {
                screenType = ScreenType.平板电脑;
            }
            else
            {
                bool is18_9 = TKScreenUIScaler.CheckWideScreen();
                if (is18_9)
                {
                    screenType = ScreenType.全面屏手机;
                }
                else
                {
                    screenType = ScreenType.普通手机;
                }
            }

            bool findWithCondition = m_cameraDefaultPos.Count > 0;      // 说明有不同的机位 这里也要跟随不同的机位去设置
            bool find = false;
            for (int i = 0; i < m_cameraInfo.Count; ++i)
            {
                var info = m_cameraInfo[i];
                if (info.uiType == uiType && info.screenType == screenType)
                {
                    if (TryAdapterCameraPos(info, withAnim, findWithCondition))
                    {
                        find = true;
                        break;
                    }
                }
            }

            if (!find)
            {
                for (int i = 0; i < m_cameraInfo.Count; ++i)
                {
                    var info = m_cameraInfo[i];
                    if (info.uiType == uiType && info.screenType == ScreenType.兼容所有屏幕)
                    {
                        if (TryAdapterCameraPos(info, withAnim, findWithCondition))
                        {
                            find = true;
                            break;
                        }
                    }
                }
            }

            if (find)
            {
                var camera = GetCamera();
                if (camera != null)
                {
                    float fov;
                    if (isIpad)
                        fov = m_defaultFov + m_ipadFovOffset;
                    else
                        fov = m_defaultFov;
                    if (!withAnim)
                    {
                        camera.fieldOfView = fov;
                    }
                    else
                    {
                        float fadeOutTimeS = m_cameraFadeOutTime;
                        var tw = GameTween.DOFov(camera.gameObject, camera.fieldOfView, fov, 0.0f, fadeOutTimeS);
                        tw.ignoreTimeScale = false;
                    }
                }
            }

            return find;
        }

        private bool TryAdapterCameraPos(CameraInfo info, bool withAnim, bool findWithCondition)
        {
            if (findWithCondition)
            {
                if (m_conditions.Count > 0)  // 机位开启中
                {
                    if (!string.IsNullOrEmpty(info.condition) && m_conditions.Contains(info.condition))
                    {
                        AdapterCameraPos(info, withAnim);
                        return true;
                    }
                }
                else // 默认机位
                {
                    if (string.IsNullOrEmpty(info.condition))
                    {
                        AdapterCameraPos(info, withAnim);
                        return true;
                    }
                }
            }
            else
            {
                AdapterCameraPos(info, withAnim);
                return true;
            }
            return false;
        }

        public void AdapterCameraPos(CameraInfo cameraInfo, bool withAnim)
        {
            var camera = GetCamera();
            if (!withAnim)
            {
                camera.transform.localPosition = cameraInfo.position;
                camera.transform.localRotation = Quaternion.Euler(cameraInfo.rotation);
            }
            else
            {
                float fadeOutTimeS = m_cameraFadeOutTime;
                var tw = GameTween.DOLocalMove(camera.gameObject, camera.transform.localPosition, cameraInfo.position, 0.0f,
                    fadeOutTimeS, 1, UITweener.Style.Once);
                tw.ignoreTimeScale = false;
                tw = GameTween.DOLocalRotate(camera.gameObject, camera.transform.localRotation, Quaternion.Euler(cameraInfo.rotation), 0.0f,
                    fadeOutTimeS, 1, UITweener.Style.Once, UITweener.Method.Linear, null);
                tw.ignoreTimeScale = false;
            }
        }

        public void RecordCurrentCameraInfoTo(CameraInfo cameraInfo)
        {
            cameraInfo.position = transform.localPosition;
            cameraInfo.rotation = transform.localRotation.eulerAngles;
        }

        public void RecordCurrentCameraDefaultPosTo(CameraDefaultPos cameraDefaultPos)
        {
            cameraDefaultPos.position = transform.localPosition;
            cameraDefaultPos.rotation = transform.localRotation.eulerAngles;
            cameraDefaultPos.fov = GetComponent<Camera>().fieldOfView;
        }

        private void OnDestroy()
        {
            ACGEventManager.Instance.RemoveEventListener("OnAndroidScreenSizeChange", AdapterListener);
        }
    }
}
