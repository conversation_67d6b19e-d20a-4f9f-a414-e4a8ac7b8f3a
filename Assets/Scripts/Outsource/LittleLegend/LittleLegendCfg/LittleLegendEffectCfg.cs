using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

[Serializable]
[LittleLegendDesc("特效", "特效组件")]
public class LittleLegendEffectCfg : LittleLegendActionTriggerCfg
{
    //光效预制体
    public GameObject effect;

    //光效挂点
    public CharacterHangPoint.SupportHangPointType effectHangingPoint;

    #region 场景特效相关
    //场景特效 挂点在场景的0,0位置 (只有局外小小英雄才有这种东西)
    public bool sceneEffect = false;
    #endregion

    #region 挂点特效相关
    //是否随骨骼移动
    public bool bMoveByBone = true;

    //是否随骨骼旋转
    public bool bRotateByBone = true;

    //是否随角色缩放
    public bool bScaleWithCharacter = true;
    #endregion

    #region 通用属性
    //是否循环播放
    public bool IsLoop;

    //是否唯一
    public bool IsAlone = false;

    //是否自动清理
    public bool bCleanWhenTransition = true;

    //指定删除动画的动作状态
    public string stateCleanWhenTransition = "";
    #endregion

    public override void AddToCfg(LittleLegendActionCfg actionCfg)
    {
        base.AddToCfg(actionCfg);

        actionCfg.m_effectCfgList.Add(this);
    }

    public override void RemoveFromCfg(LittleLegendActionCfg actionCfg)
    {
        base.RemoveFromCfg(actionCfg);

        actionCfg.m_effectCfgList.Remove(this);
    }
}

