using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

[Serializable]
[LittleLegendDesc("音效", "音效组件")]
public class LittleLegendSoundCfg : LittleLegendActionTriggerCfg
{
    public string bankName;

    public string soundName;

    [NonSerialized]
    public int whiteActionMask = 0;

    public List<int> whiteActionHashList = new List<int>();     // 遇到这些动作 不清理当前声音

    public bool loopTrigger = false;

    public override void AddToCfg(LittleLegendActionCfg actionCfg)
    {
        base.AddToCfg(actionCfg);

        actionCfg.m_soundCfgList.Add(this);
    }

    public override void RemoveFromCfg(LittleLegendActionCfg actionCfg)
    {
        base.RemoveFromCfg(actionCfg);

        actionCfg.m_soundCfgList.Remove(this);
    }
}

