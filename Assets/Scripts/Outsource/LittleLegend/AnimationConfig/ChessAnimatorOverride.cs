using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

namespace ZGameChess
{
    [RequireComponent(typeof(Animator))]
    public class ChessAnimatorOverride : MonoBehaviour
    {
        public List<AnimationClip> m_overrideClips = new List<AnimationClip>();

        private void Awake()
        {
            var animator = GetComponent<Animator>();
            if (animator != null && animator.runtimeAnimatorController != null && animator.runtimeAnimatorController.animationClips != null)
            {
                AnimatorOverrideController overrideController = new AnimatorOverrideController(animator.runtimeAnimatorController);
                TKDictionary<string, AnimationClip> orginClipDict = new TKDictionary<string, AnimationClip>();
                for (int i = 0; i < animator.runtimeAnimatorController.animationClips.Length; ++i)
                {
                    var clip = animator.runtimeAnimatorController.animationClips[i];
                    if (clip != null)
                    {
                        var clipName = clip.name;
                        if (clipName.Contains("@"))
                            clipName = clipName.Split('@')[1];

                        if (!orginClipDict.ContainsKey(clipName))
                            orginClipDict.Add(clipName, clip);
                        else
                            TKFrame.Diagnostic.Warn("clipName: " + clipName + " already exist!");
                    }
                }
                for (int i = 0; i < m_overrideClips.Count; ++i)
                {
                    var clip = m_overrideClips[i];
                    var clipName = clip.name;
                    if (clipName.Contains("@"))
                        clipName = clipName.Split('@')[1];
                    if (orginClipDict.TryGetValue(clipName, out AnimationClip orginClip))
                        overrideController[orginClip] = clip;
                }
                animator.runtimeAnimatorController = overrideController;
            }
        }
    }
}
