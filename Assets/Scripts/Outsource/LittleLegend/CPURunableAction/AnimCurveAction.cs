using System;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;

namespace ZGameChess
{
    // 这玩意有毒 以后记得改... 
    class AnimCurveAction : BaseCPURunableAction
    {
        private Vector3 _dir;
        private Vector3 _startPoint;
        private Vector3 _lastFramePoint;
        private ChessPlayerUnit _target;
        private float _lifeTime;
        private float _life;
        private AnimationCurve _curve;
        private Transform _transform;
        private bool _isBody = false;

        public AnimCurveAction(ChessPlayerUnit target, Vector3 dir, float time, AnimationCurve curve, bool isBody, Action actionEndCB = null)
        {
            _ActionEndCB = actionEndCB;
            _target = target;
            _dir = dir;
            _isBody = isBody;

            if (_isBody)
                _transform = target.BodyTransform;
            else
                _transform = target.transform;

            _life = _lifeTime = time;
            _curve = curve;

            if( _life <= Mathf.Epsilon )
            {
                _life = _lifeTime = 2.0f;
                _curve = null;
            }

            _startPoint = _transform.localPosition;
            _lastFramePoint = _startPoint;

            //Diagnostic.Log("[AnimCurveAction] begin ");
        }

        public override void Run(float timeSecond)
        {
            base.Run(timeSecond);

            _lifeTime -= timeSecond;
            if(_lifeTime > 0.0f)
            {
                float a = _life - _lifeTime;
                if(_curve != null)
                {
                    a = _curve.Evaluate(a);
                }
                var newPoint = _startPoint + _dir * a;
                var delta = newPoint - _lastFramePoint;

                float height = 0.0f;
                if (_isBody)
                {
                    height = newPoint.y;
                }
                else
                {
                    height = _target.lowestHeight + newPoint.y;
                }
                
                float deltaY = height - _transform.localPosition.y;
                delta.y = deltaY;
                if (_isBody)
                {
                    _transform.localPosition += delta;
                }
                else
                {
                    _target.MoveTo(delta);
                }
                _target.FallToGround();

                _lastFramePoint = newPoint;
                //_target.transform.position += _dir * timeSecond * 0.80f;
            }
            else
            {
                this.Ended = true;
                //Diagnostic.Log("[AnimCurveAction] end ");
            }

            //if (_target.IsPlayingAnimStr("hurt_02"))
            //{
            //    _target.transform.position += _dir * timeSecond * 0.80f;
            //    _target.transform.position = ChessPlayerUnit.CheckBound(_target.transform.position);
            //}
            //else
            //{
            //    this.Ended = true;
            //}
        }

        protected override void End()
        {
            base.End();

            // 不这么做 小小英雄会越飞越高
            if (_isBody)
                _transform.localPosition = _startPoint;
        }
    }
}
