using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using ZGame;
using static ZGameChess.ChessUtil;
using ZGameClient;

namespace ZGameChess
{
    public enum ELoadResMode
    {
        OnlyLocal,      // 仅从本地加载
        OnlyCOS,        // 仅从COS加载
        LocalFirst,     // 本地加载优先
        COSFirst,       // COS加载优先
    }


    public class ModelPathUtil
    {
        public enum ModelLod
        {
            None,
            Low,
            High,
            Pad,
        }

        // GM命令设置的模型LOD
        public static ModelLod GlobalModelLod = ModelLod.None;

        public static readonly string[] LODTAGLIST = new string[1] { "_low" };

        public static string GetLodTag(string abName)
        {
            for (int i = 0; i < LODTAGLIST.Length; ++i)
                if (abName.EndsWith(LODTAGLIST[i]))
                    return LODTAGLIST[i];
            return string.Empty;
        }

        public static ELoadResMode GetMapLoadMode(int id)
        {
#if UNITY_EDITOR
            if (AssetBundleManager.SimulateAssetBundleInEditor)
                return ELoadResMode.OnlyLocal;
#endif
            return ELoadResMode.LocalFirst;
        }

        public static bool GetMapAssetPath(ref string scene_path, string scene_name, out bool fromCos, ELoadResMode resMode = ELoadResMode.COSFirst)
        {
            if (resMode == ELoadResMode.COSFirst)
            {
                if (COS.SettingPrefs.GetInt(TKFrame.COSResource.COSSettingPrefs.COS_BUNDLE_ENABLE, 1) != 1)     // 如果没开 就用本地加载
                {
                    resMode = ELoadResMode.LocalFirst;
                }
            }
            return GetAssetPath(resMode, ref scene_path, scene_name, out fromCos);
        }

        #region 小小英雄
        public static ELoadResMode GetTinyLoadMode(int id)
        {
#if UNITY_EDITOR
            if (AssetBundleManager.SimulateAssetBundleInEditor)
                return ELoadResMode.OnlyLocal;
#endif
            //var tinyCfg = DataBaseManager.Instance.SearchTinyHero(id);
            //if (tinyCfg != null)
            //{
            //    var baseCfg = DataBaseManager.Instance.SearchTinyHero(tinyCfg.iConditionId);
            //    if (baseCfg != null)
            //    {
            //        var cosLittleLegendCfg = COSLittleLegendCfg.GetInstance();
            //        ResLoadSourece loadResource = ResLoadSourece.FromPackage;
            //        if (cosLittleLegendCfg != null)
            //            loadResource = cosLittleLegendCfg.GetLoadSource(baseCfg.iID);
            //        if (loadResource != ResLoadSourece.FromPackage)
            //            return ELoadResMode.COSFirst;
            //    }

            //}
            return ELoadResMode.LocalFirst;
        }

        public static bool GetLittleLegendSceneAssetPath(ref string scene_path, string scene_name, out bool fromCos, ELoadResMode resMode = ELoadResMode.COSFirst)
        {
            if (resMode == ELoadResMode.COSFirst)
            {
                if (COS.SettingPrefs.GetInt(TKFrame.COSResource.COSSettingPrefs.COS_BUNDLE_ENABLE, 1) != 1)     // 如果没开 就用本地加载
                {
                    resMode = ELoadResMode.LocalFirst;
                }
            }
            return GetAssetPath(resMode, ref scene_path, scene_name, out fromCos);
        }

        /// <summary>
        /// 获取小小英雄的路径
        /// </summary>
        /// <param name="inGame">是否是局内低模小小英雄</param>
        /// <param name="model_path"></param>
        /// <param name="model_name"></param>
        /// <param name="fromCos"></param>
        /// <returns></returns>
        public static bool GetLittleLegendAssetPath(ref string model_path, string model_name, out bool fromCos, ELoadResMode resMode = ELoadResMode.COSFirst)
        {
            if (resMode == ELoadResMode.COSFirst)
            {
                if (COS.SettingPrefs.GetInt(TKFrame.COSResource.COSSettingPrefs.COS_BUNDLE_ENABLE, 1) != 1)     // 如果没开 就用本地加载
                {
                    resMode = ELoadResMode.LocalFirst;
                }
            }
            //ELoadResMode resMode = ELoadResMode.LocalFirst;
            //if (forceUseCos)
            //    resMode = ELoadResMode.OnlyCOS;
            //else if (!inGame && COS.SettingPrefs.GetInt(TKFrame.COSResource.COSSettingPrefs.COS_BUNDLE_ENABLE, 1) == 1) // 局外&开启COS加载，优先用COS上的
            //    resMode = ELoadResMode.COSFirst;
            //else if (inGame)    // 局内只用本地的
            //    resMode = ELoadResMode.OnlyLocal;

            return GetAssetPath(resMode, ref model_path, model_name, out fromCos);
        }

        // 无后缀说明这个英雄没有lod
        private static List<string> ms_assetHighSuffixList = new List<string>() { "", "_low" };
        private static bool GetHighAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_assetHighSuffixList, model_name, out fromCos);
        }

        private static List<string> ms_assetLowSuffixList = new List<string>() { "_low", "" };
        private static bool GetLowAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_assetLowSuffixList, model_name, out fromCos);
        }
        // 获取小小英雄的lod资源
        public static bool GetAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            switch (GlobalModelLod)
            {
                case ModelLod.None:  // 正常对局进入的话 走这里
                    {
                        if (HardwareCheck.GetMemoryGB() <= 2.1f)
                        { // 小于等于2G的机器 就不要用高模了 老老实实低模
                            return GetLowAssetPath(loadModel, ref model_path, model_name, out fromCos);
                        }
                        else
                        {
                            var dp = GameLOD.Instance.DevicePower;
                            if (dp == EDevicePower.EDP_Low || dp == EDevicePower.EDP_Middle)
                            {
                                return GetLowAssetPath(loadModel, ref model_path, model_name, out fromCos);
                            }
                            else
                            {
                                return GetHighAssetPath(loadModel, ref model_path, model_name, out fromCos);
                            }
                        }
                    }
                case ModelLod.Low:
                    return GetLowAssetPath(loadModel, ref model_path, model_name, out fromCos);
                case ModelLod.High:
                case ModelLod.Pad:
                    return GetHighAssetPath(loadModel, ref model_path, model_name, out fromCos);
                default:
                    break;
            }
            fromCos = false;
            return false;
        }
#endregion

        private static bool GetHeroAssetPath(ref string model_path, string suffix, string model_name, bool fromCos)
        {
            string ab = string.IsNullOrEmpty(suffix) ? model_path : model_path + suffix;
            if (!fromCos)
            {
                if (AssetBundleManager.CheckAssetExist(ab, model_name))
                {
                    model_path = ab;
                    return true;
                }
            }
            else
            {
                if (COS.ABAssetManager.CheckAssetbundleExist(ab))
                {
                    model_path = ab;
                    return true;
                }
            }
            return false;
        }

        private static bool GetHeroAssetPathFromList(ref string model_path, List<string> suffixs, string model_name, bool fromCos)
        {
            for (int i = 0; i < suffixs.Count; ++i)
            {
                if (GetHeroAssetPath(ref model_path, suffixs[i], model_name, fromCos))
                {
                    return true;
                }
            }
            return false;
        }

        private static bool GetHeroAssetPathFromList(ELoadResMode loadModel, ref string model_path, List<string> suffixs, string model_name, out bool fromCos)
        {
            if (loadModel == ELoadResMode.COSFirst)
            {
                // 优先从COS加载 其次从本地加载
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, true))
                {
                    fromCos = true;
                    return true;
                }
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, false))
                {
                    Diagnostic.Warn("load model from local! model_path: {0} model_name: {1}", model_path, model_name);
                    fromCos = false;
                    return true;
                }
            }
            else if (loadModel == ELoadResMode.LocalFirst)
            {
                // 优先从本地加载 其次从COS加载
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, false))
                {
                    fromCos = false;
                    return true;
                }
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, true))
                {
                    Diagnostic.Warn("load model from cos! model_path: {0} model_name: {1}", model_path, model_name);
                    fromCos = true;
                    return true;
                }
            }
            else if (loadModel == ELoadResMode.OnlyCOS)
            {
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, true))
                {
                    fromCos = true;
                    return true;
                }
            }
            else if (loadModel == ELoadResMode.OnlyLocal)
            {
                if (GetHeroAssetPathFromList(ref model_path, suffixs, model_name, false))
                {
                    fromCos = false;
                    return true;
                }
            }
            fromCos = false;
            return false;
        }

        // 无后缀说明这个英雄没有lod
        private static List<string> ms_highSuffixList = new List<string>() { "_high", "_low", "" };
        private static bool GetHighHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_highSuffixList, model_name, out fromCos);
        }

        private static List<string> ms_lowSuffixList = new List<string>() { "_low", "_high", "" };
        private static bool GetLowHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_lowSuffixList, model_name, out fromCos);
        }

        private static List<string> ms_padSuffixList = new List<string>() { "_pad", "_high", "_low", "" };
        private static bool GetPadHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPathFromList(loadModel, ref model_path, ms_padSuffixList, model_name, out fromCos);
        }

        public static bool GetHeroAssetPath(ref string model_path, string model_name, out bool fromCos)
        {
            return GetHeroAssetPath(ChessBattleGlobal.Instance.LoadResFromCos ? ELoadResMode.COSFirst : ELoadResMode.LocalFirst, ref model_path, model_name, out fromCos);
        }

        public static bool GetHeroAssetPath(ELoadResMode loadModel, ref string model_path, string model_name, out bool fromCos)
        {
            switch (GlobalModelLod)
            {
                case ModelLod.None:  // 正常对局进入的话 走这里
                    {
                        if (HardwareCheck.GetMemoryGB() <= 2.1f)
                        { // 小于等于2G的机器 就不要用高模了 老老实实低模
                            return GetLowHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                        }
                        else if (NotchSizeImp.IsPad)
                        {
                            return GetPadHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                        }
                        else
                        {
                            var dp = GameLOD.Instance.DevicePower;
                            if (dp == EDevicePower.EDP_Low || dp == EDevicePower.EDP_Middle)
                            {
                                return GetLowHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                            }
                            else
                            {
                                return GetHighHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                            }
                        }
                    }
                case ModelLod.Low:
                    return GetLowHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                case ModelLod.High:
                    return GetHighHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                case ModelLod.Pad:
                    return GetPadHeroAssetPath(loadModel, ref model_path, model_name, out fromCos);
                default:
                    break;
            }
            fromCos = false;
            return false;
        }

        /// <summary>
        /// 获取英雄的模型AB资源路径
        /// </summary>
        public static void GetHeroAssetPath(int tabId, out string model_path, out string model_name, int playerId, out bool fromCos)
        {
            TACG_Hero_Client heroTab = ChessModelManager.Instance.GetBattleModel().SoGameData_View.GetAutoChessHeroInfoByID(tabId);
            if (heroTab == null)
            {
                model_name = "";
                model_path = Model_Path;
                fromCos = false;
                return;
            }

            string modelName = GetHeroSkinModelName(heroTab.sPrefabShowID, tabId, playerId);

            int setId = heroTab != null ? heroTab.iSetNum : 1;
            string modelBasePath = Model_Path + ResPath.GetSetVersion(modelName, setId) + modelName.ToLower();

            GetHeroAssetPath(ChessBattleGlobal.Instance.LoadResFromCos ? ELoadResMode.COSFirst : ELoadResMode.LocalFirst, ref modelBasePath, modelName, out fromCos);

            model_name = modelName;
            model_path = modelBasePath;
        }

        public static void SplitAssetPath(string path, out string ab_path, out string ab_name)
        {
            FastStringSplit strs = path.BeginSplit('|');
            if (strs.Length == 2)
            {
                ab_path = strs[0];
                ab_name = strs[1];
            }
            else
            {
                ab_path = "";
                ab_name = "";

                //Diagnostic.Error("SplitAssetPath: " + path);
            }

            strs.EndSplit();

            if (LocalizationData.Instance.GetCurrentLanguage() != SystemLanguage.Chinese)
            {
                if (ab_path.Contains("_cn") && ab_name.Contains("zh_cn_"))
                {
                    ab_path = ab_path.Replace("_cn", "_en");
                    ab_name = ab_name.Replace("zh_cn_", "");
                }
            }
        }
    }
}