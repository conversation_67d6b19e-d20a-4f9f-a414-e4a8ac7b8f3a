#if ACGGAME_CLIENT
using System;
using NTween;
using TKFrame;
using UnityEngine;
using ZGameChess;

/// <summary>
/// 艺术镜头同步;
/// 统称用于使用美术在3DMax等软件里K好的动画同步到游戏中使用;
/// 而这些软件的fov会和unity有差异，所以要进行部分数值的转换;
/// created: xfilsonpan
/// date:2020-6-17
/// </summary>
public class ArtCameraSyncBase : MonoBehaviour
{
    protected bool isStarted = false;

    protected bool isPlayingNewState = false;
    //累计的时间;
    protected float sumTime = 0.0f;
    protected float delayS = 0.0f;
    protected float durationS = 0.0f;
    protected Animator artAnimator;
    protected string artAnimationName;
    protected int artAnimationNameShortHash;
    protected Camera oprCamera;
    protected bool isLocHasChanged = false;
    protected Transform locPosTransNode;
    protected Transform locFovTransNode;
    protected Camera artNodeCamera;
    protected float fadeOutTimeS;
    protected bool ingoreTransstion;

    protected Vector3 recordWorldPos;
    protected Quaternion recordRotation;
    protected float recordFov;

    protected float filmWidth = 36.0f;
    protected float filmHeight = 24.0f;

    public bool IsStarted
    {
        get { return isStarted; }
    }

    public Transform LocPosTransNode { get { return locPosTransNode; } }
    public Transform LocFovTransNode { get { return locFovTransNode; } }

    public virtual bool CheckIsValid(Animator animator)
    {
        bool isValid = false;
        do
        {
            if (animator == null) break;
            this.DecodeNode(animator);
            if (this.locPosTransNode == null) break;
            if (this.locFovTransNode == null) break;
            isValid = true;
        } while (false);

        return isValid;
    }

    public void StartSync(Animator artAnimator, string artAnimationName, Camera oprCamera, float delayMs, float durationMs, float fadeOutTimeS = 0.0f, bool ingoreTransstion = false)
    {
        // 播放镜头动画前屏蔽相机控制
        var camZoom = oprCamera.gameObject.GetComponent<CameraPanAndZoom>();
        if (camZoom != null)
        {
            camZoom.enabled = false;
        }

        this.oprCamera = oprCamera;
        this.recordWorldPos = this.oprCamera.transform.position;
        this.recordRotation = this.oprCamera.transform.rotation;
        this.recordFov = this.oprCamera.fieldOfView;

        this.artAnimator = artAnimator;
        this.artAnimationName = artAnimationName;
        this.artAnimationNameShortHash = Animator.StringToHash(this.artAnimationName);
        this.fadeOutTimeS = fadeOutTimeS;
        this.ingoreTransstion = ingoreTransstion;

        this.sumTime = 0.0f;
        this.delayS = delayMs / 1000.0f;
        this.durationS = durationMs / 1000.0f;
        this.isStarted = true;
        this.isPlayingNewState = false;
        this.artAnimator.Play(this.artAnimationName);
    }

    protected void DecodeNode(Animator animator)
    {
        //this.locPosTransNode = animator.transform.Find("root/root_r/Cam_loc");
        this.locPosTransNode = CommonUtil.FindTransform(animator.transform, "cam_loc");
        if (locPosTransNode == null)
            locPosTransNode = CommonUtil.FindTransform(animator.transform, "Cam_loc");
        if (locPosTransNode == null)
            locPosTransNode = CommonUtil.FindTransform(animator.transform, "Cam_Loc");
        //this.locFovTransNode = animator.transform.Find("root/root_r/Cam_Fov");
        this.locFovTransNode = animator.transform.Find("Cam_Fov");
        if (locFovTransNode == null)
            locFovTransNode = CommonUtil.FindTransform(animator.transform, "Cam_Fov");

        if (this.locPosTransNode != null)
        {
            this.artNodeCamera = this.locPosTransNode.gameObject.TryGetComponent<Camera>();
            this.artNodeCamera.enabled = false;
        }
        else
        {
            Diagnostic.Log($"ArkCameraSyncBase DecodeNode goName:{gameObject.name} animatorName:{animator.name} 没有找到cam_loc节点，请检查");
        }
    }

    protected virtual void LateUpdate()
    {
        //是否启动了;
        if (!this.isStarted) return;
        //是否还是播放的指定动作;
        int curPlayingNameHash = this.artAnimator.GetCurrentAnimatorStateInfo(0).shortNameHash;
        if (curPlayingNameHash != this.artAnimationNameShortHash)
        {
            this.isStarted = false;
            this.OnFadeOut();
            return;
        }
        if (ingoreTransstion && artAnimator.IsInTransition(0))
        {
            this.isStarted = false;
            this.OnFadeOut();
            return;
        }
        if (!this.isPlayingNewState)
        {
            this.isPlayingNewState = true;
            if (this.durationS < 0)
            {
                this.durationS = this.artAnimator.GetCurrentAnimatorStateInfo(0).length;
            }
        }
        //只有正在播放到对应动作才开始计算;
        if (!this.isPlayingNewState) return;
        //累积时间增加;
        this.sumTime += Time.deltaTime;
        //是否可以同步了;
        if ((this.sumTime > this.delayS) && (this.durationS < 0 || this.sumTime <= this.durationS))
        {
            //if (oprCamera != null)
            //    Diagnostic.Log($"[ArtCameraSyncBase] RequestSyncData 0 {Time.frameCount} position: {oprCamera.transform.position} rotation: {oprCamera.transform.rotation} fieldOfView: {oprCamera.fieldOfView}");

            this.RequestSyncData();

            //if (oprCamera != null)
            //    Diagnostic.Log($"[ArtCameraSyncBase] RequestSyncData 1 {Time.frameCount} position: {oprCamera.transform.position} rotation: {oprCamera.transform.rotation} fieldOfView: {oprCamera.fieldOfView}");
        }
        //是否超过持续时间了;
        if (this.durationS > 0 && this.sumTime > this.durationS)
        {
            this.isStarted = false;
            this.OnFadeOut();
            return;
        }
    }

    protected virtual void OnFadeOut()
    {
        if (fadeOutTimeS < 0)
        {
            return;
        }
        if (oprCamera == null)
        {
            return;
        }
        if (fadeOutTimeS > 0)
        {
            GameTween.DOWorldMove(this.oprCamera.gameObject, this.oprCamera.transform.position, this.recordWorldPos, 0.0f,
                this.fadeOutTimeS, 1, UITweener.Style.Once);
            GameTween.DOWorldRotate(this.oprCamera.gameObject, this.oprCamera.transform.rotation, this.recordRotation, 0.0f,
                this.fadeOutTimeS, 1, UITweener.Style.Once, UITweener.Method.Linear, null, TweenWorldRotation.RotateMode.Fast);
            GameTween.DOFov(this.oprCamera.gameObject, this.oprCamera.fieldOfView, this.recordFov, 0.0f, this.fadeOutTimeS);

            Diagnostic.Log($"[Tiny Restore Camera] anim recordWorldPos: {oprCamera.transform.position} recordRotation: {oprCamera.transform.rotation.eulerAngles} recordFov: {oprCamera.fieldOfView}");
        }
        else
        {
            oprCamera.transform.position = recordWorldPos;
            oprCamera.transform.rotation = recordRotation;
            oprCamera.fieldOfView = recordFov;

            Diagnostic.Log($"[Tiny Restore Camera] recordWorldPos: {oprCamera.transform.position} recordRotation: {oprCamera.transform.rotation.eulerAngles} recordFov: {oprCamera.fieldOfView}");
        }

        //Diagnostic.Log($"[ArtCameraSyncBase] {Time.frameCount} OnFadeOut position: {oprCamera.transform.position} rotation: {oprCamera.transform.rotation} fieldOfView: {oprCamera.fieldOfView}");

        //播完镜头动画后恢复镜头控制
        //var camZoom = oprCamera.gameObject.GetComponent<CameraPanAndZoom>();
        //if (camZoom != null)
        //{
        //    camZoom.enabled = true;
        //}
    }

    //private void Update()
    //{
    //    if (oprCamera != null)
    //        Diagnostic.Log($"[ArtCameraSyncBase] {Time.frameCount} position: {oprCamera.transform.position} rotation: {oprCamera.transform.rotation} fieldOfView: {oprCamera.fieldOfView}");
    //}

    public void ForceFadeOut()
    {
        fadeOutTimeS = 0;
        OnFadeOut();
    }

    protected virtual void OnAnimationEnd()
    {
        GameTween.DOFov(this.oprCamera.gameObject, this.oprCamera.fieldOfView, this.recordFov, 0.0f, this.fadeOutTimeS);
    }

    protected virtual void RequestSyncData()
    {
        if (!this.isLocHasChanged && this.locPosTransNode != null && !this.locPosTransNode.localPosition.Equals(Vector3.zero))
        {
            this.isLocHasChanged = true;
        }

        if (isLocHasChanged)
        {
            this.ReallySyncData();
        }
    }

    protected virtual void ReallySyncData()
    {

    }

    #region Unity => maya
    // 将Unity中相机的Fov值转换到maya中相机中的焦距值
    public float RecoveryFocalLength(float fov)
    {
        var scale = 16f / 9f;
        return filmWidth / (Mathf.Tan(fov / 2 * Mathf.Deg2Rad) * scale) / 2f;
    }

    public Vector3 RecoveryUnityRotationToMayaRotation(Quaternion camRotation)
    {
        Quaternion rightHandRotation = new Quaternion(camRotation.x, -camRotation.y, -camRotation.z, camRotation.w);
        Quaternion rotationY = Quaternion.AngleAxis(180, Vector3.up);
        rightHandRotation *= rotationY;

        Vector3 finalEulerAngles = QuaternionToEulerForOrderXYZ(rightHandRotation);

        return finalEulerAngles;
    }

    public Vector3 QuaternionToEulerForOrderXYZ(Quaternion rotation)
    {
        Quaternion q = rotation.normalized;
        //setup all needed values
        float[] d = { q.x * q.x, q.x * q.y, q.x * q.z, q.x * q.w, q.y * q.y, q.y * q.z, q.y * q.w, q.z * q.z, q.z * q.w, q.w * q.w };

        //Float array for values needed to calculate the angles
        float[] v = { 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f };

        const float singularityCutoff = 0.499999f;
        Func<float, float, float>[] qFunc = new Func<float, float, float>[3];
        qFunc[0] = qAtan2;
        qFunc[1] = qAsin;
        qFunc[2] = qAtan2;

        v[6] = d[2] - d[6];
        v[0] = 2.0f * (d[5] + d[3]);
        v[1] = d[7] - d[4] - d[0] + d[9];
        v[2] = -1.0f;
        v[3] = 2.0f * v[6];

        if (Mathf.Abs(v[6]) < singularityCutoff)
        {
            v[4] = 2.0f * (d[1] + d[8]);
            v[5] = d[0] - d[7] - d[4] + d[9];
        }
        else //x == zxz x == 0
        {
            float a, b, c, e;
            a = d[2] - d[6];
            b = d[5] + d[3];
            c = d[2] + d[6];
            e = -d[5] + d[3];

            v[4] = a * e + b * c;
            v[5] = b * e - a * c;
            qFunc[0] = qNull;
        }

        Vector3 eulerAngles = new Vector3(qFunc[0](v[0], v[1]), qFunc[1](v[2], v[3]), qFunc[2](v[4], v[5])) * Mathf.Rad2Deg;
        return eulerAngles;
    }

    private float qAsin(float a, float b)
    {
        return (float)(a * Math.Asin(Mathf.Clamp(b, -1.0f, 1.0f)));
    }

    private float qAtan2(float a, float b)
    {
        return (float)Math.Atan2(a, b);
    }

    private float qNull(float a, float b)
    {
        return 0;
    }
    #endregion

    protected virtual void OnDestroy()
    {
        if (this.oprCamera != null)
        {
            GameTween.DoKill(this.oprCamera.transform);
        }
    }
}
#endif