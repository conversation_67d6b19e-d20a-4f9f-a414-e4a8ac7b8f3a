#if ACGGAME_CLIENT
using System.Collections.Generic;
using DG.Tweening;
using UnityEditor;
using UnityEngine;
using Action = System.Action;
using CameraSkillActionPreset = CameraSkillPathConfig.CameraSkillActionPreset;
using CameraSkillSegmentPreset = CameraSkillPathConfig.CameraSkillSegmentPreset;
/// <summary>
/// 镜头技能路路径
/// 用法:Play("test1", Camera.main.transform, GameObject.Find("Cube2").transform);
/// created:xfilsonpan
/// date:2020-5-7
/// </summary>
public class CameraSkillPath : MonoBehaviour
{

    #region 子类
    /// <summary>
    /// 镜头的技能动作;
    /// </summary>
    public class CameraSkillAction
    {
        //相对坐标原点对象;
        private Transform refTarget;
        //操作的目标;
        private Transform oprTarget;
        //注释的焦点;
        private Transform lookAtTarget;
        private bool needLookAt = true;

        public CameraSkillActionPreset actionPreset;
        //当前播放的片段索引;
        public int curIndex { get; private set; } = 0;
        public bool toggleForDebugShow = false;
        public bool toggleShowInSceneGUI = true;

        private Action allCompleteAction;
        
        public Tweener cameraPathTweener { get; private set; }

        public CameraSkillAction()
        {
            
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="refTarget"></param>
        /// <param name="oprTarget"></param>
        /// <param name="lookAtTarget">看准的目标transform</param>
        /// <param name="fixedLookAtTargetPos">固定的焦点位置(只有fixedTransform类型时才会用到;)</param>
        /// <param name="allCompleteAction"></param>
        public void Play(Transform refTarget, Transform oprTarget, Transform lookAtTarget, Action allCompleteAction = null, int segmentIndex = 0)
        {
            this.Reset();
            this.refTarget = refTarget;
            this.oprTarget = oprTarget;
            //自定义焦点比较特殊一点，要自行创建一个焦点出来;
            this.lookAtTarget = lookAtTarget;
            this.allCompleteAction = allCompleteAction;
            this.RequestStartTransitionIn(segmentIndex);
        }

        private void RequestStartTransitionIn(int segmentIndex)
        {
            this.curIndex = -1;
            GameTween.DoKill(oprTarget);
            this.ReallyStartTransitionIn(segmentIndex);
        }

        private void ReallyStartTransitionIn(int segmentIndex)
        {
            this.PlaySegment(segmentIndex);
        }

        public void GetSegmentFirstLookAtTargetWorldPos(int index, out Vector3 oprTargetWorldPos){
            CameraSkillSegmentPreset segmentPath = this.actionPreset.segmentList[index];
            //优化第一个点的位置;
            if (actionPreset.IsGlobal)
            {
                oprTargetWorldPos = this.refTarget.TransformPoint(segmentPath.posList[0]);
            }
            else
            {
                //转换下坐标系到世界坐标;
                oprTargetWorldPos = this.refTarget.TransformPoint(segmentPath.posList[0]);
            }
        }

        private void PlaySegment(int index)
        {
            this.curIndex = index;
            if (this.IsPlaying)
            {
                this.oprTarget.DOKill();
            }

            CameraSkillSegmentPreset segmentPath = this.actionPreset.segmentList[this.curIndex];
            if (!segmentPath.IsEmpty)
            {
                Vector3 oprTargetWorldPos;
                this.GetSegmentFirstLookAtTargetWorldPos(this.curIndex, out oprTargetWorldPos);
                this.oprTarget.position = oprTargetWorldPos;
                this.needLookAt = segmentPath.needLookAt;
                this.cameraPathTweener = PlaySegmentPath(this.oprTarget, segmentPath);
                this.cameraPathTweener.onUpdate = this.OnTweenUpdate;
                this.cameraPathTweener.onComplete = this.OnTweenSegmentComplete;                
            }
            else
            {
                this.OnTweenSegmentComplete();
            }
        }

        private Tweener PlaySegmentPath(Transform oprTargetTrans, CameraSkillSegmentPreset segmentPath)
        {
            List<Vector3> worldPointList = this.GetSegmentPathWorldPosList(segmentPath);
            Tweener tweener = GameTween.DOPath(oprTargetTrans, worldPointList.ToArray(),
                segmentPath.durationS,
                segmentPath.pathType, segmentPath.pathMode, segmentPath.resolution, Color.red, 1, LoopType.Incremental, Ease.INTERNAL_Custom, segmentPath.animationCurve);
            return tweener;
        }

        /// <summary>
        /// 获取曲线的路径点;
        /// </summary>
        /// <param name="segmentPath"></param>
        /// <returns></returns>
        public List<Vector3> GetSegmentPathWorldPosList(CameraSkillSegmentPreset segmentPath)
        {
            if (actionPreset.IsGlobal)
            {
                List<Vector3> worldPointList =
                    segmentPath.GetWorldPointList(segmentPath.posList, this.refTarget);
                return worldPointList;
            }
            else
            {
                //转换为世界坐标;
                List<Vector3> worldPointList = new List<Vector3>();
                for (int i = 0, len = segmentPath.posList.Count; i < len; i++)
                {
                    worldPointList.Add(this.refTarget.TransformPoint(segmentPath.posList[i]));
                }
                return worldPointList;    
            }
            return null;
        }

        private void OnTweenUpdate()
        {
            this.oprTarget.LookAt(lookAtWorldPos);
        }

        public Vector3 lookAtWorldPos
        {
            get
            {
                return lookAtTarget.position;       
            }
        }

        private void OnTweenSegmentComplete()
        {
            int nextIndex = this.curIndex + 1;
            if (nextIndex < this.actionPreset.segmentList.Count)
            {
                CameraSkillSegmentPreset segmentPath = this.actionPreset.segmentList[nextIndex];
                if (!segmentPath.IsEmpty)
                {
                    Tweener tweener;
                    if (actionPreset.IsGlobal)
                    {
                        Vector3 worldPoint = this.refTarget.TransformPoint(segmentPath.posList[0]);
                        tweener = this.oprTarget.DOMove(worldPoint, this.actionPreset.linkTimeList[this.curIndex]);
                    }
                    else
                    {
                        Vector3 worldPoint =
                            this.refTarget.TransformPoint(segmentPath.posList[0]);
                        tweener = this.oprTarget.DOMove(worldPoint,
                            this.actionPreset.linkTimeList[this.curIndex]);
                    }
                    this.needLookAt = segmentPath.needLookAt;
                    tweener.onUpdate = this.OnTweenUpdate;
                    tweener.onComplete = this.OnTweenSegmentLinkComplete;
                }
                else
                {
                    this.OnTweenSegmentLinkComplete();
                }
            }
            else
            {
                //播放完毕;
                this.OnAllComplete();
            }
        }

        private void OnTweenSegmentLinkComplete()
        {
            int nextIndex = this.curIndex + 1;
            this.PlaySegment(nextIndex);
        }

        private void OnAllComplete()
        {
            if (this.allCompleteAction != null)
            {
                this.allCompleteAction();
            }
        }

        public bool IsPlaying
        {
            get
            {
                if (this.oprTarget != null && DOTween.IsTweening(this.oprTarget))
                {
                    return true;
                }

                return false;
            }
        }

        public CameraSkillSegmentPreset CurSegmentPreset
        {
            get
            {
                if (this.IsPlaying)
                {
                    if (this.curIndex < this.actionPreset.segmentList.Count)
                    {
                        return this.actionPreset.segmentList[this.curIndex];
                    }   
                }
                return null;
            }
        }

        public void Reset()
        {
            if (this.oprTarget != null)
            {
                this.oprTarget.DOKill();
            }
            this.curIndex = 0;
        }
    }
    
    /// <summary>
    /// 曲线片段;
    /// </summary>
    public class SegmentPath
    {
        //本地坐标;
        public List<Vector3> localPointList = new List<Vector3>();
        public float durationSeconds = 1.0f;
        [HideInInspector] public PathType pathType = PathType.CatmullRom;
        [HideInInspector] public PathMode pathMode = PathMode.Full3D;
        [Range(0, 10)] public int resolution = 5;
        //调试用途的;
        [HideInInspector] public Vector3[] forDebugPointShowArr;

        [HideInInspector] public bool toggleForDebugShow = false;
        
        [HideInInspector]public AnimationCurve  animationCurve   = AnimationCurve.Linear(0,0,1,1);
        //支持的挂点类型;
        public CharacterHangPoint.SupportHangPointType supportHangPointType = CharacterHangPoint.SupportHangPointType.GROUND_LOC;
        //预览片段路径的时间;
        public float previewSegmentPathTime = 0.0f;
        public bool runtimePreview = true;
        public CameraSkillAction cameraSkillAction;
        public SegmentPath()
        {
            
        }

        public List<Vector3> GetWorldPointList(List<Vector3> localPointList, Transform refTarget)
        {
            List<Vector3> worldPointList = new List<Vector3>(localPointList.Count);
            for (int i = 0, len = localPointList.Count; i < len; i++)
            {
                worldPointList.Add(refTarget.TransformPoint(localPointList[i]));
            }

            return worldPointList;
        }

        public bool IsEmpty
        {
            get { return this.localPointList.Count <= 0; }
        }


    }

    /// <summary>
    /// 镜头曲线片段;
    /// </summary>
    public class CameraSkillSegmentPath : SegmentPath
    {
        
    }
    
    #endregion
    //镜头的焦点变换配置
    public CameraSkillPathConfig cameraSkillPathConfig;
    [Header("操作的目标-一般为镜头")]
    public Transform oprTarget;
    [Header("要注释的焦点-一般就是人物")]
    public Transform lookAtTarget;
    [Header("调试球大小")]
    public float sizeValue = 0.2f;
    //当前正在播放的镜头动作;
    public CameraSkillAction curPlayingAction = new CameraSkillAction();
    public CameraSkillActionPreset GetCameraSkillActionByKey(string keyName)
    {
        return cameraSkillPathConfig.GetValue(keyName);
    }

    private void Awake()
    {
        //设置下引用;
        for (int i = 0, len = cameraSkillPathConfig.actionList.Count; i < len; i++)
        {
            CameraSkillActionPreset actionPreset = cameraSkillPathConfig.actionList[i];
            for (int k = 0, klen = actionPreset.segmentList.Count; k < klen; k++)
            {
                CameraSkillSegmentPreset segmentPath = actionPreset.segmentList[k];
                segmentPath.actionPreset = actionPreset;
            }
        }

    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="refTarget">相对坐标原点对象</param>
    /// <param name="oprTarget">操作目标</param>
    /// <param name="lookAtTarget">焦点目标</param>
    public void Play(string cameraSkillActionKey, Transform oprTarget, Transform lookAtTarget, int segmentIndex = 0)
    {
        CameraSkillActionPreset actionPreset = this.GetCameraSkillActionByKey(cameraSkillActionKey);
        if (actionPreset == null)
        {
            Debug.LogError("找不到对应的镜头技能动作: " + cameraSkillActionKey);
            return;
        }

        this.oprTarget = oprTarget;
        this.lookAtTarget = lookAtTarget;
        this.curPlayingAction.actionPreset = actionPreset;
        this.curPlayingAction.Play(this.transform, oprTarget, this.lookAtTarget, this.OnSkillActionPlayComplete, segmentIndex);
    }

    public void Stop()
    {
        if (this.curPlayingAction != null && this.curPlayingAction.IsPlaying)
        {
            this.curPlayingAction.Reset();
        }
    }

    private void OnSkillActionPlayComplete()
    {
        this.curPlayingAction.actionPreset = null;
    }

    public bool IsPlaying
    {
        get
        {
            return this.curPlayingAction.IsPlaying;
        }
    }

    private void OnDestroy()
    {
        this.curPlayingAction.Reset();
    }

#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (this.curPlayingAction != null && this.curPlayingAction.IsPlaying)
        {
            Color bakColor = Handles.color;
            Handles.color = Color.green;
            Handles.SphereHandleCap(0, this.curPlayingAction.lookAtWorldPos, Quaternion.identity, this.sizeValue, EventType.Repaint);
            Handles.Label(this.curPlayingAction.lookAtWorldPos, "位置:"+this.curPlayingAction.lookAtWorldPos);
            Handles.color = bakColor;
        }
    }

    // private void OnGUI()
    // {
    //     if (GUILayout.Button("11111"))
    //     {
    //         
    //     }
    // }
#endif
}
#endif