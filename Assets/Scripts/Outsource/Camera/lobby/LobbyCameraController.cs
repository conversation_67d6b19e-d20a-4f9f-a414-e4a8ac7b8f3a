#if ACGGAME_CLIENT
using System;
using DG.Tweening;
using NTween;
using TKAction;
using UnityEngine;
using LobbyCameraLookPreset = LobbyCameraLookConfig.LobbyCameraLookPreset;
/// <summary>
/// 主界面的镜头管理器;
/// creatd:xfilsonpan
/// date:2020-10-13
/// </summary>
public class LobbyCameraController : MonoBehaviour
{
    //镜头的焦点变换配置
    public LobbyCameraLookConfig lobbyCameraLookConfig;
    [Header("当前焦点")]
    public Transform curFocusTrans;
    [Header("当前操作的移动对象，一般就是镜头")]
    public Transform oprTrans;
    [Header("当前操作的镜头")]
    public Camera oprCamera;
    [HideInInspector]
    public LobbyCameraLookPreset curPreset;

    private Tweener tweenerPos;
    private Tweener tweenerRotation;
    private Tweener tweenerFov;
    
    private CCNode ccNode = new CCNode();
    private Action playCompleteAction = null;

    public void SetCameraData(Camera camera)
    {
        this.oprCamera = camera;
        this.oprTrans = camera.transform;
    }

    public bool IsValid
    {
        get { return this.oprTrans != null && this.oprCamera != null; }
    }

    /// <summary>
    /// 设置焦点;
    /// </summary>
    /// <param name="focusTrans"></param>
    /// <param name="configKey"></param>
    /// <param name="needTween">是否需要缓动过去</param>
    public void SwitchFocus(Transform focusTrans, string configKey, bool needTween)
    {
        if (IsValid)
        {
            LobbyCameraLookPreset switchPreset = this.lobbyCameraLookConfig.GetValue(configKey);
            this.SwitchFocus(focusTrans, switchPreset, needTween);
        }
    }

    public void SwitchFocus(Transform focusTrans, LobbyCameraLookPreset switchPreset, bool needTween, Action playCompleteAction = null)
    {
        if (IsValid && focusTrans != null)
        {
            this.playCompleteAction = playCompleteAction;
            curFocusTrans = focusTrans;
            switchPreset.CopyTo(curPreset);
            this.Stop();
            Vector3 targetPos = curFocusTrans.position + curPreset.relativePos;
            Vector3 targetGlobalEuler = curPreset.globalEuler;
            float targetFov = curPreset.fov;
            //计算需要消耗的时间;
            float calcDuration = (targetPos - this.oprTrans.position).magnitude / this.lobbyCameraLookConfig.switchFocusSpeedPerSecond;
            float convertDuration = System.Math.Max(curPreset.atLeastSwitchDurationS, calcDuration);
            if (!needTween)
            {
                convertDuration = 0.0f;
            }
            GameTween.DOWorldMove(this.oprTrans.gameObject, this.oprTrans.position, targetPos, 0.0f, convertDuration, 1,
                LoopType.Incremental);
            GameTween.DOWorldRotate(this.oprTrans.gameObject, this.oprTrans.eulerAngles, targetGlobalEuler, 0.0f, convertDuration, 1,
                LoopType.Incremental);
            if (curPreset.needChangeFov)
            {
                GameTween.DoFov(this.oprTrans.gameObject, this.oprCamera.fieldOfView, targetFov, 0.0f, convertDuration, 1,
                    LoopType.Incremental);                
            }

            if (this.playCompleteAction != null)
            {
                ccNode.ScheduleOnce(OnPlayCompleteCallback, convertDuration);   
            }
        }
    }

    private void OnPlayCompleteCallback(float value)
    {
        if (this.playCompleteAction != null)
        {
            this.playCompleteAction();
        }
    }

    public void Stop()
    {
        if (this.tweenerPos != null){
            GameTween.DoKill(this.tweenerPos);
            this.tweenerPos = null;
        }
        if (this.tweenerRotation != null){
            GameTween.DoKill(this.tweenerRotation);
            this.tweenerRotation = null;
        }
        if (this.tweenerFov != null){
            GameTween.DoKill(this.tweenerFov);
            this.tweenerFov = null;
        }
        ccNode.UnscheduleAllSelectors();
    }

}
#endif