using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

[Serializable]
[ProtoContract(ImplicitFields = ImplicitFields.AllPublic)]
public class ChessAttackMovieConfig
{
    public enum PlayCompletedAction
    {
        KeepLastFrame,
        Hide,
    }

    public bool useUICamera = true;     // 是否共用UI相机进行渲染视频
    public int uiSortingLayer = 12;
    public int cameraDepth = 5;     // 独立相机的层级

    public GameObject startUIEffect;
    public float startUIEffectTime = 0f;

    public string moviePath;
    public float movieTime = 10f;
    public bool useCosMovie = false;
    public PlayCompletedAction playCompleteAction = PlayCompletedAction.KeepLastFrame;

    // 视频播放完成触发的事件
    public int movieCompleteTrigger = 0;

    public float blendInStartTime;
    public float blendInTime;

    public float blendOutStartTime;
    public float blendOutTime;

    public GameObject endUIEffect;
    public float endUIEffectTime = 0f;
}
