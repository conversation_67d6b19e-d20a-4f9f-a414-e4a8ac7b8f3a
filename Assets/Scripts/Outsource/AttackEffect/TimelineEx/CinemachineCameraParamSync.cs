using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;
using ZGameChess;
using ZGameClient;

namespace TimelineEx
{
    public class CinemachineCameraParamSync : MonoBehaviour
    {
        public enum Mode
        {
            None,
            Auto,        // 自动计算
            FixedValue,  // 使用固定值
            Sync,        // 保证默认相机一致
        }

        public enum SyncMode
        {
            Start,
            PerFrame,
        }

        [Serializable]
        public class FloatParam
        {
            public Mode mode = Mode.Auto;

            public float padValue;
            public float phoneValue;

            public FloatParam(Mode mode)
            {
                this.mode = mode;
            }
        }

        public FloatParam fieldOfView;
        public SyncMode syncMode = SyncMode.Start;

        protected float m_standFov = 29;

        protected float m_fovDetla = 0;
        protected float m_initFov = 29;
        protected Cinemachine.CinemachineVirtualCamera m_virtualCamera;
        protected Camera m_mainCamera;

        private void Awake()
        {
            m_virtualCamera = GetComponent<Cinemachine.CinemachineVirtualCamera>();
            if (m_virtualCamera != null)
            {
                m_initFov = m_virtualCamera.m_Lens.FieldOfView;
            }
        }

        public void SetMainCamera(Camera camera)
        {
            m_mainCamera = camera;

            var cz = m_mainCamera.GetComponent<CameraPanAndZoom>();
            if (cz != null)
            {
                // 拿到的是正常手机的参数 用于对比
                TACG_Camera_Client camTabConfig = CameraConfigManager.Instance.GetCameraConfig(cz.camConfigName);
                if (camTabConfig != null)
                {
                    m_standFov = camTabConfig.fFOV;
                }
            }
            SetFov();
        }

        //private void OnEnable()
        //{
        //    SetFov();
        //}

        private void OnDisable()
        {
            if (m_virtualCamera != null)
            {
                m_virtualCamera.m_Lens.FieldOfView = m_initFov;
            }
        }

        private void Update()
        {
            if (syncMode == SyncMode.PerFrame)
            {
                SetFov();
            }
        }

        private void SetFov()
        {
            if (m_virtualCamera != null)
            {
                if (fieldOfView != null && fieldOfView.mode != Mode.None)
                {
                    if (fieldOfView.mode == Mode.FixedValue)
                    {
                        if (NotchSizeImp.IsPad)
                            m_virtualCamera.m_Lens.FieldOfView = fieldOfView.padValue;
                        else
                            m_virtualCamera.m_Lens.FieldOfView = fieldOfView.phoneValue;
                    }
                    else if (fieldOfView.mode == Mode.Auto)
                    {
                        if (m_mainCamera != null)
                        {
                            m_virtualCamera.m_Lens.FieldOfView = m_virtualCamera.m_Lens.FieldOfView + (m_mainCamera.fieldOfView - m_standFov);
                        }
                    }
                    else if (fieldOfView.mode == Mode.Sync)
                    {
                        if (m_mainCamera != null)
                        {
                            m_virtualCamera.m_Lens.FieldOfView = m_mainCamera.fieldOfView;
                        }
                        else
                        {
                            Diagnostic.Warn("[CinemachineCameraParamSync]MainCamera is null");
                        }
                    }
                }
            }
        }
    }
}
