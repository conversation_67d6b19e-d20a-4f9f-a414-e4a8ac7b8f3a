using System;

namespace UnityEngine.Timeline
{
    struct TKDiscreteTime : IComparable
    {
        const double k_Tick = 1e-12;
        public static readonly TKDiscreteTime kMaxTime = new TKDiscreteTime(Int64.MaxValue);

        readonly Int64 m_DiscreteTime;

        public static double tickValue { get { return k_Tick; } }

        public TKDiscreteTime(TKDiscreteTime time)
        {
            m_DiscreteTime = time.m_DiscreteTime;
        }

        TKDiscreteTime(Int64 time)
        {
            m_DiscreteTime = time;
        }

        public TKDiscreteTime(double time)
        {
            m_DiscreteTime = DoubleToDiscreteTime(time);
        }

        public TKDiscreteTime(float time)
        {
            m_DiscreteTime = FloatToDiscreteTime(time);
        }

        public TKDiscreteTime(int time)
        {
            m_DiscreteTime = IntToDiscreteTime(time);
        }

        public TKDiscreteTime(int frame, double fps)
        {
            m_DiscreteTime = DoubleToDiscreteTime(frame * fps);
        }

        public TKDiscreteTime OneTickBefore()
        {
            return new TKDiscreteTime(m_DiscreteTime - 1);
        }

        public TKDiscreteTime OneTickAfter()
        {
            return new TKDiscreteTime(m_DiscreteTime + 1);
        }

        public Int64 GetTick()
        {
            return m_DiscreteTime;
        }

        public static TKDiscreteTime FromTicks(Int64 ticks)
        {
            return new TKDiscreteTime(ticks);
        }

        public int CompareTo(object obj)
        {
            if (obj is TKDiscreteTime)
                return m_DiscreteTime.CompareTo(((TKDiscreteTime)obj).m_DiscreteTime);
            return 1;
        }

        public bool Equals(TKDiscreteTime other)
        {
            return m_DiscreteTime == other.m_DiscreteTime;
        }

        public override bool Equals(object obj)
        {
            if (obj is TKDiscreteTime)
                return Equals((TKDiscreteTime)obj);
            return false;
        }

        static Int64 DoubleToDiscreteTime(double time)
        {
            double number = (time / k_Tick) + 0.5;
            if (number < Int64.MaxValue && number > Int64.MinValue)
                return (Int64)number;
            throw new ArgumentOutOfRangeException("Time is over the discrete range.");
        }

        static Int64 FloatToDiscreteTime(float time)
        {
            float number = (time / (float)k_Tick) + 0.5f;
            if (number < Int64.MaxValue && number > Int64.MinValue)
                return (Int64)number;
            throw new ArgumentOutOfRangeException("Time is over the discrete range.");
        }

        static Int64 IntToDiscreteTime(int time)
        {
            return DoubleToDiscreteTime(time);
        }

        static double ToDouble(Int64 time)
        {
            return time * k_Tick;
        }

        static float ToFloat(Int64 time)
        {
            return (float)ToDouble(time);
        }

        public static explicit operator double(TKDiscreteTime b)
        {
            return ToDouble(b.m_DiscreteTime);
        }

        public static explicit operator float(TKDiscreteTime b)
        {
            return ToFloat(b.m_DiscreteTime);
        }

        public static explicit operator Int64(TKDiscreteTime b)
        {
            return b.m_DiscreteTime;
        }

        public static explicit operator TKDiscreteTime(double time)
        {
            return new TKDiscreteTime(time);
        }

        public static explicit operator TKDiscreteTime(float time)
        {
            return new TKDiscreteTime(time);
        }

        public static implicit operator TKDiscreteTime(Int32 time)
        {
            return new TKDiscreteTime(time);
        }

        public static explicit operator TKDiscreteTime(Int64 time)
        {
            return new TKDiscreteTime(time);
        }

        public static bool operator ==(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return lhs.m_DiscreteTime == rhs.m_DiscreteTime;
        }

        public static bool operator !=(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return !(lhs == rhs);
        }

        public static bool operator >(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return lhs.m_DiscreteTime > rhs.m_DiscreteTime;
        }

        public static bool operator <(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return lhs.m_DiscreteTime < rhs.m_DiscreteTime;
        }

        public static bool operator <=(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return lhs.m_DiscreteTime <= rhs.m_DiscreteTime;
        }

        public static bool operator >=(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return lhs.m_DiscreteTime >= rhs.m_DiscreteTime;
        }

        public static TKDiscreteTime operator +(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return new TKDiscreteTime(lhs.m_DiscreteTime + rhs.m_DiscreteTime);
        }

        public static TKDiscreteTime operator -(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return new TKDiscreteTime(lhs.m_DiscreteTime - rhs.m_DiscreteTime);
        }

        public override string ToString()
        {
            return m_DiscreteTime.ToString();
        }

        public override int GetHashCode()
        {
            return m_DiscreteTime.GetHashCode();
        }

        public static TKDiscreteTime Min(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return new TKDiscreteTime(Math.Min(lhs.m_DiscreteTime, rhs.m_DiscreteTime));
        }

        public static TKDiscreteTime Max(TKDiscreteTime lhs, TKDiscreteTime rhs)
        {
            return new TKDiscreteTime(Math.Max(lhs.m_DiscreteTime, rhs.m_DiscreteTime));
        }

        public static double SnapToNearestTick(double time)
        {
            Int64 discreteTime = DoubleToDiscreteTime(time);
            return ToDouble(discreteTime);
        }

        public static float SnapToNearestTick(float time)
        {
            Int64 discreteTime = FloatToDiscreteTime(time);
            return ToFloat(discreteTime);
        }

        public static Int64 GetNearestTick(double time)
        {
            return DoubleToDiscreteTime(time);
        }
    }
}
