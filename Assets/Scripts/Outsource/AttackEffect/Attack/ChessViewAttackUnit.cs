using GfxFramework;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using TriggerSystem;
using UnityEngine;
using ZGame;
using ZGame.GameSystem;
using ZGameChess;
using ZGameClient;

public class ChessViewAttackUnit : TKBehaviour
{
    public ChessAttackConfig m_attackConfig = null;
    public ChessPlayerUnit m_attacker = null;
    public ChessPlayerUnit m_target = null;

    protected ChessViewAttackTimelineUnit m_cameraUnit = new ChessViewAttackTimelineUnit();
    protected List<ChessAttackEffectBase> m_effect = new List<ChessAttackEffectBase>();

    private ChessViewAttackSkipUI m_skipUI = null;


    protected float m_startTime = 0.0f;
    protected float m_currentTime = 0.0f;
    protected float m_delayCameraTrigger = 0.0f;
    protected float m_delayTimelineTrigger = 0.0f;

    protected string m_cfgName = string.Empty;
    protected bool m_loading = false;
    protected bool m_loaded = false;

    private bool m_loadDefault = false;

    private SmartResourceLoader<ChessAttackConfig> m_loader = null;

    public float CurrentTime
    {
        get
        {
            return m_currentTime;
        }
    }

    public bool IsAttack()
    {
        return gameObject != null && gameObject.activeSelf;
    }

    public bool IsOwner(ChessPlayerUnit attacker)
    {
        return m_attacker == attacker;
    }

    public bool IsLoading()
    {
        return m_loading;
    }

    public bool IsLoaded()
    {
        return m_loaded;
    }

    public void Init(string cfgName)
    {
        m_cfgName = cfgName;

        if (string.IsNullOrEmpty(m_cfgName))
        {
            Diagnostic.Warn("攻击特效配置名称设置为null");
        }
    }

    public void LoadConfig()
    {
        //if (!GMSwitchPanel.GetGMSwitch(GMSwitchPanel.Type.LoadLittleHero))
        //    return;

        if (string.IsNullOrEmpty(m_cfgName))
        {
            Diagnostic.Warn("加载攻击特效配置错误! 未初始化攻击特效，或者是将攻击特效配置名称设置为null");
        }

        UnloadConfig();

        m_loading = true;
        m_loaded = false;

        string cfgAB = "art_tft_raw/cfg/team_leader_attack_cfg/" + m_cfgName.ToLower();
        string cfgName = Path.GetFileNameWithoutExtension(m_cfgName);

        // 局内不允许下载
        //var stage = QQGameSystem.Instance.GetStage();
        //if (stage is ChessBattleStage)
        //{
        //    if (SmartResourceLoader<GameObject>.CheckAssetNeedDownload(cfgAB, cfgName))
        //    {
        //        GetDefaultCfgPath(cfgAB, cfgName, out cfgAB, out cfgName);
        //        m_loadDefault = true;
        //    }
        //}

        m_loader = SmartResourceLoader<ChessAttackConfig>.Load(cfgAB, cfgName, "ChessAttackConfig", OnCfgLoaded, this);
        //ResourceUtil.LoadAsset<ChessAttackConfig>("art_tft_raw/cfg/team_leader_attack_cfg/" + m_cfgName, Path.GetFileNameWithoutExtension(m_cfgName), OnCfgLoaded, null);
    }

    private static bool GetDefaultCfgPath(string orginCfgAB, string orginCfgName, out string cfgAB, out string cfgName)
    {
        TTAC_Global_Client global_def = DataBaseManager.Instance.SearchTACGlobalCfg(1090);
        if (global_def != null)
        {
            int.TryParse(global_def.sParam1, out int damageId);

            var damageCfg = DataBaseManager.Instance.SearchACGAttackEffect(damageId);
            if (damageCfg != null)
            {
                var attackCfgDict = TinyAttackData.GetAttackCfgDict(damageCfg.sAttackConfig);
                foreach (var attackItem in attackCfgDict)
                {
                    var attackCfgName = attackItem.Value;
                    cfgAB = "art_tft_raw/cfg/team_leader_attack_cfg/" + attackCfgName.ToLower();
                    cfgName = Path.GetFileNameWithoutExtension(attackCfgName);
                    Diagnostic.Log("[GetDefaultCfgPath] cfgAB: {0} cfgName: {1} need download. use default attack id: {2} defaultCfgAB: {3} defaultCfgName: {4}. time: {5}", orginCfgAB, orginCfgName, cfgAB, cfgName, damageId, Time.time);
                    return true;
                }

                cfgAB = string.Empty;
                cfgName = string.Empty;
                Diagnostic.Log("[GetDefaultCfgPath] cfgAB: {0} cfgName: {1} need download. but find default attack id: {2} config faild: {3}. time: {4}", orginCfgAB, orginCfgName, damageId, damageCfg.sAttackConfig, Time.time);
                return false;
            }
            else
            {
                cfgAB = string.Empty;
                cfgName = string.Empty;
                Diagnostic.Log("[GetDefaultCfgPath] cfgAB: {0} cfgName: {1} need download. but find default attack id: {2} find from cfg faild. time: {3}", orginCfgAB, orginCfgName, damageId, Time.time);
                return false;
            }
        }
        else
        {
            cfgAB = string.Empty;
            cfgName = string.Empty;
            Diagnostic.Log("[GetDefaultCfgPath] cfgAB: {0} cfgName: {1} need download. but find default attack id faild. time: {2}", orginCfgAB, orginCfgName, Time.time);
            return false;
        }
    }

    public void UnloadConfig()
    {
        m_loading = false;
        m_loaded = false;

        //if (m_attackConfig != null)
        //    Destroy(m_attackConfig);
        m_attackConfig = null;

        if (m_loader != null)
            m_loader.Release();
        m_loader = null;

        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            effect.OnDestory();
        }
        m_effect.Clear();
    }

    public void OnCfgLoaded(ChessAttackConfig t, string assetBundleName)
    {
        m_loading = false;
        m_loaded = true;
        m_attackConfig = t;

        if (t == null)
        {
            GetDefaultCfgPath(string.Empty, string.Empty, out string newCfgAB, out string newCfgName);
            if (m_loadDefault)
            {
                Diagnostic.Log("Load Attack cfg faild. {0} {1} {2}", m_cfgName, newCfgAB, newCfgName);
                return;     // 默认的也加载不出来了 就什么都不处理
            }
            else  // 尝试加载默认的
            {
                UnloadConfig();
                m_loading = true;
                m_loaded = false;
                m_loadDefault = true;
                m_loader = SmartResourceLoader<ChessAttackConfig>.Load(newCfgAB, newCfgName, "ChessAttackConfig", OnCfgLoaded, this);
                return;
            }
        }

        bool active = false;
        if (gameObject != null)
        {
            active = gameObject.activeSelf;
            gameObject.SetActive(true);
        }

        if (m_attackConfig != null)
            InitEnv();

        if (gameObject != null)
        {
            gameObject.SetActive(active);
        }
    }

    protected GameObject InstacneEffect(GameObject go)
    {
        if (go == null)
            return null;

        var inst = GameObject.Instantiate(go, transform);
        ChessUtil.MakeTransformIdentity(inst.transform);
        if (m_attackConfig != null && m_attackConfig.m_smartSetLayer)
            SetEffectLayer(inst);
        else
            CommonUtil.SetLayerRecursive(inst, GameObjectLayer.BattleEffect);


        var gfxRoot = inst.GetComponent<GfxRoot>();
        if (gfxRoot != null)
        {
            gfxRoot.OnGfxPlayEnd = OnEffectPlayEnd;
        }

        inst.SetActive(false);
        return inst;
    }

    private void SetEffectLayer(GameObject obj)
    {
        var renderers = obj.GetComponentsInChildren<Renderer>();
        foreach (var r in renderers)
        {
            var mr = r as MeshRenderer;
            var smr = r as SkinnedMeshRenderer;
            if (mr && mr.shadowCastingMode != UnityEngine.Rendering.ShadowCastingMode.Off)
            {
                r.gameObject.layer = GameObjectLayer.MiniHero;
            }
            if (smr && smr.shadowCastingMode != UnityEngine.Rendering.ShadowCastingMode.Off)
            {
                r.gameObject.layer = GameObjectLayer.MiniHero;
            }
            else
            {
                r.gameObject.layer = GameObjectLayer.BattleEffect;
            }
        }
    }

    protected void OnEffectPlayEnd(GfxRoot root)
    {
        if (!root.m_timeSystem.m_loop)
        {
            root.gameObject.SetActive(false);
        }
    }

    protected void InitEnv()
    {
        m_effect.Clear();
        for (int i = 0; i < m_attackConfig.m_effects.Count; ++i)
        {
            var bulletData = m_attackConfig.m_effects[i];
            bool isMainAttack = i == m_attackConfig.m_mainAttackItemIndex;
            if (!bulletData.m_enable && !isMainAttack)
                continue;

            GameObject castEffect = null;
            if (bulletData.m_hasCast)
            {
                if (SceneCameraManager.instance == null || SceneCameraManager.instance.IsNormal || bulletData.m_cast.m_mirrorCastEffect == null)
                {
                    castEffect = InstacneEffect(bulletData.m_cast.m_castEffect);
                }
                else
                {
                    castEffect = InstacneEffect(bulletData.m_cast.m_mirrorCastEffect);
                }
            }

            ChessAttackEffectBase effect = null;
            switch (bulletData.m_attackType)
            {
                case ChessAttackItemConfig.AttackType.Bullet:
                    {
                        //var bullet = SceneCameraManager.instance == null || SceneCameraManager.instance.IsNormal || bulletData.m_mirrorBullet == null ? bulletData.m_bullet : bulletData.m_mirrorBullet;
                        //var spawnEffect = SceneCameraManager.instance == null || SceneCameraManager.instance.IsNormal || bulletData.m_mirrorSpawnEffect == null ? bulletData.m_spawnEffect : bulletData.m_mirrorSpawnEffect;
                        //var bulletGo = InstacneEffect(bullet);
                        var hitEffectGo = InstacneEffect(bulletData.m_hitEffect);
                        //var spawnEffectGo = InstacneEffect(spawnEffect);
                        var bulletEffect = new ChessAttackBullet(this, hitEffectGo, castEffect, bulletData, isMainAttack);
                        effect = bulletEffect;

                        var bulletGo = InstacneEffect(bulletData.m_bullet);
                        var mirrorBullet = InstacneEffect(bulletData.m_mirrorBullet);
                        bulletEffect.SetBullet(bulletGo != null ? bulletGo.transform : null, mirrorBullet != null ? mirrorBullet.transform : null);

                        var spawnEffect = InstacneEffect(bulletData.m_spawnEffect);
                        var mirrorspawnEffect = InstacneEffect(bulletData.m_mirrorSpawnEffect);
                        bulletEffect.SetSpawnEffect(spawnEffect, mirrorspawnEffect);
                    }
                    break;
                case ChessAttackItemConfig.AttackType.Hit:
                    {
                        var hitEffectGo = InstacneEffect(bulletData.m_hitEffect);
                        effect = new ChessAttackHit(this, hitEffectGo, castEffect, bulletData, isMainAttack);
                    }
                    break;
                //case ChessAttackItemConfig.AttackType.Line:
                //     break;
                case ChessAttackItemConfig.AttackType.RunAttack:
                case ChessAttackItemConfig.AttackType.TeleportAttack:
                    {
                        var hitEffectGo = InstacneEffect(bulletData.m_hitEffect);
                        var runFinishedGo = InstacneEffect(bulletData.m_runFinishedEffect);
                        effect = new ChessAttackBase(this, hitEffectGo, runFinishedGo, castEffect, bulletData, isMainAttack);
                    }
                    break;
                case ChessAttackItemConfig.AttackType.Movie:
                    {
                        var hitEffectGo = InstacneEffect(bulletData.m_hitEffect);
                        effect = new ChessAttackMovie(this, hitEffectGo, castEffect, bulletData, isMainAttack);
                    }
                    break;
                default:
                    break;
            }

            if (effect != null)
                m_effect.Add(effect);
        }

        m_cameraUnit.InitEnv(this, m_attackConfig.m_timelineConfig);

        // 如果有攻击场景，并且攻击场景有独立摄像机，（并且独立摄像机的层级高于UI 这个条件需实时判断）；或者是攻击特效隐藏了UI，则需要增加跳过按钮
        //bool instanceSkipUI = m_cameraUnit.HasCustomCameraInAttackTimeline;
        //instanceSkipUI |= (m_attackConfig.m_effects.Count > m_attackConfig.m_mainAttackItemIndex && m_attackConfig.m_mainAttackItemIndex >= 0 && m_attackConfig.m_effects[m_attackConfig.m_mainAttackItemIndex].m_hideUI.m_enable);
        //if (instanceSkipUI)
        //{
        //    InstanceSkipUI();
        //}
    }

    private void InstanceSkipUI()
    {
        ResourceUtil.LoadAsset<GameObject>("chessart/ui/battle/littlelegendattackskip", "LittleLegendAttackSkip", (go, abName) =>
        {
            var inst = GameObject.Instantiate(go);
            inst.transform.SetParent(transform);
            ChessUtil.MakeTransformIdentity(inst.transform);
            m_skipUI = inst.TryGetComponent<ChessViewAttackSkipUI>();
            m_skipUI.BindAttack(this);
            inst.SetActive(false);
        });
    }

    public static Transform GetLoc(ChessPlayerUnit player, CharacterHangPoint.SupportHangPointType locType)
    {
        if (player == null)
            return null;
        if (player.CharacterHangPoint == null)
            return player.transform;
        var trans = player.CharacterHangPoint.__GetReallyPosData(locType);
        if (trans != null && trans.bindTrans != null)
            return trans.bindTrans;
        else
            return player.transform;
    }

    public bool IsInited()
    {
        if (!m_loaded)
        {
            return false;
        }

        for (int i = 0; i < m_effect.Count; ++i)
        {
            if (!m_effect[i].IsInited())
            {
                return false;
            }
        }

        return true;
    }

    private IEnumerator AttackAsync(float offsetTime)
    {
        if (!m_loaded && !m_loading)
        {
            LoadConfig();
        }

        while (!m_loaded)
        {
            yield return null;
            offsetTime += Time.deltaTime;
        }

        for (int i = 0; i < m_effect.Count; ++i)
        {
            if (!m_effect[i].IsInited())
            {
                yield return null;
                offsetTime += Time.deltaTime;
            }
        }

        if (m_cameraUnit.HasTimeline && m_attackConfig != null && m_attackConfig.m_timelineConfig.m_attackDelay > 0)
        {
            m_delayCameraTrigger = m_attackConfig.m_timelineConfig.m_attackDelay;
        }
        else
        {
            m_delayCameraTrigger = 0.0f;

            AttackImpl(offsetTime);
        }

        // 模拟追帧
        while (offsetTime > 0)
        {
            Update();
            offsetTime -= Time.deltaTime;
        }
    }

    public void StartAttack(ChessPlayerUnit attacker, ChessPlayerUnit target, float offsetTime)
    {
        m_attacker = attacker;
        m_target = target;
        m_startTime = Time.time - offsetTime;
        m_currentTime = m_startTime;

        if (gameObject != null)
        {
            gameObject.SetActive(true);
        }

        StartCoroutine(AttackAsync(offsetTime));
    }

    private void AttackImpl(float offsetTime)
    {
        // 攻击动作
        PlayAttackAction();

        InitEffects(m_attacker, ChessAttackItemConfig.TriggerCondition.Start);

        if (m_attackConfig != null)
        {
            if (m_attackConfig.m_timelineConfig.m_attackTimelineDelay <= offsetTime)
                TriggerTimelineOnAttackStart(offsetTime);
            else
                m_delayTimelineTrigger = m_attackConfig.m_timelineConfig.m_attackTimelineDelay - offsetTime;
        }

        // 受击者面向攻击方
        if (m_target != null && m_attacker != null)
        {
            Vector3 dir = m_attacker.transform.position - m_target.transform.position;
            m_target.FaceTo(m_target.Direction2Angle(dir));
        }
    }

    private void TriggerTimelineOnAttackStart(float offsetTime)
    {
        m_delayTimelineTrigger = 0f;
        if (m_cameraUnit.HasTimeline)
        {
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_StartTeamLeaderAttackTimeline);
        }

        m_cameraUnit.OnAttackBegin();
        m_cameraUnit.Trigger(ChessAttackCameraTimePoint.AttackStart, m_attacker, m_target, offsetTime);
    }

    public void TriggerEvent(int eventType, float time = -1)
    {
        if (time == -1)
            time = CurrentTime;

        for (int i = 0; i < ChessAttackItemConfig.TriggerEventName.Length; ++i)
        {
            var triggerName = ChessAttackItemConfig.TriggerEventName[i];
            if ((eventType & (1 << i)) != 0)
            {
                if (triggerName == "自定义1镜头")
                {
                    TriggerCustomTimeline(ChessAttackCameraTimePoint.CustomEvent1);
                }
                else if (triggerName == "自定义2镜头")
                {
                    TriggerCustomTimeline(ChessAttackCameraTimePoint.CustomEvent2);
                }
                else if (triggerName == "自定义3镜头")
                {
                    TriggerCustomTimeline(ChessAttackCameraTimePoint.CustomEvent3);
                }
                else if (triggerName == "自定义阶段1")
                {
                    TriggerCustomPhase(ChessAttackItemConfig.HideParam.Phase.CustomPhase1, time);
                }
                else if (triggerName == "自定义阶段2")
                {
                    TriggerCustomPhase(ChessAttackItemConfig.HideParam.Phase.CustomPhase2, time);
                }
                else if (triggerName == "自定义阶段3")
                {
                    TriggerCustomPhase(ChessAttackItemConfig.HideParam.Phase.CustomPhase3, time);
                }
                else if (triggerName == "自定义阶段4")
                {
                    TriggerCustomPhase(ChessAttackItemConfig.HideParam.Phase.CustomPhase4, time);
                }
            }
        }
    }

    public void TriggerCustomTimeline(ChessAttackCameraTimePoint timePoint)
    {
        m_cameraUnit.Trigger(timePoint, m_attacker, m_target, 0);
    }

    public void TriggerCustomPhase(ChessAttackItemConfig.HideParam.Phase phase, float time)
    {
        for (int i = 0; i < m_effect.Count; ++i)
        {
            if (!m_effect[i].IsMainAttack())
            {
                m_effect[i].CheckThenHide(time, phase);
                break;
            }
            else
            {
                if (phase == ChessAttackItemConfig.HideParam.Phase.CustomPhase1)
                {
                    m_effect[i].Init(m_attacker, m_target, m_startTime, ChessAttackItemConfig.TriggerCondition.CustomPhase1, m_currentTime);
                }
                else if (phase == ChessAttackItemConfig.HideParam.Phase.CustomPhase2)
                {
                    m_effect[i].Init(m_attacker, m_target, m_startTime, ChessAttackItemConfig.TriggerCondition.CustomPhase2, m_currentTime);
                }
                else if (phase == ChessAttackItemConfig.HideParam.Phase.CustomPhase3)
                {
                    m_effect[i].Init(m_attacker, m_target, m_startTime, ChessAttackItemConfig.TriggerCondition.CustomPhase3, m_currentTime);
                }
                else if (phase == ChessAttackItemConfig.HideParam.Phase.CustomPhase4)
                {
                    m_effect[i].Init(m_attacker, m_target, m_startTime, ChessAttackItemConfig.TriggerCondition.CustomPhase4, m_currentTime);
                }
            }
        }
    }

    protected void PlayAttackAction()
    {
        if (m_attacker != null)
        {
            if (!m_attacker.IsMeleeAttack())
            {
                string anim = m_attackConfig != null ? m_attackConfig.m_attackAction : string.Empty;
                if (string.IsNullOrEmpty(anim))
                {
                    anim = m_attacker.GetTriggerParam<string>(TriggerEnum.AttackAnim);
                    if (string.IsNullOrEmpty(anim) && m_attacker.config != null)
                        anim = m_attacker.config.GetAnimName("Attack");
                }

                if (!string.IsNullOrEmpty(anim))
                {
                    if (m_attacker.AnimBehaviour != null)
                        m_attacker.AnimBehaviour.PlayAnim(anim);
                    else
                        m_attacker.PlayAnim(anim);
                }
            }
        }
    }

    protected void InitEffects(ChessPlayerUnit attacker, ChessAttackItemConfig.TriggerCondition triggerCondition)
    {
        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            if (effect != null)
            {
                effect.Init(attacker, m_target, m_startTime, triggerCondition, m_currentTime);
            }
        }
    }

    public void LogicHit()
    {
        if (this.gameObject.activeSelf)
            StartCoroutine(LogicHitAsync());
    }

    private IEnumerator LogicHitAsync()
    {
        while (!m_loaded)
        {
            yield return null;
        }

        InitEffects(m_attacker, ChessAttackItemConfig.TriggerCondition.AfterHit);

        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            effect.OnLogicHit(m_currentTime);
        }
    }

    public void LogicWillHit()
    {
        if (this.gameObject.activeSelf)
            StartCoroutine(LogicWillHitAsync());
    }

    private IEnumerator LogicWillHitAsync()
    {
        while (!m_loaded)
        {
            yield return null;
        }

        m_cameraUnit.Trigger(ChessAttackCameraTimePoint.HitStart, m_attacker, m_target, 0);

        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            if (effect != null)
            {
                effect.OnLogicWillHit(m_currentTime);
            }
        }
    }

    // 仅中断斩杀场景
    public void Skip()
    {
        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            effect.ResetVisible();
        }

        m_cameraUnit.OnAttackBreak();
    }

    public void StopAttack()
    {
        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            effect.Reset();
        }

        ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_StopTeamLeaderAttackTimeline);

        m_cameraUnit.OnAttackBreak();

        gameObject.SetActive(false);
    }

    public void StopAttack_StopSound()
    {
        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            effect.Reset();
            effect.StopSound();
        }

        ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_StopTeamLeaderAttackTimeline);

        m_cameraUnit.OnAttackBreak();

        gameObject.SetActive(false);
    }

    public Vector3 GetBulletPos(int index)
    {
        if (m_effect.Count > index && index >= 0)
        {
            var effect = m_effect[index];
            var bulletEffect = effect as ChessAttackBullet;
            if (bulletEffect != null)
            {
                return bulletEffect.GetCurrentBulletPos();
            }
        }
        return Vector3.zero;
    }

    private void Update()
    {
        if (m_attackConfig == null)
        {
            return;
        }

        if (m_delayCameraTrigger > 0f)
        {
            m_delayCameraTrigger -= Time.deltaTime;

            if (m_delayCameraTrigger <= 0f)
            {
                AttackImpl(0f);
                return;
            }
            else
            {
                return;
            }
        }

        if (m_delayTimelineTrigger > 0)
        {
            m_delayTimelineTrigger -= Time.deltaTime;

            if (m_delayTimelineTrigger <= 0f)
                TriggerTimelineOnAttackStart(0);
        }

        m_currentTime += Time.deltaTime;
        bool finished = true;
        for (int i = 0; i < m_effect.Count; ++i)
        {
            var effect = m_effect[i];
            effect.RunTick(m_currentTime);
            finished &= effect.Finished;
        }

        UpdateSkipUIVisible();

        finished &= !m_cameraUnit.IsPlaying;
        finished &= m_delayCameraTrigger <= 0;
        if (finished)
        {
            gameObject.SetActive(false);
            m_cameraUnit.OnTimelineStop();
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_StopTeamLeaderAttackTimeline);
#if UNITY_EDITOR
            ACGEventManager.Instance.Send(EventType_UI.UNITY_EDITOR_AUTO_RECORDER_ACTION_CHANGE);
#endif
            Diagnostic.Log("All Attack Effect Play Finished");
        }
    }

    private void UpdateSkipUIVisible()
    {
        if (m_skipUI != null)
        {
            #region 只有我和我的队友有资格跳过
            if (m_attacker == null || m_attacker.PlayerData == null)
            {
                return;
            }

            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (m_attacker.PlayerData.ChessPlayerId != battleModel.MyPlayerId /*&& !battleModel.IsTeammate(m_attacker.PlayerData.ChessPlayerId, battleModel.MyPlayerId)*/)
            {
                return;
            }
            #endregion

            bool visible = false;
            for (int i = 0; i < m_effect.Count; ++i)
            {
                var effect = m_effect[i];
                if (effect.HideUI)
                {
                    visible = true;
                    break;
                }
            }
            if (!visible)
                visible = m_cameraUnit.IsAttackCustomCameraOverUI(10);

            if (m_skipUI.gameObject.activeSelf != visible)
                m_skipUI.gameObject.SetActive(visible);
        }
    }

    public bool Awaked { get; private set; } = false;

    protected override void Awake()
    {
        base.Awake();

        Awaked = true;
    }

    public void Destory()
    {
        if (!Awaked)
            OnDestroy();

        GameObject.Destroy(gameObject);
    }

    protected override void OnDestroy()
    {
        UnloadConfig();

        base.OnDestroy();
    }

#if UNITY_EDITOR
    List<string> checkCameraList = new List<string>();

    private void OnPreRender()
    {
        var cameras = Camera.allCameras;
        checkCameraList.Clear();
        for (int i = 0; i < cameras.Length; ++i)
        {
            if (cameras[i].GetComponent<TKPostProcessingStack>() != null)
            {
                checkCameraList.Add(cameras[i].name);
            }
        }

        if (checkCameraList.Count > 1)
        { // 不允许同时出现两个带后处理的相机 这里弹一个报警
            UnityEditor.EditorUtility.DisplayDialog("错误", "不允许同时出现两个带后处理的相机，请删除其中一个!!! 带后处理的相机列表： " + string.Join(", ", checkCameraList), "懂了，我这就去删");
            UnityEditor.EditorApplication.isPaused = true;
        }
    }
#endif
}
