using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using TKFrame.Item;
using UnityEngine;
using ZGame;
using ZGameChess;

public class ChessAttackMovieUI : UIPanel
{
    [UIObject("Player")]
    public GameObject Player
    {
        get;
        set;
    }

    [UIObject("EffectNode")]
    public GameObject EffectNode
    {
        get;
        set;
    }

    private int tryIndex = 1;
#if !OUTSOURCE
    private VideoComponentAVPro _player;
#endif
    string moviePath;
    bool loadFromCos;
    float movieTime;
    ChessAttackMovieConfig.PlayCompletedAction completedAction = ChessAttackMovieConfig.PlayCompletedAction.KeepLastFrame;
    int movieCompletedTrigger;
    float blendInStartTime;
    float blendInTime;

    float blendOutStartTime;
    float blendOutTime;

    private bool adaptFullScreen;
    private ScreenBgScaler _screenBgScaler;

    GameObject startUIEffect;
    float startUIEffectTime;
    GameObject endUIEffect;
    float endUIEffectTime;
    float endUIEffectGfxTime;   //  特效中的实际播放时间

    private bool m_resInited = false;

    GameObject m_startUIEffectInst;
    GameObject m_endUIEffectInst;

    private bool m_initTimeTag = false;
    private bool m_playStartEffect = false;
    private float m_playMovieTime = 0;
    private bool m_playMovie = false;
    private float m_playEndEffectTime = 0;
    private bool m_playEndEffect = false;
    private bool m_moviePlayCompleted = false;
    private float m_playEndEffectCompletedTime = 0;
    private int uiSortingLayer = 0;

    private bool m_blendIn = false;
    private float m_blendInStartTime = 0;
    private bool m_blendOut = false;
    private float m_blendOutStartTime = 0;

    private bool m_finished = false;

    public bool Finished { get { return m_finished; } }

    public bool ResInited { get { return m_resInited; } }


    ChessAttackMovie m_owner;

    public void Init(ChessAttackMovie owner, ChessAttackMovieConfig cfg, bool adaptFullScreen)
    {
        m_owner = owner;
        this.moviePath = cfg.moviePath.Replace("\\", "/");
        this.movieTime = cfg.movieTime;
        this.adaptFullScreen = adaptFullScreen;
        this.loadFromCos = cfg.useCosMovie;
        this.completedAction = cfg.playCompleteAction;
        this.movieCompletedTrigger = cfg.movieCompleteTrigger;
        this.blendInStartTime = cfg.blendInStartTime;
        this.blendInTime = cfg.blendInTime;
        this.blendOutStartTime = cfg.blendOutStartTime;
        this.blendOutTime = cfg.blendOutTime;
        this.startUIEffect = cfg.startUIEffect;
        this.startUIEffectTime = cfg.startUIEffectTime;
        this.endUIEffect = cfg.endUIEffect;
        this.endUIEffectTime = cfg.endUIEffectTime;
        this.uiSortingLayer = cfg.uiSortingLayer;
    }

    public override IEnumerator OnInit()
    {
        var camera = GetComponentInChildren<Camera>();
        if (camera != null)
        {
            camera.SetActive(false);
            camera.depth = uiSortingLayer;
        }
        yield return base.OnInit();

        InitComponents();
        Preload();

        // ACGEventManager.Instance.Send(ACGEventManager.Instance.CreateEvent(WinCntTips.HideFireFx, null, 1, true));
#if !OUTSOURCE
        // 等一下视频加载
        int waitFrame = 60;
        while (!_player.IsPrepare() && waitFrame > 0)
        {
            yield return null;
            --waitFrame;
        }
        Diagnostic.Log("ChessAttackMovieUI.OnInit waitFrame left: " + waitFrame + " prepare: " + _player.IsPrepare() + " moviePath: " + moviePath + " playerMoviePath: " + _player.VideoPath);
#endif

        // 加载完成以后要先隐藏
        Hide();

        if (camera != null)
            camera.SetActive(true);

        VisiableOnLoaded = false;

        m_resInited = true;
    }

    private void Preload()
    {
#if !OUTSOURCE
        _player.SetVideoClip(loadFromCos ? VideoComponentBase.FileLocation.DownLoadMovieFolder : VideoComponentBase.FileLocation.RelativeToStreamingAssetsFolder,
                moviePath, false, false);
#endif
        if (startUIEffect != null)
        {
            m_startUIEffectInst = GameObject.Instantiate(startUIEffect);
            m_startUIEffectInst.transform.SetParent(EffectNode.transform);
            ChessUtil.MakeTransformIdentity(m_startUIEffectInst.transform);
            m_startUIEffectInst.SetActive(false);

            var gfxRoot = m_startUIEffectInst.GetComponent<GfxFramework.GfxRoot>();
            if (gfxRoot != null)
            {
                gfxRoot.OnGfxPlayEnd = delegate (GfxFramework.GfxRoot gfx)
                {
                    gfx.SetActive(false);
                };
            }
        }

        endUIEffectGfxTime = 0;
        if (endUIEffect != null)
        {
            m_endUIEffectInst = GameObject.Instantiate(endUIEffect);
            m_endUIEffectInst.transform.SetParent(EffectNode.transform);
            ChessUtil.MakeTransformIdentity(m_endUIEffectInst.transform);
            m_endUIEffectInst.SetActive(false);

            var gfxRoot = m_endUIEffectInst.GetComponent<GfxFramework.GfxRoot>();
            if (gfxRoot != null)
            {
                endUIEffectGfxTime = gfxRoot.m_timeSystem.LifeTime;

                gfxRoot.OnGfxPlayEnd = delegate (GfxFramework.GfxRoot gfx)
                {
                    gfx.SetActive(false);
                };
            }
        }
    }

    public void Reset()
    {
        bool rewind = false;
#if !OUTSOURCE
        if (_player != null && _player.IsPrepare())
        {
            _player.Rewind(true);
            rewind = true;
        }
#endif
        HideAll();
        Hide();

        m_initTimeTag = false;
        m_playStartEffect = false;
        m_playMovieTime = 0;
        m_playMovie = false;
        m_playEndEffectTime = 0;
        m_playEndEffect = false;
        m_moviePlayCompleted = false;

        m_blendIn = false;
        m_blendInStartTime = 0;
        m_blendOut = false;
        m_blendOutStartTime = 0;

        m_finished = false;

        Diagnostic.Log("ChessAttackMovieUI.Reset rewind: " + rewind);
    }

    private void InitTimeTag(float time)
    {
        Diagnostic.Log("ChessAttackMovieUI.InitTimeTag time: " + time + " m_playMovieTime: " + m_playMovieTime + " m_playEndEffectTime: " + m_playEndEffectTime);

        m_playMovieTime = time + startUIEffectTime;
        m_playEndEffectTime = m_playMovieTime + movieTime;
        m_blendInStartTime = m_playMovieTime + blendInStartTime;
        m_blendOutStartTime = m_playMovieTime + blendOutStartTime;
        m_playEndEffectCompletedTime = m_playEndEffectTime + endUIEffectGfxTime;
    }

    public void RunTick(float time)
    {
        if (!m_resInited)
            return;

        if (!IsActive)
        {
            HideAll();
            Show();
        }

        if (!m_initTimeTag)
        {
            InitTimeTag(time);
            m_initTimeTag = true;
        }

        if (!m_playStartEffect)
        {
            PlayStartEffect();
            m_playStartEffect = true;
        }

        if (!m_playMovie && time >= m_playMovieTime)
        {
            InitBlendIn();
            PlayMovie();
            m_playMovie = true;
        }

        if (!m_blendIn && blendInTime > 0 && time > m_blendInStartTime)
        {
            BlendIn();

            m_blendIn = true;
        }

        if (!m_blendOut && blendOutTime > 0 && time > m_blendOutStartTime)
        {
            BlendOut();

            m_blendOut = true;
        }

        if (!m_playEndEffect && time > m_playEndEffectTime)
        {
            PlayEndEffect(time);
            m_playEndEffect = true;
        }

        if (!m_finished && m_moviePlayCompleted && time > m_playEndEffectCompletedTime)
        {
            OnFinished();

            m_finished = true;
        }
    }

    public void OnFinished()
    {
        HideAll();
        Hide();

        // ACGEventManager.Instance.Send(ACGEventManager.Instance.CreateEvent(WinCntTips.HideFireFx, null, 1, false));
    }

    public void HideAll()
    {
        if (m_startUIEffectInst != null)
        {
            m_startUIEffectInst.SetActive(false);
        }

        if (m_endUIEffectInst != null)
        {
            m_endUIEffectInst.SetActive(false);
        }

        GameUtil.SetScaleToZero(this.Player, true);
#if !OUTSOURCE
        if (_player != null)
            _player.Pause();
#endif

    }

    public void PlayStartEffect()
    {
        if (m_startUIEffectInst != null)
        {
            m_startUIEffectInst.SetActive(true);
        }
    }

    public void PlayEndEffect(float time)
    {
        if (m_endUIEffectInst != null)
        {
            m_endUIEffectInst.SetActive(true);
        }
    }

    private void PlayMovie()
    {
#if !OUTSOURCE
        tryIndex = 1;
        if (_player.VideoPath == moviePath && _player.IsPrepare())
        {
            _player.Play();

            Diagnostic.Log("[ChessAttackMovieUI.Play] : " + moviePath + " [cache]");
        }
        else
        {
            _player.SetVideoClip(loadFromCos ? VideoComponentBase.FileLocation.DownLoadMovieFolder : VideoComponentBase.FileLocation.RelativeToStreamingAssetsFolder,
                moviePath, false, true);

            Diagnostic.Log("[ChessAttackMovieUI.Play] : " + moviePath);
        }
        GameUtil.SetScaleToZero(this.Player, false);

        ACGEventManager.Instance.Send(ACGEventManager.Instance.CreateEvent(WinCntTips.HideFireFx, null, 1, true));
#endif
    }

    private void InitBlendIn()
    {
#if !OUTSOURCE
        if (blendInTime == 0)
        {
            var canvasGroup = _player.gameObject.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
                canvasGroup.alpha = 1;
        }
        else if (m_playMovieTime >= m_blendInStartTime)
        {
            var canvasGroup = _player.gameObject.TryGetComponent<CanvasGroup>();
            if (canvasGroup != null)
                canvasGroup.alpha = 0;
        }
#endif
    }

    private void BlendIn()
    {
#if !OUTSOURCE
        NTween.TweenAlpha.Begin(_player.gameObject, blendInTime, 0, 1, false, true);
#endif
    }

    private void BlendOut()
    {
#if !OUTSOURCE
        NTween.TweenAlpha.Begin(_player.gameObject, blendOutTime, 1, 0, false, true);
#endif
    }

    private void InitComponents()
    {
        var canvas = this._PanelGO.GetComponent<Canvas>();
        if (canvas != null)
        {
            canvas.sortingOrder = uiSortingLayer;
            var curStage = SystemManager.getInstance().GetStage() as BaseStage;
            if (curStage != null)
            {
                bool canSet = curStage is ChessBattleStage
                    || curStage is TinyAttackSceneUIStage;
                if (!canSet)
                {
                    canvas.sortingOrder = 0;
                }
            }
        }
#if !OUTSOURCE
        this._player = Player.GetComponent<VideoComponentAVPro>();
        this._player.OnVideoPreparedCompleted = this.OnPreparedCompleted;
        this._player.OnPlayErrorCallback = this.OnPlayErrorCallback;
        this._player.OnPlayCompleted = this.OnPlayCompleted;
        this._player.OnPlayStarted = this.OnStartPlay;
        if (adaptFullScreen)
        {
            this._screenBgScaler = this._player.GetComponent<ScreenBgScaler>();
        }
#endif
        GameUtil.SetScaleToZero(this.Player, true);
    }

    private void OnPreparedCompleted()
    {
        if (adaptFullScreen)
        {
            //适配到全屏，这样写是为了重构的时候，不影响原逻辑
            if (NotchSizeImp.IsPad)
            {

            }
            else
            {
                AdjustRectTrans();
                if (_screenBgScaler != null)
                    this._screenBgScaler.Addapt(ScreenBgScaler.Pivot_Type_Vertical.Middle);
            }
        }
        else
        {
            AdjustRectTrans();
        }
    }

    private void AdjustRectTrans()
    {
#if !OUTSOURCE
        _player?.FitVideoUI();
#endif
    }

    private void OnStartPlay()
    {
        Diagnostic.Log("[ChessAttackMovieUI.OnStartPlay] : " + moviePath);
        //GameUtil.SetScaleToZero(this.Player, false);
    }

    private void OnPlayCompleted()
    {
        Diagnostic.Log("[ChessAttackMovieUI.OnPlayCompleted] : " + moviePath);
        //dispose();
        m_moviePlayCompleted = true;

        if (completedAction == ChessAttackMovieConfig.PlayCompletedAction.Hide)
        {
            GameUtil.SetScaleToZero(this.Player, true);
        }

        m_owner.TriggerEvent(movieCompletedTrigger);

#if !OUTSOURCE
        ACGEventManager.Instance.Send(ACGEventManager.Instance.CreateEvent(WinCntTips.HideFireFx, null, 1, false));
#endif
    }
    public void dispose()
    {
#if !OUTSOURCE
        if (_player != null)
        {
            _player.OnVideoPreparedCompleted = null;
            _player.OnPlayErrorCallback = null;
            _player.OnPlayCompleted = null;
            _player.OnPlayStarted = null;
        }
#endif
        GameObject.Destroy(gameObject);
    }
    private void OnPlayErrorCallback(string errorMsg)
    {
        if (this.tryIndex <= 1)
        {
#if !OUTSOURCE
            _player.SetVideoClip(loadFromCos ? VideoComponentBase.FileLocation.DownLoadMovieFolder : VideoComponentBase.FileLocation.RelativeToStreamingAssetsFolder,
                moviePath, false, true);
#endif
            this.tryIndex++;
            Diagnostic.Warn("ChessAttackMovieUIDialog::OnPlayErrorCallback [" + errorMsg + "] try replay.");
        }
        else
        {
            Diagnostic.Warn("ChessAttackMovieUIDialog::OnPlayErrorCallback " + errorMsg);
            m_moviePlayCompleted = true;
        }
    }
}

