using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZGameChess
{
    // 局外预览专用
    public class ChessBattleLogicFieldPreview : ChessBattleLogicField
    {
        public bool willKill = false;

        public ChessBattleLogicFieldPreview() : base(0, null, -1)
        {
            NeedDeductLife = true;
        }

        protected override void OnHitBullet(ChessBattleLogicPlayer attacker, int hitterId, int deductLife)
        {
            //base.OnHitBullet(attacker, hitterId, deductLife);

            var hitter = owner.playerData.PlayerId == hitterId ? owner : enemy;
            hitter.SetHit(true, true, willKill, deductLife, deductLife, willKill ? 0 : 100);

            attacker.SetAttack(null);
        }
    }
}
