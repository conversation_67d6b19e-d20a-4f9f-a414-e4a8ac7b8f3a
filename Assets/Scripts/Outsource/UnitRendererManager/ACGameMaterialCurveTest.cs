using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using ZGame;
using ZGameChess;

public class ACGameMaterialCurveTest : MonoBehaviour
{
    // this script only available in editor, for testing purpose.
#if UNITY_EDITOR && !ENABLE_TYPE_TREE_IGNORE

    public List<ACGameMaterialCurve> CurveToExecuteList;

    private Unit m_Unit;

    private void Awake()
    {
        
    }

    private IEnumerator Start()
    {
        DataBaseManager.Instance.Initialize();
        AssetService assetService = TKFrame.Services.GetService<TKFrame.IAssetService>() as AssetService;
        if (assetService == null)
        {
            assetService = gameObject.AddComponent(typeof(AssetService)) as AssetService;
            yield return assetService.Initialize();
        }

        if (ChessBattleGlobal.Instance.HeroMatCfg == null)
        {
            ChessBattleGlobal.Instance.HeroMatCfg = new HeroMaterialConfig();
            yield return ChessBattleGlobal.Instance.HeroMatCfg.Init();
        }

        m_Unit = GetComponent<Unit>();
        m_Unit.OnBodyLoaded();
    }

    private IEnumerator DoExecuteImpl()
    {
        if (m_Unit == null)
            yield return Start();
        if (m_Unit == null)
            throw new System.ArgumentNullException("ACGameMaterialCurveTest: 该脚本只能在战场编辑器使用");
        else
        {
            for (int i = 0, len = CurveToExecuteList.Count; i < len; i++)
            {
                if (CurveToExecuteList[i] != null)
                {
                    m_Unit.HeroMaterialChange(CurveToExecuteList[i]);
                }
            }
        }
    }

    public void DoExecute()
    {
        StartCoroutine(DoExecuteImpl());
    }

    IEnumerator DoRestoreImpl()
    {
        if (m_Unit == null)
            yield return Start();
        if (m_Unit == null)
            throw new System.ArgumentNullException("ACGameMaterialCurveTest: 该脚本只能在战场编辑器使用");
        else
            m_Unit.ResetMat();
    }

    public void DoRestore()
    {
        StartCoroutine(DoRestoreImpl());
    }


#elif !UNITY_EDITOR

    private void Start()
    {
        throw new System.InvalidOperationException("ACGameMaterialCurveTest: 这个脚本是用于特效测试用，请不要用于游戏版本内，该脚本挂在" + gameObject.name + "上");
    }
    
#endif
}