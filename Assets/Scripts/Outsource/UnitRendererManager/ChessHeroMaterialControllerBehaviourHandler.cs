using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using TKFrame.COSResource;
using TKFrame.Utility;
using UnityEngine;

// AddComponent<MonoBehaviour> is not allowed. So create an empty class derive it.
// using class Unit as coroutine handler will cause unexpected problem, so be it.
public class ChessHeroMaterialControllerBehaviourHandler : MonoBehaviour
{
    private ChessHeroMaterialController controller;

    private List<ChessHeroMaterialCurveTask> cuverTasks = new List<ChessHeroMaterialCurveTask>();

    //curve的组合器, 美术需求多个cfg同时存在;
    public ACGameMaterialCurveComposer CurveComposer { get; private set; }

    private List<ISmartResourceLoader> loaders = new List<ISmartResourceLoader>();
    //private UnityEngine.Coroutine coroutine = null;

    #region 内存池
    private static Stack<ChessHeroMaterialCurveTask> ms_taskPool = new Stack<ChessHeroMaterialCurveTask>();

    private static ChessHeroMaterialCurveTask AllocTask()
    {
        if (ms_taskPool.Count > 0)
        {
            return ms_taskPool.Pop();
        }
        else
        {
            return new ChessHeroMaterialCurveTask();
        }
    }
    private static void ReleaseTask(ChessHeroMaterialCurveTask task)
    {
        task.Reset();
        ms_taskPool.Push(task);
    }
    #endregion

    public void SetController(ChessHeroMaterialController controller)
    {
        this.controller = controller;
        this.CurveComposer = new ACGameMaterialCurveComposer(controller);
    }

    public void RunCurve(ACGameMaterialCurve curve, float overrideTime, bool competeRightNow)
    {
        var task = AllocTask();
        task.Initialize(controller, curve, overrideTime, competeRightNow);
        cuverTasks.Add(task);

        //if (gameObject.activeSelf && gameObject.activeInHierarchy && coroutine == null)
        //{
        //    coroutine = StartCoroutine(RunCurveTask());
        //}
    }

    public void AddLoader(ISmartResourceLoader loader)
    {
        if (loader != null)
            loaders.Add(loader);
    }

    public void ClearAllCurve(bool includeRuningCurve = false)
    {
        cuverTasks.Clear();

        if (includeRuningCurve)
        {
            CurveComposer.Clear();
        }
    }

    /*private void Update()
    {
        if (loaders.Count > 0)
        {
            for (int i = 0; i < loaders.Count; ++i)
            {
                // 如果有必要资源还在加载中，则不执行任何Curve，等资源加载完成后再执行
                if (loaders[i] != null && !loaders[i].IsLoaded())
                    return;
            }

            loaders.Clear();
            return;
        }

        bool competeRightNow = false;
        if (cuverTasks.Count > 0)
        {
            competeRightNow = TickTask();
        }

        if (CurveComposer.CurrentRunType != ACGameMaterialCurveComposer.RunType.None)
        {
            TickCurveComposer();
        }

        if (competeRightNow)
        {
            CurveComposer.CompeteRightNow();
        }
    }*/

    /*private bool TickTask()
    {
        bool competeRightNow = false;
        for (int i = 0; i < cuverTasks.Count; ++i)
        {
            var task = cuverTasks[i];
            task.DoUpdate();
        }

        for (int i = cuverTasks.Count - 1; i >= 0; --i)
        {
            var task = cuverTasks[i];
            if (task.state == ChessHeroMaterialCurveTask.State.Finished)
            {
                CurveComposer.AddCurve(task.curveInfo, task.overrideTime);
                if (task.competeRightNow)
                    competeRightNow = true;
                ReleaseTask(task);
                cuverTasks.RemoveAt(i);
            }
        }
        return competeRightNow;
    }*/

    /// <summary>
    /// 如果有任务未完成, 需要恢复携程
    /// </summary>
    private void OnEnable()
    {
        //if (cuverTasks.Count > 0 || 
        //    (CurveComposer != null && CurveComposer.CurrentRunType != ACGameMaterialCurveComposer.RunType.None))
        //{
        //    if (coroutine == null)
        //    {
        //        coroutine = StartCoroutine(RunCurveTask());
        //    }
        //}
    }

    private void OnDisable()
    {
        //coroutine = null;
    }

    /*private void TickCurveComposer()
    {
        controller.TickCurveComposer(CurveComposer);
    }*/
}