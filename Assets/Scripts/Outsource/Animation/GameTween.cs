#if ACGGAME_CLIENT
using System;
using DG.Tweening;
using NTween;
using UnityEngine;
using UnityEngine.UI;
using UITweenerEase = NTween.UITweener.Method;
using UITweenerLoopType = NTween.UITweener.Style;

/// <summary>
/// Desc: 缓动便捷接口;
/// Author : xfilsonpan
/// Date : 9/3/2019
/// </summary>
public class GameTween
{
    public delegate void OnUpdateValueFloatCallback(float value);
    private static void SetCommonProperties(UITweener tweener, float delay, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null) {
        tweener.delay = delay;
        tweener.loopTimes = loopCount;
        tweener.style = loopType;
        tweener.method = ease;
        tweener.animationCurve = animationCurve;
    }

    public static UITweener DOFov(GameObject go, float startValue, float endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        Camera cam = go.GetComponent<Camera>();
        if (cam == null)
        {
            return null;
        }
        UITweener tweener = TweenFov.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }

    public static UITweener DOColor(GameObject go, Color startValue, Color endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        Graphic uiBasicSprite = go.GetComponent<Graphic>();
        if (uiBasicSprite == null)
        {
            return null;
        }
        UITweener tweener = TweenColor.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
    
    public static UITweener DOCanvasRenderColor(GameObject go, Color startValue, Color endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        CanvasRenderer canvasRenderer = go.GetComponent<CanvasRenderer>();
        if (canvasRenderer == null)
        {
            return null;
        }
        UITweener tweener = TweenCanvasRenderColor.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }

    public static UITweener DOCanvasGroupAlpha(GameObject go, float startValue, float endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        TweenAlpha tweener = TweenAlpha.Begin(go, duration, startValue, endValue, false, true);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }

    public static UITweener DOAnchorPosMove(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        UITweener tweener = TweenAnchoredPosition.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
#if !OUTSOURCE
    public static UITweener DOAnchorLayoutElement(GameObject go, LayoutElementData startValue, LayoutElementData endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null, bool needResetByInitValues = true)
    {
        if (startValue.usePreferredHeightRate && endValue.usePreferredHeightRate)
        {
            var tle = go.GetComponent<TweenLayoutElementData>();
            if (tle != null && needResetByInitValues)
            {
                tle.ResetAwakeValue();
            }

            //这里就要进过一次转换了;
            LayoutElement le = go.GetComponent<LayoutElement>();
            if (le != null)
            {
                float rawHeightValue = le.preferredHeight;
                startValue.SetValueByLayoutElement(le);
                startValue.preferredHeight = rawHeightValue * startValue.preferredHeightRate;
                
                endValue.SetValueByLayoutElement(le);
                endValue.preferredHeight = rawHeightValue * endValue.preferredHeightRate;
            }
        }

        UITweener tweener = TweenLayoutElementData.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
#endif

    public static UITweener DOWorldMove(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        UITweener tweener = TweenWorldPosition.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }

    public static UITweener DOLocalMove(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        UITweener tweener = TweenPosition.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
    
    public static UITweener DOWorldRotate(GameObject go, Quaternion startValue, Quaternion endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null, TweenWorldRotation.RotateMode rotateMode = TweenWorldRotation.RotateMode.FastBeyond360)
    {
        UITweener tweener = TweenWorldRotation.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            TweenWorldRotation twr = tweener as TweenWorldRotation;
            twr.rotateMode = rotateMode;
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
    
    public static UITweener DOLocalRotate(GameObject go, Quaternion startValue, Quaternion endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        UITweener tweener = TweenRotation.Begin(go, duration, startValue, endValue, true);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
    
    public static UITweener DOScale(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, UITweenerLoopType loopType = UITweenerLoopType.Once, UITweenerEase ease = UITweenerEase.Linear, AnimationCurve animationCurve = null)
    {
        UITweener tweener = TweenScale.Begin(go, duration, startValue, endValue);
        if(tweener != null){
            SetCommonProperties(tweener, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }

    public static void DoKill(Transform transform) {
        if (transform == null) return;
		UITweener[] tweenerArr = transform.GetComponentsInChildren<UITweener>(true);
        for(int i = 0, len = tweenerArr.Length; i<len; i++){
            DoKill(tweenerArr[i]);
        }
	}

	public static void DoKill(UITweener tweener)
	{
        if (tweener == null) return;
        GameObject.Destroy(tweener);
	}

    public static void DoKillDOTween(Component com)
    {
        com.DOKill();
    }

    public static bool IsTweening(Transform transform) {
        if (transform == null) return false;
        UITweener[] tweenerArr = transform.GetComponentsInChildren<UITweener>();
        for(int i = 0, len = tweenerArr.Length; i<len; i++){
            if(tweenerArr[i].enabled){
                return true;
            }
        }
        return false;
	}

    #region DOTween相关

    public static Tweener DOPath(Transform oprTrans, Vector3[] path,
        float duration,
        PathType pathType = PathType.Linear,
        PathMode pathMode = PathMode.Full3D,
        int resolution = 10,
        Color? gizmoColor = null, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Tweener tweener = oprTrans.DOPath(path, duration, pathType, pathMode, resolution, gizmoColor);
        tweener.SetLoops(loopCount, loopType);
        if (ease.Equals(Ease.Unset) || ease.Equals(Ease.INTERNAL_Custom))
        {
            tweener.SetEase(animationCurve);
        }
        else
        {
            tweener.SetEase(ease);
        }
        return tweener;
    }
    
    public static Tweener DoFov(GameObject go, float startValue, float endValue, float delay, float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Camera camera = go.GetComponent<Camera>();
        Tweener tweener = camera.DOFieldOfView(endValue, duration);
        if(tweener != null){
            SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        }
        return tweener;
    }
    
    public static Tweener DoImageFillAmount(GameObject go, float startValue, float endValue, float delay,
        float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Image img = go.GetComponent<Image>();
        if (img == null)
        {
            return null;
        }
        Tweener tweener = DOTween.To(() => img.fillAmount, x => img.fillAmount = x, endValue, duration).SetTarget(img);
        SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        return tweener;
    }
    
    public static Tweener DOColor(GameObject go, Color startValue, Color endValue, float delay, float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Graphic uiBasicSprite = go.GetComponent<Graphic>();
        if (uiBasicSprite == null)
        {
            return null;
        }
        Tweener tweener = DOTween.To(() => uiBasicSprite.color, x => uiBasicSprite.color = x, endValue, duration).SetTarget(uiBasicSprite);
        SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        return tweener;
    }
    
    public static Tweener DOCanvasRenderColor(GameObject go, Color startValue, Color endValue, float delay, float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Graphic graphic = go.GetComponent<Graphic>();
        if (graphic == null || graphic.canvasRenderer == null)
        {
            return null;
        }
        Tweener tweener = DOTween.To(() => graphic.canvasRenderer.GetColor(), x => graphic.canvasRenderer.SetColor(x), endValue, duration).SetTarget(graphic);
        SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        return tweener;
    }
    
    public static Tweener DOScale(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Tweener tweener = go.transform.DOScale(endValue, duration);
        SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        return tweener;
    }
    
    public static Tweener DOWorldRotate(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Tweener tweener = go.transform.DORotate(endValue, duration);
        SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        return tweener;
    }
    
    public static Tweener DOWorldMove(GameObject go, Vector3 startValue, Vector3 endValue, float delay, float duration, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null)
    {
        Tweener tweener = go.transform.DOMove(endValue, duration);
        SetCommonProperties(tweener, startValue, delay, loopCount, loopType, ease, animationCurve);
        return tweener;
    }
    
    public static void DoKill(Tweener tweener)
    {
        if (tweener == null) return;
        tweener.Kill();
    }
    
    public static void DoPause(Tweener tweener)
    {
        if (tweener == null) return;
        tweener.Pause();
    }
    
    public static void SetCommonProperties(Tweener tweener, object startValue, float delay, int loopCount = 1, LoopType loopType = LoopType.Incremental, Ease ease = Ease.Linear, AnimationCurve animationCurve = null) {
        tweener.OnStart(delegate() { tweener.ChangeStartValue(startValue);});
        if (delay > 0)
        {
            tweener.SetDelay(delay);   
        }

        tweener.SetLoops(loopCount, loopType);
        if (ease.Equals(Ease.Unset) || ease.Equals(Ease.INTERNAL_Custom))
        {
            tweener.SetEase(animationCurve);
        }
        else
        {
            tweener.SetEase(ease);
        }
    }
    

    #endregion

}  
#endif