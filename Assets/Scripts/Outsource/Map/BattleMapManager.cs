using GameFramework.FMath;
using GfxFramework;
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using ZGame;
using ZGame.GameSystem;
using System.Text;
using ACG.Core;

#if UNITY_EDITOR && !OUTSOURCE
using Camera<PERSON>ensDirector;
#endif

namespace ZGameChess
{
    public class BattleMapManager : TKBehaviour
    {
        public static bool PARTICLE_SYSTEM_CULLING = true;

        //public const int ROUND_SELECT_SCENE_ID = -1;
        //public const string ROUND_SELECT_SCENE_NAME = "s2_tft_battlefield_center";
        public static readonly Vector3 ROUND_SELECT_SCENE_POSITION = new Vector3(500, 0, 0);

        //public string m_RoundSelectSceneName = "s2_tft_battlefield_center";

        private static BattleMapManager _instance = null;
        private static List<BattleMapManager> m_instanceStack = new List<BattleMapManager>();
        public static BattleMapManager Instance
        {
            get
            {
                return _instance;
            }
        }

        [SerializeField]
        private GameObject m_gameDynamicRoot;
        [SerializeField]
        private GameObject m_roundSelectDynamicRoot;
#if UNITY_EDITOR
        public void SetGameDynamicRoot(GameObject gameDynamicRoot)
        {
            m_gameDynamicRoot = gameDynamicRoot;
        }
#endif

        private Camera m_gameSceneCamera;
        private Transform m_gameSceneCameraAnimation;
        private List<Collider> m_gameSceneColliders = new List<Collider>();
        private GameObject m_gameSceneProjector;

        private Camera m_roundSelectSceneCamera;
        private Transform m_roundSelectCameraAnimation;
        private List<Collider> m_roundSelectColliders = new List<Collider>();
        private GameObject m_roundSelectProjector;

        private GameObject m_defaultInterestTemplate = null;

        //private string m_defaultMapName = string.Empty;

        private string m_currentActiveMap = string.Empty;
        private List<int> m_listBlocks = null;
        private LinkedHashMap<string, BattleMap> m_sceneDict = new LinkedHashMap<string, BattleMap>(9);     // 8个棋盘+轮抽棋盘

        private bool m_waitResLoaded = false;
        //目前只有战斗场景里增加了脚本;
        private FightBattleCameraShake _fightBattleCameraShake;
        //public static bool FixTinyHeroAndMap = true;
        public List<Transform> RoundSelectLODEffect = null;

        private HashSet<string> m_unloadMaps = new HashSet<string>();

        #region ROUND_SELECT_PARTICLE_CULL

        private const int MAX_DISABLE_CULL_FRAME = 5;

        private Transform m_camTransform;
        private Vector3 m_camLastPosition = Vector3.zero;
        private bool m_cullEnable = false;
        private int m_disableCullFrame = MAX_DISABLE_CULL_FRAME;

        #endregion


        private Dictionary<int,GameObject> m_sourceInterestDic;
        private Dictionary<int, GameObject> m_sourceInterest2Dic;
        private List<int> m_tempBlocks;

        public GameObject TryGetSourceInterest(GameObject go, bool home, int mapID)
        {
            if (go == null)
            {
                return null;
            }

            if (home)
            {
                if (m_sourceInterestDic == null)
                {
                    m_sourceInterestDic = new Dictionary<int, GameObject>();
                }

                if (!m_sourceInterestDic.ContainsKey(mapID))
                {
                    m_sourceInterestDic.Add(mapID, go);
                }
                else if (m_sourceInterestDic[mapID] == null)
                {
                    m_sourceInterestDic[mapID] = go;
                }

                if (m_sourceInterestDic.ContainsKey(mapID))
                {
                    return m_sourceInterestDic[mapID];
                }
            }
            else
            {
                if (m_sourceInterest2Dic == null)
                {
                    m_sourceInterest2Dic = new Dictionary<int, GameObject>();
                }

                if (!m_sourceInterest2Dic.ContainsKey(mapID))
                {
                    m_sourceInterest2Dic.Add(mapID, go);
                }
                else if (m_sourceInterest2Dic[mapID] == null)
                {
                    m_sourceInterest2Dic[mapID] = go;
                }

                if (m_sourceInterest2Dic.ContainsKey(mapID))
                {
                    return m_sourceInterest2Dic[mapID];
                }
            }

            return null;
        }

        public Transform GetBigCat()
        {
            var catroot = GetCatRoot();
            if (catroot != null)
            {
                return catroot.Find("s8_carousel_bigcat");
            }

            return null;
        }


        public Transform GetCatRoot()
        {
            var temp = this.transform.Find("s8_tft_battlefield_center(Clone)");
            if (temp != null)
            {
                var animation = temp.Find("effects");
                if (animation != null)
                {
                    var bigcat = animation.Find("s8_carousel_blackhole");
                    if (bigcat != null)
                    {
                        return bigcat.Find("rot");
                    }
                }
            }
            return null;
        }



        public Transform GetSmallCat()
        {
            var catroot = GetCatRoot();
            if (catroot != null)
            {
                return catroot.Find("s8_carousel_littlecat");
            }

            return null;
        }

        protected override void Awake()
        {
            base.Awake();

            //if (Instance != null)
            //{
            //    Diagnostic.Error("Muti Instance BattleSceneManager.");
            //    return;
            //}

            m_instanceStack.Add(this);
            _instance = this;

            Diagnostic.Log("BattleSceneManager Awake. m_instanceStack.Count: " + m_instanceStack.Count + " hierarchyPath: " + GameUtil.GetHierarchyPath(transform));
            //Instance = this;
        }

        private void RegistUpdateCamera()
        {
            UnRegistUpdateCamera();
            V_DriverManager.Instance.updateRunner.DriveMethod(LateUpdateCamera, RunPriority.LATECAMERA + 1);
        }

        private void UnRegistUpdateCamera()
        {
            var dm = V_DriverManager.Instance;
            if (dm != null)
                dm.updateRunner.RemoveDriveMethod(LateUpdateCamera);
        }

        private void LateUpdateCamera()
        {
            if (m_camTransform.position != m_camLastPosition)
            {
                m_camLastPosition = m_camTransform.position;
                m_disableCullFrame = MAX_DISABLE_CULL_FRAME;

                if (!m_cullEnable)
                {
                    m_cullEnable = true;
#if !OUTSOURCE
                    var curMap = GetCurrentBattleMap();
                    if (curMap != null)
                        curMap.StartCullGfx();
#endif
                }
            }
            else if (m_cullEnable)
            {
                --m_disableCullFrame;
                if (m_disableCullFrame <= 0)
                {
                    m_cullEnable = false;
#if !OUTSOURCE
                    var curMap = GetCurrentBattleMap();
                    if (curMap != null)
                        curMap.PauseCullGfx();
#endif
                }
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            UnRegistUpdateCamera();

            foreach (var sence in m_sceneDict)
            {
                sence.Value.Unload(this);
                sence.Value.Dispose();
            }

            Deactive();

            if (m_instanceStack.Count > 0)
                m_instanceStack.Remove(this);

            if (_instance == this)
                _instance = null;

            //foreach (var sence in m_sceneDict)
            //{
            //    sence.Value.Dispose();
            //}
            m_sceneDict.Clear();
            Diagnostic.Log("BattleSceneManager OnDestroy. m_instanceStack.Count: " + m_instanceStack.Count + " clearInstance: " + (_instance == null) + " hierarchyPath: " + GameUtil.GetHierarchyPath(transform));
            //Instance = null;
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            // 每次激活的时候 都转移一下当前的实例
            if (_instance != this)
            {
                Diagnostic.Log("BattleSceneManager OnEnable. m_instanceStack.Count: " + m_instanceStack.Count + " switch instance hierarchyPath: " + GameUtil.GetHierarchyPath(transform));
                _instance = this;
            }
        }

        public Transform GetCamAnimitionGameObject()
        {
            return m_gameSceneCameraAnimation;
        }

        // 主要加载的时候是想要预加载一些东西，预加载的时候不希望场景的dynamic节点设置为隐藏
        // 另外加载界面也不希望听到地图上的音效
        public void SetWaitResLoad(bool wait)
        {
            m_waitResLoaded = wait;

            if (!m_waitResLoaded)
            {
                ActiveCurMap();
            }
        }

        public void Initialize(/*int sceneId, ESetID setId*/)
        {
            if (m_gameDynamicRoot != null)
            {
                m_gameSceneCamera = InitializeCamera(m_gameDynamicRoot, true);
                m_gameSceneCameraAnimation = GameUtil.FindTransform(m_gameDynamicRoot.transform, "CamAnimation");

                m_gameSceneColliders.Clear();
                m_gameDynamicRoot.GetComponentsInChildren<Collider>(m_gameSceneColliders);

                var projector = m_gameDynamicRoot.GetComponentInChildren<Projector>(true);
                if (projector != null)
                {
                    m_gameSceneProjector = projector.gameObject;
                    if (!m_gameSceneProjector.activeSelf)
                        m_gameSceneProjector.SetActive(true);
                }

                // 支持缩放
                if (m_gameSceneCamera != null)
                {
                    var zoom = m_gameSceneCamera.gameObject.GetComponent<CameraPanAndZoom>();
                    if (zoom != null)
                    {
                        zoom.OnCameraPositionChanged = OnCameraPositionChanged;
                    }

#if UNITY_EDITOR && !OUTSOURCE
                    var camDirector = m_gameSceneCamera.gameObject.GetComponent<CameraDirector>();
                    if (camDirector != null)
                    {
                        camDirector.OnCameraPositionChanged = OnCameraPositionChanged;
                    }
#endif
                    if (GameLOD.Instance.UseOffscreenRender)
                    {
                        var offscreenRender = m_gameSceneCamera.GetComponent<GfxOffScreenRenderCamera>();
                        if (offscreenRender != null)
                        {
                            offscreenRender.enabled = true;
                        }
                    }

                    _fightBattleCameraShake = m_gameSceneCameraAnimation.gameObject.TryGetComponent<FightBattleCameraShake>();
                    if (_fightBattleCameraShake.fightCameraShakeConfig == null)
                    {
                        Diagnostic.Warn("camerashake config is null !!! ");
                    }
                }
            }
            else
            {
                Diagnostic.Warn("[BattleScene.Initialize] m_gameDynamicRoot is null!!!");
            }

            if (m_roundSelectDynamicRoot != null)
            {
                m_roundSelectSceneCamera = InitializeCamera(m_roundSelectDynamicRoot, false);
                m_roundSelectCameraAnimation = GameUtil.FindTransform(m_roundSelectDynamicRoot.transform, "CamAnimation");

                m_roundSelectColliders.Clear();
                m_roundSelectDynamicRoot.GetComponentsInChildren<Collider>(m_roundSelectColliders);
                m_roundSelectDynamicRoot.transform.localPosition = ROUND_SELECT_SCENE_POSITION;

                var projector = m_roundSelectDynamicRoot.GetComponentInChildren<Projector>();
                if (projector != null)
                    m_roundSelectProjector = projector.gameObject;

                if (m_roundSelectSceneCamera != null)
                {
                    var zoom = m_roundSelectSceneCamera.gameObject.GetComponent<CameraPanAndZoom>();
                    if (zoom != null)
                    {
                        zoom.isRoundSelectCamera = true;
                        zoom.OnCameraPositionChanged = OnCameraPositionChanged;
                    }

#if UNITY_EDITOR && !OUTSOURCE
                    var camDirector = m_gameSceneCamera.gameObject.GetComponent<CameraDirector>();
                    if (camDirector != null)
                    {
                        camDirector.OnCameraPositionChanged = OnCameraPositionChanged;
                    }
#endif

                    if (GameLOD.Instance.UseOffscreenRender)
                    {
                        var offscreenRender = m_roundSelectSceneCamera.GetComponent<GfxOffScreenRenderCamera>();
                        if (offscreenRender != null)
                        {
                            offscreenRender.enabled = true;
                        }
                    }
                }
            }
            else
            {
                Diagnostic.Warn("[BattleScene.Initialize] m_roundSelectDynamicRoot is null!!!");
            }
        }

        public IEnumerator InitializeInterest()
        {
            var interestAsset = ResourceUtil.LoadAsset<GameObject>("art_tft_raw/interest/i_interest", "i_interest", null, this);
            while (!interestAsset.IsLoaded) yield return null;
            m_defaultInterestTemplate = interestAsset.GetAsset<GameObject>();
        }

        public void FreezeCameraPanAndZoom()
        {
            // 不要直接移除或disable, 相机控制脚本需要监听部分回调事件处理一些特殊情况, 比如折叠屏展开
            CameraPanAndZoom camCtrl = m_gameSceneCamera.gameObject.GetComponent<CameraPanAndZoom>();
            if (camCtrl != null)
            {
                camCtrl.FreezeCamera = true;
            }
        }

        public void UnFreezeCameraPanAndZoom()
        {
            if(m_gameSceneCamera != null && m_gameSceneCamera.gameObject != null)
            {
                CameraPanAndZoom camCtrl = m_gameSceneCamera.gameObject.GetComponent<CameraPanAndZoom>();
                if (camCtrl != null)
                {
                    camCtrl.FreezeCamera = false;
                }
            }
        }

        //public Camera GetSceneCamera()
        //{
        //    if (_gameSceneCamera == null)
        //        _gameSceneCamera = InitializeCamera(_gameDynamicRoot);
        //    return _gameSceneCamera;
        //}

        private Camera InitializeCamera(GameObject root, bool initCamera)
        {
            if (root == null)
            {
                Diagnostic.Error("[BattleScene.InitializeCamera] root is null!!!");
                return null;
            }
            var sceneCamera = root.GetComponentInChildren<Camera>(true);
            if (sceneCamera == null)
            {
                Diagnostic.Error("[[BattleScene.InitializeCamera] _gameDynamicRoot find camera failed!!!");
                return null;
            }
            sceneCamera.gameObject.SetActive(initCamera);
            sceneCamera.enabled = true;
            sceneCamera.cullingMask &= ~(1 << GameObjectLayer.UI);

            return sceneCamera;
        }

        public void LoadMapByName(int mapId, string mapName, List<int> blocklist = null, bool isMineMap = false)
        {
            BattleMap battleScene;
            if (m_sceneDict.TryGetValue(mapName, out battleScene))
            {
                if (battleScene.IsLoading || battleScene.IsLoaded)
                {
                    if (isMineMap)
                        battleScene.NeedSetState = true;
                    return;
                }
                battleScene.Load(this);
            }
            else
            {
                Projector projector = null;
                if (mapId == SDFConfigCache.ROUND_SELECT_SCENE_ID)
                    projector = m_roundSelectProjector?.GetComponent<Projector>();
                else
                    projector = m_gameSceneProjector?.GetComponent<Projector>();

                battleScene = new BattleMap(mapId, mapName, projector, this, OnLoadMapFinished);
                battleScene.Load(this);
                m_sceneDict.Add(mapName, battleScene);
            }

            battleScene.SetNeedLoadBlocks(blocklist);

            if (isMineMap)
                battleScene.NeedSetState = true;
        }

        public void LoadMap(int mapId, bool isMineMap = false)
        {
            // 设置一个默认的map
            if (mapId == 0)// || FixTinyHeroAndMap)
                mapId = SDFConfigCache.DEFAULT_SCENE_ID;

            string mapName = GetMapNameById(mapId);
#if !OUTSOURCE
            List<int> blockIDs = DataBaseManager.Instance.GetMapCanUseDIYPartIDs(mapId);
#else
            List<int> blockIDs = new List<int>();
#endif

            bool isInGame = false;

            //筛选出玩家使用的
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel != null)
            {
                var allPlayers = battleModel.GetAllPlayers();
                if (allPlayers != null && allPlayers.Count > 0)
                {
                    isInGame = true;
                    if (m_tempBlocks == null)
                    {
                        m_tempBlocks = new List<int>();
                    }
                    m_tempBlocks.Clear();

                    foreach (var player in allPlayers.Values)
                    {
                        if (player != null
                            && player.Playerinfo != null
                            && player.Playerinfo.stTAC_GameTinyData != null
                            && player.Playerinfo.stTAC_GameTinyData.iMapId == mapId
                            && player.Playerinfo.stTAC_GameTinyData.mapTinyMapDIYPartId != null)
                        {
                            foreach (var kvp in player.Playerinfo.stTAC_GameTinyData.mapTinyMapDIYPartId)
                            {
                                m_tempBlocks.Add(kvp.Value);
                            }
                        }
                    }
                }
            }

            List<int> needLoadBlockIDs = null;
            if (isInGame)
            {
                //局内筛选出所有玩家带的部件
                if (blockIDs != null)
                {
                    needLoadBlockIDs = new List<int>();
                    foreach (var blockID in blockIDs)
                    {
                        if (m_tempBlocks.Contains(blockID))
                        {
                            needLoadBlockIDs.Add(blockID);
                        }
                    }
                }
            }
            else
            {
                //局外直接加载所有的
                needLoadBlockIDs = blockIDs;
            }

            LoadMapByName(mapId, mapName, needLoadBlockIDs, isMineMap);
        }

#if UNITY_EDITOR
        public void BindMapInEditor(GameObject map)
        {
            if (map == null)
                return;
            var battleScene = new BattleMap(0, map.name, null, this, OnLoadMapFinished);
            battleScene.BindSceneObject(map);
            battleScene.Active = true;
            battleScene.InitSDFData();
            m_sceneDict.Add(map.name, battleScene);

            m_currentActiveMap = map.name;
        }
#endif

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mapId"></param>
        /// <param name="needRealUnload">实际战斗中不去卸载 只有编辑器用</param>
        public void UnloadMap(int mapId, bool needRealUnload = false)
        {
            string mapName = GetMapNameById(mapId);
            Diagnostic.Log(" BattleMapManager unload map: {0} {1} needRealUnload: {2}", mapId, mapName, needRealUnload);
            BattleMap battleScene;
            if (m_sceneDict.TryGetValue(mapName, out battleScene))
            {
                if (needRealUnload)
                {
                    if (!battleScene.Active)
                    {
                        battleScene.Unload(this);
                        battleScene.Dispose();
                        m_sceneDict.Remove(mapName);

                        if (!m_unloadMaps.Contains(mapName))
                            m_unloadMaps.Add(mapName);
                    }
                }
            }
            else
            {
                Diagnostic.Warn("Find Map: {0} Faild!", mapName);
            }
        }

        public bool NeedRealUnload()
        {
            return m_unloadMaps.Count >= 8;
        }

        public void ClearRecordUnloadMap()
        {
            m_unloadMaps.Clear();
        }
#if !OUTSOURCE
        /// <summary>
        /// 百人专用 加载对手棋盘并且卸载无用棋盘
        /// </summary>
        public void LoadEnemyMapAndUnloadOther(List<int> vecAllOpponentMatch)
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            if (!battleModel.IsHundredMode())
                return;

            // 百人分为两个阶段
            // 开始的时候，只能看到我方棋盘和对手棋盘，如果这一回合我们要去客场作战，则需要加载对手棋盘，如果主场作战，则不需要加载棋盘，只卸载上一场（如果有）的对手棋盘
            // 决赛阶段，此时和正常对局没什么区别，所以只需要加载所有棋盘，及时卸载已经失败的人的棋盘就好了。
            List<PlayerModel> playerModels = new List<PlayerModel>();
            if (!battleModel.IsBRInFinalPhase && false)
            {
                var myPlayerModel = battleModel.GetMyPlayerModel();
                playerModels.Add(myPlayerModel);

                var matchList = vecAllOpponentMatch;
                for (int i = 0; i < matchList.Count; ++i)
                {
                    int match_data = matchList[i];
                    int ghostFlag = match_data & 0x1;
                    int player1 = match_data >> 16;
                    int player2 = (match_data >> 8) & 0xff;
                    //客场
                    if (player2 == myPlayerModel.PlayerId && ghostFlag == 0)
                    {
                        playerModels.Add(battleModel.GetPlayerModel(player1));
                    }
                }
            }
            else
            {
                var matchList = vecAllOpponentMatch;
                HashSet<int> _chairSet = new HashSet<int>();
                for (int i = 0; i < matchList.Count; ++i)
                {
                    int match_data = matchList[i];
                    int player1 = match_data >> 16;
                    int player2 = (match_data >> 8) & 0xff;

                    _chairSet.Add(player1); _chairSet.Add(player2);
                }
                foreach (var id in _chairSet)
                    playerModels.Add(battleModel.GetPlayerModel(id));
            }

            AutoLoadAndUnloadMapImpl(playerModels);
        }

        public bool UnloadUnuseMap()
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            if (battleModel.IsHundredMode())
                return false;

            // 只找那些还活着的人
            List<PlayerModel> playerModels = new List<PlayerModel>();
            var allPlayerModels = battleModel.GetAllPlayers();
            foreach (var pm in allPlayerModels)
            {
                if (pm.Value.IsDead() == false || pm.Value.PlayerId == battleModel.CurrentPlayerId || pm.Value.PlayerId == battleModel.MyPlayerId)
                    playerModels.Add(pm.Value);
            }

            return AutoLoadAndUnloadMapImpl(playerModels);
        }

        private bool AutoLoadAndUnloadMapImpl(List<PlayerModel> playerModels)
        {
            StringBuilder sb = new StringBuilder();
            var mapMgr = BattleMapManager.Instance;
            List<int> mapList = mapMgr.GetAllMapIDList();
            HashSet<int> unloads = new HashSet<int>();
            foreach (var mapId in mapList)
            {
                // 轮抽棋盘不卸载
                if (mapId == -1)
                    continue;
                if (!unloads.Contains(mapId))
                    unloads.Add(mapId);
            }

            for (int i = 0; i < playerModels.Count; ++i)
            {
                var pm = playerModels[i];
                if (pm.Playerinfo == null)
                {
                    Diagnostic.Log("ChessBattleAdapter_LoadEnemyMapAndUnloadOther player: " + pm.PlayerId + " Playerinfo is null.");
                    continue;
                }
                var mapId = pm.Playerinfo.stTAC_GameTinyData.iMapId;
                if (!ChessItemModel.IsMapItem(mapId))
                    mapId = SDFConfigCache.DEFAULT_SCENE_ID;
                unloads.Remove(mapId);

                if (!mapMgr.IsMapLoad(mapId))
                    mapMgr.LoadMap(mapId);

                sb.AppendFormat("uin: {0} hp: {1} maxhp: {2} map_id: {3}\n", pm.Playerinfo.lUin, pm.iPlayerHpLeft, pm.iPlayerHpMax, pm.Playerinfo.stTAC_GameTinyData.iMapId);
            }

            foreach (var mapId in unloads)
            {
                mapMgr.UnloadMap(mapId, true);
            }

            Diagnostic.Log("BattleMapManager.UnloadUnuseMap mapList: " + string.Join(", ", mapList) + "\n" + sb.ToString());

            return unloads.Count > 0;
        }

        public void DestoryAllMap()
        {
            foreach (var map in m_sceneDict)
            {
                map.Value.Unload(this);
                map.Value.Dispose();
            }
            m_sceneDict.Clear();
        }
#endif
        public bool IsAllLoaded()
        {
            var e = m_sceneDict.GetEnumerator();
            while (e.MoveNext())
            {
                var v = e.Current.Value;
                if (!v.IsLoaded) return false;
            }
            return true;
        }

        public bool IsMapLoad(int mapId)
        {
            string mapName = GetMapNameById(mapId);
            BattleMap battleScene;
            if (m_sceneDict.TryGetValue(mapName, out battleScene))
            {
                return battleScene.IsLoading || battleScene.IsLoaded ||!battleScene.IsBlocksLoaded();
            }
            return false;
        }

        public bool IsMapLoaded(int mapId)
        {
            string mapName = GetMapNameById(mapId);
            BattleMap battleScene;
            if (m_sceneDict.TryGetValue(mapName, out battleScene))
            {
                return battleScene.IsLoaded && battleScene.IsBlocksLoaded();
            }
            return false;
        }

        public List<int> GetAllMapIDList()
        {
            List<int> retList = new List<int>();
            foreach (KeyValuePair<string, BattleMap> item in m_sceneDict)
            {
                retList.Add(item.Value.MapId);
            }
            return retList;
        }

#if UNITY_EDITOR
        public BattleMap GetMapByGameObject(GameObject mapGo)
        {
            foreach (var item in m_sceneDict)
            {
                if (item.Value.SceneObject == mapGo)
                    return item.Value;
            }
            return null;
        }
#endif

        public GameObject GetMapById(int mapId)
        {
            string mapName = GetMapNameById(mapId);
            var battleScene = GetMapByName(mapName);

            return battleScene != null ? battleScene.SceneObject : null;
        }

        public BattleMap GetMapDataById(int mapId)
        {
            string mapName = GetMapNameById(mapId);
            return GetMapByName(mapName);
        }

        private BattleMap GetMapByName(string mapName)
        {
            BattleMap battleScene;
            if (m_sceneDict.TryGetValue(mapName, out battleScene))
            {
                return battleScene;
            }

            Diagnostic.Warn("Find Map {0} Faild!", mapName);
            return null;
        }

        /// <summary>
        /// 获取当前地图配套的利息球
        /// </summary>
        /// <returns></returns>
        public GameObject GetCurrentActiveInterest(out bool showEmpty)
        {
            BattleMap battleScene;
            showEmpty = false;
            if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene))
            {
                if (battleScene.Config != null && battleScene.Config.InterestTemplate != null)
                {
                    showEmpty = battleScene.Config.showHostEmpty;
                    return battleScene.Config.InterestTemplate;
                }
            }
            return m_defaultInterestTemplate;
        }

        /// <summary>
        /// 获取第二套利息球
        /// </summary>
        /// <returns></returns>
        public GameObject GetOtherInterest(out bool showEmpty)
        {
            BattleMap battleScene;
            showEmpty = false;
            if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene))
            {
                if (battleScene.Config != null && battleScene.Config.InterestTemplate != null)
                {
                    showEmpty = battleScene.Config.showGuestEmpty;
                    return battleScene.Config.InterestTemplate2;
                }
            }

            return null;
        }

        public void InitAllMapSDFData()
        {
            foreach (var scene in m_sceneDict)
            {
                scene.Value.InitSDFData();
            }
        }

        public SDFPathConfig GetSDFPath()
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    return battleScene.SDFPath;
                }
            }
            return null;
        }

        public bool IsReachable(FVector2 pos, Fix64 playerRadius)
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    Fix64 r = battleScene.SDFPath.Sample(ref pos);
                    return r >= playerRadius;
                }
            }
            return false;
        }

        public bool FindNearPosByMap(FVector2 pos, Fix64 playerRadius, out FVector2 nearPos)
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    if (battleScene.SDFPath.FindNearPosition(ref pos, ref playerRadius))
                    {
                        nearPos = pos;
                        return true;
                    }
                    else
                    {
                        nearPos = pos;
                        return false;
                    }
                }
            }
            nearPos = FVector2.zero;
            return false;
        }

        public string GetCurTrigger(FVector2 pos, Fix64 playerRadius)
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    // TODO 这里后面补充一下playerRadius
                    return battleScene.SDFPath.GetTrigger(ref pos);
                }
            }
            return string.Empty;
        }

        public Fix64 FindMyBattleFieldHeight(FVector2 pos)
        {
            var myPlayer = ChessModelManager.Instance.GetBattleModel().GetMyPlayerModel();
            if (myPlayer != null)
            {
                var mapId = myPlayer.Playerinfo.stTAC_GameTinyData.iMapId;
                var mapName = GetMapNameById(mapId);

                BattleMap battleScene;
                if (!string.IsNullOrEmpty(mapName))
                {
                    if (m_sceneDict.TryGetValue(mapName, out battleScene)
                        && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                    {
                        return battleScene.SDFPath.SimpleHeight(ref pos);
                    }
                }
            }
            Diagnostic.Warn("FindMyBattleFieldHeight faild!");
            return Fix64.zero;
        }

        public Fix64 FindRoundSelectHeightByMay(FVector2 pos)
        {
            var roundSelectName = GetMapNameById(SDFConfigCache.ROUND_SELECT_SCENE_ID);
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(roundSelectName))
            {
                if (m_sceneDict.TryGetValue(roundSelectName, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    return battleScene.SDFPath.SimpleHeight(ref pos);
                }
            }
            Diagnostic.Warn("FindHeightByMay faild!");
            return Fix64.zero;
        }

        public Fix64 FindHeightByMay(FVector2 pos)
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    return battleScene.SDFPath.SimpleHeight(ref pos);
                }
            }
            Diagnostic.Warn("FindHeightByMay faild!");
            return Fix64.zero;
        }

        public FVector2 GetGradientByMap(ref FVector2 pos)
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene)
                    && battleScene.SDFPath != null && battleScene.SDFPath.m_isInited)
                {
                    return battleScene.SDFPath.Gradient(ref pos);
                }
            }
            return FVector2.zero;
        }

        public bool IsCurrentRoundSelectMap()
        {
            return m_currentActiveMap == GetMapNameById(SDFConfigCache.ROUND_SELECT_SCENE_ID);
        }

        public string GetCurrentMapName()
        {
            return m_currentActiveMap;
        }

        public GameObject GetCurrentActiveMap()
        {
            var battleScene = GetMapByName(m_currentActiveMap);

            return battleScene != null ? battleScene.SceneObject : null;
        }

        public BattleMap GetCurrentBattleMap()
        {
            return GetMapByName(m_currentActiveMap);
        }

        public void Deactive()
        {
            BattleMap battleScene;
            if (!string.IsNullOrEmpty(m_currentActiveMap))
            {
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene))
                {
                    battleScene.Active = false;

                    //if (battleScene.ReferenceCount <= 0)
                    //{
                    //    battleScene.Unload(this);
                    //}
                }
            }
#if !OUTSOURCE
            var inst = QQGameSystem.Instance;
            if (inst != null && inst.GetStage() is ChessBattleStage)
            {
                inst.ReSetWwiseListenerPosition();
            }
#endif
            m_currentActiveMap = string.Empty;
        }

        public void Active(int mapId, List<int> listBlocks = null)
        {
            string mapName = GetMapNameById(mapId);
            ActiveByName(mapName, listBlocks);
        }

        
        public void ActiveByName(string mapName, List<int> listBlocks = null)
        {
            m_listBlocks = listBlocks;

            if (m_currentActiveMap == mapName)
            {
                BattleMap battleScene;
                if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene))
                {
                    //这里要先处理，不然直接return了，没办法设置blocks
                    battleScene.SetBlocks(m_listBlocks);
                }

                Diagnostic.Log("[BattleMapManager.ActiveByName] " + m_currentActiveMap + " Actived!");
                return;
            }

            Deactive();

            m_currentActiveMap = mapName;
            if (!m_waitResLoaded)
            {
                ActiveCurMap();
            }
        }

        
        private void ActiveCurMap()
        {
            // 先处理好相机 
            OnMapPreActive(m_currentActiveMap);

            BattleMap battleScene;
            if (m_sceneDict.TryGetValue(m_currentActiveMap, out battleScene))
            {
                battleScene.Active = true;
                battleScene.SetBlocks(m_listBlocks);
            }

            OnMapActive(battleScene, m_currentActiveMap);
        }


        public static string MapChange = "MapChanged";

        private void OnMapPreActive(string mapName)
        {
            Camera curCamera;
            if (mapName == GetMapNameById(SDFConfigCache.ROUND_SELECT_SCENE_ID))
            {
                var world = MicroMgr.Instance.GetMicroObj();
                if (world == null || world.m_sdfCache == null) //仅限局外使用判断
                    m_roundSelectDynamicRoot?.SetActive(true);

                if (m_gameSceneCamera != null)
                {
                    m_gameSceneCamera.gameObject.SetActive(false);
                }

                if (m_roundSelectSceneCamera != null)
                {
                    m_roundSelectSceneCamera.gameObject.SetActive(true);
                    GfxManager.Instance.Camera = m_roundSelectSceneCamera;
                }

                for (int i = 0; i < m_gameSceneColliders.Count; ++i)
                {
                    if (m_gameSceneColliders[i] != null)
                        m_gameSceneColliders[i].enabled = false;
                }

                for (int i = 0; i < m_roundSelectColliders.Count; ++i)
                {
                    if (m_roundSelectColliders[i] != null)
                        m_roundSelectColliders[i].enabled = true;
                }

                if (m_roundSelectProjector != null) m_roundSelectProjector.SetActive(true);
                if (m_gameSceneProjector != null) m_gameSceneProjector.SetActive(false);

                curCamera = m_roundSelectSceneCamera;

                // cull
                m_camTransform = curCamera.transform;
                RegistUpdateCamera();

                QQGameSystem.Instance.SetWwiseListenerPosition(ROUND_SELECT_SCENE_POSITION);
                Diagnostic.Log("[BattleMapManager.OnMapActive] mapName: [" + mapName + "] show round select camera");
            }
            else
            {
                var world = MicroMgr.Instance.GetMicroObj();
                if (world == null || world.m_sdfCache == null) //仅限局外使用判断
                {
                    if (m_roundSelectDynamicRoot != null)
                        m_roundSelectDynamicRoot.SetActive(false);
                }

                if (m_roundSelectSceneCamera != null)
                {
                    m_roundSelectSceneCamera.gameObject.SetActive(false);
                }

                if (m_gameSceneCamera != null)
                {
                    m_gameSceneCamera.gameObject.SetActive(true);
                    GfxManager.Instance.Camera = m_gameSceneCamera;
                }

                for (int i = 0; i < m_gameSceneColliders.Count; ++i)
                {
                    if (m_gameSceneColliders[i] != null)
                        m_gameSceneColliders[i].enabled = true;
                }

                for (int i = 0; i < m_roundSelectColliders.Count; ++i)
                {
                    if (m_roundSelectColliders[i] != null)
                        m_roundSelectColliders[i].enabled = false;
                }

                if (m_roundSelectProjector != null) m_roundSelectProjector.SetActive(false);
                if (m_gameSceneProjector != null) m_gameSceneProjector.SetActive(true);

                curCamera = m_gameSceneCamera;

                // cull
                UnRegistUpdateCamera();
#if !OUTSOURCE
                if (QQGameSystem.Instance != null && QQGameSystem.Instance.GetStage() is ChessBattleStage)
                    QQGameSystem.Instance.ReSetWwiseListenerPosition();
#endif
                Diagnostic.Log("[BattleMapManager.OnMapActive] mapName: [" + mapName + "] show game camera");
            }

            if (curCamera != null)
            {
                var gameSystem = QQGameSystem.Instance;
                if (gameSystem != null)
                {
#if !OUTSOURCE
                    ChessBattleStage stage = gameSystem.GetStage() as ChessBattleStage;
                    if (stage != null)
                    {
#endif
                        TKCameraManager camMgr = Services.GetService<TKCameraManager>();
                        camMgr.InitSceneCamera(curCamera.gameObject);
#if !OUTSOURCE
                        camMgr.UICamera = stage.UICamera;
                    }
#endif
                }

                //battleScene.AutoCullGfx(curCamera);
            }
        }

        private void OnMapActive(BattleMap battleScene, string mapName)
        {
            if (mapName == GetMapNameById(SDFConfigCache.ROUND_SELECT_SCENE_ID))
            {
                if (battleScene != null && battleScene.SceneObject != null)
                    battleScene.SceneObject.transform.localPosition = ROUND_SELECT_SCENE_POSITION;
            }

            ACGEventManager.Instance.Send(MapChange);
#if !OUTSOURCE
            ChessModelManager.Instance.GetBattleModel().GetCurScenePlayerModel().OnBattleMapLoaded();
#endif
        }

        public string GetMapNameById(int mapId)
        {
            //if (FixTinyHeroAndMap)
            //    mapId = SDFConfigCache.DEFAULT_SCENE_ID;

            // 战斗内用微内核的逻辑 保持统一
            var world = MicroMgr.Instance.GetMicroObj();

            string mapName = null;
            if (world != null && world.m_sdfCache != null)
            {
                mapName = world.m_sdfCache.GetMapNameById(mapId);
            }
            if (!string.IsNullOrEmpty(mapName))
            {
                return mapName;
            }

            // 局外走下面的
            // 设置一个默认的map
            if (mapId == 0)
                mapId = SDFConfigCache.DEFAULT_SCENE_ID;
            mapName = string.Empty;
            var itemCfg = DataBaseManager.Instance.SearchACGItem(mapId);
            if (itemCfg != null && !string.IsNullOrEmpty(itemCfg.sPreviewResource))
            {
                mapName = itemCfg.sPreviewResource;
            }
            if (mapId == -1)
                return "s1_tft_battlefield_center";
            return mapName;
        }

        private void OnLoadMapFinished(BattleMap scene)
        {
            scene.SceneObject.transform.parent = transform;

            var cameraAnimation = scene.GetCameraAnimManager();
            if (cameraAnimation != null)
            {
                Transform camParent;
                Camera cam;
                if (scene.MapName == GetMapNameById(SDFConfigCache.ROUND_SELECT_SCENE_ID))
                {
                    camParent = m_roundSelectCameraAnimation;
                    cam = m_roundSelectSceneCamera;
                }
                else
                {
                    camParent = m_gameSceneCameraAnimation;
                    cam = m_gameSceneCamera;
                }

                // 这里其实有两套相机动画，一套是支持美术从maya导出的动画
                cameraAnimation.InitArtCamera(cam);
                // 这里是另外一套，美术在unity里面K帧的动画，这一套不支持FOV
                if (camParent != null)
                {
                    var animation = camParent.gameObject.TryGetComponent<Animation>();
                    if (animation != null)
                    {
                        cameraAnimation.InitCameraAnimation(animation);
                    }
                }
            }
#if !OUTSOURCE
            if (ChessUtil.Optimized_Round_Select_Effect && (QQGameSystem.Instance.GetStage() is ChessBattleStage) && (scene.MapName == GetMapNameById(SDFConfigCache.ROUND_SELECT_SCENE_ID)) && (MicroMgr.Instance.GetMicroObj().CSoGame.SetID == ESetID.e_Set8))
            {
                if (RoundSelectLODEffect == null)
                    RoundSelectLODEffect = new List<Transform>();
                RoundSelectLODEffect.Clear();
                for (int i = 0; i < 8; i++)
                {
                    Transform retTran = null;
                    if (i == 0)
                    {
                        retTran = GameUtil.FindTransform(scene.SceneObject.transform, "s8_scene_effect_screen");
                    }
                    else
                    {
                        retTran = GameUtil.FindTransform(scene.SceneObject.transform, "s8_scene_effect_screen" + " (" + i + ")");
                    }

                    if (retTran != null)
                    {
                        RoundSelectLODEffect.Add(retTran);
                    }
                }
            }
#endif
            //scene.SceneObject.transform.localPosition = Vector3.zero;
            //scene.SceneObject.transform.localRotation = Quaternion.identity;
            //scene.SceneObject.transform.localScale = Vector3.one;
        }

        public Camera GetRoundSelectSceneCamera()
        {
            return m_roundSelectSceneCamera;
        }

        public Camera GetActiveCamera()
        {
            if (IsCurrentRoundSelectMap())
                return m_roundSelectSceneCamera;
            else
                return m_gameSceneCamera;
        }
#if !OUTSOURCE
        public void SwitchOffscreenRender(bool open)
        {
            if (m_roundSelectSceneCamera != null)
            {
                var offscreenRender = m_roundSelectSceneCamera.GetComponent<GfxOffScreenRenderCamera>();
                if (offscreenRender != null && offscreenRender.enabled)
                {
                    Diagnostic.Log("[m_roundSelectSceneCamera] set offscreen render: " + open);
                    offscreenRender.SwitchOffscreen(open);
                }
            }

            if (m_gameSceneCamera != null)
            {
                var offscreenRender = m_gameSceneCamera.GetComponent<GfxOffScreenRenderCamera>();
                if (offscreenRender != null && offscreenRender.enabled)
                {
                    Diagnostic.Log("[m_gameSceneCamera] set offscreen render: " + open);
                    offscreenRender.SwitchOffscreen(open);
                }
            }
        }
#endif
        //public void MoveRoundSelectCameraOffset(Vector3 offset)
        //{
        //    var activeCamera = GetRoundSelectSceneCamera();
        //    if (activeCamera != null)
        //    {
        //        activeCamera.transform.parent.localPosition = offset;
        //    }
        //}

        //public void ClearRoundSelectCameraOffset()
        //{
        //    MoveRoundSelectCameraOffset(Vector3.zero);
        //}

        public void SetActive(bool active)
        {
            if (m_roundSelectDynamicRoot != null)
                m_roundSelectDynamicRoot.SetActive(active);
            if (m_gameDynamicRoot != null)
                m_gameDynamicRoot.SetActive(active);
        }

        public Camera GetBattleSceneCamera()
        {
            return m_gameSceneCamera;
        }

        #region Camera Animation Trigger

        public void OnTurnStart(int turnCount, bool isPreview = false, int showType = 0)
        {
            bool isShowCameraEffect = true;
            bool isShowTurnStartEffect = false;
            if (isPreview)
            {
                isShowCameraEffect = showType == 1;
                isShowTurnStartEffect = showType != 0;
            }

            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (isShowCameraEffect && !battleModel.IsChallengeMode() && !battleModel.IsHundredMode())
            {
                if (battleModel.IsNormalType())
                    turnCount = turnCount - 1;  //   第一次轮抽 在这里不算回合... 
                var currentMap = GetCurrentBattleMap();
                if (currentMap != null)
                {
                    var cameraAnimation = currentMap.GetCameraAnimManager();
                    if (cameraAnimation != null)
                    {
                        cameraAnimation.OnTurnStart(turnCount, isPreview);
                    }
                }
            }
            else
            {
                //停止正在播放的摄像机动画
                StopSceneCameraAnimation();
            }

            if (isPreview && isShowTurnStartEffect)
            {
                //预览模式下
                battleModel.CurrentTurnCount = 2;

                var currentMap = GetCurrentBattleMap();
                if (currentMap != null)
                {
                    var trigger = currentMap.GetBattleMapTrggierManager();
                    if (trigger != null)
                        trigger.OnTurnStart(null);
                }
            }
        }

        public void OnTurnStart()
        {
            OnTurnStart(ChessModelManager.Instance.GetBattleModel().CurrentTurnCount);
        }

        public void OnRoundSelectStart(int pos)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                var cameraAnimation = currentMap.GetCameraAnimManager();
                if (cameraAnimation != null)
                {
                    cameraAnimation.OnRounSelectStart(pos);
                }
            }
        }

        public void OnFirstRankFoucs(bool isHomeField, bool isSpecial)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
#if !OUTSOURCE
                if (currentMap.MapId == 300080)
                {
                    //热更特殊处理：2023世界赛 棋盘
                    //吃鸡播放视频

                    Component mediaPlayerComp = currentMap.SceneObject.GetComponentInChildren(typeof(VideoComponentAVPro), true);
                    Transform videoCamera = GameUtil.FindTransform(currentMap.SceneObject.transform, "VideoCamera");

                    if (mediaPlayerComp is VideoComponentAVPro && videoCamera != null)
                    {
                        VideoComponentAVPro mediaPlayer = mediaPlayerComp as VideoComponentAVPro;

                        mediaPlayer.SetVideoClip(VideoComponentBase.FileLocation.RelativeToStreamingAssetsFolder,
                            "Movie/s9_tft_championship_mp.mp4", false, true);

                        mediaPlayer.OnPlayStarted = () =>
                        {
                            videoCamera.SetActive(true);
                        };
                        
                        mediaPlayer.OnVideoPreparedCompleted = () =>
                        {
                            if (NotchSizeImp.IsPad)
                            {
                                mediaPlayer.gameObject.GetRectTransform().anchorMax = Vector2.one * 0.5f;
                                mediaPlayer.gameObject.GetRectTransform().anchorMin = Vector2.one * 0.5f;
                                mediaPlayer.FitVideoUI();
                            }
                            mediaPlayer.Play();
                        };

                        if (mediaPlayer.IsPrepared)
                        {
                            if (NotchSizeImp.IsPad)
                            {
                                mediaPlayer.gameObject.GetRectTransform().anchorMax = Vector2.one * 0.5f;
                                mediaPlayer.gameObject.GetRectTransform().anchorMin = Vector2.one * 0.5f;
                                mediaPlayer.FitVideoUI();
                            }
                            mediaPlayer.Play();
                        }
                    }
                    return;
                }
#endif
                var cameraAnimation = currentMap.GetCameraAnimManager();
                if (cameraAnimation != null)
                {
                    cameraAnimation.OnFirstRankFoucs(isHomeField, isSpecial);
                }
            }
        }

        public void PreviewSpecialWinRank(int rank)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    triggerMgr.SpecialRankReachTemp(rank, false, false);
                }
            }
        }

        public bool HasFirstRankSpecialCameraEffects(ref float playTime)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                var cameraAnimation = currentMap.GetCameraAnimManager();
                if (cameraAnimation != null)
                {
                    return cameraAnimation.HashFirstRankSpecialCameraEffects(ref playTime);
                }
            }

            return false;
        }

        /// <summary>
        /// 吃鸡动画，有小小英雄挂点动效
        /// </summary>
        public bool HaveRankOneHangHeroAnim()
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    return triggerMgr.HaveRankOneHangHeroAnim();
                }
            }

            return false;
        }

        public void PreviewForceTriggerAnimAndEffect(BattleMapTriggerAminationConfig cfg, bool reset)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    var enterData = cfg.GetEnterAnimData();
                    if (enterData != null)
                    {
                        triggerMgr.PlayTriggerEffect(cfg, enterData.animName, directToEnd: reset);
                        triggerMgr.PreviewPlayEffectAddition(cfg);
                    }
                }
            }
        }

        public void PreviewRefreshConfig(BattleMapConfig cfg)
        {
            if (cfg == null) return;

            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    triggerMgr.UpdateConfig(cfg);
                }
            }
        }

        #endregion

        public void StopSceneCameraAnimation(bool directToGameStartAnimEnd = false)
        {
            var currentBattleMap = GetCurrentBattleMap();
            if (currentBattleMap != null)
                currentBattleMap.StopSceneCameraAnimation(directToGameStartAnimEnd);
        }

        public void PreviewDirectToEnd(string triggerName)
        {
            var currentBattleMap = GetCurrentBattleMap();
            if (currentBattleMap != null)
                currentBattleMap.PreviewDirectToEnd(triggerName);
        }

        public void DisableAllPlaySound()
        {
            var currentBattleMap = GetCurrentBattleMap();
            if (currentBattleMap != null)
                currentBattleMap.DisableAllPlaySound();
        }

        public bool IsSceneAnimPlaying()
        {
            
            bool ret = false;
            var temp = GetCurrentActiveMap();
            if (temp != null)
            {
                var animation = temp.GetComponent<BattleMapCameraAnimation>();
                if (animation != null && animation.isPlaying)
                {
                    return true;
                }
            }
            
            return false;
            
            // bool ret = false;
            // if (m_gameSceneCameraAnimation != null)
            // {
            //     var anim = m_gameSceneCameraAnimation.GetComponent<Animation>();
            //     if (anim != null)
            //     {
            //         ret = anim.isPlaying;
            //     }
            // }
            // return ret;
        }

        public void OnCameraPositionChanged()
        {
#if !OUTSOURCE
            var bus = ChessBattleGlobal.Instance.BattleUnitService;
            if (bus == null)
                return;
            List<ChessBattleUnit> units = bus.GetAllBattleUnit();
            var buim = ChessBattleGlobal.Instance.BattleUnitInfoMgr;
            if (buim == null)
                return;
            foreach (ChessBattleUnit unit in units)
            {
                if (unit == null || unit.Data == null) continue;
                ChessBattleUnitInfo info = buim.GetUnitInfoByEntityID(unit.Data.heroId);
                if (info != null)
                {
                    info.ResetOffset();
                    info.UpdateProgressPos();
                }
            }

            var outFieldUnitInfos = buim.GetAllOutFieldUnitInfo();
            foreach (var info in outFieldUnitInfos)
            {
                info.Value.UpdateProgressPos();
            }

            var cpc = ChessBattleGlobal.Instance.ChessPlayerCtrl;
            if (cpc != null)
                cpc.UpdateAllUnitInfoPos();

            var bcm = ChessBattleGlobal.Instance.battleScreenMgr;
            if (bcm != null)
            {
                var battleScreen = bcm.GetBattleScreen() as CommonBattleScreen;
                if (battleScreen != null)
                {
                    //更新盒子位置
                    battleScreen.UpdateBoxPos();
                }
            }
            if (ChessBattleGlobal.Instance.HAController != null)
            {
                ChessBattleGlobal.Instance.HAController.ResetUnitInfoPos();
            }
#endif
        }

        /// <summary>
        /// 震动;
        /// </summary>
        /// <param name="shakeName"></param>
        public void Shake(string shakeName, Transform shakeRoot = null)
        {
            if (_fightBattleCameraShake == null)
            {

            }
            if (this._fightBattleCameraShake != null)
            {
                this._fightBattleCameraShake.ShakeUsingPreset(shakeName, shakeRoot);
            }
        }

        public void RefershAwayFaceCamera()
        {
            Diagnostic.Log("RefershAwayFaceCamera");
            var curMap = GetCurrentBattleMap();
            if (curMap != null)
            {
                curMap.RefershAwayFaceCamera();
            }
        }

        #region cull 

        private bool m_gfxCullEnable = false;
        private UnityEngine.Coroutine m_gfxCullCoroutine = null;

        [ContextMenu("ActiveGfxCull")]
        public void ActiveGfxCull()
        {
            if (!PARTICLE_SYSTEM_CULLING)
                return;

            if (m_gfxCullCoroutine != null)
            {
                StopCoroutine(m_gfxCullCoroutine);
                m_gfxCullCoroutine = null;
            }

            if (!m_gfxCullEnable)
            {
                m_gfxCullEnable = true;

                var curMap = GetCurrentBattleMap();
                if (curMap != null)
                    m_gfxCullCoroutine = StartCoroutine(curMap.AutoCullGfx(GetActiveCamera()));
            }
        }

        [ContextMenu("DisableGfxCull")]
        public void DisableGfxCull()
        {
            if (m_gfxCullCoroutine != null)
            {
                StopCoroutine(m_gfxCullCoroutine);
                m_gfxCullCoroutine = null;
            }

            if (m_gfxCullEnable)
            {
                m_gfxCullEnable = false;

                var curMap = GetCurrentBattleMap();
                if (curMap != null)
                    curMap.DisableGfxCull();
            }
        }

        #endregion

        #region 局外棋盘触发trigger，局内不要使用
        public void PreviewMapTrigger(string triggerName)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                if (currentMap.MapId == 300080 && triggerName == BattleMapTriggerAminationConfig.E_CheckType.RANK_REACH)
                {
                    //特殊处理：2023世界赛 棋盘
                    //吃鸡播放视频，这里吃鸡不重置相机，不然会造成视频之前闪一下
                }
                else
                {
                    StopSceneCameraAnimation(true);
                }

                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    triggerMgr.PreviewMapTrigger(triggerName);
                }
            }
        }

        public bool CheckTriggerActive(string triggerName)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    return triggerMgr.CheckTriggerActive(triggerName);
                }
            }

            return false;
        }

        public void SetPreviewSelfUnit(ChessPlayerUnit selfUnit)
        {
            var currentMap = GetCurrentBattleMap();
            if (currentMap != null)
            {
                BattleMapTriggerManager triggerMgr = currentMap.GetBattleMapTrggierManager();
                if (triggerMgr != null)
                {
                    triggerMgr.SetPreviewSelfUnit(selfUnit);
                }
            }
        }

        public void ResetCameraParentPos()
        {
            var tempCameraAni = GetCamAnimitionGameObject();
            if (tempCameraAni != null)
            {
                Transform cameraParent = tempCameraAni.Find("CamParent");
                if (cameraParent != null)
                {
                    cameraParent.localPosition = Vector3.zero;
                }
            }
        }


        #endregion

    }
}
