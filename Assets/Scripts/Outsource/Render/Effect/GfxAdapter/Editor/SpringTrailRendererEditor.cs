using UnityEngine;
using UnityEditor;
using GfxFramework;

[CustomEditor(typeof(SpringTrailRenderer))]
public class SpringTrailRendererEditor : Editor
{
    private void OnEnable()
    {
        SpringTrailRenderer builder = target as SpringTrailRenderer;
        if (builder == null)
            return;
    }

    public void OnDisable()
    {

    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();

        SpringTrailRenderer builder = target as SpringTrailRenderer;
        if (builder == null)
            return;

        GfxEditorUtility.ContentsHeader("曲线");
        GfxEditorUtility.BeginContents();
        EditorGUI.BeginChangeCheck();

        builder.xCurve = EditorGUILayout.CurveField("x曲线", builder.xCurve);
        builder.yCurve = EditorGUILayout.CurveField("y曲线", builder.yCurve);
        builder.zCurve = EditorGUILayout.CurveField("z曲线", builder.zCurve);
        builder.vertexCount = EditorGUILayout.IntField("顶点数", builder.vertexCount);

        if (EditorGUI.EndChangeCheck())
        {
            builder.Cal();
            EditorUtility.SetDirty(target);
        }
        GfxEditorUtility.EndContents();

        GfxEditorUtility.ContentsHeader("线段");
        GfxEditorUtility.BeginContents();
        EditorGUI.BeginChangeCheck();

        builder.maxDistance = EditorGUILayout.Slider("节点间最大距离", builder.maxDistance, 0.05f, 100f);
        builder.elasticity = EditorGUILayout.Slider("弹力", builder.elasticity, 0f, 100f);
        builder.elasticityAttenuation = EditorGUILayout.Slider("顶点弹力衰减", builder.elasticityAttenuation, -0.1f, 0.1f);
        builder.resistance = EditorGUILayout.Slider("阻力", builder.resistance, 0f, 1f);
        builder.inertia = EditorGUILayout.Slider("惯性", builder.inertia, 0f, 1f);

        GfxEditorUtility.EndContents();

        if (EditorGUI.EndChangeCheck())
        {
            EditorUtility.SetDirty(target);
        }
    }
}
