using UnityEngine;
using System.Collections;
using UnityEditor;

[CustomEditor(typeof(SpringTrailBezierBuilder))]
public class SpringTrailBezierBuilderEditor : Editor
{
    private void OnEnable()
    {
        SpringTrailBezierBuilder builder = target as SpringTrailBezierBuilder;
        if (builder == null)
            return;
    }

    public void OnDisable()
    {

    }

    public override void OnInspectorGUI()
    {
        SpringTrailBezierBuilder builder = target as SpringTrailBezierBuilder;
        if (builder == null)
            return;

        EditorGUI.BeginChangeCheck();

        builder.xCurve = EditorGUILayout.CurveField("x曲线", builder.xCurve);
        builder.yCurve = EditorGUILayout.CurveField("x曲线", builder.yCurve);
        builder.zCurve = EditorGUILayout.CurveField("x曲线", builder.zCurve);
        builder.vertexCount = EditorGUILayout.IntField("顶点数", builder.vertexCount);

        if (EditorGUI.EndChangeCheck())
        {
            builder.Cal();
        }
    }
}
