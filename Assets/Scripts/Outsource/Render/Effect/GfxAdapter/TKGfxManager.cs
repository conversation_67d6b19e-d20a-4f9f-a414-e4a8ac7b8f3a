using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GfxFramework;

public class TKGfxManager : GfxLodManager
{
    // GM面板指定LOD
    private GfxLod m_globalLod = GfxLod.None;
    public GfxLod GlobalLod
    {
        get
        {
            return m_globalLod;
        }
        set
        {
            m_globalLod = value;
            if (m_globalLod != GfxLod.None)
                Lod = m_globalLod;
            else
                Lod = (GfxLod)GameLOD.Instance.DevicePower;
        }
    }

    private float m_pixelFill = 0;

    public override float PixelFill
    {
        get
        {
            return m_pixelFill;
        }
    }

    public static TKGfxManager GetInstance()
    {
        return Instance as TKGfxManager;
    }

    protected override void Awake()
    {
        base.Awake();

        GameLOD.Instance.EffectLODEventRaise += OnLODSettingChange;

        RefershLod();

        ActiveTag(Lod >= GfxLod.Ultra ? StrBloom : StrNoBloom);
        ActiveTag(Lod >= GfxLod.High ? StrHighModel : StrLowModel);
    }

    private void RefershLod()
    {
        Lod = DevicePower2GFXLod(GameLOD.Instance.DevicePower);

        m_dirty = true;
    }

    public static GfxLod DevicePower2GFXLod(TKFrame.EDevicePower devicePower)
    {
        switch (devicePower)
        {
            case TKFrame.EDevicePower.EDP_None:
            case TKFrame.EDevicePower.EDP_Low:
            case TKFrame.EDevicePower.EDP_Middle:
            case TKFrame.EDevicePower.EDP_High:
            case TKFrame.EDevicePower.EDP_Ultra:
                return (GfxLod)devicePower;
            default:
                {
                    if (devicePower >= TKFrame.EDevicePower.EDP_Extreme)
                        return GfxLod.Ultra;
                    else
                        return GfxLod.Low;
                }
        }
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        GameLOD.Instance.EffectLODEventRaise -= OnLODSettingChange;
    }

    private void OnLODSettingChange(object sender, EventLODEventArgs arg)
    {
        if (GlobalLod == GfxLod.None)
            RefershLod();
        else
            Lod = GlobalLod;

        if (Lod >= GfxLod.High)
            SwitchTag(StrLowModel, StrHighModel);
        else
            SwitchTag(StrHighModel, StrLowModel);
    }

    protected override void OnUpdate()
    {
        base.OnUpdate();

        if (m_dirty)
        {
            UpdatePixelFill();
        }
    }

    private void UpdatePixelFill()
    {
        if (FxEffect.EffectProfilerTable.Instance.IsInited())
        {
            var node = GfxList.First;
            m_pixelFill = 0;
            while (node != null)
            {
                var nextNode = node.GetNext();
                float pixelFill = node.GetValue().GetPixelFill();
                m_pixelFill += pixelFill;
                node = nextNode;
            }
        }
    }
}
