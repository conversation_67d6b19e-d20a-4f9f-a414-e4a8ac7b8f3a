using UnityEngine;
using System.Collections;
using System.Collections.Generic;

[RequireComponent(typeof(LineRenderer))]
public class SpringTrailBezierBuilder : MonoBehaviour
{
	private LineRenderer lineRenderer;
	public AnimationCurve xCurve = AnimationCurve.Constant(0, 1, 0);
	public AnimationCurve yCurve = AnimationCurve.Constant(0, 1, 0);
	public AnimationCurve zCurve = AnimationCurve.Constant(0, 1, 0);
	public int vertexCount;

	[ContextMenu("计算")]
	public void Cal()
	{
		lineRenderer = gameObject.GetComponent<LineRenderer>();

		List<Vector3> pointList = new List<Vector3>();
		for (float ratio = 0; ratio <= 1; ratio += 1.0f / vertexCount)
		{
			pointList.Add(new Vector3(
				xCurve != null ? xCurve.Evaluate(ratio) : 0, 
				yCurve != null ? yCurve.Evaluate(ratio) : 0,
				zCurve != null ? zCurve.Evaluate(ratio) : 0));
		}
		lineRenderer.positionCount = pointList.Count;
		lineRenderer.SetPositions(pointList.ToArray());

	}


}
