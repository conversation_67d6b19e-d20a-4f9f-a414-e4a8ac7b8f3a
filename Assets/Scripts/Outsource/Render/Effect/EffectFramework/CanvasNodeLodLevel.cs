using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

namespace FxEffect
{
    [Serializable]
    public class CanvasNodeLodLevel
    {
        public EDevicePower displayLevel = EDevicePower.EDP_Low;
        public Canvas canvas;

        public virtual void SetActive(bool active)
        {
            if (canvas != null)
                canvas.enabled = active;
        }

        public virtual void ExceLod(EDevicePower curGraphicPower)
        {
            SetActive((int)curGraphicPower >= (int)displayLevel);
        }
    }
}
