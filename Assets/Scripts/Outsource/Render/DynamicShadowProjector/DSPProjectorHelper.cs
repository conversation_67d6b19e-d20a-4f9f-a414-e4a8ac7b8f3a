using TKFrame;
using UnityEngine;
using DynamicShadowProjector;

namespace ZGameChess
{
    public class DSPProjectorHelper : MonoBehaviour
    {
        // GM面板全局控制开关
        public static bool GlobalEnableDSP = true;
        private Projector m_Projector;
        private DrawSceneObject m_DrawSceneObject;

        public bool IsDSPAllowed
        {
            get
            {
                if (m_DrawSceneObject != null)
                {
                    return m_DrawSceneObject.enabled;
                }
                return false;
            }

            set
            {
                SetDSPProjectorStatus(value);
            }
        }

        private void Start()
        {
            m_Projector = GetComponent<Projector>();
            m_DrawSceneObject = GetComponent<DrawSceneObject>();

            GameLOD.Instance.LODEventRaise += OnLODChange;
            DoLodChange(GameLOD.Instance.DevicePower);
        }

        private void OnEnable()
        {
            DoLodChange(GameLOD.Instance.DevicePower);
        }

        private void DoLodChange(EDevicePower devicePower)
        {
            bool isDSPShouldEnable;
            if (devicePower >= EDevicePower.EDP_High)
            {
                isDSPShouldEnable = true;
            }
            else
            {
                isDSPShouldEnable = false;
            }

            if (isDSPShouldEnable != IsDSPAllowed)
            {
                IsDSPAllowed = isDSPShouldEnable;
            }

            IsDSPAllowed = IsDSPAllowed && GlobalEnableDSP;
        }

        private void OnLODChange(object sender, EventLODEventArgs arg)
        {
            DoLodChange(arg.DevicePower);
        }

        private void SetDSPProjectorStatus(bool allowEnable)
        {
            if (m_DrawSceneObject != null)
            {
                m_DrawSceneObject.shadowTextureRenderer.enabled = allowEnable;
                m_DrawSceneObject.enabled = allowEnable;
            }
            if (m_Projector != null)
            {
                m_Projector.enabled = allowEnable;
            }

        }

        private void OnDestroy()
        {
            GameLOD.Instance.LODEventRaise -= OnLODChange;
        }
    }
}
