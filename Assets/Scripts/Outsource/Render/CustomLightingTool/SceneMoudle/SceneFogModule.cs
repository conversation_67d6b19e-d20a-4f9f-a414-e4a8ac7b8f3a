using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace PBRTools
{
    public enum PBRFogType
    {
        None,
        Hight,      // 高度雾
        Distance,   // 距离雾
    }

    [Serializable]
    public struct SceneFogModule
    {
        // 高度雾
        public PBRFogType fogType;
        public Color fogColor;
        public float fogStart;
        public float fogEnd;
        public Transform ground;

        // 距离雾
        public Color fogDisColor;
        public float fogDisStart;
        public float fogDisEnd;





        private float m_lastPositionY;
        private Color m_runtimeColor;
        private float m_runtimeFogStart;
        private float m_runtimeFogEnd;

        public static SceneFogModule m_activeFog;

        public bool Equals(ref SceneFogModule obj)
        {
            return fogType == obj.fogType && fogColor == obj.fogColor && fogStart == obj.fogStart && fogEnd == obj.fogEnd
                && ground == obj.ground && fogDisColor == obj.fogDisColor && fogDisStart == obj.fogDisStart && fogDisEnd == obj.fogDisEnd;
        }

        public void Reset()
        {
            fogType = PBRFogType.None;
            fogColor = Color.white;
            fogStart = -10f;
            fogEnd = 1f;
            ground = null;

            fogDisColor = new Color(0.2f, 0.5f, 0.6f);
            fogDisStart = 2f;
            fogDisEnd = 999f;

            m_lastPositionY = 0f;
        }

        public void Initialize(Transform defaultGroud)
        {
            if (ground == null)
            {
                ground = defaultGroud;
            }

            if (ground != null)
            {
                m_lastPositionY = ground.position.y;
            }
        }

        public void AutoApply(Transform t)
        {
            if (ground != null)
            {
                if (ground.position.y != m_lastPositionY)
                {
                    //TKFrame.Diagnostic.Log("[SceneTool]Update Update Fog Setting fogColor: " + fogColor + " fogStart: " + (fogStart + ground.position.y) + " fogEnd: " + (fogEnd + ground.position.y) + " path: " + GetPath());

                    Apply(t);

                    m_lastPositionY = ground.position.y;
                }

                if (fogType == PBRFogType.Hight)
                {
                    if (m_runtimeColor != fogColor || m_runtimeFogStart != fogStart || m_runtimeFogEnd != fogEnd)
                        Apply(t, false);
                }
                else if (fogType == PBRFogType.Distance)
                {
                    if (m_runtimeColor != fogDisColor || m_runtimeFogStart != fogDisStart || m_runtimeFogEnd != fogDisEnd)
                        Apply(t, false);
                }
            }
        }

        private string GetPath(Transform t)
        {
            if (t == null)
                return string.Empty;

            StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
            while (t != null)
            {
                sb.Append(t.name).Append("/");
                t = t.parent;
            }
            string resultStr = sb.ToString();
            MicroObjectPool<StringBuilder>.Release(sb);
            return resultStr;
        }

        public void Apply(Transform t, bool log = true)
        {
            if (log)
            {
                if (fogType == PBRFogType.Hight)
                {
                    if (ground != null)
                        TKFrame.Diagnostic.Log("[SceneTool]Update Fog Setting fogColor: " + fogColor + " fogStart: " + (fogStart + ground.position.y) + " fogEnd: " + (fogEnd + ground.position.y) + " path: " + GetPath(t));
                    else
                        TKFrame.Diagnostic.Log("[SceneTool]Update Fog Setting fogColor: " + fogColor + " fogStart: " + fogStart + " fogEnd: " + fogEnd + " path: " + GetPath(t));
                }
                else
                {
                    TKFrame.Diagnostic.Log("[SceneTool]Update Fog Setting fogDisColor: " + fogDisColor + " fogDisStart: " + fogDisStart + " fogDisEnd: " + fogDisEnd + " path: " + GetPath(t));
                }
            }


            if (ground == null)
            {
                Shader.SetGlobalColor("_SceneFogColor", fogColor);
                Shader.SetGlobalFloat("_SceneFogStart", fogStart);
                Shader.SetGlobalFloat("_SceneFogEnd", fogEnd);
            }
            else
            {
                Shader.SetGlobalColor("_SceneFogColor", fogColor);
                Shader.SetGlobalFloat("_SceneFogStart", fogStart + ground.position.y);
                Shader.SetGlobalFloat("_SceneFogEnd", fogEnd + ground.position.y);
            }

            if (fogType == PBRFogType.Hight)
            {
                Shader.SetGlobalFloat("_SceneFog", 1);

                m_runtimeColor = fogColor;
                m_runtimeFogStart = fogStart;
                m_runtimeFogEnd = fogEnd;
            }
            else
            {
                Shader.SetGlobalFloat("_SceneFog", 0);
            }

            Shader.SetGlobalColor("_SceneDistanceFogColor", fogDisColor);
            Shader.SetGlobalFloat("_SceneDistanceFogStart", fogDisStart*0.1f);
            Shader.SetGlobalFloat("_SceneDistanceFogEnd", fogDisEnd*0.1f);
            if (fogType == PBRFogType.Distance)
            {
                Shader.SetGlobalFloat("_SceneDistanceFog", 1);

                m_runtimeColor = fogDisColor;
                m_runtimeFogStart = fogDisStart;
                m_runtimeFogEnd = fogDisEnd;
            }
            else
            {
                Shader.SetGlobalFloat("_SceneDistanceFog", 0);
            }

            if (log)
            {
                if (!m_activeFog.Equals(ref this))
                {
                    TKFrame.Diagnostic.Warn("[SceneTool]exist fog setting, will override fog [ fogColor = " + m_activeFog.fogColor + " fogStart = " + m_activeFog.fogStart + "]");
                }
            }

            m_activeFog = this;
        }

        public void DisableFog()
        {
            if (m_activeFog.Equals(this))
            {
                DisableAllFog();
                m_activeFog.Reset();

                m_runtimeColor = Color.white;
                m_runtimeFogStart = 0;
                m_runtimeFogEnd = 0;
            }
        }

        public static void DisableAllFog()
        {
            Shader.SetGlobalFloat("_SceneFog", 0);
            Shader.SetGlobalFloat("_SceneDistanceFog", 0);

            TKFrame.Diagnostic.Log("[SceneTool] DisableAllFog");
        }
    }
}
