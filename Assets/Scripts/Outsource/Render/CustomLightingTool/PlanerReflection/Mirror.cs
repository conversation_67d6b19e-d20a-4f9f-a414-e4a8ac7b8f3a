using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using TKFrame;
#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(Mirror))]
public sealed class MirrorsManagerEditor : Editor
{
    private Mirror targetMirror;
    void OnEnable()
    {
        targetMirror = (Mirror)serializedObject.targetObject;
    }

    public override void OnInspectorGUI()
    {
        targetMirror.m_ClipPlaneOffset = EditorGUILayout.FloatField("Clip Plane Offset", Mathf.Max(-3.0f, targetMirror.m_ClipPlaneOffset));
        targetMirror.textureSize = EditorGUILayout.IntField("Reflect Resolution", targetMirror.textureSize);
        base.OnInspectorGUI();
        if (targetMirror.enableSelfCullingDistance)
        {
            for (int i = 0; i < 32; i++)
            {
                string currentName = LayerMask.LayerToName(i);

                if (!string.IsNullOrEmpty(currentName))
                {
                    float currentValue = targetMirror.layerCullingDistances[i];
                    currentValue = EditorGUILayout.FloatField("   " + currentName, currentValue);
                    targetMirror.layerCullingDistances.SetValue(currentValue, i);
                }
            }
        }

        EditorGUILayout.Space();
        if (GUILayout.Button("Update"))
            targetMirror.UpdateSetting();

        Repaint();
        if (GUI.changed)
            EditorUtility.SetDirty(targetMirror);
    }
}
#endif

[ExecuteInEditMode]
public sealed class Mirror : MonoBehaviour
{
    public static bool FeatureEnableMark = true;

    public Camera refCam = null;
    public bool HDR = true;
    [Tooltip("如果不是大物体（屏占比非常高）那种，请置为false")]
    public bool isBigObject = false;

    [HideInInspector]
    const string ReflectionSample = "_ReflectionTex";
    public static bool s_InsideRendering = false;
    private int uniqueTextureID = -1;

    [HideInInspector]
    public int textureSize
    {
        get
        {
            return m_TextureSize;
        }
        set
        {
            if (!Application.isPlaying)
                m_TextureSize = Mathf.Clamp(value, 1, 2048);
        }
    }

    public float heightFade = 0.5f;

    [HideInInspector]
    public int m_TextureSize = 256;
    private float sizeRatio = 1.0f;
    private const float minRatio = 10e-3f;
    private const float verySmallPositiveNumber = 1.0e-7f;

    [HideInInspector]
    public float m_ClipPlaneOffset = 0.01f;
    [Tooltip("The normal transform(transform.up as normal)")]
    public Transform normalTrans;

    public enum RenderQuality
    {
        Default,
        High,
        Medium,
        Low
    }

    private RenderTexture m_ReflectionTexture = null;

    [HideInInspector]
    public float[] layerCullingDistances = new float[32];
    [HideInInspector]
    public Renderer render;
    private Camera cam;
    private Camera reflectionCamera;
    private Transform refT;
    private Transform camT;

    /// <summary>
    /// The other Material
    /// A Hack way
    /// </summary>
    public List<Material> externalMaterial = new List<Material>();
    private List<Material> allMats = new List<Material>();
    private float widthHeightRate;

    [Tooltip("Mirror mask")]
    public LayerMask m_ReflectLayers = -1;
    public bool enableSelfCullingDistance = true;

    public void UpdateSetting()
    {
        OnEnable();
    }

    void Awake()
    {
        render = GetComponent<Renderer>();
        //var ds = Services.GetService<DeviceSettingPerformer>();
        //if (ds != null)
        //{
        //    if (ds.RecommendDevicePower <= EDevicePower.EDP_Low)
        //    {
        //        Destroy(this);
        //        return;
        //    }
        //}
        // considering the fact that this feature is enabled by default, so for editor
        // we could enable this as well, otherwise, artist could not see the effect
        if (!FeatureEnableMark)
        {
            // do not render anything, to save performance.
            if (render != null)
                render.enabled = false;
            Destroy(this);
            return;
        }
        // 编辑器允许保留，不然美术无法做效果。真机上如果ds为null无法定位机型，则默认不生效
        //else if(!Application.isEditor)
        //    Destroy(this);
        uniqueTextureID = Shader.PropertyToID(ReflectionSample);
        if (!normalTrans)
        {
            normalTrans = new GameObject("Normal Trans").transform;
            normalTrans.position = transform.position;
            normalTrans.rotation = transform.rotation;
            normalTrans.SetParent(transform);
        }
        //render = GetComponent<Renderer>();
        if (!render || !render.sharedMaterial)
        {
            Destroy(this);
        }
        for (int i = 0, length = render.sharedMaterials.Length; i < length; ++i)
        {
            Material m = render.sharedMaterials[i];
            if (!allMats.Contains(m))
                allMats.Add(m);
        }
        allMats.AddRange(externalMaterial);

        widthHeightRate = (float)Screen.height / Screen.width;
        SetReflectionrResolution();
        //m_ReflectionTexture.antiAliasing = 1;
#if UNITY_EDITOR
        GameObject go = null;
        Transform tran = normalTrans.Find("MirrorCam");

        if (tran != null)
            go = tran.gameObject;
        else
            go = new GameObject("MirrorCam", typeof(Camera), typeof(FlareLayer));
#else
        GameObject go = new GameObject("MirrorCam", typeof(Camera), typeof(FlareLayer));
#endif

        reflectionCamera = go.GetComponent<Camera>();
        go.transform.SetParent(normalTrans);
        go.transform.localPosition = Vector3.zero;
        reflectionCamera.enabled = false;
        reflectionCamera.targetTexture = m_ReflectionTexture;
        reflectionCamera.cullingMask = ~(1 << 4) & m_ReflectLayers.value;
        reflectionCamera.layerCullSpherical = enableSelfCullingDistance;
        reflectionCamera.allowMSAA = false;

        if (refCam != null)
        {
            reflectionCamera.clearFlags = refCam.clearFlags;
            reflectionCamera.backgroundColor = refCam.backgroundColor;
        }
        else
        {
            reflectionCamera.backgroundColor = Color.black;
            reflectionCamera.clearFlags = CameraClearFlags.Color;
        }
        reflectionCamera.backgroundColor = new Color(0, 0, 0, 0);

        reflectionCamera.depthTextureMode = DepthTextureMode.None;
        reflectionCamera.allowHDR = HDR;
        refT = reflectionCamera.transform;
        if (!enableSelfCullingDistance)
        {
            for (int i = 0, length = layerCullingDistances.Length; i < length; ++i)
            {
                layerCullingDistances[i] = 0;
            }
        }
        else
        {
            reflectionCamera.layerCullDistances = layerCullingDistances;
        }
        reflectionCamera.useOcclusionCulling = false;       //Custom Projection Camera should not use occlusionCulling!
        SetTexture(m_ReflectionTexture);
    }

    private void Start()
    {
        if (!FeatureEnableMark)
            return;
        GameLOD.Instance.LODEventRaise += OnLODChange;
        GameLOD.Instance.ResolutionRaise += OnResolutionChange;
    }

    private void SetTexture(RenderTexture rt)
    {
        if (rt == null)
            return;
        for (int i = 0, length = allMats.Count; i < length; ++i)
        {
            Material m = allMats[i];
            m.SetTexture(uniqueTextureID, rt);
            m.SetVector("_PlaneReflectionPar", new Vector4(normalTrans.position.y, heightFade, 0, 0));
        }
    }

    Vector3 pos;
    Vector3 normal;
    Vector4 reflectionPlane;
    Vector4 clipPlane;
    Matrix4x4 reflection = Matrix4x4.identity;
    Matrix4x4 ref_WorldToCam;
    Matrix4x4 ref_ClipToWorld;

    //[IFix.Patch]
    private void OnEnable()
    {
#if UNITY_EDITOR
        // Awake only execute once during entire life cycle
        // but OnEnable will execute every time when you enable the component
        // so we need to mark it execute once again.
        Awake();
#endif
        if (!FeatureEnableMark)
            return;
        SetTexture(m_ReflectionTexture);
        normal = normalTrans.up;
        pos = normalTrans.position;
        float d = -Dot(ref normal, ref pos) - m_ClipPlaneOffset;
        reflectionPlane = new Vector4(normal.x, normal.y, normal.z, d);
        CalculateReflectionMatrix(ref reflection, ref reflectionPlane);

    }

    static float Dot(ref Vector3 left, ref Vector3 right)
    {
        return left.x * right.x + left.y * right.y + left.z * right.z;
    }

    /// <summary>
    /// <br>If camera's cliptoworld matrix is zero, will cause error Screen position out of view frustum.</br>
    /// <br>Check it here.</br>
    /// </summary>
    /// <returns>True means okay, false, you should not use this matrix.</returns>
    private bool CheckClipToWorld(ref Matrix4x4 clipToWorld)
    {
        //Rect screenRect = CameraUtil.GetScreenViewportRect(reflectionCamera, true);
        var in0 = new Vector3(-1f, -1f, 0.95f);
        var in1 = new Vector3(1f, -1f, 0.95f);

        float w0 = clipToWorld.m30 * in0.x + clipToWorld.m31 * in0.y + clipToWorld.m32 * in0.z + clipToWorld.m33;
        float w1 = clipToWorld.m30 * in1.x + clipToWorld.m31 * in1.y + clipToWorld.m32 * in1.z + clipToWorld.m33;

        return Mathf.Abs(w0) > verySmallPositiveNumber && Mathf.Abs(w1) > verySmallPositiveNumber;
    }

    public void OnWillRenderObject()
    {
        if (!FeatureEnableMark || s_InsideRendering || !render.enabled || !enabled)
            return;
        s_InsideRendering = true;
        if (cam != refCam && cam != Camera.current && !Camera.current.orthographic)
        {

            if (Application.isPlaying)
            {
                if (refCam != null)
                {
                    cam = refCam;
                }
                else
                {
                    cam = Camera.current;
                }
            }
            else
            {
#if UNITY_EDITOR
                cam = SceneView.lastActiveSceneView.camera;
#else
                cam = Camera.current;
#endif
            }

            camT = cam.transform;
            reflectionCamera.fieldOfView = cam.fieldOfView;
            reflectionCamera.aspect = cam.aspect;
        }
        if (null == normalTrans || null == camT)
        {
            return;
        }

        if (normal != normalTrans.up || pos != normalTrans.position)
        {
            normal = normalTrans.up;
            pos = normalTrans.position;
            float d = -Dot(ref normal, ref pos) - m_ClipPlaneOffset;
            reflectionPlane = new Vector4(normal.x, normal.y, normal.z, d);
            CalculateReflectionMatrix(ref reflection, ref reflectionPlane);
        }

        var localPos = normalTrans.worldToLocalMatrix.MultiplyPoint3x4(camT.position);
        refT.eulerAngles = camT.eulerAngles;
        var localEuler = refT.localEulerAngles;
        localEuler.x *= -1;
        localEuler.z *= -1;
        localPos.y *= -1;
        refT.localEulerAngles = localEuler;
        refT.localPosition = localPos;
        ref_WorldToCam = cam.worldToCameraMatrix * reflection;
        reflectionCamera.worldToCameraMatrix = ref_WorldToCam;
        CameraSpacePlane(ref clipPlane, ref ref_WorldToCam, ref pos, ref normal);
        reflectionCamera.projectionMatrix = cam.CalculateObliqueMatrix(clipPlane);
        GL.invertCulling = true;    //Inverse Cull to use reflection local matrix
        ref_ClipToWorld = (reflectionCamera.projectionMatrix * reflectionCamera.worldToCameraMatrix).inverse;
        if (CheckClipToWorld(ref ref_ClipToWorld))  // Fix: Screen position out of view frustum
        {
            reflectionCamera.Render();  //postProcessAction();
        }
        GL.invertCulling = false;
        s_InsideRendering = false;

#if UNITY_EDITOR
        SetTexture(m_ReflectionTexture);
#endif
    }

    private void CameraSpacePlane(ref Vector4 clipPlane, ref Matrix4x4 worldToCameraMatrix, ref Vector3 pos, ref Vector3 normal)
    {
        var offsetPos = pos + normal * m_ClipPlaneOffset;
        var cpos = worldToCameraMatrix.MultiplyPoint3x4
(offsetPos);
        var cnormal = worldToCameraMatrix.MultiplyVector(normal).normalized;
        clipPlane = new Vector4(cnormal.x, cnormal.y, cnormal.z, -Dot(ref cpos, ref cnormal));
    }

    private void SetReflectionrResolution()
    {
        if (!FeatureEnableMark)
        {
            ReleaseRT();
            return;
        }

        ReleaseRT();

        if (sizeRatio > minRatio)
        {
            int textureSize = Mathf.FloorToInt((isBigObject ? Mathf.Clamp(m_TextureSize, 1, 256) : m_TextureSize) * sizeRatio);

            m_ReflectionTexture = new RenderTexture(textureSize, (int)(textureSize * widthHeightRate + 0.5), 24, RenderTextureFormat.ARGB32, RenderTextureReadWrite.Default);
            m_ReflectionTexture.useMipMap = false;
            m_ReflectionTexture.name = "ReflectionTex " + GetInstanceID();
            m_ReflectionTexture.isPowerOfTwo = true;
            m_ReflectionTexture.filterMode = FilterMode.Bilinear;
        }
    }

    private void ReleaseRT()
    {
        if (m_ReflectionTexture != null)
        {
            m_ReflectionTexture.Release();
#if UNITY_EDITOR
            DestroyImmediate(m_ReflectionTexture);
#else
            Destroy(m_ReflectionTexture);
#endif
            m_ReflectionTexture = null;
        }
    }

    private void OnResolutionChange(object sender, EventResolutionEventArgs args)
    {
        widthHeightRate = (float)args.TargetScreenHeight / args.TargetScreenWidth;
        SetReflectionrResolution();
    }

    private void OnLODChange(object sender, EventLODEventArgs arg)
    {
        switch (arg.DevicePower)
        {
            case EDevicePower.EDP_Middle:
                {
                    if (render != null && !render.enabled)
                    {
                        render.enabled = true;
                    }
                    enabled = true;
                    sizeRatio = 0.5f;
                    SetReflectionrResolution();
                    reflectionCamera.targetTexture = m_ReflectionTexture;
                    SetTexture(m_ReflectionTexture);
                }
                break;
            case EDevicePower.EDP_Low:
                {
                    if (render != null && render.enabled)
                    {
                        render.enabled = false;
                    }
                    enabled = false;
                    sizeRatio = 0f;
                    SetReflectionrResolution();
                    SetTexture(m_ReflectionTexture);
                }
                break;
            default:
                if (arg.DevicePower >= EDevicePower.EDP_High)
                {
                    if (render != null && !render.enabled)
                    {
                        render.enabled = true;
                    }
                    enabled = true;
                    sizeRatio = 1.0f;
                    SetReflectionrResolution();
                    reflectionCamera.targetTexture = m_ReflectionTexture;
                    SetTexture(m_ReflectionTexture);
                }
                else
                {
                    if (render != null && render.enabled)
                    {
                        render.enabled = false;
                    }
                    enabled = false;
                    sizeRatio = 0f;
                    SetReflectionrResolution();
                    SetTexture(m_ReflectionTexture);
                }
                break;
        }
    }

    private void OnDestroy()
    {
        // Only FeatureEnableMark returns true, the two events will be added.
        if (FeatureEnableMark)
        {
            GameLOD.Instance.LODEventRaise -= OnLODChange;
            GameLOD.Instance.ResolutionRaise -= OnResolutionChange;
        }
        ReleaseRT();
    }

    private static void CalculateReflectionMatrix(ref Matrix4x4 reflectionMat, ref Vector4 plane)
    {
        reflectionMat.m00 = (1F - 2F * plane[0] * plane[0]);
        reflectionMat.m01 = (-2F * plane[0] * plane[1]);
        reflectionMat.m02 = (-2F * plane[0] * plane[2]);
        reflectionMat.m03 = (-2F * plane[3] * plane[0]);

        reflectionMat.m10 = (-2F * plane[1] * plane[0]);
        reflectionMat.m11 = (1F - 2F * plane[1] * plane[1]);
        reflectionMat.m12 = (-2F * plane[1] * plane[2]);
        reflectionMat.m13 = (-2F * plane[3] * plane[1]);

        reflectionMat.m20 = (-2F * plane[2] * plane[0]);
        reflectionMat.m21 = (-2F * plane[2] * plane[1]);
        reflectionMat.m22 = (1F - 2F * plane[2] * plane[2]);
        reflectionMat.m23 = (-2F * plane[3] * plane[2]);
    }
}
