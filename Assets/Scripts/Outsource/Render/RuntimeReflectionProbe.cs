using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

/// <summary>
/// 运行时实时修改反射贴图
/// </summary>
[ExecuteInEditMode]
public class RuntimeReflectionProbe : MonoBehaviour
{
    public Texture m_ReflectionProbeTexture = null;

    private void OnEnable()
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            var r = GetComponent<ReflectionProbe>();
            if (r != null && r.bakedTexture != null)
                m_ReflectionProbeTexture = r.bakedTexture;
        }
#endif
        if (
#if UNITY_EDITOR
            Application.isPlaying &&
#endif
            m_ReflectionProbeTexture != null)
        {
            var r = GetComponent<ReflectionProbe>();
            if (r != null)
            {
                r.mode = UnityEngine.Rendering.ReflectionProbeMode.Custom;
                r.customBakedTexture = m_ReflectionProbeTexture;
            }
            else
            {
                Diagnostic.Warn("[RuntimeReflectionProbe] ReflectionProbe == null");
            }
        }
        else
        {
            Diagnostic.Warn("[RuntimeReflectionProbe] m_ReflectionProbeTexture == null");
        }
    }
}

