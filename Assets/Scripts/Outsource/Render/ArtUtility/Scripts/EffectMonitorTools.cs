#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

namespace ArtEditor
{

    [ExecuteInEditMode]

    [Serializable]
    public class EffectMonitorData
    {
        public GameObject obj;
        public bool loop;
        public int maxcount;
        public List<Texture2D> useTextures;
    }

    public class ParticleSystemCurveData
    {
        public float delayTime;
        public float duration;
        public float lifeTime;
        public int count;
    }

    public class EffectMonitorTools : MonoBehaviour
    {
        GameObject effect_root;

        public AnimationCurve AnimCurve = new AnimationCurve();

        public int MaxParticleCount;

        public int TextureCount;

        public List<Texture2D> 超大的贴图 = new List<Texture2D>();

        public List<EffectMonitorData> mEffectMonitorDataList = new List<EffectMonitorData>();

        private List<ParticleSystemCurveData> mParticleSystemCurveData = new List<ParticleSystemCurveData>();

        public ParticleSystem[] ParticleSystemList;
        //int presecondCount = 0;
        int cursecondCount = 0;

        const int MAXTIME = 5;

        float deltaTime = 0f;
        //float loopTime = 0f;

        //int CurveTime = 0;
        float timer = 0f;
        //bool recordCurve = false;
        void Awake()
        {
            effect_root = GetComponent<ACGameEffectPreview>().特效;
            ParticleSystemList = effect_root.GetComponentsInChildren<ParticleSystem>();

        }

        void OnEnable()
        {
            timer = 0f;
            //CurveTime = 0;
        }

        public void BuildInfo()
        {
            effect_root = GetComponent<ACGameEffectPreview>().EffectObj;
            ParticleSystemList = effect_root.GetComponentsInChildren<ParticleSystem>();
            ParticleSystemList = effect_root.GetComponentsInChildren<ParticleSystem>();
            mEffectMonitorDataList.Clear();
            mParticleSystemCurveData.Clear();
            超大的贴图.Clear();
            for (int i = 0; i < ParticleSystemList.Length; i++)
            {
                float t_time = ParticleSystemList[i].main.duration;
                if (deltaTime == 0)
                {
                    deltaTime = t_time;
                }
                else if (deltaTime < t_time)
                {
                    deltaTime = t_time;
                }
                EffectMonitorData data = new EffectMonitorData();
                data.obj = ParticleSystemList[i].gameObject;
                data.loop = ParticleSystemList[i].main.loop;
                data.maxcount = ParticleSystemList[i].main.maxParticles;
                mEffectMonitorDataList.Add(data);

                ParticleSystemCurveData pardata = new ParticleSystemCurveData();
                pardata.delayTime = ParticleSystemList[i].main.startDelay.constant;
                pardata.duration = ParticleSystemList[i].emission.GetBurst(0).cycleCount;
                pardata.lifeTime = ParticleSystemList[i].main.startLifetime.constant;
                pardata.count = Mathf.FloorToInt(ParticleSystemList[i].emission.GetBurst(0).count.constant);
                mParticleSystemCurveData.Add(pardata);
            }
            for (int i = 0; i < mEffectMonitorDataList.Count; i++)
            {
                if (mEffectMonitorDataList[i] != null && mEffectMonitorDataList[i].useTextures != null)
                {
                    for (int j = 0; j < mEffectMonitorDataList[i].useTextures.Count; j++)
                    {
                        if (mEffectMonitorDataList[i].useTextures[j].width > 256)
                        {
                            超大的贴图.Add(mEffectMonitorDataList[i].useTextures[j]);
                        }
                    }
                }
            }

            for (int i = 0; i < ParticleSystemList.Length; i++)
            {
                ParticleSystemList[i].Stop();
            }

            for (int i = 0; i < ParticleSystemList.Length; i++)
            {
                ParticleSystemList[i].Play();
            }
            
            MaxParticleCount = 0;
            timer = 0f;
            AnimCurve.keys = new Keyframe[0];
            //recordCurve = true;
            InvokeRepeating("CalculParticleNum", 0f, 0.1f);
        }

        private void CalculParticleNum()
        {
            timer += 0.1f;
            cursecondCount = 0;
            for (int i = 0; i < ParticleSystemList.Length; i++)
            {
                cursecondCount += ParticleSystemList[i].particleCount;
            }
            AnimCurve.AddKey(timer, cursecondCount);
            if (cursecondCount > MaxParticleCount)
            {
                MaxParticleCount = cursecondCount;
            }
            if (timer > MAXTIME)
            {
                CancelInvoke("CalculParticleNum");
            }
        }

    }
}
#endif