using ACG.Core;
using GameFramework.FMath;
using Lucifer.ActCore;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;
using ZGame;

namespace ZGameChess
{
    public class MapDamagePreviewScene : Mono<PERSON>ehaviour, IReleaseList
    {
        private IEnumerator _priviewHurtEffectIE;

        public List<IRefCounter> AutoReleaseList { get; set; }

        protected MapPreviewManager m_mgr;


        protected ChessPlayerUnit m_vAttacker = null;
        protected ChessPlayerUnit m_vDefender = null;

        protected ChessViewAttackSet m_attackSet = null;
        protected GameObject m_attackTemplate = null;

        protected ChessBattleLogicFieldPreview m_battleLogicField = null;
        protected ChessBattleLogicPlayer m_attacker = null;
        protected ChessBattleLogicPlayer m_defender = null;

        //protected ChessLogicConfigCache m_logicConfigCache = null;

        protected readonly Vector3 m_attackerPos = new Vector3(-6.59f, 0.6f, -4.9f);
        protected readonly Vector3 m_defenderPos = new Vector3(6.32f, 0.6f, 4f);

        protected Vector3 m_runtimeDefenderPos;

        protected bool m_willKill = false;

        private void Awake()
        {
            m_mgr = GetComponent<MapPreviewManager>();
        }

        private void OnDestroy()
        {
            this.DisposeReleaseList();
        }

        // 清理伤害特效的所有环境
        public void DestoryDamageEnv()
        {
            BattleCommonNet.Client_Frame = 0;

            if (V_DriverManager.Instance != null && V_DriverManager.Instance.updateRunner != null)
                V_DriverManager.Instance.updateRunner.RemoveDriveMethod(AttackRunTick);

            m_vAttacker = null;
            m_vDefender = null;

            if (m_attackTemplate != null)
            {
                GameObject.Destroy(m_attackTemplate);
                m_attackTemplate = null;
            }

            m_battleLogicField = null;
            m_attacker = null;
            m_defender = null;

            //m_logicConfigCache = null;

            if (m_mgr != null)
                m_mgr.UninitViewRunner();

            if (m_attackSet != null)
            {
                foreach (var item in m_attackSet.AttackDict)
                {
                    var list = item.Value;
                    for (int i = 0; i < list.Count; ++i)
                    {
                        var attackUnit = list[i];
                        if (attackUnit == null || attackUnit.gameObject == null)
                            continue;
                        if (attackUnit.gameObject.activeSelf)
                        {
                            attackUnit.StopAttack_StopSound();
                        }
                        GameObject.Destroy(attackUnit.gameObject);
                    }
                }
                m_attackSet.AttackDict.Clear();
            }

            ChessBattleGlobal.Instance.ChessPlayerCtrl = null;
            //ChessModelManager.Instance.ClearBattleModel();
        }

        public void CreatePreviewDamageScene(int myTinyId, int damageId, bool willKill)
        {
            DestoryPreviewDamageScene();
            _priviewHurtEffectIE = CreatePreviewDamageSceneImpl(myTinyId, damageId, willKill);
            StartCoroutine(_priviewHurtEffectIE);
        }

        private bool CanAttack()
        {
            if (m_vAttacker == null || m_vAttacker.AnimBehaviour == null
                || m_vDefender == null || m_vDefender.AnimBehaviour == null)
            {
                return false;
            }

            return m_vAttacker.AnimBehaviour.IsInIdle()
                && !m_vAttacker.AnimBehaviour.HasRunAct && !m_vDefender.AnimBehaviour.HasRunAct
                && !m_vAttacker.HasEffectRun() && !m_vDefender.HasEffectRun() && !m_vDefender.InDeathAni;
        }

        public IEnumerator CreatePreviewDamageSceneImpl(int myTinyId, int damageId, bool willKill)
        {
            m_willKill = willKill;

            SceneCameraManager.InitCamera();

            //Diagnostic.Log("CreatePreviewDamageSceneImpl damageId: " + damageId);
            while (m_mgr.MapMgr == null)
                yield return null;
            var activeMap = m_mgr.MapMgr.GetMapDataById(m_mgr.ActiveMapId);
            while (activeMap != null && !activeMap.IsLoaded)
                yield return null;

            if (m_mgr.Projector != null)
            {
                m_mgr.Projector.gameObject.SetActive(true);
            }

            m_runtimeDefenderPos = m_defenderPos;
            var damageCfg = DataBaseManager.Instance.SearchACGAttackEffect(damageId);
            if (damageCfg != null)
            {
                var attackCfgDict = TinyAttackData.GetAttackCfgDict(damageCfg.sAttackConfig);
                if (attackCfgDict.TryGetValue((int)TinyAttackTriggerType.Normal, out string attackCfgName))
                {
                    var loadedAsset = ResourceUtil.LoadAsset<ChessAttackConfig>("art_tft_raw/cfg/team_leader_attack_cfg/" + attackCfgName, Path.GetFileNameWithoutExtension(attackCfgName), null, this);
                    while (!loadedAsset.IsLoaded)
                        yield return null;
                    var attackConfig = loadedAsset.GetAsset<ChessAttackConfig>();
                    if (attackConfig != null && attackConfig.m_defenderPosEnable)
                    {
                        m_runtimeDefenderPos = attackConfig.m_defenderPos;
                    }
                }
                else
                {
                    foreach (var attackItem in attackCfgDict)
                    {
                        attackCfgName = attackItem.Value;
                        var loadedAsset = ResourceUtil.LoadAsset<ChessAttackConfig>("art_tft_raw/cfg/team_leader_attack_cfg/" + attackCfgName, Path.GetFileNameWithoutExtension(attackCfgName), null, this);
                        while (!loadedAsset.IsLoaded)
                            yield return null;
                        var attackConfig = loadedAsset.GetAsset<ChessAttackConfig>();
                        if (attackConfig != null && attackConfig.m_defenderPosEnable)
                        {
                            m_runtimeDefenderPos = attackConfig.m_defenderPos;
                            break;
                        }
                    }
                }

            }

            var logicConfigCache = MicroMgr.Instance.GetMicroObj().m_logicCfgCache;
            logicConfigCache.PreloadInClient(new List<int>() { myTinyId, }, new List<int>() { damageId });
            yield return logicConfigCache.LoadAllCfgForClient(false);

            // 初始化逻辑层世界
            CreateLogicWorld(myTinyId, damageId, willKill);
            // 初始化表现层世界
            InitViewWorld();
            // 初始化逻辑层小小英雄数据
            InitLogicPlayer(m_attackerPos, m_runtimeDefenderPos, damageId);
            // 初始化表现层小小英雄数据 (和子弹)
            yield return CreateViewPlayer(myTinyId, damageId, m_attackerPos, m_runtimeDefenderPos);

            var assetService = Services.GetService<IAssetService>();
            if (assetService != null)
            {
                while (assetService.Loading)
                    yield return null;
            }

            V_DriverManager.Instance.updateRunner.DriveMethod(AttackRunTick, RunPriority.TURN_TICK);
            m_dtAcc = 0f;
        }

        // 锁15帧 避免逻辑帧太快
        float m_frameInterval = ActionCoreConfig.DefaultFrameInterval / 1000f;
        float m_dtAcc = 0f;
        protected void AttackRunTick()
        {
            if (!InAttack() && CanAttack() && m_attackSet.IsAttackAllFinished())
            {
                // 重置一下受击者的位置
                m_attacker.SetPositionAndRotation(m_attackerPos.ToFVector3(), m_attacker.dir);
                m_defender.SetPositionAndRotation(m_runtimeDefenderPos.ToFVector3(), m_defender.dir);

                // 重置一下受击者材质
                if (m_willKill)
                {
                    m_vDefender.ShowBody();
                    m_vDefender.ResetMat();
                }

                foreach (var kv in m_attackSet.AttackDict)
                {
                    var list = kv.Value;
                    for (int i = 0; i < list.Count; ++i)
                    {
                        var attackUnit = list[i];
                        if (attackUnit != null)
                            attackUnit.StopAttack();
                    }
                }

                LogicAttack();
            }

            m_dtAcc += Time.deltaTime;
            if (m_dtAcc >= m_frameInterval)
            {
                if (BattleCommonNet.Client_Frame == 0)
                { // 逻辑层开跑了再设置 避免表现层多跑0.5帧 导致第一次显示不对
                    m_vAttacker.SetLogicQueue(m_attacker.queueId);
                    m_vDefender.SetLogicQueue(m_defender.queueId);
                }

                BattleCommonNet.Client_Frame++;
                //Diagnostic.Log($">>bai field fc: {BattleCommonNet.Client_Frame}");

                m_battleLogicField.RunTick(Fix64.FromSingle(m_frameInterval));
                m_dtAcc -= m_frameInterval;
            }
        }

        public void DestoryPreviewDamageScene()
        {
            if (m_attackSet != null)
            {
                foreach (var kv in m_attackSet.AttackDict)
                {
                    var list = kv.Value;
                    for (int i = 0; i < list.Count; ++i)
                    {
                        var attackUnit = list[i];
                        if (attackUnit != null)
                            attackUnit.StopAttack();
                    }
                }
            }

            if (_priviewHurtEffectIE != null)
            {
                StopCoroutine(_priviewHurtEffectIE);
                _priviewHurtEffectIE = null;
            }

            // 清理战场上的飞行道具
            if (m_battleLogicField != null)
            {
                m_battleLogicField.ChessBattleResultState = ChessBattleResultState.End;
                m_battleLogicField.ClearTask();
                m_battleLogicField.ClearLastState();
            }

            if (m_mgr != null)
                m_mgr.DestoryAllPlayerUnit();

            m_attacker = null;
            m_defender = null;

            if (V_DriverManager.Instance != null && V_DriverManager.Instance.updateRunner != null)
                V_DriverManager.Instance.updateRunner.RemoveDriveMethod(AttackRunTick);
        }

        #region 表现层

        protected IEnumerator InitViewAttack(int bulletId)
        {
            if (m_attackSet == null)
            {
                m_attackSet = new ChessViewAttackSet();
            }

            if (m_attackTemplate == null)
            {
                GameObject tmpGo = new GameObject();
                tmpGo.SetActive(false);
                tmpGo.layer = GameObjectLayer.Hero;
                tmpGo.AddComponent<ChessViewAttackUnit>();
                m_attackTemplate = tmpGo;
            }

            m_attackSet.InitAttacks(bulletId, m_attackTemplate, true);
            var battleRoot = m_mgr.MapMgr.transform.Find("game_dynamic/BattleRoot");
            if (battleRoot != null)
            {
                foreach (var kv in m_attackSet.AttackDict)
                {
                    var list = kv.Value;
                    for (int i = 0; i < list.Count; ++i)
                    {
                        var item = list[i];
                        item.transform.SetParent(battleRoot);
                        ChessUtil.MakeTransformIdentity(item.transform);
                        item.StopAttack();
                    }
                }
            }

            while (!m_attackSet.IsAllInited())
                yield return null;
        }

        protected void InitViewWorld()
        {
            m_mgr.InitViewRunner();

            InitViewModel();
        }

        protected void InitViewModel()
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            battleModel.GetPlayerModel(0).EnemyPlayerID = 1;
            battleModel.GetPlayerModel(1).EnemyPlayerID = 0;
        }

        protected IEnumerator CreateViewPlayer(int myTinyId, int damageId, Vector3 attackerPos, Vector3 defenderPos)
        {
            var vAttacker = m_mgr.LoadTinyModel(myTinyId, attackerPos);
            if (vAttacker == null)
            {
                Diagnostic.Error("vAttacker is null myTinyId: " + myTinyId + " damageId: " + damageId);
                yield break;
            }
            vAttacker.PlayerData.ChessPlayerId = 0;
            vAttacker.PlayerData.InBattle = true;
            var vDefender = m_mgr.LoadTinyModel(100001, defenderPos);
            if (vDefender == null)
            {
                Diagnostic.Error("vDefender is null myTinyId: " + myTinyId + " damageId: " + damageId);
                yield break;
            }
            vDefender.PlayerData.ChessPlayerId = 1;

            while (!vAttacker.Inited || !vAttacker.IsLoaded || vAttacker.config == null)
                yield return null;
            while (!vDefender.Inited || !vDefender.IsLoaded || vDefender.config == null)
                yield return null;

            vAttacker.SetUpHurtEffectId(damageId);
            if (vAttacker.PlayerData.TinyData == null)
            {
                vAttacker.PlayerData.TinyData = new ZGameClient.TAC_GameTinyData();
            }
            vAttacker.PlayerData.TinyData.iDamageId = damageId;
            m_vAttacker = vAttacker;
            m_vDefender = vDefender;

            yield return InitViewAttack(damageId);

            if (ChessBattleGlobal.Instance.ChessPlayerCtrl == null)
            {
                ChessBattleGlobal.Instance.ChessPlayerCtrl = new ChessPlayerController();
            }
            ChessBattleGlobal.Instance.ChessPlayerCtrl.chessViewAttackSet = m_attackSet;

            ChessBattleGlobal.Instance.ChessPlayerCtrl.SetVS(m_vAttacker, m_vDefender);

            Vector3 dir = m_vAttacker.transform.position - m_vDefender.transform.position;
            m_vDefender.FaceTo(m_vDefender.Direction2Angle(dir), 0);
        }

        #endregion

        #region 逻辑层
        protected void CreateLogicWorld(int tinyId, int bulletId, bool willKill)
        {
            BattleCommonNet.Client_Frame = 0;

            if (m_battleLogicField == null)
            {
                m_battleLogicField = new ChessBattleLogicFieldPreview();
            }
            m_battleLogicField.willKill = willKill;

            var world = MicroMgr.Instance.GetMicroObj();
            if (m_attacker == null)
            {
                m_attacker = new ChessBattleLogicPlayer(world);
                ChessBattleLogicPlayerData playerData = new ChessBattleLogicPlayerData();
                m_attacker.InitData(m_mgr.GetLogicPlayerData(0, tinyId, bulletId), playerData);
                m_battleLogicField.SetFirstOwner(m_attacker);
            }

            if (m_defender == null)
            {
                m_defender = new ChessBattleLogicPlayer(world);
                m_defender.InitData(m_mgr.GetLogicPlayerData(1, tinyId, bulletId));
                m_battleLogicField.JoinInBattleField(m_defender, ChessBattleLogicPlayer.PlayerState.Enemy, setPos: false);
            }
        }

        protected void InitLogicPlayer(Vector3 attackerPos, Vector3 defenderPos, int bulletId)
        {
            m_battleLogicField.ChessBattleResultState = ChessBattleResultState.End;
            m_battleLogicField.ClearTask();
            m_battleLogicField.ClearLastState();

            m_attacker.playerData.UserInfo.stUsedCaptainInfo.iDamageId = bulletId;
            m_attacker.playerData.UserInfo.stUsedCaptainInfo.iEliminateId = bulletId;

            // 初始化双方位置
            m_attacker.CloseQueue();
            m_attacker.LoadConfig();
            m_attacker.LoadAttackConfig(bulletId, MicroMgr.Instance.GetMicroObj().m_logicCfgCache);
            m_attacker.SetAttack(null);
            m_attacker.SetMap(m_mgr.MapMgr.GetSDFPath());
            m_attacker.fTransform.position = attackerPos.ToFVector3();
            m_attacker.SetRotation((defenderPos - attackerPos).ToFVector3().normalized);
            m_attacker.SyncMovingState();

            m_defender.CloseQueue();
            m_defender.LoadConfig();
            m_defender.SetMap(m_mgr.MapMgr.GetSDFPath());
            m_defender.fTransform.position = defenderPos.ToFVector3();
            m_defender.SetRotation((attackerPos - defenderPos).ToFVector3().normalized);
            m_defender.SyncMovingState();
        }

        protected void LogicAttack()
        {
#if UNITY_EDITOR
            if (GlobalConfig.ReloadData)
            {
                GlobalConfig.ReloadData = false;

                //ChessPlayerLogicConfigManager.Instance.Release();
                //ChessAttackLogicConfigManager.Instance.Release();
                m_vAttacker.ResetChessPlayerLogicConfig();
                ChessPlayerConfigManager.Instance.Release();
                ChessPlayerConfigManager.Instance.GetPlayerConfig(m_vAttacker.PlayerData.TinyId, (cfg) =>
                {
                    if (cfg != null)
                        m_vAttacker.SetConfig(cfg);
                });

                ChessBattleLogicPlayerData playerData = new ChessBattleLogicPlayerData();
                m_attacker.InitData(m_attacker.playerData, playerData);
            }
#endif

            //Diagnostic.Log("LogicAttack fc: " + BattleCommonNet.Client_Frame);
            m_battleLogicField.ClearTask();
            m_battleLogicField.ClearLastState();
            m_battleLogicField.ChessBattleResultState = ChessBattleResultState.Start;
            m_battleLogicField.Attack(m_attacker, m_defender, m_willKill, 10, 1, 10);
        }
        protected bool InAttack()
        {
            if (m_battleLogicField == null)
                return true;
            if (m_battleLogicField.ChessBattleResultState == ChessBattleResultState.Start)
                return true;
            if (m_attacker == null)
                return true;
            return m_attacker.InAttack();
        }
        #endregion
    }
}
