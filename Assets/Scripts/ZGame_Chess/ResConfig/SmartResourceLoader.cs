using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using TKFrame.COSResource;
using UnityEngine;
using ZGame.GameSystem;
using ZGameChess;

public interface ISmartResourceLoader
{
    bool Load();
    void Release();
    bool IsLoading();
    bool IsLoaded();
    
    string AssetbundleName { get; }
    string AssetName { get; }
}

/// <summary>
/// 自动根据情况去加载COS上或者本地的资源，自动加载分级资源
/// </summary>
public class SmartResourceLoader<T> : ISmartResourceLoader where T : UnityEngine.Object
{
    public delegate void AssetLoadCallbackWithOwner(T t, string assetBundleName, ISmartResourceLoader loderInst);
    protected COSABAssetListener m_cosListener = null;
    protected LoadedAsset m_loadedAsset = null;
    protected AssetLoadCallback<T> m_localLoadedCB = null;

    protected string m_assetbundleName;
    protected string m_assetName;
    protected ELoadResMode m_loadMode = ELoadResMode.COSFirst;
    protected bool m_isFirstLoad = false;
    protected bool m_isBackground = false;
    protected AssetLoadCallback<T> onLoaded;
    protected AssetLoadCallbackWithOwner OnLoadedWithOwner;
    protected IReleaseList m_releaseList;
    public string AssetbundleName { get { return m_assetbundleName; } }
    public string AssetName { get { return m_assetName; } }

#if ENABLE_ASSET_INFO
    protected string m_assetType = "None";
#endif

    public static bool CheckAssetExist(string abName, string assetName, ELoadResMode mode = ELoadResMode.LocalFirst)
    {
#if UNITY_EDITOR        // 编辑器下非ab模式加载cosab可能会崩溃
        if (AssetBundleManager.SimulateAssetBundleInEditor)
        {
            mode = ELoadResMode.OnlyLocal;
        }
#endif

        ModelPathUtil.GetAssetPath(mode, ref abName, assetName, out bool fromCos);

        if (fromCos)
        {
            if (COS.ABAssetManager.CheckAssetbundleExist(abName))
            {
                // 但是这个接口还是会存在没下载的问题 没下载的就拿不到这个资源在不在ab里面了 有空再改
                //return COS.ABManager.CheckExistAsset(abName, assetName);

                return true;
            }
        }
        else
        {
            if (AssetBundleManager.CheckAssetExist(abName, assetName))
            {
                return true;
            }
        }
        return false;
    }

    public static SmartResourceLoader<T> Load(string abName, string assetName, string typeName, AssetLoadCallback<T> onLoaded, IReleaseList releaseList, ELoadResMode mode = ELoadResMode.LocalFirst, bool firstLoad = false, bool isBackground = false)
    {
        SmartResourceLoader<T> asset = CreateLoader(abName, assetName, typeName, onLoaded, releaseList, mode, firstLoad, isBackground);
        asset.Load();
        return asset;
    }
    
    /// <summary>
    /// AssetLoadCallback&lt;T&gt; onLoaded
    /// </summary>
    /// <param name="abName"></param>
    /// <param name="assetName"></param>
    /// <param name="typeName"></param>
    /// <param name="onLoaded"></param>
    /// <param name="releaseList"></param>
    /// <param name="mode"></param>
    /// <param name="firstLoad"></param>
    /// <param name="isBackground"></param>
    /// <returns></returns>
    public static SmartResourceLoader<T> CreateLoader(string abName, string assetName, string typeName, AssetLoadCallback<T> onLoaded, IReleaseList releaseList, ELoadResMode mode = ELoadResMode.LocalFirst, bool firstLoad = false, bool isBackground = false)
    {
        SmartResourceLoader<T> asset = CreateLoader(abName, assetName, typeName, releaseList, mode, firstLoad, isBackground);
        asset.onLoaded = onLoaded;
        return asset;
    }
    
    /// <summary>
    /// AssetLoadCallbackWithOwner onLoadedWithOwner
    /// </summary>
    /// <param name="abName"></param>
    /// <param name="assetName"></param>
    /// <param name="typeName"></param>
    /// <param name="onLoadedWithOwner"></param>
    /// <param name="releaseList"></param>
    /// <param name="mode"></param>
    /// <param name="firstLoad"></param>
    /// <param name="isBackground"></param>
    /// <returns></returns>
    public static SmartResourceLoader<T> CreateLoader(string abName, string assetName, string typeName, AssetLoadCallbackWithOwner onLoadedWithOwner, IReleaseList releaseList, ELoadResMode mode = ELoadResMode.LocalFirst, bool firstLoad = false, bool isBackground = false)
    {
        SmartResourceLoader<T> asset = CreateLoader(abName, assetName, typeName, releaseList, mode, firstLoad, isBackground);
        asset.OnLoadedWithOwner = onLoadedWithOwner;
        return asset;
    }
    
    private static SmartResourceLoader<T> CreateLoader(string abName, string assetName, string typeName, IReleaseList releaseList, ELoadResMode mode = ELoadResMode.LocalFirst, bool firstLoad = false, bool isBackground = false)
    {
        SmartResourceLoader<T> asset = new SmartResourceLoader<T>();
        asset.m_assetbundleName = abName;
        asset.m_assetName = assetName;
        asset.m_releaseList = releaseList;
#if ENABLE_ASSET_INFO
        asset.m_assetType = typeName;
#endif

#if UNITY_EDITOR        // 编辑器下非ab模式加载cosab可能会崩溃
        if (AssetBundleManager.SimulateAssetBundleInEditor)
        {
            asset.m_loadMode = ELoadResMode.OnlyLocal;
        }
        else
#endif
        {
            asset.m_loadMode = mode;
        }

        asset.m_isFirstLoad = firstLoad;
        asset.m_isBackground = isBackground;
        return asset;
    }
    

    public bool Load()
    {
#if ENABLE_ASSET_INFO
        this.PrintRef(true);
#endif
        string abName = m_assetbundleName;

        ModelPathUtil.GetAssetPath(m_loadMode, ref abName, m_assetName, out bool fromCos);
        {
            if (fromCos)
            {
                if (!LoadFromCos(abName))
                {
                    if (m_loadMode != ELoadResMode.OnlyCOS)
                        return LoadFromLocal(abName);
                    else
                        return false;
                }
                else
                    return true;
            }
            else
            {
                if (!LoadFromLocal(abName))
                {
                    if (m_loadMode != ELoadResMode.OnlyLocal)
                        return LoadFromCos(abName);
                    else
                        return false;
                }
                else
                    return true;
            }
        }
    }

    protected bool LoadFromCos(string abName)
    {
        if (COS.ABAssetManager.CheckAssetbundleExist(abName))
        {
            Diagnostic.Log("[SmartResourceLoader]Load from cos start. ab: {0} asset: {1} m_loadMode: {2}", abName, m_assetName, m_loadMode);
            m_cosListener = COS.ABAssetManager.Load(abName, m_assetName, OnCosLoaded, m_releaseList, true);
            return true;
        }
        else
        {
            Diagnostic.Warn("[SmartResourceLoader]Load from cos faild. ab: {0} asset: {1} find faild!  m_loadMode: {2}", abName, m_assetName, m_loadMode);
            return false;
        }
    }

    private void OnCosLoaded(COSABAssetRequest.Status status, COSBundleAsset asset)
    {
        if (m_cosListener == null)
        {
            string abName = asset != null && asset.bundle != null ? asset.bundle.BundlePath : m_assetbundleName;
            Diagnostic.Warn("[SmartResourceLoader]Load from cos finished. but asset release. status: {0} ab: {1} asset: {2} m_loadMode: {3}", status, abName, m_assetName, m_loadMode);
            onLoaded?.Invoke(null, abName);
            OnLoadedWithOwner?.Invoke(null, abName, this);
        }
        else if (status == COSABAssetRequest.Status.Success)
        {
            Diagnostic.Log("[SmartResourceLoader]Load from cos finished. success ab: {0} asset: {1} m_loadMode: {2}", asset.bundle.BundlePath, m_assetName, m_loadMode);
            onLoaded?.Invoke(asset.Get<T>(), asset.bundle.BundlePath);
            OnLoadedWithOwner?.Invoke(asset.Get<T>(), asset.bundle.BundlePath, this);
        }
        else
        {
            string abName = asset != null && asset.bundle != null ? asset.bundle.BundlePath : m_assetbundleName;
            Diagnostic.Warn("[SmartResourceLoader]Load from cos finished. status: {0} ab: {1} asset: {2} m_loadMode: {3}", status, abName, m_assetName, m_loadMode);
            onLoaded?.Invoke(null, abName);
            OnLoadedWithOwner?.Invoke(null, abName, this);
        }
    }

    protected bool LoadFromLocal(string abName)
    {
        if (AssetBundleManager.CheckAssetBundleExist(abName))
        {
            if (m_isBackground)
            {
                m_loadedAsset = ResourceUtil.LoadAssetSmoothBackground(abName, m_assetName, OnLocalLoaded, out m_localLoadedCB, m_releaseList, m_isFirstLoad);
            }
            else
            {
                m_loadedAsset = ResourceUtil.LoadAssetSmooth(abName, m_assetName, OnLocalLoaded, out m_localLoadedCB, m_releaseList, isFirstLoad: m_isFirstLoad);
            }
            return m_loadedAsset != null;
        }
        Diagnostic.Warn("[SmartResourceLoader]Load from local faild. ab: {0} asset: {1} find faild! m_loadMode: {2}", abName, m_assetName, m_loadMode);
        OnLocalLoaded(null, abName);
        return false;
    }

    private void OnLocalLoaded(T t, string assetBundleName)
    {
        if (m_loadedAsset == null) 
        {
            t = null;
            Diagnostic.Log("[SmartResourceLoader]Load from local finished. but asset release. success ab: {0} asset: {1} m_loadMode: {2}", assetBundleName, m_assetName, m_loadMode);
        }
        else if (t != null)
        {
            Diagnostic.Log("[SmartResourceLoader]Load from local finished. success ab: {0} asset: {1} m_loadMode: {2}", assetBundleName, m_assetName, m_loadMode);
        }
        else
        {
            Diagnostic.Warn("[SmartResourceLoader]Load from local finished. faild ab: {0} asset: {1} m_loadMode: {2}", assetBundleName, m_assetName, m_loadMode);
        }

        onLoaded?.Invoke(t, assetBundleName);
        OnLoadedWithOwner?.Invoke(t, assetBundleName, this);
    }

    public void Release()
    {
        Diagnostic.Log("[SmartResourceLoader] release ab: {0} asset: {1} m_loadMode: {2}", m_assetbundleName, m_assetName, m_loadMode);

#if ENABLE_ASSET_INFO
        PrintRef(false);
#endif

        if (m_cosListener != null)
        {
            m_cosListener.CancelLoad();
            COSPool.Free(m_cosListener);
            m_cosListener = null;
        }

        if (m_loadedAsset != null)
        {
            if (!m_loadedAsset.IsLoaded)
                m_loadedAsset.CancelCallback(m_localLoadedCB);

            if (m_releaseList != null && m_releaseList.removeFromReleaseList(m_loadedAsset))
                m_loadedAsset.Release();

            m_loadedAsset = null;
            m_localLoadedCB = null;
        }
    }

    public bool IsVaild()
    {
        return m_cosListener != null || m_loadedAsset != null;
    }

    public bool IsLoading()
    {
        if (m_loadedAsset != null)
            return !m_loadedAsset.IsLoaded;
        else if (m_cosListener != null)
            return !m_cosListener.IsLoaded();
        return false;
    }

    public bool IsLoaded()
    {
        if (m_loadedAsset != null)
            return m_loadedAsset.IsLoaded;
        else if (m_cosListener != null)
            return m_cosListener.IsLoaded();
        return false;
    }

    public T GetObj()
    {
        if (m_loadedAsset != null)
            return m_loadedAsset.GetAsset<T>();
        else if (m_cosListener != null)
            return m_cosListener.Get<T>();
        return null;
    }

    public bool RebindReleaseListTo(IReleaseList releaseList)
    {
        if (m_loadedAsset != null)
        {
            if (m_releaseList.removeFromReleaseList(m_loadedAsset))
            {
                releaseList.addToReleaseList(m_loadedAsset);
                m_releaseList = releaseList;
                return true;
            }
        }
        else if (m_cosListener != null)
        {
            m_cosListener.RebindReleaseListTo(m_releaseList);
            return true;
        }
        return false;
    }

#if ENABLE_ASSET_INFO

    private void PrintRef(bool isLoad)
    {
        if (!AssetBundleManager.isPrintAssetInfo || m_loadedAsset == null)
            return;
        
        string status = isLoad ? "Load" : "Release";
        Diagnostic.Log("AssetService "+ m_assetType +" "+ status + " " + m_loadedAsset.AssetName + " Ref " + m_loadedAsset.RefCount);
    }
#endif
}

