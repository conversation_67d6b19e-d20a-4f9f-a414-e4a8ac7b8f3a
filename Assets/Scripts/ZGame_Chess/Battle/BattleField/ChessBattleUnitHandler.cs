using System;
using System.Collections.Generic;
using System.Collections;
using TKFrame;
using UnityEngine;
using ZGame.Battle;
using UnityEngine.EventSystems;

namespace ZGameChess
{
    /// <summary>
    /// 英雄功能组件的基类
    /// </summary>
    public class ChessBattleUnitHandler: MonoBehaviour
    {
        protected ChessBattleUnit _unit;

        public ChessBattleUnit Unit
        {
            get { return _unit; }
        }


        protected virtual void OnDestroy()
        {
            _unit = null;
        }

        public virtual void Init(ChessBattleUnit unit)
        {
            _unit = unit;
        }

        public virtual void Tick(float deltaTime)
        {

        }

        public virtual void Release()
        {

        }

        public virtual void OnBodyLoaded()
        {

        }
    }
}
