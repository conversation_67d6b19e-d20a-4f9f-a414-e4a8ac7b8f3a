using System;
using System.Collections.Generic;
using UnityEngine;
using TKFrame;

namespace ZGame.Battle.ChessVisualAction
{
    public class MoveAction : ZGame.Battle.VisualAction.BaseAction
    {
        private Vector3 _targetPostion;
        private Quaternion _targetOrientation;
        private Vector3 _srcPos;
        private Quaternion _srcRotaion;

        private float _duration;
        private float _lifeTime;

        private Transform _transform;
        private MoveActionType moveType = MoveActionType.Linear;

        public override void SetupAction(params object[] actParams)
        {
            Ended = false;
            _transform = this.gameObject.transform;

            if( actParams.Length > 2 )
            {
                _targetPostion = (Vector3)actParams[0];
                _targetOrientation = (Quaternion)actParams[1];
                _lifeTime = (float)actParams[2];
                _duration = 0.0f;
            }

           if( actParams.Length > 3 )
            {
                moveType = (MoveActionType)actParams[3];
            }

        }

        public override void StartAction()
        {
            base.StartAction();
            _srcPos = _transform.position;
            _srcRotaion = transform.rotation;
        }

        public void Update()
        {
            if (this.Ended)
            {
                return;
            }

            _duration += Time.deltaTime;
            Vector3 curPos = _transform.position;
            Quaternion curRotaion = _transform.rotation;
            float a =  Mathf.Clamp01( _duration / _lifeTime);
            switch( moveType )
            {
                case MoveActionType.Linear:
                    
                    break;
                case MoveActionType.EaseInSine:
                    a = MathUtil.EaseInSine(a);
                    break;
                case MoveActionType.EaseOutSine:
                    a = MathUtil.EaseOutSine(a);
                    break;
            }

            _transform.position = Vector3.Lerp(_srcPos, _targetPostion,a);
           // _transform.rotation = Quaternion.Lerp(_srcRotaion, _targetOrientation, a);

            if( a + 0.0001f > 1.0f  )
            {
                _transform.position = _targetPostion;
               // _transform.rotation = _targetOrientation;
                this.Ended = true;
            }

        }

    }
}
