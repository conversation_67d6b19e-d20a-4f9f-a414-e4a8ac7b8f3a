using System;
using System.Collections.Generic;
using UnityEngine;

namespace ZGame.Battle.ChessVisualAction
{


    public class SmoothDampAction : ZGame.Battle.VisualAction.BaseAction
    {
        private Vector3 _targetPosition;
        private bool  _rotX;
        private float _rotXAngle;
        private bool _rotY;
        private float _rotYAngle;
        private bool _rotZ;
        private float _rotZAngle;

        private bool _bScale = false;
        private float _scale;
        private float _scaleVelocity;


        private Vector3 _velocity;
        private float _rotXVelocity;
        private float _rotYVelocity;
        private float _rotZVelocity;

        private float smoothTime = 0.2F;
        private float lifeTime = 0.0F;

        public override void SetupAction(params object[] actParams)
        {
            base.SetupAction(actParams);
            _rotX = _rotY = _rotZ = false;
            if( actParams.Length > 0 )
            {
                _targetPosition = (Vector3) actParams[0];
            }
            if ( actParams.Length > 6 )
            {
                _rotX = (bool)actParams[1];
                _rotXAngle = (float)actParams[2];

                _rotY = (bool)actParams[3];
                _rotYAngle = (float)actParams[4];

                _rotZ = (bool)actParams[5];
                _rotZAngle = (float)actParams[6];
            }
           
            if( actParams.Length > 7 )
            {
                smoothTime = (float)actParams[7];
            }

            if (actParams.Length > 8)
            {
                _bScale = true;
                _scale = (float)actParams[8];
            }
        }

        public override void StartAction()
        {
            base.StartAction();
            lifeTime = 0.0f;
            _velocity = Vector3.zero;
        }

        //停止Action 执行回调处理
        public override void StopAction()
        {
            if (this._Ended) return;

            var locActCb = _ActionEndCB;
            _ActionEndCB = null;
            if (locActCb != null)
            {
                locActCb(this);
            }
            this._Ended = true;

        }

        public void Update()
        {
            if (this.Ended)
            {
                return;
            }

            float deltaTime = 0.0f;

            deltaTime = Time.deltaTime;

            lifeTime += deltaTime;
            transform.position = Vector3.SmoothDamp(transform.position, _targetPosition, ref _velocity, smoothTime, float.PositiveInfinity, deltaTime);
           
            Vector3 eulerAngle = transform.rotation.eulerAngles;
            if( _rotX )
            {
                eulerAngle.x = Mathf.SmoothDampAngle(eulerAngle.x, _rotXAngle, ref _rotXVelocity, smoothTime, float.PositiveInfinity, deltaTime);
            }

            if (_rotY)
            {
                eulerAngle.y = Mathf.SmoothDampAngle(eulerAngle.y, _rotYAngle, ref _rotYVelocity, smoothTime, float.PositiveInfinity, deltaTime);
            }

            if (_rotZ)
            {
                eulerAngle.z = Mathf.SmoothDampAngle(eulerAngle.z, _rotZAngle, ref _rotZVelocity, smoothTime, float.PositiveInfinity, deltaTime);
            }
            transform.rotation = Quaternion.Euler(eulerAngle);

            if( _bScale )
            {
                float curScale = transform.localScale.x;
                curScale = Mathf.SmoothDamp(curScale, _scale, ref _scaleVelocity, smoothTime, float.PositiveInfinity, deltaTime);
                transform.localScale = new Vector3(curScale, curScale, curScale);
            }

            if (Vector3.Distance(transform.position, _targetPosition) < 0.001f && lifeTime > smoothTime)
            {
                transform.position = _targetPosition;
                this.Ended = true;

                Vector3 newEulerAngle = transform.rotation.eulerAngles;
                if (_rotX)
                {
                    newEulerAngle.x =_rotXAngle;
                }

                if (_rotY)
                {
                    newEulerAngle.y =_rotYAngle;
                }

                if (_rotZ)
                {
                    newEulerAngle.z = _rotZAngle;
                }
                transform.rotation = Quaternion.Euler(eulerAngle);

            }
        }
    }
}
