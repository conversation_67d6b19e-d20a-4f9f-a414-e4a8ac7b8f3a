//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.18408
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TKFrame;
/**
 **增加可以渐显和渐隐的Action
 **参数1： para1:播放类型--Fade_In_Type渐显   Fade_Out_Type渐隐
 **参数2： para2:播放时间
 **/

namespace ZGame.Battle.ChessVisualAction
{
	public enum FadeInOutType
	{
		Fade_In_Type,
		Fade_Out_Type
	}

	public class FadeInOutAction : ZGame.Battle.VisualAction.BaseAction
    {
		private FadeInOutType aniType;
		private float _duration;
		private float _lifeTime;
		public FadeInOutAction ()
		{
		}

		//设置动画参数
		public override void SetupAction(params object[] actParams)
		{
			Ended = false;

			if( actParams.Length == 2 )
			{
				aniType = (FadeInOutType)actParams[0];
				_lifeTime = (float)actParams[1];
				_duration = 0.0f;
			}
		}

		//开始播放动画
		public override void StartAction()
		{
			base.StartAction();

			Graphic[] allImages = this.GetComponentsInChildren<Graphic>();
			if(aniType == FadeInOutType.Fade_In_Type)
			{
				for( int i = 0; i < allImages.Length; i++ )
				{
					allImages[i].CrossFadeAlpha(1.0f, _lifeTime, false);
				}
			}
			else if(aniType == FadeInOutType.Fade_Out_Type)
			{
				for (int i = 0; i < allImages.Length; i++)
				{
					allImages[i].CrossFadeAlpha(0.0f, _lifeTime, false);
				}
			}
		}

		//动画更新
		public void Update()
		{
			if (this.Ended)
			{
				return;
			}
			
			_duration += Time.deltaTime;
			float a =  Mathf.Clamp01( _duration / _lifeTime);
			if( a + 0.0001f > 1.0f  )
			{
				this.Ended = true;
			}
			
		}
	}
}

