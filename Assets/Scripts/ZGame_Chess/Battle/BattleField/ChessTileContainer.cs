using System;
using System.Collections;
using System.Collections.Generic;
using ACG.Core;
using TKFrame;
using UnityEngine;
using UnityEngine.EventSystems;
using GameFramework.FMath;

namespace ZGameChess
{
    /// <summary>
    /// 英雄布阵操作接口
    /// </summary>
    public interface IChessManipulator
    {
        void OnBeginDragContainer(PointerEventData eventData);

        void OnDragContainer(PointerEventData eventData);

        void OnEndDragContainer(PointerEventData eventData);

        void OnClickedContainer(PointerEventData eventData);

        void OnClickedUpContainer(PointerEventData eventData);
    }

    /// <summary>
    /// 格子的容器（阵形，兵营）
    /// </summary>
    public class ChessTileContainer : MonoBehaviour, IDragHandler, IEndDragHandler, IBeginDragHandler, IPointerDownHandler, IPointerUpHandler
    {

        public GameObject RootNode
        {
            get { return gameObject; }
        }


        public AreaType containerType;

        private Dictionary<string, GameObject> _commonFxDict = new Dictionary<string, GameObject>();

        private int _row, _col;
        private int _availableTilesCount;
        public IEnumerator Init(AreaType type, int row_num, int col_num)
        {
            containerType = type;
            ACGEventManager.Instance.AddEventListener(BattleMapManager.MapChange, RefreshTilePos);

            _row = row_num;
            _col = col_num;
            _availableTilesCount = row_num * col_num;

            yield break;
        }

        public void RefreshTilePos(GEvent e)
        {

        }

        public Dictionary<int, List<string>> tileEffDic = new Dictionary<int, List<string>>();
        
        public Dictionary<int, List<GameObject>> EffDic = new Dictionary<int, List<GameObject>>();



        public void Release()
        {

        }

        public void OnBeginDrag(PointerEventData eventData)
        {
            if (ChessBattleGlobal.Instance.ChessManipulator != null)
            {
                ChessBattleGlobal.Instance.ChessManipulator.OnBeginDragContainer(eventData);
            }
        }

        public void OnDrag(PointerEventData eventData)
        {
            if (ChessBattleGlobal.Instance.ChessManipulator != null)
            {
                ChessBattleGlobal.Instance.ChessManipulator.OnDragContainer(eventData);
            }
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            if (ChessBattleGlobal.Instance.ChessManipulator != null)
            {
                ChessBattleGlobal.Instance.ChessManipulator.OnEndDragContainer(eventData);
            }
        }

        public void OnPointerDown(PointerEventData eventData)
        {
            if (ChessBattleGlobal.Instance.ChessManipulator != null)
            {
                ChessBattleGlobal.Instance.ChessManipulator.OnClickedContainer(eventData);
            }
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            if (ChessBattleGlobal.Instance.ChessManipulator != null)
            {
                ChessBattleGlobal.Instance.ChessManipulator.OnClickedUpContainer(eventData);
                //ChessBattleGlobal.Instance.ChessManipulator.OnClickedContainer(eventData);
            }
        }

       

        
        private static FixedDataList<AutoChessGrid.GridOffset> resultList_specialRange = null;

        public static void ClearResultListSpecialRange()
        {
            resultList_specialRange = null;
        }
    }
}
