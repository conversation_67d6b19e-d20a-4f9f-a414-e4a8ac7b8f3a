using ACG.Core;
using GameFramework.FMath;
using Lucifer.ActCore;
using NTween;
using System;
using System.Collections;
using System.Collections.Generic;
using TDRConfig;
using TKFrame;
using TKFrame.Utility;
using TKPlugins;
using UnityEngine;
using Z_PVE;
using ZGame;
using ZGame.Battle.ChessVisualAction;
using ZGame.Level;
using ZGameClient;

namespace ZGameChess
{
    public enum DropShowType
    {
        HomeField = 1,
        AwayField = 2,
        BothField = 3,
        MyField = 4,
    }

    public class EquipmentController
    {
        //野怪掉落相关资源
        //private List<EquipmentDropBehaviour> _monsterDropItemList = new List<EquipmentDropBehaviour>();
        //private Dictionary<string, LoadedAsset> _monsterDropItemAssetDic = new Dictionary<string, LoadedAsset>();
        //private Dictionary<string, Queue<EquipmentDropBehaviour>> _monsterDropItemPoolDic = new Dictionary<string, Queue<EquipmentDropBehaviour>>();

        //装备掉落相关资源
        protected LoadedAsset _equipmentDropItemAsset;
        protected GameObject _equipmentDropItemAssetGameObject;
        protected GameObject _equipmentDropBoxSamepleGameObject;

        // 掉落问号相关资源
        protected List<DropBox> m_dropboxList = new List<DropBox>();
        protected Queue<DropBox> m_dropboxItemPool = new Queue<DropBox>();
        protected int m_dropboxCount = 0;

        public string defaultIconName = "Widgets_Image_Default_Unknown";

        // 掉落问号的皮肤 （可更换）
        private int m_dropBoxSkinId = 1;
        public int DropBoxSkinId
        {
            get
            {
                return m_dropBoxSkinId;
            }
            set
            {
                m_dropBoxSkinId = value;
            }
        }


        protected Transform _dropRoot;

        protected UnityCoroutine m_unityCoroutine;

        private UnityCoroutine m_mercenaryCoroutine;

        public virtual IEnumerator Initialize()
        {
            m_dropboxCount = 0;

            _dropRoot = new GameObject("DropRoot").transform;
            //_dropRoot.SetParent(BattleWorldObject.Instance.WorldRoot.transform);

            PreloadRes();

            while (!IsAssetLoaded())
            {
                yield return null;
            }

            InitPool();
        }

        private bool IsAssetLoaded()
        {
            if (_equipmentDropItemAsset == null || !_equipmentDropItemAsset.IsLoaded)
            {
                return false;
            }

            //foreach (var item in _monsterDropItemAssetDic.Values)
            //{
            //    if (item == null || !item.IsLoaded)
            //        return false;
            //}
            return true;
        }

        private void PreloadRes()
        {
            _equipmentDropItemAsset = ResourceUtil.LoadAssetToReleaseList<GameObject>("art_tft_raw/equipment/equipment_drop_cube", "equipment_drop_cube", null, null);

            //int defaultDropboxSkinId = 1;
            ////List<string> monsterAssetNames = new List<string>();
            //var defaultDropboxSkinCfg = DataBaseManager.Instance.SearchACGBoxSkin(defaultDropboxSkinId);
            //if (defaultDropboxSkinCfg != null)
            //{
            //    //defaultDropboxSkinCfg
            //}
            //monsterAssetNames.Add("scene_bronze_box");
            //monsterAssetNames.Add("scene_silver_box");
            //monsterAssetNames.Add("scene_golden_box");

            //for (int i = 0; i < monsterAssetNames.Count; i++)
            //{
            //    var loadedAsset = ResourceUtil.LoadAssetToReleaseList<GameObject>(ChessAssetBundleRespository.DROP_BOX_SKIN_RES_AB, monsterAssetNames[i], null, null);
            //    if (!_monsterDropItemAssetDic.ContainsKey(monsterAssetNames[i]))
            //        _monsterDropItemAssetDic.Add(monsterAssetNames[i], loadedAsset);
            //}
        }

        private void InitPool()
        {
            //List<EquipmentDropBehaviour> preAlloc = new List<EquipmentDropBehaviour>();

            //List<string> monsterAssetNames = new List<string>();
            //monsterAssetNames.Add("scene_bronze_box");
            //monsterAssetNames.Add("scene_silver_box");
            //monsterAssetNames.Add("scene_golden_box");

            //for (int i = 0; i < monsterAssetNames.Count; i++)
            //{
            //    preAlloc.Clear();
            //    for (int k = 0; k < 10; k++)
            //    {
            //        preAlloc.Add(CreateMonsterDropItem(0, monsterAssetNames[i]));
            //    }

            //    for (int k = 0; k < preAlloc.Count; k++)
            //    {
            //        RecycleMonsterDropItem(preAlloc[k]);
            //    }
            //}

            //preAlloc.Clear();
            //for (int i = 0; i < 10; i++)
            //{
            //    preAlloc.Add(CreateEquipmentDropItem());
            //}
            //for (int k = 0; k < preAlloc.Count; k++)
            //{
            //    RecycleEquipmentDropItem(preAlloc[k]);
            //}

            List<DropBox> dropBoxList = new List<DropBox>();
            for (int i = 0; i < 10; ++i)
            {
                dropBoxList.Add(CreateDropBoxItem());
            }
            for (int i = 0; i < dropBoxList.Count; ++i)
                ReleaseDropbox(dropBoxList[i]);
        }

        public void BindNotifier()
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            PlayerModel notifier = battleModel.GetCurPlayerModel();
            //视角玩家绑定相关事件
            notifier.ObserverList -= OnPlayerObserveMessage;
            notifier.ObserverList += OnPlayerObserveMessage;
            
            //场地双方玩家绑定通用场地事件
            notifier.ObserverList -= OnBattleFieldObserveMessage;
            notifier.ObserverList += OnBattleFieldObserveMessage;
            var enemyNotifier = battleModel.GetPlayerModel(notifier.EnemyPlayerID);
            if (enemyNotifier != null)
            {
                enemyNotifier.ObserverList -= OnBattleFieldObserveMessage;
                enemyNotifier.ObserverList += OnBattleFieldObserveMessage;
            }
            //PlayerModel homeNotifier = battleModel.GetCurHomePlayerModel();
            //homeNotifier.ObserverList -= OnBattleFieldObserveMessage;
            //homeNotifier.ObserverList += OnBattleFieldObserveMessage;

            ChessModelManager.Instance.GetBattleModel().ObserverList += this.HandleMsgEvent;
        }

        public void UnBindNotifier()
        {
            ChessBattleModel battleModel = ChessModelManager.Instance.GetBattleModel();
            foreach (var notifier in battleModel.GetAllPlayers().Values)
            {
                notifier.ObserverList -= OnPlayerObserveMessage;
                notifier.ObserverList -= OnBattleFieldObserveMessage;
            }

            ChessModelManager.Instance.GetBattleModel().ObserverList -= this.HandleMsgEvent;
        }

        public void ReBindBattleField()
        {
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            foreach (var notifier in battleModel.GetAllPlayers().Values)
            {
                notifier.ObserverList -= OnBattleFieldObserveMessage;
            }
            PlayerModel curNotifier = battleModel.GetCurPlayerModel();
            curNotifier.ObserverList += OnBattleFieldObserveMessage;
            PlayerModel enemyNotifier = battleModel.GetPlayerModel(curNotifier.EnemyPlayerID);
            if (enemyNotifier != null)
                enemyNotifier.ObserverList += OnBattleFieldObserveMessage;
        }

        //场地相关事件，需要对战双方都看得到
        private void OnBattleFieldObserveMessage(ObservableMessage message)
        {
            switch (message.Name)
            {
                case PlayerModel.NOTIFY_CUSTOM_ID_PICKUP_MONSTER_DROP:
                    {
                        OnPickUpMonsterDrop(message.Value as ACG_TCmdS2CNotifyPickUpMonsterDrop);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_DROP_COIN:
                    {
                        OnDropCoin(message.Value as ACG_TCmdS2CNotifyCoinDrop);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_PICKUP_DROP_COIN:
                    {
                        OnPickUpDropCoin(message.Value as ACG_TCmdS2CNotifyPickUpDropCoin);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_AUTO_PICK_DROP_COINS:
                    {
                        //OnAutoPickDropCoins(message.Value as TAC_TCmdS2CNotifyAutoPickGoldCoins);
                    }
                    break;
                case PlayerModel.NOTIFY_CUNSOM_ID_OPEN_DROP_BOX:
                    {
                        //ChessBattleGlobal.Instance.DropBoxCtl.OnOpenDropBox(message.Value as ACG_TCmdS2CNotifyOpenDropBox);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_DROP_HERO:
                    {
                        //OnDropHero(message.Value as ACG_TCmdS2CNotifyDropHero);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_DROP_HERO_FROM_HEX:
                {
                    //Vector3 start = Vector3.one;
                    //if(ChessBattleGlobal.Instance.HAController != null)
                    //    start = ChessBattleGlobal.Instance.HAController.HomeHexcoreUnit.transform.position;
                    //OnDropHeroFromHexUnit(message.Value as ACG_TCmdS2CNotifyDropHero, start);
                }
                    break;
                case PlayerModel.NOTIFY_ROLL_DICE:
                    {
                        //OnRollDice(message.Value as TAC_TCmdS2CNotifyMercenaryDice);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_AUTO_PICK_SPACE_PIRATE_COINS:
                    {
                        //OnAutoPickSpacePirateCoins(message.Value as TAC_TCmdS2CNotifyAutoPickSpacePirateCoins);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_MONSTER_DROP:
                    {
                        OnMonsterDrop(message.Value as ACG_TCmdS2CNotifyMonsterDrop);
                    }
                    break;
            }
        }

        private void OnPlayerObserveMessage(ObservableMessage message)
        {
            switch (message.Name)
            {
                case PlayerModel.NOTIFY_CUSTON_ID_UPDATE_HERO_EQUIPMENT:
                    {
                        //List<int> values = message.Value as List<int>;
                        //PlayerModel playerModel = null;
                        //UnitData unitData = null;
                        //if (values != null && values.Count == 3)
                        //{
                        //    playerModel = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(values[0]);
                        //    if (playerModel != null)
                        //        unitData = playerModel.GetUnitData(values[1]);
                        //    //if (values[2] == (int)HeroEquipmentUpdateType.BackEquipmentOP)
                        //    if (unitData != null)
                        //        HeroInfoShow.RefreshEquipment(unitData.heroId);
                        //}
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_UPDATE_REPLAY_DATA:
                    {
                        //ReShowMonsterDrop();
                        //ChessBattleGlobal.Instance.DropBoxCtl.OnUpdateReplayData();
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_UNQUIP_EXCLUSIVE_EQUIPMENT:
                    {
                        //OnUnquipExclusiveEquipment(message.Value as ACG_TCmdS2CNotifyUnquipExclusiveEquipment);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_EQUIPMENT_CHANGE_BY_SELL_HERO:
                    {
                        //OnEquipmentChangeBySellHero(message.Value as ACG_TCmdS2CNotifyUpdatePlayerEquipment);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_EQUIPMENT_CHANGE_BY_UPGRADE_HERO:
                    {
                        //OnEquipmentChangeBySellHero(message.Value as ACG_TCmdS2CNotifyUpdatePlayerEquipment);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_CLONE_HERO_FOR_EQUIPMENT:
                    {
                        //OnCloneHeroForEquipment(message.Value as ACG_TCmdS2CNotifyCloneHeroForEquipment);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_CLONE_HERO_FOR_EQUIPMENT_TIPS:
                    {
                        //OnCloneHeroForEquipmentTips(message.Value as ACG_TCmdS2CNotifyCloneHeroForEquipment);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_TURN_START_STEAL_EQUIPMENTS:
                    {
                        //OnStealEquipments(message.Value as ACG_TCmdS2CNotifyStealEquipment);
                    }
                    break;
                case PlayerModel.NOTIFY_YORDLE_SUMMON:
                    {
                        //OnYordleSummon(message.Value as TAC_TCmdS2CNotifyYordleSummon);
                    }
                    break;
                case PlayerModel.NOTIFY_CUSTOM_ID_GADGETEENS_EQUIPMENTS:
                    {
                        //OnGadgeteensEquipments(message.Value as ACG_TCmdS2CNotifyGadgeteensEquipment);
                    }
                    break;
            }
        }

        
        public void OnMonsterDrop(ACG_TCmdS2CNotifyMonsterDrop notifyMonsterDrop)
        {
            if (notifyMonsterDrop.stMonsterDropItem != null )
            {
                CheckCreateDropBoxUnit(notifyMonsterDrop.iPlayerID, notifyMonsterDrop.stMonsterDropItem, true);
            }
        }


        public void OnPickUpMonsterDrop(ACG_TCmdS2CNotifyPickUpMonsterDrop notifyPickUpMonsterDrop)
        {
            //Diagnostic.Error("neison OnPickUpMonsterDrop playerID:{0}|dropID:{1}|currentPlayerID:{2}", notifyPickUpMonsterDrop.iPlayerID, notifyPickUpMonsterDrop.pickUpItem.iEntityID, ChessModelManager.Instance.GetBattleModel().CurrentPlayerId);
            //if (ChessModelManager.Instance.GetBattleModel().CheckShowType(notifyPickUpMonsterDrop.iPlayerID,DropShowType.BothField))
            {
                if (notifyPickUpMonsterDrop.iRet == 0)
                {
                    var dropItem = GetDropBox(notifyPickUpMonsterDrop.pickUpItem.iEntityID);

                    //if (notifyPickUpMonsterDrop.pickUpItem.iDropPoolID >= 0)
                    //{
                    //    int boxType = ChessModelManager.Instance.GetBattleModel().SoGameData_View.GetBoxType(notifyPickUpMonsterDrop.pickUpItem.iDropPoolID);

                    //    if (boxType != 0)
                    //    {
                    //        if (dropItem != null)
                    //            GameUtil.PlayPoolEffect(boxType == (int)DropBox.Level.Golden ? "scene_dropitem1" : "scene_dropitem", dropItem.transform.position);
                    //    }
                    //    else
                    //    {
                    //        var boxInfo = DataBaseManager.Instance.GetBoxInfo(notifyPickUpMonsterDrop.pickUpItem.iDropPoolID);
                    //        if (dropItem != null && boxInfo != null)
                    //        {
                    //            GameUtil.PlayPoolEffect(boxInfo.iBoxLevel == (int)DropBox.Level.Golden ? "scene_dropitem1" : "scene_dropitem", dropItem.transform.position);
                    //        }
                    //    }
                    //}
                    //else
                    //{
                    //    int boxType = 0 - notifyPickUpMonsterDrop.pickUpItem.iDropPoolID;
                    //    if (dropItem != null)
                    //        GameUtil.PlayPoolEffect(boxType == (int)DropBox.Level.Golden ? "scene_dropitem1" : "scene_dropitem", dropItem.transform.position);
                    //}

                    ClearMonsterDropByEntityId(notifyPickUpMonsterDrop.pickUpItem.iEntityID);
                    // 拾取装备音效 V1.60
                    if (dropItem != null)
                        ChessUtil.PlayWwiseBankByPath("UI", "Play_sfx_tft_item_mysterybox_open_level2_01", dropItem.gameObject);

                }
                else if (notifyPickUpMonsterDrop.iRet == -97)
                {
                    ClearMonsterDropByEntityId(notifyPickUpMonsterDrop.pickUpItem.iEntityID);
                }
            }
        }

        public void OnDropCoin(ACG_TCmdS2CNotifyCoinDrop notifyCoinDrop)
        {
            //ChessBattleGlobal.Instance.DropBoxCtl.OnDropCoin(notifyCoinDrop);
        }

        public void OnPickUpDropCoin(ACG_TCmdS2CNotifyPickUpDropCoin notifyCoinDrop)
        {
            //ChessBattleGlobal.Instance.DropBoxCtl.OnPickUpDropCoin(notifyCoinDrop);
        }
        private List<int> FlyEffectFilter = new List<int>();
        public bool IsCloneEquipment(int effectType)
        {
            return (effectType == (int)EquipmentEffecType.HERO_CLONE
                || effectType == (int)EquipmentEffecType.HERO_CLONE_INDEPENDENT
                || effectType == (int)EquipmentEffecType.HERO_CLONE_PARTER);
        }
 
        public void HandleMsgEvent(ObservableMessage msg)
        {
            if (msg.ID == (int)TAC_GAME_SC_MSG_ID.TAC_SC_NOTIFY_TURN_START)
            {
                //RefreshUnitsEquipmentData(ChessModelManager.Instance.GetBattleModel().GetCurPlayerModel().BattleUnitDataList);
                //RefreshUnitsEquipmentData(ChessModelManager.Instance.GetBattleModel().GetCurPlayerModel().WaitUnitDataList);
            }
        }



        public void ClearMonsterDrop()
        {
            //移除
            //for (int i = 0; i < _monsterDropItemList.Count; i++)
            //{
            //    _monsterDropItemList[i].Close() ;

            //    RecycleMonsterDropItem(_monsterDropItemList[i]);
            //}
            //_monsterDropItemList.Clear();

            for (int i = 0; i < m_dropboxList.Count; ++i)
            {
                ReleaseDropbox(m_dropboxList[i]);
            }
            m_dropboxList.Clear();
        }

        public DropBox CreateDropBoxItem()
        {
            var count = m_dropboxItemPool.Count;
            if (count > 0)
            {
                DropBox dropbox = m_dropboxItemPool.Dequeue();
                dropbox.gameObject.SetActive(true);
                return dropbox;
            }
            else
            {
                ++m_dropboxCount;
                // 防止加载时因add component导致的卡顿
                if (_equipmentDropBoxSamepleGameObject == null)
                {
                    _equipmentDropBoxSamepleGameObject = new GameObject();
                    _equipmentDropBoxSamepleGameObject.AddComponent<ParabolaCurveMoveHandler>();
                    _equipmentDropBoxSamepleGameObject.AddComponent<DropBox>();
                    _equipmentDropBoxSamepleGameObject.SetActive(false);
                }
                GameObject dropboxGo = GameObject.Instantiate(_equipmentDropBoxSamepleGameObject, _dropRoot);
                dropboxGo.SetActive(true);
                dropboxGo.name = "DropBox_" + m_dropboxCount;
                DropBox dropBox = dropboxGo.GetComponent<DropBox>();
                return dropBox;
            }
        }

        public void ReleaseDropbox(DropBox dropbox)
        {
            dropbox.CloseView();
            dropbox.gameObject.SetActive(false);
            m_dropboxItemPool.Enqueue(dropbox);
        }

        public DropBox GetDropBox(int entityID)
        {
            for (int i = 0; i < m_dropboxList.Count; i++)
            {
                if (m_dropboxList[i].MonsterDropItem.iEntityID == entityID)
                {
                    return m_dropboxList[i];
                }
            }
            return null;
        }
        

        private DropBox CreateDropBox(int poolID,int skinID)
        {
            int boxType = 0;
            DropBox.Level boxLevel;
            if (boxType != 0)
            {
                boxLevel = boxType != 0 ? (DropBox.Level)boxType : DropBox.Level.Bronze;
            }
            else
            {
                TACG_BoxType_Client boxInfo = DataBaseManager.Instance.GetBoxInfo(poolID);
                boxLevel = boxInfo != null ? (DropBox.Level)boxInfo.iBoxLevel : DropBox.Level.Bronze;
            }

            DropBox dropBox = CreateDropBoxItem();
            dropBox.SetLevel(boxLevel);
            dropBox.SetSkin(skinID == 0 ? DropBoxSkinId : skinID);
            dropBox.ShowView();
            return dropBox;


            //Queue<EquipmentDropBehaviour> dropItemPool = GetDropItemPool(assetName);
            //LoadedAsset dropItemAsset = null;
            //_monsterDropItemAssetDic.TryGetValue(assetName, out dropItemAsset);

            //if (dropItemPool.Count > 0)
            //{
            //    EquipmentDropBehaviour dropBehaviour = dropItemPool.Dequeue();
            //    dropBehaviour.gameObject.SetActive(true);
            //    return dropBehaviour;
            //}
            //else if (dropItemAsset != null && dropItemAsset.IsLoaded)
            //{
            //    GameObject dropItem = GameObject.Instantiate(dropItemAsset.GetAsset<GameObject>(), _dropRoot);
            //    //dropItem.transform.SetParent(_dropRoot);
            //    EquipmentDropBehaviour dropBehaviour = dropItem.AddComponent<EquipmentDropBehaviour>();
            //    dropBehaviour.DropBehaviourType = DropBehaviourType.MonsterItem;
            //    dropBehaviour.PoolKey = assetName;
            //    return dropBehaviour;
            //}
            //return null;
        }

        //public void RecycleMonsterDropItem(EquipmentDropBehaviour item)
        //{
        //    if (item == null || string.IsNullOrEmpty(item.PoolKey))
        //        return;

        //    string assetName = item.PoolKey;
        //    //if (string.IsNullOrEmpty(assetName))
        //    //{
        //    //    TACG_BoxType_Client boxType = CSoGame.CSoConfig.GetBoxType(item.ID);
        //    //    assetName = boxType != null && !string.IsNullOrEmpty(boxType.sBoxAvatar) ? boxType.sBoxAvatar : "scene_bronze_box";
        //    //}

        //    Queue<EquipmentDropBehaviour> cachePool = null;
        //    if (!_monsterDropItemPoolDic.TryGetValue(assetName, out cachePool))
        //    {
        //        cachePool = new Queue<EquipmentDropBehaviour>();
        //        _monsterDropItemPoolDic.Add(assetName, cachePool);
        //    }
        //    item.gameObject.SetActive(false);
        //    item.OnRecycle();
        //    cachePool.Enqueue(item);
        //}


        public void ClearMonsterDropByEntityId(int iEntityID)
        {
            //移除
            //for (int i = 0; i < _monsterDropItemList.Count; i++)
            //{
            //    if (_monsterDropItemList[i].MonsterDropItem.iEntityID == iEntityID)
            //    {
            //        _monsterDropItemList[i].gameObject.SetActive(false);
            //        RecycleMonsterDropItem(_monsterDropItemList[i]);
            //        _monsterDropItemList.Remove(_monsterDropItemList[i]);
            //    }
            //}

            for (int i = 0; i < m_dropboxList.Count; ++i)
            {
                var dropBox = m_dropboxList[i];
                if (dropBox.MonsterDropItem != null && dropBox.MonsterDropItem.iEntityID == iEntityID)
                {
                    ReleaseDropbox(dropBox);
                    m_dropboxList.RemoveAt(i);
                    break;
                }
            }
        }

        public void ReShowMonsterDrop()
        {
            ClearMonsterDrop();

            var battleModel = ChessModelManager.Instance.GetBattleModel();
            var homePlayerModel = battleModel.GetCurHomePlayerModel();

            List<ACG_MonsterDropItem> monsterDropItems = homePlayerModel.MonsterDropItems;
            List<DropBox> preDrops = m_dropboxList;
            m_dropboxList = new List<DropBox>();

            if (monsterDropItems.Count > 0)
            {
                for (int i = 0; i < monsterDropItems.Count; i++)
                {
                    bool isExist = false;
                    for (int j = 0; j < preDrops.Count; j++)
                    {
                        if (preDrops[j].MonsterDropItem.iEntityID == monsterDropItems[i].iEntityID)
                        {
                            m_dropboxList.Add(preDrops[j]);
                            preDrops[j].SetPlay(preDrops[j].MonsterDropItem, false);
                            preDrops.RemoveAt(j);
                            isExist = true;
                            break;
                        }
                    }
                    if (!isExist)
                    {
                        CheckCreateDropBoxUnit(homePlayerModel.PlayerId, monsterDropItems[i]);
                    }
                }
            }

            for (int i = 0; i < preDrops.Count; i++)
            {
                ReleaseDropbox(preDrops[i]);
            }
            preDrops.Clear();
        }
        

        //private Queue<EquipmentDropBehaviour> GetDropItemPool(string assetName)
        //{
        //    Queue<EquipmentDropBehaviour> cachePool = null;
        //    if (!_monsterDropItemPoolDic.TryGetValue(assetName, out cachePool))
        //    {
        //        cachePool = new Queue<EquipmentDropBehaviour>();
        //        _monsterDropItemPoolDic.Add(assetName, cachePool);
        //    }
        //    return cachePool;
        //}


        private void CheckCreateDropBoxUnit(int playerId, ACG_MonsterDropItem notifyMonsterDrop, bool playAnim = false)
        {
            var player = ChessModelManager.Instance.GetBattleModel().GetCurPlayerModel();
            if (player != null && player.iPlayerHpLeft <= 0 || notifyMonsterDrop == null)
                return;

            if (player.PlayerId == playerId || player.EnemyPlayerID == playerId)
            {
                DropBox dropbox = CreateDropBox(notifyMonsterDrop.iDropPoolID, notifyMonsterDrop.iSkinID);
                if (dropbox != null)
                {
                    m_dropboxList.Add(dropbox);
                    dropbox.SetPlay(notifyMonsterDrop, playAnim);
                }
            }
        }

        private bool CanCreate(ACG_MonsterDropItem monsterDrop)
        {
            bool canCreate = true;
            for (int i = 0; i < m_dropboxList.Count; i++)
            {
                if (m_dropboxList[i].MonsterDropItem.iEntityID == monsterDrop.iEntityID)
                {
                    canCreate = false;
                }
            }

            if (canCreate)
            {
                List<ACG_MonsterDropItem> monsterDropItems = ChessModelManager.Instance.GetBattleModel().GetCurPlayerModel().MonsterDropItems;
                for (int i = 0; i < monsterDropItems.Count; ++i)
                {
                    if (monsterDropItems[i].iEntityID == monsterDrop.iEntityID)
                        return true;
                }
                return false;
            }

            return canCreate;
        }

        protected virtual IEnumerator PlayEquipmentDropEff(int playerID, ACG_Equipment acgEquipment, int heroEntityID, float waitTime = 0.0f, ChessBattleUnit defautUnit = null)
        {

            {
                yield return null;
            }

        }

        private static WaitForSeconds ONE_HALF_SECONDS = new WaitForSeconds(1.5f);


        public virtual void Release()
        {
           // if (_equipmentDropItemAsset != null)
             //   _equipmentDropItemAsset.Release();

            m_dropboxCount = 0;
            foreach (var item in m_dropboxList)
            {
                item.CloseView();
            }
            m_dropboxList.Clear();
            m_dropboxItemPool.Clear();

            _dropRoot = null;

            UnBindNotifier();
        }
    }
}
