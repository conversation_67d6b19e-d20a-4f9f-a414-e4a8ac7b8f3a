
using TKFrame;
using UnityEngine;
using Z_PVE;
using ZGame.Battle;
using ZGameClient;

namespace ZGameChess
{
    public class Equipment : Actor
    {
        public ACG_Equipment _equipmentData = null;
        public ACG_Equipment EquipmentData
        {
            get
            {
                return _equipmentData;
            }
            set
            {
                _equipmentData = value;
                TableID = _equipmentData != null ? _equipmentData.iTableID : -1;
            }
        }

        private int _tableID = -1;
        public int TableID
        {
            get
            {
                return _tableID;
            }
            set
            {
                if(_tableID != value)
                {
                    _tableID = value;
                    UpdateRes();
                }
            }
        }

        private string _resName = string.Empty;

        private GameObject _resGO = null;

        private void UpdateRes()
        {
            if(_tableID == -1)
            {
                return;
            }
            _resName = "equipment_cube";

            if(_resGO != null)
            {
                EquipmentPool.FreeRes(_resGO.name, _resGO);
            }

            _resGO = EquipmentPool.AllocateRes(_resName);

            if(_resGO == null)
            {
                //Callback_LoadedAsset(EquipmentTest.ResPrefab, "");
            }
            else
            {
                _resGO.transform.SetParent(this.transform);
            }
        }

        private void Callback_LoadedAsset(GameObject tplGo, string asset)
        {
            _resGO = GameObject.Instantiate(tplGo);
            _resGO.transform.SetParent(this.transform);
        }

        public void OnAllocate(ACG_Equipment equipmentData)
        {
            EquipmentData = equipmentData;
        }

        public void OnFree()
        {
            EquipmentData = null;
            EquipmentPool.FreeRes(_resName, _resGO);
            _resGO = null;
        }
    }
}
