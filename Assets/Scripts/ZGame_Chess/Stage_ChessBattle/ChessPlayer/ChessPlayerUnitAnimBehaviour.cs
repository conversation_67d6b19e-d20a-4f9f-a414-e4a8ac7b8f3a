using System;
using System.Collections.Generic;
using UnityEngine;
using TKFrame;
using TriggerSystem;
using ZGame.Battle;
using Random = System.Random;

namespace ZGameChess
{
    public enum CGDragOpState
    {
        CGDragOp_None = 0,
        CGDragOp_Draging = 1,
        CGDragOp_DragEnd = 2,
    }

    public struct ChessGameDOperation
    {
        public CGDragOpState CurrentState;
        public bool BAnimReq;
        public Vector3 WPostion;
    }

    public class DestMovingAnimState
    {
        public bool BMoving;
        public bool BArriving;
        public bool BFinished;
        public PlayerMoveMessage MovingType;
        public DestMovingAnimState()
        {
            BMoving = true;
            BArriving = false;
            BFinished = false;
        }
    }

    public class ChessPlayerUnitAnimBehaviour : TKBehaviour, IChessGameDragListener
    {
        public const string idle_anim = "idle";
        private const string command_start_anim = "command_start";
        private const string command_end_anim = "command_end";

        private const string finale_failed_anim = "hurt_02";
        private const string hit_anim = "hurt_01";

        public const string attack_anim = "attack";
        public const string win_anim = "win";

        public bool IsMoving { get; set; } = false;

        public bool IsArriving { get; set; } = false;
        private bool _bWillDestory = false;

        public float MSpeed { get; set; }

        public IActionState _lastFrameActState = null;

        private ChessPlayerUnit _playerUnit;
        private ActionStateController _actionStateController;
        private ChessGameDOperation _chessGameOp = new ChessGameDOperation();

        private bool m_isAlreadyHit = false;

        public bool isAlreadHit
        {
            set { m_isAlreadyHit = value; }
        }

        public bool IsInBattleAction { get; private set; } = false;
        private int _actPlayFramecount;

        private List<ICPURunableAction> _runingActLst = new List<ICPURunableAction>();

        public bool HasRunAct
        {
            get
            {
                return _runingActLst.Count > 0;
            }
        }

        private AnimEventReceiver _animEvtReceiver;
        public AnimEventReceiver AnimEvtReceiver
        {
            set
            {
                _animEvtReceiver = value;
                _animEvtReceiver.AnimEventHandler += this.AnimEventHandler;
            }
        }

        public void SetDestination()
        {
            IsArriving = false;
        }

        public void ClearDestination()
        {

        }

        //public void PlayWinAnim(bool isAttackerWin)
        //{
        //    IsInBattleAction = true;
        //    _actPlayFramecount = Time.frameCount;

        //    _playerUnit.PlayAnim(win_anim);
        //    _playerUnit.PlayWinVoice();
        //    if (isAttackerWin)
        //    {
        //        Lucifer.ActCore.BattleManager.AddAutoReleaseEffect("scene_fireworks", _playerUnit.transform.position,
        //            false, default, null, 2);
        //    }
        //}
        
        public void PlayAnim(string animName, float lockTime, System.Action callback = null)
        {
            if (_bWillDestory)
                return;

            if (_playerUnit != null)
            {
                CPUDelayAction attackReq = new CPUDelayAction(lockTime, null);

                if (_playerUnit.m_moveState != PlayerMoveMessage.JumpBack
                    && _playerUnit.m_moveState != PlayerMoveMessage.JumpTo)
                {
                    ChessBattleUnitAnimatorHandler animatorHandler = _playerUnit.animatorHandler;
                    ChessPlayerConfig config = _playerUnit.config;
                    if (animatorHandler != null)
                    {
                        float fadeTime = config != null ? config.GetCrossFadeTime(animatorHandler.CurrentAnim, animName, 0.2f) : 0.2f;
                        _playerUnit.PlayAnim(animName, AnimType.NORMAL, false, false, -0.1f, fadeTime, -1.0f, false, (System.Action)callback);
                    }

                    _runingActLst.Add(attackReq);
                }
            }
        }

        // 这里播的这个动作 不可被状态机切换打断 必须要等动作播完
        public void PlayAnim(string animName, System.Action callback = null)
        {
            if (_bWillDestory)
                return;

            if (_playerUnit != null)
            {
                CPURunablePlayAnimAction attackReq = new CPURunablePlayAnimAction(_playerUnit, animName, () =>
                {
                    if (_playerUnit != null
                     && _playerUnit.m_moveState != PlayerMoveMessage.JumpBack
                     && _playerUnit.m_moveState != PlayerMoveMessage.JumpTo)
                    {
                        if (_lastFrameActState != null)
                            _lastFrameActState.PlayAnim();
                        else
                            _playerUnit.PlayAnim(idle_anim);
                    }
                });

                if (_playerUnit.m_moveState != PlayerMoveMessage.JumpBack
                     && _playerUnit.m_moveState != PlayerMoveMessage.JumpTo)
                {
                    ChessBattleUnitAnimatorHandler animatorHandler = _playerUnit.animatorHandler;
                    ChessPlayerConfig config = _playerUnit.config;
                    if (animatorHandler != null)
                    {
                        float fadeTime = config != null ? config.GetCrossFadeTime(animatorHandler.CurrentAnim, animName, 0.2f) : 0.2f;
                        _playerUnit.PlayAnim(animName, AnimType.NORMAL, false, false, -0.1f, fadeTime, -1.0f, (System.Action)callback);
                    }

                    _runingActLst.Add(attackReq);
                }
            }
        }
        
        public void PlayCurveAction(string animName, Vector3 dir, float moveCurveTime, AnimationCurve moveCurve, float heightCurveTime, AnimationCurve heightCurve)
        {
            _playerUnit.PlayAnim(animName);

            if (heightCurve != null && heightCurveTime > 0f && _playerUnit.BodyTransform != null)
            {
                AnimCurveAction distanceAct = new AnimCurveAction(_playerUnit, Vector3.up, heightCurveTime, heightCurve, true);
                _runingActLst.Add(distanceAct);
            }

            if (moveCurve != null && moveCurveTime > 0f)
            {
                AnimCurveAction hurtAct = new AnimCurveAction(_playerUnit, dir, moveCurveTime, moveCurve, false);
                _runingActLst.Add(hurtAct);
            }
        }

        public void PlayHurtAction(bool bfinal, Vector3 attDir)
        {
            IsInBattleAction = true;
            _actPlayFramecount = Time.frameCount;
            ChessPlayerConfig config = _playerUnit.config;

            if (bfinal)
            {
                string hitAnim = _playerUnit.GetTriggerParam<string>(TriggerEnum.HitAnim);
                if (string.IsNullOrEmpty(hitAnim))
                    hitAnim = finale_failed_anim;
                if (config != null)
                    PlayCurveAction(hitAnim, attDir.normalized, config.hurtTime2, config.hurtCurve2, config.hurtTime2, config.hurtHeightCurve2);
            }
            else
            {
                _playerUnit.PlayAnim(hit_anim);

                if (!m_isAlreadyHit)
                {
                    if (config != null)
                    {
                        AnimCurveAction hurtAct = new AnimCurveAction(_playerUnit, attDir.normalized, config.hurtTime1, config.hurtCurve1, false);
                        _runingActLst.Add(hurtAct);
                    }
                    m_isAlreadyHit = true;
                }
            }
        }

        private const string baseIdleAnim = "Base Layer." + idle_anim;
        public bool IsInIdle()
        {
            if (_playerUnit == null || _playerUnit.animator == null)
                return false;

            if (_playerUnit.animator.IsInTransition(0))
                return false;
                        
            bool retb = _playerUnit.IsPlayingAnim(baseIdleAnim);
            retb |= _playerUnit.IsPlayingAnim("Base Layer.RandomRelax.relax01");
            retb |= _playerUnit.IsPlayingAnim("Base Layer.RandomRelax.relax02");
            retb |= _playerUnit.IsPlayingAnim("Base Layer.RandomRelax.relax03");

            return retb;
        }

        public bool IsInAct()
        {
            if (_playerUnit == null || _playerUnit.animator == null)
                return false;

            bool retb = _playerUnit.IsPlayingAnimStr(hit_anim);
            retb |= _playerUnit.IsPlayingAnimStr(finale_failed_anim);
            retb |= _playerUnit.IsPlayingAnimStr(attack_anim);
            retb |= _playerUnit.IsPlayingAnimStr(win_anim);

            return retb;
        }

        public void SetChessGameDragListener()
        {
            

        }

        public void Init(ChessPlayerUnit owner, ActionStateController actionStateController)
        {
            _playerUnit = owner;
            _actionStateController = actionStateController;
            SetChessGameDragListener();
        }

        public void Reset()
        {
            IsMoving = false;
            m_isAlreadyHit = false;
        }

        public void ClearAll()
        {
            Reset();

            _playerUnit = null;
            _actionStateController = null;

            _runingActLst.Clear();

            _lastFrameActState = null;
            IsInBattleAction = false;
        }

        protected override void Start()
        {
            base.Start();

            SetChessGameDragListener();
        }

        new public void OnEnable()
        {
            SetChessGameDragListener();

            ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_AckBattleField, OnAckBattleField);
        }

        new public void OnDisable()
        {
            if (ReferenceEquals(ChessBattleGlobal.Instance.ChessGameDragListener, this))
            {
                ChessBattleGlobal.Instance.ChessGameDragListener = null;
            }

            ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_AckBattleField, OnAckBattleField);

            this._runingActLst.Clear();

            _lastFrameActState = null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            _playerUnit = null;
            _bWillDestory = true;
            if (_animEvtReceiver != null)
            {
                _animEvtReceiver.AnimEventHandler -= this.AnimEventHandler;
                _animEvtReceiver = null;
            }
        }

        public void UpdateCallback()
        {
            if (_playerUnit == null)
            {
                return;
            }

            if (_playerUnit.isDead)
            {
                if (HasRunAct)
                {
                    RunAct();
                }
                return;
            }

            // 解决行走的时候受击 受击动作播放以后 不播run动作的bug
            if (_actionStateController != null && _playerUnit.PlayerData != null && _playerUnit.PlayerData.InBattle)
            {
                if (_lastFrameActState != null && _lastFrameActState.IsLoop() && IsMoving && IsInIdle())
                {
                    //Diagnostic.Log("[xiaobai]_actionStateController.CurrentState.PlayAnim");
                    //播放移动动画。
                    _chessGameOp.CurrentState = CGDragOpState.CGDragOp_None;
                    _actionStateController.CurrentState.PlayAnim();
                    _lastFrameActState = _actionStateController.CurrentState;
                }
            }
            
            bool hasAct = RunAct();
            if (hasAct)
            {
                return;
            }

            if (IsInBattleAction)
            {
                if ((Time.frameCount - _actPlayFramecount) > 2 && !IsInAct())
                {
                    IsInBattleAction = false;
                }
                return;
            }

            if (_actionStateController != null)
            {
                if (_actionStateController.CurrentState != _lastFrameActState)
                {
                    //Diagnostic.Log("[xiaobai]_actionStateController.CurrentState.PlayAnim");
                    //播放移动动画。
                    _chessGameOp.CurrentState = CGDragOpState.CGDragOp_None;
                    if(_actionStateController.CurrentState != null)
                        _actionStateController.CurrentState.PlayAnim();
                    _lastFrameActState = _actionStateController.CurrentState;
                }
            }

            IsMoving = !(_lastFrameActState is IdleAction);

            // 播放指挥动画。
            //if (!ChessModelManager.Instance.GetBattleModel().IsInBattle())
            if (!IsMoving)
            {
                if (_chessGameOp.CurrentState == CGDragOpState.CGDragOp_Draging)
                {
                    if (!_chessGameOp.BAnimReq && !IsMoving)
                    {
                        _playerUnit.PlayAnim(command_start_anim);

                        _chessGameOp.BAnimReq = true;
                    }

                    if (!IsMoving && !_playerUnit.IsPlayingAnimStr(idle_anim))
                        FaceToTarget(_chessGameOp.WPostion);
                }
                else if (_chessGameOp.CurrentState == CGDragOpState.CGDragOp_DragEnd)
                {
                    FaceToTarget(_chessGameOp.WPostion);
                    _playerUnit.PlayAnim(command_end_anim);


                    _chessGameOp.CurrentState = CGDragOpState.CGDragOp_None;
                    _chessGameOp.BAnimReq = false;
                }
            }

            //播放点击动画。
            if (_playerUnit.InputHandler != null)
            {
                if (_playerUnit.InputHandler.ClickEvtFired)
                {
                    _playerUnit.InputHandler.ClearEvent();
                }
            }
        }

        private bool RunAct()
        {
            float deltaTime = Time.deltaTime;
            for (int i = _runingActLst.Count - 1; i >= 0; i--)
            {
                ICPURunableAction runingAct = _runingActLst[i];
                runingAct.Run(deltaTime);
                if (runingAct.Ended)
                {
                    _runingActLst.RemoveAt(i);
                }
            }

            bool bAct = _runingActLst.Count > 0;
            return bAct;
        }

        private float m_lockDragTime = 0f;

        private void OnAckBattleField(GEvent e)
        {
            if (_chessGameOp.CurrentState == CGDragOpState.CGDragOp_Draging)
            {
                _chessGameOp.CurrentState = CGDragOpState.CGDragOp_DragEnd;
                m_lockDragTime = Time.time + 0.2f;
            }
            //Diagnostic.Log("[xiaobai]OnAckBattleField _chessGameOp.CurrentState: " + _chessGameOp.CurrentState);
        }

        public void OnDrag(Vector3 postion, object payload)
        {
            //if (ChessModelManager.Instance.GetBattleModel().IsInBattle())
            //    return;
            //if (/*ChessModelManager.Instance.GetBattleModel().IsInBattle() && */IsMoving)
            //    return;

            // v_yimanchen 2021.5.20 移动的时候不播指挥动作
            if (IsMoving)
                return;

            if (m_lockDragTime > Time.time)
                return; // 避免OnAckBattleField了以后马上接OnDrag，然后又不接OnDragEnd的情况

            if (_chessGameOp.CurrentState != CGDragOpState.CGDragOp_Draging && IsMoving)
            { // 让小小英雄自己停下来
                ChessOperationInput.WriteInput((byte)ChessOperationType.OPT_TINY_MOVE_SET_LOCK, 1);
            }

            if (_chessGameOp.CurrentState == CGDragOpState.CGDragOp_None)
            {
                _chessGameOp.CurrentState = CGDragOpState.CGDragOp_Draging;
            }

            if (payload == null)
            {
                var posRet = RaycastWorldPos(postion);
                if (posRet.HasValue)
                {
                    _chessGameOp.WPostion = posRet.Value;
                }
            }
            else if (payload is ChessBattleUnit)
            {
                _chessGameOp.WPostion = postion;
            }

            //Diagnostic.Log("[xiaobai]OnDrag _chessGameOp.CurrentState: " + _chessGameOp.CurrentState);
        }

        public void OnDragEnd(Vector3 postion, object payload)
        {
            if (_chessGameOp.CurrentState != CGDragOpState.CGDragOp_Draging)
            {
                return;
            }

            _chessGameOp.CurrentState = CGDragOpState.CGDragOp_DragEnd;
            if (payload == null)
            {
                var posRet = RaycastWorldPos(postion);
                if (posRet.HasValue)
                {
                    _chessGameOp.WPostion = posRet.Value;
                }
            }
            else if (payload is ChessBattleUnit)
            {
                _chessGameOp.WPostion = postion;
            }

            m_lockDragTime = Time.time + 0.2f;

            //Diagnostic.Log("[xiaobai]OnDragEnd _chessGameOp.CurrentState: " + _chessGameOp.CurrentState);
        }

        RaycastHit[] m_Results = new RaycastHit[5];
        public Vector3? RaycastWorldPos(Vector3 screenPos)
        {
            var ray = CameraUtil.ScreenPointToRay(screenPos, Camera.main);

            //【【#2620】【局外】【小小英雄】拖动装备时、不会随着装备和英雄转向而转向】
            // http://tapd.oa.com/cchess/bugtrace/bugs/view/1020417564087835419
            //for (int i = 0; i < m_Results.Length; ++i)
            //{
            //    if (m_Results[i].collider != null)
            //        m_Results[i] = new RaycastHit();
            //}

            int hitCount = Physics.RaycastNonAlloc(ray, m_Results, 500.0f, 1 << GameObjectLayer.Scene);
            Vector3? retPos = null;
            double minDist = double.MaxValue;
            //RaycastHit hit = new RaycastHit();
            //foreach (RaycastHit rhit in m_Results)
            for (int i = 0; i < hitCount; ++i)
            {
                var rhit = m_Results[i];
                if (rhit.collider == null)
                    continue;
                if (rhit.distance < minDist)
                {
                    retPos = rhit.point;
                    minDist = rhit.distance;
                    //hit = rhit;
                }
            }
            //if (retPos.HasValue)
            //    Diagnostic.Log("screenPos: " + screenPos + " pos: " + retPos.Value + " minDist: " + minDist + " hit: " + hit.collider.name);
            //else
            //    Diagnostic.Log("screenPos: " + screenPos);
            return retPos;
        }

        private void FaceToTarget(Vector3 target)
        {
            Vector3 start = this.transform.position;
            Vector3 delta = target - start;
            delta.y = 0;
            delta.Normalize();

            float angle = Vector3.Angle(Vector3.forward, delta);

            if (delta.x < 0)
                angle = -angle;

            _playerUnit.FaceTo(angle);
        }

        public void AnimEventHandler(AnimEvtParam evtParam)
        {
            for (int i = _runingActLst.Count - 1; i >= 0; i--)
            {
                ICPURunableAction runingAct = _runingActLst[i];
                runingAct.OnAnimEvent(evtParam);
            }
        }

        public bool CanPlayCommunicateAction()
        {
            if (_chessGameOp.CurrentState != CGDragOpState.CGDragOp_None)
                return false;

            if (_playerUnit.IsPlayingAnim(command_start_anim) 
            || _playerUnit.IsPlayingAnim(command_end_anim) 
            || _playerUnit.IsPlayingAnim(finale_failed_anim) 
            || _playerUnit.IsPlayingAnim(hit_anim)
            || _playerUnit.IsPlayingAnim(attack_anim) 
            || _playerUnit.IsPlayingAnim(win_anim)
            || _playerUnit.m_moveState == PlayerMoveMessage.JumpTo
            || _playerUnit.m_moveState == PlayerMoveMessage.JumpBack)
                return false;

            return true;
        }
    }
}