//#define Old_AsynAsset_Loading 

//using System;
//using System.Collections.Generic;
//using TKFrame;
//using UnityEngine;
//using ZGame.Battle;
//using ZGame.Battle.ChessVisualAction;

//namespace ZGameChess
//{
//    public  class ChessPlayerMount : ChessBattleUnit
//    {
//        public void Init()
//        {
//            if (_inited)
//                return;
            
//            _moveHandler = gameObject.AddComponent<ChessBattleUnitMoveHandler>();
//            _inited = true;
//            UpdateBody();

//            _curAngle = this.transform.rotation.eulerAngles.y;
//            Angle = _curAngle;
 
//        }
//        protected override void UpdateBody()
//        {
//            string model_path, model_name;
//            model_path = "art_mt/teamavatar/zuoqi";
//            model_name = "zuoqi@Prefab";
            
//#if Old_AsynAsset_Loading 
//            AssetLoadCallback<GameObject> cb;
//            _bodyAsset = ResourceUtil.LoadAssetSmooth<GameObject>(model_path, model_name, Callback_LoadedBodyAsset, out cb);
//            _bodyAsset.Retain();
//#else
//            m_model = new AsynGameObject(Callback_LoadedBodyAsset);
//            m_model.Load(model_path, model_name);
//#endif
//        }


//        private void Callback_LoadedBodyAsset(GameObject tplGo, string asset)
//        {
//            OnBodyLoaded(tplGo, asset);

//            Collider col = GetComponent<Collider>();
//            if (col != null)
//            {
//                col.enabled = false;
//            }
//        }

//        protected override void OnDestroy()
//        {
//            base.OnDestroy();
//            base.Release();
//        }

//		public override void ReleaseGameObj()
//		{
//			if (gameObject)
//				GameObject.Destroy(gameObject);
//		}
//	}
//}
