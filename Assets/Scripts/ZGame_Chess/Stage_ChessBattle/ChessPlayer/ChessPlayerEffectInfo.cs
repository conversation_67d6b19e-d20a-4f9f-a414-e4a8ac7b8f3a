using GfxFramework;
using Lucifer.ActCore;
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;
using ZGame.Battle;
using ZGameChess;

public class ChessPlayerEffectInfo 
{
    private long m_id;
    private string m_assetbundle = ChessConst.TEAMLEADER_EFFECT_COMMON_AB;
    private string m_effect;
    private bool m_updatePosPerFrame;       // 是否每帧更新位置
    private bool m_autoDestory;
    private Transform m_parent;
    private bool m_bindRotation = true;
    private bool m_bindScale = false;

    private Vector3 m_posOffset = Vector3.zero;
    private Vector3 m_rotOffset = Vector3.zero;

    private LoadedAsset m_loadedAsset;
    private GameObject m_effectGo;

    private bool m_destory = false;
    private bool m_cacheActive = true;

    private Callback<long> m_onDestory;

    public ChessPlayerEffectInfo(long id, GameObject go)
    {
        m_id = id;
        m_effectGo = go;
    }

    public ChessPlayerEffectInfo(long id, string effect, bool autoDestory, Transform parent, Callback<long> onDestory)
    {
        m_id = id;
        m_effect = effect;
        m_autoDestory = autoDestory;
        m_parent = parent;
        m_onDestory = onDestory;
    }
    
    public ChessPlayerEffectInfo(long id, string assetbundle, string effect, bool autoDestory, Transform parent, bool updatePosPerFrame, bool bindRotation, bool bindScale, Callback<long> onDestory)
    {
        m_id = id;
        m_assetbundle = assetbundle;
        m_effect = effect;
        m_autoDestory = autoDestory;
        m_parent = parent;
        m_updatePosPerFrame = updatePosPerFrame;
        m_onDestory = onDestory;
        m_bindRotation = bindRotation;
        m_bindScale = bindScale;
    }

    public ChessPlayerEffectInfo(long id, string assetbundle, string effect, bool autoDestory, Transform parent, bool updatePosPerFrame, Vector3 posOffset, Vector3 rotOffset, bool bindRotation, bool bindScale, Callback<long> onDestory)
    {
        m_id = id;
        m_assetbundle = assetbundle;
        m_effect = effect;
        m_autoDestory = autoDestory;
        m_parent = parent;
        m_updatePosPerFrame = updatePosPerFrame;
        m_onDestory = onDestory;
        m_bindRotation = bindRotation;
        m_bindScale = bindScale;
        m_posOffset = posOffset;
        m_rotOffset = rotOffset;
    }

    public void RunEffect(IReleaseList container)
    {
        m_loadedAsset = ResourceUtil.LoadAsset<GameObject>(m_assetbundle, m_effect, OnEffectLoaded, container);
    }

    private void OnEffectLoaded(GameObject go, string assetName)
    {
        if (m_destory)
            return;

        if (go != null)
        {
            RunEffect_Impl(go);
        }
        else
        {
            Diagnostic.Warn("effect load faild! effect ab: " + m_assetbundle + " name: " + m_effect + " id: " + m_id);
            Destory();
        }

        m_loadedAsset = null;

    }

    private void RunEffect_Impl(GameObject goTemplate)
    {
        if (goTemplate == null)
        {
            Diagnostic.Error("load effect: " + m_effect + "in ab:" + m_assetbundle + " faild!");
            return;
        }
        GameObject go = null;
        //BattlePooledEffectsContainer sp = ChessBattleGlobal.Instance.PooledEffectsContainer;
        //if (sp != null)
        //{
        //    go = sp.GetPooledEffectInst(m_effect, m_loadedAsset, null, false);
        //}
        //else
        {
            go = GameObject.Instantiate(goTemplate);
        }

        if (go != null)
        {
            var gfx = go.GetComponent<GfxRoot_Unity>();
            if (gfx != null)
            {
                if (m_autoDestory && !gfx.m_timeSystem.m_loop)
                {
                    gfx.OnGfxPlayEnd -= OnGfxPlayEnd;
                    gfx.OnGfxPlayEnd += OnGfxPlayEnd;
                }
            }
            else
            {
                Diagnostic.Warn("Effect: [{0}] miss GfxRoot_Unity!!", m_effect);
            }

            if(!m_bindRotation)
            {
                go.transform.rotation = Quaternion.Euler(m_rotOffset);;
            }
            
            if (m_updatePosPerFrame && m_parent != null)
            {
                GameEffectController gec = go.GetComponent<GameEffectController>();
                if(!m_bindRotation && gec == null)
                    gec = go.AddComponent<GameEffectController>();

                //if (gec != null)
                //{
                //    Linker_Parent linker = new Linker_Parent(gec.GoTransform, m_parent, m_posOffset, m_rotOffset, true, m_bindRotation, false, m_bindScale);
                //    linker.type = Linker_Runner_Type.LATE_UPDATE_RUNNER;
                //    gec.addLinker(linker);
                //    linker.doLink();
                //}
                //else
                {
                    go.SetActive(false);
                    go.transform.SetParent(m_parent, false);
                    ChessUtil.MakeTransformIdentity(go.transform);
                    go.SetActive(true);

                    Vector3 scale = go.transform.localToWorldMatrix.lossyScale;
                    go.transform.localScale = new Vector3(Mathf.Abs(scale.x) < 0.000001 ? 1 : 1 / scale.x,
                                                           Mathf.Abs(scale.y) < 0.000001 ? 1 : 1 / scale.y,
                                                           Mathf.Abs(scale.z) < 0.000001 ? 1 : 1 / scale.z);
                }
            }
            else if (m_parent != null)
            {
                go.transform.position = m_parent.position;
            }
        }

        m_effectGo = go;

        if (!m_cacheActive && m_effectGo)
        {
            m_effectGo.SetActive(false);
            m_cacheActive = true;
        }
    }

    private void OnGfxPlayEnd(GfxRoot gfxRoot)
    {
        Destory();

        if (gfxRoot != null)
            gfxRoot.OnGfxPlayEnd -= OnGfxPlayEnd;
    }

    public void SetActive(bool active)
    {
        if (m_effectGo != null)
            m_effectGo.SetActive(active);
        else
            m_cacheActive = active;
    }

    public void Destory()
    {
        m_destory = true;

        if (m_onDestory != null)
        {
            m_onDestory(m_id);
        }

        if (m_loadedAsset != null)
        {
            m_loadedAsset.CancelCallback<GameObject>(OnEffectLoaded);
            m_loadedAsset = null;
        }

        if (m_effectGo != null)
        {
            var gfx = m_effectGo.GetComponent<GfxRoot_Unity>();
            if (gfx != null)
                gfx.OnGfxPlayEnd = null;

            //BattlePooledEffectsContainer sp = ChessBattleGlobal.Instance.PooledEffectsContainer;
            //if (sp != null)
            //{
            //    var poolBehaviour = m_effectGo.GetComponent<EffectPoolBehaviour>();
            //    if (poolBehaviour != null)
            //        sp.ReleasePooledEffectInst(poolBehaviour);
            //    else
            //        GameObject.Destroy(m_effectGo);
            //}
            //else
            {
                GameObject.Destroy(m_effectGo);
            }
        }
    }
}
