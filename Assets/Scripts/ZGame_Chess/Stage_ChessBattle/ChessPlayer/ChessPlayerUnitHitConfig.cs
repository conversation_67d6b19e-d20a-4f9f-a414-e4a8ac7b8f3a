using System.Collections;
using System.Collections.Generic;
using UnityEngine;


/// <summary>
/// 小小英雄受击相关的默认配置
/// ID:1098     DES:小小英雄伤害飞行道具特效perfab   
/// ID:1099     DES:小小英雄伤害飞行道具命中特效perfab        sParamContent : 特效名称|特效挂点       sParam1 : 特效名称|特效挂点 (该参数为地板特效)
/// ID:1100     DES:小小英雄伤害飞行道具速度
/// ID:1101     DES:棋子伤害飞行道具特效perfab
/// ID:1102     DES:棋子伤害飞行道具命中特效perfab            sParamContent : 特效名称|特效挂点       
/// ID:1103     DES:棋子伤害飞行道具速度
/// ID:1104     DES:默认小小英雄伤害蓄力特效
/// ID:1105     DES:默认小小英雄伤害蓄力时间
/// ID:1106     DES:是否仅显示己方小小英雄受伤冒字
/// ID:1107     DES:是否局内观战显示小小英雄伤害冒字
/// </summary>
namespace ZGameChess
{
    public enum EffectHangPoint
    {
        Head = 0,
        Body = 1,
        Ground = 2
    }

    public class ChessPlayerUnitHitConfig
    {
        // 小队长飞行道具相关
        public string flyEffect = "scene_hero_bullet";  // 小小英雄伤害飞行道具Prefab
        public string hitEffect = "scene_hero_bullethit"; // 小小英雄飞行道具命中特效Prefab
        public string hitEffectGround = "scene_hero_bullethit_ground"; // 小小英雄飞行道具命中后的地板特效Prefab

        // 场上英雄飞行道具相关
        public string heroFlyEffect = "scene_bullet"; // 棋子伤害飞行道具特效perfab
        public string heroHitEffect = "scene_bullethit"; // 棋子伤害飞行道具命中特效perfab    

        public string heroRefreshShop = "effect_teamleader_refreshshop";//刷新商店时候小小英雄播放特效prefab

        // 飞行道具速度
        public float flySpeed = 14f;  
        public float heroFlySpeed = 11f;

        // 小小英雄蓄力相关、
        public string playerCastEffect = ""; // 蓄力特效
        public float playerCastTime = 0.7f;  // 蓄力时长

        public bool showBothHitCount = false;
        public bool showWatchHitCount = false;

        public string abPath = "art_tft_raw/effects/scene_effect";

        // 命中特效挂点相关
        public EffectHangPoint hitEffectHangPoint = EffectHangPoint.Body;
        public EffectHangPoint hitEffectGroundHangPoint = EffectHangPoint.Ground;
        public EffectHangPoint heroHitEffectHangPoint = EffectHangPoint.Body;
        public void InitConfig()
        {
            
            
            flyEffect = GameDataMng.Instance.GetFlyEffect();
            hitEffect =  GameDataMng.Instance.GetHitEffect();
            hitEffectHangPoint = GetHangPointByInt( GameDataMng.Instance.GetHitEffectHangPoint());
            hitEffectGround =  GameDataMng.Instance.GetHitEffectGround();
            hitEffectGroundHangPoint = GetHangPointByInt(GameDataMng.Instance.GetHitEffectGroundHangPoint());
            flySpeed = GameDataMng.Instance.GetFlySpeed();
            heroFlyEffect = GameDataMng.Instance.GetHeroFlyEffect();
            heroHitEffect = GameDataMng.Instance.GetHeroHitEffect();
            heroHitEffectHangPoint = GetHangPointByInt(GameDataMng.Instance.GetHeroHitEffectHangPoint());
            heroFlySpeed = GameDataMng.Instance.GetHeroFlySpeed();
            playerCastEffect = GameDataMng.Instance.GetPlayerCastEffect();
            playerCastTime = GameDataMng.Instance.GetPlayerCastTime();
            showBothHitCount = GameDataMng.Instance.GetShowBothHitCount();
            showWatchHitCount = GameDataMng.Instance.GetShowWatchHitCount();
        }

        EffectHangPoint GetHangPointByInt(int id)
        {
            switch(id)
            {
                case 1:return EffectHangPoint.Body;
                case 2:return EffectHangPoint.Ground;
                default:return EffectHangPoint.Body;
            }
        }
    }
}

