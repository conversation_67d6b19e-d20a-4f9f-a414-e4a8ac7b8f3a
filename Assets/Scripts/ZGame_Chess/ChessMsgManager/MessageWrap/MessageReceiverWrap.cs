using MiniGameClientProto;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using TKFrame.Utility;
using Wup.Jce;
using ZGameClient;

namespace ZGameChess
{
    // 接收者和传播者是一体的
    public class MessageReceiverWrap<TRsp> : IMessageReceiver, IMessageDispatcher<TRsp>, IMessageDispatcherCount where TRsp : JceStruct, new()
    {
        private E_SVR_MSG_ID _msgId;
        //private Type _rspType;
        private event Action<TRsp> _onRspFunc;

        private int _resFuncEventCount = 0;

        private ObjectPool<TRsp> _rspMsgPool;

        public E_SVR_MSG_ID GetMsgID()
        {
            return _msgId;
        }

        public MessageReceiverWrap(E_SVR_MSG_ID msgId, Action<TRsp> onRspFunc)
        {
            _msgId = msgId;
            //_rspType = typeof(TRsp);
            _rspMsgPool = new ObjectPool<TRsp>(null, null);

            AddRspEvent(onRspFunc);
        }

        public void AddRspEvent(Action<TRsp> onRspFunc)
        {
            _onRspFunc += onRspFunc;
            ++_resFuncEventCount;
        }

        public int GetResFuncEventCount()
        {
            return _resFuncEventCount;
        }

        public JceStruct OnResMessage(MemoryStream data)
        {
            var rsp = _rspMsgPool.Get();
            if (rsp != null)
            {
                data.Position = 0;
                //ProtoUtil.MemoryStreamToJceStruct(data, rsp);
                
                if (_onRspFunc != null)
                {
                    _onRspFunc(rsp);
                }
            }
            _rspMsgPool.Release(rsp);
            return rsp;     //所以这里不保证外界使用到的这个rsp是不是已经被复用了 标准的做法是在_onRspFunc去使用
        }

        public void RemoveRspEvent(Action<TRsp> onRspFunc)
        {
            _onRspFunc -= onRspFunc;
            --_resFuncEventCount;
        }
    }
}
