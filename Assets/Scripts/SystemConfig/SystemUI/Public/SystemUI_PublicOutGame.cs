using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace SystemConfigDll
{
    public class SystemUI_PublicOutGame
    {
        //通过代码加载的AB
        public static readonly string chessart__ui__arena__chesscommonfullscreentips = "chessart/ui/arena/chesscommonfullscreentips";
        public static readonly string prefab__ui__policy_panel = "prefab/ui/policy_panel";
        public static readonly string scene__loading = "scene/loading";
        public static readonly string chessart__ui__login__loginbg = "chessart/ui/login/loginbg";
        public static readonly string prefab__ui__logo_panel = "prefab/ui/logo_panel";
        public static readonly string prefab__ui__gcloudvoice = "prefab/ui/gcloudvoice";
        public static readonly string scene__version_update = "scene/version_update";
        public static readonly string prefab__ui__update__updatestage = "prefab/ui/update/updatestage";
        public static readonly string prefab__ui__checkasset__checkasset_panel = "prefab/ui/checkasset/checkasset_panel";
        public static readonly string dynamicassets__prefab__ui__update__cosupdatestage = "dynamicassets/prefab/ui/update/cosupdatestage";
        public static readonly string dynamicassets__prefab__ui__update__gameinitstage = "dynamicassets/prefab/ui/update/gameinitstage";
        public static readonly string chessart__ui__dialog__dialog_confirm_cancel_miniversionupdate = "chessart/ui/dialog/dialog_confirm_cancel_miniversionupdate";
        public static readonly string scene__login = "scene/login";
        public static readonly string prefab__ui__login__login_panel = "prefab/ui/login/login_panel";
        public static readonly string prefab__ui__login__login_panel_oversea = "prefab/ui/login/login_panel_oversea";
        public static readonly string prefab__ui__serverlist__serverlist_panel = "prefab/ui/serverlist/serverlist_panel";
        public static readonly string prefab__ui__serverlist__serverlist_panel_oversea = "prefab/ui/serverlist/serverlist_panel_oversea";
        public static readonly string prefab__ui__serverlist__serverlist = "prefab/ui/serverlist/serverlist";
        public static readonly string prefab__ui__serverlist__serverlist_item = "prefab/ui/serverlist/serverlist_item";
        public static readonly string chessart__ui__mainhall__chessinputnamepanel = "chessart/ui/mainhall/chessinputnamepanel";
        public static readonly string chessart__ui__setting__privacysettingconfirmdialog = "chessart/ui/setting/privacysettingconfirmdialog";
        public static readonly string chessart__ui__common = "chessart/ui/common";
        public static readonly string chessart__ui__common__tabprefab = "chessart/ui/common/tabprefab";
        public static readonly string chessart__uiatlas__emojiv2 = "chessart/uiatlas/emojiv2";
        public static readonly string chessart__ui__multilingualpanel__national_options__national_options_tip = "chessart/ui/multilingualpanel/national_options/national_options_tip";
        public static readonly string prefab__ui__update__dialog_cosresdownload = "prefab/ui/update/dialog_cosresdownload";

        //依赖太多需要主动进包的AB
        public static readonly string chessart__uiatlas__l_login = "chessart/uiatlas/l_login";
        public static readonly string chessart__uiatlas__l_policy = "chessart/uiatlas/l_policy";
        public static readonly string chessart__currencyicon = "chessart/currencyicon";
        public static readonly string chessart__rankicon = "chessart/rankicon";
        public static readonly string chessart__tinyactionicon = "chessart/tinyactionicon";
        public static readonly string chessart__tinyloadicon = "chessart/tinyloadicon";
    }
}
