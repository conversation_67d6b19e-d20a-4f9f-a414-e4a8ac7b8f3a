using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace SystemConfigDll
{
    public class SystemUI_PublicInGame
    {
        //通过代码加载的AB
        public static readonly string scene__tft_battle_scene = "scene/tft_battle_scene";
        public static readonly string prefab__ui__battleunitinfo = "prefab/ui/battleunitinfo";
        public static readonly string chessart__ui__battle__battleplayerinfo = "chessart/ui/battle/battleplayerinfo";
        public static readonly string chessart__ui__battle__battleplayerhpimgs = "chessart/ui/battle/battleplayerhpimgs";
        public static readonly string chessart__ui__battle__herofightcountcanvas = "chessart/ui/battle/herofightcountcanvas";
        public static readonly string prefab__ui__battle__roundtips = "prefab/ui/battle/roundtips";
        public static readonly string chessart__ui__battle__surplusherotips = "chessart/ui/battle/surplusherotips";
        public static readonly string chessart__ui__battle__tomeofemblemstips = "chessart/ui/battle/tomeofemblemstips";
        public static readonly string chessart__ui__battle__herobuffpanel = "chessart/ui/battle/herobuffpanel";//UI分包需要拆分
        public static readonly string chessart__ui__setting__setingingamepanel = "chessart/ui/setting/setingingamepanel";//UI分包需要拆分
        public static readonly string prefab__ui__battle_panel = "prefab/ui/battle_panel";//UI分包需要拆分
        public static readonly string chessart__ui__heroinfo__heroinfopanel = "chessart/ui/heroinfo/heroinfopanel";
        public static readonly string chessart__ui__heroinfo__commtips = "chessart/ui/heroinfo/commtips";
        public static readonly string chessart__ui__battle__roundselectunitinfo = "chessart/ui/battle/roundselectunitinfo";
        public static readonly string chessart__ui__battle__outfieldunitinfo = "chessart/ui/battle/outfieldunitinfo";
        public static readonly string chessart__ui__battle__herosellareanew = "chessart/ui/battle/herosellareanew";
        public static readonly string chessart__ui__buyhero__buyheroview = "chessart/ui/buyhero/buyheroview";//UI分包需要拆分
        public static readonly string chessart__ui__buyhero__button = "chessart/ui/buyhero/button";//UI分包需要拆分
        public static readonly string chessart__ui__joystick__btnjoystick = "chessart/ui/joystick/btnjoystick";
        public static readonly string chessart__ui__battle__equipmentdressinfopanel = "chessart/ui/battle/equipmentdressinfopanel";//UI分包需要拆分
        public static readonly string chessart__ui__battle__battlebroadcast__battlebroadcastidmapping = "chessart/ui/battle/battlebroadcast/battlebroadcastidmapping";
        public static readonly string chessart__ui__battle__battlebroadcast__broadcasttext_map = "chessart/ui/battle/battlebroadcast/broadcasttext_map";
        public static readonly string chessart__ui__battle__animationbroadcast__animationbroadcast_map_allset = "chessart/ui/battle/animationbroadcast/animationbroadcast_map_allset";//UI分包需要拆分
        public static readonly string chessart__ui__battle__animationbroadcast__animationbroadcast_text = "chessart/ui/battle/animationbroadcast/animationbroadcast_text";//UI分包需要拆分 需要LQA
        public static readonly string chessart__ui__battle__battleunitinfo_equipment = "chessart/ui/battle/battleunitinfo_equipment";
        public static readonly string chessart__ui__battle__equipmentdragitem = "chessart/ui/battle/equipmentdragitem";
        public static readonly string chessart__ui__battle__battlebroadcast__battlebroadcast_abmap = "chessart/ui/battle/battlebroadcast/battlebroadcast_abmap";
        public static readonly string chessart__ui__battle__battlebroadcast__battlebroadcast_basev2_modian_hero = "chessart/ui/battle/battlebroadcast/battlebroadcast_basev2_modian_hero";
        public static readonly string chessart__ui__dialog__battle_mini_tips_red = "chessart/ui/dialog/battle_mini_tips_red";
        public static readonly string chessart__ui__dialog__battle_mini_tips_gray = "chessart/ui/dialog/battle_mini_tips_gray";
        public static readonly string chessart__ui__battle__tips__conwintips = "chessart/ui/battle/tips/conwintips";
        public static readonly string chessart__ui__battle__tips__conlosetips = "chessart/ui/battle/tips/conlosetips";
        public static readonly string chessart__ui__battle__tips__battleeventtips = "chessart/ui/battle/tips/battleeventtips";
        public static readonly string chessart__ui__battle__tips__battleopentips = "chessart/ui/battle/tips/battleopeningtips";
        public static readonly string chessart__ui__battle__population_alpha = "chessart/ui/battle/population_alpha";
        public static readonly string chessart__ui__battle__population_blue = "chessart/ui/battle/population_blue";
        public static readonly string chessart__ui__battle__population_red = "chessart/ui/battle/population_red";
        public static readonly string chessart__ui__battle__equipmenttips = "chessart/ui/battle/equipmenttips";
        public static readonly string chessart__ui__battle__equipmentcomposetips = "chessart/ui/battle/equipmentcomposetips";
        public static readonly string chessart__ui__heroinfo__heroinfofetterpanel = "chessart/ui/heroinfo/heroinfofetterpanel";
        public static readonly string chessart__ui__battle__tips__fetterinfotips = "chessart/ui/battle/tips/fetterinfotips";
        public static readonly string chessart__ui__battle__tips__battleitemtips = "chessart/ui/battle/tips/battleitemtips";
        public static readonly string chessart__uiatlas__l_ingame1 = "chessart/uiatlas/l_ingame1";
        public static readonly string chessart__ui__battle__battleplayerend = "chessart/ui/battle/battleplayerend";//UI分包需要拆分

        //依赖太多需要主动进包的AB
        public static readonly string chessart__uiatlas__l_ingame_chosen = "chessart/uiatlas/l_ingame_chosen";
        public static readonly string chessart__icon = "chessart/icon";
    }
}
