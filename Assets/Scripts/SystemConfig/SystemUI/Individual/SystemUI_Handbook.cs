using System.Collections;
using System.Collections.Generic;

namespace SystemConfigDll
{
    public class SystemUI_Handbook
    {
        //通过代码加载的AB
        public static readonly string prefab__ui__battlepass__chessbattlepass = "prefab/ui/battlepass/chessbattlepass";//done
        public static readonly string prefab__ui__battlepass__bptaskpanel = "prefab/ui/battlepass/bptaskpanel";//done
        public static readonly string chessart__ui__activity__luxurybookbanner__luxurybookbanner = "chessart/ui/activity/luxurybookbanner/luxurybookbanner";//done
        public static readonly string chessart__ui__t_battlepass__dialog_bpupdatepreview = "chessart/ui/t_battlepass/dialog_bpupdatepreview";//done
        public static readonly string chessart__ui__t_battlepass__zhizhenexchangepanel = "chessart/ui/t_battlepass/zhizhenexchangepanel";//done
        public static readonly string chessart__ui__t_battlepass__passcheckupgradepanel = "chessart/ui/t_battlepass/passcheckupgradepanel";//done
        public static readonly string chessart__ui__t_battlepass__bpupgradpanel = "chessart/ui/t_battlepass/bpupgradpanel";//done
        public static readonly string chessart__ui__t_battlepass__bpobtainpanel2 = "chessart/ui/t_battlepass/bpobtainpanel2";//done
        public static readonly string chessart__ui__t_battlepass__dialog_zhizhenexchange = "chessart/ui/t_battlepass/dialog_zhizhenexchange";//done
        public static readonly string chessart__ui__t_battlepass__bpbattleendrewardpanel = "chessart/ui/t_battlepass/bpbattleendrewardpanel";//done
        public static readonly string chessart__ui__t_battlepass__parallelbpticketpanel = "chessart/ui/t_battlepass/parallelbpticketpanel";//done
        public static readonly string prefab__ui__activity__galacticbattlepass__galacticbptaskpanel = "chessart/ui/galacticbattlepass/galacticbptaskpanel";//done
        public static readonly string chessart__ui__t_battlepass__parallelbpbuyleveldialog = "chessart/ui/t_battlepass/parallelbpbuyleveldialog";//done
        public static readonly string chessart__ui__t_battlepass__parallelbplevelupdialog = "chessart/ui/t_battlepass/parallelbplevelupdialog";//done

        //依赖太多需要主动进包的AB
        public static readonly string chessart__uiatlas__l_bp = "chessart/uiatlas/l_bp";
        public static readonly string chessart__uiatlas__l_bpendcount = "chessart/uiatlas/l_bpendcount";
    }
}
