using System.Collections;
using System.Collections.Generic;

namespace SystemConfigDll
{
    public class SystemUI_Collection
    {
        public static readonly string chessart__ui__warehouse__warehousepanel = "chessart/ui/warehouse/warehousepanel";
        public static readonly string chessart__ui__warehouse__warehousedetailpanel = "chessart/ui/warehouse/warehousedetailpanel";
        public static readonly string chessart__ui__warehouse__warehousegoodsfetterpanel = "chessart/ui/warehouse/warehousegoodsfetterpanel";
        public static readonly string chessart__ui__warehouse__warehousecustomizemappanel = "chessart/ui/warehouse/warehousecustomizemappanel";
        public static readonly string chessart__ui__warehouse__warehousesimplerespondpanel = "chessart/ui/warehouse/warehousesimplerespondpanel";
        public static readonly string chessart__ui__warehouse__expressionpanel = "chessart/ui/warehouse/expressionpanel";
        public static readonly string chessart__ui__warehouse__dialog_graffitipreview = "chessart/ui/warehouse/dialog_graffitipreview";
        public static readonly string chessart__ui__warehouse__warehousechatbubblelistpanel = "chessart/ui/warehouse/warehousechatbubblelistpanel";
        public static readonly string chessart__ui__warehouse__dialog_personalitybuttonpreview = "chessart/ui/warehouse/dialog_personalitybuttonpreview";
        public static readonly string chessart__ui__warehouse__personalitybutton__normal__personalitydialog = "chessart/ui/warehouse/personalitybutton/normal/personalitydialog";
        public static readonly string chessart__ui__warehouse__personalitybutton__ipad__personalitydialog_ipad = "chessart/ui/warehouse/personalitybutton/ipad/personalitydialog_ipad";
        public static readonly string chessart__ui__warehouse__goodssortlistpanel = "chessart/ui/warehouse/goodssortlistpanel";
        public static readonly string chessart__ui__warehouse__dialog_risingstarall = "chessart/ui/warehouse/dialog_risingstarall";
        public static readonly string chessart__ui__warehouse__goodssortlcollectpanel = "chessart/ui/warehouse/goodssortlcollectpanel";
        public static readonly string chessart__ui__warehouse__tinysharepanel = "chessart/ui/warehouse/tinysharepanel";
        public static readonly string chessart__ui__dialog__dialog_tinystory = "chessart/ui/dialog/dialog_tinystory";
        public static readonly string chessart__ui__chessshare__chesssharefetter = "chessart/ui/chessshare/chesssharefetter";
        public static readonly string chessart__uiatlas__l_warehouse = "chessart/uiatlas/l_warehouse";
    }
}
