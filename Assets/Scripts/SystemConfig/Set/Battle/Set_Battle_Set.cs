using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace SystemConfigDll
{
    public class Set_Battle_Set
    {
        public static readonly string art_tft_raw__scenes__prefab__s_tft_battlefield_center = "art_tft_raw/scenes/prefab/s{setid}_tft_battlefield_center";
        public static readonly string chessart__ui__battle__animationbroadcast__animationbroadcast_config_set = "chessart/ui/battle/animationbroadcast/animationbroadcast_config_set{setid}";
        public static readonly string art_tft_raw__effects__battle__set__set  = "art_tft_raw/effects/battle/set/set{setid}";
        public static readonly string art_tft_raw__effects__battle__set__set_config = "art_tft_raw/effects/battle/set/set{setid}_config";
        public static readonly string art_tft_raw__effects__battle__gameplay__galaxy__portals__set = "art_tft_raw/effects/battle/gameplay/galaxy/portals/set{setid}";
        public static readonly string art_tft_raw__effects__battle__gameplay__galaxy__portals__set_config = "art_tft_raw/effects/battle/gameplay/galaxy/portals/set{setid}_config";
        public static readonly string art_tft_raw__ui__tft_set__tft_skill = "art_tft_raw/ui/tft_set{setid}/tft_skill";
        public static readonly string art_tft_raw__ui__tft_set__tft_hero_headicon = "art_tft_raw/ui/tft_set{setid}/tft_hero_headicon";
        public static readonly string art_tft_raw__ui__tft_set__tft_hero = "art_tft_raw/ui/tft_set{setid}/tft_hero";
        public static readonly string art_tft_raw__ui__tft_set__tft_fettericon = "art_tft_raw/ui/tft_set{setid}/tft_fettericon";
        public static readonly string art_tft_raw__ui__tft_set__tft_areaicon_pic = "art_tft_raw/ui/tft_set{setid}/tft_areaicon_pic";
        public static readonly string art_tft_raw__ui__tft_set__tft_areaicon_small = "art_tft_raw/ui/tft_set{setid}/tft_areaicon_small";
        public static readonly string art_tft_raw__ui__tft_set__tft_areaicon_smallpic = "art_tft_raw/ui/tft_set{setid}/tft_areaicon_smallpic";
        public static readonly string art_tft_raw__ui__tft_set__tft_hexgonstrengthen = "art_tft_raw/ui/tft_set{setid}/tft_hexgonstrengthen";
        public static readonly string art_tft_raw__ui__tft_set__tft_hexgonstrengthen_small = "art_tft_raw/ui/tft_set{setid}/tft_hexgonstrengthen_small";
        public static readonly string chessart__ui__battle__hexgonstrengthen_s = "chessart/ui/battle/set{setid}/normal/hexgonstrengthen";
        public static readonly string chessart__ui__battle__ipad__hexgonstrengthenpanel_ipad_s = "chessart/ui/battle/set{setid}/ipad/hexgonstrengthen";
        public static readonly string art_tft_raw__ui__tft_set__tft_galaxy = "chessart/ui/battle/set{setid}/normal/galaxy";
        public static readonly string art_tft_raw__ui__tft_set__tft_equipicon = "art_tft_raw/ui/tft_set{setid}/tft_equipicon";
        public static readonly string art_tft_raw__hero__hero_show = "art_tft_raw/hero/hero_show/set{setid}/*_show";
        public static readonly string art_tft_raw__hero__hero_show__sealow__res = "art_tft_raw/hero/hero_show/set{setid}/*_show_sealow_res";
        public static readonly string art_tft_raw__hero__hero_show__low__res = "art_tft_raw/hero/hero_show/set{setid}/*_show_low_res";
        public static readonly string art_tft_raw__hero__hero_show__middle__res = "art_tft_raw/hero/hero_show/set{setid}/*_show_middle_res";
        public static readonly string art_tft_raw__hero__hero_show__high__res = "art_tft_raw/hero/hero_show/set{setid}/*_show_high_res";
        public static readonly string art_tft_raw__cfg__per_hero_mat_cfg_set = "art_tft_raw/cfg/per_hero_mat_cfg/set{setid}";
        public static readonly string art_tft_raw__cfg__hero_material_cfg_overrides = "art_tft_raw/cfg/hero_material_cfg_overrides/set{setid}";
        public static readonly string art_tft_raw__cfg__per_hero_mat_cfg = "art_tft_raw/cfg/per_hero_mat_cfg";
        public static readonly string chessart__ui__battle__set__normal__chosen = "chessart/ui/battle/set{setid}/normal/chosen";
        public static readonly string chessart__ui__battle__set__normal__eshop = "chessart/ui/battle/set{setid}/normal/eshop";
        public static readonly string chessart__ui__battle__set__ipad__eshop = "chessart/ui/battle/set{setid}/ipad/eshop";
        public static readonly string art_tft_raw__model_res__hero__set = "art_tft_raw/model_res/hero/set{setid}/*";
        public static readonly string art_tft_raw__model_res__hero__set_skin = "art_tft_raw/model_res/hero/set{setid}_skin/*";
        public static readonly string art_tft_raw__model_res__hero__set__texture = "art_tft_raw/model_res/hero/set{setid}/texture/*/lv1/materials/textures";
        public static readonly string art_tft_raw__ui__tft_set__tft_hero_broadcastex = "art_tft_raw/ui/tft_set{setid}/tft_hero_broadcastex/*";

        #region 根据Set获取路径 请按照规范写 方法名是get_路径

        public static string get_art_tft_raw__ui__tft_set__tft_hero_broadcastex(int setId, string heroName)
        {
            return AssetbundleNameCache.get_ab_name(AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_hero_broadcastex, setId), new KeyValuePair<string, string>("*", heroName));
        }

        public static string get_art_tft_raw__cfg__per_hero_mat_cfg(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__cfg__per_hero_mat_cfg_set, setId);
        }

        public static string get_art_tft_raw__cfg__hero_material_cfg_overrides(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__cfg__hero_material_cfg_overrides, setId);
        }

        public static string get_art_tft_raw__hero__hero_show(int setId, string heroName)
        {
            return AssetbundleNameCache.get_ab_name(AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__scenes__prefab__s_tft_battlefield_center, setId), new KeyValuePair<string, string>("*_show", heroName));
        }

        public static string get_art_tft_raw__scenes__prefab__s_tft_battlefield_center(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__scenes__prefab__s_tft_battlefield_center, setId);
        }

        public static string get_chessart__ui__battle__animationbroadcast__animationbroadcast_config_set(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.chessart__ui__battle__animationbroadcast__animationbroadcast_config_set, setId);
        }

        public static string get_art_tft_raw__effects__battle__set__set(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__effects__battle__set__set, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_skill(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_skill, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_hero_headicon(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_hero_headicon, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_hero(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_hero, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_fettericon(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_fettericon, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_areaicon_pic(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_areaicon_pic, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_areaicon_small(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_areaicon_small, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_areaicon_smallpic(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_areaicon_smallpic, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_hexgonstrengthen(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_hexgonstrengthen, setId);
        }
        
        public static string get_art_tft_raw__ui__tft_set__tft_galaxy(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_galaxy, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_hexgonstrengthen_small(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_hexgonstrengthen_small, setId);
        }

        public static string get_chessart__ui__battle__hexgonstrengthen_s(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.chessart__ui__battle__hexgonstrengthen_s, setId);
        }

        public static string get_chessart__ui__battle__ipad__hexgonstrengthenpanel_ipad_s(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.chessart__ui__battle__ipad__hexgonstrengthenpanel_ipad_s, setId);
        }

        public static string get_art_tft_raw__ui__tft_set__tft_equipicon(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__ui__tft_set__tft_equipicon, setId);
        }

        public static string get_art_tft_raw__effects__battle__gameplay__galaxy__portals__set(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__effects__battle__gameplay__galaxy__portals__set, setId);
        }

        public static string get_art_tft_raw__effects__battle__gameplay__galaxy__portals__set_config(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.art_tft_raw__effects__battle__gameplay__galaxy__portals__set_config, setId);
        }

        public static string get_chessart_ui_battle_set_normal_chosen(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.chessart__ui__battle__set__normal__chosen, setId);
        }
        public static string get_chessart_ui_battle_set_normal_eshop(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.chessart__ui__battle__set__normal__eshop, setId);
        }
        public static string get_chessart_ui_battle_set_ipad_eshop(int setId)
        {
            return AssetbundleNameCache.get_ab_name(Set_Battle_Set.chessart__ui__battle__set__ipad__eshop, setId);
        }
        #endregion
    }
}
