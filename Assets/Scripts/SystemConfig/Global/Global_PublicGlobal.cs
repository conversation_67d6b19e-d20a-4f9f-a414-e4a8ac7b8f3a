using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace SystemConfigDll
{
    public class Global_PublicGlobal
    {
        public static readonly string shaders__based_shaders = "shaders/based_shaders";
        public static readonly string shaders__dynamicshadow = "shaders/dynamicshadow";
        public static readonly string shaders__decalres = "shaders/decalres";
        public static readonly string shaders__pps_shaders = "shaders/pps_shaders";
        public static readonly string prefab__statemachine__net_state_machine = "prefab/statemachine/net_state_machine";
        public static readonly string art_tft_raw__little_legend_res__cos_littlelegend_cfg = "art_tft_raw/little_legend_res/cos_littlelegend_cfg";
        public static readonly string art_tft_raw__cfg__effect_profiler_cfg = "art_tft_raw/cfg/effect_profiler_cfg";
        public static readonly string art_tft_raw__cfg__res_cfg__package = "art_tft_raw/cfg/res_cfg/package";
    }
}
