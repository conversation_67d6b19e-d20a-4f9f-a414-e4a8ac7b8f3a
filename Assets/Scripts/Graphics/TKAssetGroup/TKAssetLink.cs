using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TKFrame.Asset
{
    public enum TKAssetLinkType
    {
        Mesh,
        SkinnedMesh,
        Material,
        AnimationController,
        Avatar,
        SpriteMask,
        <PERSON>prite<PERSON><PERSON>er,
        Animation_Main,
        Animation_State_Arr,
        DirectGPUSkinnedMesh,
        RawImage_Texture,
        RawImage_Material,
    }

    [Serializable]
    public class TKAssetLink
    {
        [SerializeField] private string m_path;

        [SerializeField] private TKAssetLinkType m_linkType;
        
        [SerializeField] private UnityEngine.Object m_asset;

        public string path
        {
            get { return m_path; }
        }
        
        public TKAssetLinkType linkType
        {
            get { return m_linkType; }
        }
        
        public UnityEngine.Object asset
        {
            get { return m_asset; }
        }

        /// <summary>
        /// 额外的数据，例如material的index
        /// </summary>
        [SerializeField] private int m_extraInt;

        private RecoverType m_recoverType = RecoverType.overlay;

        public TKAssetLink(string path, TKAssetLinkType type, UnityEngine.Object asset)
        {
            m_path = path;
            m_linkType = type;
            m_asset = asset;
        }

        public void SetExtraInt(int extraInt)
        {
            m_extraInt = extraInt;
        }
        
        /**
         * 恢复LOD资源;
         *
         * 分离的步骤查看TKAssetMap.GenLod_Base
         */
        public void RecoverLodAsset(Transform root, List<string> erroList, ref TKAssetMapRecoverCacher assetMapRecoverCacher, RecoverType recoverType = RecoverType.overlay)
        {
            if (!root)
            {
                return;
            }

            m_recoverType = recoverType;
            var findedRefHoster = FindedRefHoster(root, this.m_path);

            if (findedRefHoster)
            {
                if (m_asset)
                {
                    switch (m_linkType)
                    {
                        case TKAssetLinkType.Mesh:
                        {
                            Recover_Mesh(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.SkinnedMesh:
                        {
                            Recover_SkinnedMesh(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.DirectGPUSkinnedMesh:
                        {
                            Recover_DirectGPUSkinnedMesh(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.Material:
                        {
                            Recover_Material(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.AnimationController:
                        {
                            Recover_AnimationController(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.Avatar:
                        {
                            Recover_Avatar(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.SpriteMask:
                        {
                            Recover_SpriteMask(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.SpriteRenderer:
                        {
                            Recover_SpriteRendererr(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.Animation_Main:
                        {
                            Recover_Animation_Main(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.Animation_State_Arr:
                        {
                            Recover_Animation_State_Arr(erroList, findedRefHoster, ref assetMapRecoverCacher);
                            break;
                        }
                        case TKAssetLinkType.RawImage_Texture:
                        {
                            Recover_RawImage_Texture(erroList, findedRefHoster);
                            break;
                        }
                        case TKAssetLinkType.RawImage_Material:
                        {
                            Recover_RawImage_Material(erroList, findedRefHoster);
                            break;
                        }
                    }
                }
            }
            else
            {
                erroList.Add(string.Format("找不到对应的Transform: path={0}", m_path));
            }
        }

        public static Transform FindedRefHoster(Transform root, string childPath)
        {
            Transform findedRefHoster = null;
            if (childPath.Equals(string.Empty))
            {
                findedRefHoster = root;
            }
            else
            {
                findedRefHoster = root.Find(childPath);
            }

            return findedRefHoster;
        }

        private void Recover_Animation_State_Arr(List<string> erroList, Transform findedRefHoster, ref TKAssetMapRecoverCacher assetMapRecoverCacher)
        {
            if (assetMapRecoverCacher == null)
            {
                assetMapRecoverCacher = new TKAssetMapRecoverCacher();
                assetMapRecoverCacher.Animation_State_Arr_ClipDic = new Dictionary<string, List<AnimationClip>>();
            }

            List<AnimationClip> clipList = null;
            if (!assetMapRecoverCacher.Animation_State_Arr_ClipDic.ContainsKey(this.m_path))
            {
                assetMapRecoverCacher.Animation_State_Arr_ClipDic.Add(this.m_path, new List<AnimationClip>());
            }
            clipList = assetMapRecoverCacher.Animation_State_Arr_ClipDic[this.m_path];
            var animationComp = findedRefHoster.GetComponent<Animation>();
            if (animationComp)
            {
                if (m_extraInt >= clipList.Count)
                {
                    for (int k = clipList.Count; k < m_extraInt + 1; k++)
                    {
                        clipList.Add(null);
                    }
                }
                clipList[m_extraInt] = (AnimationClip)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的Animation组件: path={0}", m_path));
            }
        }
        
        private void Recover_RawImage_Texture(List<string> erroList, Transform findedRefHoster)
        {
            var rawImage = findedRefHoster.GetComponent<RawImage>();
            if (rawImage)
            {
                if (isNeedRecoverValue(rawImage.texture))
                {
                    rawImage.texture = (Texture)m_asset;   
                }
            }
            else
            {
                erroList.Add(string.Format("找不到对应的RawImage组件: path={0}", m_path));
            }
        }

        private void Recover_RawImage_Material(List<string> erroList, Transform findedRefHoster)
        {
            var rawImage = findedRefHoster.GetComponent<RawImage>();
            if (rawImage)
            {
                if (rawImage.material == rawImage.defaultMaterial || isNeedRecoverValue(rawImage.material))
                {
                    rawImage.material = (Material)m_asset;   
                }
            }
            else
            {
                erroList.Add(string.Format("找不到对应的RawImage组件: path={0}", m_path));
            }
        }
        
        private bool isNeedRecoverValue(UnityEngine.Object preObjValue)
        {
            switch (m_recoverType)
            {
                case RecoverType.overlay:
                {
                    return true;
                }
                case RecoverType.reserve_res:
                {
                    if (preObjValue)
                    {
                        return false;
                    }
                    return true;
                }
            }
            return true;
        }

        private void Recover_Animation_Main(List<string> erroList, Transform findedRefHoster)
        {
            var animationComp = findedRefHoster.GetComponent<Animation>();
            if (animationComp)
            {
                animationComp.clip = (AnimationClip)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的Animation组件: path={0}", m_path));
            }
        }

        private void Recover_SpriteRendererr(List<string> erroList, Transform findedRefHoster)
        {
            var spriteRenderer = findedRefHoster.GetComponent<SpriteRenderer>();
            if (spriteRenderer)
            {
                spriteRenderer.sprite = (Sprite)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的SpriteRenderer组件: path={0}", m_path));
            }
        }

        private void Recover_SpriteMask(List<string> erroList, Transform findedRefHoster)
        {
            var spriteMask = findedRefHoster.GetComponent<SpriteMask>();
            if (spriteMask)
            {
                spriteMask.sprite = (Sprite)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的SpriteMask组件: path={0}", m_path));
            }
        }

        private void Recover_Avatar(List<string> erroList, Transform findedRefHoster)
        {
            var animator = findedRefHoster.GetComponent<Animator>();
            if (animator)
            {
                animator.avatar = (Avatar)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的Animator组件: path={0}", m_path));
            }
        }

        private void Recover_AnimationController(List<string> erroList, Transform findedRefHoster)
        {
            var animator = findedRefHoster.GetComponent<Animator>();
            if (animator)
            {
                animator.runtimeAnimatorController = (RuntimeAnimatorController)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的Animator组件: path={0}", m_path));
            }
        }

        private void Recover_Material(List<string> erroList, Transform findedRefHoster)
        {
            var renderer = findedRefHoster.GetComponent<Renderer>();
            if (renderer)
            {
                List<Material> matList = new List<Material>(renderer.sharedMaterials);
                if (m_extraInt >= matList.Count)
                {
                    for (int k = matList.Count; k < m_extraInt + 1; k++)
                    {
                        matList.Add(null);
                    }
                }

                matList[m_extraInt] = (Material)m_asset;
                renderer.sharedMaterials = matList.ToArray();
            }
            else
            {
                erroList.Add(string.Format("找不到对应的Renderer组件: path={0}", m_path));
            }
        }

        private void Recover_SkinnedMesh(List<string> erroList, Transform findedRefHoster)
        {
            var skinnedMeshRenderer = findedRefHoster.GetComponent<SkinnedMeshRenderer>();
            if (skinnedMeshRenderer)
            {
                skinnedMeshRenderer.sharedMesh = (Mesh)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的SkinnedMeshRenderer组件: path={0}", m_path));
            }
        }
        
        private void Recover_DirectGPUSkinnedMesh(List<string> erroList, Transform findedRefHoster)
        {
#if !OUTSOURCE
            var directGPUSkinnedMeshRenderer = findedRefHoster.GetComponent<DirectGPUSkinnedMeshRenderer>();
            if (directGPUSkinnedMeshRenderer)
            {
                directGPUSkinnedMeshRenderer.sharedMesh = (Mesh)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的DirectGPUSkinnedMeshRenderer组件: path={0}", m_path));
            }
#endif
        }

        private void Recover_Mesh(List<string> erroList, Transform findedRefHoster)
        {
            var meshFilter = findedRefHoster.GetComponent<MeshFilter>();
            if (meshFilter)
            {
                meshFilter.sharedMesh = (Mesh)m_asset;
            }
            else
            {
                erroList.Add(string.Format("找不到对应的MeshFilter组件: path={0}", m_path));
            }
        }
    }

}