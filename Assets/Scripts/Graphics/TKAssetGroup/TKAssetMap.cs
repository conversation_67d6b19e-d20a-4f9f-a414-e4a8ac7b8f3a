using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using ZGame;

namespace TKFrame.Asset
{
    /// <summary>
    /// AssetMap在还原时候的缓存器;
    /// 如animation在还原的时候只有add,remove操作，要兼容中间插入的话，只能做个中间的缓存器做处理;
    /// </summary>
    public class TKAssetMapRecoverCacher
    { 
        /// <summary>
        /// Animation_State_Arr用的clip缓存;
        /// </summary>
        public Dictionary<string, List<AnimationClip>> Animation_State_Arr_ClipDic;
    }
    
    public class TKAssetMap : ScriptableObject
    {
        [SerializeField]
        private List<TKAssetLink> m_assets = new List<TKAssetLink>();

        public List<TKAssetLink> assets
        {
            get { return m_assets; }
        }

        public bool IsDataEmpty()
        {
            return this.m_assets.Count == 0;
        }

        private static readonly TKAssetLinkType[] FirstRecoverLinkTypeArr = new[]
        {
            TKAssetLinkType.SkinnedMesh,
            TKAssetLinkType.DirectGPUSkinnedMesh,
            TKAssetLinkType.Avatar
            
        };
        
        /**
         * 恢复Lod资源;
         */
        public void RecoverLodAssets(Transform root, List<string> erroList, RecoverType recoverType = RecoverType.overlay)
        {
            //注意，因为这个Cacher不常用，所以用到的时候自行初始化判断;
            TKAssetMapRecoverCacher assetMapRecoverCacher = null;
            //这里先还原avatar, 这样ac还原的时候就可以播放动画了;
            for (int i = 0; i < m_assets.Count; i++)
            {
                var assetLink = m_assets[i];
                if (FirstRecoverLinkTypeArr.Contains(assetLink.linkType))
                {
                    assetLink.RecoverLodAsset(root, erroList, ref assetMapRecoverCacher, recoverType);   
                }
            }
            //再还原剩下的资源;
            for (int i = 0; i < m_assets.Count; i++)
            {
                var assetLink = m_assets[i];
                if (!FirstRecoverLinkTypeArr.Contains(assetLink.linkType))
                {
                    assetLink.RecoverLodAsset(root, erroList, ref assetMapRecoverCacher, recoverType);   
                }
            }
            #region 在还原资源后的收尾工作;
            if (assetMapRecoverCacher != null)
            {
                if (assetMapRecoverCacher.Animation_State_Arr_ClipDic != null)
                {
                    foreach (var kvp in assetMapRecoverCacher.Animation_State_Arr_ClipDic)
                    {
                        var findedRefHoster = TKAssetLink.FindedRefHoster(root, kvp.Key);
                        if (findedRefHoster)
                        {
                            var animationComp = findedRefHoster.GetComponent<Animation>();
                            if (animationComp)
                            {
                                //先清除原有的clip arr数据;
                                List<AnimationClip> clipList = new List<AnimationClip>();
                                foreach (AnimationState state in animationComp)
                                {
                                    clipList.Add(state.clip);
                                }
                                for (int i = 0; i < clipList.Count; i++)
                                {
                                    animationComp.RemoveClip(clipList[i]);
                                }
                                //填充新的clip arr数据;
                                for (int i = 0; i < kvp.Value.Count; i++)
                                {
                                    animationComp.AddClip(kvp.Value[i], kvp.Value[i].name);
                                }
                            }
                        }
                    }
                }
            }
            #endregion
        }
        
#if UNITY_EDITOR

        public void GenLod(Transform root, LodType lodType, TKAssetPath tkAssetPath)
        {
            //需要依据类型进行裁减的可以在这里搞, 比如依据root所在路径去判断类型;
            //生成lod引用资源配置;
            //为了兼容高级LOD可能没有资源的情况，向下去找资源供使用;
            for (int i = (int)lodType; i > (int)LodType.none ; i--)
            {
                LodType checkLodType = (LodType)i;
                if (tkAssetPath != null)
                {
                    //这里有字段冗余，不过消耗可以忽略不计，为了防止全资源重新生成一次，先这样吧。最好是在filterResultLodType == FilterResultLodType.Success的情况下才去记录的..
                    tkAssetPath.usedLodTypeList.Add(checkLodType);
                }
                m_assets.Clear();
                FilterResultLodType filterResultLodType = GenLod_Base(m_assets, root, checkLodType, false);
                if(filterResultLodType == FilterResultLodType.Success)break;
            }
        }
        
        /**
         * 判断下父级的名字是否为lod;
         */
        static Transform IsFirstParentOrSelfLod(Transform obj, Transform root)
        {
            Transform currentParent = obj;

            while (currentParent != null && (currentParent.IsChildOf(root) || currentParent == root))
            {
                for (int i = 0; i < TKAssetGroup.allLodNameList.Length; i++)
                {
                    if (currentParent.name == TKAssetGroup.allLodNameList[i])
                    {
                        return currentParent;
                    }   
                }
                currentParent = currentParent.parent;
            }

            return null;
        }
        
        private enum FilterResultLodType
        {
            Failed,
            Common,
            Success,
        }

        private static bool IsFilterResultLodTypeValid(FilterResultLodType filterResultLodType)
        {
            return filterResultLodType == FilterResultLodType.Common ||
                   filterResultLodType == FilterResultLodType.Success;
        }

        /**
         * 是否满足Lod的过滤;
         */
        private static FilterResultLodType IsFilterLod(Transform checkItem, Transform root, LodType lodType)
        {
            if (!checkItem.IsChildOf(root) && checkItem != root) return FilterResultLodType.Failed;
            string lodName = TKAssetGroup.GetLodNameByLodType(lodType);
            if (checkItem.name == lodName)
            {
                return FilterResultLodType.Success;
            }

            #region 认为是所有LOD都需要用到的

            Transform firstParentLod = IsFirstParentOrSelfLod(checkItem, root);
            if (firstParentLod == null)
            {
                return FilterResultLodType.Common;
            }
            else if(firstParentLod.name == lodName)
            {
                return FilterResultLodType.Success;
            }

            #endregion

            return FilterResultLodType.Failed;
        }

        public static void ClearLodRefRes(Transform root)
        {
            for (int i = (int)LodType.sealow; i <= (int)LodType.high; i++)
            {
                GenLod_Base(null, root, (LodType)i, true);   
            }
        }

        /// <summary>
        /// 分离LOD资源
        /// </summary>
        /// <param name="assets"></param>
        /// <param name="root"></param>
        /// <param name="lodType"></param>
        /// <param name="isClear"></param>
        /// <returns>这里是判断是否有合适的LOD过滤的判断，比如有success就用sucess</returns>
        private static FilterResultLodType GenLod_Base(List<TKAssetLink> assets, Transform root, LodType lodType, bool isClear)
        {
            FilterResultLodType resultLodType = FilterResultLodType.Failed;
            var renderer = root.GetComponentsInChildren<Renderer>(true);
            foreach (var r in renderer)
            {
                if (!isClear)
                {
                    FilterResultLodType filterResultLodType = IsFilterLod(r.transform, root, lodType);
                    if (!IsFilterResultLodTypeValid(filterResultLodType))continue;
                    resultLodType = (FilterResultLodType)Mathf.Max((int)resultLodType, (int)filterResultLodType);
                }
                // MeshRenderer
                Record_MeshRenderer(assets, root, isClear, r);
                // SkinnedMeshRenderer
                Record_SkinnedMeshRenderer(assets, root, isClear, r);
                // DirectGPUSkinnedMeshRenderer
                Record_DirectGPUSkinnedMesh(assets, root, isClear, r);
                // SharedMaterials (包括了下面的共享材质: MeshRenderer / SkinnedMeshRenderer / TrailRenderer / LineRenderer / SpriteRenderer)
                Record_SharedMaterials(assets, root, isClear, r);
                // SpriteMask
                Record_SpriteMask(assets, root, isClear, r);
                // SpriteRenderer
                Record_SpriteRenderer(assets, root, isClear, r);
            }

            // Animator
            resultLodType = Record_Animator(assets, root, lodType, isClear, ref resultLodType);
            // Animation
            resultLodType = Record_Animation(assets, root, lodType, isClear, ref resultLodType);

            return resultLodType;
        }
        
        private static void Record_SpriteMask(List<TKAssetLink> assets, Transform root, bool isClear, Renderer r)
        {
            // SpriteMask
            var sm = r as SpriteMask;
            if (sm != null && sm.sprite != null)
            {
                if (!isClear)
                {
                    TKAssetLink assetLink = new TKAssetLink(GameUtil.GetHierarchyPath(sm.transform, root),
                        TKAssetLinkType.SpriteMask, sm.sprite);
                    assets.Add(assetLink);
                }
                else
                {
                    sm.sprite = null;
                }
            }
        }
        
        private static void Record_SpriteRenderer(List<TKAssetLink> assets, Transform root, bool isClear, Renderer r)
        {
            var sr = r as SpriteRenderer;
            if (sr != null && sr.sprite != null)
            {
                if (!isClear)
                {
                    TKAssetLink assetLink = new TKAssetLink(GameUtil.GetHierarchyPath(sr.transform, root),
                        TKAssetLinkType.SpriteRenderer, sr.sprite);
                    assets.Add(assetLink);
                }
                else
                {
                    sr.sprite = null;
                }
            }
        }
        
        private static FilterResultLodType Record_Animation(List<TKAssetLink> assets, Transform root, LodType lodType, bool isClear, ref FilterResultLodType resultLodType)
        {
            var animationCompArr = root.GetComponentsInChildren<Animation>();
            foreach (var animationComp in animationCompArr)
            {
                if (!isClear)
                {
                    FilterResultLodType filterResultLodType = IsFilterLod(animationComp.transform, root, lodType);
                    if (!IsFilterResultLodTypeValid(filterResultLodType)) continue;
                    resultLodType = (FilterResultLodType)Mathf.Max((int)resultLodType, (int)filterResultLodType);
                    assets.Add(new TKAssetLink(GameUtil.GetHierarchyPath(animationComp.transform, root), TKAssetLinkType.Animation_Main, animationComp.clip));

                    var aniClipArr = AnimationUtility.GetAnimationClips(animationComp);
                    for (int i = 0; i < aniClipArr.Length; i++)
                    {
                        var clip = aniClipArr[i];
                        TKAssetLink assetLink = new TKAssetLink(GameUtil.GetHierarchyPath(animationComp.transform, root), TKAssetLinkType.Animation_State_Arr, clip);
                        assetLink.SetExtraInt(i);
                        assets.Add(assetLink);
                    }
                }
                else
                {
                    animationComp.clip = null;
                    AnimationUtility.SetAnimationClips(animationComp, new AnimationClip[0]);
                }
            }
            return resultLodType;
        }

        private static FilterResultLodType Record_Animator(List<TKAssetLink> assets, Transform root, LodType lodType, bool isClear, ref FilterResultLodType resultLodType)
        {
            var animators = root.GetComponentsInChildren<Animator>();
            foreach (var animator in animators)
            {
                if (!isClear)
                {
                    FilterResultLodType filterResultLodType = IsFilterLod(animator.transform, root, lodType);
                    if (!IsFilterResultLodTypeValid(filterResultLodType)) continue;
                    resultLodType = (FilterResultLodType)Mathf.Max((int)resultLodType, (int)filterResultLodType);
                    var ctrl = animator.runtimeAnimatorController;
                    if (ctrl != null)
                        assets.Add(new TKAssetLink(GameUtil.GetHierarchyPath(animator.transform, root),
                            TKAssetLinkType.AnimationController, ctrl));
                    var avatar = animator.avatar;
                    if (avatar != null)
                        assets.Add(new TKAssetLink(GameUtil.GetHierarchyPath(animator.transform, root),
                            TKAssetLinkType.Avatar, avatar));
                }
                else
                {
                    animator.runtimeAnimatorController = null;
                    animator.avatar = null;
                }
            }

            return resultLodType;
        }

        private static void Record_SharedMaterials(List<TKAssetLink> assets, Transform root, bool isClear, Renderer r)
        {
            for (int i = 0; i < r.sharedMaterials.Length; ++i)
            {
                var mat = r.sharedMaterials[i];
                if (!isClear)
                {
                    TKAssetLink assetLink = new TKAssetLink(GameUtil.GetHierarchyPath(r.transform, root),
                        TKAssetLinkType.Material, mat);
                    assetLink.SetExtraInt(i);
                    assets.Add(assetLink);
                }
            }

            if (isClear)
            {
                r.sharedMaterials = new Material[0];
            }   
        }

        private static void Record_SkinnedMeshRenderer(List<TKAssetLink> assets, Transform root, bool isClear, Renderer r)
        {
            var smr = r as SkinnedMeshRenderer;
            if (smr != null && smr.sharedMesh != null)
            {
                if (!isClear)
                {
                    TKAssetLink assetLink = new TKAssetLink(GameUtil.GetHierarchyPath(smr.transform, root),
                        TKAssetLinkType.SkinnedMesh, smr.sharedMesh);
                    assets.Add(assetLink);
                }
                else
                {
                    smr.sharedMesh = null;
                }
            }
        }
        
        private static void Record_DirectGPUSkinnedMesh(List<TKAssetLink> assets, Transform root, bool isClear, Renderer r)
        {
#if !OUTSOURCE
            var smr = r as DirectGPUSkinnedMeshRenderer;
            if (smr != null && smr.sharedMesh != null)
            {
                if (!isClear)
                {
                    TKAssetLink assetLink = new TKAssetLink(GameUtil.GetHierarchyPath(smr.transform, root),
                        TKAssetLinkType.DirectGPUSkinnedMesh, smr.sharedMesh);
                    assets.Add(assetLink);
                }
                else
                {
                    smr.sharedMesh = null;
                }
            }
#endif
        }

        private static void Record_MeshRenderer(List<TKAssetLink> assets, Transform root, bool isClear, Renderer r)
        {
            var mr = r as MeshRenderer;
            if (mr != null)
            {
                var mf = mr.GetComponent<MeshFilter>();
                if (mf != null && mf.sharedMesh != null)
                {
                    if (!isClear)
                    {
                        assets.Add(new TKAssetLink(GameUtil.GetHierarchyPath(mf.transform, root), TKAssetLinkType.Mesh,
                            mf.sharedMesh));
                    }
                    else
                    {
                        mf.sharedMesh = null;
                    }
                }
            }
        }
#endif
        }
}
