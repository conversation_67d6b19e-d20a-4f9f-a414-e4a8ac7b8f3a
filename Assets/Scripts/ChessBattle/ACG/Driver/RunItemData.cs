namespace ACG.Core
{
    /// <summary>
    /// TODO 池化
    /// </summary>
	public class RunItemData
	{
		public IRunable iRunable;

		public RunDelegate runDelegate;

		// 优先级
		public int priority;

		// 是否激活
		public bool activation = true;

        /// <summary>
        /// 是否需要移除
        /// </summary>
        public bool needDelete = false;

		public int priorityIndex = 0;

		public RunItemData(RunDelegate runDelegate, int priority)
		{
			this.runDelegate = runDelegate;
			this.priority = priority;
		}
	}
}
