using System;
using System.Collections.Generic;
using TDRConfig;
#if ACGGAME_CLIENT && !LOGIC_THREAD
using GameFramework.FMath;
using UnityEngine;
#else
#endif
using Z_PVE;
using ZGameClient;
using System.Runtime.Serialization.Formatters.Binary;
using System.IO;
using System.Linq;
using Lucifer.ActCore;
using TKFrame;
using ZGame;

/// <summary>
/// 扩展方法类
/// Author : shinychen
/// Date : 2018.08.23
/// </summary>
public static class ExtendMethods
{
#if ACGGAME_CLIENT

    #region 从潘多拉移植的，防止误引用

    public static void ForEach<T>(this IList<T> list, Action<T> func)
    {
        for (int index = 0; index < list.Count; ++index)
            func(list[index]);
    }

    public static void ForEach<T>(this IList<T> list, Action<int, T> func)
    {
        for (int index = 0; index < list.Count; ++index)
            func(index, list[index]);
    }

    public static void ForEach<T>(this IEnumerable<T> list, Action<T> func)
    {
        foreach (T obj in list)
            func(obj);
    }

    public static void ForEach<T>(this IEnumerable<T> list, Action<int, T> func)
    {
        int num = 0;
        foreach (T obj in list)
        {
            func(num, obj);
            ++num;
        }
    }

    public static void AddRange<T>(this ICollection<T> list, IEnumerable<T> vals)
    {
        foreach (T val in vals)
            list.Add(val);
    }

    public static IEnumerable<T> Append<T>(this IEnumerable<T> list, T v)
    {
        foreach (T obj in list)
            yield return obj;
        yield return v;
    }

    public static IEnumerable<T> Prepend<T>(this IEnumerable<T> list, T v)
    {
        yield return v;
        foreach (T obj in list)
            yield return obj;
    }

    public static bool IsEmpty(this System.Collections.ICollection list)
    {
        return list.Count == 0;
    }

    public static TValue Get<TKey, TValue>(this TKDictionary<TKey, TValue> dict, TKey key)
    {
        TValue obj;
        dict.TryGetValue(key, out obj);
        return obj;
    }

    public static TValue GetOrAdd<TKey, TValue>(
      this TKDictionary<TKey, TValue> dict,
      TKey key,
      Func<TValue> getter)
    {
        TValue obj1;
        if (dict.TryGetValue(key, out obj1))
            return obj1;
        TValue obj2 = getter();
        dict.Add(key, obj2);
        return obj2;
    }

    public static TValue Get<TKey, TValue>(this Dictionary<TKey, TValue> dict, TKey key)
    {
        TValue obj;
        dict.TryGetValue(key, out obj);
        return obj;
    }

    public static TValue GetOrAdd<TKey, TValue>(
        this Dictionary<TKey, TValue> dict,
        TKey key,
        Func<TValue> getter)
    {
        TValue obj1;
        if (dict.TryGetValue(key, out obj1))
            return obj1;
        TValue obj2 = getter();
        dict.Add(key, obj2);
        return obj2;
    }

    public static UnityEngine.RectTransform GetRectTransform(this UnityEngine.GameObject go)
    {
        UnityEngine.RectTransform rectTransform = go.transform as UnityEngine.RectTransform;
        if (!(bool)((UnityEngine.Object)rectTransform))
        {
            UnityEngine.Object.Destroy((UnityEngine.Object)go.transform);
            rectTransform = go.AddComponent<UnityEngine.RectTransform>();
            rectTransform.SetIdentity();
        }
        return rectTransform;
    }

    public static void SetActive(this UnityEngine.Component component, bool v)
    {
        component.gameObject.SetActive2(v);
    }

    public static void SetIdentity(this UnityEngine.RectTransform rectTransform)
    {
        rectTransform.SetIdentity(UnityEngine.Vector2.up);
    }

    public static void SetIdentity(this UnityEngine.RectTransform rectTransform, UnityEngine.Vector2 pt)
    {
        rectTransform.pivot = pt;
        rectTransform.anchorMin = pt;
        rectTransform.anchorMax = pt;
        rectTransform.localPosition = UnityEngine.Vector3.zero;
        rectTransform.anchoredPosition = UnityEngine.Vector2.zero;
        rectTransform.sizeDelta = UnityEngine.Vector2.zero;
        rectTransform.localScale = UnityEngine.Vector3.one;
        rectTransform.rotation = UnityEngine.Quaternion.identity;
    }

    public static TKDictionary<TKey, TValue> ReHash<TKey, TValue>(
      this TKDictionary<TKey, TValue> dict,
      int capacity)
    {
        TKDictionary<TKey, TValue> dictionary = new TKDictionary<TKey, TValue>(capacity);
        foreach (KeyValuePair<TKey, TValue> keyValuePair in dict)
            dictionary.Add(keyValuePair.Key, keyValuePair.Value);
        return dictionary;
    }

    public static Dictionary<TKey, TValue> ReHash<TKey, TValue>(
        this Dictionary<TKey, TValue> dict,
        int capacity)
    {
        Dictionary<TKey, TValue> dictionary = new Dictionary<TKey, TValue>(capacity);
        foreach (KeyValuePair<TKey, TValue> keyValuePair in dict)
            dictionary.Add(keyValuePair.Key, keyValuePair.Value);
        return dictionary;
    }

    public static void SetSize<T>(this Stack<T> stack, int size)
    {
        while (stack.Count > size)
            stack.Pop();
    }

    public static void SetSize<T>(this TKStack<T> stack, int size)
    {
        while (stack.Count > size)
            stack.Pop();
    }

    public static T GetFirst<T>(this IList<T> list)
    {
        return list[0];
    }

    public static T GetLast<T>(this IList<T> list)
    {
        return list[list.Count - 1];
    }

    public static string Remove(this string str, string target)
    {
        return str.Replace(target, string.Empty);
    }


    public static bool IsWebUrl(this string url)
    {
        Uri result;
        if (!Uri.TryCreate(url, UriKind.Absolute, out result))
            return false;
        if (!(result.Scheme == Uri.UriSchemeHttp))
            return result.Scheme == Uri.UriSchemeHttps;
        return true;
    }

    public static string GetPathRoot(this string path)
    {
        if (path.IsWebUrl())
            return path;
        string directoryName = Path.GetDirectoryName(path);
        return directoryName.IsNullOrEmpty() ? "." : directoryName;
    }


    #endregion

    public static void DestroyChildren(this UnityEngine.Transform t)
    {
        if (t != null)
        {
            bool isPlaying = UnityEngine.Application.isPlaying;
            int childCount = t.childCount;
            for (int i = childCount - 1; i > -1; i--)
            {
                var child = t.GetChild(i);
                child.name = "WaitToDestroyObject";//删除前修改一下名字，避免策划按了暂停重复使用了

                if (isPlaying)
                {
                    UnityEngine.Object.Destroy(child.gameObject);
                }
                else
                {
                    UnityEngine.Object.DestroyImmediate(child.gameObject);
                }
            }
        }
    }

    public static void SetActive2(this UnityEngine.GameObject go, bool value)
    {
        if (go != null && go.activeSelf != value)
        {
            go.SetActive(value);
        }
    }

    public static T2 TryGetValue<T1, T2>(this TKDictionary<T1, T2> dict, T1 key, T2 defaultValue = default(T2))
    {
        if (dict != null && dict.ContainsKey(key))
        {
            return dict[key];
        }

        return defaultValue;
    }

    public static T2 TryGetValue<T1, T2>(this Dictionary<T1, T2> dict, T1 key, T2 defaultValue = default(T2))
    {
        if (dict != null && dict.ContainsKey(key))
        {
            return dict[key];
        }

        return defaultValue;
    }

#elif !LOGIC_THREAD
    public static void AddRange<T>(this ICollection<T> list, IEnumerable<T> vals)
    {
      foreach (T val in vals)
        list.Add(val);
    }
#endif

#if ACGGAME_CLIENT
    public static T TryGetComponent<T>(this UnityEngine.GameObject go) where T : UnityEngine.Component
#else
    public static T TryGetComponent<T>(this UnityEngine4Server.GameObject go) where T : UnityEngine4Server.Component
#endif
    {
        if (go == null)
        {
            return default(T);
        }

        var comp = go.GetComponent<T>();
        if (comp == null || comp.Equals(null))
        {
            comp = go.AddComponent<T>();
        }

        return comp;
    }

#if ACGGAME_CLIENT
    public static UnityEngine.Component TryGetComponent(Type t, UnityEngine.GameObject go)
#else
    public static UnityEngine4Server.Component TryGetComponent(Type t, UnityEngine4Server.GameObject go)
#endif
    {
        if (go == null)
        {
            return null;
        }

        var comp = go.GetComponent(t);
        if (comp == null || comp.Equals(null))
        {
            comp = go.AddComponent(t);
        }

        return comp;
    }


#if LOGIC_THREAD
    public static T TryGetComponent<T>(this UnityEngine4Server.GameObject go) where T : UnityEngine4Server.Component

    {
        if (go == null)
        {
            return default(T);
        }

        var comp = go.GetComponent<T>();
        if (comp == null || comp.Equals(null))
        {
            comp = go.AddComponent<T>();
        }

        return comp;
    }
#endif

#if ACGGAME_CLIENT
    public static bool HasComponent<T>(this UnityEngine.GameObject go) where T : UnityEngine.Component
    {
        if (go == null)
        {
            return false;
        }

        var comp = go.GetComponent<T>();
        return comp != null;
    }

    public static void RemoveComponent<T>(this UnityEngine.GameObject go, bool destroyImmediate = false) where T : UnityEngine.Component
    {
        if (go != null)
        {
            var comp = go.GetComponent<T>();
            if (comp != null)
            {
                //删除组件
                if (destroyImmediate)
                {
                    UnityEngine.GameObject.DestroyImmediate(comp);
                }
                else
                {
                    UnityEngine.GameObject.Destroy(comp);
                }
            }
        }
    }

    public static void RemoveComponent(Type t, UnityEngine.GameObject go, bool destroyImmediate = false)
    {
        if (!t.IsSubclassOf(typeof(UnityEngine.Component)))
        {
            return;
        }
        if (go != null)
        {
            var comp = go.GetComponent(t);
            if (comp != null)
            {
                //删除组件
                if (destroyImmediate)
                {
                    UnityEngine.GameObject.DestroyImmediate(comp);
                }
                else
                {
                    UnityEngine.GameObject.Destroy(comp);
                }
            }
        }
    }

    public static void RemoveComponents<T>(this UnityEngine.GameObject go) where T : UnityEngine.Component
    {
        if (go != null)
        {
            var comps = go.GetComponents<T>();
            if (comps != null && comps.Length > 0)
            {
                foreach (var comp in comps)
                {

#if UNITY_EDITOR
                    UnityEngine.GameObject.DestroyImmediate(comp);
#else
                    // According to https://docs.unity3d.com/ScriptReference/Object.DestroyImmediate.html
                    // We do not support destroy array in immediate mode on devices. (aka. DestroyImmediate).
                    UnityEngine.GameObject.Destroy(comp);
#endif
                }
            }
        }
    }

    public static UnityEngine.Vector3 Clone(this UnityEngine.Vector3 target)
    {
        return new UnityEngine.Vector3(target.x, target.y, target.z);
    }

    public static UnityEngine.Vector2 Clone(this UnityEngine.Vector2 target)
    {
        return new UnityEngine.Vector2(target.x, target.y);
    }
#endif

    public static bool ContainsItem<T>(this T[] source, T value) where T : IEquatable<T>
    {
        for (int i = 0; i < source.Length; i++)
        {
            if (source[i] != null && source[i].Equals(value))
                return true;

        }
        return false;
    }

    /// <summary>
    /// 设置目标是否变灰
    /// </summary>
    //     public static void SetGray(this UIWidget target, bool value)
    //     {
    //         if(target != null)
    //         {
    //             var color = target.color;
    //             color.r = value ? 0 : 1;
    //             target.color = color;
    //         }
    //     }



    public static bool IsLux(this TACG_Hero_Client client)
    {
        return client.sClass == "13";
    }

    /// <summary>
    /// 是否是天选BUFF棋子
    /// </summary>
    /// <param name="hero"></param>
    /// <returns></returns>
    public static bool IsHeavenHero(this TAC_HeroEntity hero)
    {
        var obj = MicroMgr.Instance.GetMicroObj();
        if (hero != null && obj != null && obj.CSoGame != null && obj.CSoGame.CSoConfig != null)
        {
            var conf = obj.CSoGame.CSoConfig.GetAutoChessHeroInfoByID(hero.iHeroConfID);
            if (conf != null)
                return conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.HEAVEN_BUFF_HERO;

        }
        return false;
    }

    /// <summary>
    /// 是否是无素使召唤的英雄
    /// </summary>
    /// <param name="hero"></param>
    /// <returns></returns>
    public static bool IsSummonHero(this TAC_HeroEntity hero)
    {
        var obj = MicroMgr.Instance.GetMicroObj();
        if (hero != null && obj != null && obj.CSoGame != null && obj.CSoGame.CSoConfig != null)
        {
            var conf = obj.CSoGame.CSoConfig.GetAutoChessHeroInfoByID(hero.iHeroConfID);
            if (conf != null)
                return conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.SUMMON ||
                    conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.SCENCE_OBJ;

        }
        return false;
    }

    public static bool IsSummonHeroWithoutFetter(this TAC_HeroEntity hero)
    {
        var obj = MicroMgr.Instance.GetMicroObj();
        if (hero != null && obj != null && obj.CSoGame != null && obj.CSoGame.CSoConfig != null)
        {
            var conf = obj.CSoGame.CSoConfig.GetAutoChessHeroInfoByID(hero.iHeroConfID);
            if (conf != null)
                return (conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.SUMMON ||
                    conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.SCENCE_OBJ)
                    && conf.iMoreFunType != (int)HeroMoreFunType.SummonWithFetter;

        }
        return false;
    }
    /// <summary>
    /// s5墓碑属于场景物件，不可移动
    /// </summary>
    /// <param name="hero"></param>
    /// <returns></returns>
    public static bool IsScenceObj(this TAC_HeroEntity hero)
    {
        var obj = MicroMgr.Instance.GetMicroObj();
        if (hero != null && obj != null && obj.CSoGame != null && obj.CSoGame.CSoConfig != null)
        {
            var conf = obj.CSoGame.CSoConfig.GetAutoChessHeroInfoByID(hero.iHeroConfID);
            if (conf != null && conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.SCENCE_OBJ)
                return true;
        }
        return false;
    }
    /// <summary>
    /// 一些不能上阵只能出现在备战席的特殊物品（龙蛋、铁砧之类的），有些在生成的时候被挤上阵，这个在生成的时候特殊处理
    /// </summary>
    /// <param name="hero"></param>
    /// <returns></returns>
    public static bool IsWaitOnly(this TAC_HeroEntity hero)
    {
        var obj = MicroMgr.Instance.GetMicroObj();
        if (hero != null && obj != null && obj.CSoGame != null && obj.CSoGame.CSoConfig != null)
        {
            var conf = obj.CSoGame.CSoConfig.GetAutoChessHeroInfoByID(hero.iHeroConfID);
            if (conf != null && conf.iHeroType == (int)TDRConfig.CHESS_UNIT_TYPE.WAIT_ONLY)
                return true;
        }
        return false;
    }

    #region JCE结构体快捷接口

    public static void SetData(this TAC_HeroEntity entity, TAC_HeroEntity data)
    {
        entity.iEntityID = data.iEntityID;
        entity.iHeroConfID = data.iHeroConfID;
    }

    public static void SetData(this TAC_BattleHeroPos entity, TAC_BattleHeroPos data)
    {
        entity.x = data.x;
        entity.y = data.y;
        //entity.timeStampMs = data.timeStampMs;
        entity.stHeroEntity.SetData(data.stHeroEntity);
    }

    public static void SetData(this CTAC_Hero entity, CTAC_Hero data)
    {
        entity.m_iPosType = data.m_iPosType;
        entity.m_iPos = data.m_iPos;
        //entity.m_timeStampMs = data.m_timeStampMs;
        entity.m_iBuddChildCnt = data.m_iBuddChildCnt;
        entity.m_iDragonEggsCfg = data.m_iDragonEggsCfg;
        entity.m_bIsPartnerHero = data.m_bIsPartnerHero;
        entity.m_bIsRealHero = data.m_bIsRealHero;
        entity.m_iSrcPos = data.m_iSrcPos;
        entity.m_stHeroEntity.iEntityID = data.m_stHeroEntity.iEntityID;
        entity.m_stHeroEntity.iHeroConfID = data.m_stHeroEntity.iHeroConfID;
    }

    private static string[] defaultResult = new string[] { "" };


    public static int GetHeroGroupId(this TACG_Hero_Client heroCfg)
    {
        if (heroCfg.iMoreFunType == (int)HeroMoreFunType.Breakout)
            return heroCfg.iGroup;
        return heroCfg.iHeroTypeGroup <= 0 ? heroCfg.iGroup : heroCfg.iHeroTypeGroup;
    }
    #endregion

    #region Vector3的noAlloc接口

#if ACGGAME_CLIENT
    public static void NoAllocMinus(this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 resultVec3, ref UnityEngine.Vector3 bVec3)
    {
        resultVec3.x = thisVec3.x - bVec3.x;
        resultVec3.y = thisVec3.y - bVec3.y;
        resultVec3.z = thisVec3.z - bVec3.z;
    }

    public static void NoAllocAdd(this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 resultVec3, ref UnityEngine.Vector3 bVec3)
    {
        resultVec3.x = thisVec3.x + bVec3.x;
        resultVec3.y = thisVec3.y + bVec3.y;
        resultVec3.z = thisVec3.z + bVec3.z;
    }

    public static void NoAllocDivide(this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 resultVec3, float divideValue)
    {
        resultVec3.x = thisVec3.x / divideValue;
        resultVec3.y = thisVec3.y / divideValue;
        resultVec3.z = thisVec3.z / divideValue;
    }

    public static void NoAllocMultiply(this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 resultVec3, float divideValue)
    {
        resultVec3.x = thisVec3.x * divideValue;
        resultVec3.y = thisVec3.y * divideValue;
        resultVec3.z = thisVec3.z * divideValue;
    }
    public static float NoAllocDistance(this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 resultVec3, ref UnityEngine.Vector3 bVec3)
    {
        thisVec3.NoAllocMinus(ref resultVec3, ref bVec3);
        return UnityEngine.Vector3.Magnitude(resultVec3);
    }

    public static void NoAllocNormalize(this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 resultVec3)
    {
        float num = UnityEngine.Vector3.Magnitude(thisVec3);
        if ((double)num > 9.99999974737875E-06)
        {
            thisVec3.NoAllocDivide(ref resultVec3, num);
        }
        else
        {
            resultVec3.SetZero();
        }
    }

    public static void Set(ref this UnityEngine.Vector3 thisVec3, ref UnityEngine.Vector3 newVec3Value)
    {
        thisVec3.x = newVec3Value.x;
        thisVec3.y = newVec3Value.y;
        thisVec3.z = newVec3Value.z;
    }

    public static void SetZero(ref this UnityEngine.Vector3 thisVec3)
    {
        thisVec3.x = 0;
        thisVec3.y = 0;
        thisVec3.z = 0;
    }

    public static void NoAllocLerp(this UnityEngine.Vector2 thisVec2, ref UnityEngine.Vector2 resultVec2, ref UnityEngine.Vector2 b, float t)
    {
        t = UnityEngine.Mathf.Clamp01(t);
        resultVec2.x = thisVec2.x + (b.x - thisVec2.x) * t;
        resultVec2.y = thisVec2.y + (b.y - thisVec2.y) * t;
    }

    /// <summary>
    /// 深拷贝
    /// 注意：T必须标识为可序列化[Serializable]
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static T DeepCopy<T>(this T obj) where T : class
    {
        try
        {
            if (obj == null)
            {
                return null;
            }

            BinaryFormatter binaryFormatter = new BinaryFormatter();
            using (MemoryStream stream = new MemoryStream())
            {
                binaryFormatter.Serialize(stream, obj);
                stream.Position = 0;
                return binaryFormatter.Deserialize(stream) as T;
            }
        }
        catch (Exception ex)
        {
            Diagnostic.Error(ex.Message);
            return null;
        }
    }

#endif
    #endregion
}