#if !ACGGAME_CLIENT || LOGIC_THREAD
using UnityEngine4Server;
#else
using UnityEngine;
#endif
using System.Collections;
using System.Collections.Generic;
using Lucifer.ActCore;
using TKFrame;

public class ProjectilePoolManager
{
    public static Dictionary<string, Stack<GameObject>> m_ProjectilePool = null;
    public static Dictionary<string, Stack<GameObject>> ProjectilePool
    {
        get
        {
            if (m_ProjectilePool == null)
                m_ProjectilePool = new Dictionary<string, Stack<GameObject>>(64);
            return m_ProjectilePool;
        }
    }

    public static void Dispose()
    {



        if (ProjectilesPoolRoot != null)
        {
            GameObject.Destroy(ProjectilesPoolRoot);
        }

        m_ProjectilePool = null;


    }

    public static void Reset()
    {

    }

    public static void PreloadProjectileObject(string typeName)
    {
        GameObject projectile = RoleResDataMananger.GetProjectile(typeName);
        if (projectile == null)
            return;

        FreeToPool(typeName, projectile);
    }


    public static GameObject GetProjectileObject(string typeName)
    {
        Stack<GameObject> poolStack;
        if (!ProjectilePool.TryGetValue(typeName, out poolStack))
        {
            poolStack = new Stack<GameObject>(16);
            ProjectilePool.Add(typeName, poolStack);
        }
        GameObject projectile = null;
        if (poolStack.Count > 0)
        {
            projectile = poolStack.Pop();
        }
        else
        {
            projectile = RoleResDataMananger.GetProjectile(typeName);
        }
        //不去单例 统一的池 
        RoleResDataMananger.Instance.AddProjectile(projectile);

        return projectile;
    }

    public static void FreeToPool(string typeName, GameObject projectile)
    {
        Stack<GameObject> stackData;
        if (ProjectilePool.TryGetValue(typeName, out stackData))
        {
#if ACGGAME_CLIENT && !LOGIC_THREAD
            projectile.SetActive(false);


            AddProjectile(projectile);
#endif

            stackData.Push(projectile);
        }
    }

#if ACGGAME_CLIENT && !LOGIC_THREAD
    static GameObject ProjectilesPoolRoot;

    public static void AddProjectile(GameObject projectile)
    {
        if (ProjectilesPoolRoot == null)
        {
            ProjectilesPoolRoot = new GameObject("飞行道具池Root");
        }
        if (projectile != null)
            projectile.transform.parent = ProjectilesPoolRoot.transform;
    }
#endif
}


public class CharacterPoolManager
{
    private class StaticField : ReleaseSingletonLogic<StaticField>

    {
        public Dictionary<string, Stack<GameObject>> m_CharacterPool = null;
        public Dictionary<string, Stack<GameObject>> m_CharacterBufferPool = new Dictionary<string, Stack<GameObject>>(64);
        public GameObject CharacterPoolRoot;
    }

    public static Dictionary<string, Stack<GameObject>> CharacterPool
    {
        get
        {
            StaticField staticField = StaticField.Ins;
            if (staticField.m_CharacterPool == null)
                staticField.m_CharacterPool = new Dictionary<string, Stack<GameObject>>(64);
            return staticField.m_CharacterPool;
        }
    }

    public static Dictionary<string, Stack<GameObject>> CharacterBufferPool
    {
        get
        {
            StaticField staticField = StaticField.Ins;
            if (staticField.m_CharacterBufferPool == null)
                staticField.m_CharacterBufferPool = new Dictionary<string, Stack<GameObject>>(64);
            return staticField.m_CharacterBufferPool;
        }
    }
    public static void Dispose()
    {
    }

    public static void Reset()
    {

    }

    public static GameObject GetCharacter(string typeName)
    {

        if (!CharacterPool.ContainsKey(typeName))
        {
            CharacterPool.Add(typeName, new Stack<GameObject>(8));
        }
        if (CharacterPool[typeName].Count > 0)
        {
            GameObject Character = CharacterPool[typeName].Pop();

            return Character;
        }

        return null;
    }
    public static int GetCharactersCount(string typeName)
    {

        if (!CharacterPool.ContainsKey(typeName))
        {
            CharacterPool.Add(typeName, new Stack<GameObject>(8));
        }

        return CharacterPool[typeName].Count;

    }
    /// <summary>
    /// 角色池 已经preinit过的
    /// </summary>
    /// <param name="typeName"></param>
    /// <param name="Character"></param>
    public static void FreeToPool(string typeName, GameObject Character)
    {
        if (!CharacterPool.ContainsKey(typeName))
        {
            CharacterPool.Add(typeName, new Stack<GameObject>(8));
        }
        Character.SetActive(false);
        AddCharacter(Character);
        CharacterPool[typeName].Push(Character);

    }



    public static GameObject GetBufferCharacter(string typeName)
    {

        if (!CharacterBufferPool.ContainsKey(typeName))
        {
            CharacterBufferPool.Add(typeName, new Stack<GameObject>(8));
        }
        if (CharacterBufferPool[typeName].Count > 0)
        {
            GameObject Character = CharacterBufferPool[typeName].Pop();

            return Character;
        }

        return null;
    }
    /// <summary>
    /// 缓冲池  ，战场重置后  把角色放在buffer池中，以供 预加载处理
    /// </summary>
    /// <param name="typeName"></param>
    /// <param name="Character"></param>
    public static void FreeToBufferPool(string typeName, GameObject Character)
    {
        if (!CharacterBufferPool.ContainsKey(typeName))
        {
            CharacterBufferPool.Add(typeName, new Stack<GameObject>(8));
        }
        Character.SetActive(false);
        AddCharacter(Character);
        CharacterBufferPool[typeName].Push(Character);

    }

    public static void AddCharacter(GameObject Character)
    {
        StaticField staticField = StaticField.Ins;
        if (staticField.CharacterPoolRoot == null)
        {
            staticField.CharacterPoolRoot = new GameObject("角色池Root");
        }
        if (Character != null)
            Character.transform.parent = staticField.CharacterPoolRoot.transform;
    }
}


#if ACGGAME_CLIENT

/// <summary>
/// 用于预创建角色 让角色的初始化平滑
/// </summary>
public static class CharacterActionDataFactory
{

    static int UpdateCount;

    static List<int> NeedCreateHeroIDList = new List<int>();

    public static Dictionary<string, int> HeroCountInFields = new Dictionary<string, int>();
    static int CurIndex = 0;
    public static void Init()
    {
        UpdateCount = 0;

#if LOGIC_THREAD
        MicroMgr.Instance.GetMicroObj().DriverMgr.netRunner.DriveMethod(OnUpdateCreating, ACG.Core.RunPriority.TIME_SCALE);
#else
        MicroMgr.Instance.GetMicroObj().DriverMgr.netRunner.DriveMethod(OnUpdateCreating, ACG.Core.RunPriority.TIME_SCALE);
#endif
        ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_HeroLogicDataCreate, OnCreateUnit);
        ACGEventManager.Instance.AddEventListener(EventType_BattleView.AutoChess_Battle_HeroCreateStart, OnMonsterPreCreate);
    }



    static void OnUpdateCreating()
    {

    }

    static void PreLoadCharecter(string sPrefabID, int uHeroId, string sAIName)
    {

    }


    //     static void LoadPreloadRes(int tabId)
    //     {
    //         string preload = ChessUtil.GetHeroPreloadName(tabId);
    //         if (string.IsNullOrEmpty(preload))
    //             return;
    //         GameRoleResourcesData data = RoleResDataMananger.GetRoleDataConfig(preload);
    //         if (data == null)
    //         {
    //             Diagnostic.Log("loading preload config failed: {0}", preload);
    //             return;
    //         }
    //         // parse effects
    //         for (int i = 0; i < data.effects.Count; i++)
    //         {
    //             //string ab = data.effects[i].packageName + "/" + data.effects[i].effectName;
    //             //PreloadResPool.Inst().Load(ab, data.effects[i].effectName);
    //             //m_preloads.Add(ab);
    //         }
    //         // parse sub-level model
    //         for (int i = 0; i < data.prePrefabs.Count; i++)
    //         {
    //             GameRoleResourcesData sub = RoleResDataMananger.GetRoleDataConfig(data.prePrefabs[i]);
    //             if (sub == null || string.IsNullOrEmpty(sub.perModel))
    //             {
    //                 Diagnostic.Log("loading preload config failed, config: {0}, sub: {1}", preload, data.prePrefabs[i]);
    //                 continue;
    //             }
    // 
    //             //                 string whichVersion = "set1/";
    //             //                 if (sub.perModel.IndexOf("s2") == 0)
    //             //                     whichVersion = "set2/";
    // 
    //             string ab = ChessUtil.Model_Path + ResPath.GetSetVersion(sub.perModel) + sub.perModel;
    //             PreloadResPool.Inst().Load(ab, sub.perModel);
    //             // can't add, transform hero need reserved
    //             //m_preloads.Add(ab);
    //         }
    //     }
    static void OnCreateUnit(GEvent e)
    {
        int id = e.intData;

        NeedCreateHeroIDList.Add(e.intData);
    }

    public static void BattleStartCleanPreCreateList(GEvent e)
    {
        UpdateCount = 0;
        CurIndex = 0;
        NeedCreateHeroIDList.Clear();
        HeroCountInFields.Clear();
    }

    public static void OnMonsterPreCreate(GEvent e)
    {


    }
    public static void Dispose()
    {
        ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_HeroCreateStart, OnMonsterPreCreate);
        ACGEventManager.Instance.RemoveEventListener(EventType_BattleView.AutoChess_Battle_HeroLogicDataCreate, OnCreateUnit);


#if LOGIC_THREAD
        MicroMgr.Instance.GetMicroObj().DriverMgr.netRunner.RemoveDriveMethod(OnUpdateCreating);
#else
        MicroMgr.Instance.GetMicroObj().DriverMgr.netRunner.RemoveDriveMethod(OnUpdateCreating);
#endif
        NeedCreateHeroIDList.Clear();
        HeroCountInFields.Clear();
    }

}
#endif