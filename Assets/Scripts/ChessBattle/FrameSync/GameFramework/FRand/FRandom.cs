using System;
using System.Collections.Generic;
using GameFramework.FMath;
using TKFrame;

namespace GameFramework.FRand
{
	public class FRandUtils
	{
        /// <summary>
        /// interfaces
        /// </summary>
        static private FRandom s_random = new FRandom();

        public static int DefaultSeed;

        public static void InitSeed(int seed)
		{
			Diagnostic.Log("[FRandUtils InitSeed] RandomSeed: {0}", seed);
            //s_random._randomCount = 0;
            DefaultSeed = seed;
            s_random.InitSeed(seed);
		}

        public static IList<T> Shuffle<T>(IList<T> deck)
		{
			if (deck == null)
				return null;

			for (int n = deck.Count - 1; n > 0; --n)
			{
				int k = RandInt(n + 1);
				T temp = deck[n];
				deck[n] = deck[k];
				deck[k] = temp;
			}
			return deck;
		}
        
        
        public static IList<T> ShuffleWithRandom<T>(IList<T> deck,FRandom _fRandom)
        {
            if (deck == null)
                return null;

            for (int n = deck.Count - 1; n > 0; --n)
            {
                int k = _fRandom.RandomInt(n + 1);
                T temp = deck[n];
                deck[n] = deck[k];
                deck[k] = temp;
            }
            return deck;
        }

        public static int RunCount
        {
            get { return s_random._randomCount; }
        }



        /// <summary>
        /// TODO: 不同步记录，服务器验证时容易出现不同步问题;
        /// 出现场景：一个进程启动多个内核;
        /// </summary>
        /// <returns></returns>
		public static int RandInt()
		{
			return s_random.RandomInt();
		}

		public static int RandInt(int min, int max)
		{
			return s_random.RandomInt(min, max);
		}

		public static int RandInt(int max)
		{
			return s_random.RandomInt(max);
		}

		public static Fix64 RandFix64()
		{
			return s_random.RandFix64();
		}

        public static int RandWeightsListIndex(List<int> weightsList)
        {
            if(weightsList == null || weightsList.Count == 0)
            {
                return -1;
            }

            int allWeights = 0;
            for(int i = 0; i < weightsList.Count; i++)
            {
                allWeights += weightsList[i];
            }
            int randWeights = RandInt() % allWeights;
            for(int i = 0; i < weightsList.Count; i++)
            {
                if(randWeights < weightsList[i])
                {
                    return i;
                }
                randWeights -= weightsList[i];
            }

            return 0;
        }

        public static int GetRandomCount()
        {
            return s_random._randomCount;
        }

		/// <summary>
		/// internal class
		/// </summary>		
	}

    public class FRandom
    {
        private const int MBIG = Int32.MaxValue;
        private const int MSEED = 161803398;
        private const int MZ = 0;

        private int inext;
        private int inextp;
        private int[] SeedArray = new int[56];

        private int count = 0;

        public int _randomCount = 0;
        public int battleIndex = -1;

#if ACGGAME_CLIENT
        public int m_currentSeed;
#endif

        public FRandom()
        {

        }

        public int GetRandomCount()
        {
            return _randomCount;
        }

        public void InitSeed(int Seed)
        {
            _randomCount = 0;
            
            int ii;
            int mj, mk;

            //Initialize our Seed array.
            //This algorithm comes from Numerical Recipes in C (2nd Ed.)
            int subtraction = (Seed == Int32.MinValue) ? Int32.MaxValue : System.Math.Abs(Seed);
            mj = MSEED - subtraction;
            SeedArray[55] = mj;
            mk = 1;
            for (int i = 1; i < 55; i++)
            {  //Apparently the range [1..55] is special (Knuth) and so we're wasting the 0'th position.
                ii = (21 * i) % 55;
                SeedArray[ii] = mk;
                mk = mj - mk;
                if (mk < 0) mk += MBIG;
                mj = SeedArray[ii];
            }
            for (int k = 1; k < 5; k++)
            {
                for (int i = 1; i < 56; i++)
                {
                    SeedArray[i] -= SeedArray[1 + (i + 30) % 55];
                    if (SeedArray[i] < 0) SeedArray[i] += MBIG;
                }
            }
            inext = 0;
            inextp = 21;
            count = 0;
        }

        public void SetSeedArray(int[] seedArray)
        {
            SeedArray = seedArray;
        }

        public int[] GetSeedArray()
        {
            return SeedArray;
        }

        //
        // Package Private Methods
        //

        /*====================================Sample====================================
        **Action: Return a new random number [0..1) and reSeed the Seed array.
        **Returns: A fix64 [0..1)
        **Arguments: None
        **Exceptions: None
        ==============================================================================*/
        protected virtual long Sample()
        {
            //Including this division at the end gives us significantly improved
            //random number distribution.

            int seed = InternalSample();
#if UNITY_EDITOR && ACGGAME_CLIENT
            m_currentSeed = seed;
#endif

            return seed;
        }

        private int InternalSample()
        {
            _randomCount++;

            int retVal;
            int locINext = inext;
            int locINextp = inextp;

            if (++locINext >= 56) locINext = 1;
            if (++locINextp >= 56) locINextp = 1;

            retVal = SeedArray[locINext] - SeedArray[locINextp];

            if (retVal == MBIG) retVal--;
            if (retVal < 0) retVal += MBIG;

            SeedArray[locINext] = retVal;

            inext = locINext;
            inextp = locINextp;
            count++;

            //uint frameId = MicroMgr.Instance.GetMicroObj().fightContext.director != null ? MicroMgr.Instance.GetMicroObj().fightContext.director.frameCount : 0;
            if(LockStepFightConfig.openFRandomLog)
                Diagnostic.Log("#Battle#------[Random] frame id: {0}, trigger times: {1}, rand result: {2},BattleInde  :{3}", BattleCommonNet.GetBattleRunFrame()/* MicroMgr.Instance.GetMicroObj().fightContext.director.frameCount*/, _randomCount, retVal, battleIndex);

            return retVal;
        }

        //
        // Public Instance Methods
        // 

        //[G6.G6TraceInfo(8)]
        //public int randomCount { get; set; }

        /*=====================================Next=====================================
        **Returns: An int [0..Int32.MaxValue)
        **Arguments: None
        **Exceptions: None.
        ==============================================================================*/
        public virtual int RandomInt()
        {
            int seed = InternalSample();
#if UNITY_EDITOR && ACGGAME_CLIENT
            m_currentSeed = seed;
#endif

            return seed;
        }

        /*=====================================Next=====================================
        **Returns: An int [minvalue..maxvalue)
        **Arguments: minValue -- the least legal value for the Random number.
        **           maxValue -- One greater than the greatest legal return value.
        **           maxvalue - minvalue < Int32.MaxValue
        **Exceptions: None.
        ==============================================================================*/
        public virtual int RandomInt(int minValue, int maxValue)
        {
            if (minValue >= maxValue)
            {
                return 0;
            }

            long range = (long)maxValue - minValue;
            if (range <= (long)Int32.MaxValue)
            {
                long temp = Sample() * range;
                temp = temp / MBIG;
                return (int)(temp + minValue);
            }
            else
            {
                return 0;
            }
        }


        /*=====================================Next=====================================
        **Returns: An int [0..maxValue)
        **Arguments: maxValue -- One more than the greatest legal return value.
        **Exceptions: None.
        ==============================================================================*/
        public virtual int RandomInt(int maxValue)
        {
            if (maxValue < 0)
            {
                return 0;
            }
            long temp = Sample() * maxValue;
            temp = temp / MBIG;
            return (int)(temp);
        }


        /*=====================================Next=====================================
        **Returns: A fix64 [0..1)
        **Arguments: None
        **Exceptions: None
        ==============================================================================*/
        public virtual Fix64 RandFix64()
        {
            int seed = InternalSample();
#if UNITY_EDITOR && ACGGAME_CLIENT
            m_currentSeed = seed;
#endif

            Fix64 fix64_r = Fix64.FromInt(seed);
            Fix64 fix64_m = Fix64.FromInt(MBIG);
            return fix64_r / fix64_m;
        }


        /*==================================NextBytes===================================
        **Action:  Fills the byte array with random bytes [0..0x7f].  The entire array is filled.
        **Returns:Void
        **Arugments:  buffer -- the array to be filled.
        **Exceptions: None
        ==============================================================================*/
        public virtual void NextBytes(byte[] buffer)
        {
            if (buffer != null)
            {
                for (int i = 0; i < buffer.Length; i++)
                {
                    buffer[i] = (byte)(InternalSample() % (Byte.MaxValue + 1));
                }
            }
        }
    }
}// namespace