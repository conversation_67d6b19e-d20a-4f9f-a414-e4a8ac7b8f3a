using System;
using System.Collections.Generic;

namespace GameFramework.Common
{

    public interface FS_IClassPool
    {
        void Release(FS_PoolableObject objToRelease);

        FS_PoolableObject Obtain();
    }

    public abstract class FS_BaseClassPool : FS_IClassPool
    {
        protected List<object> Pool = new List<object>(128);

        public uint ReqSeq;

        public int Capacity
        {
            get { return this.Pool.Capacity; }

            set { this.Pool.Capacity = value; }
        }

        public abstract void Release(FS_PoolableObject objToRelease);

        public abstract FS_PoolableObject Obtain();
    }

    public abstract class FS_PoolableObject
    {
        public FS_IClassPool PoolHolder;
        public uint UsingSeq = 0;

        public abstract void OnReuse();
        protected abstract void ResetAllFields();
        protected abstract void OnRelease();
        public void Release()
        {
            if (this.PoolHolder != null)
            {
                this.OnRelease();
                this.PoolHolder.Release(this);
            }
        }
    }

    public class FS_ClassPool<T> : FS_BaseClassPool
        where T : FS_PoolableObject, new()
    {

        private static FS_ClassPool<T> instance = null;

        public override FS_PoolableObject Obtain()
        {
            this.ReqSeq++;


            if (this.Pool.Count > 0)
            {
                T instanceToReuse = (T) this.Pool[this.Pool.Count - 1];
                this.Pool.RemoveAt(this.Pool.Count - 1);

                instanceToReuse.OnReuse();
                instanceToReuse.UsingSeq = this.ReqSeq;
                instanceToReuse.PoolHolder = this;
                return instanceToReuse;
            }

            var newInstance = this.NewInstance();
            newInstance.OnReuse();
            return newInstance;
        }


        public void Prealloc(int count)
        {
            for (int index = 0; index < count; index++)
            {
                T newObject = this.NewInstance();
                this.Release(newObject);
            }
        }

        private T NewInstance()
        {
            var newInstance = new T();
            newInstance.UsingSeq = this.ReqSeq;
            newInstance.PoolHolder = this;
            return newInstance;
        }


        public static FS_ClassPool<T> GetInstance()
        {
            return Instance;
        }


        public static FS_ClassPool<T> Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FS_ClassPool<T>();
                }

                return instance;
            }
        }

        public static uint NewSeq()
        {
            Instance.ReqSeq++;
            return Instance.ReqSeq;
        }

        public static T Get()
        {
            return Instance.Obtain() as T;
        }


        public override void Release(FS_PoolableObject objToRelease)
        {

            objToRelease.UsingSeq = 0;

            var objectToPool = objToRelease as T;

            objToRelease.PoolHolder = null;
            this.Pool.Add(objectToPool);
        }
    }
}