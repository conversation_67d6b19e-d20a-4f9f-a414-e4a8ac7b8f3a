///////////////////////////////////////////////////////////////////////////////
/// created by : taodeng
/// time : 2017-5-11 15:41
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.Common
{
    public class BitField
    {
        byte[] mBytes;
        private ushort keyNum;

        public BitField(int numBits)
        {
            keyNum = (ushort) numBits;
            int numBytes = (numBits + 7) / 8;

            if (numBytes > 0)
            {
                mBytes = new byte[numBytes];
            }
        }

        public BitField(byte[] bytes)
        {
            if (bytes.Length > 0)
            {
                mBytes = new byte[bytes.Length];
                bytes.CopyTo(mBytes, 0);
            }
        }

        public BitField(bool[] values)
        {
            if (values.Length > 0)
            {
                int len = (values.Length + 7) / 8;
                mBytes = new byte[len];

                for (int i = 0; i < values.Length; ++i)
                {
                    SetBit(i, values[i]);
                }
            }
        }

        public bool this[int index]
        {
            get { return GetBit(index); }

            set { SetBit(index, value); }
        }

        public void SetAll(bool value)
        {
            byte byteValue = 0;

            if (value)
            {
                byteValue = 0xff;
            }

            for (int i = 0; i < mBytes.Length; ++i)
            {
                mBytes[i] = byteValue;
            }
        }

        public void SetBit(int index, bool value)
        {
            if (index >= 0 && index < bitsLength)
            {
                int bitIndex = index / 8;
                int bitOffset = index % 8;

                if (value)
                {
                    mBytes[bitIndex] = (byte) (mBytes[bitIndex] | (byte) (1 << bitOffset));
                }
                else
                {
                    mBytes[bitIndex] = (byte) (mBytes[bitIndex] & ~((byte) (1 << bitOffset)));
                }
            }
        }

        public bool GetBit(int index)
        {
            if (index >= 0 && index < bitsLength)
            {
                int bitIndex = index / 8;
                int bitOffset = index % 8;

                return (mBytes[bitIndex] & (byte) (1 << bitOffset)) == 0 ? false : true;
            }

            return false;
        }

        public int bytesLength
        {
            get { return mBytes.Length; }
        }

        public int bitsLength
        {
            get { return mBytes.Length * 8; }
        }

        public byte[] bytes
        {
            get { return mBytes; }

            set { mBytes = value; }
        }

        public void CopyTo(BitField other)
        {
            mBytes.CopyTo(other.mBytes, 0);
        }

        public void CopyFrom(byte[] bytes)
        {
            bytes.CopyTo(mBytes, 0);
        }


        public override string ToString()
        {
            string msg = "";
            for (int i = 0; i < keyNum; i++)
            {
                msg += this[i] ? "1" : "0";
            }

            return msg;
        }
    }
}