///////////////////////////////////////////////////////////////////////////////
/// created by : taodeng
/// time :   2017-5-10 20:06
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System.Collections.Generic;

namespace GameFramework.Messaging
{
    public class MessageDispatcher<T> : IMessageDispatcher<T>
    {
        public MessageDispatchedDelegate<T> OnMessageDispatched { get; set; }

        public void AddListener(T msgKey, IMessageListener<T> listener)
        {
            List<IMessageListener<T>> listenerList = GetListenerListByMsgKey(msgKey);

            if (null == listenerList)
            {
                listenerList = new List<IMessageListener<T>>();
                mListeners.Add(msgKey, listenerList);
            }

            listenerList.Add(listener);
        }

        public void RemoveListener(T msgKey, IMessageListener<T> listener)
        {
            List<IMessageListener<T>> listenerList = GetListenerListByMsgKey(msgKey);

            if (null != listenerList)
            {
                listenerList.Remove(listener);

                if (listenerList.Count == 0)
                {
                    mListeners.Remove(msgKey);
                }
            }
        }

        public void RemoveAllListenersOf(T msgKey)
        {
            mListeners.Remove(msgKey);
        }

        public void RemoveAllListeners()
        {
            mListeners.Clear();
        }

        public void Dispatch(Message<T> msg)
        {
            if(null != msg)
            {
                List<IMessageListener<T>> listenerList = GetListenerListByMsgKey(msg.key);

                if (null != listenerList)
                {
                    for (int i = 0; i < listenerList.Count; ++i)
                    {
                        if (null != listenerList[i] && DispatchCheck(listenerList[i], msg))
                        {
                            listenerList[i].OnMessage(msg);
                        }
                    }
                }

                if (null != OnMessageDispatched)
                {
                    OnMessageDispatched(msg);
                }
            }
            else
            {
                // error: attempt to dispatch a null msg.
            }
        }

        protected virtual bool DispatchCheck(IMessageListener<T> listener, Message<T> msg)
        {
            return true;
        }

        List<IMessageListener<T>> GetListenerListByMsgKey(T msgKey)
        {
            List<IMessageListener<T>> listenerList;

            if (mListeners.TryGetValue(msgKey, out listenerList))
            {
                return listenerList;
            }

            return null;
        }

        public void BeginLoop()
        {
        }

        public void EndLoop()
        {
        }

        Dictionary<T, List<IMessageListener<T>>> mListeners = 
            new Dictionary<T, List<IMessageListener<T>>>();
    }
}