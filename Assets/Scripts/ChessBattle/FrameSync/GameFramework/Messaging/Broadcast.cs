///////////////////////////////////////////////////////////////////////////////
/// created by : taodeng
/// time : 2017-5-10 9:53
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using UnityEngine;
using System.Collections;

namespace GameFramework.Messaging
{
    /// <summary>
    /// 广播
    /// </summary>
    public class Broadcast<T>
    {
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="key"></param>
        /// <param name="extraData"></param>
        /// <param name="arg"></param>
        protected virtual void _Post(T key, object data = null)
        {
            if (null != messagePool)
            {
                Message<T> msg = messagePool.Alloc(key, data);

                if (null != msg)
                {
                    msg.seqNum = mNextMsgSeqNum++;

                    PrePostMessage(msg);
                    PostMessage(msg);
                }
            }
        }

        protected virtual void PrePostMessage(Message<T> msg)
        {
        }

        protected virtual void _PostImmediately(T key, object data = null)
        {
            if (null != messagePool)
            {
                Message<T> msg = messagePool.Alloc(key, data);

                if (null != msg)
                {
                    msg.seqNum = mNextMsgSeqNum++;

                    DispatchMessage(msg);
                }
            }
        }

        protected virtual IMessageListener<T> _AddListener(T msgKey, 
            MessageListener<T>.MessageHandler handler)
        {
            IMessageListener<T> listener = new MessageListener<T>(handler);
            _AddListener(msgKey, listener);

            return listener;
        }

        protected virtual IMessageListener<T> _RemoveListener(T msgKey,
            MessageListener<T>.MessageHandler handler)
        {
            IMessageListener<T> listener = new MessageListener<T>(handler);
            _AddListener(msgKey, listener);

            return listener;
        }

        protected virtual void _AddListener(T msgKey, IMessageListener<T> listener)
        {
            if (null != messagePump &&
                null != messagePump.messageDispatcher)
            {
                messagePump.messageDispatcher.AddListener(msgKey, listener);
            }
        }

        protected virtual void _RemoveListener(T msgKey, IMessageListener<T> listener)
        {
            if (null != messagePump &&
                 null != messagePump.messageDispatcher)
            {
                messagePump.messageDispatcher.RemoveListener(msgKey, listener);
            }
        }

        protected virtual void _RemoveAllListeners()
        {
            if (null != messagePump &&
                null != messagePump.messageDispatcher)
            {
                messagePump.messageDispatcher.RemoveAllListeners();
            }
        }

        protected virtual void _MessageLoop()
        {
            if (null != messagePump)
            {
                messagePump.Loop();
            }
        }

        protected virtual void _ClearMessageQueue()
        {
            if (null != messagePump &&
               null != messagePump.messageQueue)
            {
                messagePump.messageQueue.Clear();
            }
        }

        protected virtual void PostMessage(Message<T> msg)
        {
            if (null != messagePump &&
                null != messagePump.messageQueue)
            {
                messagePump.messageQueue.Enqueue(msg);
            }
        }

        protected virtual void DispatchMessage(Message<T> msg)
        {
            if (null != messagePump &&
                null != messagePump.messageDispatcher)
            {
                messagePump.messageDispatcher.Dispatch(msg);
            }
        }

        public Broadcast()
        {
            mPump = new MessagePump<T>();
            mPump.messageQueue = new MessageQueue<T>();
            mPump.messageDispatcher = new MessageDispatcher<T>();
            mPump.messageDispatcher.OnMessageDispatched = OnMessageDispatched;
        }

        public Broadcast(MessagePump<T> pump)
        {
            mPump = pump;
        }

        protected void OnMessageDispatched(Message<T> msg)
        {
            //发送完成之后，将message回收
            //回收的是包装，里面的内容已经在Dispatch函数里面拿走了.
            messagePool.Free(msg);
        }

        public MessagePump<T> messagePump { get { return mPump; } }
        MessagePump<T> mPump;

        public IMessagePool<T> messagePool
        {
            get
            {
                if (null == mCurrentPool)
                {
                    return mDefaultPool;
                }
                else
                {
                    return mCurrentPool;
                }
            }

            set
            {
                mCurrentPool = value;
            }
        }

        IMessagePool<T> mCurrentPool;
        MessagePool<T> mDefaultPool = new MessagePool<T>();

        int mNextMsgSeqNum;
    }
}
