///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-8-28 15:44
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GameFramework.Network
{
    public class ConstantDelay : IDelayStrategy
    {
        public ConstantDelay()
        {
            delayTime = 0;
        }

        public ConstantDelay(int delayTime)
        {
            this.delayTime = delayTime;
        }

        public int CalcDelayTime(DelayFilter delayFilter)
        {
            return delayTime;
        }

        public int delayTime { get; set; }
    }
}
