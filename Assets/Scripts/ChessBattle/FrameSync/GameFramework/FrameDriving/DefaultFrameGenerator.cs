///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-4-11 41:50
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GameFramework.FrameDriving
{
    using Common;

    /// <summary>
    /// 默认的帧生成器实现。
    /// </summary>
    public class DefaultFrameGenerator : FrameGeneratorBase, IFrameGenerator
    {
        /// <summary>
        /// 是否为固定帧间隔。
        /// </summary>
        public bool fixedFrameInterval { get; set; }

        /// <summary>
        /// 不用fps，而使用帧间隔的目的是，把时间片的具体毫秒数值交给用户处理。
        /// </summary>
        public int frameInterval { get; set; }

        int lastFrameTime = 0;

        /// <summary>
        /// 生成帧。
        /// 默认的帧生成器提供两种基本策略：
        /// 1.浮动时间间隔。
        /// 2.固定时间间隔，类似unity的fixedupdate，会将时间切成相等的片。
        /// 注意：
        /// 有一些特别的帧，由于前一帧卡太久（加载），导致时间片过大，有时候需要过滤掉。
        /// 默认的实现，暂未处理。
        /// </summary>
        /// <param name="frameDriver"></param>
        /// <param name="gameTime"></param>
        public void GenerateFrames(FrameDriver frameDriver, int elapsedTime)
        {
            int elpsedTime = frameDriver.totalTime - lastFrameTime;

            //frameDriver.director.time
            if (fixedFrameInterval) // 固定帧间隔
            {
                //1.计算出当前时间到上帧更新时间的间隔，看可以生产多少帧
                int frameCount = elpsedTime / frameInterval;

                // 2.生成帧
                for (int i = 0; i < frameCount; ++i)
                {
                    GenerateOneFrame(frameDriver, frameInterval, null, 0);
                }

                //3.记录消耗掉的时间
                lastFrameTime += frameCount * frameInterval;
            }
            else  //非固定帧间隔
            {
                if (elpsedTime >= frameInterval)
                {
                    GenerateOneFrame(frameDriver, elpsedTime, null, 0);

                    // 更新时间
                    lastFrameTime = frameDriver.totalTime;
                }
                else
                {
                }
            }
        }

        /// <summary>
        /// 帧被dispath之后的回调。
        /// </summary>
        /// <param name="frame"></param>
        public void OnFrameDispatched(Frame frame)
        {
        }

        public void PauseFrame(bool bPause)
        {
        }
    }
}


