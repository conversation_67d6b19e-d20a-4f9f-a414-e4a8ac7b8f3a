
#if !ACGGAME_CLIENT

namespace GameFramework.FrameDriving
{
    public class FrameDriver
    {
        public delegate void FrameDelegate(Frame frame);
        public FrameDelegate onFrameCallback { get; set; }
        public int frameAccuTime { get; set; }
        public int totalTime { get; set; }

        /// <summary>
        /// 设置查询最大缓存的帧数量
        /// </summary>
        private int _maxFrameCount;

        public FrameDriver()
        {
            _maxFrameCount = 50;
        }
        /// <summary>
        /// 执行一帧的回调。
        /// </summary>
        /// <param name="frame"></param>
        public void DoFrame(Frame frame)
        {
            TriggerOnFrame(frame);
        }
        
        public void Reset()
        {
            frameAccuTime = 0;
            totalTime = 0;
        }

        void TriggerOnFrame(Frame frame)
        {
            if (null != onFrameCallback)
            {
                onFrameCallback(frame);
            }
        }

        public void Update(int elapsedTime)
        {
            totalTime += elapsedTime;

            // 产生帧
            if (null != frameGenerator)
            {
                frameGenerator.GenerateFrames(this, elapsedTime);
            }

            // 消费帧
            if (null != frameDispatcher)
            {
                frameDispatcher.DispatchFrames(this);
            }
        }

        /// <summary>
        /// 可插的帧队列。
        /// </summary>
        public IFrameQueue frameQueue { get; set; }

        /// <summary>
        /// 可插的帧产生器。
        /// </summary>
        public IFrameGenerator frameGenerator { get; set; }

        /// <summary>
        /// 可插的帧分发器。
        /// </summary>
        public IFrameDispatcher frameDispatcher { get; set; }

        /// <summary>
        /// 可插的帧产生器。
        /// </summary>
        public IFramePool framePool { get; set; }


        public bool IsFrameQueueFull(int addCount = 0)
        {
            return frameQueue.Count + addCount >= _maxFrameCount;
        }
    }
}

#endif
