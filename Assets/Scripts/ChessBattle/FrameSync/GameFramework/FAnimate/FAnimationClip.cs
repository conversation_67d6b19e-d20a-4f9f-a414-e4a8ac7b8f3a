///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-7-11 16:36
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;

namespace GameFramework.FAnimate
{
    using FMath;

    public class FAnimationClip
    {
        public FAnimationWrapMode wrapMode { get; set; }
        public int startTime { get; set; }
        public int stopTime { get; set; }

        //public Fix64 sampleRate { get; set; }

        /// <summary>
        /// length of clip = max(curve[i].length, i = 0 to curveCount - 1)
        /// </summary>
        public int length
        {
            get
            {
                return stopTime - startTime;
                //int maxLen = 0;

                //fix64Curves.ForEach((FAnimationCurveFix64 curve) =>
                //{
                //    if (curve.lastFrameTime > maxLen)
                //    {
                //        maxLen = curve.lastFrameTime;
                //    }
                //});

                //// FVector2 curves.
                //vector2Curves.ForEach((FAnimationCurveFVec2 curve) =>
                //{
                //    if (curve.lastFrameTime > maxLen)
                //    {
                //        maxLen = curve.lastFrameTime;
                //    }
                //});

                //// FVector3 curves.
                //vector3Curves.ForEach((FAnimationCurveFVec3 curve) =>
                //{
                //    if (curve.lastFrameTime > maxLen)
                //    {
                //        maxLen = curve.lastFrameTime;
                //    }
                //});

                //// FQuaternion curves.
                //quaternionCurves.ForEach((FAnimationCurveFQuaternion curve) =>
                //{
                //    if (curve.lastFrameTime > maxLen)
                //    {
                //        maxLen = curve.lastFrameTime;
                //    }
                //});

                //return maxLen;

            }
        }

        #region bind

        public void Bind(object target)
        {
            // fix64 curves.
            fix64Curves.ForEach((FAnimationCurveFix64 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Bind(target, curve.path, curve.type, curve.name);
                }
            });

            // FVector2 curves.
            vector2Curves.ForEach((FAnimationCurveFVec2 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Bind(target, curve.path, curve.type, curve.name);
                }
            });

            // FVector3 curves.
            vector3Curves.ForEach((FAnimationCurveFVec3 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Bind(target, curve.path, curve.type, curve.name);
                }
            });

            // FQuaternion curves.
            quaternionCurves.ForEach((FAnimationCurveFQuaternion curve) =>
            {
                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Bind(target, curve.path, curve.type, curve.name);
                }
            });
        }

        #endregion

        #region Sample

        public void Start()
        {
            // fix64 curves.
            fix64Curves.ForEach((FAnimationCurveFix64 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Start();
                }
            });

            // FVector2 curves.
            vector2Curves.ForEach((FAnimationCurveFVec2 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Start();
                }
            });

            // FVector3 curves.
            vector3Curves.ForEach((FAnimationCurveFVec3 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Start();
                }
            });

            // FQuaternion curves.
            quaternionCurves.ForEach((FAnimationCurveFQuaternion curve) =>
            {
                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Start();
                }
            });
        }

        public void Stop()
        {
            // fix64 curves.
            fix64Curves.ForEach((FAnimationCurveFix64 curve) =>
            {
                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Stop();
                }
            });

            // FVector2 curves.
            vector2Curves.ForEach((FAnimationCurveFVec2 curve) =>
            {
                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Stop();
                }
            });

            // FVector3 curves.
            vector3Curves.ForEach((FAnimationCurveFVec3 curve) =>
            {
                // 使用线性插值.
                curve.interpolator = FInterpolate.Lerp;

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Stop();
                }
            });

            // FQuaternion curves.
            quaternionCurves.ForEach((FAnimationCurveFQuaternion curve) =>
            {
                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    binding.Stop();
                }
            });
        }

        public void Sample(int timeSinceAnimationStart)
        {
            int time = 0;

            if (length > 0)
            {
                // 默认时间从0开始，到最后一帧结束.
                time = FAnimationWrapTimeUtils.WrapTime(
                    (int)timeSinceAnimationStart,
                    startTime, stopTime, wrapMode);
            }

            // fix64 curves.
            fix64Curves.ForEach((FAnimationCurveFix64 curve)=>
            {
                Fix64 val = curve.Evaluate(time);

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    curve.binding.Solve(val);
                }
            });

            // FVector2 curves.
            vector2Curves.ForEach((FAnimationCurveFVec2 curve) =>
            {
                FVector2 val = curve.Evaluate(time);

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    curve.binding.Solve(val);
                }
            });

            // FVector3 curves.
            vector3Curves.ForEach((FAnimationCurveFVec3 curve) =>
            {
                FVector3 val = curve.Evaluate(time);

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    curve.binding.Solve(val);
                }
            });

            // FQuaternion curves.
            quaternionCurves.ForEach((FAnimationCurveFQuaternion curve) =>
            {
                FQuaternion val = curve.Evaluate(time);

                // binding
                IFAnimationCurveBinding binding = curve.binding;

                if (null != curve.binding)
                {
                    curve.binding.Solve(val);
                }
            });
        }

        #endregion

        #region curves

        public void AddFix64Curve(FAnimationCurveFix64 curve, string path)
        {
            curve.path = path;
            fix64Curves.Add(curve);
        }

        public void AddVector2Curve(FAnimationCurveFVec2 curve, string path)
        {
            curve.path = path;
            vector2Curves.Add(curve);
        }

        public void AddVector3Curve(FAnimationCurveFVec3 curve, string path)
        {
            curve.path = path;
            vector3Curves.Add(curve);
        }

        public void AddVectorQuaterionCurve(FAnimationCurveFQuaternion curve, string path)
        {
            curve.path = path;
            quaternionCurves.Add(curve);
        }

        public List<FAnimationCurveFix64> fix64Curves { get { return mFix64Curves; } }
        public List<FAnimationCurveFVec2> vector2Curves { get { return mVector2Curves; } }
        public List<FAnimationCurveFVec3> vector3Curves { get { return mVector3Curves; } }
        public List<FAnimationCurveFQuaternion> quaternionCurves { get { return mQuaternionCurve; } }

        List<FAnimationCurveFix64> mFix64Curves = new List<FAnimationCurveFix64>();
        List<FAnimationCurveFVec2> mVector2Curves = new List<FAnimationCurveFVec2>();
        List<FAnimationCurveFVec3> mVector3Curves = new List<FAnimationCurveFVec3>();
        List<FAnimationCurveFQuaternion> mQuaternionCurve = new List<FAnimationCurveFQuaternion>();
        #endregion
    }
}
