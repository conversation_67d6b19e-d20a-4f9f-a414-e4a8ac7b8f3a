///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-5-15 22:53
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;

namespace GameFramework.StateSynchronization
{
    public class SyncObject
    {
        public delegate void MasterChangeDelegate(SyncObject sender);

        public MasterChangeDelegate OnBecameMaster;
        public MasterChangeDelegate OnBecameSlave;

        public delegate void DestroyDelegate(SyncObject sender);
        public DestroyDelegate destroyEvent;

        // 序列号一旦创建就不能再改变了(用于跟踪pool复用)
        public uint serialNum { get; set; }

        public static SyncObject Create()
        {
            StateSyncContext syc = StateSyncContext.inst;
            ulong id = syc.objectIdProvider.Generate();

            return Create(id);
        }

        public static SyncObject Create(ulong id)
        {
            StateSyncContext syc = StateSyncContext.inst;
            SyncObject obj = syc.syncObjectPool.Alloc(id);

            if (null != obj && null != syc.scene)
            {
                syc.scene.Add(obj);
            }

            return obj;
        }

        //public static SyncObject FindObject(string name)
        //{
        //    return StateSyncContext.inst.scene.FindObject((SyncObject obj) =>
        //    {
        //        return string.IsNullOrEmpty(obj.name) && string.IsNullOrEmpty(name) &&
        //            obj.tag.Equals(name);
        //    });
        //}

        public static SyncObject FindObjectById(ulong id)
        {
            return StateSyncContext.inst.scene.FindObject((SyncObject obj)=>
            {
                return obj.objectId == id;
            });
        }

        public void TriggerDestroyEvent()
        {
            if (null != destroyEvent)
            {
                destroyEvent(this);
            }
        }

        public static void Destroy(SyncObject obj)
        {
            if (null != obj)
            {
                obj.OnDestroy();
                obj.TriggerDestroyEvent();
                obj.scene.Remove(obj);
                StateSyncContext.inst.syncObjectPool.Free(obj);
            }
        }

        public static List<SyncObject> masters
        {
            get { return StateSyncContext.inst.scene.masters; }
        }

        public static List<SyncObject> slaves
        {
            get { return StateSyncContext.inst.scene.slaves; }
        }

        public static SyncObject FindMasterById(ulong id)
        {
            return StateSyncContext.inst.scene.FindObject((SyncObject obj) =>
            {
                return obj.objectId == id;
            }, true, false);
        }

        public static SyncObject FindSlaveById(ulong id)
        {
            return StateSyncContext.inst.scene.FindObject((SyncObject obj) =>
            {
                return obj.objectId == id;
            }, false, true);
        }

        public string name { get; set; }
        public string tag { get; set; }
        public object userData { get; set; }
        public bool isMaster { get; protected set; }
        public ulong objectId { get; set; }
        public SyncScene scene { get; set; }

        public void SetMaster(bool bMaster)
        {
            if (isMaster != bMaster)
            {
                isMaster = bMaster;

                if (bMaster)
                {
                    if (null != scene)
                    {
                        scene.RemoveFromSlaveList(this);
                        scene.AddToMasterList(this);
                    }

                    if (null != OnBecameMaster)
                    {
                        OnBecameMaster(this);
                    }
                }
                else
                {
                    if (null != scene)
                    {
                        scene.RemoveFromMasterList(this);
                        scene.AddToSlaveList(this);
                    }

                    if (null != OnBecameSlave)
                    {
                        OnBecameSlave(this);
                    }
                }
            }
        }

        public SyncObject()
        {
        }

        public SyncObject(ulong id)
        {
            objectId = id;
        }

        public void Read(ISyncStream stream, SyncReadWriteContext context)
        {
            uint componentCount = context.readWrite.ReadComponentCount(stream, context); 

            for (int i = 0; i < componentCount; ++i)
            {
                uint typeId = context.readWrite.ReadComponentTypeId(stream, context);
                uint contentSize = context.readWrite.ReadComponentContentSize(stream, context);

                if (contentSize==0)
                {
                    continue;
                }

                SyncComponent sc = GetComponentWithTypeId(typeId);

                if (null != sc)
                {
                    if (null != context.componentFilter)
                    {
                        if (context.componentFilter.DoFilter(sc))
                        {
                            context.readWrite.ReadComponent(sc, stream, context);
                        }
                    }
                    else
                    {
                        context.readWrite.ReadComponent(sc, stream, context);
                    }
                }
                else
                {
                    // skip over
                    stream.Seek((int)contentSize, SyncStreamSeekOrigin.Current);
                }
            }
        }

        public void Write(ISyncStream stream, SyncReadWriteContext context)
        {
            uint componentCountBlockStart = stream.position;
            context.readWrite.WriteComponentCount(0, stream, context);

            uint componentCount = 0;

            mComponents.ForEach((SyncComponent component) =>
            {
                if (null != context.componentFilter)
                {
                    if (context.componentFilter.DoFilter(component))
                    {
                        WriteComponent(component, stream, context);
                        ++componentCount;
                    }
                }
                else
                {
                    WriteComponent(component, stream, context);
                    ++componentCount;
                }
            });

            uint componentCountBlockEnd = stream.position;

            stream.position = componentCountBlockStart;
            context.readWrite.WriteObjectCount(componentCount, stream, context);
            stream.position = componentCountBlockEnd;
        }

        void WriteComponent(SyncComponent component, ISyncStream stream,
            SyncReadWriteContext context)
        {
            // begin
            context.readWrite.WriteComponentTypeId(component.typeId,
                stream, context);

            uint beginPos = stream.position;
            context.readWrite.WriteComponentContentSize(component.typeId,
                stream, context);

            // write component.
            context.readWrite.WriteComponent(component,
                stream, context);

            // end
            uint blockEnd = stream.position;

            stream.position = beginPos;
            uint contentSize = blockEnd - beginPos - sizeof(uint);
            context.readWrite.WriteComponentContentSize(contentSize, stream, context);
            stream.position = blockEnd;
        }

        /// <summary>
        /// adds a component to object.
        /// </summary>
        /// <param name="component"></param>
        public void AddComponent(SyncComponent component)
        {
            mComponents.Add(component);
        }

        /// <summary>
        /// 通过typeid取得Component.
        /// </summary>
        /// <param name="typeId"></param>
        /// <returns></returns>
        public SyncComponent GetComponentWithTypeId(uint typeId)
        {
            return mComponents.Find(
                (SyncComponent component) => 
                {
                    return component.typeId == typeId;
                });
        }

        public void OnDestroy()
        {
            mComponents.ForEach((SyncComponent component) =>
            {
                component.OnDestroy();
            });
            mComponents.Clear();
        }

        public SyncComponent FindComponent(Predicate<SyncComponent> match)
        {
            return mComponents.Find(match);
        }

        public void ForEachComponent(Action<SyncComponent> action)
        {
            mComponents.ForEach(action);
        }
        
        List<SyncComponent> mComponents = new List<SyncComponent>();
    }
}
