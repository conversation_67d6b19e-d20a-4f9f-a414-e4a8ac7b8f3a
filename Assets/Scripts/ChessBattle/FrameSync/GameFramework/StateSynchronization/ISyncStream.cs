///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-5-15 22:51
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.StateSynchronization
{
    using FMath;

     /// <summary>
     /// seek的起始位置.
     /// </summary>
     public enum SyncStreamSeekOrigin        
     {
        Begin = 0,
        Current = 1,
        End = 2,
    }

    public interface ISyncStream
    {
        #region seek

        bool CanRead { get; }
        bool CanWrite { get; }

        /// <summary>
        /// 返回数据buffer.
        /// </summary>
        /// <returns></returns>
        byte[] GetBuffer();

        /// <summary>
        /// 取得当前读写指针的位置.
        /// </summary>
        uint position { get; set; }

        /// <summary>
        /// 取得当前数据的长度.
        /// </summary>
        uint length { get; }

        /// <summary>
        /// sets length of current stream.
        /// </summary>
        /// <param name="value"></param>
        void SetLength(uint value);

        /// <summary>
        /// 移动当前的读写指针.
        /// </summary>
        /// <param name="origin"></param>
        /// <param name="offset"></param>
        void Seek(int offset, SyncStreamSeekOrigin origin);

        #endregion seek

        #region primitive read

        /// <summary>
        /// read byte buffer from stream.
        /// </summary>
        /// <param name="recvBuffer">receive buffer</param>
        /// <param name="recvBufferOffset">offset of receiver buffer.</param>
        /// <param name="count">byte number of reading.</param>
        void Read(byte[] recvBuffer, uint recvBufferOffset, uint count);
        bool ReadBoolean();
        byte ReadByte();
        char ReadChar();
        short ReadShort();
        ushort ReadUShort();
        int ReadInt();
        uint ReadUInt();
        long ReadLong();
        ulong ReadULong();
        float ReadSingle();
        double ReadDouble();
        Fix64 ReadFix64();

        #endregion read

        #region primitive write

        /// <summary>
        /// write binary data to stream.
        /// </summary>
        /// <param name="data"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        void Write(byte[] data, int offset, int count);
        void Write(byte[] data);

        void WriteBoolean(bool value);
        void WriteByte(byte value);
        void WriteChar(char value);
        void WriteShort(short value);
        void WriteUShort(ushort value);
        void WriteInt(int value);
        void WriteUInt(uint value);
        void WriteLong(long value);
        void WriteULong(ulong value);
        void WriteSingle(float value);
        void WriteDouble(double value);
        void WriteFix64(Fix64 value);

        #endregion write
    }
}
