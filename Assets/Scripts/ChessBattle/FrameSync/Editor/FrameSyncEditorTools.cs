#if ACGGAME_CLIENT
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using GameFramework.Common;
using GameFramework.FrameSync;
using GCloud;
using GCloud.LockStep;
using GloryLockStep.Room;
using TKPlugins;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using Wup.Jce;
using ZGameChess;
using ZGameClient;

public class FrameSyncEditorTools : MonoBehaviour
{
    // Start is called before the first frame update
    private static RoomSerialize roomSerialize1;
    private static StreamWriter steam1;

    private static RoomSerialize roomSerialize2;
    private static StreamWriter steam2;

    private static FrameInfoWrapper _infoWrapper;
    private static StringBuilder _stringBuilder = new StringBuilder();
    private static RelayData _relayData;

    public static readonly string PATH = Application.streamingAssetsPath;


    private class FrameDataInfo
    {
        public int type;
        public int count;
        public int totalSize;
        public float avgSize;
    }
}
#endif