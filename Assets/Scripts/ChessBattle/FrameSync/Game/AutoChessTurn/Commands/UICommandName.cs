
public class UICommandId
{
    public const string AllGameData = "AllGameData";
    public const string GameStart = "GameStart";
    public const string GameEnd = "GameEnd";
    public const string ShowFirstAllotChess = "ShowFirstAllotChess";
	public const string ExchangeChess = "ExchangeChess";
	public const string TurnStart = "TurnStart";
    public const string BattleAllFinished = "BattleAllFinished";
    public const string TurnAckBattleField= "TurnAckBattleField";
    public const string TurnArrive= "TurnArrive";
	public const string TurnStartPlay= "TurnStartPlay";
	public const string TurnFightOverTime= "TurnFightOverTime";
	public const string TurnPlayBattleEnd= "TurnPlayBattleEnd";
	public const string TurnDeparture= "TurnDeparture";
    public const string TurnDepartureDelay = "TurnDepartureDelay";
	public const string TurnStartDraft= "TurnStartDraft";
	public const string TurnLeaveDraft= "TurnLeaveDraft";
	public const string TurnEndDraft= "TurnEndDraft";
	public const string TurnUserOut= "TurnUserOut";
	public const string BuyChess= "BuyChess";
    public const string BuySkill = "BuySkill";
    public const string BuyBuff = "BuyBuff";
    public const string SellChess= "SellChess";
    public const string SellChessRecover = "SellChessRecover";
    public const string RefreshChess= "RefreshChess";
	public const string RefreshStatus= "RefreshStatus";
    public const string RemoveHero= "RemoveHero";
    public const string AddHero= "AddHero";
    public const string ChangeHero= "ChangeHero";
    public const string TransferHeros = "TransferHeros";
    public const string StarUpgrade= "StarUpgrade";
    public const string UpHero= "UpHero";
	public const string MoveHero= "MoveHero";
	public const string BuyToBattleGround= "BuyToBattleGround";
    public const string  DownHero= "DownHero";
    public const string MoveWaitHero= "MoveWaitHero";
    public const string WaitHeroPromotion= "WaitHeroPromotion";
    public const string WaitHeroPromotionFinish= "WaitHeroPromotionFinish";

    public const string BuyChessToBattleGround= "BuyChessToBattleGround";
    public const string UpdatePlayerLevel= "UpdatePlayerLevel";
    public const string AutoUp= "AutoUp";
    public const string DropEquipment= "DropEquipment";
    public const string MonsterDrop= "MonsterDrop";
    public const string PickUpMonsterDrop= "PickUpMonsterDrop";
    public const string DropCoin= "DropCoin";
    public const string StartPickUpDropCoin= "StartPickUpDropCoin";
    public const string PickUpDropCoin= "PickUpDropCoin";
    public const string EquipEquipment= "EquipEquipment";
    public const string SynthesisPlayerEquipment= "SynthesisPlayerEquipment";
    public const string  UpdatePlayerEquipment= "UpdatePlayerEquipment";
    public const string UpdateHeroEquipment= "UpdateHeroEquipment";
    public const string UnquipExclusiveEquipment= "UnquipExclusiveEquipment";
    public const string BroadCast= "BroadCast";
    public const string OtherHero= "OtherHero";
    public const string OtherMoney= "OtherMoney";
    public const string OtherLife= "OtherLife";
    public const string OtherWin= "OtherWin";
    public const string SwitchPlayer= "SwitchPlayer";
    public const string OtherMaxHeroNum= "OtherMaxHeroNum";
    public const string TurnToChooseHero= "TurnToChooseHero";
    public const string ChooseHero= "ChooseHero";
    public const string TurnPlayBattleResult= "TurnPlayBattleResult";
    public const string MonsterWin = "MonsterWin";
    public const string TurnPlayerDeductLife= "TurnPlayerDeductLife";
    public const string DropBox= "DropBox";
    public const string OpenDropBox= "OpenDropBox";
    public const string ReplayData= "ReplayData";
    public const string BuyHeroViewStatus= "BuyHeroViewStatus";
    public const string SortWaitHero= "SortWaitHero";
    public const string SortWaitHeroStatus= "SortWaitHeroStatus";
    public const string SendHeroExpression= "SendHeroExpression";
    public const string SendTinyExpression = "SendTinyExpression";
    public const string SendMagicExpression = "SendMagicExpression";
    public const string SendHeroAction = "SendHeroAction";
    public const string SendHeroSwitchModel = "SendHeroSwitchModel";
    public const string SendChatMsg = "SendChatMsg";
    public const string SwitchDropBoxSkin = "SwitchDropBoxSkin";
    public const string HextechHeroIDChange= "HextechHeroIDChange";
    public const string ExitGame= "ExitGame";
    public const string DeleteHeroEquipment= "DeleteHeroEquipment";
    public const string DropHero= "DropHero";
    public const string CloneHeroForEquipment= "CloneHeroForEquipment";
    public const string RecoverFromMoveHero = "RecoverFromMoveHero";
    public const string RecoverFromMoveWaitHero = "RecoverFromMoveWaitHero";
    public const string RecoverFromAllHeros = "RecoverFromAllHero";

    public const string AddCoin= "AddCoin";
    public const string AddEquipment= "AddEquipment";
    public const string OponentEquipmentStatus = "OponentEquipmentStatus";
    public const string AutoPickGoldCoinsStatus = "AutoPickGoldCoinsStatus";
    public const string AutoPickGoldCoins = "AutoPickGoldCoins";
    public const string AutoPickSpacePirateCoins = "AutoPickSpacePirateCoins";
    public const string StealEquipments = "StealEquipments";
    public const string SurplusHero = "SurplusHero";
    public const string BuyHeroInfoChange = "BuyHeroInfoChange";
    public const string ResetPlayerUnit = "ResetPlayerUnit";

    public const string NotifyBuyEquipment = "NotifyBuyEquipment";
    public const string NotifyEquipmentSellList = "NotifyEquipmentSellList";
    public const string PlayerRank = "PlayerRank";
    public const string SortRank = "SortRank";

    public const string NotifyUpdateFetterMergeHero = "NotifyUpdateFetterMergeHero";

    public const string PlayerBloodChange = "PlayerBloodChange";

    public const string Matching = "Matching";

    public const string HundredPanelCountDown = "HundredPanelCountDown";

    public const string ChangeRefreshPrice = "ChangeRefreshPrice";
    public const string SummerWaitHero = "SummerWaitHero";
    //
    public const string BuddChild = "BuddChild";
    //
    public const string DragonEggs = "DragonEggs";
    //
    public const string MercenaryChest = "MercenaryChest";
    public const string MercenaryDice = "MercenaryDice";
    //
    public const string YordleSummon = "YordleSummon";
    // 
    public const string LogReport = "LogReport";
    public const string LogClear = "LogClear";
    // 
    public const string NotifyObservePlayer = "NotifyObservePlayer";
    // 
    public const string NotifyPlayerChange = "NotifyPlayerChange";
    // 
    public const string NotifyObserveChange = "NotifyObserveChange";
    
    // 
    public const string WarmChangeHeroKuID = "WarmChangeHeroKuID";

    // 
    public const string TeamMoney = "TeamMoney";
    //
    public const string ShowTeamFetter = "ShowTeamFetter";
    //
    public const string ShowTeamHero = "ShowTeamHero";

    // 
    public const string MsgPack = "MsgPack";
    
    // 
    public const string ChangeControlWay = "NotifyChangeControlWay";

    // 
    public const string TurnTransferMsg = "TurnTransferMsg";

    // 
    public const string MapEventMsg = "MapEventMsg";

    // 
    public const string DualTinyTransfer = "DaulTinyTransfer";
    
    // 
    public const string CommonMsg = "CommonMsg";

    //
    public const string TriggerMapLogicArea = "TriggerMapLogicArea";
}

public class UICommandSubName
{
    public const string None = "";
    public const string ExchangeHero = "TAC_ReqExchangeHero";
    public const string NotifyUpdateFettersDynamic = "NotifyUpdateFettersDynamic";
    public const string NotifyMergeInfoChange = "NotifyMergeInfoChange";
    public const string NotifyAllEquipments = "NotifyAllEquipments";
    public const string NotifyMateAllEquipments = "NotifyMateAllEquipments";
    public const string NotifyOutUserCount = "NotifyOutUserCount";
    public const string UpdateNpcPlayer = "UpdateNpcPlayer";
    public const string NotifyUpdateNPCEquipments = "NotifyUpdateNPCEquipments";
    public const string NotifyAttrExChange = "NotifyAttrExChange";
    public const string WhenEquipFetter = "WhenEquipFetter";
    public const string NotifyRandomBuffInfo = "NotifyRandomBuffInfo";
    public const string NotifyDropPosDataMapInfo = "NotifyDropPosDataMapInfo";
    public const string NotifyMatchRank = "NotifyMatchRank";
    public const string NotifyGuideResetHp = "NotifyGuideResetHp";
    public const string NotifyTeamRank = "NotifyTeamRank";
    public const string NotifyCloneFakeHero = "NotifyCloneFakeHero";
    public const string NotifyLevelUpTimer = "NotifyLevelUpTimer";
    public const string NotifyReplaceEquipment = "NotifyReplaceEquipment";
    
    public const string NotifyOpenRecruitPanel = "NotifyOpenRecruitPanel";
    
    public const string NotifyNoCntLimitInfo = "NotifyNoCntLimitInfo";
    
    public const string HeroChangeOneTimeEquipment = "HeroChangeOneTimeEquipment";
    public const string OneTimeEquipmentState = "OneTimeEquipmentState";
    public const string OneTimeEquipmentAudio = "OneTimeEquipmentAudio";
    public const string NotifyAddHeroToBattle = "NotifyAddHeroToBattle";
    public const string NotifyAddHeroToWait = "NotifyAddHeroToWait";
    public const string NotifyAddOutFieldUnit = "NotifyAddOutFieldUnit";
    public const string NotifyRemoveOutFieldUnit = "NotifyRemoveOutFieldUnit";
    public const string NotifyTimeMachine = "NotifyTimeMachine";
    public const string NotifyRuinedLegionCtl_S5 = "NotifyRuinedLegionCtl_S5";
    public const string NotifyRuinedLegionCtl_Move_S5 = "NotifyRuinedLegionCtl_Move_S5";
    public const string NotifyIFixMsg = "NotifyIFixMsg";
    public const string NotifyHeroRecordDataChange = "NotifyHeroRecordDataChange";
    public const string NotifySingleHeroRecordDataChange = "NotifySingleHeroRecordDataChange";
    public const string NotifyEnterGameFetterRandomBuffInfo = "NotifyEnterGameFetterRandomBuffInfo";
    public const string NotifyHeroPool_S6 = "NotifyHeroPool_S6";
    public const string NotifyGluttonCfg_S6 = "NotifyGluttonCfg_S6";
    public const string NotifyEatHeroList_S6 = "NotifyEatHeroList_S6";
    public const string NotifyEatHeroResult_S6 = "NotifyEatHeroResult_S6";
    public const string NotifyUpdateHeroFetterDynamicCnt = "NotifyUpdateHeroFetterDynamicCnt";
    public const string NotifyForeverDataDict = "NotifyForeverDataDict";
    
    
    public const string NotifyUpdateMaxHeroNum = "NotifyUpdateMaxHeroNum";
	
    
    
    public const string NotifyAllCombatEffectInfo = "NotifyAllCombatEffectInfo";
    public const string NotifyCombatEffectInfo = "NotifyCombatEffectInfo";
    public const string NotifyTerrainGridInfo = "NotifyTerrainGridInfo";
    public const string NotifyLastRoundActiveFettersInfo = "NotifyLastRoundActiveFettersInfo";

    

    public const string NotifyHAColliderFactor = "NotifyHAColliderFactor";

    public const string NotifyHAAddExtraEquip = "NotifyHAAddExtraEquip";
    public const string NotifyHARemoveExtraEquip = "NotifyHARemoveExtraEquip";
    
    

    public const string NotifyHeavenChooserS4 = "NotifyHeavenChooserS4";
    public const string NotifyHeavenChooserS4_Debug = "NotifyHeavenChooserS4_Debug";
    //匹配对手数据
    public const string NotifyPreMatchData = "NotifyPreMatchData";
    public const string NotifyMatchTable = "NotifyMatchTable";
    public const string NotifyChosenList = "NotifyChosenList";

    public const string NotifyScopdeData = "NotifyScopdeData";
    
    // 限制英雄相关
    public const string NotifyLimitedHero = nameof(NotifyLimitedHero);
    public const string NotifyCommonLimitedHero = nameof(NotifyCommonLimitedHero);
    public const string NotifyLimitedHeroOpenTime = nameof(NotifyLimitedHeroOpenTime);

    public const string NotifyAddLimitedHero = nameof(NotifyAddLimitedHero);
    //s6.5 执事
    public const string NotifyDeboairData = "NotifyDeboairData";
    public const string NotifyDeboairData_Debug = "NotifyDeboairData_Debug";

    //S7 吟游诗人
    public const string NotifyBardData = "NotifyBardData";
    public const string NotifyZoeData_S7 = "NotifyZoeData_S7";
    
    //TFT双人小队存活状态
    public const string NotifyDualTeamState = "NotifyDualTeamState";
    //TFT双人小队投降消息
    public const string NotifyDualSurrenderVote = "NotifyDualSurrenderVote";

    //Team玩法投降消息
    public const string NotifyTeamSurrenderVote = "NotifyTeamSurrenderVote";

    //S7 驯龙师
    public const string NotifyDragonTrainer = "NotifyDragonTrainer";
    //S7 星系刷新特效
    public const string NotifyAstralFX = "NotifyAstralFX";
    //S8小天才
    public const string NotifyGadgeteensFx = "NotifyGadgeteensFx";
    public const string NotifyGadgeteensGroundFx = "NotifyGadgeteensGroundFx";
    
    //战场规则相关
    public const string NotifyBattleRulesKeyData = nameof(NotifyBattleRulesKeyData);
    public const string NotifyBattleRulesEffData = nameof(NotifyBattleRulesEffData);
    public const string NotifyBattleRulesJobData = nameof(NotifyBattleRulesJobData);
    public const string NotifyBattleRulesOtherData = nameof(NotifyBattleRulesOtherData);
    public const string NotifyBattleRulesOtherIntData = nameof(NotifyBattleRulesOtherIntData);
    public const string NotifyBattleRulesDeductHPData = nameof(NotifyBattleRulesDeductHPData);

    //战场规则相关
    public const string NotifyDropCountTip = nameof(NotifyDropCountTip);
    
    //龙全局特效
    public const string NotifyDragon_EFF_S7 = "NotifyDragon_EFF_S7";
    
    //PrimeMsg
    public const string NotifyPrime_Data = "NotifyPrime_Data";
    public const string NotifyPrime_Msg = "NotifyPrime_Msg";
    public const string NotifyPrime_Error_Msg = "NotifyPrime_Error_Msg";
    public const string NotifyPrime_Error_Equip_Msg = "NotifyPrime_Error_Equip_Msg";
    public const string NotifyPrime_HeroID_Msg = "NotifyPrime_HeroID_Msg";
    
    
    // DragonMancer
    public const string NotifyDragonMancer_Msg_S7 = "NotifyDragonMancer_Msg_S7";
    public const string NotifyDragonMancer_Error_Equip_Msg_S7 = "NotifyDragonMancer_Error_Equip_Msg_S7";
    public const string NotifyDragonMancer_HeroID_Msg_S7 = "NotifyDragonMancer_HeroID_Msg_S7";

    //刷新失败场次
    public const string NotifyLoseCount = "NotifyLoseCount";

    //无战斗回合结束
    public const string NotifyNoBattleTurnEnd = "NotifyNoBattleTurnEnd";
    //S7 环礁海灵奖励计数
    public const string NotifyReserveRecord = "NotifyReserveRecord";

    public const string NotifyFakeSummonHeroData = nameof(NotifyFakeSummonHeroData);
    public const string NotifyClearFakeSummonHeroData = nameof(NotifyClearFakeSummonHeroData);
    //s7.5 诺姆希随机
    public const string NotifyRandomHeroMap = "NotifyRandomHeroMap";

    public const string NotifyOrnn = "NotifyOrnn";
    public const string PlayerCostData = "PlayerCostData";
    public const string TransferHero = nameof(TransferHero);


    #region 商店类型消息
    
    public const string NOTIFY_STORE_DATA = nameof(NOTIFY_STORE_DATA);
    //海克斯商店
    public const string NotifyHextechAugmentStoreList_S6 = "NotityHextechAugmentStoreList_S6";
    public const string NotifyHextechAugmentStoreChose_S6 = "NotityHextechAugmentStoreChose_S6";
    public const string NotifyHextechAugmentStoreClose_S6 = "NotityHextechAugmentStoreClose_S6";
    public const string NotifyPlayerHextechAugmentConfig_S6 = "NotityPlayerHextechAugmentConfig_S6";
    public const string NotifyHextechAugmentStoreRefreshInfo = "NotifyHextechAugmentStoreRefreshInfo";
    public const string NotifyHextechAugmentCountInfo = "NotifyHextechAugmentCountInfo";
    public const string NotifyHeroStrengthInfo = "NotifyHeroStrengthInfo";
    
    public const string NotifyHABattleRules_S6 = "NotifyHABattleRules_S6";
    public const string NotifyDropHeroFromHex = nameof(NotifyDropHeroFromHex);

    public const string NotifyEmblems_Data = nameof(NotifyEmblems_Data);
    public const string NotifyManager_Data = nameof(NotifyManager_Data);
    
    /// <summary>
    /// 装备商店
    /// </summary>
    public const string NotifyEquipStoreList_S5 = "NotifyEquipStoreList_S5";
    public const string NotifyEquipStoreChose_S5 = "NotifyEquipStoreChose_S5";
    public const string NotifyEquipStore_Timer_S5 = "NotifyEquipStore_Timer_S5";
    public const string NotifyEquipStoreTurnCount_S5 = "NotifyEquipStoreTurnCount_S5";
    public const string NotifyEquipStoreClose_S5 = "NotifyEquipStoreClose_S5";
    public const string NotifyEquipStoreErrTips_S5 = "NotifyEquipStoreErrTips_S5";
    
    /// <summary>
    /// 英雄包
    /// </summary>
    public const string NotifyHeroPackShow = nameof(NotifyHeroPackShow);
    public const string NotifyHeroPackChose= nameof(NotifyHeroPackChose);
    public const string NotifyHeroPackRefreshCount= nameof(NotifyHeroPackRefreshCount);

    public const string Notify_Store_Data = nameof(Notify_Store_Data);
    public const string Notify_Store_Turn = nameof(Notify_Store_Turn);
    public const string Notify_Store_Choose = nameof(Notify_Store_Choose);
    public const string Notify_Store_Timer = nameof(Notify_Store_Timer);
    public const string Notify_Store_Error = nameof(Notify_Store_Error);
    public const string Notify_Store_Open = nameof(Notify_Store_Open);
    public const string Notify_Store_Close = nameof(Notify_Store_Close);
    public const string Notify_Store_Ani = nameof(Notify_Store_Ani);
    public const string Notify_Store_Refresh = nameof(Notify_Store_Refresh);
    public const string Notify_Store_Extra = nameof(Notify_Store_Extra);
    #endregion

    #region 控制器相关
    public const string CtrlDataToView = "CtrlDataToView";
    #endregion

    #region 4V4
    
    public const string Notify_Tag_Hero_Msg = nameof(Notify_Tag_Hero_Msg);
    public const string Notify_Tag_Hero_Data = nameof(Notify_Tag_Hero_Data);
    public const string Notify_Remove_Tag_Hero = nameof(Notify_Remove_Tag_Hero);
    public const string Notify_All_Ready_Decisive = nameof(Notify_All_Ready_Decisive);

    #endregion
}