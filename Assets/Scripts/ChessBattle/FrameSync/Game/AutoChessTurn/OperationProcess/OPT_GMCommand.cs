using System;
using System.Collections.Generic;

/// <summary>
/// 一些GM指令
/// </summary>
public class OPT_GMCommand : IOperationProcess, IWorldObject
{
    public MicroObject World { get; set; }

    public OPT_GMCommand(MicroObject obj)
    {
        World = obj;
    }

    public void Dispose()
    {

    }

    public void Execute(VOOperationData data)
    {
#if !JK_RELEASE
        if (data.GetSourceDatasLen() >= 2)
        {
            int commandType = UtilTools.byteListToInt(data.GetSourceDatas(), data.GetSourceDatasLen());
            switch (commandType)
            {
                case (int)GM_COMMAND_TYPE.ONE_HP:
                    var players = World.CSoGame.PlayerMgr.allPlayer;
                    foreach(var player in players.Values)
                    {
                        if (player.iPlayerLife > 0)
                            player.iPlayerLife = 1;
                    }
                    break;
                case (int)GM_COMMAND_TYPE.ONE_ROUND_END:
                    foreach (var player in World.CSoGame.PlayerMgr.allPlayer.Values)
                    {
                        //AIPlayerData aiPlayerData = World.CSoGame.m_AiOperationFactory.m_AiPlayerDataManager.GetAIPlayerDataByUin(player.UIN) as AIPlayerData;
                        //if (aiPlayerData != null)
                        //    aiPlayerData.m_IsOneRoundEnd = true;
                    }
                    break;
                case (int)GM_COMMAND_TYPE.SCRAP_ALL:
                    foreach (var player in World.CSoGame.PlayerMgr.allPlayer.Values)
                    {
                        //player.EquipmentMgr.isOpenScrapAll = !player.EquipmentMgr.isOpenScrapAll;
                    }
                    break;
            }
        } 
#endif
       
    }

    public void ProcessMicroCoreMsg(Type msgType, Wup.Jce.JceStruct msgData)
    {

    }
}

public enum GM_COMMAND_TYPE
{
    ONE_HP = 0,
    ONE_ROUND_END,
    SCRAP_ALL,
    Refreh1000,
    RefreshDraftData,
}