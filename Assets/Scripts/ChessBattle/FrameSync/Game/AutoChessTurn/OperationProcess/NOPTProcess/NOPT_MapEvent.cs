using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZGameClient;

public class NOPT_MapEvent : IOperationProcess
{
    public void Dispose()
    {
    }

    public void Execute(VOOperationData data)
    {

    }

    public void ProcessMicroCoreMsg(Type msgType, Wup.Jce.JceStruct msgData)
    {
#if ACGGAME_CLIENT
        UICommandMapEvent command = LogicToUIViewCommandManager.Instance.Alloc<UICommandMapEvent>();
        command.CommandId = UICommandId.MapEventMsg;
        command.NotifyMapEvent = msgData as ACG_TCmdS2CMapEvent;
        LogicToUIViewCommandManager.Instance.Send(command);
#endif
    }

}
