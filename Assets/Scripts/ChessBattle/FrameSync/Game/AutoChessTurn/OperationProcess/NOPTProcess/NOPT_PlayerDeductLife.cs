#if ACGGAME_CLIENT 
using System;
using ZGameClient;

/// <summary>
///回合扣血
/// </summary>
public class NOPT_PlayerDeductLife : IOperationProcess
{
    public void Dispose()
    {

    }

    public void Execute(VOOperationData data)
    {

    }

    public void ProcessMicroCoreMsg(Type msgType, Wup.Jce.JceStruct msgData)
    {
        UICommandPlayerDeductLife command = LogicToUIViewCommandManager.Instance.Alloc<UICommandPlayerDeductLife>();
        command.CommandId = UICommandId.TurnPlayerDeductLife;

        command.deductPlayerLifeInfo = msgData as ACG_NotifyPlayerDeductLife;
        command.chairID = command.deductPlayerLifeInfo.iChairID;
        LogicToUIViewCommandManager.Instance.Send(command);
    }
}
#endif
