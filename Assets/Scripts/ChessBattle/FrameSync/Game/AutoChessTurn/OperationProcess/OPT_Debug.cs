
using System.Collections.Generic;
using ZGameClient;
using ZGameChess;
using Z_PVE;
using UnityEngine;
using System;
using TKFrame;

/// <summary>
/// 调试指令
/// </summary>

public class OPT_Debug : IOperationProcess, IWorldObject
{
    public const int DEBUG_TURN_KEY = 202207290;
    public const int DEBUG_MATCH_KEY = 202207291;
    
    public MicroObject World { get; set; }
    //构造函数，在SoMsgManager中注册
    public OPT_Debug(MicroObject obj)
    {
        World = obj;
    }

    public void Dispose()
    {

    }

    public void Execute(VOOperationData data)
    {
     
    }

    #region 天选英雄
    protected void procChooserHero(VOOperationData data)
    {

    }
    #endregion

    public void ProcessMicroCoreMsg(Type msgType, Wup.Jce.JceStruct msgData)
    {

    }
}
