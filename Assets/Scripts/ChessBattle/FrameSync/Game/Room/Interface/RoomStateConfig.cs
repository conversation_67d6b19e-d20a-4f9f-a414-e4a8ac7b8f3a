#if ACGGAME_CLIENT
using System.Collections;
using System.IO;
using GameFramework.Common.FSM;
using GloryLockStep.Room.RoomStates;
using TKFrame;
using UnityEngine;
using ZGameClient;

namespace GloryLockStep.Room
{
    public abstract class RoomStateConfig
    {
        private RoomControl m_RoomControl;

        protected StateGraph<RoomControl> m_Graph;
        protected State<RoomControl> m_DefaultState;

        protected State<RoomControl> m_LoadState;
        protected State<RoomControl> m_FightState;
        protected State<RoomControl> m_ResultState;

        protected string m_StartTrigger;
        protected string m_FightTrigger;
        protected string m_CompleteTrigger;
        protected string m_AbnormalTrigger;
        public RoomStateConfig()
        {
            m_Graph = new StateGraphT<RoomControl, StateGraphStorage_AdjacencyList<RoomControl>>();

            m_DefaultState = m_Graph.AddState("RoomState_Enter", new RoomControl.RoomState_Enter());
            m_DefaultState.SetAsDefaultState();
        }

        protected void SetLoadState(string stateName, IStateListener<RoomControl> stateListener)
        {
            if (!(stateListener is RoomState_BaseLoading))
            {
                Diagnostic.Error(stateName + " 要继承 RoomState_BaseLoading");
            }
            m_LoadState = m_Graph.AddState(stateName, stateListener);
        }


        protected void SetFightState(string stateName, IStateListener<RoomControl> stateListener)
        {
            m_FightState = m_Graph.AddState(stateName, stateListener);
        }

        protected void SetResultState(string stateName, IStateListener<RoomControl> stateListener)
        {
            m_ResultState = m_Graph.AddState(stateName, stateListener);
        }

        protected void SetLoadTransition(string trigger)
        {
            m_StartTrigger = trigger;
            m_Graph.AddTransition(m_DefaultState, m_LoadState,
                new GameFramework.Common.FSM.TransitConditions.CheckTrigger<RoomControl>(trigger));
        }

        protected void SetFightTransition(string trigger)
        {
            m_FightTrigger = trigger;
            m_Graph.AddTransition(m_LoadState, m_FightState,
                new GameFramework.Common.FSM.TransitConditions.CheckTrigger<RoomControl>(trigger));
        }

        protected void SetResultTransition(string trigger, string abnormal)
        {
            m_CompleteTrigger = trigger;
            m_AbnormalTrigger = abnormal;
            
            m_Graph.AddTransition(m_FightState, m_ResultState,
                new GameFramework.Common.FSM.TransitConditions.CheckTrigger<RoomControl>(trigger));
            
            m_Graph.AddTransition(m_LoadState, m_ResultState,
                new GameFramework.Common.FSM.TransitConditions.CheckTrigger<RoomControl>(abnormal));
        }

        public virtual void TriggerToFight(bool isExcuteNow = false)
        {
            RoomState_BaseLoading loadingState = m_LoadState.listener as RoomState_BaseLoading;
            if (loadingState !=  null && loadingState.Error)
            {
                TriggerToAbNormalResult(true);
            }
            else
            {
                m_RoomControl.SetTrigger(m_FightTrigger, isExcuteNow);
            }
        }
        
        public virtual void TriggerToAbNormalResult(bool isExcuteNow = false)
        {
            m_RoomControl.IsAbnormalExit = true;
            m_RoomControl.SetTrigger(m_AbnormalTrigger, isExcuteNow);
        }
        

        public virtual StateGraph<RoomControl> Init(RoomControl roomControl)
        {
            m_RoomControl = roomControl;
            m_RoomControl.InitWithGraph(m_Graph);
            m_RoomControl.Start(); // 启动状态机
            return m_Graph;
        }

        public void Start()
        {
            m_RoomControl.SetTrigger(m_StartTrigger, true);
        }

        public virtual bool IsLoading()
        {
            if (m_RoomControl == null) return false;
            if (m_LoadState == null) return false;
            return m_RoomControl.currentState.name == m_LoadState.name;
        }

        public virtual bool IsFighting()
        {
            if (m_RoomControl == null) return false;
            if (m_FightState == null) return false;
            return m_RoomControl.currentState.name == m_FightState.name;
        }

        public virtual bool IsResulting()
        {
            if (m_RoomControl == null) return false;
            if (m_ResultState == null) return false;
            return m_RoomControl.currentState.name == m_ResultState.name;
        }
    }
}

#endif
