#if ACGGAME_CLIENT 
using GameFramework;
using GameFramework.Common.FSM;
using GameFramework.Messaging;
using TKFrame;
using UnityEngine;
using GameFramework.FrameSync;
using ZGame.GameSystem;
using ZGameChess;

namespace GloryLockStep.Room.RoomStates
{
    class RoomState_NoNetworkResult : IStateListener<RoomControl>
    {
        public void OnStateEnter(RoomControl context, State<RoomControl> state)
        {
            Log.InfoChannel(LogChannel.LockStep, "RoomState_NoNetworkResult.OnStateEnter");

            ACGEventManager.Instance.Send(EventType_RoomFight.ShowResultView);

            NoNetworkData.ResetAllData();
        }

        public void OnStateUpdate(RoomControl context, State<RoomControl> state)
        { }

        public void OnStateExit(RoomControl context, State<RoomControl> state)
        {
            Log.InfoChannel(LogChannel.LockStep, "RoomState_NoNetworkResult.OnStateExit");
        }

        public void SyncState(MovingState movingState)
        {
        }

        private void ShowStage()
        {

        }
    }
}
#endif