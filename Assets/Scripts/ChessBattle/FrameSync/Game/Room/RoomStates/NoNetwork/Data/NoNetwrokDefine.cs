using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// 区别于网络来的scene id请求，这个是专门给构造sitdown的地方用的，一般以负数为主（怕正数与后期协议新增有冲突）
public enum NoNetworkSceneID
{
    NORAML_NO_NETWORK = -2,
    // 战场编辑器模式
    TRAIN_BATTLE_MODE = -3,
    // 小队长编辑模式
    TEAM_LEADER_MODE = -4,

    NOVICE_ROOKIE_STAGE = -5,
    // -5 已经废弃 原关卡式新手菜鸡模式
    // 关卡式新手经验模式
    NOVICE_EXPERIENCE_STAGE = -6,
    // 关卡大神
    NOVICE_MASTER_STAGE = -7,
    // 人机对战
    PLAY_WITH_COMPUTER = -8,
    // 挑战玩法
    CHALLENGE_MODE = -9,


    // 默认玩法模式
    DEFAULT = -999,
}