using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;

namespace GloryLockStep.Room
{
    [Serializable]
    public class ParticipantManager : ISerializable
    {
        private LinkedHashMap<int, Participant> mParticipanties = new LinkedHashMap<int, Participant>();
        private LinkedHashMap<int, Participant> m_ObParticipanties = new LinkedHashMap<int, Participant>();
        private LinkedHashMap<int, Participant> m_JudgeParticipanties = new LinkedHashMap<int, Participant>();
        
        
        private int mMaxNumber;

        /// <summary>
        /// 创建时必须制定最大的数量.
        /// </summary>
        /// <param name="maxNumber"></param>
        public ParticipantManager(int maxNumber)
        {
            mMaxNumber = maxNumber;
        }

        public LinkedHashMap<int, Participant> ParticipantieList
        {
            get { return mParticipanties; }
        }
        
        public LinkedHashMap<int, Participant> ObParticipantieList
        {
            get { return m_ObParticipanties; }
        }

        public LinkedHashMap<int, Participant> JudgeParticipantieList
        {
            get { return m_JudgeParticipanties; }
        }

        public bool Add(Participant participant)
        {

            LinkedHashMap<int, Participant> map = null;

            if (participant.isJudge)
                map = m_JudgeParticipanties;
            else if (participant.isObserver)
                map = m_JudgeParticipanties;//没写错 你没看错！暂时只有裁判
            else
                map = mParticipanties;
            
            if (map.ContainsKey(participant.participantChariId))
            {
                map[participant.participantChariId] = participant;
            }
            else
            {
                map.Add(participant.participantChariId, participant);
            }

            return true;
        }
        
        public Participant GetParticipantByUin(long uin)
        {
            foreach (Participant tempParticipants in mParticipanties.Values)
            {
                if (tempParticipants.participantUin == uin)
                    return tempParticipants;
            }

            foreach (Participant tempParticipants in m_ObParticipanties.Values)
            {
                if (tempParticipants.participantUin == uin)
                    return tempParticipants;
            }

            foreach (Participant tempParticipants in m_JudgeParticipanties.Values)
            {
                if (tempParticipants.participantUin == uin)
                    return tempParticipants;
            }
            
            return null;
        }

        public Participant GetParticipantById(int participantId)
        {
            foreach (Participant tempParticipants in mParticipanties.Values)
            {
                if (tempParticipants.participantId == participantId)
                    return tempParticipants;
            }

            foreach (Participant tempParticipants in m_ObParticipanties.Values)
            {
                if (tempParticipants.participantId == participantId)
                    return tempParticipants;
            }

            foreach (Participant tempParticipants in m_JudgeParticipanties.Values)
            {
                if (tempParticipants.participantId == participantId)
                    return tempParticipants;
            }

            return null;
        }

        public void GetObjectData(SerializationInfo info, StreamingContext context)
        {
       
        }
    }
}