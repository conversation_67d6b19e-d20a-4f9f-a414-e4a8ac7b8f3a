using System.Collections;
using System.Collections.Generic;
using TDRConfig;
using UnityEngine;
using Z_PVE;
using ZGameClient;

/// <summary>
/// 回合结束时记录player场上所有英雄的伤害
/// </summary>
public class RoundDamageData
{
    private LinkedHashMap<int, DamageDate> heroDamageDataList; //heroEntity:damage
    public CPlayer player;
    public RoundDamageData(CPlayer curPlayer)
    {
        heroDamageDataList = new LinkedHashMap<int, DamageDate>();
        player = curPlayer;
    }
    public LinkedHashMap<int, int> GetAllTotalDamageValue()
    {
        LinkedHashMap<int, int> ret = new LinkedHashMap<int, int>();
        foreach(var kv in heroDamageDataList)
        {
            ret.Add(kv.Key, kv.Value.TotalDamageValue);
        }
        return ret;
    }
    public void SetAllTotalDamageValue(LinkedHashMap<int, int> damageValue)
    {
        heroDamageDataList.Clear();
        foreach (var kv in damageValue)
        {
            heroDamageDataList.Add(kv.Key, new DamageDate(kv.Value));
        }
    }
    public void SetTotalDamageValue(int entityId, int value)
    {
        if (heroDamageDataList.ContainsKey(entityId))
            heroDamageDataList[entityId].SetTotalDamageValue(value);
        else
            heroDamageDataList.Add(entityId, new DamageDate(value));
    }
    public int GetEntityIdMax()
    {
        int maxValue = -1;
        int maxEntityId = -1;
        foreach(var kv in heroDamageDataList)
        {
            int curValue = kv.Value.TotalDamageValue;
            if (curValue > maxValue)
            {
                maxValue = curValue;
                maxEntityId = kv.Key;
            }
        }
        return maxEntityId;
    }
    public int GetEntityIdMin()
    {
        int minValue = int.MaxValue;
        int minEntityId = -1;
        foreach(var kv in heroDamageDataList)
        {
            int curValue = kv.Value.TotalDamageValue;
            if (curValue < minValue)
            {
                minValue = curValue;
                minEntityId = kv.Key;
            }
        }
        return minEntityId;
    }
    public int GetEntityIdMaxByFetter(int fetterType, int fetterCheckId, CSoConfig soConfig)
    {
        int maxEntityId = -1;

        return maxEntityId;
    }
    public int GetEntityIdMinByFetter(int fetterType, int fetterCheckId, CSoConfig soConfig)
    {

        int minEntityId = -1;

        return minEntityId;
    }
    public void Reset()
    {
        heroDamageDataList.Clear();
    }
}

public class DamageDate
{
    //private int m_magicalDamageValue;
    //public int MagicalDamageValue
    //{
    //    get
    //    {
    //        return m_magicalDamageValue;
    //    }
    //}
    //private int m_physicalDamageValue;
    //public int PhysicalDamageValue
    //{
    //    get
    //    {
    //        return m_physicalDamageValue;
    //    }
    //}
    //private int m_pureDamageValue;
    //public int PureDamageValue
    //{
    //    get
    //    {
    //        return m_pureDamageValue;
    //    }
    //}
    private int m_totalDamageValue;
    public int TotalDamageValue
    {
        get
        {
            return m_totalDamageValue;
        }
    }
    public DamageDate()
    {
        //m_magicalDamageValue = 0;
        //m_physicalDamageValue = 0;
        //m_pureDamageValue = 0;
        m_totalDamageValue = 0;
    }
    public DamageDate(int m_totalValue = 0)
    {
        m_totalDamageValue = m_totalValue;
    }
    public DamageDate(int magicalValue, int physicalValue, int pureValue)
    {
        //m_magicalDamageValue = magicalValue;
        //m_physicalDamageValue = physicalValue;
        //m_pureDamageValue = pureValue;
        m_totalDamageValue = magicalValue + physicalValue + pureValue;
    }
    //public void SetDamageValue(DamageType damageType, int value)
    //{
    //    switch (damageType)
    //    {
    //        case DamageType.Magical:
    //            m_magicalDamageValue = value;
    //            break;
    //        case DamageType.Physical:
    //            m_physicalDamageValue = value;
    //            break; 
    //        case DamageType.Pure:
    //            m_pureDamageValue = value;
    //            break;
    //        default:
    //            break;
    //    }
    //}
    public void SetTotalDamageValue(int value)
    {
        m_totalDamageValue = value;
    }
}