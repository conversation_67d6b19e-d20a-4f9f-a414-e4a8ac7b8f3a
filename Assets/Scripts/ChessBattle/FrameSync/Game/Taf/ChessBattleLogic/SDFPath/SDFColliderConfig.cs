using System.IO;
using GameFramework.FMath;

namespace SDFCollider
{
    public enum ColliderType
    {
        Circle,
        Octagon,
        MutiType,
    }

    public class SDFColliderConfig
    {
        public string colliderName; // 名字
        public Fix64 radium;
        public Fix64[,] m_points; // SDF图

        public void Decode(Stream stream)
        {
            using (BinaryReader reader = new BinaryReader(stream))
            {
                colliderName = reader.ReadString();
                radium = Fix64.FromRawValue(reader.ReadInt64());
                int count = reader.ReadInt32();

                m_points = new Fix64[count, count];

                for (int x = 0; x < count; ++x)
                {
                    for (int y = 0; y < count; ++y)
                    {
                        m_points[x, y] = Fix64.FromRawValue(reader.ReadInt64());
                    }
                }
            }
        }
    }
}