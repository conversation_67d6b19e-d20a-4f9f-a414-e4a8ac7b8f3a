using GameFramework.FMath;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


public class LogicTaskTeleport : LogicTaskBase
{
    public FVector3 m_targetPosition;
    public int m_delayMS;
    protected int m_frameCount;

    public LogicTaskTeleport() : base(true)
    {

    }

    public LogicTaskTeleport Init(int playerId, FVector3 targetPosition, int delayMS)
    {
        m_targetPosition = targetPosition;
        m_delayMS = delayMS;
        BindPlayer(playerId);
        return this;
    }

    public override void Reset()
    {
        base.Reset();
        m_targetPosition = FVector3.zero;
        m_delayMS = 0;
        m_frameCount = 0;
    }

    public override void Start()
    {
        base.Start();

        m_frameCount = ConvertToFrameCount(m_delayMS);
    }

    protected override void UpdateImpl(Fix64 deltaTime)
    {
        --m_frameCount;
        if (m_frameCount <= 0)
            m_finished = true;
    }

    public override void End(bool isBreak)
    {
        base.End(isBreak);

        DoAction();
    }

    private void DoAction()
    {
        var player = GetPlayer();
        if (player != null)
        {
            player.Teleport(m_targetPosition);
        }
    }

    protected override void SyncImpl()
    {
        if (m_end)
            DoAction();
    }
}

