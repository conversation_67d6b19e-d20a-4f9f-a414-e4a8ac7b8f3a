using GameFramework.FMath;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//public class LogicTaskData
//{
//    public int m_playerId;
//    public ChessBattleLogicField m_field;

//    private bool m_setPosition = false;
//    private FVector3 m_position;
//    private bool m_setRotation = false;
//    private FVector3 m_direction;
//    private string m_currentAction;
//    private bool m_triggerWillHit = false;

//    public ChessBattleLogicPlayer GetPlayer()
//    {
//        return m_field != null ? m_field.GetPlayer(m_playerId) : null;
//    }

//    public void SetPosition(FVector3 position)
//    {
//        m_setPosition = true;
//        m_position = position;
//    }

//    public void SetDirection(FVector3 direction)
//    {
//        m_setRotation = true;
//        m_direction = direction;
//    }

//    public void SetAction(string action)
//    {
//        m_currentAction = action;
//    }

//    public void SetWillHit()
//    {
//        m_triggerWillHit = true;
//    }

//    // 切战场以后 真人假人同步数据到表现层
//    public void Sync()
//    {
//        var player = GetPlayer();
//        if (player != null)
//        {
//            FVector3 pos;
//            FVector3 dir;
//            if (m_setPosition)
//                pos = m_position;
//            else
//                pos = player.fTransform.position;

//            if (m_setRotation)
//                dir = m_direction;
//            else
//                dir = player.dir;

//            if (m_setPosition || m_setRotation)
//                player.SetPositionAndRotation(pos, dir);

//            if (!string.IsNullOrEmpty(m_currentAction))
//                player.PlayAction(m_currentAction);

//            if (m_triggerWillHit)
//                player.SetWillHit();
//        }
//    }

//    public void Reset()
//    {
//        m_playerId = -1;
//        m_field = null;
//        m_setPosition = false;
//        m_position = FVector3.zero;
//        m_setRotation = false;
//        m_direction = FVector3.zero;
//        m_currentAction = string.Empty;
//        m_triggerWillHit = false;
//    }

//    public void CopyFrom(LogicTaskData data)
//    {
//        m_playerId = data.m_playerId;
//        m_field = data.m_field;
//        m_setPosition = data.m_setPosition;
//        m_position = data.m_position;
//        m_setRotation = data.m_setRotation;
//        m_direction = data.m_direction;
//        m_currentAction = data.m_currentAction;
//        m_triggerWillHit = data.m_triggerWillHit;
//    }
//}

public abstract class LogicTaskBase
{
    //protected LogicTaskData m_data = new LogicTaskData();
    protected int m_playerId = -1;
    protected ChessBattleLogicField m_field = null;
    protected LogicTaskBase m_nextTask = null;
    protected bool m_start = false;
    protected bool m_finished = false;
    protected bool m_end = false;
    protected bool m_lockOperation = true;

    public LogicTaskBase(bool lockOperation)
    {
        m_lockOperation = lockOperation;
    }

    public virtual void Reset()
    {
        m_playerId = -1;
        m_field = null;
        m_nextTask = null;
        m_start = false;
        m_finished = false;
        m_end = false;
    }

    public LogicTaskBase Append(LogicTaskBase nextTask)
    {
        m_nextTask = nextTask;
        return nextTask;
    }

    public void Bind(ChessBattleLogicField field)
    {
        m_field = field;
    }

    protected void BindPlayer(int playerId)
    {
        m_playerId = playerId;
    }

    public ChessBattleLogicPlayer GetPlayer(bool limitField = true)
    {
        var player = m_field != null ? m_field.GetPlayer(m_playerId) : null;
        if (player == null && !limitField)
        {
            if (m_field != null && m_field.SoGame != null && m_field.SoGame.chessBattleCore != null)
            {
                player = m_field.SoGame.chessBattleCore.GetPlayerByChairId(m_playerId);
            }
        }
        if (player == null)
            TKFrame.Diagnostic.Warn("GetPlayer faild m_playerId:" + m_playerId);
        return player;
    }

    public virtual void Start()
    {
        var player = GetPlayer();
        if (player != null)
        {
            player.PrintLog("task: " + this.GetType() + " start!");
        }
        m_start = true;
    }

    public void Sync(int playerId)
    {
        if (m_playerId == playerId)
        {
            if (m_start) SyncImpl();
        }

        if (m_nextTask != null)
        {
            if (m_nextTask.m_start)
                m_nextTask.Sync(playerId);
        }
    }

    public bool IsLockOperation(int playerId)
    {
        if (m_start && !m_end && playerId == m_playerId)
            return m_lockOperation;
        else if (m_end && m_nextTask != null)
            return m_nextTask.IsLockOperation(playerId);
        return false;
    }

    protected abstract void SyncImpl();

    protected abstract void UpdateImpl(Fix64 deltaTime);

    public bool Update(Fix64 deltaTime)
    {
        if (!m_start)
        {
            Start();
        }
        else if (!m_finished && !m_end)
        {
            UpdateImpl(deltaTime);
        }

        if (m_end)
        {
            if (m_nextTask != null)
            {
                return m_nextTask.Update(deltaTime);
            }
            return false;
            //return m_nextTask;
        }

        if (m_finished)
        {
            m_end = true;
            m_finished = false;
            End(false);
            return true;
            //return m_nextTask;
        }
        else
        {
            return true;
        }
    }

    public virtual void OnBreak()
    {
        End(true);
    }

    public void RecycleToPool()
    {
        var nextTask = m_nextTask;
        while (nextTask != null)
        {
            LogicTaskPool.Instance.RecycleMsg(nextTask);
            nextTask = nextTask.m_nextTask;
        }

        LogicTaskPool.Instance.RecycleMsg(this);
    }

    public virtual void End(bool isBreak)
    {
        if (!m_start)
        {
            return;
        }
        var player = GetPlayer();
        if (player != null)
        {
            player.PrintLog("task: " + this.GetType() + " end!");
        }

        if (!isBreak)
        {
            if (m_nextTask != null)
            {
                m_nextTask.Bind(m_field);
                m_nextTask.Start();
            }
        }
    }

    protected int ConvertToFrameCount(int time)
    {
        int interval = Lucifer.ActCore.ActionCoreConfig.DefaultFrameInterval;
        //if (soGame != null)
        //    interval = soGame.World.fightContext.roomControl.playerInfo.roomInfo.GetFrameInterval();
        return time / interval;
    }
}