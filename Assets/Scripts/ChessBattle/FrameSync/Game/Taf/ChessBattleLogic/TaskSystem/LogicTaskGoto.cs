using ACG.Core;
using GameFramework.FMath;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

public class LogicTaskGoto : LogicTaskBase
{
    private static FVector2 m_tmpVector2 = FVector2.zero;

    public FVector2 m_targetPosition;
    public int m_timeOut;

    protected int m_frameCount;

    protected FixedDataList_Struct<FVector2> m_pathList = new FixedDataList_Struct<FVector2>(4);

    protected FVector3 m_curPosition;

    private Fix64 m_distance = Fix64.zero;
    private FVector3 m_stepDst = FVector3.zero;        // 阶段目标点
    protected Fix64 m_stepDistance = Fix64.zero;      // 当前阶段的距离

    protected int m_stateKey;
    protected int m_state;

    public LogicTaskGoto() : base(true)
    {

    }

    public LogicTaskGoto Init(int playerId, FVector2 targetPosition, int timeOut = 5000)
    {
        m_targetPosition = targetPosition;
        m_timeOut = timeOut;

        BindPlayer(playerId);
        return this;
    }

    public override void Reset()
    {
        base.Reset();
        m_targetPosition = FVector2.zero;
        m_frameCount = 0;
    }

    public override void Start()
    {
        base.Start();

        var player = GetPlayer();
        if (player != null)
        {
            var sdfPath = m_field.m_sdfPathCfg;
            if (sdfPath == null)
                sdfPath = player.GetSDFPath();
            SDFMovementCtrlCore.CalMovePath(player, sdfPath, player.Radius, m_targetPosition, ref m_pathList, ref m_distance);
            m_curPosition = player.fTransform.position;
            if (SDFMovementCtrlCore.GetNextPathPoint(ref m_pathList, ref m_tmpVector2))
            {
                player.movementState.SetFix64(MovementStateMachine.FromDstSqr, m_distance.rawValue);
                SDFMovementCtrlCore.InitStep(player, ref m_tmpVector2, ref m_stepDistance, ref m_stepDst);
                player.SetTargetImpl(ref m_tmpVector2, m_stepDistance);
                player.PrepareMove();
            }

            m_stateKey = player.movementState.GetIntIndexByKey(MovementStateMachine.MoveState);
            m_state = player.movementState.GetIntByIndex(m_stateKey);
            //player.onMoveEvent += OnMoveEvent;
            //player.onMoveStateChanged += OnMoveStateChanged;

            m_frameCount = ConvertToFrameCount(m_timeOut);
        }
    }

    public void MoveUpdate(Fix64 deltaTime)
    {
        var player = GetPlayer();
        if (player != null)
        {
            Fix64 delta = deltaTime * player.m_speed;

            if (m_distance > delta)
            {
                m_distance -= delta;
            }
            else if (m_distance != Fix64.zero)
            {
                delta = m_distance;
                m_distance = Fix64.zero;
            }
            else if (m_stepDistance != Fix64.zero) // 因为SDFMovementCtrlCore.Move可能会偏移位置 导致m_stepDistance的总和大于m_distance的情况 所以这里需要做个兜底 不然有可能卡在这个循环里面
            {
                delta = m_stepDistance;
                m_stepDistance = Fix64.zero;
            }

            player.movementState.SetFix64(MovementStateMachine.FromDstSqr, m_distance.rawValue);
            var sdfPath = m_field.m_sdfPathCfg;
            if (sdfPath == null)
                sdfPath = player.GetSDFPath();
            SDFMovementCtrlCore.Move(sdfPath, ref m_curPosition, player.Radius, ref m_stepDst, ref delta);
            var state = player.movementState.GetIntByIndex(m_stateKey);
            if (state != m_state && m_stepDistance != Fix64.zero)
            {
                // 这时候可能是由于切观战了 导致小小英雄状态重置，需要fix一下
                m_tmpVector2.x = m_stepDst.x;
                m_tmpVector2.y = m_stepDst.z;
                player.SetTargetImpl(ref m_tmpVector2, m_stepDistance);
                //player.SendTarget(m_stepDistance);
                player.PrepareMove();
            }
            player.fTransform.position = m_curPosition;

            CheckNextStep(player, ref delta);
        }
    }

    private void CheckNextStep(ChessBattleLogicPlayer player, ref Fix64 detla)
    {
        m_stepDistance -= detla;
        if (m_stepDistance <= Fix64.zero)
        {
            // 找下一个目标点
            if (SDFMovementCtrlCore.GetNextPathPoint(ref m_pathList, ref m_tmpVector2))
            {
                SDFMovementCtrlCore.InitStep(player, ref m_tmpVector2, ref m_stepDistance, ref m_stepDst);
                player.SetTargetImpl(ref m_tmpVector2, m_stepDistance);
            }
            else
            {
                m_distance = Fix64.zero;
                m_stepDistance = Fix64.zero;
                m_stepDst = FVector3.zero;

                m_finished = true;
            }
        }
    }

    protected override void UpdateImpl(Fix64 deltaTime)
    {
        MoveUpdate(deltaTime);

        --m_frameCount;
        if (m_frameCount <= 0)
            m_finished = true;
    }

    public override void End(bool isBreak)
    {
        base.End(isBreak);

        //var player = GetPlayer();
        //if (player != null)
        //{
        //    player.onMoveEvent -= OnMoveEvent;
        //    player.onMoveStateChanged -= OnMoveStateChanged;
        //}
    }

    protected override void SyncImpl()
    {
        if (m_start)
        {
            var player = GetPlayer();
            if (player != null && !player.isFake)
            {
                player.SetPositionAndRotation(m_curPosition, player.dir);
            }
        }
    }

    //private void OnMoveStateChanged(PlayerMoveMessage moveState)
    //{
    //    if (moveState == PlayerMoveMessage.Idle
    //        || moveState == PlayerMoveMessage.Stop)
    //    {
    //        m_finished = true;
    //    }
    //}

    //void OnMoveEvent(SDFMovementCtrl.EventType e)
    //{
    //    if (e == SDFMovementCtrl.EventType.Break)
    //    {
    //        OnBreak();
    //    }
    //    else if (e == SDFMovementCtrl.EventType.End)
    //    {
    //        m_finished = true;
    //    }
    //}
}

