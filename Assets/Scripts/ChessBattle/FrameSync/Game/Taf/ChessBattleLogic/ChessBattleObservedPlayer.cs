

using System.Linq;
#if ACGGAME_CLIENT
using System.Collections.Generic;
using TKFrame;
using Z_PVE;
using ZGameClient;

public class ChessBattleObservedPlayer
{
    public static bool isOn = true;
    
    private ChessBattleLogicField m_observedField = null;
    private ChessBattleLogicPlayer m_observedPlayer = null;
    private CPlayer m_sender = null;
    private CPlayer m_myCplayer = null;
    private int m_chairId = -1;
    private int m_myChairId = -1;

    public int myChairId
    {
        get { return m_myChairId; }
    }

    public ChessBattleLogicField observedField
    {
        get
        {
            return m_observedField;
        }
    }

    public ChessBattleLogicPlayer observedPlayer
    {
        get
        {
            return m_observedPlayer;
        }
    }

    private List<LogicHitBullet> m_bullectList = new List<LogicHitBullet>();
    private List<int> m_bullets = new List<int>();
    
    private LinkedHashSet<ChessBattleLogicPlayer> m_playersSet = new LinkedHashSet<ChessBattleLogicPlayer>();
    private List<PlayerData> m_players = new List<PlayerData>();

    public ChessBattleObservedPlayer()
    {
        RuleOfSwitchBattleField.SetObservedPlayer(this);
    }

    public void SetSender(CPlayer data)
    {
        m_sender = data;
    }

    public CPlayer GetSender()
    {
        return m_sender;
    }

    public void SetMyChairId(int mySelfId)
    {
        if (!LockStepFightProcess.IsFreeSight())
        {
            m_myChairId = mySelfId;
            m_myCplayer = MicroMgr.Instance.GetMicroObj().CSoGame.PlayerMgr.GetPlayerByChairId(mySelfId);
        }
        else
        {
            m_myChairId = -1;
            m_myCplayer = null;
        }
    }
    
    public bool isObserverdPlayer(ChessBattleLogicPlayer logicPlayer)
    {
        bool isMe = false;
        if (m_myChairId != -1 && m_myCplayer != null)
        {
            if (!m_myCplayer.IsTinyHeroDying())
            {
                if (logicPlayer.ChairId == m_myChairId)
                {
                    isMe = true;
                }
            }
        }

        // 该被观察的人是被观察者，而且自己还活着，不是自己
        return logicPlayer.ChairId == m_chairId && !isMe;
    }

    public bool isMyCommand(int chairId)
    {
        return m_myChairId == chairId;
    }

    public bool isObservingPlayer()
    {
        return m_chairId != -1;
    }

    #region 观察者变更
    
    // 观察的是同一个人，但是从木桩变真人，或者真人变木桩
    public void ChangePlayer(ChessBattleLogicPlayer logicPlayer, bool isCloseField, bool isClosePreObservedPlayer = false)
    {
        if (m_observedPlayer != null)
        {
            if (m_observedPlayer != logicPlayer)
            {
                m_observedPlayer.onChangeBattleFieldCallback -= SyncObservedField_Impl;

                ChessBattleLogicPlayer preObservedPlayer = m_observedPlayer;
                m_observedPlayer = logicPlayer;
                m_chairId = m_observedPlayer.ChairId;
                            
                Diagnostic.Log("ObservedManager Change Observer player " + m_chairId + " isFake " + m_observedPlayer.isFake);
                            
                m_observedPlayer.onChangeBattleFieldCallback += SyncObservedField_Impl;
                
                if (isClosePreObservedPlayer)
                {
                    SyncObservedPlayer(preObservedPlayer, false);
                }

                if (isCloseField)
                {
                    SyncObservedField_Impl(null);
                }
            }
        }
    }

    // 观察别人
    public void ObservedPlayer(ChessBattleLogicPlayer logicPlayer)
    {
        if (logicPlayer == null)
        {
            m_chairId = -1;
            if (m_observedPlayer != null)
            {
                m_observedPlayer.onChangeBattleFieldCallback -= SyncObservedField_Impl;
                Diagnostic.Log("ObservedManager Player " + m_observedPlayer.ChairId + " close queue " + m_observedPlayer.queueId + " isFake " + m_observedPlayer.isFake);
                m_observedPlayer.CloseQueue();
                m_observedPlayer = null;
                SyncObservedField_Impl(null);
            }

            return;
        }
        
        if (m_observedPlayer == null || m_observedPlayer != logicPlayer)
        {
            if (m_observedPlayer != null)
            {
                m_observedPlayer.onChangeBattleFieldCallback -= SyncObservedField_Impl;
                Diagnostic.Log("ObservedManager Player " + m_observedPlayer.ChairId + " close queue " + m_observedPlayer.queueId + " isFake " + m_observedPlayer.isFake);
                m_observedPlayer.CloseQueue();
                m_observedPlayer = null;
                SyncObservedField_Impl(null);
            }

            m_observedPlayer = logicPlayer;
            m_chairId = m_observedPlayer.ChairId;
            
#if UNITY_EDITOR && ACGGAME_CLIENT
            LogicDebug.instance.SetPlayerId(m_chairId);
#endif
            
            m_observedPlayer.onChangeBattleFieldCallback += SyncObservedField_Impl;
            m_observedPlayer.SyncMovingState();
            Diagnostic.Log("ObservedManager Player " + m_observedPlayer.ChairId + " open queue " + m_observedPlayer.queueId + " isFake " + m_observedPlayer.isFake);
            
            SyncObservedField_Impl(m_observedPlayer.BattleLogicField);
        }
    }

    public void SyncObservedField(ChessBattleLogicField logicField)
    {
        ObservedPlayer(null);
        ReplaceObserved(logicField);
        SendObservedMsg();
    }

    // 战场同步到表现层
    private void SyncObservedField_Impl(ChessBattleLogicField logicField)
    {
        ReplaceObserved(logicField);
        SendObservedMsg();
    }
    
    #endregion

    #region 战场变更同步到主线程

    // 战场变更
    private void ReplaceObserved(ChessBattleLogicField logicField)
    {
        if (logicField == null)
        {
            if (m_observedField != null)
            {
                CloseObservedField(m_observedField);
                m_observedField = null;
            }

            return;
        }

        if (m_observedField == null || m_observedField != logicField)
        {
            CloseObservedField(m_observedField);
            m_observedField = logicField;
        }
        
        OpenObservedField(m_observedField);
    }

    // 关闭战场观察通道
    private void CloseObservedField(ChessBattleLogicField observedField)
    {
        if (observedField != null)
        {
            Diagnostic.Log("ObservedManager ChessBattleField  close observe " + observedField.ID);
            
            ClosePlayer(observedField.owner);
            ClosePlayer(observedField.enemy);

            foreach (ChessBattleLogicPlayer friend in observedField.friends.Values)
            {
                ClosePlayer(friend);
            }
            
            foreach (ChessBattleLogicPlayer enemyFriend in observedField.enemyFriends.Values)
            {
                ClosePlayer(enemyFriend);
            }
            
            foreach (ChessBattleLogicPlayer observer in observedField.observers.Values)
            {
                ClosePlayer(observer);
            }
            
            observedField.onPlayerChangeCallback -= SyncObservedPlayer;
            observedField.onBulletChangeCallback -= SyncBattleBullet;
            observedField.onBulletLaunchCallback -= SendObservedMsg;
        }
    }

    // 打开战场观察通道
    private void OpenObservedField(ChessBattleLogicField observedField)
    {
        if (observedField != null)
        {
            Diagnostic.Log("ObservedManager ChessBattleField open observe " + observedField.ID);

            OpenPlayer(observedField.owner);
            OpenPlayer(observedField.enemy);

            foreach (ChessBattleLogicPlayer friend in observedField.friends.Values)
            {
                OpenPlayer(friend);
            }
            
            foreach (ChessBattleLogicPlayer enemyFriend in observedField.enemyFriends.Values)
            {
                OpenPlayer(enemyFriend);
            }
            
            foreach (ChessBattleLogicPlayer observer in observedField.observers.Values)
            {
                OpenPlayer(observer);
            }

            observedField.onPlayerChangeCallback = null;
            observedField.onPlayerChangeCallback += SyncObservedPlayer;
            observedField.onBulletChangeCallback = null;
            observedField.onBulletChangeCallback += SyncBattleBullet;
            observedField.onBulletLaunchCallback = null;
            observedField.onBulletLaunchCallback += SendObservedMsg;
        }
    }
    
    #endregion

    #region 玩家变更同步到主线程
    
    private bool OpenPlayer(ChessBattleLogicPlayer logicPlayer)
    {
        if (logicPlayer != null && !m_playersSet.Contains(logicPlayer))
        {
            if (!logicPlayer.isOpenQueue())
            {
                logicPlayer.SyncMovingState();
                Diagnostic.Log("ObservedManager Player " + logicPlayer.ChairId + " open queue " + logicPlayer.queueId + " isFake " + logicPlayer.isFake);
            }
            return true;
        }

        return false;
    }

    private bool ClosePlayer(ChessBattleLogicPlayer logicPlayer)
    {
        if (logicPlayer != null && isNeedClose(logicPlayer))
        {
            if (logicPlayer.isOpenQueue())
            {
                Diagnostic.Log("ObservedManager Player " + logicPlayer.ChairId + " close queue " + logicPlayer.queueId + " isFake " + logicPlayer.isFake);
                logicPlayer.CloseQueue();
            }
            return true;
        }
        return false;
    }

    // 只有被观察者和观察被观察者的人才不需要关闭，因为是一直跟着被观察者的
    private bool isNeedClose(ChessBattleLogicPlayer logicPlayer)
    {
        if(m_observedPlayer == null)
		{
			return true;
		}
		
        if (logicPlayer == m_observedPlayer)
        {
            return false;
        }

        ChessBattleLogicPlayerObserverModel observerModel = m_observedPlayer.ObserverModel;
        foreach (ChessBattleLogicPlayer observerPlayer in observerModel.beObserverPlayers.Values)
        {
            if (observerPlayer == logicPlayer)
            {
                return false;
            }
        }
        
        return true;
    }
    
    #endregion

    #region 消息同步和发送
    
    // 同步战场玩家变更
    private void SyncObservedPlayer(ChessBattleLogicPlayer logicPlayer, bool isAdd)
    {
        bool sendMsg = false;
        if (isAdd)
        {
            sendMsg = OpenPlayer(logicPlayer);
        }
        else
        {
            sendMsg = ClosePlayer(logicPlayer);
        }

        if (sendMsg)
        {
            if (isAdd)
            {
                Diagnostic.Log("ObservedManager BattleField " + m_observedField.ID + " Add Player " + logicPlayer.ChairId + " isFake " + logicPlayer.isFake);
                
                PlayerData playerData = GenPlayerData(logicPlayer);
                
                if (playerData != null)
                {
                    m_playersSet.Add(logicPlayer);
                    SendPlayerChangeMsg(playerData, true);
                }
            }
            else
            {
                Diagnostic.Log("ObservedManager BattleField " + m_observedField.ID + " Close Player " + logicPlayer.ChairId + " isFake " + logicPlayer.isFake);
                
                PlayerData playerData = new PlayerData();
                playerData.iChairID = logicPlayer.ChairId;
                playerData.iQueueID = logicPlayer.queueId;
                playerData.bIsFake = logicPlayer.isFake;
                playerData.sType = (short)logicPlayer.playerState;

                m_playersSet.Remove(logicPlayer);
                SendPlayerChangeMsg(playerData, false);
            }
        }
    }
    
    private void SendPlayerChangeMsg(PlayerData playerData, bool isAdd)
    {
        if (m_sender != null)
        {
            ACG_TCmdS2CNotifyPlayerChange msg = m_sender.Alloc<ACG_TCmdS2CNotifyPlayerChange>();
            msg.playerChange = playerData;
            msg.iMode = isAdd ? 0 :1;
            m_sender.EqueueMessage(msg);
        }
    }

    private void SyncBattleBullet(LogicHitBullet bullet, bool isAdd)
    {
        if (m_sender != null && bullet.isMoving)
        {
            Diagnostic.Log("ObservedManager BattleField " + m_observedField.ID + " Add Bullet " + isAdd);
            bullet.OpenLogicQueue();
            PlayerData playerData = new PlayerData();
            playerData.iChairID = -1;
            playerData.iQueueID = bullet.queueId;
            playerData.bIsFake = false;
            playerData.sType = 0;
            
            ACG_TCmdS2CNotifyPlayerChange msg = m_sender.Alloc<ACG_TCmdS2CNotifyPlayerChange>();
            msg.playerChange = playerData;
            msg.iMode = isAdd ? 3 :4;
            m_sender.EqueueMessage(msg);
        }
    }
    
    public void SendObservedMsg()
    {
        if (m_sender != null)
        {
            ACG_TCmdS2CNotifyObservedObject msg = m_sender.Alloc<ACG_TCmdS2CNotifyObservedObject>();
            CollectBattleFieldBullet(m_observedField);
            GetObservedObject(msg);
            m_sender.EqueueMessage(msg);
        }
    }

    private void GetObservedObject(ACG_TCmdS2CNotifyObservedObject observedMsg)
    {
        m_playersSet.Clear();
        m_players.Clear();
        m_bullets.Clear();
        
        observedMsg.players = m_players;
        observedMsg.bulletIds = m_bullets;

        if (m_observedField != null)
        {
            observedMsg.iChairID = m_observedField.ID;
            
            if (m_observedField.owner != null)
            {
                PlayerData playerData = GenPlayerData(m_observedField.owner);
                if (playerData != null)
                {
                    m_players.Add(playerData);
                    m_playersSet.Add(m_observedField.owner);
                }
            }
            
            if (m_observedField.enemy != null)
            {
                PlayerData playerData = GenPlayerData(m_observedField.enemy);
                if (playerData != null)
                {
                    m_players.Add(playerData);
                    m_playersSet.Add(m_observedField.enemy);
                }
            }
            
            foreach (ChessBattleLogicPlayer friend in m_observedField.friends.Values)
            {
                PlayerData playerData = GenPlayerData(friend);
                if (playerData != null)
                {
                    m_players.Add(playerData);
                    m_playersSet.Add(friend);
                }
            }
            
            foreach (ChessBattleLogicPlayer enemyFriend in m_observedField.enemyFriends.Values)
            {
                PlayerData playerData = GenPlayerData(enemyFriend);
                if (playerData != null)
                {
                    m_players.Add(playerData);
                    m_playersSet.Add(enemyFriend);
                }
            }
            
            foreach (ChessBattleLogicPlayer observer in m_observedField.observers.Values)
            {
                PlayerData playerData = GenPlayerData(observer);
                if (playerData != null)
                {
                    m_players.Add(playerData);
                    m_playersSet.Add(observer);
                }
            }
            
            foreach (LogicHitBullet hitBullet in m_bullectList)
            {
                m_bullets.Add(hitBullet.queueId);
            }
        }
    }

    private PlayerData GenPlayerData(ChessBattleLogicPlayer player)
    {
        int queueId = player.queueId;
        if (queueId != -1)
        {
            PlayerData playerData = new PlayerData();
            playerData.iChairID = player.ChairId;
            playerData.iQueueID = player.queueId;
            playerData.bIsFake = player.isFake;
            playerData.sType = (short)player.playerState;

            return playerData;
        }

        return null;
    }
    
    private void CollectBattleFieldBullet(ChessBattleLogicField field)
    {
        ClearBullet();

        if (field == null)
            return;

        foreach (List<LogicHitBullet> bullectList in field.battleBulletItemDic.Values)
        {
            foreach (LogicHitBullet bullet in bullectList)
            {
                if (bullet.isMoving)
                {
                    bullet.OpenLogicQueue();
                    m_bullectList.Add(bullet);
                }
            }
        }
    }
        
    public void SendMsg(Wup.Jce.JceStruct msg)
    {
        if (m_sender != null)
        {
            m_sender.EqueueMessage(msg);
        }
    }

    // 初始化所有的玩家
    public void InitAllPlayer()
    {
        if (m_sender != null)
        {
            ACG_TCmdS2CNotifyPlayerChange msg = m_sender.Alloc<ACG_TCmdS2CNotifyPlayerChange>();
            msg.playerChange = null;
            msg.iMode = 2;
            m_sender.EqueueMessage(msg);
        }
    }

    public void ClearBullet()
    {
        foreach (LogicHitBullet hitBullet in m_bullectList)
        {
            hitBullet.CloseQueue();
        }
        m_bullectList.Clear();
    }
    
    #endregion

    public void Release()
    {
        m_sender = null;
        ObservedPlayer(null);
        
        m_players.Clear();
        m_bullets.Clear();
        m_playersSet.Clear();
        
        m_myChairId = -1;

        ClearBullet();
    }
}

#endif
