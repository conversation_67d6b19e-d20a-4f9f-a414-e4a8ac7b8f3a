using ACG.Core;
using GameFramework.FMath;
using LogicFrameWork;
using TKFrame;
using Z_PVE;
using ZGameClient;

/// <summary>
/// 轮抽部分待选英雄逻辑
/// </summary>
public class RoundSelectLogicHero : LogicGameObject
{
    public int Pos
    {
        get;
        set;
    }

    private int m_ID;
    public int Id
    {
        get
        {
            return m_ID;
        }
    }

    private ACG_Equipment m_equipMent;
    public ACG_Equipment equipMent
    {
        get
        {
            return m_equipMent;
        }
    }

    private int m_type;
    public int Type
    {
        get
        {
            return m_type;
        }
    }

    private int m_extraCount;
    public int ExtraCount
    {
        get
        {
            return m_extraCount;
        }
    }

    private TACG_Hero_Client m_tabData;
    public TACG_Hero_Client tabData
    {
        get
        {
            return m_tabData;
        }
    }

    private RoundSelectLogicPlayer m_owner = null;
    public bool isSelected
    {
        get
        {
            return m_owner == null ? false : true;
        }
    }

    // 起始位置
    private FVector3 m_initPos;
    public FVector3 initPos
    {
        get
        {
            return m_initPos;
        }
    }

    // 计算旋转使用
    private FTransform m_center;
    private FVector3 m_offset;
    private FQuaternion m_initRotation;
    private FQuaternion lastCenterRotation = FQuaternion.identity;

    public RoundSelectLogicHero()
    {
#if UNITY_EDITOR && ACGGAME_CLIENT
        logicType = LogicType.RoundSelectBattleLogicHero;
        AddDebug();
#endif
    }

    public void InitPos(FTransform center, Fix64 radius, Fix64 num, Fix64 totalNum)
    {
        m_center = center;
        m_offset = new FVector3(radius, Fix64.zero, Fix64.zero);

        Fix64 angle = Fix64.pi * 2 * num / totalNum;
        m_initRotation = FQuaternion.AngleAxis(angle, FVector3.up);

        m_initPos = m_initRotation * m_offset;
        fTransform.position = m_initPos;
    }

    public RoundSelectAddHero InitHero(CSoConfig soConfig, bool isOpenQueue)
    {
        m_coliderR = GameDataMng.Instance.GetRoundSelectHeroRadius();
        fTransform.position = m_center.rotation * m_initRotation * m_offset;
        Diagnostic.Log("Logic Hero init " + m_ID + " " + Pos);
        
        RoundSelectAddHero addHero = null;
        
#if ACGGAME_CLIENT
        if (isOpenQueue)
        {
            addHero = LogicMsgPool.Instance.GetMsg<RoundSelectAddHero>();
            addHero.pos = Pos;
            addHero.id = m_ID;
            addHero.type = m_type;
            addHero.extraCount = m_extraCount;
            addHero.tabData = m_tabData;
            addHero.equipMent = m_equipMent;
            addHero.initPos = m_initPos;
        }
#endif

        return addHero;
    }

    public override void Release()
    {
        base.Release();
        fTransform.position = m_initPos;
        m_owner = null;
    }

    public override void RunTick(Fix64 deltaTime)
    {
        base.RunTick(deltaTime);
        if (!isSelected)
        {
            FQuaternion curQua = m_center.rotation;
            if (!FQuaternion.Equal_Ref(ref lastCenterRotation, ref curQua))
            {
                fTransform.position = curQua * m_initRotation * m_offset;
                lastCenterRotation = curQua;
            }
        }
    }

    public void SetSelected(RoundSelectLogicPlayer player)
    {
        m_owner = player;
    }


}


