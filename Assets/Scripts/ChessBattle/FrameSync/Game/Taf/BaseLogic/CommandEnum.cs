public enum RoundSelectMessage
{
    Init = 100,
    TurnToReleasePre3s,
    TurnToReleasePlayers,
    ChooseHero,
    CollideOwn,
    CollideOther,
    SwitchControlWay,
}

public enum AllGameMessage
{
    // 轮抽事件
    RoundSelect_StartPreShow,
    RoundSelect_ShowPlayer,
    RoundSelect_EndPreShow,
    RoundSelect_AddHero,
    RoundSelect_TurnToReleasePlayers,
    RoundSelect_TurnToReleasePre3s,
    RoundSelect_ChooseHero,
    RoundSelect_FirstRelease,
    RoundSelect_ShowUnRelease,
    RoundSelect_Rotation,

    // 子弹事件
    Bullet_None,
    Bullet_StartFly,
    Bullet_Fly,
    Bullet_Stop,
    Bullet_Reset,
}

public enum BulletMoveMessege
{
    None = 0,
    Wait = 1,
    StartFly = 2,
    Fly = 3,
    Stop = 4,
    Hide = 5,
    Reset = 6,
}

public enum PlayerMoveMessage
{
    SimpleWalk = 0,
    StartWalk,
    Walk,
    StopWalk,
    StopWalk02,
    SimpleRun,
    StartRun,
    Run,
    StopRun,
    StopRun02,
    Rush,
    PreStop,
    Stop,
    GoBack,
    JumpTo,
    JumpBack,
    Idle,
    SetTarget,
    CorrectPos,
    UpdateConfig,
    ResetPlayer,
    Rotate,
    PositionAndRotate,
    Init,

    PlayAnimation,
    StopAnimation,
    PlayEffect,
    StopEffect,
    PlayExpression,

    // 轮抽小队长事件
    RoundSelectPlayer_CollideOwn,
    RoundSelectPlayer_CollideOther,
    Teleport,      // 传送到某个点

    PlayAction, // 播动作

    TriggerAction,

    Attack,     // 攻击对手
    Hit,       // 被攻击
    WillHit,    // 到达目标 即将攻击对手
    SimpleHit,      // 当真人跑去其他战场的时候，通知真人做一些简单的受击反应

    RootMotion,

    None = 9999,
}