using System.Collections.Generic;
using TKFrame;

public class LogicData
{
    public int Id;
    public object data;
    public uint frameCount;
    public volatile bool isExecuted;

    public void Clear()
    {
        frameCount = 0;
        Id = -1;
        data = null;
        isExecuted = true;
    }
}

public class LogicDataPool : ReleaseSingleton<LogicDataPool>
{

    private int maxNum = 40;
    private Queue<LogicData> m_pools;

    public LogicDataPool()
    {
        m_pools = new Queue<LogicData>(maxNum);
    }

    public LogicData Get()
    {
        LogicData result = null;
        if(m_pools.Count == 0)
        {
            result = new LogicData();
        }
        else
        {
            result = m_pools.Dequeue();
        }
        return result;
    }

    public void Free(LogicData data)
    {
        data.Clear();
        if (m_pools.Count < 40)
        {
            m_pools.Enqueue(data);
        }
    }
}
