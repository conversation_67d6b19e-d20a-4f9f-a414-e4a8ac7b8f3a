using GameFramework.Common.FSM;
using GameFramework.Common.FSM.TransitConditions;
using GameFramework.FMath;
using ZGame;
using System.Collections.Generic;
using System;

public enum EndMoveWay
{
    Idle,
    WalkStop1,
    WalkStop2,
    RunStop1,
    RunStop2,
    Rush,
}

public class MovementStateMachine : StateMachine<MovementStateMachine>
{
    public static bool useConfigMode = false;
    
    private static readonly Fix64 m_minDis = Fix64._01;

    #region 条件命名
    
    public const string MoveState = "MoveState";
    public const string FromDstSqr = "FromDstSqr";
    public const string MoveNum = "MoveNum";
    
    #endregion
    
    #region 统一生成转移条件

    private static readonly CheckInt<MovementStateMachine> startWalkTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.StartWalk, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> walkTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.Walk, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> stopWalkTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.StopWalk, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> stopWalk2Trans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.StopWalk02, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> simpleWalkTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.SimpleWalk, CompFunc.Equal);
            
    private static readonly CheckInt<MovementStateMachine> startRunTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.StartRun, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> runTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.Run, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> stopRunTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.StopRun, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> stopRun2Trans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.StopRun02, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> simpleRunTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.SimpleRun, CompFunc.Equal);

    private static readonly CheckInt<MovementStateMachine> jumpToTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.JumpTo, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> jumpBackTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.JumpBack, CompFunc.Equal);
            
    private static readonly CheckInt<MovementStateMachine> rushTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.Rush, CompFunc.Equal);
            
    private static readonly CheckInt<MovementStateMachine> idleTrans = new CheckInt<MovementStateMachine>(MoveState, (int) PlayerMoveMessage.Stop, CompFunc.Equal);
            
    private static readonly CheckInt<MovementStateMachine> walkStop1Weight = new CheckInt<MovementStateMachine>(MoveNum, (int)EndMoveWay.WalkStop1, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> walkStop2Weight = new CheckInt<MovementStateMachine>(MoveNum, (int)EndMoveWay.WalkStop2, CompFunc.Equal);
    
    private static readonly CheckInt<MovementStateMachine> runStop1Weight = new CheckInt<MovementStateMachine>(MoveNum, (int)EndMoveWay.RunStop1, CompFunc.Equal);
    private static readonly CheckInt<MovementStateMachine> runStop2Weight = new CheckInt<MovementStateMachine>(MoveNum, (int)EndMoveWay.RunStop2, CompFunc.Equal);
    
    private static readonly CheckInt<MovementStateMachine> rushWeight = new CheckInt<MovementStateMachine>(MoveNum, (int)EndMoveWay.Rush, CompFunc.Equal);

    private static readonly CheckComplete<MovementStateMachine> completeTrans = new CheckComplete<MovementStateMachine>();
    
    private static readonly CheckFix64<MovementStateMachine> minDisTrans = new CheckFix64<MovementStateMachine>(FromDstSqr, m_minDis, CompFunc.EqualOrLess);

    #endregion
    
    public static Dictionary<string, List<TransitionNode>> s_cacheNodeDic = new Dictionary<string, List<TransitionNode>>();
    public const string commonConfingName = "common_statemachine";

    private int moveNumKeyIndex = -1;

    private BaseLogicMovement m_logicMovement;
    public BaseLogicMovement logicMovement
    {
        get
        {
            return m_logicMovement;
        }
    }

    public ChessPlayerLogicConfig m_config;

    public Fix64 deltaTime
    {
        get;
        set;
    }

    public CSoGame m_soGame;

    public MovementStateMachine() { }

    public MovementStateMachine(BaseLogicMovement logicMovement, CSoGame soGame)
    {
        m_soGame = soGame;
        context = this;
        m_logicMovement = logicMovement;
        m_config = logicMovement.playerConfig;
        Init();
    }

    public MovementStateMachine(ChessPlayerLogicConfig playerConfig, CSoGame soGame)
    {
        m_soGame = soGame;
        if (playerConfig != null)
        {
            m_config = playerConfig;
            Init();
        }
    }

    public void InitMovementData(BaseLogicMovement logicMovement)
    {
        context = this;
        m_logicMovement = logicMovement;
        Start();
    }

    public void Reset()
    {
        SetInt(MoveState, (int)PlayerMoveMessage.Stop);
        SetFix64(FromDstSqr, Fix64.zero.rawValue);
        SetInt(MoveNum, (int)EndMoveWay.Idle);
        Stop();
    }

    private void Init()
    {
        StateGraph<MovementStateMachine> graph = new StateGraphT<MovementStateMachine, StateGraphStorage_AdjacencyList<MovementStateMachine>>();

        // idle状态
        State<MovementStateMachine> idle = graph.AddState(IdleState.StateName, new IdleState());
        idle.SetAsDefaultState();

        // 跳回起点
        State<MovementStateMachine> jumpBack = graph.AddState(JumpBackState.StateName, new JumpBackState());

        // 跳到敌人位置
        State<MovementStateMachine> jumpTo = graph.AddState(JumpToState.StateName, new JumpToState());

        // 开始行走
        State<MovementStateMachine> startWalk = graph.AddState(StartWalkState.StateName, new StartWalkState());

        // 行走中
        State<MovementStateMachine> walking = graph.AddState(WalkState.StateName, new WalkState());

        // 行走结束
        State<MovementStateMachine> stopWalk = graph.AddState(StopWalkState.StateName, new StopWalkState());
        
        // 行走结束短距离
        State<MovementStateMachine> stopWalk02 = graph.AddState(StopWalk02State.StateName, new StopWalk02State());
        
        // 开始奔跑
        State<MovementStateMachine> startRun = graph.AddState(StartRunState.StateName, new StartRunState());

        // 奔跑中
        State<MovementStateMachine> running = graph.AddState(RunState.StateName, new RunState());

        // 奔跑结束
        State<MovementStateMachine> stopRun = graph.AddState(StopRunState.StateName, new StopRunState());
        
        // 奔跑结束短距离
        State<MovementStateMachine> stopRun02 = graph.AddState(StopRun02State.StateName, new StopRun02State());

        // 简单奔跑
        State<MovementStateMachine> simpleRunning = graph.AddState(SimpleRunState.StateName, new SimpleRunState());

        // 简单行走
        State<MovementStateMachine> simpleWalking = graph.AddState(SimpleWalkState.StateName, new SimpleWalkState());

        // 冲刺
        State<MovementStateMachine> rushing = graph.AddState(RushState.StateName, new RushState());

        if (useConfigMode)
        {
            #region 新的读配置实现
            if (s_cacheNodeDic.TryGetValue(commonConfingName, out List<TransitionNode> nodeList))
            {
                //直接使用缓存加载好的数据列表
                ParseNodeList(graph, nodeList);
            }
            #endregion
        }
        else
        {
            #region 旧的硬代码实现
            
            
            
            #region 简单走路

            graph.AddTransition(simpleWalking, running, runTrans);
            graph.AddTransition(simpleWalking, rushing, rushTrans);
            graph.AddTransition(simpleWalking, jumpTo, jumpToTrans);
            graph.AddTransition(simpleWalking, jumpBack, jumpBackTrans);
            graph.AddTransition(simpleWalking, idle, idleTrans);
            graph.AddTransition(simpleWalking, walking, walkTrans);

            graph.AddTransition(idle, simpleWalking, simpleWalkTrans);
            graph.AddTransition(jumpBack, simpleWalking, simpleWalkTrans);
            graph.AddTransition(jumpTo, simpleWalking, simpleWalkTrans);
            
            graph.AddTransition(startWalk, simpleWalking, simpleWalkTrans);
            graph.AddTransition(walking, simpleWalking, simpleWalkTrans);
            graph.AddTransition(stopWalk, simpleWalking, simpleWalkTrans);
            graph.AddTransition(stopWalk02, simpleWalking, simpleWalkTrans);
            
            graph.AddTransition(startRun, simpleWalking, simpleWalkTrans);
            graph.AddTransition(running, simpleWalking, simpleWalkTrans);
            graph.AddTransition(stopRun, simpleWalking, simpleWalkTrans);
            graph.AddTransition(stopRun02, simpleWalking, simpleWalkTrans);
            
            graph.AddTransition(rushing, simpleWalking, simpleWalkTrans);
            
            graph.AddTransition(simpleWalking, idle, minDisTrans);
            #endregion

            #region 走路
            
            #region Walking
            
            graph.AddTransition(walking, running, runTrans);
            graph.AddTransition(walking, rushing, rushTrans);
            graph.AddTransition(walking, jumpTo, jumpToTrans);
            graph.AddTransition(walking, jumpBack, jumpBackTrans);
            graph.AddTransition(walking, idle, idleTrans);
            
            #endregion

            #region StartWalking
            
            if (m_config != null && m_config.hasWalkStart())
            {
                graph.AddTransition(startWalk, running, runTrans);
                graph.AddTransition(startWalk, rushing, rushTrans);
                graph.AddTransition(startWalk, jumpTo, jumpToTrans);
                graph.AddTransition(startWalk, jumpBack, jumpBackTrans);
                graph.AddTransition(startWalk, idle, idleTrans);
                graph.AddTransition(startWalk, walking, walkTrans);

                graph.AddTransition(idle, startWalk, startWalkTrans);
                graph.AddTransition(jumpBack, startWalk, startWalkTrans);
                graph.AddTransition(jumpTo, startWalk, startWalkTrans);
                graph.AddTransition(startWalk, walking, completeTrans);
            }
            else
            {
                graph.AddTransition(idle, walking, walkTrans);
                graph.AddTransition(jumpBack, walking, walkTrans);
                graph.AddTransition(jumpTo, walking, walkTrans);
            }
            
            #endregion

            #region stopWalking
            
            int walkStopWeight = m_config.walkStopPre;
            int walk02StopWeight = m_config.walk02StopPre;
            
            if (m_config.totalWalkPre > 0)
            {
                #region stopWalk 设置
                
                if (m_config.hasWalkStop())
                {
                    graph.AddTransition(stopWalk, running, runTrans);
                    graph.AddTransition(stopWalk, rushing, rushTrans);
                    graph.AddTransition(stopWalk, jumpTo, jumpToTrans);
                    graph.AddTransition(stopWalk, jumpBack, jumpBackTrans);
                    graph.AddTransition(stopWalk, idle, idleTrans);
                    graph.AddTransition(stopWalk, walking, walkTrans);
                    
                    if (walkStopWeight > 0)
                    {
                        // 距离权值
                        Fix64 stopDis = m_config.walkStopDisFix64;
                        CheckFix64<MovementStateMachine> disCondition = new CheckFix64<MovementStateMachine>(FromDstSqr, stopDis + m_minDis, CompFunc.EqualOrLess);

                        // 合并，同时满足
                        ITransitCondition<MovementStateMachine> combeCondition = new CombeCondition<MovementStateMachine>(disCondition, walkStop1Weight,
                            CombeCondition<MovementStateMachine>.Operation.And);
                    
                        graph.AddTransition(walking, stopWalk, combeCondition);
                    }
                }
                
                #endregion

                #region stopWalk02 设置
                
                if (m_config.hasWalk02Stop())
                {
                    graph.AddTransition(stopWalk02, running, runTrans);
                    graph.AddTransition(stopWalk02, rushing, rushTrans);
                    graph.AddTransition(stopWalk02, jumpTo, jumpToTrans);
                    graph.AddTransition(stopWalk02, jumpBack, jumpBackTrans);
                    graph.AddTransition(stopWalk02, idle, idleTrans);
                    graph.AddTransition(stopWalk02, walking, walkTrans);
                    
                    if (walk02StopWeight > 0)
                    {
                        // 距离权值
                        Fix64 stopDis = m_config.walk02StopDisFix64;
                        CheckFix64<MovementStateMachine> disCondition = new CheckFix64<MovementStateMachine>(FromDstSqr, stopDis + m_minDis, CompFunc.EqualOrLess);

                        // 合并，同时满足
                        ITransitCondition<MovementStateMachine> combeCondition = new CombeCondition<MovementStateMachine>(disCondition, walkStop2Weight,
                            CombeCondition<MovementStateMachine>.Operation.And);
                    
                        graph.AddTransition(walking, stopWalk02, combeCondition);
                    }
                }
                
                #endregion

                #region 默认执行Idle
                
                if (m_config.totalWalkPre == 0)
                {
                    graph.AddTransition(walking, idle, minDisTrans);
                }
                
                #endregion

                #region 短距离停止,不经过Walking,选择距离短的Stop
                
                Fix64 minDis = Fix64.zero;
                State<MovementStateMachine> minDisStopState = idle;
                
                if (m_config.hasRunStop())
                {
                    minDis = m_config.walkStopDisFix64;
                    minDisStopState = stopWalk;
                }
                else if (m_config.hasRun02Stop())
                {
                    if (minDis == Fix64.zero || m_config.run02StopDisFix64 < minDis)
                    {
                        minDis = m_config.run02StopDisFix64;
                        minDisStopState = stopWalk02;
                    }
                }

                CheckFix64<MovementStateMachine> disConditon = new CheckFix64<MovementStateMachine>(FromDstSqr, minDis + m_minDis, CompFunc.EqualOrLess);
                graph.AddTransition(startWalk, minDisStopState, disConditon);
                
                #endregion

                #region stop后进入Idle
                
                if (m_config.hasWalkStop())
                    graph.AddTransition(stopWalk, idle, completeTrans);
                
                if (m_config.hasWalk02Stop())
                    graph.AddTransition(stopWalk02, idle, completeTrans);
                
                #endregion
            }
            else
            {
                graph.AddTransition(walking, idle, minDisTrans);
            }
            
            #endregion
            
            #endregion

            #region 冲刺
            
            if (m_config.hasRush())
            {
                graph.AddTransition(rushing, jumpTo, jumpToTrans);
                graph.AddTransition(rushing, jumpBack, jumpBackTrans);
                graph.AddTransition(rushing, idle, idleTrans);
                graph.AddTransition(rushing, walking, walkTrans);
                graph.AddTransition(rushing, simpleWalking, simpleWalkTrans);
                graph.AddTransition(rushing, running, runTrans);
                graph.AddTransition(rushing, simpleRunning, simpleRunTrans);

                // 距离条件
                Fix64 stopDis = m_config.rushDisFix64;
                CheckFix64<MovementStateMachine> disCondition = new CheckFix64<MovementStateMachine>(FromDstSqr, stopDis + m_minDis, CompFunc.EqualOrLess);

                // 合并，同时满足
                ITransitCondition<MovementStateMachine> combeCondition = new CombeCondition<MovementStateMachine>(disCondition, rushWeight,
                    CombeCondition<MovementStateMachine>.Operation.And);
                
                graph.AddTransition(running, rushing, combeCondition);
                graph.AddTransition(simpleRunning, rushing, combeCondition);
                graph.AddTransition(idle, rushing, rushTrans);
            
                graph.AddTransition(rushing, idle, completeTrans);
            }
            
            #endregion

            #region 简单跑步

            graph.AddTransition(simpleRunning, running, runTrans);
            graph.AddTransition(simpleRunning, rushing, rushTrans);
            graph.AddTransition(simpleRunning, jumpTo, jumpToTrans);
            graph.AddTransition(simpleRunning, jumpBack, jumpBackTrans);
            graph.AddTransition(simpleRunning, idle, idleTrans);
            graph.AddTransition(simpleRunning, walking, walkTrans);

            graph.AddTransition(idle, simpleRunning, simpleRunTrans);
            graph.AddTransition(jumpBack, simpleRunning, simpleRunTrans);
            graph.AddTransition(jumpTo, simpleRunning, simpleRunTrans);
            
            graph.AddTransition(startWalk, simpleRunning, simpleRunTrans);
            graph.AddTransition(walking, simpleRunning, simpleRunTrans);
            graph.AddTransition(stopWalk, simpleRunning, simpleRunTrans);
            graph.AddTransition(stopWalk02, simpleRunning, simpleRunTrans);
            
            graph.AddTransition(startRun, simpleRunning, simpleRunTrans);
            graph.AddTransition(running, simpleRunning, simpleRunTrans);
            graph.AddTransition(stopRun, simpleRunning, simpleRunTrans);
            graph.AddTransition(stopRun02, simpleRunning, simpleRunTrans);
            
            graph.AddTransition(rushing, simpleRunning, simpleRunTrans);
            
            graph.AddTransition(simpleRunning, idle, minDisTrans);
            #endregion

            #region 跑步
            
            #region Running
            
            graph.AddTransition(running, walking, walkTrans);
            graph.AddTransition(running, rushing, rushTrans);
            graph.AddTransition(running, jumpTo, jumpToTrans);
            graph.AddTransition(running, jumpBack, jumpBackTrans);
            graph.AddTransition(running, idle, idleTrans);
            
            graph.AddTransition(idle, running, runTrans);
            graph.AddTransition(jumpBack, running, runTrans);
            graph.AddTransition(jumpTo, running, runTrans);
            
            #endregion

            #region startRunning
            
            if (m_config.hasRunStart())
            {
                graph.AddTransition(startRun, walking, walkTrans);
                graph.AddTransition(startRun, rushing, rushTrans);
                graph.AddTransition(startRun, jumpTo, jumpToTrans);
                graph.AddTransition(startRun, jumpBack, jumpBackTrans);
                graph.AddTransition(startRun, idle, idleTrans);
                graph.AddTransition(startRun, running, runTrans);

                graph.AddTransition(idle, startRun, startRunTrans);
                graph.AddTransition(jumpBack, startRun, startRunTrans);
                graph.AddTransition(jumpTo, startRun, startRunTrans);
                graph.AddTransition(startRun, running, completeTrans);
            }

            #endregion

            #region stopRunning

            // 有停止动作
            if (m_config.totalRunPre > 0)
            {
                #region stopRun 设置
                
                if (m_config.hasRunStop())
                {
                    graph.AddTransition(stopRun, walking, walkTrans);
                    graph.AddTransition(stopRun, rushing, rushTrans);
                    graph.AddTransition(stopRun, jumpTo, jumpToTrans);
                    graph.AddTransition(stopRun, jumpBack, jumpBackTrans);
                    graph.AddTransition(stopRun, idle, idleTrans);
                    graph.AddTransition(stopRun, running, runTrans);
                    
                    if (m_config.runStopPre > 0)
                    {
                        // 距离权值
                        Fix64 stopDis = m_config.runStopDisFix64;
                        CheckFix64<MovementStateMachine> disCondition = new CheckFix64<MovementStateMachine>(FromDstSqr, stopDis + m_minDis, CompFunc.EqualOrLess);

                        // 合并，同时满足
                        ITransitCondition<MovementStateMachine> combeCondition = new CombeCondition<MovementStateMachine>(disCondition, runStop1Weight,
                            CombeCondition<MovementStateMachine>.Operation.And);
                    
                        graph.AddTransition(running, stopRun, combeCondition);
                    }
                }
                
                #endregion
                
                #region stopRun02 设置
                
                if (m_config.hasRun02Stop())
                {
                    graph.AddTransition(stopRun02, walking, walkTrans);
                    graph.AddTransition(stopRun02, rushing, rushTrans);
                    graph.AddTransition(stopRun02, jumpTo, jumpToTrans);
                    graph.AddTransition(stopRun02, jumpBack, jumpBackTrans);
                    graph.AddTransition(stopRun02, idle, idleTrans);
                    graph.AddTransition(stopRun02, running, runTrans);
                    
                    if (m_config.run02StopPre > 0)
                    {
                        // 距离权值
                        Fix64 stopDis = m_config.run02StopDisFix64;
                        CheckFix64<MovementStateMachine> disCondition = new CheckFix64<MovementStateMachine>(FromDstSqr, stopDis + m_minDis, CompFunc.EqualOrLess);

                        // 合并，同时满足
                        ITransitCondition<MovementStateMachine> combeCondition = new CombeCondition<MovementStateMachine>(disCondition, runStop2Weight,
                            CombeCondition<MovementStateMachine>.Operation.And);
                    
                        graph.AddTransition(running, stopRun02, combeCondition);
                    }
                }
                
                #endregion

                #region 默认执行Idle
                
                if (m_config.totalRunPre == 0)
                {
                    graph.AddTransition(running, idle, minDisTrans);
                }
                
                #endregion
                
                #region 短距离停止,不经过running,选择距离短的Stop
                
                Fix64 minDis = Fix64.zero;
                State<MovementStateMachine> minDisStopState = idle;

                if (m_config.hasRunStop())
                {
                    minDis = m_config.runStopDisFix64;
                    minDisStopState = stopRun;
                }
                else if (m_config.hasRun02Stop())
                {
                    if (minDis == Fix64.zero || m_config.run02StopDisFix64 < minDis)
                    {
                        minDis = m_config.run02StopDisFix64;
                        minDisStopState = stopRun02;
                    }
                }

                CheckFix64<MovementStateMachine> disConditon = new CheckFix64<MovementStateMachine>(FromDstSqr, minDis + m_minDis, CompFunc.EqualOrLess);
                graph.AddTransition(startRun, minDisStopState, disConditon);
                
                #endregion

                #region stop后进入Idle
                
                if (m_config.hasRunStop())
                    graph.AddTransition(stopRun, idle, completeTrans);
                
                if (m_config.hasRun02Stop())
                    graph.AddTransition(stopRun02, idle, completeTrans);
                
                #endregion
                
            }
            else
            {
                graph.AddTransition(running, idle, minDisTrans);
            }
            
            #endregion
            
            #endregion

            #region 跳回去
            graph.AddTransition(idle, jumpBack, jumpBackTrans);
            graph.AddTransition(jumpBack, idle, completeTrans);
            #endregion

            #region 跳到敌方位置
            graph.AddTransition(idle, jumpTo, jumpToTrans);
            graph.AddTransition(jumpTo, idle, completeTrans);
            #endregion
            
            #endregion
        }

        InitWithGraph(graph);
        Start();
    }

    private void ParseNodeList(StateGraph<MovementStateMachine> graph, List<TransitionNode> nodeList)
    {
        for (int i = 0, n = nodeList.Count; i < n; i++)
        {
            ParseOne(graph, nodeList[i]);
        }
    }

    private void ParseOne(StateGraph<MovementStateMachine> graph, TransitionNode node)
    {
        if (node.checkStateEnum == E_CheckStateCount.NONE)
        {
            AddTransitionByConfig(graph, node);
        }
        else if (node.checkStateEnum == E_CheckStateCount.WALK_START)
        {
            if (node.isCheckStateElse)
            {
                if (m_config.walkStartFix64.Count <= 0)
                {
                    AddTransitionByConfig(graph, node);
                }
            }
            else if (m_config.walkStartFix64.Count > 0)
            {
                AddTransitionByConfig(graph, node);
            }
        }
        else if (node.checkStateEnum == E_CheckStateCount.WALK_STOP)
        {
            if (node.isCheckStateElse)
            {
                if (m_config.walkStopFix64.Count <= 0)
                {
                    AddTransitionByConfig(graph, node);
                }
            }
            else if (m_config.walkStopFix64.Count > 0)
            {
                AddTransitionByConfig(graph, node);
            }
        }
        else if (node.checkStateEnum == E_CheckStateCount.RUN_START)
        {
            if (node.isCheckStateElse)
            {
                if (m_config.runStartFix64.Count <= 0)
                {
                    AddTransitionByConfig(graph, node);
                }
            }
            else if (m_config.runStartFix64.Count > 0)
            {
                AddTransitionByConfig(graph, node);
            }
        }
        else if (node.checkStateEnum == E_CheckStateCount.RUN_STOP)
        {
            if (node.isCheckStateElse)
            {
                if (m_config.runStopFix64.Count <= 0)
                {
                    AddTransitionByConfig(graph, node);
                }
            }
            else if (m_config.runStopFix64.Count > 0)
            {
                AddTransitionByConfig(graph, node);
            }
        }
    }

    private void AddTransitionByConfig(StateGraph<MovementStateMachine> graph, TransitionNode node)
    {
        var preState = graph.GetStateByName(node.preAction);
        var toState = graph.GetStateByName(node.toAction);

        graph.AddTransition(preState, toState, GetCondition(
            node.conditionName, node.triggerName,
            node.moveType, node.compFunc,
            node.paramType, node.extendDataList)
            );
    }

    private ITransitCondition<MovementStateMachine> GetCondition(string conditionName, string triggerName,
        PlayerMoveMessage moveType, CompFunc compFunc, E_CheckConditionParam paramType, List<CombeConditionData> extList = null)
    {
        switch (conditionName)
        {
            case "CheckInt":
                return new CheckInt<MovementStateMachine>(triggerName, (int)moveType, compFunc);
            case "CheckFix64":
                return new CheckFix64<MovementStateMachine>(triggerName, GetParamByType(paramType), compFunc);
            case "CheckComplete":
                return new CheckComplete<MovementStateMachine>();
            case "CombeCondition":
                if (extList != null && extList.Count > 0)
                {
                    var extData1 = extList[0];
                    var a = GetCondition(extData1.conditionName, extData1.triggerName,
                        PlayerMoveMessage.None, extData1.compFunc, extData1.paramType);

                    var extData2 = extList[1];
                    var b = GetCondition(extData2.conditionName, extData2.triggerName,
                        PlayerMoveMessage.None, extData2.compFunc, extData2.paramType);

                    return new CombeCondition<MovementStateMachine>(a, b, CombeCondition<MovementStateMachine>.Operation.And);
                }
                break;
        }

        return null;
    }

    private Fix64 GetParamByType(E_CheckConditionParam type)
    {
        switch (type)
        {
            case E_CheckConditionParam.MIN_DIS:
                return m_minDis;
            case E_CheckConditionParam.WALK_STOP:
                return m_config.walkStopPre.ToFix64();
            case E_CheckConditionParam.WALK_STOP_AND_WALK_IDLE:
                return (m_config.walkStopPre + m_config.walk02StopPre).ToFix64();
            case E_CheckConditionParam.WALK_STOP_DIS:
                return m_config.walkStopDisFix64 * m_config.walkStopDisFix64 + m_minDis;
            case E_CheckConditionParam.RUN_STOP:
                return m_config.runStopPre.ToFix64();
            case E_CheckConditionParam.RUN_STOP_AND_RUN_IDLE:
                return (m_config.runStopPre + m_config.run02StopPre).ToFix64();
            case E_CheckConditionParam.RUN_STOP_DIS:
                return m_config.runStopDisFix64 * m_config.runStopDisFix64 + m_minDis;
        }

        return Fix64.zero;
    }

public void GetRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {
    }

    public void DoRegainData(RegainPlayerData regainPlayerData, RegainLogicPlayerData regainLogicPlayerData)
    {

    }

    public string GetStateName(PlayerMoveMessage message)
    {
        return message.ToString();
    }
}

public enum E_CheckStateCount
{
    NONE = 0,
    WALK_START = 1,
    WALK_STOP = 2,
    RUN_START = 3,
    RUN_STOP = 4,
}

public enum E_CheckConditionParam
{
    NODE = 0,
    MIN_DIS = 1,
    WALK_STOP = 2,
    WALK_STOP_AND_WALK_IDLE = 3,
    WALK_STOP_DIS = 4,
    RUN_STOP = 5,
    RUN_STOP_AND_RUN_IDLE = 6,
    RUN_STOP_DIS = 7,
}

[Serializable]
public class CombeConditionData
{
    public string triggerName = "MoveState";

    public string conditionName = "CheckInt";

    public CompFunc compFunc = CompFunc.Equal;

    public E_CheckConditionParam paramType = E_CheckConditionParam.NODE;
}