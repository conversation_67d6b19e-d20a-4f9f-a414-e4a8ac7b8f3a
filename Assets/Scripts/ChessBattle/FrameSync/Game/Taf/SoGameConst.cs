namespace ZGameClient
{
	public enum E_DEAL_HERO_AREA_TYPE
	{
		E_DEAL_HERO_AREA_TYPE_WAIT = 0,
		E_DEAL_HERO_AREA_TYPE_SHARE = 1,
		E_DE<PERSON>_HERO_AREA_TYPE_BATTLE = 2,
		E_DEAL_HERO_AREA_TYPE_PURCHASE = 3,
		E_DEAL_HERO_AREA_TYPE_NONE = 4,
	}

	public enum TAC_GameState
	{
		TAC_GameState_Begin = 0,
		TAC_GameState_GameStart = 1,
		TAC_GameState_LoadData,
		TAC_GameState_ExchangeHero,
		TAC_GameState_Ready,
		TAC_GameState_Arrive,
		TAC_GameState_Play,
		TAC_GameState_FightOverTime,
		TAC_GameState_FightDelay,
		TAC_GameState_Departure,
        TAC_GameState_DepartureDelay,
		TAC_GameState_Finish,
		TAC_GameState_RoundSelect,
		TAC_GameState_RoundSelectStart,
		TAC_GameState_RoundSelectChoose,
		TAC_GameState_RoundSelectStop,
	}

	public enum TAC_GAME_SC_MSG_ID
	{
		TAC_SC_NOTIFY_GAME_START = 1,
		TAC_SC_NOTIFY_GAME_END = 2,
		TAC_SC_NOTIFY_TURN_START = 3,
		TAC_SC_NOTIFY_TURN_ACKED_BATTLEFIELD = 4,
		TAC_SC_NOTIFY_BATTLE_RESULT = 5,
		TAC_SC_NOTIFY_ALL_GAME_DATA = 6,
		TAC_SC_NOTIFY_BUY_HERO = 7,
		TAC_SC_NOTIFY_UP_HERO = 8,
		TAC_SC_NOTIFY_DOWN_HERO = 9,
		TAC_SC_NOTIFY_PLAY_BATTLE_END = 10,
		TAC_SC_NOTIFY_SELL_HERO = 11,
		TAC_SC_NOTIFY_REFRESH_BUYHERO_LIST = 12,
		TAC_SC_NOTIFY_MOVE_BATTLEGROUND_HERO = 13,
		TAC_SC_NOTIFY_UPDATE_LEVEL = 14,
		TAC_SC_NOTIFY_GAMEOVER_USER_RANK_LIST = 15,
		TAC_SC_NOTIFY_MOVE_WAIT_HERO = 16,
		TAC_SC_NOTIFY_USER_OUT = 17,
		TAC_SC_NOTIFY_START_PLAY = 18,
		TAC_SC_NOTIFY_SET_REFRESH_STATUS = 19,
		TAC_SC_NOTIFY_BUY_HERO_TO_BATTLEGROUND = 20,
		TAC_SC_NOTIFY_SET_AUTO_UP_STATUS = 21,
		TAC_SC_NOTIFY_SWITCH_PLAYER = 22,
		TAC_SC_NOTIFY_HERO_MARKET_BUY = 23,
		TAC_SC_NOTIFY_INITIATIVE_HERO_PROMOTION = 24,
		TAC_SC_NOTIFY_TURN_ARRIVE = 25,
		TAC_SC_NOTIFY_HERO_PROMOTION_FINISH = 26,
		TAC_SC_NOTIFY_UPDATE_FETTER_MERGE_HERO = 27,
		TAC_SC_NOTIFY_WAIT_HERO_PROMOTION = 28,
		TAC_SC_NOTIFY_OBSERVE_PLAYER = 29,
		TAC_SC_NOTIFY_PLAYER_CHANGE = 30,
		TAC_SC_NOTIFY_PLAYER_SUPPORT_FRIEND = 31,
	}

	public enum TAC_REFRESH_STATUS
	{
		TAC_REFRESH_STATUS_UNLOCK = 0,
		TAC_REFRESH_STATUS_LOCK = 1,
	}

	public enum SO_ERROR_CODE
	{
		ERROR_TAC_ROOM_ID_NOT_ENOUGH_COIN = 100302,
		ERROR_TAC_ROOM_ID_NO_LIFE = 100303,
		ERROR_TAC_ROOM_ID_PARAM_ERROR = 100304,
		ERROR_TAC_ROOM_ID_STATE_ERROR = 100305,
		ERROR_TAC_ROOM_ID_WAIT_NOT_EMPTY = 100306,
		ERROR_TAC_ROOM_ID_HERO_NO_EXIST = 100307,
		ERROR_TAC_ROOM_ID_MAX_LEVEL = 100308,
		ERROR_TAC_ROOM_ID_BATTLEGROUND_NOT_EMPTY = 100309,
		ERROR_TAC_ROOM_ID_HERO_HAS_BEEN_LOCKED = 100310,
        ERROR_TAC_ROOM_ID_MULING_NO_POS = 100311,
        ERROR_TAC_ROOM_ID_PLAYER_EQUIPMENTS_FULL = 100312,
        ERROR_TAC_ROOM_ID_NOT_ENOUGH_LIFE = 100313,
        ERROR_TAC_ROOM_ID_HERO_COST_LIFE_CAN_NOT_SELL = 100314,
        ERROR_TAC_ROOM_ID_CANT_BUT_EXP_THIS_TURN = 100315,
        ERROR_TAC_ROOM_ID_DRAGON_EGGS_NO_POS = 100316,
        ERROR_TAC_ROOM_ID_FIXED_HERO_DROP_NO_POS = 100317,
        ERROR_TAC_ROOM_ID_LUCKY_HERO_CAN_NOT_CLONE = 100318,
		ERROR_TAC_ROOM_ID_NOT_TORMENTED_HERO = 100319,
		ERROR_TAC_ROOM_ID_NOT_TORMENTED_TURNSTATE = 100320,
		ERROR_TAC_ROOM_ID_NOT_LIMITED_HERO = 100321,
		CLIENT_ERR_CODE_BATTLE_MOVE_WAIT_HERO_INVALID_POSITION = 100322,
		ERROR_TAC_ROOM_ID_NO_REFRESH_CNT = 100323,
		ERROR_TAC_ROOM_ID_NO_ENOUGHT_LIFE = 100324,
		ERROR_TAC_ROOM_ID_HERO_UN_LOCK = 100325,
		ERROR_TAC_ROOM_ID_CAN_NOT_BUY_MORE_HEAVEN = 100326,
	}

    public enum SO_ERROR_CODE_DUALMODE
    {
        ERROR_TAC_DUALMODE_ID_NOFIND_PARTNER = 100601,
        ERROR_TAC_DUALMODE_ID_NOLIFE_PARTNER = 100602,
        ERROR_TAC_DUALMODE_ID_NOPOS_PARTNER = 100603,
        ERROR_TAC_DUALMODE_ID_QUALITYLIMIT_PARTNER = 100604,
    }

    public enum SO_BATTLE_ERR_CODE
    {
        CLIENT_BATTLE_ERR_HERO_MORE_CHANGE_HERO = 100501,
        CLIENT_BATTLE_ERR_HERO_MORE_BATTLE_MOVE_HERO = 100502,
        CLIENT_BATTLE_ERR_HERO_MORE_UP_HERO = 100503,
        CLIENT_BATTLE_ERR_HERO_MORE_DOWN_HERO = 100504,
        CLIENT_BATTLE_ERR_BATTLE_UP_HERO = 100505,
        CLIENT_BATTLE_ERR_DRAG_TIME_UP_HERO = 100506,
        CLIENT_BATTLE_ERR_HERO_MORE_UP_HERO_COLOSUS = 100507,
    }

	public enum SO_BATTLE_TIP_CODE
	{
		CLIENT_BATTLE_TIP_CODE_DUAL_NIKO = 100701,
		CLIENT_BATTLE_TIP_CODE_GET_NIKKO_TIP = 100702,
		CLIENT_BATTLE_TIP_CODE_NIKKO_HERO_TIP = 100703,
		CLIENT_BATTLE_PING_TIP_CODE_NORMAL_TEXT = 102001,

	}

	public enum SO_CARD_COLLECT_TIP_CODE
    {
        CLIENT_CARD_COLLECT_TIP_CODE_CANT_CRAFT = 101000,
		CLIENT_CARD_COLLECT_TIP_CODE_CANT_DISMANTLE = 101001,
		CLIENT_CARD_COLLECT_TIP_CODE_UPGRADE_TIPS = 101002,
		CLIENT_CARD_COLLECT_TIP_CODE_UPGRADE_UNLOCK = 101003,
		CLIENT_CARD_COLLECT_TIP_CODE_DISMANTLE_TIPS = 101004,
		CLIENT_CARD_COLLECT_TIP_CODE_CRAFT_TIPS = 101005,
		CLIENT_CARD_COLLECT_TIP_CODE_CRAFT_FULL = 101006,
		CLIENT_CARD_COLLECT_TIP_CODE_USING = 101007,
		CLIENT_CARD_COLLECT_TIP_CODE_UNUSING = 101008,
		CLIENT_CARD_COLLECT_TIP_CODE_3STAR_UNLOCK = 101009,
		CLIENT_CARD_COLLECT_TIP_CODE_3STAR_GET = 101010,
		CLIENT_CARD_COLLECT_TIP_CODE_BATCH_UPGRADE_FINISH = 101011,
        CLIENT_CARD_COLLECT_TIP_CODE_BATCH_DISMANTLE_FINISH = 101012,
		CLIENT_CARD_COLLECT_TIP_CODE_BATCH_UPGRADE_TIPS = 101013,
		CLIENT_CARD_COLLECT_TIP_CODE_BATCH_DISMANTLE_TIPS = 101014,
	}

    public enum TAC_HERO_VALUE_CHANGE_TYPE
	{
		TAC_HERO_VALUE_CHANGE_COIN = 0,
		TAC_HERO_VALUE_CHANGE_EXP = 1,
	}
	
	public enum TAC_COIN_CHANGE_REASON_TYPE
	{
		TAC_COIN_CHANGE_ADD_BASICES = 1,
		TAC_COIN_CHANGE_ADD_INTEREST = 2,
		TAC_COIN_CHANGE_ADD_CONWIN = 3,
		TAC_COIN_CHANGE_ADD_CONLOSE = 4,
		TAC_COIN_CHANGE_ADD_BOUNTY = 5,
		TAC_COIN_CHANGE_ADD_SELL_HERO = 6,
		TAC_COIN_CHANGE_SUB_REFRESH_HERO = 7,
		TAC_COIN_CHANGE_ADD_RANK = 8,
	}
	
	public enum TAC_EXP_CHANGE_REASON_TYPE
	{
		TAC_EXP_CHANGE_ADD_BASICES = 1,
		TAC_EXP_CHANGE_ADD_BUYEXP = 2,
	}
	
	public enum TAC_AUTO_UP_STATUS
	{
		TAC_AUTO_UP_STATUS_UNLOCK = 0,
		TAC_AUTO_UP_STATUS_LOCK = 1,
	}
	
	public enum TAC_AUTO_WAIT_PROMOTION_STATUS
	{
		TAC_AUTO_WAIT_PROMOTION_STATUS_UNLOCK = 0,
		TAC_AUTO_WAIT_PROMOTION_STATUS_LOCK = 1,
	}
	
	public enum TAC_BROADCAST_TYPE
	{
		TAC_BROADCAST_TYPE_DEFAULT = 0,
		TAC_BROADCAST_TYPE_SPE_EFFECTS = 1,
		TAC_BROADCAST_TYPE_SNIP_CON_WIN = 2,
	}
	
	public enum TAC_GAME_QUEST_TURN
	{
		TAC_GAME_QUEST_TURN_ENEMY_OFFSET = 10000,
	}
}