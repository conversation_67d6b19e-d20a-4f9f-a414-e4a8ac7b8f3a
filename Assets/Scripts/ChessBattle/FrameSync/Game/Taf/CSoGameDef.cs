using ZGameChess;
using ZGameClient;

public enum BattlePlayerType
{
    BATTLE_TRUE_PLAYER = 0,
    BATTLE_MIRROR_PLAYER,
    BATTLE_MONSTER,
}

//移到 game fw
// public enum ESetID
// {
//     none = 0,
//     e_Set1 = 1,
//     e_Set2 = 2,
//     e_Set3 = 3,
//     e_Set4 = 4,
//     e_Set5 = 5,
//     e_Set6 = 6,
//     e_Set7 = 7,
//     e_SetPlus = 998,
//     e_SetBoss = 999
// }

//购买经验相关配置
public struct BuyExpConf
{
    public bool BuyExpOpen;   //是否可以购买经验
    public int BuyExpPrice;  //花费金币
    public int BuyExpAddExp; //获得多少经验
}

//匹配规则配置
public struct MatchConf
{
    public int MatchID;
    public int CellReduce;
}

//升星所需要英雄个数
public struct HeroUpConf
{
    public int HeroCount;
}

//装备相关配置
public struct EquipConf
{
    public int EquipmentPlanId;   //装备方案
    public int UnLoadEquipPlanId; //卸装备采用的方案（1：不分解 2：成品装备分解为散件）
    public int EquipmentStorePlanId_S5;   //装备方案
    public bool IsNeedSplit() { return UnLoadEquipPlanId == 2; }
    public bool IsHasEquipmentStoreS5() { return EquipmentStorePlanId_S5  > 0; }
}

/// <summary>
/// 海克斯强化配置相关
/// </summary>
public struct HAConf
{
    public int HAPlanID;
    public int HAHeroPlanID;
    public bool IsHasHAStore()
    {
        return HAPlanID > 0;
    }
    
    public bool IsHasHAHeroStore()
    {
        return HAHeroPlanID > 0;
    }
}
/// <summary>
/// 英雄包配置相关
/// </summary>
public struct HeroPackConf
{
    public int PKGPlanID;
    public bool IsHasHeroPackStore()
    {
        return PKGPlanID > 0;
    }
}

//胜负结算方案
public struct GameOverConf
{
    public int GameOverPlanId;  //结算方案 1：经典场方式   2：双人玩法
}

//天选BUFF相关配置
public struct HeavenSelectBuffConf
{
    public bool HeavenSelectBuffFlag; //是否开启天选BUFF
    public int HeavenSelectBuffPlanId;//开选BUFF对应的方案
}

public struct HeavenChooserConf
{
    public int LevelPlanId;
    public int RefreshPlanId;
    public int CfgPlanId;
    public int ArrPlanId;

    public bool IsOpen()
    {
        return LevelPlanId > 0;
    }

    public HeavenChooserConf(string param)
    {
        LevelPlanId = -1;
        RefreshPlanId = -1;
        CfgPlanId = -1;
        ArrPlanId = -1;
        if (!string.IsNullOrEmpty(param))
            ParasString(param);

        DebugMode();
    }

    public void ParasString(string param)
    {
        var cfgs = param.BeginSplit('|');
        if (cfgs != null && cfgs.Length == 4)
        {
            LevelPlanId = cfgs.ParseInt32(0);
            RefreshPlanId = cfgs.ParseInt32(1);
            CfgPlanId = cfgs.ParseInt32(2);
            ArrPlanId = cfgs.ParseInt32(3);
        }
        cfgs.EndSplit();
    }

    public void DebugMode()
    {
#if !JK_RELEASE
        if (MicroMgr.Instance.GetMicroObj().CSoGame.SceneId == (int)NoNetworkSceneID.TRAIN_BATTLE_MODE)
            ParasString("1|1|1|1");
#endif
    }

}

//幸运儿刷新方案配置
public struct LuckyHeroRefreshConf
{
    public int HeroKuId;
    public int HeroNumLimit;
}

public struct TSetConf
{
    public int SetID;
    public int HeroPoolId;
    public bool ElementFlag;
    public int DamageRuleType;
    public int MonsterQuestType;
    public int QuestType;
    public int LevelPlanID;
    public int ShareDraftPlanID;
    public int InitLife;
    public int InitDrop;             //初始掉落方案
    public int RankDropPlanID;       //排名送装备球方案
    public int KillDropPlanID;       //死亡掉落方案
    public int HolyDropPlanID;       //神圣祝福法球掉落
    public bool ThreeStarReward;      //是否开启三星掉落
    public bool SoulEquipFlag;
    public bool AutoRefreshFlag;      //每回合是否自动刷新
    public int GalaxyModeID;
    public int DropPlanId;            //掉落方案
    public bool MirrorDeduct;         //是否开启镜像扣血
    public int AiUseSet;
    public int LootType;
    public int GetGoldPlanID;// = setConfig.iGetGold;
    public int GetExpPlanID;// = setConfig.iGetExp;
    public int GetCtrlPlanID;
    public BuyExpConf BuyExpConfData;
    public MatchConf MatchConfData;
    public HeroUpConf HeroUpConfData;
    public string HeroSellPrice;
    public EquipConf EquipConfData;
    public HAConf HAConfData;
    public HeroPackConf HeroPackConfData;
    public GameOverConf GameOverConfData;
    public int LimitedHeroRule;
    
    public HeavenSelectBuffConf HeavenSelectBuffConfData;
    public HeavenChooserConf HeavenChooserConfData;
    public int ExtraRoomCfgPlanID; //特殊模式特殊方案id
    public LuckyHeroRefreshConf LuckyHeroRefreshConfData;
    public int RefreshMoney;
    public string FakeHeroRule; // 假人规则

    public PlayModeType PlayMode; // 具体玩法判断
    public PlayModeData PlayMode_Data; // 具体玩法判断

    public string sBadLuckProtectConf;

    public bool IsOpenInitDrop() { return InitDrop != 0; } //是否开启初始掉落
    public bool IsOpenRankDrop() { return RankDropPlanID != 0; }
    public bool IsOpenKillDrop() { return KillDropPlanID != 0; }

    public string VipHeroConf;

    public int StagePlusPlan;

    public int GetFakeHeroCfgID()
    {
        switch (SetID)
        {
            case 6: return 11996;
            case 7: return 11896;
            case 8: return 18028;
            case 4: return 11166;
        }

        return -1;
    }
}

public struct TRoundConf
{
    public int RoundType; //关卡类型 done
    public int SharedDraftType;  //轮抽类型 done
    public int SpecialDraftOpen;  //轮抽棋子、装备、组合特殊方案开关 暂无
    public int DraftHeroNumber;  //轮抽棋子个数 done
    public int DraftHeroPlan;  //轮抽棋子方案 done
    public int DraftEquipPlan;  //轮抽装备方案 done
    public int DraftHeroEquipMatchPlan;  //轮抽棋子装备组合方案 暂无，目前matchplan无planid
    public string ShardDraftTime;  //轮抽释放时间间隔 done
    public int RoundCoin;  //回合开始获得金币数 done
    public string CountInterest;  //利息金币获得规则 done
    public string ConWinAddMoney;  //连胜金币获得规则 done
    public string ConLoseAddMoney;  //连败金币获得规则 done
    public int RoundExp;  //回合开始获得经验数 done
    public int CanBuyExp;  //是否可以购买经验 done
    public string Drop;  //回合开始获得掉落 done
    public int MatchEnemyType;  //对手匹配规则方案  暂无
    public string NPC;  //野怪配置 done
    public int ReadyTime;  //备战时间 done
    public int FightTime;  //战斗时间 done
    public int FightOverTime;  //加时模式时间 done
    public int ArriveTime; //抵达时间 done
    public int DepartureTime; //启程时间 done
    public int FightDelayTime; //战斗结束延迟 done
    public string ReplyMP;  //战斗回蓝规则 done
    public int LoseDamage;  //战斗失败固定入血 done
    public int LoseHeroDamageType;  //战斗失败棋子扣血规则
    public string LoseHeroDamage; //战斗失败棋子扣血参数
    public int LoseSpecialDamageType; //战斗失败召唤物扣血规则
    public int WinLoseCount;  //战斗是否累积连胜连败 done
    public int WinCoin;  //战斗胜利获得金币数 done
    public int LoseCoin;  //战斗失败获得金币数 done
    public string RefreshCountCoin; //免费刷新次数
    public bool AutoRefresh; //回合开始是否自动刷新

    public void SetRoundConf(TTAC_Round_Client cfg)
    {
        this.RoundType = cfg.iRoundType;
        this.SharedDraftType = cfg.iSharedDraftType;
        this.SpecialDraftOpen = cfg.iSharedDraftOpen;
        this.DraftHeroNumber = cfg.iHeroNum;
        this.DraftHeroPlan = cfg.iHeroRate;
        this.DraftEquipPlan = cfg.iEquipRate;
        this.DraftHeroEquipMatchPlan = cfg.iHeroEquipMatchRate;
        this.ShardDraftTime = cfg.sSharedDraftTime;
        this.RoundCoin = cfg.iRoundCoin;
        this.CountInterest = cfg.sCountInterest;
        this.ConWinAddMoney = cfg.sConWinAddMoney;
        this.ConLoseAddMoney = cfg.sConLoseAddMoney;
        this.RoundExp = cfg.iRoundExp;
        this.CanBuyExp = cfg.iCanBuyExp;
        this.Drop = cfg.sDrop;
        this.MatchEnemyType = cfg.iMatchEnemyType;
        this.NPC = cfg.sNPC;
        this.ReadyTime = cfg.iReadyTime;
        this.FightTime = cfg.iFightTime;
        this.FightOverTime = cfg.iFightOverTime;
        this.ReplyMP = cfg.sReplyMp;
        this.LoseDamage = cfg.iLoseDamage;
        this.LoseHeroDamageType = cfg.iLoseHeroDamageType;
        this.LoseHeroDamage = cfg.sLoseHeroDamage;
        this.LoseSpecialDamageType = cfg.iLoseSpecialDamageType;
        this.WinLoseCount = cfg.iWinLoseCount;
        this.WinCoin = cfg.iWinCoin;
        this.LoseCoin = cfg.iLoseCoin;
        this.ArriveTime = cfg.iArriveTime;
        this.DepartureTime = cfg.iDepartureTime;
        this.FightDelayTime = cfg.iFightDelayTime;
        this.RefreshCountCoin = cfg.sReFreshCountCoin;
        this.AutoRefresh = (cfg.iAutoRefresh == 1);
    }

    public void Reset()
    {
        this.RoundType = 1;
        this.SharedDraftType = -1;
        this.SpecialDraftOpen = -1;
        this.DraftHeroNumber = -1;
        this.DraftHeroPlan = -1;
        this.DraftEquipPlan = -1;
        this.DraftHeroEquipMatchPlan = -1;
        this.ShardDraftTime = "-1";
        this.RoundCoin = -1;
        this.CountInterest = "-1";
        this.ConWinAddMoney = "-1";
        this.ConLoseAddMoney = "-1";
        this.RoundExp = -1;
        this.CanBuyExp = -1;
        this.Drop = "-1";
        this.MatchEnemyType = -1;
        this.NPC = "-1";
        this.ReadyTime = -1;
        this.FightTime = -1;
        this.FightOverTime = -1;
        this.ReplyMP = "-1";
        this.LoseDamage = -1;
        this.LoseHeroDamageType = -1;
        this.LoseHeroDamage = "-1";
        this.LoseSpecialDamageType = -1;
        this.WinLoseCount = -1;
        this.WinCoin = -1;
        this.LoseCoin = -1;
        this.ArriveTime = -1;
        this.DepartureTime = -1;
        this.FightDelayTime = -1;
        this.RefreshCountCoin = "";
        this.AutoRefresh = true;
    }

}

public struct TGlobalConf
{
    public int SurplusHeroStategy;
    public int PromoteHeroEquipStrategy;
}

public class BattleSimplePlayerData
{
    //玩家ID
    public int iChairid;
    //玩家类型--真人、镜像、野怪
    public BattlePlayerType eBattlePlayerType;
    //是否是主场
    public bool isHomeCourt;
    //镜像对应的真实玩家
    public int iRealChairId;
    public BattleSimplePlayerData(int chairid, BattlePlayerType playerType, bool homeCourt)
    {
        this.iChairid = chairid;
        this.eBattlePlayerType = playerType;
        this.isHomeCourt = homeCourt;
    }
}