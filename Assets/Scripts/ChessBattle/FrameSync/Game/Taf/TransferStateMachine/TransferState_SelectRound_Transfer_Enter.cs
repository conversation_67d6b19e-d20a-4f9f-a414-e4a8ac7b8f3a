using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GameFramework.Common.FSM;

public class TransferState_SelectRound_Transfer_Enter : TransferStateBase
{
    public override void OnStateEnter(TransferStateMachine context, State<TransferStateMachine> state)
    {
        base.OnStateEnter(context, state);

        context.SoGame.chessBattleCore.Departure();
    }
} 