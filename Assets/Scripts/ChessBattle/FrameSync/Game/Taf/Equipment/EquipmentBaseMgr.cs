
using GameFramework.FRand;
using System.Collections.Generic;
using System.Linq;
using ACG.Core;
using TKFrame;
using ZGame;
using ZGameClient;
using ZGameLog;

namespace Z_PVE
{
    public enum EquipmentEffecType
    {
        HERO_PROPERTY = 0,
        POPULATION = 1,
        SEPC = 2,
        CLASS = 3,
        HERO_CLONE = 4, //占用英雄池的克隆
        RANDOM_EFFECT = 5,//回合随机装备效果
        STEAL = 6, //窃贼手套
        PERSIS_ATTR = 7, // 每回合永久增加属性
        HERO_CLONE_INDEPENDENT = 8, //英雄池里还有就占用否则不占用
        REFRESH_SAME_HERO = 9, // 刷新出上一轮商店中的相同英雄，如果数量不够则品质随机
        REFRESH_2_STAR_HERO = 10, // 按当前的品质几率刷新若干2星英雄
        CLONE_SAME_FAKE_HERO = 11, // 克隆一个目标假人， 不能移动攻击和施法， 但是可以被攻击
        LEVEL_UP_TIMEER_ITEM = 12, // 英雄升星计时器
        BACK_TIME_DEVIEC = 13, // 时空回溯装置
        OVER_LOAD_DEVOCE = 14, // 极限超载装置
        EXTRA_STEAL = 15, //特殊窃贼手套
        SHADOW_POPULATION = 16, //暗影自然之力
        HERO_CLONE_PARTER = 17, //双人妮蔻
        ZILEANS_TIME_MACHINE = 18, // 基兰时光机器
        EQUIPMENT_RECASTING = 19, // 装备重铸
        EQUIPMENT_REMOVE_MACHINE = 20, // 装备拆卸
        EQUIPMENT_SPLIT_MACHINE = 21, // 装备分解器
        LOADED_DICE_DEVICE = 22, // 灌铅骰子
        TOME_OF_EMBLEMS = 23, // 转职之书
        FAKE_HERO = 24, // 生成假人
        TORMENTED_TRANSFER = 25, //天煞变身
        LIMITED_2_STAR_HERO = 26, // 三星大作战二星可购买的限制英雄
        ROYAL_RUNE = 27, // 双人忠诚符文，复制英雄送过去
        BUFFLAYER_RECORDED = 28, //buff层数不会回合重置
        GOLD_POPULATION = 29, //和金币数量有关的人口装备
        CombatEffectEquipment = 30, //一次性装备
        TRANSFER_EQUIPMENT = 31, //可以转化的装备
        PHILOSOPHER_STONE = 32, //哲人之石 很特殊的装备，暂时不做通用效果
        GEDGET = 33, //小发明家装备，层数归0会销毁的装备
    }

    public enum EquipmentType
    {
        None = 0,
        Common = 1,           // 普通装备
        ShadowEquipment = 2,  // 暗影装备
        FarplaneEquipment = 3, //异界包裹
        ShimmerscaleEquipment = 4, //微光之鳞装备
    }

    public enum EquipmentLevelType
    {
        BaseEquipment = 1,              //基础装备
        ShovelEquipment = 2,            //铲子
        HighEquipmentNoShovel = 3,      //无铲子高级装备
        HighEquipmentWithShovel = 4,    //有铲子高级装备
        ForceofNature = 5,              //自然之力
        HighEquipmentNoSynthesis = 6,   //无需合成的高级装备
        Consumable = 10,                //消耗品
        Gedgeteens = 11, //小天才
    }

    public class EquipmentBaseMgr
    {
        public virtual int GetPlayerID()
        {
            return -1;
        }

        private CSoGame CSoGame;

        public EquipmentBaseMgr(CSoGame ptr)
        {
            CSoGame = ptr;
        }

        /// <summary>
        /// 这里装备数停用,只用作某些特殊情况查询【窃贼手套装备数】，后续取可装备的最大装备数用MicroMgr.Instance.GetMicroObj().CSoGame.loadconfig.GetPlayerDataConfig().MAX_HERO_EQUIPMENT_COUNT
        /// </summary>
        public const int MAX_HERO_EQUIPMENT_COUNT = 3;   //英雄最大可装装备数
        /// <summary>
        /// 英雄身上装备列表
        /// Dictionary<英雄EntityID, 装备列表>>
        /// </summary>
        protected LinkedHashMap<int, List<ACG_Equipment>> _heroEquipmentDic = new LinkedHashMap<int, List<ACG_Equipment>>();
        public LinkedHashMap<int, List<ACG_Equipment>> HeroEquipmentDic
        {
            get
            {
                return _heroEquipmentDic;
            }
        }

        protected LinkedHashMap<int, List<ACG_Equipment>> _assistHeroEquipmentDic = new LinkedHashMap<int, List<ACG_Equipment>>();

        public LinkedHashMap<int, List<ACG_Equipment>> AssistHeroEquipmentDic
        {
            get
            {
                return _assistHeroEquipmentDic;
            }
        }

        /// <summary>
        /// 装备实例的缓存信息;
        /// 记录原来装备的所处位置;
        /// </summary>
        protected class ACG_EquipmentCacheInfo
        {
            //为了防止转移的装备是在中间的，然后还原回去后，跑到最后了的问题;
            public int index;
            public ACG_Equipment acgEquipment;
        }

        /// 缓存被改变的装备初始化信息;
        protected LinkedHashMap<int, int> _cacheChangedEquipDic = new LinkedHashMap<int, int>();// equipEntityId, heroEntityId

        /// <summary>
        /// 海克斯科技标记英雄ID
        /// </summary>
        public List<int> HextechHeroIDs = new List<int>();

        //镜像海克斯科技标记英雄ID
        public List<int> MirroHextechHeroIDs = new List<int>();

        //持有窃贼手套的英雄， key-手套entity，value-英雄entity
        protected LinkedHashMap<int, int> _stealHeroDic = new LinkedHashMap<int, int>();

        public LinkedHashMap<int, int> StealHeroDic
        {
            get
            {
                return _stealHeroDic;
            }
        }

        protected LinkedHashMap<int, List<ACG_Equipment>> _stealEquipDic = new LinkedHashMap<int, List<ACG_Equipment>>();

        public LinkedHashMap<int, List<ACG_Equipment>> StealEquipDic
        {
            get
            {
                return _stealEquipDic;
            }
        }

        protected LinkedHashMap<int, List<ACG_Equipment>> _tmpStealEquipDic = new LinkedHashMap<int, List<ACG_Equipment>>();//临时的窃贼手套生成装备，用于战斗时的分身克隆

        /*哲人之石
        1、每次刷新商店会叠加一层层数层数，初始层数为8
        2、你在刷新商店时，会有概率消耗所有层数来获得一个持有者的复制，每层装备层数都可以增加这个概率
        3、高费用的英雄将更难获得复制
        4、装备层数在回合间会保留
        在每次哲人之石都会生成一个触发层数，为以下4个数字之和；达到触发层数时，会获得一个携带者的复制，清空层数并重新生成触发层数
        1、8（与初始层数一致）
        2、英雄费用（非稀有度，如索尔为10）
        3、费用补正：1-3费+1，4-5费+2，6+费+3
        4、生成随机整数，1-3费为-2至2，4-5费为-3至3，6+费为-4至4*/
        public LinkedHashMap<int, int> PhilosopherStoneDic = new LinkedHashMap<int, int>();//equip,hero
        public LinkedHashMap<int, int> PhilosopherStoneBuff = new LinkedHashMap<int, int>();//equip,targetBuffLayer

        #region 装备随机Buff

        public void RemoveStealRandomBuff()
        {
            foreach (var kvp in StealEquipDic)
            {
                foreach (var equipment in kvp.Value)
                {
                    RemoveRandomBuffListByEquipMentID(equipment.iEntityID);
                }
            }
        }
        
        public virtual void Reset()
        {
            _stealHeroDic.Clear();
            _heroEquipmentDic.Clear();
            _stealEquipDic.Clear();
            _tmpStealEquipDic.Clear();
            MirroHextechHeroIDs.Clear();
            HextechHeroIDs.Clear();
        }

        protected void ResetRandomBuff()
        {
#if ACGGAME_CLIENT
            SendRandomBuffMsg();
#endif
        }

        protected void SendRandomBuffMsg()
        {

        }


        protected void RemoveRandomBuffListByEquipMentID(int id)
        {

        }

        protected void RemoveRandomBuffListByEquipMentID(List<ACG_Equipment> aCG_Equipment)
        {

        }

        public int GetRandomBuffByEquipmentId(int id)
        {
            return -1;
        }

        public void AddRandomBuffEquipment(ACG_Equipment aCG_Equipment)
        {
#if ACGGAME_CLIENT
            SendRandomBuffMsg();
#endif
        }

        public void CloneRandomBuffEquipment(ACG_Equipment src_ACG_Equipment, ACG_Equipment des_ACG_Equipment)
        {
            if (src_ACG_Equipment.iEffectType != (int)EquipmentEffecType.RANDOM_EFFECT)
                return;
            SendRandomBuffMsg();
        }
        #endregion

        #region 英雄装备

        /// <summary>
        /// 获取英雄身上的装备列表
        /// </summary>
        /// <param name="heroEntityID"></param>
        /// <returns></returns>
        public List<ACG_Equipment> GetAllHeroACGEquipment(int heroEntityID)
        {
            List<ACG_Equipment> rtnList;
            if (_heroEquipmentDic.TryGetValue(heroEntityID, out rtnList))
            {
                return rtnList;
            }
            return null;
        }

        //这个方法只用于表现层初始化全部装备时展示
        public List<ACG_Equipment> GetAllHeroACGEquipmentWithStealGloves(ref List<ACG_Equipment> equips)
        {
            if (equips == null) return equips;

            if (equips.Count == 1)
            {
                if (equips[0].iEffectType == (int)EquipmentEffecType.STEAL || equips[0].iEffectType == (int)EquipmentEffecType.EXTRA_STEAL)
                {
                    if (_stealEquipDic.ContainsKey(equips[0].iEntityID))
                    {
                        List<ACG_Equipment> list = _stealEquipDic[equips[0].iEntityID];
                        if (list != null)
                        {
                            for (int i = list.Count - 1; i >= 0; i--)
                            {
                                equips.Insert(0, list[i]);
                            }
                        }
                    }
                    else if (_tmpStealEquipDic.ContainsKey(equips[0].iEntityID))
                    {
                        List<ACG_Equipment> list = _tmpStealEquipDic[equips[0].iEntityID];
                        if (list != null)
                        {
                            for (int i = list.Count - 1; i >= 0; i--)
                            {
                                equips.Insert(0, list[i]);
                            }
                        }
                    }   
                }
            }

            return equips;
        }

        public ACG_Equipment GetHeroACGEquipment(int heroEntityID, int equipmentEntityID)
        {
            List<ACG_Equipment> allEquipments = GetAllHeroACGEquipment(heroEntityID);

            if (allEquipments == null || allEquipments.Count == 0)
                return null;

            for (int i = 0; i < allEquipments.Count; i++)
            {
                if (allEquipments[i].iEntityID == equipmentEntityID)
                {
                    return allEquipments[i];
                }
            }

            return null;
        }

        public ACG_Equipment GetAssistHeroACGEquipment(int heroEntityID, int equipmentEntityID)
        {
            List<ACG_Equipment> allEquipments = GetAssistHeroEquipments(heroEntityID);

            if (allEquipments == null || allEquipments.Count == 0)
                return null;

            for (int i = 0; i < allEquipments.Count; i++)
            {
                if (allEquipments[i].iEntityID == equipmentEntityID)
                {
                    return allEquipments[i];
                }
            }

            return null;
        }

        public List<ACG_Equipment> GetAssistHeroEquipments(int heroEntityID)
        {
            List<ACG_Equipment> rtnList;
            if (_assistHeroEquipmentDic.TryGetValue(heroEntityID, out rtnList))
            {
                return rtnList;
            }
            return null;
        }

        public ACG_Equipment GetHeroStealEquipment(int heroEntityID, int equipmentEntityID, out int stealEquipIndex)
        {
            stealEquipIndex = -1;
            foreach (var item in _stealHeroDic)
            {
                if (item.Value == heroEntityID)
                {
                    if (_stealEquipDic.ContainsKey(item.Key))
                    {
                        List<ACG_Equipment> equips = _stealEquipDic[item.Key];
                        for (int i = 0; i < equips.Count; i++)
                        {
                            if (equips[i].iEntityID == equipmentEntityID)
                            {
                                stealEquipIndex = i;
                                return equips[i];
                            }
                        }
                    }
                    else if (_tmpStealEquipDic.ContainsKey(item.Key))
                    {
                        List<ACG_Equipment> equips = _tmpStealEquipDic[item.Key];
                        for (int i = 0; i < equips.Count; i++)
                        {
                            if (equips[i].iEntityID == equipmentEntityID)
                            {
                                return equips[i];
                            }
                        }
                    }
                }
            }
            return null;
        }

        public void GetHeroStealEquipment(int heroEntityID, List<ACG_Equipment> acgEquipmentList)
        {
            foreach (var item in _stealHeroDic)
            {
                if (item.Value == heroEntityID)
                {
                    if (_stealEquipDic.ContainsKey(item.Key))
                    {
                        List<ACG_Equipment> equips = _stealEquipDic[item.Key];
                        acgEquipmentList.AddRange(equips);
                        break;
                    }
                }
            }
        }

        public List<ACG_Equipment> RemoveAllHeroACGEquipment(int heroEntityID)
        {
            List<ACG_Equipment> equipments = null;
            if (_heroEquipmentDic.ContainsKey(heroEntityID))
            {
                equipments = _heroEquipmentDic[heroEntityID];
                // 移除随机BUFF 装备
                RemoveRandomBuffListByEquipMentID(equipments);
                _heroEquipmentDic.Remove(heroEntityID);
                PrintEquipmentLog(GetPlayerID(), CSoGame,
                    "英雄装备移除 heroEntityID:{0}", heroEntityID);
                NotifyCsoGameToViewMgr.NotifyAllEquipments(this, this.GetPlayerID(), heroEntityID);
            }
            return equipments;
        }

        public List<ACG_Equipment> RecastAllHeroEquipment(int heroEntityID)
        {
            List<ACG_Equipment> equipments = null;
            if (_heroEquipmentDic.ContainsKey(heroEntityID))
            {
                equipments = _heroEquipmentDic[heroEntityID];
                // 移除随机BUFF 装备
                RemoveRandomBuffListByEquipMentID(equipments);
                _heroEquipmentDic.Remove(heroEntityID);
                PrintEquipmentLog(GetPlayerID(), CSoGame,
                    "英雄装备移除 heroEntityID:{0}", heroEntityID);
                NotifyCsoGameToViewMgr.NotifyAllEquipments(this, this.GetPlayerID(), heroEntityID);
            }
            return equipments;
        }

        public void RemoveHeroACGEquipment(int heroEntityID, int equipmentID)
        {
            List<ACG_Equipment> equipmentList;
            if (_heroEquipmentDic.TryGetValue(heroEntityID, out equipmentList))
            {
                for (int i = 0; i < equipmentList.Count; i++)
                {
                    if (equipmentList[i].iEntityID == equipmentID)
                    {
                        PrintEquipmentLog(GetPlayerID(), CSoGame,
                            "英雄装备移除 heroEntityID:{0}|entityID:{1}|tableID:{2}",
                            heroEntityID,
                            equipmentList[i].iEntityID,
                            equipmentList[i].iTableID);

                        equipmentList.RemoveAt(i);
                        // 移除随机BUFF 装备
                        RemoveRandomBuffListByEquipMentID(equipmentID);
                        break;
                    }
                }
            }

            List<ACG_Equipment> tempList = new List<ACG_Equipment>();
            if (StealEquipDic.TryGetValue(equipmentID, out tempList))
            {
                foreach (ACG_Equipment aCG_Equipment in tempList)
                    RemoveRandomBuffListByEquipMentID(aCG_Equipment.iEntityID);
            }

            StealHeroDic.Remove(equipmentID);
            NotifyCsoGameToViewMgr.NotifyAllEquipments(this, this.GetPlayerID(), heroEntityID);
        }

        public void AddHeroEquipment(int heroEntityID, ACG_Equipment equipment)
        {
            if (equipment == null)
                return;

            List<ACG_Equipment> vecEquipment = null;
            if (!_heroEquipmentDic.TryGetValue(heroEntityID, out vecEquipment))
            {
                vecEquipment = new List<ACG_Equipment>();
                _heroEquipmentDic.Add(heroEntityID, vecEquipment);
            }
            vecEquipment.Add(equipment);
            // 随机Buff装备
            AddRandomBuffEquipment(equipment);
            NotifyCsoGameToViewMgr.NotifyAllEquipments(this, this.GetPlayerID(), heroEntityID);

            PrintEquipmentLog(GetPlayerID(), CSoGame,
                    "英雄装备添加 heroEntityID:{0}|entityID:{1}|tableID:{2}",
                    heroEntityID,
                    equipment.iEntityID,
                    equipment.iTableID);
        }

        /// <summary>
        /// 是否可以装上装备
        /// </summary>
        /// <param name="heroEntityID"></param>
        /// <param name="equipLevel">准备装上的装备等级</param>
        /// <returns></returns>
        public bool IsEquipAvailable(int heroEntityID, int heroConfigId, int iID, out TACG_Equipment_Client synthesisEquipment, out ACG_Equipment removeEquipment, bool checkSyn = true)
        {

            synthesisEquipment = null;
            removeEquipment = null;


            List<ACG_Equipment> acgEquipmentList = GetAllHeroACGEquipment(heroEntityID);

            TACG_Equipment_Client equip = CSoGame.CSoConfig.SearchEquipment(iID);
            if (equip != null && (equip.iEffectType == (int)EquipmentEffecType.STEAL|| equip.iEffectType == (int)EquipmentEffecType.EXTRA_STEAL)) //无其他装备才能装备窃贼手套
            {
                if (acgEquipmentList != null && acgEquipmentList.Count > 0)
                    return false;
            }

            if (acgEquipmentList != null)
            {
                if (checkSyn)
                {
                    int id = GetPlayerID();
                    var player = CSoGame.PlayerMgr.GetPlayerAt(id);
                    for (int i = 0; i < acgEquipmentList.Count; i++)
                    {
                        if (acgEquipmentList[i].iGenerateType == (int)EquipmentGenrateType.TempTransfer)
                            continue;

                        synthesisEquipment = CSoGame.CSoConfig.GetSynthesisEquipment(iID, acgEquipmentList[i].iTableID);
                        if (synthesisEquipment != null)
                        {
                            if ((synthesisEquipment.iEffectType == (int)EquipmentEffecType.STEAL || synthesisEquipment.iEffectType == (int)EquipmentEffecType.EXTRA_STEAL) 
                                && acgEquipmentList.Count > 1)
                            {
                                removeEquipment = acgEquipmentList[i];
                                return false;
                            }
                            removeEquipment = acgEquipmentList[i];
                            //可以合成
                            return true;
                        }
                    }
                }
                //装备已有窃贼手套
                for (int i = 0; i < acgEquipmentList.Count; i++)
                {
                    if (acgEquipmentList[i].iEffectType == (int)EquipmentEffecType.STEAL || acgEquipmentList[i].iEffectType == (int)EquipmentEffecType.EXTRA_STEAL)
                    {
                        return false;
                    }
                }
            }

            if (acgEquipmentList == null || acgEquipmentList.Count < CSoGame.loadConfig.GetPlayerDataConfig().MAX_HERO_EQUIPMENT_COUNT/*MAX_HERO_EQUIPMENT_COUNT*/)
            {
                //有空格子
                return true;
            }

            return false;
        }

        /// <summary>
        /// 删除装备;
        /// </summary>
        /// <param name="heroEntity"></param>
        /// <param name="equipmentEntityID"></param>
        /// <param name="synthesisIndex"></param>
        /// <returns></returns>
        //public void DoDeleteHeroEquipment(ref ACG_TCmdS2CNotifyDeleteHeroEquipment notify, TAC_HeroEntity heroEntity, 
        //    int equipmentEntityID, int synthesisIndex = -1)
        //{
        //    notify.iPlayerID = GetPlayerID();
        //    notify.iRet = 0;

        //    if (heroEntity == null)
        //    {
        //        notify.iRet = (int)SoErrorCode.ERROR_EQUIPMENT_HERO_NOT_FOUND;
        //        return;
        //    }
        //    notify.stHeroEntity = heroEntity;

        //    ACG_Equipment equipment = GetHeroACGEquipment(heroEntity.iEntityID, equipmentEntityID);

        //    if (equipment == null)
        //    {
        //        notify.iRet = (int)SoErrorCode.ERROR_EQUIPMENT_NOT_FOUND;
        //        return;
        //    }

        //    //删除装备锁附加的特质信息;
        //    OnUnEquipEquipment(heroEntity.iEntityID, equipment.iTableID);

        //    RemoveHeroACGEquipment(heroEntity.iEntityID, equipmentEntityID);
        //    notify.heroRemoveEquipment = equipment;

        //    if (synthesisIndex != -1)
        //    {
        //        CSoConfig soConfig = CSoGame.CSoConfig;
        //        TACG_Equipment_Client equipmentClient = soConfig.GetEquipmentClient(equipment.iTableID);

        //        bool needsDecompose = equipmentClient != null && soConfig.IsSynthesisEquipment(equipmentClient);

        //        if (needsDecompose)
        //        {
        //            TACG_Equipment_Client equipmentClient1 = soConfig.GetEquipmentClient(equipmentClient.iSynthesisID1);
        //            TACG_Equipment_Client equipmentClient2 = soConfig.GetEquipmentClient(equipmentClient.iSynthesisID2);

        //            if (equipmentClient1 != null && equipmentClient2 != null)
        //            {
        //                ACG_Equipment Synthesis1 = CreateTACEquipment(equipmentClient1);
        //                ACG_Equipment Synthesis2 = CreateTACEquipment(equipmentClient2);

        //                notify.heroAddEquipment = synthesisIndex == 0 ? Synthesis2 : Synthesis1;
        //                AddHeroEquipment(heroEntity.iEntityID, notify.heroAddEquipment);

        //                notify.playerAddEquipment = synthesisIndex == 0 ? Synthesis1 : Synthesis2;
        //            }
        //        }
        //    }

        //    notify.vecHeroEquipment = GetAllHeroACGEquipment(heroEntity.iEntityID);
        //    return;
        //}


        public virtual bool OnEquipEquipment(int entityId, int equipId)
        {
            return false;
        }

        public virtual bool OnUnEquipEquipment(int entityId, int equipId)
        {
            return false;
        }

        public List<ACG_Equipment> GetHeroEquipmentIDList(int entityID)
        {
            List<ACG_Equipment> ids = new List<ACG_Equipment>(4);
            List<ACG_Equipment> acgEquipmentList;
            if (_heroEquipmentDic.TryGetValue(entityID, out acgEquipmentList))
            {
                for (int i = 0, len = acgEquipmentList.Count; i < len; i++)
                {
                    if (StealEquipDic.ContainsKey(acgEquipmentList[i].iEntityID))
                    {
                        for (int j = 0; j < StealEquipDic[acgEquipmentList[i].iEntityID].Count; j++)
                        {
                            ids.Add(StealEquipDic[acgEquipmentList[i].iEntityID][j]);
                        }
                    }
                    ids.Add(acgEquipmentList[i]);
                }
            }
            return ids;
        }

        public List<int> GetHeroEquipmentIds(int entityID)
        {
            List<int> ids = new List<int>(4);
            List<ACG_Equipment> acgEquipmentList;
            if (_heroEquipmentDic.TryGetValue(entityID, out acgEquipmentList))
            {
                for (int i = 0, len = acgEquipmentList.Count; i < len; i++)
                {
                    ids.Add(acgEquipmentList[i].iTableID);
                }
            }
            return ids;
        }

        public void ResetHeroEquipments(int heroEnityID, List<ACG_Equipment> equipments)
        {
            List<ACG_Equipment> preValue = null;
            if (_heroEquipmentDic.TryGetValue(heroEnityID, out preValue))
            {
                _heroEquipmentDic[heroEnityID] = equipments;
            }
            else
            {
                _heroEquipmentDic.Add(heroEnityID, equipments);
            }
            NotifyCsoGameToViewMgr.NotifyAllEquipments(this, this.GetPlayerID(), heroEnityID);
        }

        public void DeleteAllHeroEquipment()
        {
            _heroEquipmentDic.Clear();
        }


        public void CopyAllHeroEquipmentMap(LinkedHashMap<int, List<ACG_Equipment>> from, LinkedHashMap<int, List<ACG_Equipment>> to)
        {
            if (to == null)
                to = new LinkedHashMap<int, List<ACG_Equipment>>();
            else
                to.Clear();
            for (var iter = from.Keys.GetEnumerator(); iter.MoveNext();)
            {
                List<ACG_Equipment> equipments = new List<ACG_Equipment>();
                if (from[iter.Current] != null)
                {
                    equipments.AddRange(from[iter.Current]);
                }
                to.Add(iter.Current, equipments);
            }
        }

        //获取英雄身上只能装一个装备的ID
        public List<int> GetUniqueEquipments(int heroEntityID, int exclusiveID, int targetEquipmentTableID)
        {
            List<int> ids = new List<int>();
            if (_heroEquipmentDic.ContainsKey(heroEntityID))
            {
                for (int i = 0; i < _heroEquipmentDic[heroEntityID].Count; i++)
                {
                    ACG_Equipment acgEquipment = _heroEquipmentDic[heroEntityID][i];
                    TACG_Equipment_Client equipmentClient = CSoGame.CSoConfig.GetEquipmentClient(acgEquipment.iTableID);
                    if (equipmentClient != null && equipmentClient.iUnique == 1 && acgEquipment.iEntityID != exclusiveID)
                        ids.Add(_heroEquipmentDic[heroEntityID][i].iTableID);
                    
                    // 像暗影装备，和源装备，只能装一个
                    TACG_Equipment_Client targetEquipmentClient = CSoGame.CSoConfig.GetEquipmentClient(targetEquipmentTableID);
                    if(targetEquipmentClient == null)
                        continue;
                    if (targetEquipmentClient.iUnique == 1 && acgEquipment.iEntityID != exclusiveID &&
                        equipmentClient != null &&
                        (targetEquipmentClient.iMappingID == acgEquipment.iTableID || equipmentClient.iMappingID == targetEquipmentClient.iID))
                    {
                        ids.Add(targetEquipmentTableID);
                    }
                }
            }
            return ids;
        }

        public ACG_Equipment CreateTACEquipment(TACG_Equipment_Client equipmentClient, ACG_Equipment synthesis1 = null, ACG_Equipment synthesis2 = null)
        {
            ACG_Equipment tacEquipment = new ACG_Equipment();
            tacEquipment.iTurnCount = CSoGame.CurrentTotalTurnCount;
            tacEquipment.iTableID = equipmentClient.iID;
            tacEquipment.iEntityID = GetNewDropEntityID();
            tacEquipment.iEffectType = equipmentClient.iEffectType;
            tacEquipment.iLevel = equipmentClient.iLevel;
            tacEquipment.iHasTransferCount = equipmentClient.iInitBuffsCount;

            return tacEquipment;
        }

        /// <summary>
        /// 穿戴装备，并且通知到逻辑/战斗/显示层;
        /// </summary>
        public virtual void LoadEquipmentToLogicFightView(int heroEntityId, ACG_Equipment acgEquipment, bool bNeedPlayEquipEffect/*, CEquipmentOperation operation = CEquipmentOperation.Empty*/)
        {

        }

        /// <summary>
        /// 卸载装备，并且通知到逻辑/战斗/显示层;
        /// </summary>
        public virtual void UnloadEquipmentToLogicFightView(TAC_HeroEntity heroEntity, ACG_Equipment acgEquipment)
        {

        }

        #endregion

        #region 静态接口
        /// <summary>
        /// 创建单个装备
        /// </summary>
        /// <param name="equipmentClient"></param>
        /// <param name="isLuckyDrop"></param>
        /// <returns></returns>
        public static ACG_Equipment CreateTACEquipment(TACG_Equipment_Client equipmentClient, CSoGame CSoGame, int dropItemID, ACG_Equipment synthesis1 = null, ACG_Equipment synthesis2 = null)
        {
            ACG_Equipment tacEquipment = new ACG_Equipment();
            tacEquipment.iTurnCount = CSoGame.CurrentTotalTurnCount;
            tacEquipment.iTableID = equipmentClient.iID;
            tacEquipment.iEntityID = dropItemID;
            tacEquipment.iEffectType = equipmentClient.iEffectType;
            tacEquipment.iLevel = equipmentClient.iLevel;
            tacEquipment.iHasTransferCount = equipmentClient.iInitBuffsCount;

            return tacEquipment;
        }

        public static bool IsStealType(int equipmentId, CSoGame CSoGame)
        {
            TACG_Equipment_Client equipmentClient = CSoGame.CSoConfig.GetEquipmentClient(equipmentId);
            if (equipmentClient == null || (equipmentClient.iEffectType != (int)EquipmentEffecType.STEAL && equipmentClient.iEffectType != (int)EquipmentEffecType.EXTRA_STEAL))
                return false;

            return true;
        }

        public static bool IsEquipmentExclusive(int equipmentCfgID, int heroCfgID, List<int> onlyOneIDs, CSoGame CSoGame)
        {
            if (onlyOneIDs != null && onlyOneIDs.IndexOf(equipmentCfgID) != -1)
                return true;

            TACG_Hero_Client heroClient = CSoGame.CSoConfig.GetAutoChessHeroInfoByID(heroCfgID);
            TACG_Equipment_Client equipmentClient = CSoGame.CSoConfig.GetEquipmentClient(equipmentCfgID);

            if (heroClient == null || equipmentClient == null)
                return false;

            if (equipmentClient.iEffectType == (int)EquipmentEffecType.SEPC)
            {
                int sepcID = equipmentClient.iEffectParams;

                FastStringSplit splits = heroClient.sSpec.BeginSplit('|');
                for (int i = 0; i < splits.Length; i++)
                {
                    int tableSpec = -1;
                    if (splits.TryParse(i, out tableSpec) && sepcID == tableSpec)
                    {
                        splits.EndSplit();
                        return true;
                    }
                }
                splits.EndSplit();
            }
            else if (equipmentClient.iEffectType == (int)EquipmentEffecType.CLASS)
            {
                int classID = equipmentClient.iEffectParams;

                FastStringSplit splits = heroClient.sClass.BeginSplit('|');
                for (int i = 0; i < splits.Length; i++)
                {
                    int tableClass = -1;
                    if (splits.TryParse(i, out tableClass) && classID == tableClass)
                    {
                        splits.EndSplit();
                        return true;
                    }
                }
                splits.EndSplit();
            }

            return false;
        }
        
        public static void PrintEquipmentLog(int playerID, CSoGame CSoGame, object format, params object[] paramList)
        {

        }
        #endregion

        /// <summary>
        /// 还原回合战斗中改变的装备到英雄装备列表中;
        /// </summary>
        public virtual void RestoreCacheChangedEquipInitDic()
        {

        }



        /// <summary>
        /// 检查下是否要增加到 缓存被改变的装备初始化信息
        /// </summary>
        /// <param name="heroEntityId"></param>
        /// <param name="acgEquipment"></param>
        /// <param name="equipPreIndex"></param>
        public void CheckAddToCacheChangedEquipInit(int heroEntityId, ACG_Equipment acgEquipment)
        {
            //临时生成的装备只是战斗产生的，所以不必要记录;
            if (acgEquipment.iGenerateType == (int)EquipmentGenrateType.TempTransfer)
            {
                return;
            }
            if (acgEquipment.iGenerateType == (int)EquipmentGenrateType.Stealed)
            {
                return;
            }
            if (acgEquipment.iGenerateType == (int)EquipmentGenrateType.ExtraAddHexEquip)
            {
                return;
            }

            if (acgEquipment.iEffectType == (int)EquipmentEffecType.BUFFLAYER_RECORDED || acgEquipment.iEffectType == (int)EquipmentEffecType.TRANSFER_EQUIPMENT || acgEquipment.iEffectType == (int)EquipmentEffecType.PHILOSOPHER_STONE || acgEquipment.iEffectType == (int)EquipmentEffecType.GEDGET)
            {
                return;
            }

            if (_cacheChangedEquipDic.ContainsKey(acgEquipment.iEntityID))
            {
                _cacheChangedEquipDic[acgEquipment.iEntityID] = heroEntityId;
            }
            else
            {
                _cacheChangedEquipDic.Add(acgEquipment.iEntityID, heroEntityId);
            }
        }

        public void CloneMirrorStealEquip(LinkedHashMap<int, List<ACG_Equipment>> stealEquip, LinkedHashMap<int, int> stealHero)
        {
            _stealEquipDic.Clear();
            _stealHeroDic.Clear();
            foreach (var item in stealEquip)
            {
                List<ACG_Equipment> equipments = new List<ACG_Equipment>();
                for (int i = 0; i < item.Value.Count; i++)
                {
                    ACG_Equipment clone = (ACG_Equipment)item.Value[i].DeepClone();
                    equipments.Add(clone);
                }
                _stealEquipDic.Add(item.Key, equipments);
            }

            foreach (var item in stealHero)
            {
                _stealHeroDic.Add(item. Key, item.Value);
            }
        }
        protected int iiii = 0;
        protected int GetNewDropEntityID()
        {
            return iiii++;
        }

        public void InitPhilosopherStoneTarget(int equipEntityId, int heroConfId)
        {
            TACG_Hero_Client heroClient = CSoGame.CSoConfig.GetAutoChessHeroInfoByID(heroConfId);
            if (heroClient == null) return;
            int target = 8 + heroClient.iPrice;
            if (heroClient.iPrice <= 3)
            {
                target = target + 1 + (2 - FRandUtils.RandInt(0, 5));
            }
            else if (heroClient.iPrice <= 5)
            {
                target = target + 2 + (3 - FRandUtils.RandInt(0, 7));
            }
            else
            {
                target = target + 3 + (4 - FRandUtils.RandInt(0, 9));
            }
            if (PhilosopherStoneBuff.ContainsKey(equipEntityId))
            {
                PhilosopherStoneBuff[equipEntityId] = target;
            }
            else
            {
                PhilosopherStoneBuff.Add(equipEntityId, target);
            }
        }

    }
}
