using System.Collections.Generic;
using TKFrame;
using UniqueInfo;
using ZGameClient;
namespace Z_PVE
{
    public class TACHeroCacheMap : ConfigCacheBaseMap<TACG_Hero_Client>
    {


        public TACHeroCacheMap(CSoConfig CSoConfig) : base(CSoConfig)
        {

        }

        // 小写和STL容器保持一致
        public void clear()
        {
            _exMap.Clear();
        }

        public void AddExtraHero(TACG_Hero_Client exHero)
        {
            if (_exMap.ContainsKey(exHero.iID) == false)
                _exMap.Add(exHero.iID, exHero);
            else
                _exMap[exHero.iID] = exHero;
        }

        public override ConfigHashMap<int, TACG_Hero_Client> GetSrcMap()
        {
            return m_CSoConfig.stAllInfo.mapACG_Hero_Client;
        }

        public TACG_Hero_Client GetByGroupStar(int iGroup, int star)
        {
            TACG_Hero_Client heroClient = null;

            int curId = iGroup;
            for (int i = 0; i < 4; ++i)
            {
                heroClient = GetByID(curId);
                if (heroClient != null)
                {
                    if (heroClient.iStar == star)
                        return heroClient;
                    else
                        curId = heroClient.iNextHeroID;
                }
            }

            return null;
        }

        public TACG_Hero_Client GetLuxBySpec(string spec)
        {
            foreach (var item in _exMap)
            {
                if (item.Value.IsLux() && item.Value.sSpec == spec && item.Value.iStar == 1)
                {
                    return item.Value;
                }
            }

            var srcMap = GetSrcMap();
            foreach (var item in srcMap)
            {
                if (item.Value.IsLux() && item.Value.sSpec == spec && item.Value.iStar == 1)
                {
                    return item.Value;
                }
            }

            return null;
        }

        public List<int> GetAllLevel1Lux()
        {
            List<int> luxList = new List<int>();

            foreach (var item in _exMap)
            {
                if (item.Value.IsLux() && item.Value.iStar == 1)
                {
                    luxList.Add(item.Value.iID);
                }
            }

            var srcMap = GetSrcMap();
            foreach (var item in srcMap)
            {
                if (item.Value.IsLux() && item.Value.iStar == 1)
                {
                    if (!luxList.Contains(item.Value.iID))
                        luxList.Add(item.Value.iID);
                }
            }

            return luxList;
        }



        protected override int Compare(TACG_Hero_Client x, TACG_Hero_Client y)
        {
            if (x.iID < y.iID)
                return -1;
            return 1;
        }
    }
}

