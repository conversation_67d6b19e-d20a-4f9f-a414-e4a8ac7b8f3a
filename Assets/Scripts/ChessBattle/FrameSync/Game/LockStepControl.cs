namespace GloryLockStep
{
#if ACGGAME_CLIENT
    using GameFramework.Common.FSM;
    using GameFramework;
    using GameFramework.Unity;
    using GameFramework.Common.FSM.TransitConditions;
    using GCloud.LockStep;
    using UnityEngine;
    using States;
#endif
    using System.Collections;
    using System.Collections.Generic;

    /// <summary>
    /// 游戏帧同步的主控类,负责整个游戏应用层的调度.
    /// </summary>
    public class LockStepControl
#if ACGGAME_CLIENT
        : UnityGameStateGraphProvider
    {
        public const string ENTER_ROOM = "enter_room";

        public const string EXIT_ROOM = "EXIT_ROOM";
        // public const string EXIT_ROOM = "exit_room";

        public static LockStepControl inst
        {
            get { return msInst; }
        }

        static LockStepControl msInst;

        public LockStepControl()
        {
            msInst = this;
        }

        public override StateGraph<Game> GetStateGraph()
        {
            /*
             *    ┌─▶ GameState_Idle
             *    │     │
             *    │     │进入房间开始战斗
             *    │     │
             *    │     ▼
             *    │   GameState_Room
             *    │     │
             *    │     │         
             *    │     ▼        
             *    └────战斗完毕
             *
             */

            StateGraph<Game> graph = new StateGraphAL<Game>();
#if ACGGAME_CLIENT
            State<Game> idle = graph.AddState("Idle", new GameState_Idle());
            idle.SetAsDefaultState();

            State<Game> room = graph.AddState("Room", new GameState_Room());

            // world->room
            Transition<Game> t_room = graph.AddTransition(idle, room,
                new CheckTrigger<Game>(ENTER_ROOM));

            Transition<Game> t_Idle = graph.AddTransition(room, idle,
                new CheckTrigger<Game>(EXIT_ROOM));

#endif

            return graph;
        }

        private void OnDestroy()
        {
            Game.singleton.Stop(true);
        }

        //初始化帧同步
        public static void Init(GameObject DontDestroyOnLoadObj = null)
        {
            if (DontDestroyOnLoadObj == null)
                DontDestroyOnLoadObj = GameObject.Find("DontDestroyOnLoad");

            if (DontDestroyOnLoadObj == null)
                DontDestroyOnLoadObj = new GameObject("DontDestroyOnLoad");

            GameObject.DontDestroyOnLoad(DontDestroyOnLoadObj);

            //帧同步
            GameObject syncFrameGO = new GameObject();
            syncFrameGO.name = "GameFrameSync";
            syncFrameGO.transform.parent = DontDestroyOnLoadObj.transform;
            syncFrameGO.AddComponent<UnityGameAdapter>();
            syncFrameGO.AddComponent<LockStepControl>();

            //Driver
//             GameObject driverGO = new GameObject();
//             driverGO.name = "Driver";
//             driverGO.transform.parent = DontDestroyOnLoadObj.transform;

            //driverGO.AddComponent<ACG.Core.DriverManager>();

            // 协程代理工具
            CoroutineDelegate.Init(DontDestroyOnLoadObj.transform);
            ACG.Core.Core.Setup();
        }

        public static void ExitRoom(bool isExcuteNow = false)
        {
            if (Game.singleton.currentState.name == "Room")
            {
                Game.singleton.SetTrigger(LockStepControl.EXIT_ROOM, isExcuteNow);
            }
            else
            {
                Game.singleton.ResetTrigger(LockStepControl.ENTER_ROOM);
                Game.singleton.ResetTrigger(LockStepControl.EXIT_ROOM);
            }
        }

        public static IEnumerator InitConfig(MicroObject World)
        {
            yield return ZGameChess.GlobalConfig.Instance.Load();
            //ACG.Core.DriverManager.Create(World);
        }
#else
    {
#endif

        /// <summary>
        /// 初始化场景入口;
        /// </summary>
        /// <param name="World"></param>
        /// <returns></returns>
        public static void InitSceneInitLogic(MicroObject World)
        {
            var sceneInit = SceneInitLogic.Create(World);

            sceneInit.ExecuteStart();
        }

        /// <summary>
        /// 初始化网络帧同步模式;
        /// </summary>
        /// <param name="World"></param>
        /// <returns></returns>
        public static void InitNetFrameSyncMode(MicroObject World)
        {
            NetFrameSyncManager.Init(World);
        }

        public static void InitNetFrame(MicroObject World)
        {
            World.NetFrameSyncMgr.NetMode();

            World.LogicIniter.InitComplete();
        }
    }
}