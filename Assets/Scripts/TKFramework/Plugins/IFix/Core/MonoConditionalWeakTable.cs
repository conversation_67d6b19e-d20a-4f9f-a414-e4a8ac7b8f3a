using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Runtime.CompilerServices;

namespace IFix.Core
{
    public class MonoConditionalWeakTable<TKey, TValue> : IEnumerable<KeyValuePair<TKey, TValue>>
        where TKey : class
        where TValue : class
    {
        private struct Entry
        {
            public int hashCode;

            public int next;

            // -1 表示直接接到桶上 -2 表示空
            public int last;
            public WeakReference key;
            public TValue value;
        }

        private Entry[] entries;
        private int[] buckets;
        private object m_locker = new object();
        private int count;
        private int freeList;
        private int freeCount;
        private int minCount;

        #region Init

        public MonoConditionalWeakTable()
        {
            Initialize(0);
        }
        
        public MonoConditionalWeakTable(int size)
        {
            Initialize(size);
        }

        private void Initialize(int capacity)
        {
            lock (m_locker)
            {
                minCount = capacity <= 2 ? 2 : capacity;
                int prime = HashHelpers.GetPrime(capacity);
                buckets = new int[prime];
                for (int index = 0; index < buckets.Length; ++index)
                    buckets[index] = -1;
                entries = new Entry[prime];
                freeList = -1;
            }
        }

        #endregion
        
        #region Attach GC Detector

        public void Attach(GCDetector gcDetector)
        {
            gcDetector.AddCallback(CollectAndRemoveAllData);
        }

        public void Detach(GCDetector gcDetector)
        {
            gcDetector.RemoveCallback(CollectAndRemoveAllData);
        }
        
        #endregion

        #region Search

        public bool TryGetValue(TKey key, out TValue value)
        {
            int entry = FindEntry(key, out value);
            if (entry >= 0)
            {
                return true;
            }
            
            return false;
        }

        private int FindEntry(TKey key, out TValue value)
        {
            int num = GetHashCodeInternal(key);
            lock (m_locker)
            {
                for (int index = buckets[num % buckets.Length]; index >= 0; index = entries[index].next)
                {
                    if (entries[index].hashCode == num
                        && isAlive(index)
                        && EqualsInternal(entries[index].key, key))
                    {
                        value = entries[index].value;
                        return index;
                    }
                }
            }

            value = default(TValue);
            return -1;
        }

        #endregion

        #region Add

        public void Add(TKey key, TValue value)
        {
            TryInsert(key, value, InsertionBehavior.ThrowOnExisting);
        }

        private bool TryInsert(TKey key, TValue value, InsertionBehavior behavior)
        {
            int num1 = GetHashCodeInternal(key);
            lock (m_locker)
            {
                int currentBucketIndex = num1 % buckets.Length;
                for (int index2 = buckets[currentBucketIndex]; index2 >= 0; index2 = entries[index2].next)
                {
                    if (entries[index2].hashCode == num1
                        && isAlive(index2)
                        && EqualsInternal(entries[index2].key, key))
                    {
                        if (behavior == InsertionBehavior.OverwriteExisting)
                        {
                            entries[index2].value = value;
                            return true;
                        }

                        if (behavior == InsertionBehavior.ThrowOnExisting)
                            throw new ArgumentException(
                                string.Format(CultureInfo.InvariantCulture, "An item with the same key has already been added. Key: {0}", key));
                        return false;
                    }
                }

                int currentIndex;
                if (freeCount > 0)
                {
                    currentIndex = freeList;
                    freeList = entries[currentIndex].next;
                    --freeCount;
                }
                else
                {
                    if (count == entries.Length)
                    {
                        Resize();
                        currentBucketIndex = num1 % buckets.Length;
                    }

                    currentIndex = count;
                    ++count;
                }

                int nextIndex = buckets[currentBucketIndex];
                
                entries[currentIndex].hashCode = num1;
                entries[currentIndex].next = nextIndex;
                entries[currentIndex].last = -1;
                entries[currentIndex].key = new WeakReference(key);
                entries[currentIndex].value = value;

                buckets[currentBucketIndex] = currentIndex;

                if (nextIndex != -1)
                {
                    entries[nextIndex].last = currentIndex;
                }
            }

            return true;
        }

        #endregion

        #region Remove

        public bool Remove(TKey key)
        {
            bool result = RemoveInternal(key);
            return result;
        }

        private bool RemoveInternal(TKey key)
        {
            int num = GetHashCodeInternal(key);
            lock (m_locker)
            {
                int currentBucketIndex = num % buckets.Length;
                for (int currentIndex = buckets[currentBucketIndex];
                     currentIndex >= 0;
                     currentIndex = entries[currentIndex].next)
                {
                    if (entries[currentIndex].hashCode == num
                        && isAlive(currentIndex)
                        && EqualsInternal(entries[currentIndex].key, key))
                    {
                        RemoveIndex(currentIndex);
                        return true;
                    }
                }
            }

            return false;
        }

        private void RemoveIndex(int currentIndex)
        {
            int nextIndex = entries[currentIndex].next;
            int lastIndex = entries[currentIndex].last;

            if (nextIndex != -1)
                entries[nextIndex].last = lastIndex;

            if (lastIndex == -1)
            {
                int hashCode = entries[currentIndex].hashCode;
                int currentBucketIndex = hashCode % buckets.Length;
                buckets[currentBucketIndex] = nextIndex;
            }
            else
                entries[lastIndex].next = nextIndex;

            entries[currentIndex].hashCode = -1;
            entries[currentIndex].next = freeList;
            entries[currentIndex].last = -2;
            entries[currentIndex].key = null;
            entries[currentIndex].value = default(TValue);
            freeList = currentIndex;
            ++freeCount;
        }

        #endregion

        #region Resize

        private void Resize()
        {
            while (minCount * 2 < count)
            {
                minCount *= 2;
            }

            Resize(HashHelpers.ExpandPrime(minCount));
        }

        private void Resize(int newSize)
        {
            int[] numArray = new int[newSize];
            for (int index = 0; index < numArray.Length; ++index)
                numArray[index] = -1;
            Entry[] entryArray = new Entry[newSize];
            Array.Copy(entries, 0, entryArray, 0, count);

            for (int index1 = 0; index1 < count; ++index1)
            {
                if (entryArray[index1].hashCode >= 0)
                {
                    if (entryArray[index1].key.IsAlive)
                    {
                        int index2 = entryArray[index1].hashCode % newSize;
                        int nextIndex = numArray[index2];

                        entryArray[index1].next = nextIndex;
                        entryArray[index1].last = -1;

                        if (nextIndex != -1)
                        {
                            entryArray[nextIndex].last = index1;
                        }

                        numArray[index2] = index1;
                    }
                    else
                    {
                        entryArray[index1].hashCode = -1;
                        entryArray[index1].next = freeList;
                        entryArray[index1].last = -2;
                        entryArray[index1].key = null;
                        entryArray[index1].value = default(TValue);
                        freeList = index1;
                        ++freeCount;
                    }
                }
            }

            buckets = numArray;
            entries = entryArray;
        }

        #endregion

        #region Collect

        private bool isAlive(int index)
        {
            bool isAlive = entries[index].key.IsAlive;
            return isAlive;
        }

        public void CollectAndRemoveAllData()
        {
            lock (m_locker)
            {
                for (int index = 0; index < count; index++)
                {
                    if (entries[index].hashCode != -1 && !entries[index].key.IsAlive)
                    {
                        RemoveIndex(index);
                    }
                }
            }
        }

        #endregion

        #region HashCode & Equals

        private int GetHashCodeInternal(TKey key)
        {
            int hashCode = 0;
            hashCode = RuntimeHelpers.GetHashCode(key);
            return hashCode & 0x7ffffff;
        }

        private bool EqualsInternal(WeakReference weakReference, TKey key)
        {
            TKey obj = (TKey)weakReference.Target;
            if (obj != null)
            {
                return ReferenceEquals(key, obj);
            }
            return false;
        }

        #endregion
        
        #region IEnumerator

        public IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator()
        {
            return new Enumerator(this);
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public struct Enumerator : IEnumerator<KeyValuePair<TKey, TValue>>
        {
            private MonoConditionalWeakTable<TKey, TValue> data;
            private int index;
            private KeyValuePair<TKey, TValue> current;

            public Enumerator(MonoConditionalWeakTable<TKey, TValue> data)
            {
                this.data = data;
                index = 0;
                current = new KeyValuePair<TKey, TValue>();
            }

            public bool MoveNext()
            {
                while (index < data.count)
                {
                    if (data.entries[index].hashCode >= 0 && data.entries[index].key.IsAlive)
                    {
                        TKey key = (TKey) data.entries[index].key.Target;
                        if (key != null)
                        {
                            current = new KeyValuePair<TKey, TValue>(key, data.entries[index].value);
                        }

                        index++;
                        return true;
                    }

                    index++;
                }

                index = data.count + 1;
                current = new KeyValuePair<TKey, TValue>();
                return false;
            }

            public void Reset()
            {
                index = 0;
                current = new KeyValuePair<TKey, TValue>();
            }

            public KeyValuePair<TKey, TValue> Current
            {
                get { return current; }
            }

            object IEnumerator.Current
            {
                get { return Current; }
            }

            public void Dispose()
            {
            }
        }
        
        #endregion
    }
}