using System;
using TKFrame;
using UnityEngine;

public class GCDetector : MonoBehaviour
{
    public class GCTest
    {
        ~GCTest()
        {
            Diagnostic.Log("GC happened");
            isGC = true;
        }
    }
    
    private static bool isGC = false;

    public static GCDetector instance = null;

    private GCTest m_gcTest = new GCTest();

    private Action m_callback;

    private void Awake()
    {
        instance = this;
        m_gcTest = null;
    }

    public void AddCallback(Action callback)
    {
        lock (this)
        {
            m_callback += callback;
        }
    }

    public void RemoveCallback(Action callback)
    {
        lock (this)
        {
            m_callback -= callback;
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (isGC)
        {
            lock (this)
            {
                if (m_callback != null)
                    m_callback();
            }
            
            m_gcTest = new GCTest();
            m_gcTest = null;
            isGC = false;
        }
    }
}
