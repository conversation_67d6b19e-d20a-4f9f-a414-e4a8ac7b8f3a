Shader "Custom/GPUDeltaShader"
{
    SubShader
    {
        Name "GPUDelta"
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            sampler2D_float _TextureA;
            sampler2D_float _TextureB;
            float4 _RefTextureParams;
            float4 _RenderTextureParams;
            float4 _ColorMasks;
            struct VertexInput
            {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
            };
            struct VertexOutput
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
            };
            VertexOutput vert (VertexInput v)
            {
                VertexOutput o;
                float2 viewportSize = _RefTextureParams.xy * _RenderTextureParams.zw;
                v.vertex.xy = viewportSize * v.vertex.xy;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = v.texcoord;
                return o;
            }
            float frag (VertexOutput i) : SV_Target
            {
                float4 colA = tex2D(_TextureA, i.uv);
                float4 colB = tex2D(_TextureB, i.uv);
                float4 delta = colA - colB;
                return dot(delta * delta, _ColorMasks);
            }
            ENDCG
        }
    }
}
