using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// <br>Using a custom property drawer <see cref="ReadOnlyDrawer"/> for this empty class.</br>
/// </summary>
public class ReadOnlyAttribute : PropertyAttribute
{
    // do nothing.
    // Create this attribute and using a custom property drawer to 
    // draw readonly effect.
}

//#if UNITY_EDITOR
//[CustomPropertyDrawer(typeof(ReadOnlyAttribute))]
//public class ReadOnlyDrawer : PropertyDrawer
//{
//    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
//    {
//        return EditorGUI.GetPropertyHeight(property, label, true);
//    }

//    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
//    {
//        base.OnGUI(position, property, label);
//        GUI.enabled = false;
//        EditorGUI.PropertyField(position, property, label, true);
//        GUI.enabled = true;
//    }
//}
//#endif