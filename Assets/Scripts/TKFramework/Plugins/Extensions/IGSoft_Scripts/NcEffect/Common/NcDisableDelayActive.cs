#if TKF_ALL_EXTEND || TKFE_NCEFFECT//TKFrame Auto Gen
// ----------------------------------------------------------------------------------
//
// FXMaker
// Created by ismoon - 2012 - <EMAIL>
//
// ----------------------------------------------------------------------------------

using UnityE<PERSON><PERSON>;

using System.Collections;
using System.Collections.Generic;

public class NcDisableDelayActive : MonoBehaviour
{
/*
	// Attribute ------------------------------------------------------------------------
	// Property -------------------------------------------------------------------------
	public static void HideNcDelayActive(GameObject tarObj)
	{
		SetActiveRecursively(tarObj, false);
		NcDelayActive[]	coms = tarObj.GetComponentsInChildren<NcDelayActive>(true);
		foreach (NcDelayActive com in coms)
			com.CancelDelayActive();
	}
	// Loop Function --------------------------------------------------------------------
	void Awake()
	{
		HideNcDelayActive(gameObject);
	}

	// Control Function -----------------------------------------------------------------
	// Event Function -------------------------------------------------------------------
*/
}


#endif //TKFrame Auto Gen
