using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class NcUvDissolveAnimation : MonoBehaviour
{
    private NcCurveAnimation m_curveAnim;
    private Renderer m_renderer; 
    // Start is called before the first frame update
    void Awake()
    {
        m_curveAnim = GetComponent<NcCurveAnimation>();
        m_renderer = GetComponent<Renderer>();

    }

    internal void SetDissolveValue(float fValue)
    {
        if (m_renderer != null)
        {
            m_renderer.material.SetFloat("_Dissolve", fValue);
        }
    }
}
