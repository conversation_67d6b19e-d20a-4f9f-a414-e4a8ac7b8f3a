using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class NcUvTextureUVAnimation : MonoBehaviour
{
    private NcCurveAnimation m_curveAnim;
    private Renderer m_renderer;
    // Start is called before the first frame update
    void Awake()
    {
        m_curveAnim = GetComponent<NcCurveAnimation>();
        m_renderer = GetComponent<Renderer>();

    }

    internal void SetUVXValue(float xValue)
    {
        if (m_renderer != null)
        {
            m_renderer.material.SetFloat("_SpeedX", xValue);
        }
    }

    internal void SetUVYValue(float yValue)
    {
        if (m_renderer != null)
        {
            m_renderer.material.SetFloat("_SpeedY", yValue);
        }
    }

    internal void SetUVX2Value(float xValue)
    {
        if (m_renderer != null)
        {
            m_renderer.material.SetFloat("_SpeedX2", xValue);
        }
    }

    internal void SetUVY2Value(float yValue)
    {
        if (m_renderer != null)
        {
            m_renderer.material.SetFloat("_SpeedY2", yValue);
        }
    }

}
