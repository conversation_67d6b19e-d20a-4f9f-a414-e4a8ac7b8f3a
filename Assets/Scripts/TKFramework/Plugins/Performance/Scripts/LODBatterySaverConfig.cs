using System.Collections;
using UnityEngine;

public class LODBatterySaverConfig : LODBaseConfig
{
    public LODBatterySaverConfig()
    {
        // 渲染分辨率设置
        DesignResolutionWidth = 854;  //设置特定渲染分辨率--宽
        DesignResolutionHight = 480; //设置特定渲染分辨率--高

        // 降级渲染分辨率设置
        TraverseDesignResolutionWidth = 640; //设置降级渲染分辨率--宽
        TraverseDesignResolutionHeight = 360;  //设置降级渲染分辨率--高

        // 高清设置
        HighResolution = false;

        // 帧率设置
        TargetFrameRate = 15;

        // Shader的LOD设置
        Global_ShaderLOD = 150;

        // 摄像机LOD--bloom效果和景深效果
        CameraBloom = false;
        CameraDepth = false;

        // 资源缓冲池:最大缓冲个数
        AssetCacheNumCapacity = 80;

        // 资源缓冲池:最大缓冲内存
        AssetCacheMemCapacity = 50 * 1024 * 1024;

        Tier = UnityEngine.Rendering.GraphicsTier.Tier1;

        BoneNumber = BlendWeights.FourBones;

        ShadowCustomResolution = 512;

        Overload = 30;
    }
}