using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 高画质LOD配置
/// </summary>
public class LODHighConfig : LODBaseConfig
{
    public const int HighShaderLOD = 800;

    public LODHighConfig()
    {
        // 渲染分辨率设置
        DesignResolutionWidth = 1920;  //设置特定渲染分辨率--宽
        DesignResolutionHight = 1080; //设置特定渲染分辨率--高

        // 降级渲染分辨率设置
        TraverseDesignResolutionWidth = 1280; //设置降级渲染分辨率--宽
        TraverseDesignResolutionHeight = 720;  //设置降级渲染分辨率--高

        // 高清设置
        HighResolution = false;

        // 帧率设置
        TargetFrameRate = 30;

        // Shader的LOD设置
        // 对于高配，shader当中将lod设为800
        Global_ShaderLOD = HighShaderLOD;

        // 摄像机LOD--bloom效果和景深效果
        CameraBloom = true;
        CameraDepth = true;

        // 资源缓冲池:最大缓冲个数
        AssetCacheNumCapacity = 120;

        // 资源缓冲池:最大缓冲内存
        AssetCacheMemCapacity = 80 * 1024 * 1024;

        Tier = UnityEngine.Rendering.GraphicsTier.Tier2;

        BoneNumber = BlendWeights.FourBones;

        // Unity will automatically set this value according to it's internal policy.
        ShadowCustomResolution = 512;

        Overload = 60;
    }
}
