//#define UNITY_ANDROID
using UnityEngine;
using TKPlugins;
using System.Collections.Generic;
using System;

namespace TKFrame
{
    public class WhiteDeviceProfileInfo
    {
        public int iProfileSourceType;//该手机对应配置类型
        public string StrProfileSourceValue;//该手机对应配置类型值
        public int iRecommendDevicePower;//该手机的白名单档位设置
        public int iLODConfigVersion;//lod配置版本号
        //-----------------------可通过配置覆盖的LOD配置-------------------------//
        public string StrDesignResolutionWidth; //设置特定渲染分辨率--宽
        public string StrDesignResolutionHight; //设置特定渲染分辨率--高
        public string StrTraverseDesignResolutionWidth; //设置降级渲染分辨率--宽
        public string StrTraverseDesignResolutionHight; //设置降级渲染分辨率--高
        public string StrTargetFrameRate; // 帧率设置
        public string StrHighResolution; // 高清设置
        public string StrGlobal_ShaderLOD; // Shader的LOD设置--ShaderLOD
        public string StrCameraBloom; //像机LOD--bloom效果
        public string StrCameraDepth; //像机LOD--景深效果
    }

    public class DeviceSettingPerformer : GraphicSettingPerformerBase, IService
    {
        public static int OverloadThresholdLow = 60;
        public static int OverloadThresholdMid = 100;
        public static int OverloadThresholdHigh = 210;

        //目前最高RecommendDevicePower就是极高（包括编辑器）
        public EDevicePower RecommendDevicePower { get; set; }

        public int RecommendOverloadThreshold { get; private set; }

        public LODBaseConfig RecommendPhoneLODConfig { get; private set; }

        public List<WhiteDeviceProfileInfo> _allWhiteProfileLst = new List<WhiteDeviceProfileInfo>();

        public DeviceSettingPerformer()
        {
            Instance = this;
            GameLOD.Create();
            ScreenResolutionSystem.Create();
        }

        public void UpdateHigh()
        {
            RecommendDevicePower = EDevicePower.EDP_High;
            RecommendOverloadThreshold = OverloadThresholdHigh;

#if CANVAS_BATCHS_OPT
            OutlineFast.InitDeviceLod(true);
#endif
        }

        public void UpdateMid()
        {
            RecommendDevicePower = EDevicePower.EDP_Middle;
            RecommendOverloadThreshold = OverloadThresholdMid;

#if CANVAS_BATCHS_OPT
            OutlineFast.InitDeviceLod(true);
#endif
        }

        public void UpdateLow()
        {
            RecommendDevicePower = EDevicePower.EDP_Low;
            RecommendOverloadThreshold = OverloadThresholdLow;

#if CANVAS_BATCHS_OPT
            OutlineFast.InitDeviceLod(true);
#endif
        }

        /// <summary>
        /// 查找当前机型是否配置白名单配置
        /// </summary>
        /// <returns></returns>
        public WhiteDeviceProfileInfo FindWhiteDeviceProfile()
        {
            WhiteDeviceProfileInfo retProfileInfo = null;
            for (int i = 0; i < _allWhiteProfileLst.Count; ++i)
            {
                if (_allWhiteProfileLst[i].iProfileSourceType == (int)ESourceType.SRC_DeviceModel &&
                    _allWhiteProfileLst[i].StrProfileSourceValue == DeviceProfileManager.Instance.GetDeviceModel())
                {
                    retProfileInfo = _allWhiteProfileLst[i];
                    break;
                }
                else if (_allWhiteProfileLst[i].iProfileSourceType == (int)ESourceType.SRC_MemorySizeInGB &&
                    _allWhiteProfileLst[i].StrProfileSourceValue == DeviceProfileManager.Instance.GetMemorySizeInGB())
                {
                    retProfileInfo = _allWhiteProfileLst[i];
                    break;
                }
                else if (_allWhiteProfileLst[i].iProfileSourceType == (int)ESourceType.SRC_GpuFamily &&
                    _allWhiteProfileLst[i].StrProfileSourceValue == DeviceProfileManager.Instance.GetGPUFamily())
                {
                    retProfileInfo = _allWhiteProfileLst[i];
                    break;
                }
                else if (_allWhiteProfileLst[i].iProfileSourceType == (int)ESourceType.SRC_GlVersion &&
                    _allWhiteProfileLst[i].StrProfileSourceValue == DeviceProfileManager.Instance.GetGLVersion())
                {
                    retProfileInfo = _allWhiteProfileLst[i];
                    break;
                }
                else if (_allWhiteProfileLst[i].iProfileSourceType == (int)ESourceType.SRC_DeviceMake &&
                    _allWhiteProfileLst[i].StrProfileSourceValue == DeviceProfileManager.Instance.GetDeviceMake())
                {
                    retProfileInfo = _allWhiteProfileLst[i];
                    break;
                }
            }
            return retProfileInfo;
        }

        /// <summary>
        /// 根据配置的白名单机型配置复写lod高中低机型配置
        /// </summary>
        /// <returns></returns>
        public void OverrideRecommendDeviceProfile(WhiteDeviceProfileInfo profileInfo,int gameingSettingLodConfigVersion, ref EDevicePower gameingSettingDevicePower)
        {
            if (profileInfo != null)
            {
                if (RecommendPhoneLODConfig != null)
                {
                    if (profileInfo.iLODConfigVersion > 0)
                    {
                        LODBaseConfig.LOD_CONFIG_VERSION = profileInfo.iLODConfigVersion;
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrDesignResolutionWidth))
                    {
                        RecommendPhoneLODConfig.DesignResolutionWidth = Convert.ToInt32(profileInfo.StrDesignResolutionWidth);
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrDesignResolutionHight))
                    {
                        RecommendPhoneLODConfig.DesignResolutionHight = Convert.ToInt32(profileInfo.StrDesignResolutionHight);
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrTraverseDesignResolutionWidth))
                    {
                        RecommendPhoneLODConfig.TraverseDesignResolutionWidth = Convert.ToInt32(profileInfo.StrTraverseDesignResolutionWidth);
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrTraverseDesignResolutionHight))
                    {
                        RecommendPhoneLODConfig.TraverseDesignResolutionHeight = Convert.ToInt32(profileInfo.StrTraverseDesignResolutionHight);
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrTargetFrameRate))
                    {
                        RecommendPhoneLODConfig.TargetFrameRate = Convert.ToInt32(profileInfo.StrTargetFrameRate);
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrGlobal_ShaderLOD))
                    {
                        RecommendPhoneLODConfig.Global_ShaderLOD = Convert.ToInt32(profileInfo.StrGlobal_ShaderLOD);
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrHighResolution))
                    {
                        if (Convert.ToInt32(profileInfo.StrHighResolution) == 0)
                        {
                            RecommendPhoneLODConfig.HighResolution = false;
                        }
                        else
                        {
                            RecommendPhoneLODConfig.HighResolution = true;
                        }
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrCameraBloom))
                    {
                        if (Convert.ToInt32(profileInfo.StrCameraBloom) == 0)
                        {
                            RecommendPhoneLODConfig.CameraBloom = false;
                        }
                        else
                        {
                            RecommendPhoneLODConfig.CameraBloom = true;
                        }
                    }
                    if (!string.IsNullOrEmpty(profileInfo.StrCameraDepth))
                    {
                        if (Convert.ToInt32(profileInfo.StrCameraDepth) == 0)
                        {
                            RecommendPhoneLODConfig.CameraDepth = false;
                        }
                        else
                        {
                            RecommendPhoneLODConfig.CameraDepth = true;
                        }
                    }
                }

                if (profileInfo != null)
                {
                    if (profileInfo.iRecommendDevicePower > (int)EDevicePower.EDP_None)
                    {
                        if (gameingSettingLodConfigVersion < LODBaseConfig.LOD_CONFIG_VERSION)
                        {
                            RecommendDevicePower = (EDevicePower)profileInfo.iRecommendDevicePower;
                            gameingSettingDevicePower = EDevicePower.EDP_None;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 初始化游戏画质
        /// </summary>
        public void InitGameDisplayMode(ref EDevicePower gameingSettingDevicePower, ref int gameingSettingResolutionMode, ref int gameingSettingFrameRateMode, bool gameingSettingIsBPowerSaving, bool gameingSettingIsOutline, int gameingSettingLodConfigVersion)
        {
#if SET_SECONDRAY_ID_TEXTURE
            Diagnostic.Log("DeviceSettingPerformer: This engine enabled SET_SECONDRAY_ID_TEXTURE, output this log for debug purpose.");
#else
            Diagnostic.Log("DeviceSettingPerformer: This engine DISABLED SET_SECONDRAY_ID_TEXTURE, output this log for debug purpose.");
#endif
#if PIPELINE_SHADING_RATE
            Diagnostic.Log("DeviceSettingPerformer: This engine enabled PIPELINE_SHADING_RATE, output this log for debug purpose.");
#else
            Diagnostic.Log("DeviceSettingPerformer: This engine DISABLED PIPELINE_SHADING_RATE, output this log for debug purpose.");
#endif

            // 至臻设备直接返回Ultra。（编辑器也包括在内）
            if (HardwareCheck.IsExtremeDevice())
            {
                RecommendDevicePower = EDevicePower.EDP_Ultra;
                //if (gameingSettingDevicePower == EDevicePower.EDP_None)
                //{
                //    gameingSettingDevicePower = EDevicePower.EDP_Ultra;
                //    gameingSettingResolutionMode = (int)EResolutionType.EResolutionType_Original;
                //    gameingSettingFrameRateMode = (int)EFPS.EFPS_High;
                //}
            }
            else
                RecommendDevicePower = DeviceAssessment.CheckPerformancePower();

            //读取表格里面的机型配置白名单，用于复写lodbaseconfig配置
            TKFrameworkDelegateInterface.LoadDeviceWhiteConfig?.Invoke(_allWhiteProfileLst);
            WhiteDeviceProfileInfo whiteProfile =  FindWhiteDeviceProfile();
            //根据性能配置的高、中、低生成推荐画质对应的配置信息
            GenerateRecommendPhoneLODConfig(RecommendDevicePower);
            OverrideRecommendDeviceProfile(whiteProfile, gameingSettingLodConfigVersion, ref gameingSettingDevicePower);
            int resolutionMode;
            int frameRateMode;

            EDevicePower devicePower;
            // 首次初始化设置
            if (gameingSettingDevicePower == EDevicePower.EDP_None)
            {
                // 如果是iPad或模拟器且是高端机，默认开启高清高帧率
                if ((TKFrameworkDelegateInterface.NotchSizeImp_IsPad() || TKFrameworkDelegateInterface.IsSimulator()) && RecommendDevicePower >= EDevicePower.EDP_High)
                {
                    resolutionMode = (int)EResolutionType.EResolutionType_High;
                    frameRateMode = (int)EFPS.EFPS_High;
                }
                else
                {
                    resolutionMode = RecommendPhoneLODConfig.HighResolution ? (int)EResolutionType.EResolutionType_High : (int)EResolutionType.EResolutionType_Low;
                    frameRateMode = RecommendPhoneLODConfig.TargetFrameRate >= 60 ? (int)EFPS.EFPS_High : (int)EFPS.EFPS_Low;
                }
                devicePower = RecommendDevicePower;

                gameingSettingDevicePower = devicePower;
                gameingSettingResolutionMode = resolutionMode;
                gameingSettingFrameRateMode = frameRateMode;
                Diagnostic.Log($"DeviceSettingPerformer.cs: No previous LOD settings found, init LOD setting result: devicePower: {devicePower}, resolutionMode: {resolutionMode}, frameRateMode: {frameRateMode}");
            }
            else
            {
                devicePower = gameingSettingDevicePower;
                resolutionMode = gameingSettingResolutionMode;
                frameRateMode = gameingSettingFrameRateMode;
                Diagnostic.Log($"DeviceSettingPerformer.cs: Previous LOD settings found, using setting result: devicePower: {devicePower}, resolutionMode: {resolutionMode}, frameRateMode: {frameRateMode}");
            }

            // Do not enable tkpps for low tier devices.
            TKPostProcessingStack.FeatureEnableMark = 
                RecommendDevicePower > EDevicePower.EDP_Low
                && !HardwareCheck.IsJunkDevices;

            // Not edit mode, this is a normal game run.
            // Which means enable downsample.
            TKPostProcessingStack.IsRunningInGameSceen = true;

            if (!TKPostProcessingStack.FeatureEnableMark)
                Diagnostic.Warn("DeviceSettingPerformer.cs: TKPostProcessing is disabled for low device or low memory devices.");
            //综合根据玩家选择的画质或者用户首次进游戏的默认画质，进行LOD设置。
            GameLOD.Instance.ChangeLOD(devicePower, resolutionMode, frameRateMode, gameingSettingIsBPowerSaving);
            GameLOD.Instance.ChangeOutline(gameingSettingIsOutline);
        }

        //根据性能配置的高、中、低生成画质对应的配置信息
        // 不论设备如何强劲，最多只给生成到高配（EDP_High），更高画质玩家必须手动修改。
        public void GenerateRecommendPhoneLODConfig(EDevicePower recommendDevicePower)
        {
            if (recommendDevicePower == EDevicePower.EDP_Low)
            {
                RecommendPhoneLODConfig = new LODLowConfig();
                RecommendOverloadThreshold = OverloadThresholdLow;
            }
            else if (recommendDevicePower == EDevicePower.EDP_Middle)
            {
                RecommendPhoneLODConfig = new LODMidConfig();
                RecommendOverloadThreshold = OverloadThresholdMid;
            }
            else if (recommendDevicePower >= EDevicePower.EDP_High)
            {
                RecommendPhoneLODConfig = new LODHighConfig();
                RecommendOverloadThreshold = OverloadThresholdHigh;
            }
            else
            {
                RecommendPhoneLODConfig = new LODLowConfig();
                RecommendOverloadThreshold = OverloadThresholdLow;
                Diagnostic.Error("DeviceSettingPerformer.cs: cannot find any recommendDevicePower!!");
            }
        }

        public override bool QueryActivityByGraphicLevel(GraphicLevelBehaviour bhv)
        {

            if (GraphicsSetting.Graphics >= bhv.glLevel)
            {
                return true;
            }
            return false;
        }
    }
}