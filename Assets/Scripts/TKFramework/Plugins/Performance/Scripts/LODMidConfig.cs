using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 中画质LOD配置
/// </summary>
public class LODMidConfig : LODBaseConfig
{
    public LODMidConfig()
    {
        // 渲染分辨率设置
        DesignResolutionWidth = 1280;  //设置特定渲染分辨率--宽
        DesignResolutionHight = 720 ; //设置特定渲染分辨率--高

        // 降级渲染分辨率设置
        TraverseDesignResolutionWidth = 1136; //设置降级渲染分辨率--宽
        TraverseDesignResolutionHeight = 640;  //设置降级渲染分辨率--高

        // 高清设置
        HighResolution = false;

        // 帧率设置
        TargetFrameRate = 30;

        // Shader的LOD设置
        Global_ShaderLOD = 200;

        // 摄像机LOD--bloom效果和景深效果
        CameraBloom = false;
        CameraDepth = false;

        // 资源缓冲池:最大缓冲个数
        AssetCacheNumCapacity = 100;

        // 资源缓冲池:最大缓冲内存
        AssetCacheMemCapacity = 60 * 1024 * 1024;

        Tier = UnityEngine.Rendering.GraphicsTier.Tier2;

        BoneNumber = BlendWeights.FourBones;

        ShadowCustomResolution = 512;

        Overload = 45;
    }
}
