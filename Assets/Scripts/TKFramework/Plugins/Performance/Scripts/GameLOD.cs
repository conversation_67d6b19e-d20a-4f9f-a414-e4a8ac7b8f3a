using UnityEngine;
using System.Collections.Generic;
using NTween;
using TKFrame;
using TKPlugins;
using System;
using UnityEngine.Rendering;

#if UNITY_EDITOR
[UnityEditor.InitializeOnLoad]
// this class is used to load high graphics tier
// when editor launch, for artist to preview HDR version of artifact.
public class LoadHighGraphicsTierInEditor
{
    static LoadHighGraphicsTierInEditor() => Graphics.activeTier = GraphicsTier.Tier3;
}
#endif

/// <summary>
/// 黑名单设备信息 
/// </summary>
public class DeviceBlackListInfo
{
    public string DeviceModel;//设备名字
    public string ExcuteType; //执行类型
}

/// <summary>
/// LOD派发事件
/// </summary>
public class EventLODEventArgs : TKEventArgs
{
    public EDevicePower DevicePower { get; set; }

    public EventLODEventArgs(EDevicePower power) => DevicePower = power;
}

public class EventFPSEventArgs : TKEventArgs
{
    public int targetFPS { get; set; }
    public EventFPSEventArgs(int fps) => targetFPS = fps;
}

/// <summary>
/// 分辨率切换派发事件
/// </summary>
public class EventResolutionEventArgs : TKEventArgs
{
    public int TargetScreenWidth { get; set; }

    public int TargetScreenHeight { get; set; }

    public int EnableHighResolution { get; set; }

    public EventResolutionEventArgs(int screenWidth, int screenHeight, int highResolution)
    {
        TargetScreenWidth = screenWidth;
        TargetScreenHeight = screenHeight;
        EnableHighResolution = highResolution;
    }
}

public class EventShadowCustomResolutionEventArgs : TKEventArgs
{
    public int ShadowCustomResolution { get; private set; }

    public EventShadowCustomResolutionEventArgs(int shadowCustomResolution) => ShadowCustomResolution = shadowCustomResolution;
}

/// <summary>
/// HDR Bloom开关派发事件
/// </summary>
public class EventHDRBloomEventArgs : TKEventArgs
{
    public bool EnableHDRBloom { get; set; }

    public EventHDRBloomEventArgs(bool enableHDRBloom) => EnableHDRBloom = enableHDRBloom;
}

public class EventHDREventArgs : TKEventArgs
{
    public bool EnableHDR { get; set; }

    public EventHDREventArgs(bool enableHDR) => EnableHDR = enableHDR;
}

public class EventDecreaseGraphicsArgs : TKEventArgs
{
    public enum Reason
    {
        None = 0,
        SystemDownRateBattery = 1,
        SystemDownRateTemperature = 2,
        SystemDownRateOther = 3,
        TemperatureDownRateNoLimit = 4,
        TemperatureDownRateSlightly = 5,
        TemperatureDownRateEnormous = 6,
        TemperatureChange = 7,
    }

    public Reason DeviceChangeReasonTGPA { get; set; }

    public EventDecreaseGraphicsArgs(Reason deviceChangeReasonTGPA) => DeviceChangeReasonTGPA = deviceChangeReasonTGPA;
}

public class EventMSAAEventArgs : TKEventArgs
{
    public TKPostProcessingStack.MultiSampleAntiAliasing MultiSampleAntiAliasing { get; set; }

    public EventMSAAEventArgs(TKPostProcessingStack.MultiSampleAntiAliasing multiSampleAntiAliasing) => MultiSampleAntiAliasing = multiSampleAntiAliasing;
}

public class EventSMAAEventArgs : TKEventArgs
{
    public bool EnableSMAA { get; set; }

    public EventSMAAEventArgs(bool enableSMAA) => EnableSMAA = enableSMAA;
}

public class EventTKPPSDownSampleEventArgs : TKEventArgs
{
    public float TKPPSDownSample { get; set; }
    public EventTKPPSDownSampleEventArgs(float tkppsDownSampleRate) => TKPPSDownSample = tkppsDownSampleRate;
}

/// <summary>
/// 描边开关派发事件
/// </summary>
public class EventOutlineEventArgs : TKEventArgs
{
    public bool EnableOutline { get; set; }

    public EventOutlineEventArgs(bool enableOutline) => EnableOutline = enableOutline;
}

/// <summary>
/// fxaa
/// </summary>
public class EventFXAAEventArgs : TKEventArgs
{
    public bool EnableFXAA { get; set; }

    public EventFXAAEventArgs(bool enableFXAA) => EnableFXAA = enableFXAA;
}

public class EventAntiAliasingEventArgs : TKEventArgs
{
    public bool EnableAA { get; set; }

    public EventAntiAliasingEventArgs(bool enableAA) => EnableAA = enableAA;
}

/// <summary>
/// SSAO开关派发事件
/// </summary>
public class EventSSAOEventArgs : TKEventArgs
{
    public bool EnableSSAO { get; set; }
    public bool EnableNormalTexture { get; set; }

    public EventSSAOEventArgs(bool enableSSAO, bool enableNormalTexture)
    {
        EnableSSAO = enableSSAO;
        EnableNormalTexture = enableNormalTexture;
    }
}

public class EventSoftlightEventArgs : TKEventArgs
{
    public bool EnableSoftlight { get; set; }
    public EventSoftlightEventArgs(bool enableSoftlight) => EnableSoftlight = enableSoftlight;
}

public class EventColorGradingEventArgs : TKEventArgs
{
    public bool EnableColorGrading { get; set; }
    public EventColorGradingEventArgs(bool enableColorGrading) => EnableColorGrading = enableColorGrading;
}

public class EventGlitchRGBSplitEventArgs : TKEventArgs
{
    public bool EnableGlitchRGBSplit { get; set; }
    public EventGlitchRGBSplitEventArgs(bool enableGlitchRGBSplit) => EnableGlitchRGBSplit = enableGlitchRGBSplit;
}

public class EventContrastEnhanceEventArgs : TKEventArgs
{
    public bool EnableContrastEnhance { get; set; }
    public EventContrastEnhanceEventArgs(bool enableContrastEnhance) => EnableContrastEnhance = enableContrastEnhance;
}

/// <summary>
/// 屏幕暗角开关派发事件
/// </summary>
public class EventVignetteEventArgs : TKEventArgs
{
    public bool EnableVignette { get; set; }

    public EventVignetteEventArgs(bool enableVignette) => EnableVignette = enableVignette;
}

/// <summary>
/// The event of enable offscreen render. aka <see cref="GfxFramework.GfxOffScreenRenderCamera"/>.
/// </summary>
public class EventUseOffscreenEventArgs : TKEventArgs
{
    public bool EnableUseOffscreenRenderingTech { get; set; }
    public EventUseOffscreenEventArgs(bool enableUseOffscreenRenderingTech) => EnableUseOffscreenRenderingTech = enableUseOffscreenRenderingTech;
}

/// <summary>
/// 负载值变化派发事件
/// </summary>
public class EventOverloadEventArgs : TKEventArgs
{
    public int NewOverloadScore { get; set; }

    public EventOverloadEventArgs(int newOverloadScore) => NewOverloadScore = newOverloadScore;
}

/// <summary>
/// 全局存在的一个对象
/// </summary>
public class GameLOD : Singleton<GameLOD>
{
    private static bool s_IsBlackDevice = false;  //是否白名单设备
    private static readonly List<string> s_BlackDeviceExcTypeArray = new List<string>();   //白名单设备执行动作类型
    public const int MaximumLODWithoutGrabPass = 999; // 在没开启grabpass的前提下最大的lod

    //用户选择机型画质
    public EDevicePower DevicePower { get; private set; }

    //用户选择机型画质---对应的LOD配置
    public LODBaseConfig PhoneLODConfig { get; private set; }

    public bool IsHDREnabled
#if UNITY_EDITOR
        = true;
#else
        ;
#endif

    /// <summary>
    /// 当前的帧率
    /// </summary>
    public int TargetFrameRate = 30;

    /// <summary>
    /// 设置的帧率模式
    /// </summary>
    public int FPSMode = 1;

    // 0:low 1:high 2:ori
    public int ResolutionMode = 0;

    /// <summary>
    /// HDR Bloom开关
    /// </summary>
    public bool EffectHDRBloom = true;

    /// <summary>
    /// 角色描边开关
    /// </summary>
    public bool Outline = true;

    /// <summary>
    /// 抗锯齿开关，注意在至臻画质下，这玩意是MSAA，其他画质，这玩意是FXAA
    /// </summary>
    public bool AntiAliasing = true;

    /// <summary>
    /// FXAA
    /// </summary>
    public bool FXAA = true;

    /// <summary>
    /// MSAA
    /// </summary>
    public TKPostProcessingStack.MultiSampleAntiAliasing MSAA = TKPostProcessingStack.MultiSampleAntiAliasing.None;

    public bool SMAA = true;

    /// <summary>
    /// 全屏柔光开关
    /// </summary>
    public bool EffectSoftlight = true;
    /// <summary>
    /// 锐化开关
    /// </summary>
    public bool EffectContrastEnhance = true;
    /// <summary>
    /// 画面风格增强
    /// </summary>
    public bool EffectColorGrading = true;
    /// <summary>
    /// 环境光遮蔽
    /// </summary>
    public bool EffectSSAO = true;

    /// <summary>
    /// 屏幕暗角开关
    /// </summary>
    public bool Vignette = true;

    /// <summary>
    /// If true, will activate <see cref="GfxFramework.GfxOffScreenRenderCamera"/> to render.
    /// </summary>
    public bool UseOffscreenRender = false;

    public bool BDecreaseQuality = false;

    public bool PowerSaving = false;

    public bool isOpenPerformanceTesting = false;

    /// <summary>
    /// If true, the device seems burden heavily. So, player will receive a hint.
    /// </summary>
    public bool ShouldMentionDecreaseGraphics { get; private set; } = false;

    public int curMaxFPS = 0;
    /// <summary>
    /// <br>重要：只能被GM面板改写</br>
    /// <br>不论出于何种原因调用<see cref="SetMaxFPS(int)"/>，都会把目标帧率设置成这个值</br>
    /// </summary>
    public int GMOverrideConstantFPS { get; set; } = 0;

    /// <summary>
    /// <br>重要：只能通过GM面板改写</br>
    /// <br>不论出于何种目的调用<see cref="ChangeLOD"/>，都会把画面覆盖成这个值</br>
    /// <br>为<see cref="EDevicePower.EDP_None"/>的时候，不会覆盖</br>
    /// </summary>
    public EDevicePower GMOverrideConstantDevicePower { get; set; } = EDevicePower.EDP_None;

    /// <summary>
    /// 负载分数
    /// </summary>
    public int OverloadScore = 0;

    /// <summary>
    /// Device overload score, access and modified by xlsm.
    /// </summary>
    public int DeviceOverloadScore = 0;

    /// <summary>
    /// 场景LOD, 这个在每个场景里都会有一个，所以不需要在外层配置，每次进入一个新的场景更新这个值即可
    /// </summary>
    public SceneLODCfgLogic CurSceneCfg { get; private set; }

    //lod切换事件派发
    private EventHandler<EventLODEventArgs> _LODeventRaise;
    public event EventHandler<EventLODEventArgs> LODEventRaise
    {
        add
        {
            if (_LODeventRaise == null)
                _LODeventRaise = value;
            else
                _LODeventRaise += value;
        }

        remove => _LODeventRaise -= value;
    }

    private EventHandler<EventFPSEventArgs> _FPSEventRaise;
    public event EventHandler<EventFPSEventArgs> FPSEventRaise
    {
        add
        {
            if (_FPSEventRaise == null)
                _FPSEventRaise = value;
            else
                _FPSEventRaise += value;
        }
        remove => _FPSEventRaise -= value;
    }

    private EventHandler<EventLODEventArgs> _EffectLODeventRaise;
    public event EventHandler<EventLODEventArgs> EffectLODEventRaise
    {
        add
        {
            if (_EffectLODeventRaise == null)
                _EffectLODeventRaise = value;
            else
                _EffectLODeventRaise += value;
        }

        remove => _EffectLODeventRaise -= value;
    }

    // 分辨率切换事件派发
    private EventHandler<EventResolutionEventArgs> _ResolutionRaise;
    public event EventHandler<EventResolutionEventArgs> ResolutionRaise
    {
        add
        {
            if (_ResolutionRaise == null)
                _ResolutionRaise = value;
            else
                _ResolutionRaise += value;
        }

        remove => _ResolutionRaise -= value;
    }

    // HDR Bloom开关切换事件派发
    private EventHandler<EventHDRBloomEventArgs> _HDRBloomRaise;
    public event EventHandler<EventHDRBloomEventArgs> HDRBloomRaise
    {
        add
        {
            if (_HDRBloomRaise == null)
                _HDRBloomRaise = value;
            else
                _HDRBloomRaise += value;
        }

        remove => _HDRBloomRaise -= value;
    }

    private EventHandler<EventHDREventArgs> _HDRRaise;
    public event EventHandler<EventHDREventArgs> HDRRaise
    {
        add
        {
            if (_HDRRaise == null)
                _HDRRaise = value;
            else
                _HDRRaise += value;
        }

        remove => _HDRRaise -= value;
    }

    private EventHandler<EventContrastEnhanceEventArgs> _ContrastEnhanceRaise;
    public event EventHandler<EventContrastEnhanceEventArgs> ContrastEnhanceRaise
    {
        add
        {
            if (_ContrastEnhanceRaise == null)
                _ContrastEnhanceRaise = value;
            else
                _ContrastEnhanceRaise += value;
        }

        remove => _ContrastEnhanceRaise -= value;
    }

    private EventHandler<EventSoftlightEventArgs> _SoftlightRaise;
    public event EventHandler<EventSoftlightEventArgs> SoftlightRaise
    {
        add
        {
            if (_SoftlightRaise == null)
                _SoftlightRaise = value;
            else
                _SoftlightRaise += value;
        }

        remove => _SoftlightRaise -= value;
    }

    private EventHandler<EventColorGradingEventArgs> _ColorGradingRaise;
    public event EventHandler<EventColorGradingEventArgs> ColorGradingRaise
    {
        add
        {
            if (_ColorGradingRaise == null)
                _ColorGradingRaise = value;
            else
                _ColorGradingRaise += value;
        }

        remove => _ColorGradingRaise -= value;
    }

    private EventHandler<EventGlitchRGBSplitEventArgs> _GlitchRGBSplitRaise;
    public event EventHandler<EventGlitchRGBSplitEventArgs> GlitchRGBSplitRaise
    {
        add
        {
            if (_GlitchRGBSplitRaise == null)
                _GlitchRGBSplitRaise = value;
            else
                _GlitchRGBSplitRaise += value;
        }

        remove => _GlitchRGBSplitRaise -= value;
    }

    private EventHandler<EventSSAOEventArgs> _SSAORaise;
    public event EventHandler<EventSSAOEventArgs> SSAORaise

    {
        add
        {
            if (_SSAORaise == null)
                _SSAORaise = value;
            else
                _SSAORaise += value;
        }

        remove => _SSAORaise -= value;
    }

    private EventHandler<EventDecreaseGraphicsArgs> _eventDecreasesGraphicsRaise;
    /// <summary>
    /// Do remember to lock and unlock this if add or remove!
    /// </summary>
    public event EventHandler<EventDecreaseGraphicsArgs> EventDecreasesGraphicsRaise
    {
        add
        {
            switch (_eventDecreasesGraphicsRaise)
            {
                case null:
                    _eventDecreasesGraphicsRaise = value;
                    break;
                default:
                    _eventDecreasesGraphicsRaise += value;
                    break;
            }
        }
        remove => _eventDecreasesGraphicsRaise -= value;
    }

    private EventHandler<EventMSAAEventArgs> _msaaRaise;
    public event EventHandler<EventMSAAEventArgs> MSAARaise
    {
        add
        {
            if (_msaaRaise == null)
                _msaaRaise = value;
            else
                _msaaRaise += value;
        }

        remove => _msaaRaise -= value;
    }

    private EventHandler<EventSMAAEventArgs> _smaaRaise;
    public event EventHandler<EventSMAAEventArgs> SMAARaise
    {
        add
        {
            if (_smaaRaise == null)
                _smaaRaise = value;
            else
                _smaaRaise += value;
        }

        remove => _smaaRaise -= value;
    }

    private EventHandler<EventFXAAEventArgs> _fxaaRaise;
    public event EventHandler<EventFXAAEventArgs> FXAARaise
    {
        add
        {
            if (_fxaaRaise == null)
                _fxaaRaise = value;
            else
                _fxaaRaise += value;
        }

        remove => _fxaaRaise -= value;
    }

    private EventHandler<EventTKPPSDownSampleEventArgs> _tkppsDownSampleRaise;
    public event EventHandler<EventTKPPSDownSampleEventArgs> TKPPSDownSampleRaise
    {
        add
        {
            if (_tkppsDownSampleRaise == null)
                _tkppsDownSampleRaise = value;
            else
                _tkppsDownSampleRaise += value;
        }

        remove => _tkppsDownSampleRaise -= value;
    }

    // 描边开关切换事件派发
    private EventHandler<EventOutlineEventArgs> _OutlineRaise;
    public event EventHandler<EventOutlineEventArgs> OutlineRaise
    {
        add
        {
            if (_OutlineRaise == null)
                _OutlineRaise = value;
            else
                _OutlineRaise += value;
        }

        remove => _OutlineRaise -= value;
    }

    private EventHandler<EventShadowCustomResolutionEventArgs> _ShadowCustomResolutionRaise;
    public event EventHandler<EventShadowCustomResolutionEventArgs> ShadowCustomResolutionRaise
    {
        add
        {
            if (_ShadowCustomResolutionRaise == null)
                _ShadowCustomResolutionRaise = value;
            else
                _ShadowCustomResolutionRaise += value;
        }
        remove => _ShadowCustomResolutionRaise -= value;
    }

    // 抗锯齿开关切换事件派发
    private EventHandler<EventAntiAliasingEventArgs> _AntiAliasingRaise;
    public event EventHandler<EventAntiAliasingEventArgs> AntiAliasingRaise
    {
        add
        {
            if (_AntiAliasingRaise == null)
                _AntiAliasingRaise = value;
            else
                _AntiAliasingRaise += value;
        }

        remove => _AntiAliasingRaise -= value;
    }

    /// <summary>
    /// Event raiser of <see cref="EventUseOffscreenEventArgs"/>
    /// </summary>
    private EventHandler<EventUseOffscreenEventArgs> _UseOffscreenRaise;
    public event EventHandler<EventUseOffscreenEventArgs> UseOffscreenRaise
    {
        add
        {
            if (_UseOffscreenRaise == null)
                _UseOffscreenRaise = value;
            else
                _UseOffscreenRaise += value;
        }

        remove => _UseOffscreenRaise -= value;
    }

    // 负载值变化事件派发
    private EventHandler<EventOverloadEventArgs> _OverloadRaise;
    public event EventHandler<EventOverloadEventArgs> OverloadRaise
    {
        add
        {
            if (_OverloadRaise == null)
                _OverloadRaise = value;
            else
                _OverloadRaise += value;
        }

        remove => _OverloadRaise -= value;
    }

    public static void CheckIsBlackListDevice(List<DeviceBlackListInfo> deviceBlackList)
    {
        if (deviceBlackList != null)
        {
            s_BlackDeviceExcTypeArray.Clear();
            for (int i = deviceBlackList.Count; --i >= 0;)
            {
                string blackDevice = deviceBlackList[i].DeviceModel;
                string deviceModelLower = SystemInfo.deviceModel.ToLower();
                if (deviceModelLower.Contains(blackDevice.ToLower()))
                {
                    s_IsBlackDevice = true;
                    s_BlackDeviceExcTypeArray.Add(deviceBlackList[i].ExcuteType);
                }
            }
        }
    }

    public void GeneratePhoneLODConfig(EDevicePower devicePower)
    {
        //根据性能配置生成画质对应的配置信息
        if (devicePower == EDevicePower.EDP_Low)
            PhoneLODConfig = new LODLowConfig();
        else if (devicePower == EDevicePower.EDP_Middle)
            PhoneLODConfig = new LODMidConfig();
        else if (devicePower == EDevicePower.EDP_High)
            PhoneLODConfig = new LODHighConfig();
        else if (devicePower == EDevicePower.EDP_Ultra)
            PhoneLODConfig = new LODUltraConfig();
        else if (devicePower == EDevicePower.EDP_Extreme)
            PhoneLODConfig = new LODExtremeConfig();
        else if (devicePower == EDevicePower.EDP_ExtremeV2)
            PhoneLODConfig = new LODExtremeV2Config();
        else if (devicePower == EDevicePower.EDP_ExtremeV3)
            PhoneLODConfig = new LODExtremeV3Config();
        else
        {
            PhoneLODConfig = new LODLowConfig();
            Diagnostic.Log("GameLOD.cs: cannot find any devicePower!!");
        }
    }

    public void ChangeEffectLOD(EDevicePower effectLodDevicePower) => DispatchEffectLODChangeMessage(this, effectLodDevicePower);

    private void ChangeGraphicsTierRuntime() => ChangeGraphicsTierRuntime(PhoneLODConfig.Tier);

    public void ChangeGraphicsTierRuntime(GraphicsTier tier)
    {
        Graphics.activeTier = tier;
        Diagnostic.Log("GameLOD.cs: ChangeGraphicsTierRuntime: " + tier.ToString());
        if (Graphics.activeTier >= GraphicsTier.Tier3)
            ChangeHDR(true);
        else
            ChangeHDR(false);
    }

    /// <summary>
    /// 更新负载值
    /// </summary>
    private void UpdateOverloadScore()
    {
        float resolutionRatio = 1.0f;
        DeviceSettingPerformer ds = Services.GetService<DeviceSettingPerformer>();
        if (ds != null)
        {
            int height = Screen.height;
            int width = Screen.width;
            // 当帧SetResolution后Screen.height/width取到的不是最新值
            var srs = ScreenResolutionSystem.Instance;
            if (srs != null)
            {
                height = srs.scaleHeight;
                width = srs.scaleWidth;
            }
            // 其实由于我们的游戏是横屏显示的，所以这个总是相当于(float)height / ScreenResolutionSystem.Instance.scaleHeight。
            resolutionRatio = (float)Mathf.Min(height, width) / Mathf.Min(ScreenResolutionSystem.Instance.scaleHeight, ScreenResolutionSystem.Instance.scaleHeight);
        }

        float frameRateRatio;
        switch (FPSMode)
        {
            case (int)EFPS.EFPS_Low:
                frameRateRatio = 1.0f;
                break;
            case (int)EFPS.EFPS_High:
                frameRateRatio = 2.0f;
                break;
            case (int)EFPS.EFPS_Ultra:
                frameRateRatio = Mathf.Min(90, HardwareCheck.GetDeviceMaximumFPS()) / 30f;
                break;
            case (int)EFPS.EFPS_Ultra_1:
                frameRateRatio = HardwareCheck.GetDeviceMaximumFPS() / 30f;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(FPSMode));
        }

        int outlineOverloadFetched = HardwareCheck.GetDeviceStatusParameter("Outline");
        int antiAliasingOverloadFetched = HardwareCheck.GetDeviceStatusParameter(SMAA ? "smaa" : "fxaa");
        float outlineOverload = Outline ? outlineOverloadFetched < 0 ? 30f : outlineOverloadFetched : 0f;
        float antiAliasingOverload = AntiAliasing ? antiAliasingOverloadFetched < 0 ? 30f : antiAliasingOverloadFetched : 0f;

        int lodConfigOverload;
        if (PhoneLODConfig is LODLowConfig)
            lodConfigOverload = HardwareCheck.GetDeviceStatusParameter("LODConfig_low");
        else if (PhoneLODConfig is LODMidConfig)
            lodConfigOverload = HardwareCheck.GetDeviceStatusParameter("LODConfig_mid");
        else if (PhoneLODConfig is LODHighConfig)
            lodConfigOverload = HardwareCheck.GetDeviceStatusParameter("LODConfig_high");
        else if (PhoneLODConfig is LODUltraConfig)
            lodConfigOverload = HardwareCheck.GetDeviceStatusParameter("LODConfig_ultra");
        else if (PhoneLODConfig is LODExtremeConfig)
            lodConfigOverload = HardwareCheck.GetDeviceStatusParameter("LODConfig_extreme");
        else
            lodConfigOverload = HardwareCheck.GetDeviceStatusParameter("LODConfig_null");

        // If no overload value found in xlsm
        if (lodConfigOverload < 0)
            lodConfigOverload = PhoneLODConfig.Overload;

        if (PhoneLODConfig != null)
            OverloadScore = Mathf.FloorToInt((lodConfigOverload + outlineOverload + antiAliasingOverload) * resolutionRatio * frameRateRatio);
        DispatchOverloadChangeMessage(this, OverloadScore);
        Diagnostic.Log("GameLOD.cs: UpdateOverloadScore: " + OverloadScore);

        // Change graphics in ultra config might result in change shaderlod for enable or disable grab pass.
        // This method is invoked by sure by any graphics setting change.
        // For release common, find another method instead.
        ChangeShaderLOD(PhoneLODConfig.Global_ShaderLOD);
    }

    /// <summary>
    /// 更新场景的LOD配置数据
    /// 这个在每次进入新的局内场景需要获取最新的赋值过来使用
    /// </summary>
    /// <param name="sceneLOD"></param>
    public void UpdateSceneLOD(SceneLODCfgLogic sceneLOD) => CurSceneCfg = sceneLOD;

    public void ChangePowerSaving(bool bSaving)
    {
        bool opowerSaving = PowerSaving;
        PowerSaving = bSaving;
        ScreenResolutionSystem.Instance.BPowerSaving = PowerSaving;

        if (PowerSaving == opowerSaving)
            return;

        ChangeScreenResolution(ResolutionMode);
    }

    /// <summary>
    /// <br>这个函数不会修改<see cref="FPSMode"/>，所以如果想要还原当前场景的默认帧率，请调用<see cref="ChangeTargetFrameRateUsingFPSMode"/>并传入<see cref="FPSMode"/>。</br>
    /// <br>如果是通过GM面板修改的帧率，调用此方法将总是设置成GM面板指定的帧率<see cref="GMOverrideConstantFPS"/>，除非杀进程重启。</br>
    /// <br>游戏如果修改帧率请务必使用该函数，禁止直接使用<see cref="Application.targetFrameRate"/>！！！</br>
    /// </summary>
    /// <param name="fps"></param>
    public void SetMaxFPS(int fps)
    {
        curMaxFPS = fps;
        int targetFPS = GMOverrideConstantFPS > 0 ? GMOverrideConstantFPS : fps;
        if (Application.targetFrameRate != targetFPS)
        {
            Application.targetFrameRate = targetFPS;
            string message = GMOverrideConstantFPS > 0 ? "SetMaxFPS, overrided by GM panel: " + GMOverrideConstantFPS : "SetMaxFPS: " + fps;
            Diagnostic.Log("GameLOD.cs: " + message);
            DispatchFPSChangeMessage(this, Application.targetFrameRate);
        }
    }

    /// <summary>
    /// <br>使用(int)<see cref="EFPS"/>更改帧率。注意，该函数依赖ZGame.Stage.LoginManager.IsInGame来决定采取局内还是局外策略，故必须在修改ZGame.Stage.LoginManager.IsInGame之后调用。</br>
    /// <br>局内：遵循设置面板设置的结果，不看机型</br>
    /// <br>局外：高端机60fps，中端机及以下30fps，这个策略无视fpsMode传入的参数。注意，如果局外静置一段时间，则可能降帧处理。这部分策略写在QQGameSystem和FPSCount当中。可以前往查看。</br>
    /// <br>设置面板相关档位的显示策略写在FrameGroup.Start()函数当中。</br>
    /// <br>注意GM面板更改的帧率会覆盖其结果。如果通过GM面板修改了帧率，除非杀进程重启，否则帧率永远都是GM面板指定的值</br>
    /// </summary>
    public void ChangeTargetFrameRateUsingFPSMode(int fpsMode)
    {
        FPSMode = fpsMode;
        // 局内遵循设置面板，不看机型。
        if (TKFrameworkDelegateInterface.LoginManager_IsInGame != null && TKFrameworkDelegateInterface.LoginManager_IsInGame())
        {
            int deviceMaxFPS = HardwareCheck.GetDeviceMaximumFPS();
            switch (fpsMode)
            {
                // 如果策划表配置了限帧策略，则High FPS是限帧选项。否则，60
                case (int)EFPS.EFPS_High:
                    TargetFrameRate = Mathf.Min(deviceMaxFPS > 30 ? deviceMaxFPS : 60, 60);
                    SetMaxFPS(TargetFrameRate);
                    break;
                // 如果策划表配置的超高帧率超过90，使用90，否则使用策划表配置的帧率
                case (int)EFPS.EFPS_Ultra:
                    TargetFrameRate = Mathf.Max(Mathf.Min(90, deviceMaxFPS), 60);
                    SetMaxFPS(TargetFrameRate);
                    break;
                // 如果设备超过90，使用该帧率。如果设备支持的最高帧率不足90，则这个档位也无法看见亦无法设置。
                case (int)EFPS.EFPS_Ultra_1:
                    TargetFrameRate = Mathf.Max(90, deviceMaxFPS);
                    SetMaxFPS(TargetFrameRate);
                    break;
                // 低帧率总是30
                case (int)EFPS.EFPS_Low:
                default:
                    TargetFrameRate = 30;
                    SetMaxFPS(TargetFrameRate);
                    break;
            }
        }
        // 局外：高端机是60fps策略，中低端机总是30fps策略。无视传入的fpsMode参数。
        // 注意：局外静置超过一定时间，会自动执行降帧率策略。这部分策略写在QQGameSystem和FPSCount当中。可以前往查看。
        else
        {
            DeviceSettingPerformer ds = Services.GetService<DeviceSettingPerformer>();
            if (ds != null)
                TargetFrameRate = ds.RecommendDevicePower >= EDevicePower.EDP_High ? 60 : 30;
            else
                TargetFrameRate = 60;
            SetMaxFPS(TargetFrameRate);
        }
        UpdateOverloadScore();
    }

    /// <summary>
    /// 除了传入的参数，余下都从配置当中读取一次，避免缓存的数值是错误的。
    /// </summary>
    /// <param name="devicePower"></param>
    /// <param name="resolutionMode"></param>
    /// <param name="fpsMode"></param>
    /// <param name="isBPowerSaving"></param>
    public void ChangeLOD(EDevicePower devicePower, int resolutionMode, int fpsMode, bool isBPowerSaving)
    {
        // GM面板覆盖的版本
        if (GMOverrideConstantDevicePower != EDevicePower.EDP_None)
            devicePower = GMOverrideConstantDevicePower;
        DevicePower = devicePower;
        ScreenResolutionSystem.Instance.BPowerSaving = isBPowerSaving;

        if (TKFrameworkDelegateInterface.EffectUIClickManager_ChangeGameViewQuality != null)
            TKFrameworkDelegateInterface.EffectUIClickManager_ChangeGameViewQuality();

        GeneratePhoneLODConfig(devicePower);

        ChangeTargetFrameRateUsingFPSMode(fpsMode);

        ResetAssetCache();

        ChangeMaterialLOD(devicePower);

        ChangeGraphicsTierRuntime();

        ChangeScreenResolution(resolutionMode);

        // Enable offscreen render only in high res mode.
        // for ipad, simulator and editor only.
        // 离屏渲染在S6取得的性能优势未及预期，暂时停用
        //ChangeOffscreenRender(!isHighResolution);

        ChangeResourceVariante(devicePower);

        ChangeAnisotropicTextures(devicePower);

        ChangeBlackListDeviceShaderKeyword();

        ChangeBoneNumber();

        ChangeCustomShadowMapResolution();

        ChangeSSAO(EffectSSAO);
        ChangeHDR(IsHDREnabled);
        ChangeHDRBloom(EffectHDRBloom);
        ChangeColorGrading(EffectColorGrading);
        ChangeContrastEnhance(EffectContrastEnhance);
        ChangeSoftLight(EffectSoftlight);
        ChangeAntiAliasing(AntiAliasing);
        ChangeTKPPSDownsampleSettings(PhoneLODConfig.PostProcessDownsampleFactor);

        // VRS不列入画质选项，不放在ChangeLOD里面。如有需要请单独设置。
        //ChangeVRS(GlobalVRSMode);

        ChangeEffectLOD(devicePower);

        UpdateOverloadScore();

        DispatchLODChangeMessage(this, devicePower);

        LowIosDeviceCloseAssetCache();

        Diagnostic.Log("GameLOD.cs: ChangeLOD: " + devicePower);
    }

    private void ChangeBoneNumber() => QualitySettings.blendWeights = PhoneLODConfig.BoneNumber;

    // Set shadow map custom resolution for lights
    // Might override unity internal shadowmap resolution size policy depending on the value dispatched.
    private void ChangeCustomShadowMapResolution() => DispatchShadowCustomResolutionChangeMessage(this, PhoneLODConfig.ShadowCustomResolution);

    private void ChangeResourceVariante(EDevicePower devicePower)
    {
        if (devicePower >= EDevicePower.EDP_High)
        {

#if UNITY_IOS
                Diagnostic.Log("GameLOD.cs: IOS UnityEngine.iOS.DeviceGeneration:{0}", (int)UnityEngine.iOS.Device.generation);
                if( SystemInfo.deviceModel.Contains("iPhone9")  )
                    ResourceVariante.SetVarianteName ( "_iphone7" );
                else
                    ResourceVariante.SetVarianteName ( "_high" );
#else
            ResourceVariante.SetVarianteName("_high");
#endif

        }
        else
            ResourceVariante.SetVarianteName(string.Empty);
    }

    private void ChangeBlackListDeviceShaderKeyword()
    {
        if (s_IsBlackDevice)
            for (int i = 0; i < s_BlackDeviceExcTypeArray.Count; i++)
                if (s_BlackDeviceExcTypeArray[i] == "_NOTSUPPORT_FEATURE_ON")
                    Shader.DisableKeyword("_NOTSUPPORT_FEATURE_ON");
    }

    private void ResetAssetCache() => LoadedAssetContainer.ReduceAssetCache(PhoneLODConfig.AssetCacheNumCapacity, PhoneLODConfig.AssetCacheMemCapacity);

    /// <summary>
    /// 设置Shader的LOD级别，注意，如果不在GameLOD里面使用，请使用<see cref="ChangeShaderLOD(int)"/>代替。传入的值可以通过<see cref="PhoneLODConfig"/>的<see cref="LODBaseConfig.Global_ShaderLOD"/>获取。
    /// </summary>
    /// <param name="mode"></param>
    private void ChangeMaterialLOD(EDevicePower devicePower)
    {
        // for low memory ipad devices, set shader keywords to high instead of ultra to get rid of grabpass.
        if (HardwareCheck.IsJunkDevices && devicePower >= EDevicePower.EDP_Ultra)
            ChangeShaderLOD(LODHighConfig.HighShaderLOD);
        else
            ChangeShaderLOD(PhoneLODConfig.Global_ShaderLOD);

        // 性能考虑: 低端机不启用Low以外的Keywords
        DeviceSettingPerformer ds = Services.GetService<DeviceSettingPerformer>();
        if (ds != null && ds.RecommendDevicePower <= EDevicePower.EDP_Low)
        {
            // Equal to MaterialQualityUtilities.SetGlobalShaderKeywords(MaterialQuality.Low).
            TKFrameworkDelegateInterface.MaterialQualityUtilities_SetGlobalShaderKeywords?.Invoke(EDevicePower.EDP_Low);
            return;
        }

        switch (devicePower)
        {
            case EDevicePower.EDP_High:
                TKFrameworkDelegateInterface.MaterialQualityUtilities_SetGlobalShaderKeywords?.Invoke(EDevicePower.EDP_High);
                break;
            case EDevicePower.EDP_Middle:
                TKFrameworkDelegateInterface.MaterialQualityUtilities_SetGlobalShaderKeywords?.Invoke(EDevicePower.EDP_Middle);
                break;
            default:
                if (devicePower >= EDevicePower.EDP_Ultra)
                    TKFrameworkDelegateInterface.MaterialQualityUtilities_SetGlobalShaderKeywords?.Invoke(EDevicePower.EDP_Ultra);
                else // low or below low
                    TKFrameworkDelegateInterface.MaterialQualityUtilities_SetGlobalShaderKeywords?.Invoke(EDevicePower.EDP_Low);
                break;
        }
    }
    /// <summary>
    /// If run in an independent scene (such as 攻击特效制作专用), will retun true for artist to test best results.
    /// </summary>
    /// <returns></returns>
    public static bool ExecuteInEditorAndIndependentScene()
    {
        if (Application.isEditor
            // If executed normal launching game setup, this cannot be null.
            && DeviceSettingPerformer.Instance == null)
            return true;
        return false;
    }

    public int ChangeShaderLOD(int lod)
    {
        int targetLod;
        // 避免没有开启任何一个后处理效果的时候，grab pass shader黑色
        if (lod > MaximumLODWithoutGrabPass && NoTKPPSEffectApplied()
            // 避免一些美术制作特效专用的场景也给限制住了，否则美术无法测试形如GrabPass之类的特殊效果。对正常加载的局内流程无效。
            && !ExecuteInEditorAndIndependentScene())
            targetLod = MaximumLODWithoutGrabPass;
        else
            targetLod = lod;

        if (Shader.globalMaximumLOD == targetLod)
            return targetLod;

        Shader.globalMaximumLOD = targetLod;
        Diagnostic.Log($"GameLOD.cs: ChangeShaderLOD: {targetLod}");
        //#if !JK_RELEASE
        //        // try to output its invoke stacktrace to find out why this method is called so many times.
        //        Diagnostic.Verbose("Invoke stacktrace" + System.Environment.StackTrace);
        //#endif
        return targetLod;
    }

    private bool NoTKPPSEffectApplied()
    {
        return TKPostProcessingStack.FeatureEnableMark == false
            || (EffectHDRBloom == false && EffectColorGrading == false && EffectSSAO == false && EffectSoftlight == false && FXAA == false && EffectContrastEnhance == false && Outline == false);
    }

    /// <summary>
    /// 更改屏幕分辨率
    /// </summary>
    /// <param name="mode"></param>
    public void ChangeScreenResolution(int resMode)
    {
        ResolutionMode = resMode;
        var srs = ScreenResolutionSystem.Instance;
        if (srs != null)
        {
            srs.SetScreenResolution(ResolutionMode);
            UpdateOverloadScore();
            TKFrameworkDelegateInterface.EffectUIClickManager_ChangeGameResolutionView?.Invoke(srs.scaleWidth / 2.0f, srs.scaleHeight / 2.0f);
            TKFrameworkDelegateInterface.LobbySceneCameraOperator_ChangeGameResolutionView?.Invoke(srs.scaleWidth / 2.0f, srs.scaleHeight / 2.0f);
            DispatchResolutionChangeMessage(this, srs.scaleWidth, srs.scaleHeight, resMode);
            Diagnostic.Log("GameLOD.cs: ChangeScreenResolution: " + resMode);
        }
    }

    /// <summary>
    /// 开关HDR Bloom
    /// </summary>
    /// <param name="enableHDRBloom"></param>
    public void ChangeHDRBloom(bool enableHDRBloomin)
    {
        var enableHDRBloom = enableHDRBloomin;
        if (!IsHDREnabled)
        {
            enableHDRBloom = false;
            Diagnostic.Log("GameLOD.cs: ChangeHDRBloom: HDRBloom won't enable because hdr is not enabled");
        }
        EffectHDRBloom = enableHDRBloom;
        DispatchHDRBloomChangeMessage(this, enableHDRBloom);
        Diagnostic.Log("GameLOD.cs: ChangeHDRBloom: " + enableHDRBloom);
        UpdateOverloadScore();
    }

    /// <summary>
    /// 将HDR的开关单列出来。不在和hdrbloom一同切换
    /// 设定当中，不允许在非hdr的情况下，开启hdrbloom和colorgrading效果，否则效果不正确。
    /// </summary>
    /// <param name="enableHDR"></param>
    public void ChangeHDR(bool enableHDR)
    {
        IsHDREnabled = enableHDR;
        DispatchHDRChangeMessage(this, enableHDR);
        Diagnostic.Log("GameLOD.cs: ChangeHDR:" + enableHDR);
        UpdateOverloadScore();
    }

    public void ChangeMSAASettings(TKPostProcessingStack.MultiSampleAntiAliasing multiSampleAntiAliasing)
    {
        MSAA = multiSampleAntiAliasing;
        DispatchMSAAChangeMessage(this, multiSampleAntiAliasing);
        Diagnostic.Log("GameLOD.cs: ChangeMSAA: " + multiSampleAntiAliasing);
        UpdateOverloadScore();
    }

    public void ChangeSMAASettings(bool enableSMAA)
    {
        SMAA = enableSMAA;
        DispatchSMAAChangeMessage(this, enableSMAA);
        Diagnostic.Log("GameLOD.cs: ChangeSMAA: " + enableSMAA);
        UpdateOverloadScore();
    }

    public void ChangeFXAASettings(bool enableFXAA)
    {
        FXAA = enableFXAA;
        DispatchFXAAChangeMessage(this, enableFXAA);
        Diagnostic.Log("GameLOD.cs: ChangeFXAA: " + enableFXAA);
        UpdateOverloadScore();
    }

    /// <summary>
    /// 开关角色描边
    /// </summary>
    /// <param name="enableOutline"></param>
    public void ChangeOutline(bool enableOutline)
    {
        Outline = enableOutline;
        if (TKFrameworkDelegateInterface.LoginManager_IsInGame == null || !TKFrameworkDelegateInterface.LoginManager_IsInGame())
        {
            Diagnostic.Log("GameLOD.cs: ChangeOutline: Outline won't enable because it does not take effect outgame");
            Outline = false;
        }
        DispatchOutlineChangeMessage(this, Outline);
        Diagnostic.Log("GameLOD.cs: ChangeOutline: " + Outline.ToString());
        UpdateOverloadScore();
    }

    /// <summary>
    /// <br>开关抗锯齿，注意极高画质下的局内，使用MSAA代替FXAA，若如此做，会关闭FXAA</br>
    /// <br>FXAA和MSAA是互斥的</br>
    /// </summary>
    /// <param name="enableAntiAliasing"></param>
    public void ChangeAntiAliasing(bool enableAntiAliasing)
    {
        // Reset anti aliasing first because anti aliasing type might change (from msaa to fxaa and vice versa).
        if (enableAntiAliasing)
            ChangeAntiAliasing(false);
        AntiAliasing = enableAntiAliasing;

        if (enableAntiAliasing)
        {
            // 针对局内至臻设备极高提供smaa
            //if (TKFrameworkDelegateInterface.LoginManager_IsInGame()
            //    && DevicePower >= EDevicePower.EDP_ExtremeV2)
            //    ChangeSMAASettings(true);
            //else
                ChangeFXAASettings(true);
        }
        else
        {
            ChangeFXAASettings(false);
            ChangeSMAASettings(false);
        }
        Diagnostic.Log("GameLOD.cs: ChangeAntiAliasing: " + enableAntiAliasing);
        UpdateOverloadScore();
    }

#if !OUTSOURCE
    public delegate void SwitchVRSDelegate(VariableRateShadingMode vrsMode);
    public SwitchVRSDelegate SwitchVRSDelegateMethod;
    public VariableRateShadingMode GlobalVRSMode = VariableRateShadingMode.kVRSModeDefault;

    public void ChangeVRS(VariableRateShadingMode variableRateShadingMode)
    {
        SwitchVRSDelegateMethod?.Invoke(variableRateShadingMode);
        GlobalVRSMode = variableRateShadingMode;
        UpdateOverloadScore();
    }
#endif

    public void ChangeSoftLight(bool enableSoftLight)
    {
        EffectSoftlight = enableSoftLight;
        if (TKFrameworkDelegateInterface.LoginManager_IsInGame == null || !TKFrameworkDelegateInterface.LoginManager_IsInGame())
        {
            Diagnostic.Log("GameLOD.cs: ChangeSoftLight: SoftLight won't enable because it does not take effect outgame");
            EffectSoftlight = false;
        }
        DispatchSoftlightMessage(this, EffectSoftlight);
        Diagnostic.Log("GameLOD.cs: ChangeSoftlight:" + EffectSoftlight.ToString());
        UpdateOverloadScore();
    }

    public void ChangeContrastEnhance(bool enableContrastEnhance)
    {
        EffectContrastEnhance = enableContrastEnhance;
        if (TKFrameworkDelegateInterface.LoginManager_IsInGame == null || !TKFrameworkDelegateInterface.LoginManager_IsInGame())
        {
            EffectContrastEnhance = false;
            Diagnostic.Log("GameLOD.cs: ChangeContrastEnhance: ContrastEnhance won't enable because it does not take effect outgame");
        }
        DispatchContrastEnhanceMessage(this, EffectContrastEnhance);
        Diagnostic.Log("GameLOD.cs: ChangeContrastEnhance:" + EffectContrastEnhance.ToString());
        UpdateOverloadScore();
    }

    public void ChangeSSAO(bool enableSSAO)
    {
        EffectSSAO = enableSSAO;
        if (TKFrameworkDelegateInterface.LoginManager_IsInGame == null || !TKFrameworkDelegateInterface.LoginManager_IsInGame())
        {
            EffectSSAO = false;
            Diagnostic.Log("GameLOD.cs: ChangeSSAO: SSAO won't enable because it does not take effect outgame");
        }
        DispatchSSAOMessage(this, EffectSSAO);
        Diagnostic.Log("GameLOD.cs: ChangeSSAO:" + EffectSSAO.ToString());
        ChangeAntiAliasing(AntiAliasing);
        UpdateOverloadScore();
    }

    /// <summary>
    /// 未开启HDR的情况下不允许开启ColorGrading
    /// </summary>
    /// <param name="enableColorGradingin"></param>
    public void ChangeColorGrading(bool enableColorGradingin)
    {
        var enableColorGrading = enableColorGradingin;
        if (!IsHDREnabled)
        {
            enableColorGrading = false;
            Diagnostic.Log("GameLOD.cs: ChangeColorGrading: Color grading won't enable because hdr is disabled");
        }
        //else if (!TKFrameworkDelegateInterface.LoginManager_IsInGame())
        //{
        //    Diagnostic.Log("ChangeColorGrading: Color grading won't enable because it does not take effect outgame.");
        //    enableColorGrading = false;
        //}
        EffectColorGrading = enableColorGrading;
        DispatchColorGradingMessage(this, enableColorGrading);
        Diagnostic.Log("GameLOD.cs: ChangeColorGrading:" + enableColorGrading);
        UpdateOverloadScore();
    }

    public void ChangeOffscreenRender(bool useOffscreenRender)
    {
        // high tier pad, simulator or editor only.
        var ds = Services.GetService<DeviceSettingPerformer>();
        //if (true // (/*TKFrameworkDelegateInterface.NotchSizeImp_IsPad() && */ds != null && ds.RecommendDevicePower >= EDevicePower.EDP_High)
        //    || Application.isEditor
        //    || TKFrameworkDelegateInterface.IsSimulator())
        {
            UseOffscreenRender = useOffscreenRender;
            DispatchUseOffscreenRenderingTechMesshage(this, useOffscreenRender);

            var allCameras = Camera.allCameras;
            for (int i = 0; i < allCameras.Length; ++i)
                TKFrameworkDelegateInterface.GfxOffScreenRenderCamera_OffscreenComponent_Enable?.Invoke(allCameras[i], useOffscreenRender);
        }
        UpdateOverloadScore();
    }

    /// <summary>
    /// 根据高中低配进行各项异性设置
    /// </summary>
    /// <param name="mode"></param>
    private void ChangeAnisotropicTextures(EDevicePower devicePower)
    {
        //http://tapd.oa.com/cchessTest/bugtrace/bugs/view/1120428482081505686
        //强制关闭各向异性过滤，避免渲染错误。
        QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
    }

    public void ChangeTKPPSDownsampleSettings(float downSampleRate)
        => DispatchTKPPSDownSampleMessage(this, downSampleRate);

    private void DispatchLODChangeMessage(object obj, EDevicePower devicePower)
    {
        // Ultra or above require outline effect, require depth some time to time.
        TKPostProcessingStack.DepthEnableMark = DevicePower > EDevicePower.EDP_High;
        _LODeventRaise?.Invoke(this, new EventLODEventArgs(devicePower));
    }

    private void DispatchFPSChangeMessage(object obj, int fps) => _FPSEventRaise?.Invoke(this, new EventFPSEventArgs(fps));

    private void DispatchEffectLODChangeMessage(object obj, EDevicePower devicePower) => _EffectLODeventRaise?.Invoke(this, new EventLODEventArgs(devicePower));

    private void DispatchResolutionChangeMessage(object obj, int screenWidth, int screenHeight, int highResolution) => _ResolutionRaise?.Invoke(this, new EventResolutionEventArgs(screenWidth, screenHeight, highResolution));

    public void DispatchDecreaseGraphicsMessage(object obj, EventDecreaseGraphicsArgs args) => _eventDecreasesGraphicsRaise?.Invoke(this, args);

    private void DispatchMSAAChangeMessage(object obj, TKPostProcessingStack.MultiSampleAntiAliasing multiSampleAntiAliasing) => _msaaRaise?.Invoke(this, new EventMSAAEventArgs(multiSampleAntiAliasing));

    private void DispatchSMAAChangeMessage(object obj, bool smaaEnable) => _smaaRaise?.Invoke(this, new EventSMAAEventArgs(smaaEnable));

    private void DispatchFXAAChangeMessage(object obj, bool fxaaEnable) => _fxaaRaise?.Invoke(this, new EventFXAAEventArgs(fxaaEnable));

    private void DispatchTKPPSDownSampleMessage(object obj, float targetSampleRate) => _tkppsDownSampleRaise?.Invoke(this, new EventTKPPSDownSampleEventArgs(targetSampleRate));

    private void DispatchOutlineChangeMessage(object obj, bool enableOutline) => _OutlineRaise?.Invoke(this, new EventOutlineEventArgs(enableOutline));

    private void DispatchHDRBloomChangeMessage(object obj, bool enableHDRBloom) => _HDRBloomRaise?.Invoke(this, new EventHDRBloomEventArgs(enableHDRBloom));

    private void DispatchHDRChangeMessage(object _, bool enableHDR) => _HDRRaise?.Invoke(this, new EventHDREventArgs(enableHDR));

    private void DispatchContrastEnhanceMessage(object obj, bool enableContrastEnhance) => _ContrastEnhanceRaise?.Invoke(this, new EventContrastEnhanceEventArgs(enableContrastEnhance));

    private void DispatchSSAOMessage(object obj, bool enablessao) => _SSAORaise?.Invoke(this, new EventSSAOEventArgs(enablessao, DevicePower >= EDevicePower.EDP_ExtremeV2));

    private void DispatchSoftlightMessage(object obj, bool enableSoftlight) => _SoftlightRaise?.Invoke(this, new EventSoftlightEventArgs(enableSoftlight));

    private void DispatchColorGradingMessage(object obj, bool enableColorGrading) => _ColorGradingRaise?.Invoke(this, new EventColorGradingEventArgs(enableColorGrading));

    public void DispatchGlitchRGBSplit(object obj, bool enableGlitchRGBSplit) => _GlitchRGBSplitRaise?.Invoke(this, new EventGlitchRGBSplitEventArgs(enableGlitchRGBSplit));

    private void DispatchShadowCustomResolutionChangeMessage(object obj, int shadowCustomResolution) => _ShadowCustomResolutionRaise?.Invoke(this, new EventShadowCustomResolutionEventArgs(shadowCustomResolution));

    private void DispatchUseOffscreenRenderingTechMesshage(object obj, bool isUseOffscreenRenderingTech) => _UseOffscreenRaise?.Invoke(this, new EventUseOffscreenEventArgs(isUseOffscreenRenderingTech));

    private void DispatchOverloadChangeMessage(object obj, int overloadScore) => _OverloadRaise?.Invoke(this, new EventOverloadEventArgs(overloadScore));

    private void LowIosDeviceCloseAssetCache()
    {
#if UNITY_IOS
        DeviceSettingPerformer gs = Services.GetService<DeviceSettingPerformer>();
        EDevicePower recommendDisplayMode = gs.RecommendDevicePower;
        //iOS低端机关闭缓存
        if (recommendDisplayMode == EDevicePower.EDP_Low)
        {
            ObjectCache.Cacheable = false;
        }
#endif
    }
}
