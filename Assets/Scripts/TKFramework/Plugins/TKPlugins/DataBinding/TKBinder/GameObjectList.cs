using System;
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;

namespace TKPlugins
{
    /// <summary>
    /// 编辑器下编辑存储用来在 运行时操作的UI 元素
    /// </summary>
    /// 
    [AddComponentMenu("TKPlugins/Databinding/GameObjectList")]
    public class GameObjectList : MonoBehaviour
    {
        /// <summary>
        /// for ui editor. save editor binder.
        /// </summary>
        public List<GameObject> GameObjectLst;
        public List<string> scriptPaths;

        private int m_scriptPathCount = -1;
        public int GetScriptPathCount()
        {
            if (m_scriptPathCount == -1)
            {
                m_scriptPathCount = scriptPaths.Count;
#if !UNITY_EDITOR
                scriptPaths.Clear();        // 优化一下string内存
#endif
            }
            return m_scriptPathCount;
        }

        public GameObject Find(string name)
        {
            foreach(var item in GameObjectLst)
            {
                if (item && item.name == name)
                    return item;
            }
            return null;
        }

        public T Find<T>(string name) where T: Component
        {
            foreach (var item in GameObjectLst)
            {
                if (item && item.name == name)
                {
                    return item.GetComponent<T>();
                }
            }
            return null;
        }

        public ArrayList ToArrayList()
        {
            ArrayList arrayList = new ArrayList();

            for (int i = 0; i < GameObjectLst.Count; i++)
            {
                arrayList.Add(GameObjectLst[i]);
            }

            return arrayList;
        }

        public void SetComponent<T>(string name, Action<T> action) where T : MonoBehaviour
        {
            GameObject go = Find(name);
            if (go != null)
            {
                var component = go.GetComponent<T>();
                if (component != null)
                {
                    if (action != null)
                        action(component);
                }
                else
                {
                    Diagnostic.Warn("[ChessShareFactory.SetComponent]Find {0} Component<{1}> in golist faild!", name, typeof(T).FullName);
                }
            }
            else
            {
                Diagnostic.Warn("[ChessShareFactory.SetComponent]Find {0} in golist faild!", name, typeof(T).FullName);
            }
        }
    }
}
