using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TKPlugins
{
    /// <summary>
    /// UIText to String
    /// </summary>
    [RequireComponent(typeof(Text))]
    [AddComponentMenu("TKPlugins/Databinding/TKTextBinder")]
    public class TKTextBinder : TKModelBinder
    {
        
        protected Text Label;
        protected Font _RawFont;
        [HideInInspector]
        public BindingInfo LabelBinding = new BindingInfo { BindingName = "Label" };
        [HideInInspector]
        public BindingInfo ColorBinding = new BindingInfo { BindingName = "Color" };
        [HideInInspector]
        public BindingInfo FontBinding = new BindingInfo { BindingName = "Font", Filters = BindingFilter.Properties };

        public string FormatString = string.Empty;

        protected bool IsInit;

        public override void Awake()
        {
            Init();
        }

        public override void Init()
        {
            if (IsInit)
                return;

            IsInit = true;

            Label = GetComponentInChildren<Text>();

            if (Label == null)
                Label = GetComponent<Text>();

            if (Label == null)
                Debug.Log("Text Not Found", gameObject);
            _RawFont = Label.font;
            LabelBinding.Action = UpdateLabel;
            LabelBinding.Filters = BindingFilter.Properties;
           // LabelBinding.FilterTypes = new[] { typeof(string) };

            if ( !string.IsNullOrEmpty( ColorBinding.MemberName))
            {
                ColorBinding.Action = UpdateColor;
                ColorBinding.Filters = BindingFilter.Properties;
                ColorBinding.FilterTypes = new[] { typeof(Color) };
            }

            if (!string.IsNullOrEmpty(FontBinding.MemberName))
            {
                FontBinding.Action = UpdateFont;
                FontBinding.Filters = BindingFilter.Properties;
                FontBinding.FilterTypes = new[] { typeof(Font) };
            }

        }


        private void UpdateLabel(object arg)
        {
            var s = arg == null ? string.Empty : arg.ToString();

            if (Label)
            {
                if (string.IsNullOrEmpty(FormatString))
                {
                    if (DebugMode)
                        Debug.Log(FormatString);
                    Label.text = s;
                }
                else
                {
                    Label.text = string.Format(FormatString, arg);
                }
            }
        }

        private void UpdateColor(object arg)
        {
            if (Label)
            {
                Label.color = ((Color)arg);
            }
        }

        private void UpdateFont(object arg)
        {
            if (Label)
            {
                Font newFont = arg as Font;

                if (newFont != null)
                {
                    Label.font = newFont;
                    Label.material = newFont.material;
                }
                else
                {
                    Label.font = _RawFont;
                    Label.material = _RawFont.material;
                }


            }
        }

        protected override void   RegisterBindingInfos()
        {
            bindingLst.Clear();
            bindingLst.Add(LabelBinding);
            bindingLst.Add(ColorBinding);
            bindingLst.Add(FontBinding);

            _infoCache = bindingLst.ToArray();
        }
    }
}
