using UnityEngine;
using System.Collections;
using UnityEngine.UI;
using System;
using UnityEngine.EventSystems;
public class CircleScrollRect : ScrollRect
{
    public bool isInverse = false;

    private Action<float> scrollAction;
    private Canvas m_canvas;
    private Transform m_targetTran;
    private float m_recordRotateAngle;
    private float m_limitAngle = 0;
    private float m_singleLimitAngle = 0;
    private int m_SumCount = 0;
    private bool m_clockWise = false;

    private float m_recordScrollValue = 0.0f;
    private readonly float m_rightScrollValue = 10.5f;

    public bool IsUseConstValue = false;
    /// <summary>
    /// 忽略滑动触发
    /// </summary>
    private bool m_ignoreDragTrigger = false;

    protected override void Awake()
    {

    }

    public void ResetRuntimeData()
    {
        m_recordRotateAngle = m_recordRotateAngle = 0;
    }

    public void ResetRotate()
    {
        if (m_targetTran == null)
            transform.rotation = Quaternion.identity;
        else
            m_targetTran.rotation = Quaternion.identity;
    }

    public void SetIgnoreDragTrigger()
    {
        m_ignoreDragTrigger = true;
    }

    public void SetRotateTransform(Transform targetTran)
    {
        m_targetTran = targetTran;
    }

    public void SetCurCanvas(Canvas currentCanvas)
    {
        m_canvas = currentCanvas;
    }

    public void SetScorllAction(Action<float> scrollAction)
    {
        this.scrollAction = scrollAction;
    }

    public void SetLimitInfo(float limitSingleAngle, int sumCount)
    {
        m_limitAngle = limitSingleAngle * sumCount;
        m_singleLimitAngle = limitSingleAngle;
        m_SumCount = sumCount;
    }

    public override void OnBeginDrag(PointerEventData eventData)
    {
        base.OnBeginDrag(eventData);
        if (m_ignoreDragTrigger) return;
        m_recordScrollValue = 0;
    }

    public override void OnDrag(UnityEngine.EventSystems.PointerEventData eventData)
    {
        base.OnDrag(eventData);
        if (m_ignoreDragTrigger) return;
        if (m_canvas == null) return;

        Vector2 mousexy = eventData.delta;
        mousexy *= decelerationRate;

        Vector3? curscreenpos = CalculateWorldtoScreenpos(transform.position);
        if (curscreenpos == null) return;
        Vector2 offset = eventData.position - (Vector2)curscreenpos.Value;

        float value;
        if (Mathf.Abs(mousexy.x) > Mathf.Abs(mousexy.y))
        {
            value = mousexy.x * Mathf.Sign(-offset.y);
        }
        else
        {
            value = mousexy.y * Mathf.Sign(offset.x);
        }

        m_recordScrollValue += Math.Abs(value);
        if (m_recordScrollValue < m_rightScrollValue)
        {
            return;
        }
        else
        {
            m_recordScrollValue = 0;
        }

        if (IsUseConstValue)
        {
            if (value > 0)
            {
                value = m_singleLimitAngle;
            }
            else
            {
                value = -m_singleLimitAngle;
            }
        }

        if (!isInverse)
            value = 0 - value;

        m_recordRotateAngle += value;

        if (m_limitAngle != 0)
        {
            if (m_clockWise)
            {
                if (Mathf.Abs(m_recordRotateAngle) >= Math.Abs(m_limitAngle + -7.629395E-06) || m_recordRotateAngle > -7.629395E-06)
                {
                    m_recordRotateAngle -= value;
                    return;
                }
            }
            else
            {
                if (m_recordRotateAngle >= (m_limitAngle - -7.629395E-06) || m_recordRotateAngle < -7.629395E-06)
                {
                    m_recordRotateAngle -= value;
                    return;
                }
            }
        }

        if (m_targetTran == null)
            transform.Rotate(Vector3.forward, value, Space.Self);
        else
            m_targetTran.Rotate(Vector3.forward, value, Space.Self);

        scrollAction?.Invoke(CalculateCurSelect());
    }

    public override void OnEndDrag(UnityEngine.EventSystems.PointerEventData eventData)
    {
        base.OnEndDrag(eventData);
        if (m_ignoreDragTrigger) return;
        m_recordScrollValue = 0;
        scrollAction?.Invoke(CalculateCurSelect());
    }

    private Vector3? CalculateWorldtoScreenpos(Vector3 worldpos)
    {
        if (m_canvas == null)
            return null;

        if (m_canvas.renderMode == RenderMode.ScreenSpaceCamera)
        {
            return m_canvas.worldCamera.WorldToScreenPoint(worldpos);
        }
        else if (m_canvas.renderMode == RenderMode.ScreenSpaceOverlay)
        {
            Vector3 screenpos = m_canvas.transform.InverseTransformPoint(worldpos);
            var recttrans = m_canvas.transform as RectTransform;
            screenpos.x += recttrans.rect.width * 0.5f * recttrans.localScale.x;
            screenpos.y += recttrans.rect.height * 0.5f * recttrans.localScale.y;
            return screenpos;
        }

        return null;
    }

    private float CalculateCurSelect()
    {
        return m_recordRotateAngle;
    }

    float recordLastValue = 0;
    float realNeedScrollValue = 0;
    public void OnExternalSetScrollViewAngle(float angleValue)
    {
        recordLastValue = m_recordRotateAngle;
        realNeedScrollValue = angleValue - m_recordRotateAngle;
        m_recordRotateAngle = angleValue;

        if (m_limitAngle != 0)
        {
            if (m_clockWise)
            {
                if (Mathf.Abs(m_recordRotateAngle) >= Math.Abs(m_limitAngle + -7.629395E-06) || m_recordRotateAngle > -7.629395E-06)
                {
                    m_recordRotateAngle = recordLastValue;
                    return;
                }
            }
            else
            {
                if (m_recordRotateAngle >= (m_limitAngle - -7.629395E-06) || m_recordRotateAngle < -7.629395E-06)
                {
                    m_recordRotateAngle = recordLastValue;
                    return;
                }
            }
        }

        if (m_targetTran == null)
            transform.Rotate(Vector3.forward, realNeedScrollValue, Space.Self);
        else
            m_targetTran.Rotate(Vector3.forward, realNeedScrollValue, Space.Self);

        scrollAction?.Invoke(CalculateCurSelect());
    }
}
