using System;
using UnityEngine;
using UnityEngine.UI;

//---------------------------------------------------------
//不显示图片，只提供输入事件响应，一般与Button联合使用
//用于替换把Image的Color.Alpha设置为0的方式
//---------------------------------------------------------
[AddComponentMenu("UI/Click Event Area")]
public class ClickEventArea : MaskableGraphic
{
    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();
    }

    protected override void UpdateGeometry()
    {

    }

    public override void Rebuild(CanvasUpdate update)
    {
        return;
    }
}

