using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

/**
 * 水平布局组件，可避免Layout Rebuild
 */
[ExecuteInEditMode]
public class TKHorizontalLayout : TKLayoutGroup
{
    // Update is called once per frame
    void Update()
    {
        curActiveChilds = 0;
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            if (childRectTrans[i].gameObject.activeSelf)
            {
                ++curActiveChilds;
            }
            if (childRectTrans[i].hasChanged)
            {
                SetLayout();
                childRectTrans[i].hasChanged = false;
                break;
            }

        }

        if (curActiveChilds != lastActiveChilds)
        {
            SetLayout();
            lastActiveChilds = curActiveChilds;
        }
    }

    public override void SetLayout()
    {
        switch (childAlignment)
        {
            case ChildAlignment.UpperLeft:
                AlignUpperLeft();
                break;
            case ChildAlignment.UpperCenter:
                AlignUpperCenter();
                break;
            case ChildAlignment.UpperRight:
                AlignUpperRight();
                break;
            case ChildAlignment.MiddleLeft:
                AlignMiddleLeft();
                break;
            case ChildAlignment.MiddleCenter:
                AlignMiddleCenter();
                break;
            case ChildAlignment.MiddleRight:
                AlignMiddleRight();
                break;
            case ChildAlignment.LowerLeft:
                AlignLowerLeft();
                break;
            case ChildAlignment.LowerCenter:
                AlignLowerCenter();
                break;
            case ChildAlignment.LowerRight:
                AlignLowerRight();
                break;
            default:
                AlignMiddleLeft();
                break;
        }

        if (parentLayout != null)
        {
            parentLayout.SetLayout();
        }
    }

    private bool IsIgnoreLayout(Transform targetTransform)
    {
        bool ignoreLayout = false;
        for (int i = 0; i < ignoreLayoutChilds.Count; ++i)
        {
            if (ignoreLayoutChilds[i] == targetTransform)
            {
                ignoreLayout = true;
                break;
            }
        }

        return ignoreLayout;
    }

    /**
     * 计算父节点尺寸
     */
    private Vector2 GetParentSize()
    {
        // 子物体transform
        RectTransform child = null;
        // 父节点的总宽度(有效子节点总宽度 + Left Padding + Right Padding + (有效子节点数量 - 1) * Spacing)
        float parentWidth = 0;
        // 父节点的总高度(有效子节点最大高度 + Top Padding + Bottom Padding)
        float parentHeight = 0;

        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }

            Vector2 childSize = child.sizeDelta;
            parentWidth += (childSize.x * child.localScale.x + spacing);
            parentHeight = Mathf.Max(parentHeight, childSize.y);
        }

        parentWidth += (leftPadding + rightPadding - spacing);
        parentHeight += (topPadding + bottomPadding);

        return new Vector2(parentWidth, parentHeight);
    }

    private void ResizeParent(Vector2 parentSize)
    {
        bool needResize = false;

        Vector2 oldParentSize = rectTrans.sizeDelta;
        if (adjustParentWidth && Mathf.Abs(oldParentSize.x - parentSize.x) > 0.1 ||
            adjustParentHeight && Mathf.Abs(oldParentSize.y - parentSize.y) > 0.1)
        {
            needResize = true;
        }

        if (!adjustParentWidth)
        {
            parentSize.x = oldParentSize.x;
        }

        if (!adjustParentHeight)
        {
            parentSize.y = oldParentSize.y;
        }

        // 尽量避免频繁修改sizeDelta的Rebuild
        if (needResize)
        {
            rectTrans.sizeDelta = parentSize;
        }
    }

    private void AlignUpperLeft()
    {
        // 子节点transform
        RectTransform child = null;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        ResizeParent(GetParentSize());

        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        Vector2 parentSize = rectTrans.sizeDelta;
        // 计算子节点的最终锚点位置
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = -child.anchorMin.x * parentSize.x;
            yOffset = (1 - child.anchorMin.y) * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y * child.localScale.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset + leftPadding + travedChildsWidth + curWidth * child.pivot.x + (validChildCount - 1) * spacing;
            targetAnchorPos.y = yOffset - topPadding - curHeight * (1 - child.pivot.y);
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignUpperCenter()
    {
        // 子节点transform
        RectTransform child = null;
        // 子节点总宽度
        float totalWidth = 0;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        Vector2 parentSize = GetParentSize();
        ResizeParent(parentSize);
        totalWidth = parentSize.x;

        // 居中对齐无视左右Padding
        totalWidth -= (leftPadding + rightPadding);

        parentSize = rectTrans.sizeDelta;
        // 居中对齐相对于左对齐的整体偏移
        float totalOffset = (parentSize.x - totalWidth) * 0.5f;
        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        // 计算子节点的最终锚点位置
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = -child.anchorMin.x * parentSize.x;
            yOffset = (1 - child.anchorMin.y) * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset + travedChildsWidth + curWidth * child.pivot.x + (validChildCount - 1) * spacing + totalOffset;
            targetAnchorPos.y = yOffset - topPadding - curHeight * (1 - child.pivot.y);
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignUpperRight()
    {
        // 子节点transform
        RectTransform child = null;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        ResizeParent(GetParentSize());

        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        Vector2 parentSize = rectTrans.sizeDelta;
        // 计算子节点的最终锚点位置
        for (int i = childRectTrans.Count - 1; i >= 0; --i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = (1 - child.anchorMin.x) * parentSize.x;
            yOffset = (1 - child.anchorMin.y) * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset - rightPadding - travedChildsWidth + curWidth * (child.pivot.x - 1) - (rectTrans.childCount - validChildCount) * spacing;
            targetAnchorPos.y = yOffset - topPadding - curHeight * (1 - child.pivot.y);
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignMiddleLeft()
    {
        // 子节点transform
        RectTransform child = null;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        ResizeParent(GetParentSize());

        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        Vector2 parentSize = rectTrans.sizeDelta;
        // 计算子节点的最终锚点位置
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = -child.anchorMin.x * parentSize.x;
            yOffset = (1 - child.anchorMin.y) * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset + leftPadding + travedChildsWidth + curWidth * child.pivot.x + (validChildCount - 1) * spacing;
            targetAnchorPos.y = yOffset - curHeight * (1 - child.pivot.y) - (rectTrans.sizeDelta.y - child.sizeDelta.y) * 0.5f;
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignMiddleCenter()
    {
        // 子节点transform
        RectTransform child = null;
        // 子节点总宽度
        float totalWidth = 0;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        Vector2 parentSize = GetParentSize();
        ResizeParent(parentSize);
        totalWidth = parentSize.x;

        // 居中对齐无视左右Padding
        totalWidth -= (leftPadding + rightPadding);

        parentSize = rectTrans.sizeDelta;
        // 居中对齐相对于左对齐的整体偏移
        float totalOffset = (parentSize.x - totalWidth) * 0.5f;
        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        // 计算子节点的最终锚点位置
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = -child.anchorMin.x * parentSize.x;
            yOffset = (1 - child.anchorMin.y) * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset + travedChildsWidth + curWidth * child.pivot.x + (validChildCount - 1) * spacing + totalOffset;
            targetAnchorPos.y = yOffset - curHeight * (1 - child.pivot.y) - (parentSize.y - childSize.y) * 0.5f;
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignMiddleRight()
    {
        // 子节点transform
        RectTransform child = null;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        ResizeParent(GetParentSize());

        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        Vector2 parentSize = rectTrans.sizeDelta;
        // 计算子节点的最终锚点位置
        for (int i = childRectTrans.Count - 1; i >= 0; --i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = (1 - child.anchorMin.x) * parentSize.x;
            yOffset = (1 - child.anchorMin.y) * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset - rightPadding - travedChildsWidth + curWidth * (child.pivot.x - 1) - (rectTrans.childCount - validChildCount) * spacing;
            targetAnchorPos.y = yOffset - curHeight * (1 - child.pivot.y) - (parentSize.y - childSize.y) * 0.5f;
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignLowerLeft()
    {
        // 子节点transform
        RectTransform child = null;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        ResizeParent(GetParentSize());

        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        Vector2 parentSize = rectTrans.sizeDelta;
        // 计算子节点的最终锚点位置
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = -child.anchorMin.x * parentSize.x;
            yOffset = -child.anchorMin.y * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset + leftPadding + travedChildsWidth + curWidth * child.pivot.x + (validChildCount - 1) * spacing;
            targetAnchorPos.y = yOffset + bottomPadding + curHeight * child.pivot.y;
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignLowerCenter()
    {
        // 子节点transform
        RectTransform child = null;
        // 子节点总宽度
        float totalWidth = 0;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        Vector2 parentSize = GetParentSize();
        ResizeParent(parentSize);
        totalWidth = parentSize.x;

        // 居中对齐无视左右Padding
        totalWidth -= (leftPadding + rightPadding);

        parentSize = rectTrans.sizeDelta;
        // 居中对齐相对于左对齐的整体偏移
        float totalOffset = (parentSize.x - totalWidth) * 0.5f;
        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        // 计算子节点的最终锚点位置
        for (int i = 0; i < childRectTrans.Count; ++i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = -child.anchorMin.x * parentSize.x;
            yOffset = -child.anchorMin.y * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset + travedChildsWidth + curWidth * child.pivot.x + (validChildCount - 1) * spacing + totalOffset;
            targetAnchorPos.y = yOffset + bottomPadding + curHeight * child.pivot.y;
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }

    private void AlignLowerRight()
    {
        // 子节点transform
        RectTransform child = null;
        // 已遍历子节点的总宽度
        float travedChildsWidth = 0;
        // 根据子节点锚点计算的x轴偏移
        float xOffset = 0;
        // 根据子节点锚点计算的y轴偏移
        float yOffset = 0;
        Vector2 targetAnchorPos = Vector2.zero;

        // 如果父节点尺寸需要自适应的话，先计算父节点最终尺寸
        ResizeParent(GetParentSize());

        // 当前子节点宽度(计算缩放)
        float curWidth = 0f;
        // 当前子节点高度(不需计算缩放)
        float curHeight = 0f;
        // 有效子节点数量
        int validChildCount = 0;
        Vector2 parentSize = rectTrans.sizeDelta;
        // 计算子节点的最终锚点位置
        for (int i = childRectTrans.Count - 1; i >= 0; --i)
        {
            child = childRectTrans[i];
            if (child == null || !child.gameObject.activeSelf || IsIgnoreLayout(child))
            {
                continue;
            }
            ++validChildCount;
            Vector2 childSize = child.sizeDelta;
            xOffset = (1 - child.anchorMin.x) * parentSize.x;
            yOffset = -child.anchorMin.y * parentSize.y;
            curWidth = childSize.x * child.localScale.x;
            curHeight = childSize.y;

            targetAnchorPos = child.anchoredPosition;
            targetAnchorPos.x = xOffset - rightPadding - travedChildsWidth + curWidth * (child.pivot.x - 1) - (rectTrans.childCount - validChildCount) * spacing;
            targetAnchorPos.y = yOffset + bottomPadding + curHeight * child.pivot.y;
            child.anchoredPosition = targetAnchorPos;

            travedChildsWidth += curWidth;
        }
    }
}
