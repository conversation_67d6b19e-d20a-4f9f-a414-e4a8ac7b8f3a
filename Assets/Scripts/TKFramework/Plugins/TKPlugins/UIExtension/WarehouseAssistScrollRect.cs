using UnityEngine;
using System.Collections;
using UnityEngine.UI;
using System;
using UnityEngine.EventSystems;
public class WarehouseAssistScrollRect: ScrollRect
{
    public Action<PointerEventData> ExternalOnDragAction;

    public Action<PointerEventData> ExternalOnEndDragAction;
    public override void OnDrag(PointerEventData eventData)
    {
        base.OnDrag(eventData);
        ExternalOnDragAction?.Invoke(eventData);
    }

    public override void OnEndDrag(PointerEventData eventData)
    {
        base.OnEndDrag(eventData);
        ExternalOnEndDragAction?.Invoke(eventData);
    }
}
