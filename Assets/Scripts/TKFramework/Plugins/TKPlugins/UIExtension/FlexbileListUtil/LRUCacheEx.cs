using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Text;

namespace TKFrame
{
    public class LRUCacheEx<TKey, TValue>
    {
        private int capacity;
        private TKDictionary<TKey, DLinkedListNode<TKey, TValue>> cache;
        private DLinkedListNode<TKey, TValue> head;
        private DLinkedListNode<TKey, TValue> tail;

        private ILRUCacheHandler cacheHandler;

        public int Count
        {
            get
            {
                return cache.Count;
            }
        }

        public LRUCacheEx(ILRUCacheHandler handler, int capacity = 150)
        {
            cacheHandler = handler;

            if (capacity <= 0)
            {
                capacity = 1;
            }
            this.capacity = capacity;
            cache = new TKDictionary<TKey, DLinkedListNode<TKey, TValue>>(capacity);
            head = new DLinkedListNode<TKey, TValue>();
            tail = new DLinkedListNode<TKey, TValue>();
            head.prev = null;
            head.next = tail;
            tail.prev = head;
            tail.next = null;
        }

        public TValue Get(TKey key)
        {
            if (cache.ContainsKey(key))
            {
                DLinkedListNode<TKey, TValue> node = cache[key];
                MoveNodeToTheFront(node);
                return node.value;
            }
            else
            {
                return default;
            }
        }

        public bool TryGetValue(TKey key, out TValue value)
        {
            DLinkedListNode<TKey, TValue> node = null;
            bool exist = cache.TryGetValue(key, out node);
            value = exist ? node.value : default;

            return exist;
        }

        public void Add(TKey key, TValue value)
        {
            DLinkedListNode<TKey, TValue> existNode = null;
            if (cache.TryGetValue(key, out existNode))
            {
                // Update value
                cacheHandler.ReleaseItem(ref existNode.value);
                existNode.value = value;
                MoveNodeToTheFront(existNode);
            }
            else
            {
                DLinkedListNode<TKey, TValue> node = new DLinkedListNode<TKey, TValue>();
                node.key = key;
                node.value = value;
                cache.Add(key, node);
                LinkAtTheFront(node);
                if (capacity < Count)
                {
                    DeleteTheLastNode();
                }
            }
        }

        public void Add(DLinkedListNode<TKey, TValue> node)
        {
            if (node == null)
            {
                return;
            }

            // Delete exist node
            DLinkedListNode<TKey, TValue> existNode = null;
            if (cache.TryGetValue(node.key, out existNode))
            {
                DeleteNode(existNode);
            }

            cache.Add(node.key, node);
            LinkAtTheFront(node);
            if (capacity < Count)
            {
                DeleteTheLastNode();
            }
        }

        public bool IsFull()
        {
            return capacity <= Count;
        }

        public bool ContainsKey(TKey key)
        {
            return cache.ContainsKey(key);
        }

        public void UpdateTheLastNode(TKey key, TValue value)
        {
            if (Count <= 0)
            {
                return;
            }

            DLinkedListNode<TKey, TValue> tailPrev = tail.prev;
            cache.Remove(tailPrev.key);
            cacheHandler.ReleaseItem(ref tailPrev.value);
            tailPrev.key = key;
            tailPrev.value = value;
            cache.Add(tailPrev.key, tailPrev);
            MoveNodeToTheFront(tailPrev);
        }

        public DLinkedListNode<TKey, TValue> PopTheLastNode()
        {
            if (Count <= 0)
            {
                return null;
            }

            DLinkedListNode<TKey, TValue> tailPrev = tail.prev;
            DLinkedListNode<TKey, TValue> tailPrevPrev = tailPrev.prev;
            tailPrevPrev.next = tail;
            tail.prev = tailPrevPrev;

            cache.Remove(tailPrev.key);

            return tailPrev;
        }

        public interface ILRUCacheHandler
        {
            void ReleaseItem(ref TValue value);
        }

        public void Clear()
        {
            DLinkedListNode<TKey, TValue> curNode = head.next;
            DLinkedListNode<TKey, TValue> nextNode = null;
            while (curNode != tail)
            {
                nextNode = curNode.next;
                DeleteNode(curNode);
                curNode = nextNode;
            }

            DeleteNode(head);
            DeleteNode(tail);

            cache.Clear();
            cache = null;
        }

        private void LinkAtTheFront(DLinkedListNode<TKey, TValue> node)
        {
            DLinkedListNode<TKey, TValue> headNext = head.next;
            head.next = node;
            node.prev = head;
            node.next = headNext;
            headNext.prev = node;
        }

        private void DeleteTheLastNode()
        {
            DLinkedListNode<TKey, TValue> lastNode = PopTheLastNode();
            if (lastNode != null)
            {
                cacheHandler.ReleaseItem(ref lastNode.value);
                lastNode = null;
            }
        }

        private void MoveNodeToTheFront(DLinkedListNode<TKey, TValue> node)
        {
            DLinkedListNode<TKey, TValue> prevNode = node.prev;
            DLinkedListNode<TKey, TValue> nextNode = node.next;

            prevNode.next = nextNode;
            nextNode.prev = prevNode;

            LinkAtTheFront(node);
        }

        private void DeleteNode(DLinkedListNode<TKey, TValue> node)
        {
            if (node == null)
            {
                return;
            }

            DLinkedListNode<TKey, TValue> prevNode = node.prev;
            DLinkedListNode<TKey, TValue> nextNode = node.next;

            if (prevNode != null)
            {
                prevNode.next = nextNode;
            }

            if (nextNode != null)
            {
                nextNode.prev = prevNode;
            }

            if (node.key != null)
            {
                cache.Remove(node.key);
            }

            if (node.value != null)
            {
                cacheHandler.ReleaseItem(ref node.value);
            }

            node = null;
        }
    }

    public sealed class DLinkedListNode<TKey, TValue>
    {
        public TKey key;
        public TValue value;
        public DLinkedListNode<TKey, TValue> prev;
        public DLinkedListNode<TKey, TValue> next;
    }
}
