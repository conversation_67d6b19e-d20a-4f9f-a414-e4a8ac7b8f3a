using System;
using UnityEngine;
using TKPlugins;
using System.Collections;
using TKFrame;
using Coroutine = UnityEngine.Coroutine;

namespace ZGame.Battle
{
    public struct AnimEvtParam
    {
        public string EventName;
        public GameObject Sender;
        public UnityEngine.Object Payload;
        public object[] Parameters;
        public AnimationEvent animEvt;
        public AnimEvtParam(string evt, GameObject sender, AnimationEvent animeEvt, params object[] parameters)
        {
            EventName = evt;
            Sender = sender;
            Payload = animeEvt.objectReferenceParameter;
            Parameters = parameters;
            animEvt = animeEvt;
        }

        //public AnimEvtParam(string evt, GameObject sender, UnityEngine.Object payload, AnimationEvent animeEvt, string strParm, params object[] parameters)
        //{
        //    EventName = evt;
        //    Sender = sender;
        //    Payload = payload;
        //    Parameters = parameters;
        //    animEvt = animeEvt;

        //}
    }

    public delegate void AnimEventHandler(AnimEvtParam evtParam);
    public class AnimEventReceiver : MonoBehaviour
    {
        public float minWinLoopTime = 0;
        public float maxWinLoopTime = 0;

        private bool _IsPlaySound = true;
        public bool IsPlaySound 
        { 
            get
            {
                return _IsPlaySound;
            }
            set
            {
                _IsPlaySound = value;
            }
        }

        private bool _IsPlayEffect = true;
        public bool IsPlayEffect 
        {
            get
            {
                return _IsPlayEffect;
            }
            set
            {
                _IsPlayEffect = value;
            }
        }
        //private int _curFrameCount = -1;  
        private AnimEventHandler _eventHandler;
        public event AnimEventHandler AnimEventHandler
        {
            add
			{
				_eventHandler = (AnimEventHandler)Delegate.Remove(_eventHandler, value);
				_eventHandler = (AnimEventHandler)Delegate.Combine(_eventHandler, value);
            }
            remove
            {
                _eventHandler = (AnimEventHandler)Delegate.Remove(_eventHandler, value);
            }
        }

        public Animator Animator { get; private set; }

        protected void Awake()
        {
            Animator = GetComponent<Animator>();
        }

        protected void OnDestroy()
        {
            _eventHandler = null;
        }

        public void OnAttack(AnimationEvent animEvt)
        {
            //确保每帧只要一个动画事件执行
            //if (_curFrameCount == Time.frameCount)
            //{
            //    return;
            //}
            // _curFrameCount = Time.frameCount;

            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("attack", this.gameObject, animEvt, animEvt.objectReferenceParameter));
        }

        public void OnCamShake(AnimationEvent animEvt)
        {
            //确保每帧只要一个动画事件执行
            //if (_curFrameCount == Time.frameCount)
            //{
            //    return;
            //}
            // _curFrameCount = Time.frameCount;
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("camShake", this.gameObject, animEvt, animEvt.objectReferenceParameter));
        }

        public void OnHurt(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("hurt", this.gameObject, animEvt));
        }

        public void OnDie(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("die", this.gameObject, animEvt));
        }

        public void OnScreenDisplay(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("ScreenDisplay", this.gameObject, animEvt));
        }

        public void OnBirthEnd(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("BirthEnd", this.gameObject, animEvt));
        }

        /*
        public void PlaySound(AnimationEvent animEvt)
        {
            if (!IsPlaySound)
                return;

            //暂时屏蔽随机动画声音.
            Animator animator = this.GetComponent<Animator>();
            if (animator != null)
            {
                AnimatorStateInfo curState = animator.GetCurrentAnimatorStateInfo(0);
                AnimatorStateInfo nextState = animator.GetNextAnimatorStateInfo(0);
                if (curState.IsName("random") || nextState.IsName("random"))
                {
                    return;
                }
            }

            AudioClip audioClip = animEvt.objectReferenceParameter as AudioClip;
            if (audioClip != null)
            {
                //动作播放音效接口
                //if (_curFrameCount == Time.frameCount)
                //{
                //	return;
                //}
                //_curFrameCount = Time.frameCount;

                AudioPlay audioPlay = gameObject.GetComponent<AudioPlay>();
                if (audioPlay == null)
                {
                    audioPlay = gameObject.AddComponent<AudioPlay>();
                }
                audioPlay.PlayAudioClip(audioClip);

                //string strParameter = animEvt.stringParameter;
                //AnimatorStateInfo curState = animator.GetCurrentAnimatorStateInfo(0);
                //AnimatorStateInfo nextState = animator.GetNextAnimatorStateInfo(0);
                //if (curState.IsName(strParameter) || nextState.IsName(strParameter))
                //{
                //    AudioPlay audioPlay = this.gameObject.GetComponent<AudioPlay>();
                //    if(audioPlay == null)
                //    {
                //        audioPlay = this.gameObject.AddComponent<AudioPlay>();
                //    }
                //    audioPlay.PlayAudioClip(audioClip);
                //}
            }
            else
            {
                if (!string.IsNullOrEmpty(animEvt.stringParameter))
                {
                    string[] strArr = animEvt.stringParameter.Split('|');
                    if (strArr.Length == 2)
                    {
                        string bankName = strArr[0];
                        string eventName = strArr[1];
                        WwisePlayBankData playBankData = ZGameChess.Chess_WwisePlayManager.instance.GetBankData();
                        playBankData.gameObject = this.gameObject;
                        playBankData.wwise_bankName = bankName;
                        playBankData.wwise_eventName = eventName;
                        playBankData.wwise_stateGroup = "";
                        playBankData.wwise_state = "";
                        playBankData.isBackGround = false;
                        if(TKPluginAudioPlayDelegate.PlayWwiseBankFunc != null)
                            TKPluginAudioPlayDelegate.PlayWwiseBankFunc.Invoke(playBankData,null);
                    }
                }
                else if (!string.IsNullOrEmpty(animEvt.animatorClipInfo.clip.name))
                {
                    string[] strArr = this.gameObject.name.Split('_');
                    if (strArr != null)
                    {
                        string heroName = strArr[0];//this.gameObject.name.Replace("(Clone)", "");
                        if (strArr.Length > 2)
                        {
                            heroName += "_";
                            heroName += strArr[1];
                        }
                        string bankName = heroName;
                        string eventName = heroName + "_" + animEvt.animatorClipInfo.clip.name;
                        if (eventName.Contains("shifa"))
                            eventName = eventName.Replace("shifa", "skill");
                        WwisePlayBankData playBankData = ZGameChess.Chess_WwisePlayManager.instance.GetBankData();
                        playBankData.gameObject = this.gameObject;
                        playBankData.wwise_bankName = bankName;
                        playBankData.wwise_eventName = eventName;
                        playBankData.wwise_stateGroup = "";
                        playBankData.wwise_state = "";
                        playBankData.isBackGround = false;

                        //开启3D音效
                        AkGameObj akGameObj = gameObject.GetComponent<AkGameObj>();
                        if (akGameObj != null)
                            akGameObj.enabled = true;

                        if(TKPluginAudioPlayDelegate.PlayWwiseBankFunc != null)
                            TKPluginAudioPlayDelegate.PlayWwiseBankFunc.Invoke(playBankData,null);
                    }
                }
            }
        }
        */

        public void OnSpeedChange(float newSpeed)
        {
            if(newSpeed > 0)
            {
                for(var iter = _animFXSpeed.GetEnumerator(); iter.MoveNext();)
                {
                    if(iter.Current.Value != null)
                    {
                        iter.Current.Value.ResetSpeed(newSpeed);
                    }
                }
            }
        }

        public void OnHidden()
        {
            //确保每帧只要一个动画事件执行
            //if (_curFrameCount == Time.frameCount)
            //{
            //    return;
            //}
            //_curFrameCount = Time.frameCount;

            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("hidden", this.gameObject, null));
        }

        private TKDictionary<string, GameObject> _animEffect = new TKDictionary<string, GameObject>();
        private TKDictionary<string, FXSpeed> _animFXSpeed = new TKDictionary<string, FXSpeed>();
        //private GameObject _playingEffect = null;
        private Coroutine _playingEffectCoroutine = null;
        /*
        public void PlayEffect(AnimationEvent animEvt)
        {
            if(_playingEffect != null)
            {
                _playingEffect.SetActive(false);
                _playingEffect = null;
            }

            if (_playingEffectCoroutine != null)
            {
                StopCoroutine(_playingEffectCoroutine);
            }

            GameObject eff = animEvt.objectReferenceParameter as GameObject;
            //intParameter为0则表明特效默认跟随英雄移动，为1则表明特效就在英雄发动位置不跟随英雄移动
            int intParameter = animEvt.intParameter;
            if (eff != null)
            {
                if (IsPlayEffect)
                {
                    GameObject newEff = null;
                    FXSpeed fxSpeed = null;

                    if (_animEffect.ContainsKey(eff.name) 
                        && _animEffect[eff.name] != null
                        && _animFXSpeed.ContainsKey(eff.name)
                        && _animFXSpeed[eff.name] != null)
                    {
                        newEff = _animEffect[eff.name];
                        newEff.SetActive(true);

                        fxSpeed = _animFXSpeed[eff.name];
                        fxSpeed.Speed = animEvt.animatorStateInfo.speed;
                    }
                    else
                    {
                        newEff = Instantiate(eff, transform.position, transform.rotation) as GameObject;
                        fxSpeed = newEff.AddComponent<FXSpeed>();
                        fxSpeed.Speed = animEvt.animatorStateInfo.speed;
                        if (intParameter == 0)
                        {
                            newEff.transform.SetParent(this.transform, true);
                            newEff.transform.localScale = Vector3.one;
                        }

                        FXLifeTime fxLifeTime = newEff.GetComponent<FXLifeTime>();
                        if(fxLifeTime != null)
                        {
                            fxLifeTime.autoDestory = false;
                        }

                        if(_animFXSpeed.ContainsKey(eff.name))
                        {
                            _animFXSpeed[eff.name] = fxSpeed;
                        }
                        else
                        {
                            _animFXSpeed.Add(eff.name, fxSpeed);
                        }

                        if (_animEffect.ContainsKey(eff.name))
                        {
                            _animEffect[eff.name] = newEff;
                        }
                        else
                        {
                            _animEffect.Add(eff.name, newEff);
                        }
                    }

                    _playingEffect = newEff;
                    _playingEffectCoroutine = StartCoroutine(delayHideEffect(newEff, 2.0f));
                }
            }

        }
        */

        public IEnumerator delayHideEffect(GameObject effect, float hideTime)
        {
            yield return TKFrame.CoroutineWait.GetWaitForSeconds(hideTime);
            
            if(effect != null)
            {
                effect.SetActive(false);
            }
        }

        // int as heroid
        public void EffectModification(AnimationEvent animEvt)
        {
            // the name of effect which need to modified
            // Please make sure that the name of the gameobject is unique. Otherwise will only modify the first one.
            string strParam = animEvt.stringParameter;
            // zero represent as hide, and one stands for show
            int intParam = animEvt.intParameter;
            // change time, 0 means hide(or show) immediately
            float floatParam = animEvt.floatParameter;

            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("EffectModification", gameObject, animEvt));
        }

        public void BegunTimeScale(AnimationEvent animEvt)
        {
            //确保每帧只要一个动画事件执行
            //if (_curFrameCount == Time.frameCount)
            //{
            //    return;
            //}
            //_curFrameCount = Time.frameCount;
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("BegunTimeScale", this.gameObject, animEvt));
        }

        public void EndTimeScale(AnimationEvent animEvt)
        {
            //确保每帧只要一个动画事件执行
            //if (_curFrameCount == Time.frameCount)
            //{
            //    return;
            //}
            //_curFrameCount = Time.frameCount;
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("EndTimeScale", this.gameObject, animEvt));
        }

        public void RushEnd(AnimationEvent animEvt)
        {

        }

        //--------------------------------------------------------------------------------------------------------------------------------------------------------------

        public void OnSpawnEffect(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("SpawnEffect", this.gameObject, animEvt));
        }

        public void OnSpawned(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("Spawned", this.gameObject, animEvt));
        }

        public void OnAnimationPlay(AnimationEvent animEvt)
        {
            if(_eventHandler != null)
                _eventHandler.Invoke(new AnimEvtParam("OnAnimationPlay", this.gameObject, animEvt, animEvt.animatorClipInfo.clip.name));
        }

        public void RegistAnimationEvent(string stateName, string functionName)
        {
            if (Animator != null && Animator.runtimeAnimatorController != null)
            {
                AnimationClip[] clips = Animator.runtimeAnimatorController.animationClips;
                for (int i = 0; i < clips.Length; i++)
                {
                    var clip = clips[i];
                    bool evtExist = false;
                    if (clip.name == stateName)
                    {
                        for (int j = 0; j < clip.events.Length; j++)
                        {
                            var evt = clip.events[j];
                            if (evt.functionName == functionName)
                            {
                                evtExist = true;
                                break;
                            }
                        }

                        if (!evtExist)
                        {
                            AnimationEvent animationEvent = new AnimationEvent
                            {
                                functionName = functionName
                            };
                            clip.AddEvent(animationEvent);
                        }
                        break;
                    }
                }
            }
        }

        public void Clear()
        {
            for(var iter = _animEffect.GetEnumerator(); iter.MoveNext();)
            {
                if(iter.Current.Value != null)
                {
                    GameObject.Destroy(iter.Current.Value);
                }
            }

            _animEffect.Clear();
            _animFXSpeed.Clear();

            //_playingEffect = null;

            if(_playingEffectCoroutine != null)
            {
                StopCoroutine(_playingEffectCoroutine);

                _playingEffectCoroutine = null;
            }
        }
    }
}
