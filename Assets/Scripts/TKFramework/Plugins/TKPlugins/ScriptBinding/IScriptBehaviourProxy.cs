using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
namespace TKPlugins.ScriptBinding
{
    public interface IScriptBehaviourProxy
    {
        object GetScriptHandle();
        void Awake();
        void OnEnable();
        void Start();
        void OnDisable();
        void OnDestroy();
        void UpdateInEditor();
        void ReloadLuaInEditor();
        void OnPooledAlloc(Component spawner);
        void OnPooledGet();
        void OnPooledRelease();



        void Update();
        void FixedUpdate();
        void LateUpdate();


        void OnKeyFrameI(int i);
        void OnKeyFrameS(string s);

        void OnAnimatorStateEnter(AnimatorEventDispatcher dispatcher, int stateHash, float length);
        void OnAnimatorStateExit(AnimatorEventDispatcher dispatcher, int stateHash);

        void OnPointerClick(PointerEventData eventData);
        void OnPointerDown(PointerEventData eventData);
        void OnPointerUp(PointerEventData eventData);
        void OnPointerEnter(PointerEventData eventData);
        void OnPointerExit(PointerEventData eventData);
        void OnScroll(PointerEventData eventData);



        // Summary:
        //     Called by a BaseInputModule when a drag has been found but before it is valid
        //     to begin the drag.
        //
        // Parameters:
        //   eventData:
        void OnInitializePotentialDrag(PointerEventData eventData);
        // Summary:
        //     Called by a BaseInputModule before a drag is started.
        //
        // Parameters:
        //   eventData:
        //     Current event data.
        void OnBeginDrag(PointerEventData eventData);
        // Summary:
        //     When draging is occuring this will be called every time the cursor is moved.
        //
        // Parameters:
        //   eventData:
        //     Current event data.
        void OnDrag(PointerEventData eventData);
        // Summary:
        //     Called by a BaseInputModule on a target that can accept a drop.
        //
        // Parameters:
        //   eventData:
        //     Current event data.
        void OnDrop(PointerEventData eventData);
        // Summary:
        //     Called by a BaseInputModule when a drag is ended.
        //
        // Parameters:
        //   eventData:
        //     Current event data.
        void OnEndDrag(PointerEventData eventData);
        void OnMove(AxisEventData eventData);

        void OnCancel(BaseEventData eventData);

        void OnDeselect(BaseEventData eventData);

        void OnSelect(BaseEventData eventData);
        void OnSubmit(BaseEventData eventData);
        void OnUpdateSelected(BaseEventData eventData);


        void OnMouseDown();//	OnMouseDown is called when the user has pressed the mouse button while over the GUIElement or Collider.
        void OnMouseDrag();//	OnMouseDrag is called when the user has clicked on a GUIElement or Collider and is still holding down the mouse.
        void OnMouseEnter();//	Called when the mouse enters the GUIElement or Collider.
        void OnMouseExit();//	Called when the mouse is not any longer over the GUIElement or Collider.
        void OnMouseOver();//	Called every frame while the mouse is over the GUIElement or Collider.
        void OnMouseUp();//	OnMouseUp is called when the user has released the mouse button.
        void OnMouseUpAsButton();//	OnMouseUpAsButton is only called when the mouse is released over the same GUIElement or Collider as it was pressed.




        void OnCollisionEnter(Collision p);//	OnCollisionEnter is called when this collider/rigidbody has begun touching another rigidbody/collider.
        void OnCollisionExit(Collision p);//	OnCollisionExit is called when this collider/rigidbody has stopped touching another rigidbody/collider.
        void OnCollisionStay(Collision p);//	OnCollisionStay is called once per frame for every collider/rigidbody that is touching rigidbody/collider.
        void OnTriggerEnter(Collider p);//	OnTriggerEnter is called when the Collider other enters the trigger.
        void OnTriggerExit(Collider p);//	OnTriggerExit is called when the Collider other has stopped touching the trigger.
        void OnTriggerStay(Collider p);//	OnTriggerStay is called once per frame for every Collider other that is touching the trigger.

        void OnCollisionStay2D(Collision2D p);//	Sent each frame where a collider on another object is touching this object's collider (2D physics only).
        void OnCollisionEnter2D(Collision2D p);//	Sent when an incoming collider makes contact with this object's collider (2D physics only).
        void OnCollisionExit2D(Collision2D p);//	Sent when a collider on another object stops touching this object's collider (2D physics only).
        void OnTriggerEnter2D(Collider2D p);//	Sent when another object enters a trigger collider attached to this object (2D physics only).
        void OnTriggerExit2D(Collider2D p);//	Sent when another object leaves a trigger collider attached to this object (2D physics only).
        void OnTriggerStay2D(Collider2D p);//	Sent each frame where another object is within a trigger collider attached to this object (2D physics only).

        void OnJointBreak(float breakForce);//	Called when a joint attached to the same game object broke.


        void OnDrawGizmos();//	Implement OnDrawGizmos if you want to draw gizmos that are also pickable and always drawn.
        void OnDrawGizmosSelected();//	Implement this OnDrawGizmosSelected if you want to draw gizmos only if the object is selected.
        void OnGUI();//	OnGUI is called for rendering and handling GUI events.

        void OnTransformChildrenChanged();//	This function is called when the list of children of the transform of the GameObject has changed.
        void OnTransformParentChanged();//	This function is called when the parent property of the transform of the GameObject has changed.
        void OnLevelWasLoaded();//	This function is called after a new level was loaded.

        void OnPostRender();//	OnPostRender is called after a camera finished rendering the scene.
        void OnPreCull();//	OnPreCull is called before a camera culls the scene.
        void OnPreRender();//	OnPreRender is called before a camera starts rendering the scene.
        void OnRenderImage(RenderTexture src, RenderTexture dest);//	OnRenderImage is called after all rendering is complete to render image.
        void OnRenderObject();//	OnRenderObject is called after camera has rendered the scene.
        void OnWillRenderObject();//	OnWillRenderObject is called once for each camera if the object is visible.


        void OnBecameInvisible();//	OnBecameInvisible is called when the renderer is no longer visible by any camera.
        void OnBecameVisible();//	OnBecameVisible is called when the renderer became visible by any camera.

        void OnParticleCollision(GameObject other);//	OnParticleCollision is called when a particle hits a collider.
        void OnControllerColliderHit(ControllerColliderHit hit);//	OnControllerColliderHit is called when the controller hits a collider while performing a Move.

        void OnApplicationFocus(bool hasFocus);//	Sent to all game objects when the player gets or loses focus.
        void OnApplicationPause(bool hasPause);//	Sent to all game objects when the player pauses.
        void OnApplicationQuit();//	Sent to all game objects before the application is quit.

        void OnAnimatorIK(int layerIndex);//	Callback for setting up animation IK (inverse kinematics).
        void OnAnimatorMove();//	Callback for processing animation movements for modifying root motion.

        void OnBeforeTransformParentChanged();
        void OnCanvasGroupChanged();
        void OnCanvasHierarchyChanged();
        void OnDidApplyAnimationProperties();
        void OnRectTransformDimensionsChange();
    }
}
