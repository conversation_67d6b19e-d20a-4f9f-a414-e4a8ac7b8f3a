using UnityEngine;
using System.Collections.Generic;
using System;
using UnityEngine.UI;
using UnityEngine.EventSystems;
namespace TKPlugins.ScriptBinding
{
    public class RenderNotifier : EventNotifier
    {
        public void OnPreCull()
        {
            binder.proxy.OnPreCull();
        }
        public void OnPreRender()
        {
            binder.proxy.OnPreRender();
        }
        public void OnRenderImage(RenderTexture src, RenderTexture dest)
        {
            binder.proxy.OnRenderImage(src, dest);
        }
        public void OnWillRenderObject()
        {
            binder.proxy.OnWillRenderObject();
        }
        public void OnRenderObject()
        {
            binder.proxy.OnRenderObject();
        }
        public void OnPostRender()
        {
            binder.proxy.OnPostRender();
        }
    }
}