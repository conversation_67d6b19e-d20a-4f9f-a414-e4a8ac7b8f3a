using System;
using System.IO;

namespace TKPlugins
{
	/// <summary>
	/// game lancher 游戏启动过程日志输出
	/// </summary>
	public class GLLogger
	{
		private StreamWriter _outputStream;
		private bool _shutdowned = false;
		public GLLogger ( string filePath )
		{
			if (!File.Exists(filePath))
			{
				_outputStream = File.CreateText(filePath);
			}
			else
			{
				bool append = true;
				FileInfo fileInfo = new FileInfo(filePath);
				if (fileInfo.Exists && fileInfo.Length > 1024*1024*5)
				{
					//如果日志文件已经超过一定的大小(暂定5M)，则清除以前的。
					append = false;
				}

				_outputStream = new StreamWriter(filePath, append);
			}
		}

		//写日志接口
		public void WriteLog(string str) 
		{
			//读取当前时间
			string sLogLine =  DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

			sLogLine += " " + str + "\n";

			SaveLogToFile(sLogLine);
		}

		//保存日志到文件接口
		private void SaveLogToFile (string sLogLine)
		{
			if (_shutdowned)
				return;
			
			try {
				_outputStream.WriteLine (sLogLine);
				_outputStream.Flush ();

			} catch (Exception e) {
				//UnityEngine.Debug.LogError (e.ToString ());
			} finally {
				
			}


		}

		public  void Shutdown()
		{
			if (_outputStream != null) {
				_outputStream.Close ();
				_outputStream = null;
				_shutdowned = true;
			}
		}
	}
}

