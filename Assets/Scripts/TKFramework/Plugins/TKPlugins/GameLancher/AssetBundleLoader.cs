using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TKPlugins
{
    /// <summary>
    /// 通过www 加载AssetBundle
    /// </summary>
    public class AssetBundleLoader : MonoBehaviour
    {

        public AssetBundleLoadState LoadAssetBundleAsync(string url)
        {
            AssetBundleLoadState abLoadState = new AssetBundleLoadState();

            StartCoroutine(LoadAsync(abLoadState, url));

            return abLoadState;
        }

        private IEnumerator LoadAsync( AssetBundleLoadState abLoadState ,string url )
        {
            WWW www = new WWW(url);

            yield return www;

            if( !string.IsNullOrEmpty(www.error) )
            {
                Debug.LogError(" load assetbundle error: " + www.error);

                abLoadState.SetLoadResult(null, www.error);
                if (abLoadState.OnLoadedCB != null)
                {
                    abLoadState.OnLoadedCB(null, www.error);
                }
            }
            else
            {
                abLoadState.SetLoadResult(www.assetBundle, www.error);
                if (abLoadState.OnLoadedCB != null)
                {
                    abLoadState.OnLoadedCB(www.bytes, www.error);
                }
            }


            www.Dispose();
        }

    }
}
