using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TKFrame
{
    public class PlaySingleAnim : MonoBehaviour
    {
        protected Animator _cacheAnimator;
        public string animName;
        public void Awake()
        {
            _cacheAnimator = GetComponent<Animator>();
            if( _cacheAnimator )
            {
                _cacheAnimator = GetComponentInChildren<Animator>();
            }
        }


        public void PlayAnim(  )
        {
            PlayAnim(animName);
        }

        public void PlayAnim( string animName )
        {
            if (_cacheAnimator == null || !this.gameObject.activeInHierarchy) return;

            StartCoroutine(   DelayPlayAnim(animName));

           // _cacheAnimator.Play(animName);
        }

        IEnumerator DelayPlayAnim( string name )
        {
           
           // yield break;
            yield return TKFrame.CoroutineWait.GetWaitForSeconds(0.2f);
           // Debug.LogError("DelayPlayAnim,DelayPlayAnim!");
            _cacheAnimator.Play(animName);
        }

        void OnDestroy()
        {
            //Debug.LogError("DelayPlayAnim,OnDestroy!");
        }
    }
}
