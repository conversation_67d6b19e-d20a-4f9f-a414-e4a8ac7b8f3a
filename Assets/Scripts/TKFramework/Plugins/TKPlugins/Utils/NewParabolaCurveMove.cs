using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKPlugins
{
    /// <summary>
    /// 新的抛物线曲线, 抛物线完全由美术提供的曲线控制
    /// </summary>
    public class NewParabolaCurveMove
    {
        public float maxDist;
        public float minHeight;
        public float maxHeight;
        public AnimationCurve curve;

        private Vector3 _startPos;
        private Vector3 _endPos;
        private float _durtion;
        private float _height;
        private float _accumTime;

        public void InitParam(Vector3 startPos, Vector3 endPos, float durtion)
        {
            _startPos = startPos;
            _endPos = endPos;
            _durtion = durtion;

            float dist = Vector3.Distance(startPos, endPos);
            float distFactor = dist / maxDist;
            _height = minHeight + (maxHeight - minHeight) * distFactor;
            _accumTime = 0;

            //Debug.Log(string.Format("_startPos:{0} _endPos:{1} _durtion:{2} _height:{3}", _startPos, _endPos, _durtion, _height));
        }

        public void ResetAccumTime(float accumTime)
        {
            _accumTime = accumTime;
        }

        public float Sample(float deltaTime, out Vector3 pos)
        {
            _accumTime += deltaTime;

            float percent = _accumTime / _durtion;
            if (percent > 1f)
                percent = 1f;

            float height;
            if (curve != null)
            {
                height = _height * curve.Evaluate(percent);
            }
            else
            {
                if (percent <= 0.5f)
                {
                    height = _height * percent;
                }
                else
                {
                    height = _height * (1 - percent);
                }
            }

            Vector3 posx = Vector3.Lerp(_startPos, _endPos, percent);

            pos = new Vector3(posx.x, posx.y + height, posx.z);

            //Debug.Log(string.Format("_accumTime:{0} percent:{1} pos:{2}", _accumTime, percent, pos));

            return percent;
        }
    }
}
