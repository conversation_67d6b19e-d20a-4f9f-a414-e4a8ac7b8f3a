using System;
using System.Collections.Generic;



/*
The MIT License (MIT)


Copyright (c) 2014 matarillo


Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:


The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.


THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/


using System.Runtime.Serialization;
using TKFrame;
#if !ACGGAME_CLIENT
namespace System.Collections.Generic
{
    public interface IReadOnlyCollection<out T> : IEnumerable<T>, IEnumerable
    {
        int Count { get; }
    }
}
#endif
[Serializable]
public class LinkedHashSet<TValue> : ICollection<TValue>, IEnumerable<TValue>, System.Collections.IEnumerable, IReadOnlyCollection<TValue>, ILinkedHashSet<TValue>, IDeserializationCallback, ISerializable
{
    [NonSerialized]
    private TKDictionary<TValue, int> set;
    private readonly List<TValue> list;

    #region constructor

    public LinkedHashSet()
    {
        set = new TKDictionary<TValue, int>();
        list = new List<TValue>();
    }


    public LinkedHashSet(IEqualityComparer<TValue> comparer)
    {
        set = new TKDictionary<TValue, int>(comparer);
        list = new List<TValue>();
    }


    public LinkedHashSet(int capacity)
    {
        set = new TKDictionary<TValue, int>(capacity);
        list = new List<TValue>(capacity);
    }


    public LinkedHashSet(int capacity, IEqualityComparer<TValue> comparer)
    {
        set = new TKDictionary<TValue, int>(capacity, comparer);
        list = new List<TValue>(capacity);
    }


    public LinkedHashSet(IEnumerable<TValue> source)
    {
        if (source == null)
        {
            throw new ArgumentNullException("source");
        }
        var countable = source as System.Collections.ICollection;
        if (countable != null)
        {
            set = new TKDictionary<TValue, int>(countable.Count);
            list = new List<TValue>(countable.Count);
        }
        else
        {
            set = new TKDictionary<TValue, int>();
            list = new List<TValue>();
        }
        foreach (TValue value in source)
        {
            Add(value);
        }
    }


    public LinkedHashSet(IEnumerable<TValue> source, IEqualityComparer<TValue> comparer)
    {
        if (source == null)
        {
            throw new ArgumentNullException("source");
        }
        var countable = source as System.Collections.ICollection;
        if (countable != null)
        {
            set = new TKDictionary<TValue, int>(countable.Count, comparer);
            list = new List<TValue>(countable.Count);
        }
        else
        {
            set = new TKDictionary<TValue, int>(comparer);
            list = new List<TValue>();
        }
        foreach (TValue value in source)
        {
            Add(value);
        }
    }

    public LinkedHashSet(List<TValue> kvList)
    {
        set = new TKDictionary<TValue, int>();
        list = new List<TValue>();
        for (int i = 0; i < kvList.Count; i++)
        {
            set.Add(kvList[i], i);
            list.Add(kvList[i]);
        }
    }

    #endregion


    #region ISet implementation

    public IEqualityComparer<TValue> Comparer
    {
        get
        {
            return set.Comparer;
        }
    }

    public bool Contains(TValue value)
    {
        return set.ContainsKey(value);
    }


    public bool Add(TValue value)
    {
        if(set.ContainsKey(value))
        {
            return false;
        }
        DoAdd(value);
        return true;
    }


    private void DoAdd(TValue value)
    {
        list.Add(value);
        set.Add(value, list.Count - 1);
    }


    public bool Remove(TValue value)
    {
        int index;
        if(!set.TryGetValue(value, out index))
        {
            return false;
        }
        DoRemove(index, value);
        return true;
    }


    private void DoRemove(int index, TValue value)
    {
        list.RemoveAt(index);
        set.Remove(value);
        for (var i = index; i < list.Count; i++)
        {
            TValue v = list[i];
            set[v] = i;
        }
    }

    public void ExceptWith(IEnumerable<TValue> other)
    {
        if (other == null)
        {
            throw new ArgumentNullException("other");
        }

        if (list.Count == 0)
        {
            return;
        }

        // special case if other is this; a set minus itself is the empty set
        if (other == this)
        {
            Clear();
            return;
        }

        // remove every element in other from this
        foreach (TValue element in other)
        {
            Remove(element);
        }
    }

    public void IntersectWith(IEnumerable<TValue> other)
    {
        if (other == null)
        {
            throw new ArgumentNullException("other");
        }

        if (list.Count == 0)
        {
            return;
        }

        ICollection<TValue> otherAsCollection = other as ICollection<TValue>;
        if (otherAsCollection != null)
        {
            if (otherAsCollection.Count == 0)
            {
                Clear();
                return;
            }

            LinkedHashSet<TValue> otherAsSet = other as LinkedHashSet<TValue>;
            // faster if other is a hashset using same equality comparer; so check 
            // that other is a hashset using the same equality comparer.
            if (otherAsSet != null && AreEqualityComparersEqual(this, otherAsSet))
            {
                return;
            }
        }

    }
    public bool IsProperSubsetOf(IEnumerable<TValue> other)
    {
        return true;
    }
    public bool IsProperSupersetOf(IEnumerable<TValue> other)
    {
        return true;
    }
    public bool IsSubsetOf(IEnumerable<TValue> other)
    {
        return true;
    }
    public bool IsSupersetOf(IEnumerable<TValue> other)
    {
        return true;
    }
    public bool Overlaps(IEnumerable<TValue> other)
    {
        return true;
    }
    public bool SetEquals(IEnumerable<TValue> other)
    {
        return true;
    }

    public void SymmetricExceptWith(IEnumerable<TValue> other)
    {
        if (other == null)
        {
            throw new ArgumentNullException("other");
        }

        foreach (TValue item in other)
        {
            if (!Remove(item))
            {
                Add(item);
            }
        }
    }
    public void UnionWith(IEnumerable<TValue> other)
    {
        if (other == null)
        {
            throw new ArgumentNullException("other");
        }

        foreach (TValue item in other)
        {
            Add(item);
        }
    }

    private static bool AreEqualityComparersEqual(LinkedHashSet<TValue> set1, LinkedHashSet<TValue> set2)
    {
        return set1.Comparer.Equals(set2.Comparer);
    }

    public ICollection<TValue> Values
    {
        get
        {
            return list;
        }
    }

    public List<TValue> GetValues()
    {
        return list;
    }

#endregion


    #region ICollection implementation


    public void Clear()
    {
        set.Clear();
        list.Clear();
    }


    public int Count
    {
        get { return list.Count; }
    }


    public bool IsReadOnly
    {
        get { return false; }
    }


#endregion


    #region IEnumerable implementation

    public List<TValue>.Enumerator GetEnumerator()
    {
        return list.GetEnumerator();
    }
    
    IEnumerator<TValue> IEnumerable<TValue>.GetEnumerator()
    {
        return list.GetEnumerator();
    }


    #endregion


    #region IEnumerable implementation

    System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    #endregion


    #region explicit ICollection implementation


    void ICollection<TValue>.Add(TValue value)
    {
        Add(value);
    }


    bool ICollection<TValue>.Contains(TValue item)
    {
        return Contains(item);
    }


    void ICollection<TValue>.CopyTo(TValue[] array, int arrayIndex)
    {
        list.CopyTo(array, arrayIndex);
    }


    bool ICollection<TValue>.Remove(TValue item)
    {
        return Remove(item);
    }

    #endregion


    #region 反序列化后重新构造Set

    public void OnDeserialization(object sender)
    {
        set = new TKDictionary<TValue, int>();
        for (int i = 0; i < list.Count; i++)
        {
            set.Add(list[i], i);
        }
    }


    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        if (info == null)
        {
            throw new System.ArgumentNullException();
        }
        info.AddValue("Version", 0);

#if FEATURE_RANDOMIZED_STRING_HASHING && !FEATURE_NETCORE
        info.AddValue("Comparer", HashHelpers.GetEqualityComparerForSerialization(m_comparer), typeof(IEqualityComparer<T>));
#else
        info.AddValue("Comparer", set.Comparer, typeof(IEqualityComparer<TValue>));
#endif

        info.AddValue("Capacity", 0); //This is the length of the bucket array.

        TValue[] array = new TValue[Count];
        Array.Copy(list.ToArray(), array, Count);
        info.AddValue("Elements", array, typeof(TValue[]));
    }
    #endregion

    public List<TValue> GetLinkedHashSetList()
    {
        return new List<TValue>(list);
    }

}

public interface ILinkedHashSet<TValue> : ISet<TValue>
{
    List<TValue> GetLinkedHashSetList();
}

