using System.Collections.Generic;

namespace TKPlugins
{

    public class LogicDataAsset
    {
        public class LogicDataItem
        {
            public string name;

            public int intVal;

            public float floatVal;

            public string strVal;

            public MinMaxData minMaxVal;

            public List<float> curve;

            public DataTypeEnum type;
        }

        public List<LogicDataItem> m_dataList = new List<LogicDataItem>();

        public int GetVal(string paramName, int defaultVal = 0)
        {
            var item = GetDataItem(paramName);
            if (item == null)
                return defaultVal;

            return item.intVal;
        }

        public float GetVal(string paramName, float defaultVal = 0f)
        {
            var item = GetDataItem(paramName);
            if (item == null)
                return defaultVal;

            return item.floatVal;
        }

        public string GetVal(string paramName, string defaultVal = "")
        {
            var item = GetDataItem(paramName);
            if (item == null)
                return defaultVal;

            return item.strVal;
        }

        public List<float> GetVal(string paramName, List<float> defaultVal)
        {
            var item = GetDataItem(paramName);
            if (item == null)
                return defaultVal;

            return item.curve;
        }

        public MinMaxData GetVal(string paramName, MinMaxData defaultVal)
        {
            var item = GetDataItem(paramName);
            if (item == null)
                return defaultVal;

            return item.minMaxVal;
        }

        public LogicDataItem GetDataItem(string paramName)
        {
            for (int i = 0; i < m_dataList.Count; i++)
            {
                if (m_dataList[i].name == paramName)
                {
                    return m_dataList[i];
                }
            }
            return null;
        }
    }
}


