using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using UnityEngine.UI;

public class TextFast : Text
{
    private MeshCache m_meshCache = null;
    private Mesh m_currentMesh = null;
    private bool m_newMesh = false;
    
    #if !CANVAS_BATCHS_OPT
    
    private static FieldInfo m_colorField = null;
        
    private static FieldInfo colorField
    {
        get
        {
            if (m_colorField == null)
            {
                System.Type graphicType = typeof(Graphic);
                m_colorField = graphicType.GetField("m_Color", BindingFlags.Instance | BindingFlags.NonPublic);
            }
    
            return m_colorField;
        }
    }

    #endif

    protected override void UpdateGeometry()
    {
        base.UpdateGeometry();

        if (isUseCache)
        {
            m_meshCache.AddMesh(text, workerMesh);
        }
        else
        {
            CacheMesh();
        }
        
        m_newMesh = false;
    }
    
    private bool isUseCache
    {
        get
        {
            return m_meshCache != null;
        }
    }
    
    public void SetCache(MeshCache meshCache)
    {
        m_meshCache = meshCache;
    }
    
    private void CacheMesh()
    {
        if (m_currentMesh == null)
        {
            m_currentMesh = new Mesh();
        }
        MeshUtil.CopyMesh(workerMesh, m_currentMesh);
    }

    public void SetTextDirect(string str)
    {
        if (isUseCache)
        {
            Mesh result = m_meshCache.GetMesh(str);
            if(result != null)
            {
                m_Text = str;
                m_currentMesh = result;
                SetColor();
                canvasRenderer.SetMesh(m_currentMesh);
                canvasRenderer.SetTexture(mainTexture);
                return;
            }
        }
        
        text = str;
        m_newMesh = true;
    }
    
    public void SetColorDirect(Color targetColor)
    {
        if (color == targetColor)
        {
            return;
        }
    
        if (m_newMesh || m_currentMesh == null)
        {
            color = targetColor;
            m_newMesh = true;
            return;
        }
        
#if !CANVAS_BATCHS_OPT
        colorField.SetValue(this, targetColor);
#else
        m_Color = targetColor;
#endif
        ShowColor();
            
    }    
    
    private void ShowColor()
    {
        if (SetColor())
        {
            canvasRenderer.SetMesh(m_currentMesh);
        }
    }

    private bool SetColor()
    {
        Color[] colors = m_currentMesh.colors;
        if (colors.Length > 0 && colors[0] != color)
        {
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i].r = color.r;
                colors[i].g = color.g;
                colors[i].b = color.b;
                colors[i].a = color.a;
            }

            m_currentMesh.colors = colors;
            return true;
        }
        
        return false;
    }
}
