using System;
using System.Reflection;
using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UI/RawImageFast", 13)]
public class RawImageFast : RawImage
{
    private Mesh m_currentMesh;
    private Vector2[] uvs = null;
    
    #if !CANVAS_BATCHS_OPT
    
    private static FieldInfo m_textureField = null;
    private static FieldInfo m_colorField = null;
    
    private static FieldInfo textureField
    {
        get
        {
            if (m_textureField == null)
            {
                System.Type imageType = typeof(RawImage);
                m_textureField = imageType.GetField("m_Texture", BindingFlags.Instance | BindingFlags.NonPublic);
            }

            return m_textureField;
        }
    }
    
    private static FieldInfo colorField
    {
        get
        {
            if (m_colorField == null)
            {
                System.Type graphicType = typeof(Graphic);
                m_colorField = graphicType.GetField("m_Color", BindingFlags.Instance | BindingFlags.NonPublic);
            }

            return m_colorField;
        }
    }
    
    #endif
    
    protected override void OnEnable()
    {
        base.OnEnable();
        if (uvs == null)
        {
            uvs = new Vector2[4];
            uvs[0] = Vector2.zero;
            uvs[1] = Vector2.zero;
            uvs[2] = Vector2.zero;
            uvs[3] = Vector2.zero;
        }
    }
    
    protected override void UpdateGeometry()
    {
        base.UpdateGeometry();

        // Copy mesh data from workerMesh
        CacheMesh();
    }

    public void SetTextureDirect(Texture tex)
    {
        if (tex == null || texture == tex)
        {
            return;
        }

        if (m_currentMesh == null)
        {
            texture = tex;
            return;
        }
        
#if !CANVAS_BATCHS_OPT
        textureField.SetValue(this, tex);
#else
        m_Texture = tex;
#endif
        
        ShowTexture();
    }
    
    public void SetColorDirect(Color targetColor)
    {
        if (color == targetColor)
        {
            return;
        }
        
        if (m_currentMesh == null)
        {
            color = targetColor;
            return;
        }
        
#if !CANVAS_BATCHS_OPT
        colorField.SetValue(this, targetColor);
#else
        m_Color = targetColor;
#endif
        ShowColor();
    }
    
    private void CacheMesh()
    {
        if (m_currentMesh == null)
        {
            m_currentMesh = new Mesh();
        }
        MeshUtil.CopyMesh(workerMesh, m_currentMesh);
    }
    
    private void GetUVs()
    {
        Texture tex = mainTexture;
        float scaleX = tex.width * tex.texelSize.x;
        float scaleY = tex.height * tex.texelSize.y;
        Rect rect = uvRect;

        uvs[0].x = rect.xMin * scaleX;
        uvs[0].y = rect.yMin * scaleY;
        
        uvs[1].x = rect.xMin * scaleX;
        uvs[1].y = rect.yMax * scaleY;
        
        uvs[2].x = rect.xMax * scaleX;
        uvs[2].y = rect.yMax * scaleY;
        
        uvs[3].x = rect.xMax * scaleX;
        uvs[3].y = rect.yMin * scaleY;
    }

    private void ShowTexture()
    {
        GetUVs();
        
        m_currentMesh.uv = uvs;
        canvasRenderer.SetMesh(m_currentMesh);
        canvasRenderer.SetTexture(mainTexture);
    }
    
    private void ShowColor()
    {
        if (SetColor())
        {
            canvasRenderer.SetMesh(m_currentMesh);
        }
    }

    private bool SetColor()
    {
        Color[] colors = m_currentMesh.colors;
        if (colors.Length > 0 && colors[0] != color)
        {
            for (int i = 0; i < colors.Length; i++)
            {
                colors[i].r = color.r;
                colors[i].g = color.g;
                colors[i].b = color.b;
                colors[i].a = color.a;
            }

            m_currentMesh.colors = colors;
            return true;
        }
        
        return false;
    }
}
