#if CANVAS_BATCHS_OPT
using TKFrame;
using Unity.Collections;
using UnityEngine.UI;

public class BaseMeshEffectUtil
{
    private static int GetNewSize(int currentSize, int newSize)
    {
        while (newSize > currentSize)
        {
            currentSize *= 2;
        }
        return currentSize;
    }

    public static void CopyAndExpandVertexHelperVertexDataSize(VertexHelper vh, int size, int offset, int len)
    {
        if (len * 2 <= size)
        {
            if (vh.Vertices.Length < size)
            {
                int newSize = GetNewSize(20, size);
                NativeArray<VertexHelper.VertexHelperStruct> newDatas = new NativeArray<VertexHelper.VertexHelperStruct>(newSize, Allocator.Persistent, NativeArrayOptions.UninitializedMemory, false);
                NativeArray<VertexHelper.VertexHelperStruct>.Copy(vh.Vertices, 0, newDatas, offset, len);
                vh.Vertices.Dispose();
                vh.Vertices = newDatas;
            }
            else
            {
                NativeArray<VertexHelper.VertexHelperStruct>.Copy(vh.Vertices, 0, vh.Vertices, offset, len);
            }
        }
        else
        {
            Diagnostic.Log("VertexHelper Vertex Data Wrong Size " + size + " " + len);
        }
    }

    public static void CopyAndExpandVertexHelperIndexDataSize(VertexHelper vh, int size, int len)
    {
        if (vh.Indices.Length < size)
        {
            int newSize = GetNewSize(20, size);
            NativeArray<ushort> newDatas = new NativeArray<ushort>(newSize, Allocator.Persistent, NativeArrayOptions.UninitializedMemory, false);
            NativeArray<ushort>.Copy(vh.Indices, 0, newDatas, 0, len);
            vh.Indices.Dispose();
            vh.Indices = newDatas;
        }
    }
}
#endif
