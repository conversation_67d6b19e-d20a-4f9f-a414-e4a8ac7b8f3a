using Random = UnityEngine.Random;
#if TKF_ALL_EXTEND || TKFE_NTWEEN//TKFrame Auto Gen
//----------------------------------------------
//            NGUI: Next-Gen UI kit
// Copyright © 2011-2014 Tasharen Entertainment
//----------------------------------------------
using System;
using UnityEngine;

namespace NTween
{

    /// <summary>
    /// Tween the object's position.
    /// </summary>
    /// 


    [AddComponentMenu("NTween/TweenPositionRandomAngle")]
    public class TweenPositionRandomAngle : UITweener ,ITransformTween
    {
        public Vector3 from;
        public Vector3 to;

        private Vector3 posOffset = Vector3.zero;

        public Transform target;
        
        // 
        public AnimationCurve NormalCurve;
        public bool NormalX;
        public bool NormalY;
        public bool NormalZ;
        
        public Transform cachedTransform { get { if (target == null) target = transform; return target; } }
        
        [System.Obsolete("Use 'value' instead")]
        public Vector3 position { get { return this.value; } set { this.value = value; } }

        /// <summary>
        /// Tween's current value.
        /// </summary>
        public Vector3 value
        {
            get
            {
                return cachedTransform.localPosition;
            }
            set
            {
                cachedTransform.localPosition = value;
            }
        }

        private float angle = 0f;
        /// <summary>
        /// Tween the value.
        /// </summary>
        private void Awake()
        {
            angle = Random.Range(0f, 360f);
        }

        private void OnEnable()
        {
            angle = Random.Range(0f, 360f);
        }

        protected override void OnUpdate(float factor, bool isFinished)
        {
            Vector3 val1 = from * (1f - factor) + to * factor;
            Vector3 val2 = Vector3.zero;
            if (NormalX || NormalY || NormalZ)
            {
                float factor2 = NormalCurve.Evaluate(tweenFactor);
                val2 = from * (1f - factor2) + to * factor2;
            }
            if (val2 != Vector3.zero)
            {
                if (NormalX)
                    val1.x = val2.x;
                if (NormalY)
                    val1.y = val2.y;
                if (NormalZ)
                    val1.z = val2.z;
            }
            value = val1;
            if (NormalX || NormalY || NormalZ)
                cachedTransform.transform.RotateAround(@from, to - @from, angle);
        }
        

        [ContextMenu("Set 'From' to current value")]
        public override void SetStartToCurrentValue() { from = value; }

        [ContextMenu("Set 'To' to current value")]
        public override void SetEndToCurrentValue() { to = value; }

        [ContextMenu("Assume value of 'From'")]
        void SetCurrentValueToStart() { value = from; }

        [ContextMenu("Assume value of 'To'")]
        void SetCurrentValueToEnd() { value = to; }

        public Transform GetTarget()
        {
            return target;
        }

        public void SetTarget(Transform target)
        {
            this.target = target;
        }
        

        public void SetPositionOffset(Vector3 targetOffset)
        {
            // 先恢复from、to原始值
            from -= posOffset;
            to -= posOffset;
            // 更新posOffset
            posOffset = targetOffset;
            // 偏移量同步至from、to
            from += posOffset;
            to += posOffset;
        }
    }
}
#endif //TKFrame Auto Gen
