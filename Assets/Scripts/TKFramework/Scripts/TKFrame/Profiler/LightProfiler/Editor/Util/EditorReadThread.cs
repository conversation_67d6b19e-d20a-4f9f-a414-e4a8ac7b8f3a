using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using UnityEngine;

namespace LightProfiler
{
    public class EditorReadThread
    {
        private QueueRingBuffer m_buffer;
        private byte[] m_cacheBuffer;
        private Thread m_thread;

        private int m_cacheSize = 2 * 1024 * 1024;
        private volatile bool m_isRunning = false;

        private Action m_stopCallback = null;
        private Func<byte[], int, int, int> m_readCallback = null;
        
        public EditorReadThread(QueueRingBuffer buffer)
        {
            m_buffer = buffer;
            m_cacheBuffer = new byte[m_cacheSize];
        }

        public EditorReadThread(QueueRingBuffer buffer, int cacheSize)
        {
            m_buffer = buffer;

            m_cacheSize = cacheSize;
            m_cacheBuffer = new byte[m_cacheSize];
        }
        
        public void SetReadCallback(Func<byte[], int, int, int> callback)
        {
            m_readCallback = callback;
        }

        public void SetStopCallback(Action callback)
        {
            m_stopCallback = callback;
        }

        public void Run()
        {
            m_thread = new Thread(Read);
            m_thread.Start();
        }
        
        public void Stop()
        {
            m_isRunning = false;
        }

        private void Read()
        {
            m_isRunning = true;
            while (m_isRunning)
            {
                if (m_readCallback != null)
                {
                    int len = 0;
                    try
                    {
                        len = m_readCallback(m_cacheBuffer, 0, m_cacheSize);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                        break;
                    }

                    if (len > 0)
                        m_buffer.WriteData(m_cacheBuffer, 0, len);
                    else
                        break;
                }
            }

            if (m_stopCallback != null)
            {
                m_stopCallback();
            }
            
            m_isRunning = false;
            
            Debug.Log("Exit read Thread");
        }
    }
}


