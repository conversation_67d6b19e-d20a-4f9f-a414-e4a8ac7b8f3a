using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace LightProfiler
{
    public class MonoDetailFrameStatus
    {
        private Dictionary<ulong, ThreadClassMsg> m_threadClassMsgs = new Dictionary<ulong, ThreadClassMsg>();
        public Dictionary<ulong, ThreadClassMsg> threadClassMsgs
        {
            get { return m_threadClassMsgs; }
        }
        
        private ThreadClassMsg m_totalClassMsg = new ThreadClassMsg();
        public ThreadClassMsg totalClassMsg
        {
            get { return m_totalClassMsg; }
        }

        private ClassDataModel m_model;

        public MonoDetailFrameStatus()
        {
            m_model = ProfilerModel.Instance.GetClassDataModel();
        }

        public void Clear()
        {
            m_threadClassMsgs.Clear();
            m_totalClassMsg.Clear();
        }
        
        public bool Decode(BinaryReader br, int sizeOfPointer)
        {
            bool result = true;

            while (br.BaseStream.Position < br.BaseStream.Length)
            {
                MonoDataType dataType = (MonoDataType) br.ReadByte();

                switch (dataType)
                {
                    case MonoDataType.MallocSmallSize:
                    case MonoDataType.FreeSmallSize:
                    case MonoDataType.MallocBigSize:
                    case MonoDataType.FreeBigSize:
                    case MonoDataType.AllocHBLK:
                    case MonoDataType.FreeHBLK:
                        BohemGC_Info gcInfo = new BohemGC_Info();
                        gcInfo.Decode(dataType, br, sizeOfPointer);
                        break;
                    case MonoDataType.ExpandHeap:
                        ExpandHeap_Info expandHeapinfo = new ExpandHeap_Info();
                        expandHeapinfo.Decode(dataType, br, sizeOfPointer);
                        break;
                    case MonoDataType.PTRFREE:
                    case MonoDataType.OBJECT:
                    case MonoDataType.TYPED:
                        IL2CPP_Info il2CppInfo = new IL2CPP_Info();
                        il2CppInfo.Decode(dataType, br, sizeOfPointer);
                        AddMonoMsg(il2CppInfo);
                        break;
                    case MonoDataType.BEGINFRAME:
                        BeginFrame_Info.Skip(br);
                        break;
                    case MonoDataType.ENDFRAME:
                        EndFrame_Info.Skip(br);
                        break;
                    default:
                        Debug.Log("decode type failed " + dataType);
                        result = false;
                        break;
                }

                if (dataType == MonoDataType.ENDFRAME)
                    break;
            }

            return result;
        }
        
        public void AddMonoMsg(IL2CPP_Info info)
        {
            ThreadClassMsg threadClassMsg = getThreadClassMsg(info.thread_Id);
            threadClassMsg.count++;
            threadClassMsg.size += info.size;

            ClassMsg classMsg = getClassMsg(threadClassMsg, info.classPointer);
            classMsg.count++;
            classMsg.size +=  info.size;
            classMsg.callStackTree.AddCallNode(info.callMethodId, info.size);

            m_totalClassMsg.count++;
            m_totalClassMsg.size +=  info.size;
            
            classMsg = getClassMsg(m_totalClassMsg, info.classPointer);
            classMsg.count++;
            classMsg.size += info.size;
            classMsg.callStackTree.AddCallNode(info.callMethodId, info.size);
        }
        
        private ThreadClassMsg getThreadClassMsg(ulong threadId)
        {
            ThreadClassMsg result = null;
            if (!m_threadClassMsgs.TryGetValue(threadId, out result))
            {
                result = new ThreadClassMsg();
                result.threadId = threadId;
                m_threadClassMsgs.Add(threadId, result);
            }

            return result;
        }

        private ClassMsg getClassMsg(ThreadClassMsg threadClassMsg, ulong pointer)
        {
            Dictionary<ulong, ClassMsg> classMsgs = threadClassMsg.classMsgs;

            ClassMsg result = null;
            if (!classMsgs.TryGetValue(pointer, out result))
            {
                result = new ClassMsg();
                result.threadId = threadClassMsg.threadId;
                result.className = m_model.GetClassName(pointer);
                result.pointer = pointer;
                classMsgs.Add(pointer, result);
            }

            return result;
        }
    }

}
