using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using TKFrame;
using UnityEngine;
using UnityEngine.Profiling;

namespace LightProfiler
{
    public enum MethodDataType
    {
        BEGINFRAME = 0,
        ENDFRAME,
        BEGINFUNC,
        BEGINFUNCNAME,
        ENDFUNC,
        BEGINFUNCWITHOUTTIME,
        ENDFUNCWITHOUTTIME,
    }

    public class MethodProfilerInfo
    {
        public static void BeginFuncDecode(BinaryReader br, ref int methodId, ref double time)
        {
            methodId = (int)br.ReadUInt32();
            time = br.ReadMethodTime();
        }
        
        public static void BeginFuncNameDecode(BinaryReader br, ref int methodId, ref double time)
        {
            int len = br.ReadInt32();
            time = br.ReadMethodTime();

            //long start = br.BaseStream.Position;
            
            string methodName = br.ReadMethodName(len);
            MethodDataModel methodDataModel = ProfilerModel.Instance.GetProfilerMethodDataModel();
            methodId = methodDataModel.AddMethodName(methodName);

            //int copySize = ((len >> 2) + 1) << 2;
            //br.BaseStream.Position = start + copySize;
        }

        public static void EndDecode(BinaryReader br, ref double time)
        {
            br.BaseStream.Position += 4;
            time = br.ReadMethodTime();
        }
    }

    public class MethodNormalInfo
    {
        public static void BeginFuncDecode(BinaryReader br, ref int methodId)
        {
            br.BaseStream.Position -= 3;
            methodId = (int)br.ReadUInt32();
        }
        
        public static void EndDecode(BinaryReader br)
        {
            br.BaseStream.Position -= 3;
        }
    }

    public class FrameMethodInfo
    {
        public static void BeginDecode(BinaryReader br, ref uint frameCount, ref double frameTime)
        {
            frameCount = br.ReadUInt32();
            frameTime = -br.ReadMethodTime();
        }

        public static void EndDecode(BinaryReader br, ref double frameTime)
        {
            br.BaseStream.Position += 4;
            frameTime += br.ReadMethodTime();
        }
    }
}


