using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Profiling;

namespace LightProfiler
{
    // 一帧内线程的内存分配状况
    public class MonoThreadStatus : MonoStatus
    {
        public MonoThreadStatus(ulong threadId)
        {
            m_threadId = threadId;
        }

        public void SetFrameCount(int frameCount)
        {
            m_frameCount = frameCount;
        }
        
        public void AddSize(MonoDataType dataType, ulong size)
        {
            switch (dataType)
            {
                case MonoDataType.MallocSmallSize:
                    m_smallSizeAlloc += size;
                    break;
                case MonoDataType.FreeSmallSize:
                    m_smallSizeFree += size;
                    break;
                case MonoDataType.MallocBigSize:
                    m_bigSizeAlloc += size;
                    break;
                case MonoDataType.FreeBigSize:
                    m_bigSizeFree += size;
                    break;
                case MonoDataType.AllocHBLK:
                    m_allocHblk += size;
                    break;
                case MonoDataType.FreeHBLK:
                    m_freeHblk += size;
                    break;
                case MonoDataType.ExpandHeap:
                    m_expandSize += size;
                    break;
                case MonoDataType.PTRFREE:
                    m_allocPtr += size;
                    break;
                case MonoDataType.OBJECT:
                    m_allocObj += size;
                    break;
                case MonoDataType.TYPED:
                    m_allocType += size;
                    break;
                default:
                    break;
            }
        }
    }
}


