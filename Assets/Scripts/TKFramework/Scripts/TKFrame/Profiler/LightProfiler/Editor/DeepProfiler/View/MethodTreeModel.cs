using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace LightProfiler
{
    public class MethodTreeModel
    {
        private MethodRootElement m_rootData;
        private MethodRootElement m_searchData;
        private Stack<MethodDataTreeElement> m_searchStack = new Stack<MethodDataTreeElement>();
        private List<MethodDataTreeElement> m_allData = new List<MethodDataTreeElement>();

        private HashSet<int> m_expandId = new HashSet<int>();

        private MethodRootElement m_showData;

        public MethodRootElement rootData
        {
            get { return m_showData; }
        }

        private int m_MaxID = 0;
        private int m_frameCount = 0;

        public bool isSearch
        {
            get;
            private set;
        }

        public Action DataChanged;

        private static ProfilerCountCompare m_countCompareUp = new ProfilerCountCompare(true);
        private static ProfilerCountCompare m_countCompareDown = new ProfilerCountCompare(false);
        private static ProfilerTimeCompare m_timeCompareUp = new ProfilerTimeCompare(true);
        private static ProfilerTimeCompare m_timeCompareDown = new ProfilerTimeCompare(false);
        private static ProfilerNameCompare m_nameCompare = new ProfilerNameCompare();

        #region 初始化和清理

        public MethodTreeModel()
        {
            m_rootData = new MethodRootElement();
            m_searchData = new MethodRootElement();
            m_showData = m_rootData;
        }

        public void Clear()
        {
            m_MaxID = 0;
            m_rootData.children.Clear();
            m_searchData.children.Clear();
            m_allData.Clear();
            m_searchStack.Clear();
        }

        #endregion

        #region 生成数据

        public void SetDataSource(ProfilerFrameStatus profilerFrameStatus)
        {
            if (profilerFrameStatus == null)
            {
                Clear();
                if (DataChanged != null)
                    DataChanged();
                return;
            }

            int currentFrameCount = (int) profilerFrameStatus.frameCount;
            if (m_frameCount != currentFrameCount)
            {
                Clear();
                GenerateData(profilerFrameStatus);
                if (DataChanged != null)
                    DataChanged();
                m_frameCount = currentFrameCount;
            }
        }

        private void GenerateData(ProfilerFrameStatus profilerFrameStatus)
        {
            MethodCallStackTree callStackTree = profilerFrameStatus.methodCallStackTree;
            MethodCallNode child = callStackTree.root.child;
            while (child != null)
            {
                MethodDataTreeElement childElement = new MethodDataTreeElement(child, GetNextID(), null);
                m_allData.Add(childElement);
                m_rootData.children.Add(childElement);
                AddChild(child, childElement);

                child = child.next;
            }
        }

        private void AddChild(MethodCallNode node, MethodDataTreeElement treeElement)
        {
            MethodCallNode child = node.child;
            while (child != null)
            {
                MethodDataTreeElement childElement = new MethodDataTreeElement(child, GetNextID(), treeElement);
                m_allData.Add(childElement);
                treeElement.children.Add(childElement);
                AddChild(child, childElement);

                child = child.next;
            }
        }

        #endregion

        #region 排序数据

        public void SortData(bool ascending, int sortColunm)
        {
            IComparer<MethodDataTreeElement> comparer = GetCompare(ascending, sortColunm);
            if (comparer != null)
            {
                m_showData.children.Sort(comparer);
                foreach (MethodDataTreeElement child in m_showData.children)
                {
                    SortChildren(child, comparer);
                }
            }
        }

        private void SortChildren(MethodDataTreeElement rootData, IComparer<MethodDataTreeElement> comparer)
        {
            if (rootData.hasChildren)
            {
                rootData.children.Sort(comparer);
                foreach (MethodDataTreeElement child in rootData.children)
                {
                    SortChildren(child, comparer);
                }
            }
        }

        private IComparer<MethodDataTreeElement> GetCompare(bool ascending, int sortColunm)
        {
            IComparer<MethodDataTreeElement> result = null;
            switch (sortColunm)
            {
                case 1:
                    result = ascending ? m_countCompareUp : m_countCompareDown;
                    break;
                case 2:
                    result = ascending ? m_timeCompareUp : m_timeCompareDown;
                    break;
                default:
                    break;
            }

            return result;
        }

        #endregion

        #region 搜索数据
        
        public void Search(string searchWord)
        {
            if (!string.IsNullOrEmpty(searchWord))
            {
                isSearch = true;
                m_searchData.children.Clear();
                m_searchStack.Clear();
                
                foreach (MethodDataTreeElement child in m_rootData.children)
                {
                    m_searchStack.Push(child);
                }
            
                while (m_searchStack.Count > 0)
                {
                    MethodDataTreeElement current = m_searchStack.Pop();
                    if (current.displayName.IndexOf(searchWord, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        m_searchData.children.Add(current);
                    }
                
                    if (current.children != null && current.children.Count > 0)
                    {
                        foreach (var element in current.children)
                        {
                            m_searchStack.Push(element);
                        }
                    }
                }

                m_searchData.children.Sort(m_nameCompare);

                m_showData = m_searchData;
            }
            else
            {
                isSearch = false;
                m_showData = m_rootData;
            }
            
            if (DataChanged != null)
                DataChanged();
        }

        #endregion
        
        #region 查找调用

        public int GetMethodId(int selectedId)
        {
            return m_allData[selectedId].methodId;
        }

        public HashSet<int> GetAllMethodStack(int methodId)
        {
            m_expandId.Clear();
            foreach (MethodDataTreeElement element in m_allData)
            {
                if (element.methodId == methodId)
                {
                    MethodDataTreeElement current = element;
                    while (current.parent != null)
                    {
                        m_expandId.Add(current.parent.id);
                        current = current.parent;
                    }
                }
            }

            return m_expandId;
        }
        
        #endregion

        private int GetNextID()
        {
            return m_MaxID++;
        }
    }

    #region 排序算法
    
    public class ProfilerNameCompare : IComparer<MethodDataTreeElement>
    {
        public int Compare(MethodDataTreeElement x, MethodDataTreeElement y)
        {
            return EditorUtility.NaturalCompare (x.displayName, y.displayName);
        }
    }

    public class ProfilerCountCompare : IComparer<MethodDataTreeElement>
    {
        private bool m_ascending = true;

        public ProfilerCountCompare(bool ascending)
        {
            m_ascending = ascending;
        }

        public int Compare(MethodDataTreeElement x, MethodDataTreeElement y)
        {
            int result = x.count.CompareTo(y.count);
            if (!m_ascending)
                result = -result;
            return result;
        }
    }

    public class ProfilerTimeCompare : IComparer<MethodDataTreeElement>
    {
        private bool m_ascending = true;

        public ProfilerTimeCompare(bool ascending)
        {
            m_ascending = ascending;
        }

        public int Compare(MethodDataTreeElement x, MethodDataTreeElement y)
        {
            int result = x.time.CompareTo(y.time);
            if (!m_ascending)
                result = -result;
            return result;
        }
    }

    #endregion

    public class MethodRootElement
    {
        public int m_ID = 0;
        public string m_root = "root";
        public List<MethodDataTreeElement> m_Children = new List<MethodDataTreeElement>();

        public int id
        {
            get { return m_ID; }
        }

        public string displayName
        {
            get { return m_root; }
        }

        public List<MethodDataTreeElement> children
        {
            get { return m_Children; }
        }

        public bool hasChildren
        {
            get { return m_Children.Count > 0; }
        }
    }

    public class MethodDataTreeElement : TreeElement
    {
        public int count;
        public double time;
        public string TimeShow;
        public double selfTime;
        public string selfTimeShow;
        public int methodId;
        public bool isMeasure = true;

        private List<MethodDataTreeElement> m_Children = new List<MethodDataTreeElement>();
        private MethodDataTreeElement m_parent;

        public MethodDataTreeElement(string msg, int id) : base(msg, id)
        {
        }

        public MethodDataTreeElement(MethodCallNode callNode, int id, MethodDataTreeElement parent) : base(MethodCallNodeUtil.GetName(callNode), id)
        {
            count = callNode.count;
            time = callNode.totalTime;
            TimeShow = time.ToString();
            selfTime = callNode.selfTime;
            selfTimeShow = selfTime.ToString();
            methodId = callNode.methodId;
            isMeasure = MethodCallNodeUtil.isMeasure(callNode);
            m_parent = parent;
        }

        public List<MethodDataTreeElement> children
        {
            get { return m_Children; }
        }

        public bool hasChildren
        {
            get { return m_Children.Count > 0; }
        }

        public MethodDataTreeElement parent
        {
            get { return m_parent; }
        }
    }
}