using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using UnityEngine;

namespace LightProfiler 
{
    public class NetClient : IDisposable 
    {
        private TcpClient tcp_client = null;
        public Action<bool> connectCallback;
        
        public bool IsConnected {
            get {
                if (tcp_client != null) {
                    return tcp_client.Connected;
                }
                return false;
            }
        }

        public NetworkStream GetStream
        {
            get
            {
                if (tcp_client != null && tcp_client.Connected)
                {
                    return tcp_client.GetStream();
                }

                return null;
            }
        }

        public void Connect(string host, int port) {
            if (IsConnected) {
                return;
            }
            tcp_client = new TcpClient();
            tcp_client.BeginConnect(host, port, OnConnect, null);
        }

        public void Disconnect() {
            if (tcp_client != null) {
                tcp_client.Close();
                tcp_client = null;
            }
        }

        private void OnConnect(IAsyncResult asyncResult) {
            try
            {
                bool isConnect = false;
                if (tcp_client.Connected) {
                    Debug.Log("connect successfully");
                    Debug.Log("thread Id " + Thread.CurrentThread.ManagedThreadId);
                    isConnect = true;
                }

                if (connectCallback != null)
                {
                    connectCallback(isConnect);
                }
            }
            catch (Exception ex) {
                Debug.LogException(ex);
            }
        }

        public void Dispose() {
            Disconnect();
        }

    }

}
