using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using LightProfiler;
using UnityEditor;
using UnityEngine;

namespace LightProfiler
{
    public class ProfilerFrameModel : ProfilerTypeLineModel, IModelInterface
    {
        private List<EditorFrameStats> m_allFrameStates = new List<EditorFrameStats>();
        private List<ProfilerLineData> m_allLineDatas = new List<ProfilerLineData>();
        public FrameCountModel frameCountModel = new FrameCountModel();
        private int m_totalGCCount = 0;

        public List<ProfilerLineData> allLineDatas
        {
            get { return m_allLineDatas; }
        }

        public List<EditorFrameStats> allFrameStates
        {
            get { return m_allFrameStates; }
        }

        public int totalGCCount
        {
            get { return m_totalGCCount; }
        }

        public bool Update { get; set; } = false;
        
        public EditorFrameStats this[int index]
        {
            get { return m_allFrameStates[index]; }
        }

        public bool LoadData(string path, Action<float, int> progressCallback)
        {
            ProfilerPointModel pointModel = ProfilerModel.Instance.GetProfilerPointModel();
            //ProfilerHistoryModel historyModel = ProfilerModel.Instance.GetHistoryModel(); 
            
            Clear();
            pointModel.Clear();
            
            //historyModel.AddOneHistory(path);

            try
            {
                //读取并解析数据
                using (var stream = File.OpenRead(path))
                {
                    using (var br = new BinaryReader(stream))
                    {
                        br.ReadString();

                        //帧数量
                        int count = br.ReadInt32();

                        EditorFrameStats prevFrameStats = null;
                        
                        pointModel.Init();
                        //每帧的具体数据
                        for (int i = 0; i < count; i++)
                        {
                            var editorFrameStats = new EditorFrameStats();
                            editorFrameStats.Decode(br);
                            
                            //historyModel.Decode(editorFrameStats.rawData);
                            
                            pointModel.Decode(editorFrameStats.rawData, i);
                            m_allFrameStates.Add(editorFrameStats);

                            if (pointModel.CalculateGC(prevFrameStats, editorFrameStats, i))
                            {
                                m_totalGCCount++;
                            }
                            CalculatePrevFrameRate(prevFrameStats, editorFrameStats);
                            prevFrameStats = editorFrameStats;

                            if (progressCallback != null)
                            {
                                float ratio = (float) stream.Position / stream.Length;
                                progressCallback(ratio, i);
                            }
                        }

                        CalculateLastFrameRate();
                    }
                }

                frameCountModel.totalFrameCount = m_allFrameStates.Count;
                frameCountModel.startFrameId = m_allFrameStates[0].rawData.frameCount;
                frameCountModel.endFrameId = m_allFrameStates[m_allFrameStates.Count - 1].rawData.frameCount;

                pointModel.frameCountModel = frameCountModel;

                LoadLineData();
                
                                        
                pointModel.Update = true;
                Update = true;

                System.GC.Collect();
                
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError(e);
                return false;
            }
        }

        #region 计算帧率

        private void CalculatePrevFrameRate(EditorFrameStats prevFrame, EditorFrameStats currentFrame)
        {
            if (prevFrame != null)
            {
                ulong delta = currentFrame.rawData.startFrameTime - prevFrame.rawData.startFrameTime;
                float deltaTime = delta / 1000.0f;

                prevFrame.frameRate = 1000.0f / deltaTime;
            }
        }
        
        private void CalculateLastFrameRate()
        {
            int count = m_allFrameStates.Count;
            EditorFrameStats lastFrame = m_allFrameStates[count - 1];
            
            ulong delta = lastFrame.rawData.endFrameTime - lastFrame.rawData.startFrameTime;
            float deltaTime = delta / 1000.0f;

            lastFrame.frameRate = 1000.0f / deltaTime;
        }

        #endregion

        #region 初始化曲线信息
        private void LoadLineData()
        {
            if (m_allFrameStates != null)
            {
                ProfilerLineDataGroup group = new ProfilerLineDataGroup();
                group.threadId = 0;
                group.data = m_allFrameStates;
                group.startFrameId = frameCountModel.startFrameId;
                group.endFrameId = frameCountModel.endFrameId;
                
                group.formatStr = "<color=#{0}>{1}:{2:0.00}ms</color>";
                group.scale = 1;
                group.peak = 100;
                group.AddEditor("frameTime");
                group.Add("frameThreadTime");
                group.Add("gfxThreadTime");
                group.Fill(m_allLineDatas);

                group.formatStr = "<color=#{0}>{1}:{2:0.00}</color>";
                group.scale = 1;
                group.peak = 120;
                group.AddEditor("frameRate");
                group.Fill(m_allLineDatas);

                group.formatStr = "<color=#{0}>{1}:{2}</color>";
                group.scale = 1;
                group.peak = 400;
                group.Add("batches");
                group.Add("drawCalls");
                group.Add("setPassCalls");
				group.Add("staticBatchDrawCalls");
                group.Add("particleBatches");
                group.AddArray("layerBatches");
                group.Fill(m_allLineDatas);

                group.formatStr = "<color=#{0}>{1}:{2:0.0}K</color>";
                group.scale = 1000;
                group.peak = 300;
                group.Add("triangles");
                group.Add("vertices");
                group.Fill(m_allLineDatas);

                group.formatStr = "<color=#{0}>{1}:{2:0.00MB}</color>";
                group.scale = 1024 * 1024;
                group.peak = 300;
                group.Add("reservedTextureMemorySize");
                group.Add("textureUploadBytes");
                group.Add("vboTotalBytes");
                group.Add("renderTextureBytes");
                group.Add("monoHeapSize");
                group.Add("monoUsedSize");
                group.Fill(m_allLineDatas);
            }
        }
        #endregion
        
        
        public void Clear()
        {
            m_allFrameStates.Clear();
            m_allLineDatas.Clear();
            m_totalGCCount = 0;
        }

    }
}