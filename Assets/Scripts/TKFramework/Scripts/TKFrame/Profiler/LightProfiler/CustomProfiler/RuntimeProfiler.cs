#if ACGGAME_CLIENT
//#pragma warning disable 0219
//#pragma warning disable 0414
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using TKFrame;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace LightProfiler
{

    public class RuntimeProfiler : MonoBehaviour
    {
        private static RuntimeProfiler m_Instance;

        public static string logDir = TKApplication.StoragePath + "Profiler/";

        private static bool m_ProfileEnabled = false;
        public static bool profileEnabled
        {
            get
            {
                return m_ProfileEnabled;
            }
            private set
            {
                if (m_ProfileEnabled != value)
                {
                    m_ProfileEnabled = value;
#if ENABLE_CUSTOM_PROFILER
                    CustomProfiler.SetEnabled(value);
#endif
                }
            }
         }

        private static bool m_TraceEnabled;
        public static bool traceEnabled
        {
            get
            {
                return m_TraceEnabled;
            }
            set
            {
                if (m_TraceEnabled != value)
                {
                    m_TraceEnabled = value;
#if ENABLE_CUSTOM_PROFILER
                    CustomProfiler.SetTraceEnabled(value);
#endif
                }
            }
        }

#region 外部接口
        [Conditional("ENABLE_CUSTOM_PROFILER")]
        public static void Init()
        {
            if (m_Instance == null)
            {
                GameObject go = new GameObject("LightProfiler");
                DontDestroyOnLoad(go);
                m_Instance = go.AddComponent<RuntimeProfiler>();
            }
        }

        [Conditional("ENABLE_CUSTOM_PROFILER")]
        public static void Start(string description)
        {
            if (m_Instance != null)
            {
                m_Instance.StartInternal(description);
            }
        }

        [Conditional("ENABLE_CUSTOM_PROFILER")]
        public static void Stop()
        {
            if (m_Instance != null)
            {
                m_Instance.StopInternal();
            }
        }

#if UNITY_ANDROID
        [Conditional("ENABLE_CUSTOM_PROFILER")]
#else
        [Conditional("_")]
#endif
        internal static void BeginTrace(string name)
        {
            if (traceEnabled && m_Instance != null)
            {
#if ENABLE_CUSTOM_PROFILER
                CustomProfiler.BeginTrace(name);
#endif
            }
        }

#if UNITY_ANDROID
        [Conditional("ENABLE_CUSTOM_PROFILER")]
#else
        [Conditional("_")]
#endif
        internal static void EndTrace()
        {
            if (traceEnabled && m_Instance != null)
            {
#if ENABLE_CUSTOM_PROFILER
                CustomProfiler.EndTrace();
#endif
            }
        }

        /// <summary>
        /// 最长15个字符，自动截断
        /// </summary>
        [Conditional("ENABLE_CUSTOM_PROFILER")]
        public static void SetThreadName(string name)
        {
#if ENABLE_CUSTOM_PROFILER
            CustomProfiler.SetThreadName(name);
#endif
        }

        [Conditional("ENABLE_CUSTOM_PROFILER")]
        public static void AddPoint(string name)
        {
            if (profileEnabled && m_Instance != null)
            {
                SwapData.SetPointName(name);
            }
        }
#endregion

        private YieldInstruction m_WaitForEnd;
        
        //当前状态 0-空闲 1-运行 2-即将结束
        private int m_State;

        private AutoResetEvent m_Signal;

        private WriteThread m_writeThread;
        private BufferQueue m_bufferQueue;
        private BufferPool m_bufferPool;

        private void Awake()
        {
            m_Instance = GetComponent<RuntimeProfiler>();
            m_WaitForEnd = new WaitForEndOfFrame();

            m_Signal = new AutoResetEvent(false);
            m_bufferQueue = new BufferQueue();
            m_bufferPool = new BufferPool();
            m_writeThread = new WriteThread(m_bufferPool, m_bufferQueue, m_Signal);

            SwapData.Init();

            m_writeThread.activeSceneName = SceneManager.GetActiveScene().name;
            SceneManager.activeSceneChanged += OnSceneChanged;
        }

        void OnDestroy()
        {
            SceneManager.activeSceneChanged -= OnSceneChanged;
        }

        private void OnSceneChanged(Scene current, Scene next)
        {
            m_writeThread.activeSceneName = next.name;
        }

        private void StartInternal(string description)
        {
            if (m_State == 0 && m_writeThread.isActive())
            {
                profileEnabled = true;
                traceEnabled = true;
                m_State = 1;
                m_Signal.Reset();

                StartCoroutine(Tick(Time.frameCount));
                m_writeThread.Start(description);
            }
        }

        private void StopInternal()
        {
            if (m_State == 1)
            {
                m_State = 2;
            }
        }

        IEnumerator Tick(int startFrame)
        {
            while (Time.frameCount == startFrame)
            {
                yield return null;
            }

            FrameStats frameStats = null;

            while (true)
            {
                yield return m_WaitForEnd;
                
                if (frameStats != null)
                {
                    // 获取上一帧的数据
                    frameStats.LateCapture();
                    
                    SwapData.SwapPointName(frameStats);

                    m_bufferQueue.Enqueue(frameStats);

                    m_Signal.Set();

                    if (m_State == 2)
                    {
                        profileEnabled = false;
                        traceEnabled = false;
                        m_writeThread.Stop();
                        m_State = 0;
                        break;
                    }
                }
                
                frameStats = m_bufferPool.GetFrameStats();
                // 获取当前帧的数据  
                frameStats.Capture();
            }

            m_Signal.Set();
        }
    }

}

#else
namespace LightProfiler
{
    public class RuntimeProfiler 
    {
        public static void Begin(string name)
        {
         
        }
        public static void AddPoint(string name)
        {

        }
        public static void End()
        {
          
        }

    }
}
#endif