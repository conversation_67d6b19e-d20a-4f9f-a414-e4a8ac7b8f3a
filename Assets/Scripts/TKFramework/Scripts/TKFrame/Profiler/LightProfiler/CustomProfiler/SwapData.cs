#if ACGGAME_CLIENT 
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LightProfiler
{
    public static class SwapData
    {
        private static Queue<string>[] m_PointName;

        public static void Init()
        {
            m_PointName = new Queue<string>[2];
            m_PointName[0] = new Queue<string>(4);
            m_PointName[1] = new Queue<string>(4);
        }

        public static void SwapPointName(FrameStats frameStats)
        {
            int i = frameStats.frameCount & 1;
            frameStats.pointName.Clear();
            frameStats.pointName.AddRange(m_PointName[i]);
            m_PointName[i].Clear();
        }

        public static void SetPointName(string name)
        {
            int i = Time.frameCount & 1;
            m_PointName[i].Enqueue(name);
        }
    }
}



#endif