#if ACGGAME_CLIENT 
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Threading;

namespace LightProfiler
{
    public class WriteThread
    {
        private string m_TempDir;
        private string m_TempPath;

        private BufferPool m_bufferPool;
        private BufferQueue m_bufferQueue;

        private Thread m_FileThread;
        private AutoResetEvent m_Signal;

        private bool m_stop = false;

        public string activeSceneName
        {
            private get;
            set;
        }

        public WriteThread(BufferPool bufferPool, BufferQueue bufferQueue, AutoResetEvent signal)
        {
            m_TempDir = RuntimeProfiler.logDir;
            m_TempPath = m_TempDir + "LightProfilerTemp.data";

            m_bufferPool = bufferPool;
            m_bufferQueue = bufferQueue;
            m_Signal = signal;
        }

        public bool isActive()
        {
            return m_FileThread == null || !m_FileThread.IsAlive;
        }

        public void Start(string description)
        {
            m_stop = false;
            m_FileThread = new Thread(new ParameterizedThreadStart(WriteToFile));
            m_FileThread.Name = "ProfilerWriter";
            m_FileThread.Start(description);
        }

        public void Stop()
        {
            m_stop = true;
        }

        private void WriteToFile(object des)
        {
            string description = des as string;

            if(string.IsNullOrEmpty(description))
            {
                return;
            }

            ClearOldFiles(m_TempDir);
            if (File.Exists(m_TempPath))
            {
                File.Delete(m_TempPath);
            }

            using (var stream = File.OpenWrite(m_TempPath))
            using (var bw = new BinaryWriter(stream))
            {
                //描述信息
                bw.Write(description);

                //帧数预留
                int count = 0;
                long countPos = stream.Position;
                bw.Write(count);

                //每帧数据
                while (true)
                {
                    FrameStats frameStats = null;

                    frameStats = m_bufferQueue.Dequeue();

                    if (frameStats != null)
                    {
                        count++;

                        frameStats.Serialize(bw);

                        m_bufferPool.Recycle(frameStats);
                    }
                    else if (!m_stop)
                    {
                        m_Signal.WaitOne();
                    }
                    else if (m_bufferQueue.Count() == 0)
                    {
                        break;
                    }
                }

                //帧数
                stream.Position = countPos;
                bw.Write(count);
                stream.Position = stream.Length;
            }

            string scene = activeSceneName;
            string timeStr = DateTime.Now.ToString("yyyy.MM.dd__HH-mm-ss");

            //这里平台名字后缀android和ios有用，后面最好不要再改了，之前老版本记录的数据没有这个，参考FrameStats.IsNewRecord
            string platform = "";

#if UNITY_EDITOR
            platform = "editor";
#elif UNITY_ANDROID
            platform = "android";
#elif UNITY_IOS
            platform = "ios";  
#endif

            string name = string.Format("[{0}]{1}_{2}.data", scene, timeStr, platform);
            string path = Path.Combine(m_TempDir, name);
            File.Move(m_TempPath, path);

        }

        private void ClearOldFiles(string dir)
        {
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
                return;
            }

            var files = Directory.GetFiles(dir, "*.data", SearchOption.TopDirectoryOnly);
            if (files.Length > 20)
            {
                string oldest = "";
                double max = 0;
                foreach (var f in files)
                {
                    var days = ParseDays(f);
                    if (days > max)
                    {
                        max = days;
                        oldest = f;
                    }
                }

                if (File.Exists(oldest))
                {
                    File.Delete(oldest);
                }
            }
        }

        private double ParseDays(string path)
        {
            string name = Path.GetFileNameWithoutExtension(path);
            string[] numStr = name.Split(new char[] { '-', '.', '_', '!', '~' }, StringSplitOptions.RemoveEmptyEntries);
            if (numStr.Length == 6)
            {
                try
                {
                    int year = int.Parse(numStr[0]);
                    int month = int.Parse(numStr[1]);
                    int day = int.Parse(numStr[2]);
                    int hour = int.Parse(numStr[3]);
                    int minute = int.Parse(numStr[4]);
                    int second = int.Parse(numStr[5]);
                    DateTime time = new DateTime(year, month, day, hour, minute, second);
                    DateTime now = DateTime.Now;

                    return now.Subtract(time).TotalDays;
                }
                catch { }
            }
            return 0;
        }
    }
}



#endif