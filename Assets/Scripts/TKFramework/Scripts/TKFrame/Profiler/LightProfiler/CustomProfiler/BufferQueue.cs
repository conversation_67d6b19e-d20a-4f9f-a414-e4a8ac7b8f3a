#if ACGGAME_CLIENT 
using System.Collections.Generic;
using TKFrame;

namespace LightProfiler
{

    public class BufferPool
    {
        private TKStack<FrameStats> m_Pool;
        private object m_PoolLock;
        private int m_cacheNum = 5;

        public BufferPool()
        {
            m_Pool = new TKStack<FrameStats>(m_cacheNum);
            for (int i = 0; i < m_cacheNum; ++i)
                m_Pool.Push(new FrameStats());
            m_PoolLock = new object();
        }

        public FrameStats GetFrameStats()
        {
            FrameStats frameStats = null;
            lock (m_PoolLock)
            {
                if (m_Pool.Count > 0)
                {
                    frameStats = m_Pool.Pop();
                }
            }
            if (frameStats == null)
                frameStats = new FrameStats();
            return frameStats;
        }

        public void Recycle(FrameStats frameStats)
        {
            lock (m_PoolLock)
            {
                m_Pool.Push(frameStats);
            }
        }
    }

    public class BufferQueue
    {
        private Queue<FrameStats> m_Buffer;
        private object m_BufferLock;

        public BufferQueue()
        {
            m_Buffer = new Queue<FrameStats>(5);
            m_BufferLock = new object();
        }

        public void Enqueue(FrameStats frameStats)
        {
            lock (m_BufferLock)
            {
                m_Buffer.Enqueue(frameStats);
            }
        }

        public FrameStats Dequeue()
        {
            FrameStats frameStats = null;
            lock (m_BufferLock)
            {
                if (m_Buffer.Count > 0)
                {
                    frameStats = m_Buffer.Dequeue();
                }
            }
            return frameStats;
        }

        public int Count()
        {
            return m_Buffer.Count;
        }
    }
}

#endif