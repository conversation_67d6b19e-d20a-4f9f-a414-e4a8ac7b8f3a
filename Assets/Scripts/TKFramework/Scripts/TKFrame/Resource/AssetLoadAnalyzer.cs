#if UNITY_EDITOR

#define ASSEST_LOAD_ANALYZER

#endif

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame
{
    public class AssetLoadInfo
    {
        public string LoadTpye;
        public string AssetBundlePath;
        public string AssetName;
        public string AssetType;
        public float StartTime;
        public float CallBackStartTime;
        public float EndTime;
    }

    public class AssetLoadAnalyzer
    {
        public static List<AssetLoadInfo> ms_AssetLoadInfos = new List<AssetLoadInfo>();
        public static bool ms_started = false;
        public static float ms_startTime;
        public static float ms_finishedTime;

        public static void Start()
        {
#if ASSEST_LOAD_ANALYZER
            ms_AssetLoadInfos.Clear();
            ms_started = true;
            ms_startTime = Time.realtimeSinceStartup;
#endif
        }

        public static void Push(string loadType, string assetbundleName, string assetName, string assetType, float startTime, float endTime, float callbackStartTime)
        {
#if ASSEST_LOAD_ANALYZER
            if (ms_started)
            {
                ms_AssetLoadInfos.Add(new AssetLoadInfo()
                {
                    LoadTpye = loadType,
                    AssetBundlePath = assetbundleName,
                    AssetName = assetName,
                    AssetType = assetType,
                    StartTime = startTime,
                    CallBackStartTime = callbackStartTime,
                    EndTime = endTime
                });
            }
#endif
        }

        public static void Finish()
        {
#if ASSEST_LOAD_ANALYZER
            if (ms_started)
            {
                ms_started = false;
                ms_finishedTime = Time.realtimeSinceStartup;
                StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
                sb.AppendLine("LoadTpye, AssetBundle, Asset, Type, StartTime, LoadFinishedTime, EndTime, Elapsed, CallbackElapsed");
                sb.AppendLine($",,,Total,{ms_startTime}, , {ms_finishedTime}, {ms_finishedTime - ms_startTime}");
                foreach (var item in ms_AssetLoadInfos)
                {
                    sb.AppendLine($"{item.LoadTpye}, {item.AssetBundlePath}, {item.AssetName}, {item.AssetType}, {item.StartTime}, {item.CallBackStartTime}, {item.EndTime}, {item.EndTime - item.StartTime}, {item.EndTime - item.CallBackStartTime}");
                }
                File.WriteAllText(Application.dataPath + "/../AssetLoadTime.csv", sb.ToString());
                MicroObjectPool<StringBuilder>.Release(sb);
            }
#endif
        }
    }
}
