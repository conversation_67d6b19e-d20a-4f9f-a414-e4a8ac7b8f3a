using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace TKFrame
{
    public class AssetBundleDataBaseBuilder
    {
        public class AssetBundleInfo
        {
            public string[] abDependence;
            public bool isUnload;
        }
        
        private TKDictionary<string, AssetBundleInfo> m_allABDependences;
        private string[] m_emptyDependencies = new string[0];

        public void ReadAndBuild(string path)
        {
            using (Stream fs = FileManager.Instance.GetReadStream(path))
            {
                ReadAndBuild(fs);
            }
        }
        
        public void ReadAndBuild(byte[] data)
        {
            using (MemoryStream stream = new MemoryStream(data))
            {
                ReadAndBuild(stream);
            }
        }

        public void ReadAndBuild(Stream stream)
        {
            using (CodingStream codingStream = new CodingStream(stream, CodingStream.CodingMethod.Decode))
            {
                using (BinaryReader br = new BinaryReader(codingStream))
                {
                    ReadAndBuild(br);
                }
            }
        }

        private void ReadAndBuild(BinaryReader br)
        {
            try
            {
                // 先读AB的string列表
                int count = br.ReadUInt16();
                List<string> allAB = new List<string>(count);
                    
                for (int i = 0; i < count; i++)
                {
                    string ab = br.ReadString();
                    allAB.Add(ab);
                }

                // 生成AB依赖数据
                m_allABDependences = new TKDictionary<string, AssetBundleInfo>();
                for (int i = 0; i < count; i++)
                {
                    // 获取AB名字
                    int abId = br.ReadUInt16();
                    string abName = allAB[abId];
                    
                    AssetBundleInfo assetBundleInfo = new AssetBundleInfo();
                    m_allABDependences.Add(abName, assetBundleInfo);

                    int dependenceCount = br.ReadUInt16();
                    
                    int isUnload = br.ReadInt16();
                    assetBundleInfo.isUnload = isUnload == 1;
                    
                    if (dependenceCount > 0)
                    {
                        string[] abDependence = new string[dependenceCount];

                        for (int j = 0; j < dependenceCount; j++)
                        {
                            int dependenceId = br.ReadUInt16();
                            abDependence[j] = allAB[dependenceId];
                        }
                        
                        assetBundleInfo.abDependence = abDependence;
                    }
                    else
                    {
                        assetBundleInfo.abDependence = m_emptyDependencies;
                    }
                }
                
                Debug.Log("Load AssetBundleDataBase Success");
            }
            catch (Exception e)
            {
                Debug.Log("Load AssetBundleDataBase Failed");
                Debug.LogError(e);
                Clear();
                
                throw e;
            }
        }

        public string[] GetABDependencies(string abName)
        {
            AssetBundleInfo result = null;
            if (m_allABDependences.TryGetValue(abName, out result))
            {
                return result.abDependence;
            }
            return m_emptyDependencies;
        }

        public bool GetABUnloadable(string abName)
        {
            AssetBundleInfo result = null;
            if (m_allABDependences.TryGetValue(abName, out result))
            {
                return result.isUnload;
            }
            return false;
        }

        public void Clear()
        {
            if(m_allABDependences != null)
                m_allABDependences.Clear();
        }

        public bool isAvaliable()
        {
            if (m_allABDependences != null && m_allABDependences.Count > 0)
                return true;
            return false;
        }
    }
}

