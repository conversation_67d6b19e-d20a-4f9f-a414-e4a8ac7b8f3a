using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.IO.MemoryMappedFiles;
using System.Text;
using TKPlugins;
using UnityEngine;

namespace TKFrame
{
    /// <summary>
    /// 游戏Assetbundle资源信息
    /// </summary>
    public class TKAssetDatabase
    {
        
        private static TKAssetDatabase _instance;
        public static TKAssetDatabase Instance
        {
            get
            {
                return _instance;
            }
        }
        
        /// <summary>
        /// 资源版本号
        /// </summary>
        private int m_resourceVersion = 0;

        public int ResourceVersion
        {
            get
            {
                return m_resourceVersion;
            }
            set
            {
                m_resourceVersion = value;
            }
        }
        
        public BinaryReader _assetDatabaseBR = null;
        private TKDictionary<string, TKAssetbundleDesc> _assetDatabaseDict = new TKDictionary<string, TKAssetbundleDesc>();
        
        public BinaryReader _assetDatabaseExternalBR = null;
        private TKDictionary<string, TKAssetbundleDesc> _assetDatabaseDictExternal = new TKDictionary<string, TKAssetbundleDesc>();
        
        static TKAssetDatabase()
        {
            _instance = new TKAssetDatabase();
        }

        public TKAssetbundleDesc GetAssetbundleDesc(string assetbundle, bool external = false, bool isLoadingAssetBundleManifest = false)
        {
            TKAssetbundleDesc assetbundleDesc = null;

#if ENABLE_ASSET_BUNDLE_EXTEND
            if(isLoadingAssetBundleManifest == false)
            {
                if (assetbundle.Contains(".unity3d") == false)
                    assetbundle += ".unity3d";
            }
#endif
            
#if DESENSITIZATIONPACK
            Diagnostic.Log($"#AssetBundleAllForDESENSITIZATIONPACK# {assetbundle}, {ZGame_Chess.Stage.Desensitization.DesensitizationConfig.IsABInWhiteList(assetbundle)}");
#endif
            var dict = external ? _assetDatabaseDictExternal : _assetDatabaseDict;
            if (dict.TryGetValue(assetbundle, out assetbundleDesc))
            {
                return assetbundleDesc;
            }

            return null;
        }

        public uint GetAssetBundleCRC(string assetbundle, bool external = false)
        {
            TKAssetbundleDesc assetbundleDesc = GetAssetbundleDesc(assetbundle, external);

            if (assetbundleDesc != null)
            {
                return assetbundleDesc.CRC;
            }

            return 0u;
        }

        //优先检测external中的资源，如果没有再加载StreamingAsset的
        public string GetAssetDesc(string assetbundle, string assetName, bool external = false, bool outputLog = true)
        {
#if ENABLE_ASSET_BUNDLE_EXTEND
            if(assetbundle.Contains(".unity3d") == false)
                assetbundle += ".unity3d";
#endif
            
            if(assetName==null||assetName.Length==0)
            {
#if UNITY_EDITOR
                Debug.LogError("assetName is empty at:" + assetbundle);
#endif
                return null;
            }
            TKAssetbundleDesc assetbundleDesc = GetAssetbundleDesc(assetbundle, external);
            if (assetbundleDesc == null)
            {
#if UNITY_EDITOR
                if (outputLog)
                    Debug.LogError("Assetbundle Path is Error:" + assetbundle);
#endif
                return null;
            }

            string assetDescRet = null;
			//if (_assetDatabaseDict.TryGetValue(assetbundle, out assetbundleDesc))
			{
				if (assetbundleDesc.Assets != null) {
                    //查找对应名称的asset
					for (int i = 0; i < assetbundleDesc.Assets.Length; i++) {
						if (assetbundleDesc.Assets [i] == assetName) {
							assetDescRet = assetbundleDesc.Assets [i];
							break;
						}	
					}
                    //忽略大小写查找对应名称的asset---暂时屏蔽，有性能问题
                    if (assetDescRet==null)
                    {
                        /*for (int i = 0; i < assetbundleDesc.Assets.Length; i++)
                        {
                            if (String.Equals(assetName, assetbundleDesc.Assets[i].AssetName, StringComparison.CurrentCultureIgnoreCase))
                            {
                                assetDescRet = assetbundleDesc.Assets[i];
#if UNITY_EDITOR
                                Debug.LogError("[Not match case at GetAssetDesc] assetName:" + assetName + " --- " + assetbundleDesc.Assets[i].AssetName);
#endif
                                break;
                            }
                        }*/
                    }
				}
			}
			return assetDescRet;
		}
        

        /// <summary>
        /// 从Text 文件解析AssetDatabase
        /// </summary>
        /// <param name="fileStr"></param>
        public void Load(Stream stream)
        {
            Diagnostic.Log("Start loading internal AssetDatabase.config");

            if (_assetDatabaseBR != null)
            {
                _assetDatabaseBR.Close();
                _assetDatabaseBR = null;
            }
            
            FileManager fileManager = FileManager.Instance;
            string oldConfigPath = fileManager.GetOldTmpFile("AssetDataBase");
            fileManager.CloseMMap(oldConfigPath);
            
            string configPath = FileManager.Instance.GetTmpFile("AssetDataBase");
            
            bool success = false;
#if UNITY_EDITOR
            int tryTimes = 0;
#endif
            do
            {
                if (!File.Exists(configPath))
                {
                    Diagnostic.Log("AssetDataBase_Internal ReGenerate Config " + configPath);
                    using (GZipStream gZipStream = new GZipStream(stream, CompressionMode.Decompress, true))
                    {
                        DownloadHandlerStreamTunnel.DecodeStreamToFile(gZipStream, configPath);
                    }
                }
                else
                {
                    Diagnostic.Log("AssetDataBase_Internal Use Cache Config " + configPath);
                }
                success = fileManager.CheckMMap(configPath);
                Diagnostic.Log("AssetDataBase_Internal Use Cache Config Check " + success);
                if (!success)
                {
                    fileManager.CloseMMap(configPath);
                    File.Delete(configPath);
                    stream.Position = 0;
                }
#if UNITY_EDITOR
                if (tryTimes > 5)
                {
                    Diagnostic.Log("AssetDataBase_Internal Use Cache Config Check Failed " + configPath);
                    Application.Quit(1);
                }
                tryTimes++;
#endif
            } while (!success);
            
            Stream configStream = fileManager.GetReadStream(configPath);
            _assetDatabaseBR = new BinaryReader(configStream);

            CreateDict(_assetDatabaseBR, _assetDatabaseDict, false);
           
        }

        /// <summary>
        /// 加载外部存储上Assetbundles配置
        /// </summary>
        public void LoadExternal(Stream stream)
        {
            if (_assetDatabaseExternalBR != null)
            {
                _assetDatabaseExternalBR.Close();
                _assetDatabaseExternalBR = null;
            }
            
            FileManager fileManager = FileManager.Instance;
            string oldConfigPath = fileManager.GetOldTmpFile("AssetDataBase_External");
            fileManager.CloseMMap(oldConfigPath);
            
            string cachePath = fileManager.GetTmpFile("AssetDataBase_External");

            bool success = false;
            do
            {
                if (!File.Exists(cachePath))
                {
                    Diagnostic.Log("AssetDataBase_External ReGenerate Config " + cachePath);
                    using (GZipStream gZipStream = new GZipStream(stream, CompressionMode.Decompress, true))
                    {
                        DownloadHandlerStreamTunnel.DecodeStreamToFile(gZipStream, cachePath);
                    }
                }
                else
                {
                    Diagnostic.Log("AssetDataBase_External Use Cache Config " + cachePath);
                }

                success = fileManager.CheckMMap(cachePath);
                Diagnostic.Log("AssetDataBase_External Use Cache Config Check " + success);
                if (!success)
                {
                    fileManager.CloseMMap(cachePath);
                    File.Delete(cachePath);
                    stream.Position = 0;
                }
            } while (!success);
            
            Stream configStream = fileManager.GetReadStream(cachePath);
            _assetDatabaseExternalBR = new BinaryReader(configStream);
            
            CreateDict(_assetDatabaseExternalBR, _assetDatabaseDictExternal, true);
        }

        private void CreateDict(BinaryReader br, TKDictionary<string, TKAssetbundleDesc> dict, bool external)
        {
            UnityEngine.Profiling.Profiler.BeginSample("Load Asset Bundle Desc");
            dict.Clear();

            long assetBundleDescOffset = br.ReadInt64();
            br.BaseStream.Seek(assetBundleDescOffset, SeekOrigin.Begin);

            int len = 0;
            TKAssetbundleDesc.ZipReadInt(ref m_resourceVersion, br);
            TKAssetbundleDesc.ZipReadInt(ref len, br);
            for (int i = 0; i < len; i++)
            {
                TKAssetbundleDesc assetDesc = new TKAssetbundleDesc();
                assetDesc.isExternal = external;
                string name = UniqueString.Intern(br.ReadString());
                assetDesc.ReadInfo(br);
                dict.Add(name, assetDesc);
            }
            UnityEngine.Profiling.Profiler.EndSample();

            TKAssetbundleDesc manifestDesc = new TKAssetbundleDesc();
            string manifestName = UniqueString.Intern(BaseLoader.GetPlatformFolderForAssetBundles());
            manifestDesc.CRC = external ? 1u : 0;
            dict.Add(manifestName, manifestDesc);

            Diagnostic.Log("Loading Success");
        }

    }

    /// <summary>
    /// 资源AssetBundle信息描述
    /// </summary>
    [Serializable]
    public class TKAssetbundleDesc
    {
        public long Offset;
        private string[] m_assets;
        public uint CRC;
        public bool isReadAsset = false;
        public bool isExternal;

        public string[] Assets
        {
            get
            {
                if (!isReadAsset)
                {
                    ReadAssets();
                }
                return m_assets;
            }
            set
            {
                m_assets = value;
            }
        }

        public void ReadInfo(BinaryReader br)
        {
            CRC = br.ReadUInt32();
            Offset = br.ReadInt64();
        }

        public void ReadAssets()
        {
            UnityEngine.Profiling.Profiler.BeginSample("Load Asset Bundle Desc Assets");
            BinaryReader br = null;
            if (!isExternal)
            {
                br = TKAssetDatabase.Instance._assetDatabaseBR;
            }
            else
            {
                br = TKAssetDatabase.Instance._assetDatabaseExternalBR;
            }
            
            br.BaseStream.Seek(Offset, SeekOrigin.Begin);
            int len = 0;
            ZipReadInt(ref len, br);
            if (len > -1)
            {
                m_assets = new string[len];
                for (int i = 0; i < len; i++)
                {
                    m_assets[i] = UniqueString.Intern(br.ReadString());
                }
            }
            else
            {
                m_assets = null;
            }

            isReadAsset = true;
            UnityEngine.Profiling.Profiler.EndSample();
        }
        
        public static void ZipReadInt(ref int data, BinaryReader br)
        {
            data = 0;
            int header = br.ReadByte();
            int type = header & 0x70;
            int symb = header & 0x80;
            int high = header & 0x0f;
            
            switch (type)
            {
                case 0x0:
                    data |= high;
                    break;
                case 0x10:
                    data |= high << 8;
                    data |= br.ReadByte();
                    break;
                case 0x20:
                    data |= high << 16;
                    data |= br.ReadByte() << 8;
                    data |= br.ReadByte();
                    break;
                case 0x30:
                    data |= high << 24;
                    data |= br.ReadByte() << 16;
                    data |= br.ReadByte() << 8;
                    data |= br.ReadByte();
                    break;
                case 0x40:
                    data |= br.ReadByte() << 24;
                    data |= br.ReadByte() << 16;
                    data |= br.ReadByte() << 8;
                    data |= br.ReadByte();
                    break;
            }

            if (symb > 0)
                data = -data;
        }
    }

#if UNITY_EDITOR
    
    [Serializable]
    public class TKAssetbundleDescForEditor
    {
        public string Name;
        public uint CRC;
        public long Offset;
        public TKAssetDesc[] Assets;
    }
    
    //单个资源运行时信息描述
    [Serializable]
    public class TKAssetDesc{
        public string AssetName;
        public int MemorySize;
        public long lastWriteTime;
    }
    
    /// <summary>
    /// 资源AssetBundle信息描述
    /// </summary>
    [Serializable]
    public class TKAssetbundleDescWithModifyTime
    {
        public TKAssetbundleDescForEditor Desc;
        public long lastWriteTime;
        public string[] assetsPaths;
    }
    
#endif

}
