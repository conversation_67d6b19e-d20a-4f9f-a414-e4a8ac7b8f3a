using UnityEngine;
using System.Collections;


namespace TKFrame
{
	/// <summary>
	/// 资源变体相关
	/// </summary>
	public static class ResourceVariante 
	{
		//当前资源变体后缀名
		private static string _variantName = string.Empty;

		public static void SetVarianteName( string variant )
		{
			_variantName = variant;
		}

		/// <summary>
		/// 检查资源对应变体是否存在，存在则对应的AssetBundle 及 AssetName 加上后缀名
		/// </summary>
		/// <returns><c>true</c>, if asset variante was checked, <c>false</c> otherwise.</returns>
		/// <param name="assetbundle">Assetbundle.</param>
		/// <param name="assetName">Asset name.</param>
		public static bool CheckAssetVariante( ref string assetbundle,ref string assetName )
		{
			string oldAssetbundle = assetbundle;
			string oldAssetName = assetName;
			if (string.IsNullOrEmpty (_variantName)) {
				return false;
			}

			bool exist = AssetBundleManager.CheckAssetBundleExist (oldAssetbundle + _variantName);
			if (exist ) {
				assetbundle = oldAssetbundle + _variantName;
				assetName = oldAssetName + _variantName;

				return true;
			} else {
				
				return false;
			}
		}
	}
}
