using System.Collections;
using System.Collections.Generic;
using System.IO;
using TKFrame;
using UnityEngine;
using UnityEngine.Networking;

public abstract class WebRequestLoadOperation : IEnumerator
{
    protected UnityWebRequest m_request;

    public object Current
    {
        get
        {
            return null;
        }
    }

    public bool MoveNext()
    {
        return Update();
    }

    public virtual void Reset()
    {
        if (m_request != null)
        {
            m_request.Dispose();
            m_request = null;
        }
    }

    private bool Update()
    {
        if (m_request == null)
        {
            if (!InitRequest())
            {
                Reset();
                return false;
            }
        }
        else
        {
            if (m_request.isHttpError || m_request.isNetworkError)
            {
                OnRequestError();
                Reset();
                return false;
            }
            else if (m_request.isDone)
            {
                OnRequestDone();
                Reset();
                return false;
            }
        }

        return true;
    }
    
    protected abstract bool InitRequest();
    protected abstract void OnRequestError();
    protected abstract void OnRequestDone();
}

public class AssetBundleDataBaseLoadOperation : WebRequestLoadOperation
{
    private string m_path = string.Empty;
    private AssetBundleDataBaseBuilder m_dataBaseBuilder;

    public AssetBundleDataBaseLoadOperation(string path, AssetBundleDataBaseBuilder dataBaseBuilder)
    {
        m_path = path;
        m_dataBaseBuilder = dataBaseBuilder;
    }

    protected override bool InitRequest()
    {
        m_request = UnityWebRequest.Get(m_path);
        m_request.SendWebRequest();
        return true;
    }

    protected override void OnRequestError()
    {
        m_dataBaseBuilder.Clear();
        Diagnostic.Log("Request Error");
    }

    protected override void OnRequestDone()
    {
        Diagnostic.Log("Request Done");
        using (Stream stream = FileManager.Instance.GetReadStream(m_request.downloadHandler))
        {
            m_dataBaseBuilder.ReadAndBuild(stream);
        }
    }

    public override void Reset()
    {
        base.Reset();
        m_path = string.Empty;
        m_dataBaseBuilder = null;
    }
}
