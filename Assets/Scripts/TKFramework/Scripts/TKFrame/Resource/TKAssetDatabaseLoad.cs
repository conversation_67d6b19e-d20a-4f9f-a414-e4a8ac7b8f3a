using System.IO;
using TKPlugins;

namespace TKFrame
{
    public class TKAssetDatabaseLoad : BaseFileLoad
    {
        public const string dataBase = "AssetDatabase.config";
        public const string dataBaseAB = "ed2fa777bbd47e3685a1856e17848239.unity3d";
        
        private static TKAssetDatabaseLoad m_instance = null;
        private static object m_locker = new object();

        public static TKAssetDatabaseLoad Instance
        {
            get
            {
                if (m_instance == null)
                {
                    lock (m_locker)
                    {
                        if (m_instance == null)
                        {
                            m_instance = new TKAssetDatabaseLoad();
                        }
                    }
                }
                return m_instance;
            }
        }
        
        protected override string GetDataName()
        {
            return "AssetBundleDataBaseMd5";
        }

        protected override string GetInternalDataPath()
        {
#if ENABLE_ASSET_BUNDLE_PATH_ENCRYPT
            return AssetBundleManager.BaseDownloadingURL + dataBaseAB;
#else
            return AssetBundleManager.BaseDownloadingURL + dataBase;
#endif
            
        }

        public override string GetExternalDataPath()
        {
#if ENABLE_ASSET_BUNDLE_PATH_ENCRYPT
            return AssetBundleManager.EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles()  + "/" + dataBaseAB;
#else
            return AssetBundleManager.EXTERNAL_FOLDER + BaseLoader.GetPlatformFolderForAssetBundles()  + "/" + dataBase;
#endif
        }

        protected override string GetEditorDataPath()
        {
            return string.Empty;
        }

        protected override string GetServerDataPath()
        {
            return string.Empty;
        }

        protected override void DecodeStream(Stream stream, LoadType loadType)
        {
            if(loadType == LoadType.Internal)
                TKAssetDatabase.Instance.Load(stream);
            else if (loadType == LoadType.External)
                TKAssetDatabase.Instance.LoadExternal(stream);
        }
    }

}

