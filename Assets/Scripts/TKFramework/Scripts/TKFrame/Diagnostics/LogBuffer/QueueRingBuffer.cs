using System;
using System.Threading;

namespace TKFrame
{
    public class QueueRingBuffer
    {
        private FixRingBuffer m_ringBuffer;
        private volatile int m_canWrite = 1;
        private volatile int m_canRead = 1;

        public int size
        {
            get { return m_ringBuffer.size(); }
        }

        public void StopRead()
        {
            Interlocked.CompareExchange(ref m_canRead, 0, 1);
        }
        
        public void StopWrite()
        {
            Interlocked.CompareExchange(ref m_canWrite, 0, 1);
        }

        public QueueRingBuffer()
        {
            m_ringBuffer = new FixRingBuffer();
        }

        public QueueRingBuffer(int capacity)
        {
            m_ringBuffer = new FixRingBuffer(capacity);
        }

        public bool WriteData(byte[] data, int start, int count)
        {
            int writtenSize = 0;

            while (writtenSize < count)
            {
                if (Interlocked.CompareExchange(ref m_canWrite, 0, 0) == 0)
                    return false;
                
                int currentWrite = count - writtenSize;

                int bufferStartIndex = m_ringBuffer.WriteSize(ref currentWrite);

                if (currentWrite == 0)
                {
                    Thread.Sleep(1);
                    continue;
                }
                
                Array.Copy(data, start + writtenSize, m_ringBuffer.Data, bufferStartIndex, currentWrite);
                
                m_ringBuffer.WriteIndexUpdate(currentWrite);

                writtenSize += currentWrite;
            }

            return true;
        }

        public bool ReadData(byte[] data, int start, int count)
        {
            int readSize = 0;

            while (readSize < count)
            {
                int currentRead = count - readSize;

                int bufferStartIndex = m_ringBuffer.ReadSize(ref currentRead);

                if (currentRead == 0)
                {
                    if (Interlocked.CompareExchange(ref m_canRead, 0, 0) == 0)
                        return false;
                    Thread.Sleep(1);
                    continue;
                }
                
                Array.Copy(m_ringBuffer.Data, bufferStartIndex, data, start + readSize, currentRead);

                m_ringBuffer.ReadIndexUpdate(currentRead);
                
                readSize += currentRead;
            }

            return true;
        }

        public void Clear()
        {
            m_ringBuffer.Clear();
            Interlocked.Exchange(ref m_canRead, 1);
            Interlocked.Exchange(ref m_canWrite, 1);
        }
    }
}