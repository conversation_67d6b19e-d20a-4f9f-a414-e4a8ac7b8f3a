//#define OPEN_DEBUG

using System;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace TKFrame
{
    /// <summary>
    /// 点击事件
    /// </summary>
    public class PointerClickListener : PointerEventListener, IPointerClickHandler, IPointerDownHandler, IDragHandler, IEndDragHandler, IBeginDragHandler,IPointerUpHandler
    {
       /// <summary>
        /// 点击事件----------短按
        /// </summary>
        /// <param name="eventData"></param>
        /// 

        //短按到长按的间隔时间
        private float DEFAULT_CLICK_TIME = UIEventListenerMgr.LimitClickTime;

        //设备的分辨率
        private float _screenWidth = UIEventListenerMgr.ScreenWidth;
        private float _screenHeight = UIEventListenerMgr.ScreenHeight;

        //记录是否已经按下
        private bool _isPointerDown = false;

        //记录已经按下时间
        private float _pointerDownTimePassed;

        //一次移动的距离是否超过限制
        private bool _isOutLimit;

        //开始滑动时的坐标点
        private Vector2 _beginDragPointer;

        //是否滑动的标记
        private bool _isDrag = false;

        //按钮被按下之后的颜色
        private float _color = 0.78f;

        public void OnPointerClick(PointerEventData eventData)
        {
            if (_pointerDownTimePassed < DEFAULT_CLICK_TIME && !_isDrag)
            {
                NotifyEventCBFunc(eventData);
            }
            else
            {
                //长按事件
            }

            _isPointerDown = false;
            
        }

        public void OnPointerDown(PointerEventData eventData)
        {
            _pointerDownTimePassed = 0.0f;
            _isPointerDown = true;
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            //还原选中态的颜色
            SetBtnPressColor(_color);
        }
        public void OnBeginDrag(PointerEventData eventData)
        {
            _isOutLimit = false;
            _isDrag = true;
            if(_scrollRect != null)
            {
                _scrollRect.OnBeginDrag(eventData);
            }
            _beginDragPointer = eventData.position;
        }

        public void OnDrag(PointerEventData eventData)
        {
            if (_scrollRect != null)
            {
                _scrollRect.OnDrag(eventData);
            }

            if (!_isOutLimit && ((Math.Abs(eventData.position.x - _beginDragPointer.x) / _screenWidth > UIEventListenerMgr.LimitDragLenghtX) || (Math.Abs(eventData.position.y - _beginDragPointer.y) / _screenHeight > UIEventListenerMgr.LimitDragLenghtY)))
            {
                _isOutLimit = true;
                //超过限制之后，要将按钮的选中态去掉
                SetBtnPressColor(1f);
            }
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            if (_scrollRect != null)
            {
                _scrollRect.OnEndDrag(eventData);
            }
           
            //放开手的时候，不是长按，且移动的距离没有超过限制
            if(_pointerDownTimePassed < DEFAULT_CLICK_TIME && !_isOutLimit)
            {
                NotifyEventCBFunc(eventData);
            }

            _isPointerDown = false;
            _isDrag = false;

            //还原选中态的颜色
            SetBtnPressColor(_color);
        }

        private void Update()
        {
            if (_isPointerDown)
            {
                _pointerDownTimePassed += Time.deltaTime;
                if (_pointerDownTimePassed > DEFAULT_CLICK_TIME)
                {
                    //超过限制之后，要将按钮的选中态去掉
                    SetBtnPressColor(1);
                }
            }
        }


        private void SetBtnPressColor(float value)
        {
            if (this != null && this.gameObject != null)
            {
                Button dd = this.gameObject.GetComponent<Button>();
                if (dd != null)
                {
                    ColorBlock cb = dd.colors;
                    if (dd.transition == UnityEngine.UI.Selectable.Transition.ColorTint)
                    {
                        cb.pressedColor = new Color(value, value, value, 1);
                        dd.colors = cb;
                    }
                }
            }
           
        }
    }
}




