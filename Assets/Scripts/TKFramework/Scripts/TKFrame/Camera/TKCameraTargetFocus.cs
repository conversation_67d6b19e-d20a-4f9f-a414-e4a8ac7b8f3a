using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TKFrame
{
    /// <summary>
    /// 摄像机聚焦该目标，可调距目标距离，水平角度，倾斜角度
    /// 暂不提供切换时间
    /// </summary>
    public class TKCameraTargetFocus : TKCameraController
    {
        private static Vector3 _targetOffset = Vector3.up;
        /// <summary>
        /// 聚焦目标
        /// </summary>
        public Transform FocusTarget
        {
            get;
            set;
        }
        /// <summary>
        /// 该控制效果持续时间
        /// </summary>
        private float _lifeTime;
        /// <summary>
        /// 距离目标距离
        /// </summary>
        private float _distanceTarget;

        /// <summary>
        /// 倾斜角度
        /// </summary>
        private float _xAngle;
        /// <summary>
        /// 水平角度
        /// </summary>
        private float _yAngle;

        //private float _distanceInit;
        //private Vector3 _dirMove;

        private float _secondSinceStarted;
        public Transform FocusTransform;

        private Vector3 _initialPostion;
        private Quaternion _initalRot;
        private Vector3 _targetPostion;

        private bool _activity = false;
        public override bool Activity
        {
            get
            {

                return _activity;
            }
        }


        protected override void Awake()
        {
            base.Awake();
        }

        protected override void Start()
        {
            base.Start();

           // this.enabled = false;
            if (FocusTransform != null && cameraCtrlParams != null)
            {
                StartCameraFocus(cameraCtrlParams, FocusTransform);
                //StartCameraFocus(FocusTransform, 20.0f, -30.0f,0.0f,1.0f);
            }
        }

        // Update is called once per frame
        void Update()
        {
            if (!_activity) return;

            _secondSinceStarted += Time.deltaTime;
            if( _secondSinceStarted > _lifeTime )
            {
                OnEnded();
            }

        }

        public void StartCameraFocus(TKCameraParams camParams, Transform target)
        {
            this.enabled = false;
            FocusTarget = target;
            StartCameraFocus(FocusTarget, camParams.distance, camParams.xAngle, camParams.yAngle, camParams.lifeTime);
            //UnityCoroutine.StartCoroutine(this, StartCameraFocusCoro(camParams));
        }

        //private IEnumerator StartCameraFocusCoro(TKCameraParams camParams)
        //{
        //    yield return TKFrame.CoroutineWait.GetWaitForSeconds(camParams.delay);
        //    StartCameraFocus(FocusTarget, camParams.distance, camParams.xAngle, camParams.yAngle, camParams.lifeTime);
        //}


        public void StartCameraFocus(Transform target, float distance, float xAngle, float yAngle, float life)
        {
            FocusTarget = target;
            _lifeTime = life;
            _distanceTarget = distance;
            _xAngle = xAngle;
            _yAngle = yAngle;

            _secondSinceStarted = 0.0f;
            OnStarted();
        }

        private void OnStarted()
        {
            _activity = true;
            if (this.enabled == false)
            {
                this.enabled = true;
            }

            this._initialPostion = this.transform.position;
            this._initalRot = this.transform.rotation;

            Quaternion lookRot = this.FocusTarget.rotation * Quaternion.Euler(_xAngle, _yAngle, 0.0f);

            this.transform.rotation = Quaternion.LookRotation( lookRot * Vector3.back);

            this.transform.position = this.FocusTarget.position + _targetOffset + lookRot * Vector3.forward * _distanceTarget; 
        }

        private void OnEnded()
        {
            _activity = false;

            this.transform.position = this._initialPostion;
            this.transform.rotation =  this._initalRot;
        }
             
        
    }
}
