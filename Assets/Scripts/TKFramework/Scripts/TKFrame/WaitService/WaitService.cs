using System;

namespace TKFrame
{
    /// <summary>
    /// 等待服务类
    /// </summary>
    public abstract class WaitService
    {

        /// <summary>
        /// 是否执行完毕
        /// </summary>
        private bool _isDone;
        public bool IsDone
        {
            get
            {
                return _isDone;
            }
            protected set
            {
                if (!_isDone && value)//值由false变成true
                {
                    //非线程安全！当WaitService在多个线程执行时，此处需要通过上锁
                    //来防止WaitServiceManager的AllFinishedCallback被执行多次
                    _isDone = value;
                    if (FinishedCallback != null)
                    {
                        FinishedCallback();
                    }
                }
                else
                {
                    _isDone = value;
                }
            }
        }

        /// <summary>
        /// 服务完成时回调
        /// </summary>
        public Action FinishedCallback;

        /// <summary>
        /// 启动服务
        /// </summary>
        public abstract void Start();

        /// <summary>
        /// 停止服务
        /// </summary>
        public abstract void Stop();
       
    }
}
