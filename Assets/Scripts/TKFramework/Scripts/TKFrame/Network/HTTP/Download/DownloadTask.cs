using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Diagnostics;

namespace TKFrame
{

    /// <summary>
    /// 分片下载任务
    /// </summary>
    public class DownloadTask : ITask
    {

        private string fileURL;//下载地址
        private string savePath;//保存路径
        private FlagmentManager flagmentManager;//保存的分片信息
        private TaskListener taskListener;//子任务侦听器
        private ExceptionListener exceptionListener;//异常侦听器

        /// <summary>
        /// 初始化下载任务
        /// </summary>
        public DownloadTask(string _fileURL, string _savePath, FlagmentManager _flagmentManager, TaskListener _taskListener, ExceptionListener _exceptionListener)
        {
            fileURL = _fileURL;
            savePath = _savePath;
            flagmentManager = _flagmentManager;
            taskListener = _taskListener;
            exceptionListener = _exceptionListener;
        }


        /// <summary>
        /// 执行下载任务
        /// </summary>
        public void run()
        {
            if (taskListener != null) taskListener.notifyTaskStart(this);
            int retryTime = 0;
            //执行下载任务
            while (!flagmentManager.Exit)
            {
                long startTime = DateTime.Now.Ticks;
                //获取分片任务
                DownFlagment flagment = flagmentManager.fetchNextFlagment();
                if (flagment == null)
                {
                    Diagnostic.Log("task over, ready exit!");
                    break;
                }

                if (taskListener != null) taskListener.notifyFlagmentStart(flagment);

                Diagnostic.Log("start a flagment task");

                //下载分片任务
                //是否成功完成下载
                bool succFlag = false;
                HttpDownloader httpDownloader = new HttpDownloader();
                try
                {
                    succFlag = httpDownloader.startDownloadFile(fileURL, savePath, flagment, flagmentManager, exceptionListener);
                }catch(Exception e)
                {
                    Diagnostic.Error("[ERROR]startDownloadFile " + e.ToString());
                }
                flagmentManager.releaseFlagment(flagment);

                long endTime = DateTime.Now.Ticks;
                int secondTime = (int)(endTime - startTime) / 10000000;

                if (succFlag)//正常返回，通知帧下载完成
                {
                    retryTime = 0;
                    if (taskListener != null) taskListener.notifyFlagmentEnd(flagment);
                }
                else//非正常返回，可能网络异常，故延迟重试
                {
                    retryTime++;
                    int sleepTime = secondTime <= 1 ? 1000 : 100;
                    if (retryTime > 3 && exceptionListener != null)
                    {
                        exceptionListener.notifyDownloadError(0, 0, "网络多次异常，请重试！");
                    }
                    else
                    {
                        System.Threading.Thread.Sleep(sleepTime);
                    }
                }
            }
            //通知任务将结束，队列中已无需要下载的任务
            if (taskListener != null) taskListener.notifyTaskEnd(this);
        }
    }
}
