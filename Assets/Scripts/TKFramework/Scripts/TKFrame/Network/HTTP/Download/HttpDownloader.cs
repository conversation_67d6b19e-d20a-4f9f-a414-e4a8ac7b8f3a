using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.IO;
using System.Net;
using System.Threading;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;

namespace TKFrame
{
    /// <summary>
    /// HTTP下载器
    /// </summary>
    class HttpDownloader
    {

        /// <summary>
        /// 构造函数
        /// </summary>
        public HttpDownloader()
        {
        }

        
        /// <summary>
        /// 获取本地文件流
        /// </summary>
        private static FileStream openFileStreamShareWrite(string path)
        {
            //实例化文件流对象，保存下载数据
            FileStream fileStream = null;
			try
			{
				//打开要下载的文件
                fileStream = File.Open(path, FileMode.OpenOrCreate, FileAccess.Write, FileShare.ReadWrite);
			}catch(Exception e)
			{
				Diagnostic.Error("getFileStream at OpenWrite:"+e.ToString()+"   path:"+path);
			}
            return fileStream;

        }

        
        /// <summary>
        /// 检查证书
        /// </summary>
        private static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)  
        {
            return true; //总是接受
        } 


        /// <summary>
        /// 获取网络数据流
        /// </summary>
        private static Stream getNetStream(string fileURL, int startRange, int endRange, out HttpWebResponse response, ExceptionListener listener)
        {
            //检查证书,支持https
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);

            //打开网络连接
            HttpWebRequest fileRequest = (HttpWebRequest)HttpWebRequest.Create(fileURL);
            if (startRange >= 0)
            {
                if (endRange >= startRange)//设置Range区域
                {
                    fileRequest.AddRange(startRange, endRange);//设置Range值，用于断点续传
                }
                else//设置Range开始区域，默认到文件末尾
                {
                    fileRequest.AddRange(startRange);//设置Range值，用于断点续传
                }
            }

            //设置超时时间
            fileRequest.Timeout = 10 * 1000;//10秒
            fileRequest.ReadWriteTimeout = 10 * 1000;//10秒

            //向服务器请求,获得服务器的回应数据流
            try
            {
                response = fileRequest.GetResponse() as HttpWebResponse;
            }
            catch (WebException e)
            {
                Diagnostic.Warn("GetResponse Exception:" + e.ToString());
                response = e.Response as HttpWebResponse;
            }
            if(response==null)
            {
                return null;
            }
            
            HttpStatusCode statusCode = response.StatusCode;

            //正常访问
            if (statusCode >= HttpStatusCode.OK && statusCode <= HttpStatusCode.OK+6)//[200, 206]
            {
                var headers=response.Headers;
                string contentType = response.Headers["Content-Type"];

                //返回类型不正确，可能联网有问题，比如弹出认证页面
                if (contentType != null && contentType.Contains("text"))
                {
                    StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
                    for (int i = 0; i < headers.Count; i++)
                    {
                        sb.Append("<Header " + i + ">: " + headers.GetKey(i) + "\t=\t");
                        String[] val = headers.GetValues(i);
                        if (val != null)
                        {
                            for (int j = 0; j < val.Length; j++)
                            {
                                sb.Append(val[j]);
                                if (j < val.Length - 1) sb.Append("#");
                            }
                        }
                        sb.Append("\r\n");
                    }
                    
                    Diagnostic.Warn("网络异常，可能弹出了认证页面。\r\n" + sb.ToString());
                    MicroObjectPool<StringBuilder>.Release(sb);

                    response.Close();
                    if (listener!=null) listener.notifyWebAuth("网络异常，可能是弹出了页面需要您登录认证，建议您打开浏览器检查状态。");
                    return null;
                }

                Stream netStream = response.GetResponseStream();
                if (netStream==null)
                {
                    if(listener != null)listener.notifyDownloadError(0, 0, "网络响应异常，请重试！");
                }
                return netStream;
            }
            //重定向
            else if (statusCode >= HttpStatusCode.Redirect - 2 && statusCode <= HttpStatusCode.Redirect+5)//[300, 307]
            {
                string newURL = response.GetResponseHeader("Location");
                //Diagnostic.Log("HttpDownloader url=" + fileURL + " response=" + statusCode + " Redirec_tUrL="+newURL);
                //关闭网络资源
                response.Close();
                return getNetStream(newURL, startRange, endRange, out response, listener);
            }
            //请求异常
            else if(statusCode >= HttpStatusCode.BadRequest && statusCode <= HttpStatusCode.BadRequest+17)//[400, 417]
            {
                //Diagnostic.Log("HttpDownloader url=" + fileURL + " response=" + statusCode);
                //关闭网络资源
                response.Close();
                if (listener != null) listener.notifyDownloadError(0, 0, "网络请求异常，请重试！");
                return null;
            }
            //服务器错误
            else if (statusCode >= HttpStatusCode.InternalServerError && statusCode <= HttpStatusCode.InternalServerError + 5)//[500, 505]
            {
                //Diagnostic.Log("HttpDownloader url=" + fileURL + " response=" + statusCode);
                //关闭网络资源
                response.Close();
                if (listener != null) listener.notifyDownloadError(0, 0, "发现服务器错误，请重试！");
                return null;
            }
            else
            {
                //Diagnostic.Log("HttpDownloader url=" + fileURL + " response=" + statusCode);
                //关闭网络资源
                response.Close();
                if (listener != null) listener.notifyDownloadError(0, 0, "网络未知异常，请重试！");
                return null;
            }

        }

        
        /// <summary>
        /// 开始下载
        /// </summary>
        public bool startDownloadFile(string fileURL, string savePath, DownFlagment flagment, FlagmentManager flagmentManager, ExceptionListener listener)
        {
            Diagnostic.Log("[ThreadID:" + System.Threading.Thread.CurrentThread.ManagedThreadId + "] [Flagment]Index:" + flagment.mFlagmentIndex + " openFileStream");
            //是否成功完成下载
            bool succFlag = false;

            //获取文件流
            Stream netStream = null;
            FileStream fileStream=null;

            try
            {
                fileStream = openFileStreamShareWrite(savePath);
                if (fileStream == null)
                {
                    Diagnostic.Error("[Flagment]Index:" + flagment.mFlagmentIndex + " 打开本地文件异常");
                    return false;//下载失败
                }

                //打开已经下载的文件尺寸
                int seekPosition = flagment.mStartRange + flagment.getDownloadCount();
                if (seekPosition > 0)
                {
                    fileStream.Seek(seekPosition, SeekOrigin.Current);
                }

                //打开网络连接
                HttpWebResponse response;
                netStream = getNetStream(fileURL, seekPosition, flagment.mEndRange, out response, listener);
                if(netStream==null)
                {
                    Diagnostic.Log("[ThreadID:" + System.Threading.Thread.CurrentThread.ManagedThreadId + "] [Flagment]Index:" + flagment.mFlagmentIndex + " 打开网络连接异常");
                    return false;//下载失败
                }
                //定义读取buff
                int buffSize=256*1024;
                byte[] btContent = new byte[buffSize];
                while (!flagmentManager.Exit)
                {
                    //剩余字节数
                    int leaveSize = flagment.mFlagmentSizeTotal - flagment.getDownloadCount();

                    if (leaveSize < 0)
                        Diagnostic.ReportError("flagment_size");

                    //正常下载完成
                    if (leaveSize<=0)
                    {
                        break;
                    }
                    
                    int readSize = readFully(netStream, btContent, 0, leaveSize<buffSize? leaveSize: buffSize);//netStream.Read(btContent, 0, buffSize);

                    if (flagmentManager.Exit)
                    {
                        break;
                    }

                    if (readSize <= 0)
                    {
                        //异常，数据流已经到末尾，但任务字节数还未完成
                        string errorInfo = "[ThreadID:" + System.Threading.Thread.CurrentThread.ManagedThreadId + "[startDownloadFile] readSize is " + readSize + ", but DownloadedCount is not full!";
                        Diagnostic.Error(errorInfo);
                        Exception e = new Exception(errorInfo);
                        throw e;
                    }
                    fileStream.Write(btContent, 0, readSize);
                    fileStream.Flush();//影响效率
                    flagment.addDownloadCount(readSize);
                }
                succFlag = true;        //返回true下载成功

                Diagnostic.Log("[ThreadID:" + System.Threading.Thread.CurrentThread.ManagedThreadId + "] [Flagment]Index:" + flagment.mFlagmentIndex + " 分片下载结束");
            }
            catch (Exception e)
            {
                succFlag = false;       //返回false下载失败
                Diagnostic.Error("[ThreadID:" + System.Threading.Thread.CurrentThread.ManagedThreadId + "] [Flagment]Index:" + flagment.mFlagmentIndex + " [ERROR]HttpDownloader url=" + fileURL + " Exception:" + e.ToString());
            }
            finally
                {
                    //关闭流
                    if (fileStream != null)
                    {
                        try
                        {
                            fileStream.Close(); 
                        }
                        catch (Exception) { }
                        
                    }
                    if (netStream != null)
                    {
                        try {
                            netStream.Close();
                        }
                        catch (Exception) { }
                    }
                }
            return succFlag;
        }

        
        /// <summary>
        /// 获取文件流总字节尺寸
        /// </summary>
        public static int getTotalSizeInNetStream(string fileURL, ExceptionListener listener)
        {
            HttpWebResponse response;
            Stream netStream = getNetStream(fileURL, 0, -1, out response, listener);

            if (netStream == null)
            {
                return 0;
            }

            //内容长度
            int contentLength = (int)response.ContentLength;
            
            //区间范围
            string rangeStr = response.GetResponseHeader("Content-Range");
            int rangeTotalSize = getTotalSizeInRange(rangeStr);

            Diagnostic.Log("ContentLength:" + contentLength + " RangeSize:" + rangeTotalSize);

            //优先区间范围
            int totalSize = 0;
            if (rangeTotalSize > 0)
            {
                totalSize = rangeTotalSize;
            }
            else
            {
                totalSize = contentLength;
            }
            netStream.Close();
            return totalSize;
        }

        
        /// <summary>
        /// 获取range属性中总字节数量
        /// </summary>
        private static int getTotalSizeInRange(string rangeStr)
        {
            if (rangeStr != null && rangeStr.Length > 0)
            {
                try
                {
                    int ptIndex = rangeStr.LastIndexOf("/");
                    if (ptIndex > 0)
                    {
                        string rangeSizeStr = rangeStr.Substring(ptIndex + 1);
                        int size = int.Parse(rangeSizeStr);//解析总字节数量
                        return size;
                    }
                }
                catch (Exception e) {
                    Diagnostic.Error("[ERROR]getTotalSizeInRange:" + rangeStr +" at:" + e.ToString());
                }

            }
            return -1;
        }

        
        /// <summary>
        /// 读取指定长度数据，除非达到数据流末尾
        /// </summary>
        public int readFully(Stream netStream, byte[] b, int off, int len)
        {
            int count = 0;
            while (len > 0) {
                int n = netStream.Read(b, off, len);
                if (n <= 0)
                {//数据流末尾
                    break;
                }
                off += n;
                len -= n;
                count += n;
            }
            return count;
        }

       
    }

}
