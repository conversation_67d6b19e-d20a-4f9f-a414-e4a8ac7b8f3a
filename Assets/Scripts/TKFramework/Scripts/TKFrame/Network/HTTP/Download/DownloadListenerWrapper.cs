using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace TKFrame
{

    /// <summary>
    /// 用Action包装的下载模块侦听器
    /// </summary>
    public class DownloadListenerWrapper : DownloadListener
    {
        /// <summary>
        /// 是否在主线程通知
        /// </summary>
        public bool RunInMainThread = true;

        /// <summary>
        /// 通知-下载开始
        /// </summary>
        public Action<int, int, string> funcNotifyDownloadStart;

        /// <summary>
        /// 通知-下载进度(speed为k/s表示下载速度)
        /// </summary>
        public Action<int, int, int> funcNotifyDownloadProgress;

        /// <summary>
        /// 通知-下载结束
        /// </summary>
        public Action<int, int, string> funcNotifyDownloadEnd;


        public void setDownloadListenerAction(Action<int, int, string> funcNotifyDownloadStart,Action<int, int, int> funcNotifyDownloadProgress, Action<int, int, string> funcNotifyDownloadEnd)
        {
            this.funcNotifyDownloadStart = funcNotifyDownloadStart;
            this.funcNotifyDownloadProgress = funcNotifyDownloadProgress;
            this.funcNotifyDownloadEnd = funcNotifyDownloadEnd;
        }

        public void notifyDownloadStart(int downloadedCount, int totalSize, string des)
        {
            if (funcNotifyDownloadStart != null)
            {
                if(RunInMainThread)
                {
                    SystemManager.getInstance().AddTaskInMainThread(delegate ()
                    {
                        funcNotifyDownloadStart(downloadedCount, totalSize, des);
                    });
                }
                else
                {
                    funcNotifyDownloadStart(downloadedCount, totalSize, des);
                }
            }
        }

        public void notifyDownloadProgress(int downloadedCount, int totalSize, int speed)
        {
            if (funcNotifyDownloadProgress != null)
            {
                if (RunInMainThread)
                {
                    SystemManager.getInstance().AddTaskInMainThread(delegate ()
                    {
                        funcNotifyDownloadProgress(downloadedCount, totalSize, speed);
                    });
                }
                else
                {
                    funcNotifyDownloadProgress(downloadedCount, totalSize, speed);
                }
            }
        }

        public void notifyDownloadEnd(int downloadedCount, int totalSize, string des)
        {
            if (funcNotifyDownloadEnd != null)
            {
                if (RunInMainThread)
                {
                    SystemManager.getInstance().AddTaskInMainThread(delegate ()
                    {
                        funcNotifyDownloadEnd(downloadedCount, totalSize, des);
                    });
                }
                else
                {
                    funcNotifyDownloadEnd(downloadedCount, totalSize, des);
                }
                
            }
        }
    }
}
