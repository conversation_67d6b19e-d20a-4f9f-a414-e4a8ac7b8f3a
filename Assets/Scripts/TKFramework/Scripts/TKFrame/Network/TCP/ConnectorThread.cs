using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Security;
using System.Text;
using System.Threading;
using UnityEngine;

namespace TKFrame
{
    
    /// <summary>
    /// 网络连接线程
    /// </summary>
    public class ConnectorThread : ProxyThread
    {
        public TcpClient TCP_Client;
        public  String ip;
        public  int port;
        public IPAddress connecedIpAddress;
        private ISocketStateListener StateListener;
        private object connection;
        private Action ErrorHandle;
        private bool IsPollIp;
        public bool stopConnect = false;

        //混淆IP打印
        public static string m_IPAddress = string.Empty;
        public static string m_IPAddress_Obfuscate = string.Empty;
        public static int DnsParseIPIndex = 0;


        /// <summary>
        /// 初始化
        /// </summary>
        public ConnectorThread(String ip, int port, ISocketStateListener StateListener, object connection, Action ErrorHandle, bool isPollIp)
        {
            this.ip = ip;
            this.port = port;
            this.StateListener = StateListener;
            this.connection = connection;
            this.ErrorHandle = ErrorHandle;
            this.IsPollIp = isPollIp;
        }


        /// <summary>
        /// 运行连接任务
        /// </summary>
        public override void run()
        {
            ConnectServerImp(ip, port, StateListener, connection, ErrorHandle, IsPollIp);
        }

        public static string EncryptAndDencryptIP(string value, bool isEncrypt)
        {
            try
            {
                if (isEncrypt)
                {
                    string obfuscate = "";
                    FastStringSplit arr_sub = value.BeginSplit('.');
                    for (int i = 0; i < arr_sub.Length; i++)
                    {
                        int iSub = 0;
                        arr_sub.TryParse(i, out iSub);
                        if (i == 0)
                            obfuscate = obfuscate + ((iSub + 1994 + ((i + 1) * 14)) * (i + 1) * 2f).ToString();
                        else
                            obfuscate = obfuscate + "*" + ((iSub + 1994 + ((i + 1) * 14)) * (i + 1) * 2f).ToString();
                    }
                    arr_sub.EndSplit();
                    return obfuscate;
                }
                else
                {
                    string un_obfuscate = "";
                    FastStringSplit arr_sub = value.BeginSplit('.');
                    for (int i = 0; i < arr_sub.Length; i++)
                    {
                        int iSub = 0;
                        if (arr_sub.TryParse(i, out iSub))
                        {
                            un_obfuscate = un_obfuscate + "." + ((iSub / (2f * (i + 1))) - 1994 - ((i + 1) * 14)).ToString();
                        }
                    }
                    arr_sub.EndSplit();
                    return un_obfuscate;
                }
            }
            catch (Exception e)
            {
                Diagnostic.Error("EncryptAndDencrypt Error:" + e.ToString());
                return value;
            }
        }


        /// <summary>
        /// 连接服务器
        /// </summary>

        private void ConnectServerImp(String ip, int port, ISocketStateListener StateListener, object connection, Action ErrorHandle, bool isPollIp)
        {
            //if (StateListener != null) StateListener.onConnecting(TCP_Client, "BeginConnect", TagID);
            try
            {
                IPAddress ipAdrress = IPAddress.None;
                AddressFamily addressFamiliy = AddressFamily.InterNetwork;

                //Diagnostic.Log("ConnectServerImp() ip:{0} port:{1}", ip, port);

                if (isPollIp)//如果走了IP轮询，就不需要DNS解析了，防止DNS劫持，IP轮询必须要求轮序的直接是IP
                {
                    ipAdrress = IPAddress.Parse(ip);
                    addressFamiliy = ipAdrress.AddressFamily;
                }
                else
                {
                    string addressIp = TKFrameworkDelegateInterface.HttpDnsController_GetAddressByName(ip);//HttpDnsController.GetAddressByName(ip);
                    if (string.IsNullOrEmpty(addressIp))
                    {
#if UNITY_IPHONE && !UNITY_EDITOR
                    string newServerIp = "";
                    IPV6Helper.getIPType(ip, port.ToString(), out newServerIp, out addressFamiliy);
                    Diagnostic.Log("IPV6Helper.getIPType src:{0} newServerIp:{1}", ip, newServerIp);
                    ipAdrress = IPAddress.Parse(newServerIp);
#else
                        IPAddress[] addresses = Dns.GetHostAddresses(ip);
#if !JK_RELEASE
                        foreach (var address in addresses)
                        {
                            Diagnostic.Log("GetHostAdrress() src:{0} dest:{1} port:{2}", ip, address.ToString(), port);
                        }
#endif

                        if (DnsParseIPIndex > addresses.Length - 1)
                        {
                            DnsParseIPIndex = 0;
                        }

                        if (DnsParseIPIndex < addresses.Length)
                        {
                            if (addresses.Length > 0)
                            {
                                ipAdrress = addresses[DnsParseIPIndex];
                                addressFamiliy = addresses[DnsParseIPIndex].AddressFamily;
                            }
                        }
                        DnsParseIPIndex++;
#endif
                    }
                    else
                    {
                        ipAdrress = IPAddress.Parse(addressIp);
                        addressFamiliy = ipAdrress.AddressFamily;
                    }
                }
                
                lock (SocketConnection.SyncLock)
                {
                    //如果连接线程已经被外部停掉，就直接终止掉
                    if (stopConnect)
                    {
                        Diagnostic.Log("ConnectServerImp stopConnect {0}", TagID);
                        return;
                    }

                    connecedIpAddress = ipAdrress;
                    this.TCP_Client = new TcpClient(addressFamiliy);
                    //if (AIDebugPanel.m_IsSetLingerOption)
                    //{
                    LingerOption lingerOption = new LingerOption(true, 0);
                    this.TCP_Client.LingerState = lingerOption;
                    //}
                    if (StateListener != null) StateListener.onCreate(TCP_Client, "create TcpClient", TagID);
                    (connection as SocketConnection).TCP_Client = this.TCP_Client;
                    string cacheIp = ipAdrress.ToString();
#if !JK_RELEASE
                    Diagnostic.Log("ConnectServerImp BeginConnect:[" + cacheIp + "] Port:[" + port + "]");
#endif
                    if (cacheIp != m_IPAddress)
                    {
                        m_IPAddress = cacheIp;
                        m_IPAddress_Obfuscate = EncryptAndDencryptIP(m_IPAddress, true);
                        Diagnostic.Log("ConnectServerImp Cache");
                    }
                    Diagnostic.Log("CSI BC En:" + m_IPAddress_Obfuscate + " " + port);
#if !JK_RELEASE
                    Diagnostic.Log("CSI BC De:[" + EncryptAndDencryptIP(m_IPAddress_Obfuscate, false) + "] Port:[" + port + "]");
#endif

                    IAsyncResult iAsyncResult = TCP_Client.BeginConnect(ipAdrress, port, new AsyncCallback(ConnectedCallBack), connection);
                    if (StateListener != null) StateListener.onConnecting(TCP_Client, "BeginConnect", TagID);
                }
            }
            catch (ArgumentNullException e)
            {
                lock (SocketConnection.SyncLock)
                {
                    if (StateListener != null) StateListener.onError(TCP_Client, "host为空:" + e.ToString(), TagID, (int)NetworkStateErrorType.ArgumentNullException);
                    if (ErrorHandle != null && isActive) ErrorHandle();
                }
            }
            catch (SocketException e)
            {
                lock (SocketConnection.SyncLock)
                {
                    if (StateListener != null) StateListener.onError(TCP_Client, "访问套接字时发生错误:" + e.ToString(), TagID, (int)NetworkStateErrorType.SocketException);
                    if (ErrorHandle != null && isActive) ErrorHandle();
                }
            }
            catch (ObjectDisposedException e)
            {
                lock (SocketConnection.SyncLock)
                {
                    if (StateListener != null) StateListener.onError(TCP_Client, "Socket 已关闭:" + e.ToString(), TagID, (int)NetworkStateErrorType.ObjectDisposedException);
                    if (ErrorHandle != null && isActive) ErrorHandle();
                }
            }
            catch (SecurityException e)
            {
                lock (SocketConnection.SyncLock)
                {
                    if (StateListener != null) StateListener.onError(TCP_Client, "调用方无权执行:" + e.ToString(), TagID, (int)NetworkStateErrorType.SecurityException);
                    if (ErrorHandle != null && isActive) ErrorHandle();
                }
            }
            catch (ArgumentOutOfRangeException e)
            {
                lock (SocketConnection.SyncLock)
                {
                    if (StateListener != null) StateListener.onError(TCP_Client, "端口号无效:" + e.ToString(), TagID, (int)NetworkStateErrorType.ArgumentOutOfRangeException);
                    if (ErrorHandle != null && isActive) ErrorHandle();
                }
            }
            catch (Exception e)
            {
                lock (SocketConnection.SyncLock)
                {
                    if (StateListener != null) StateListener.onError(TCP_Client, "错误:" + e.ToString(), TagID, (int)NetworkStateErrorType.CommonException);
                    if (ErrorHandle != null && isActive) ErrorHandle();
                }
            }
        }


        /// <summary>
        /// 异步连接的回调通知
        /// </summary>
        private void ConnectedCallBack(IAsyncResult asyncresult)
        {
            lock (SocketConnection.SyncLock)
            {
                //如果连接线程已经被外部停掉，就直接终止掉
                if (stopConnect)
                {
                    if (this.TCP_Client != null) this.TCP_Client.Close();
                    Diagnostic.Log("ConnectedCallBack stopConnect {0}", TagID);
                    return;
                }

                SocketConnection connection = asyncresult.AsyncState as SocketConnection;
                //如果当前TCP_Client和connection中的TCP_Client不一致，则关掉当前的TCP_Client返回。
                if (connection != null && connection.TCP_Client != this.TCP_Client)
                {
                    this.TCP_Client.Close();
                    Diagnostic.Error("ConnectedCallBack connection.TCP_Client != this.TCP_Client:CurrSocketID {0},this.SocketID {1}", connection.SocketID, TagID);
                    return;
                }

                if (connection != null && connection.TCP_Client != null)
                {
                    int socketID = connection.SocketID;
                    try
                    {
                        if (connection.TCP_Client != null)
                        {
                            connection.TCP_Client.NoDelay = true;
                        }
                        connection.TCP_Client.EndConnect(asyncresult);

                        if (connection.IsConnected())
                        {
                            if (connection.StateListener != null)
                            {
                                connection.StateListener.onConnected(connection.TCP_Client, "EndConnect", socketID);
                            }

                            connection.StartReceiveThread();
                            connection.StateListener.onStartReceiveThread(connection.TCP_Client, "StartReceiveThread", socketID);

                            connection.StartSendThread();
                            connection.StateListener.onStartSenderThread(connection.TCP_Client, "StartSendThread", socketID);

                            connection.StateListener.onReady(connection.TCP_Client, "Ready", socketID);

                            if (connecedIpAddress != null)
                                IpPollManager.Instance.SetCurrConnectIpPort(connecedIpAddress.ToString(), port);
                        }
                        else
                        {
                            if (connection.StateListener != null)
                            {
                                connection.StateListener.onError(connection.TCP_Client, "IsConnected is false", socketID, (int)NetworkStateErrorType.ConnectedFalse);
                            }
                            connection.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        if (connection.StateListener != null)
                        {
                            connection.StateListener.onError(connection.TCP_Client, "Exception:" + ex.ToString(), socketID, (int)NetworkStateErrorType.ConnectedCallBackException);
                        }
                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }

    }
}
