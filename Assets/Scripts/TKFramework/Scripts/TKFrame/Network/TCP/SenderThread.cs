using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using UnityEngine;

namespace TKFrame
{
    
    /// <summary>
    /// 网络发送线程
    /// </summary>
    public class SenderThread : ProxyThread
    {
        //数据发送队列
        //private Queue<byte[]> sendQueue = new Queue<byte[]>();
        private static MsgSendQueue sendQueue = new MsgSendQueue();
        //private List<long> sendTime = new List<long>();
        //网络数据流
        private NetworkStream networkStream;
        //private List<KeyValuePair<byte[],long>> networkTime = new List<KeyValuePair<byte[], long>>();
        //Socket状态侦听器
        private ISocketStateListener StateListener;

        //错误处理
        public Action ErrorHandle;
        
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SenderThread()
        {
            sendQueue.Clear();
        }

        
        /// <summary>
        /// 初始化
        /// </summary>
        public void init(NetworkStream stream, ISocketStateListener listener, Action errorHandle)
        {
            networkStream = stream;
            StateListener = listener;
            ErrorHandle = errorHandle;
        }


        /// <summary>
        /// 请求发送数据
        /// </summary>
        public void send(byte[] data)
        {
            /*
            lock(sendQueue)
            {
                sendQueue.Enqueue(data);
                //sendTime.Add(DateTime.Now.Ticks);
            }
            */
            
            try
            {
                sendQueue.Enqueue(data);
            }
            catch (Exception e)
            {
                Diagnostic.Error(e);
            }

            notify();//激活线程执行发送任务
        }
        
        public void send(MemoryStream ms)
        {
            try
            {
                sendQueue.Enqueue(ms);
            }
            catch (Exception e)
            {
                Diagnostic.Error(e);
            }

            notify();//激活线程执行发送任务
        }


        /// <summary>
        /// 清除还未发送的数据
        /// </summary>
        public void clear()
        {
            /*
            lock (sendQueue)
            {
                sendQueue.Clear();
                //sendTime.Clear();
            }
            */
            
            sendQueue.Clear();
            //newSendQueue.Clear();
        }

        System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
        /// <summary>
        /// 执行发送任务
        /// </summary>
        public override void run()
        {
            if (StateListener != null) StateListener.onStartSenderThread(networkStream, "发送线程启动", TagID);
            if (networkStream != null)
            {
                while (isActive)
                {
                    if (isActive && sendQueue.Count > 0)
                    {
                        try
						{
                            /*
                            byte[] data = sendQueue.Peek();
                            //long tick = sendTime.ElementAt(0);
                            lock(sendQueue)
                            {
                                sendQueue.Dequeue();
                                //sendTime.RemoveAt(0);
                            }
                            
                            if (StateListener != null) StateListener.onSend(data, 0, data.Length, TagID);
							networkStream.Write(data, 0, data.Length);
                            */

                            int len = sendQueue.PeekLen();
                            if (StateListener != null) StateListener.onSend(null, 0, len, TagID);
                            sendQueue.Dequeue(networkStream);

                            //networkTime.Add(new KeyValuePair<byte[], long>(data,tick));
                            //等待片刻，可以与其它新消息一次性Flush
                            if (sendQueue.Count == 0)
                            {
                                wait(15);
                            }
                            //确定无新消息发送
                            if (sendQueue.Count == 0)
                            {
                                int oldSendQueueCount = sendQueue.Count;
                                stopwatch.Start();

                                networkStream.Flush();

                                if (stopwatch.ElapsedMilliseconds > 1000)
                                    Diagnostic.Warn("SenderThread.run Send data out of time:" + stopwatch.ElapsedMilliseconds + " oldSendQueue.Count: " + oldSendQueueCount + " current: " + sendQueue.Count);
                                stopwatch.Stop();
                                stopwatch.Reset();
                                //TimeSpan now = new TimeSpan(DateTime.Now.Ticks);
                                //for (int i = 0; i < networkTime.Count; i++)
                                //{
                                //    var sendData = networkTime[i].Key;
                                //    var sendDataTime = new TimeSpan(networkTime[i].Value);
                                //    var sendDelay = (now - sendDataTime).TotalSeconds;
                                //    //超过5s 应该出现问题了
                                //    if (sendDelay > 5)
                                //    {
                                //        Diagnostic.Error("SenderThread.run Send data check error: out of time:" + sendDelay+" data len:"+ sendData.Length+" bytes:"+Convert.ToBase64String(sendData));
                                //    }
                                //}
                                //networkTime.Clear();
                            }
						}
						catch (Exception e)
						{
							if (StateListener != null && isActive) StateListener.onError(networkStream, "发送数据异常:"+e.ToString(), TagID, (int)NetworkStateErrorType.SendDataException);
                            if (ErrorHandle != null && isActive)
                            {
                                ErrorHandle();
                            }
                            break;
						}
                    }
                    //确定无消息需要发送，线程进入睡眠
                    if (isActive && sendQueue.Count == 0)
                    {
                        wait();
                    }
                }


			}
            if (StateListener != null) StateListener.onCloseSenderThread(networkStream, "发送线程关闭", TagID);

            Diagnostic.Warn("SenderThread exit threadid={0}, TagID={1}", ThreadID, TagID);
        }

    }
}
