using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEngine;

public static class ComponentRefUtil
{
    static TKDictionary<string, WeakReference> s_refDict = new TKDictionary<string, WeakReference>();

    private static int count = 0;
    public static void AddRef(Component c)
    {
        if (!m_openCheck)
            return;

        sb.Append(count).Append(",").Append(c.GetType()).Append(",").Append(GetGameObjectPath(c));
        var key = sb.ToString();

        s_refDict[key] = new WeakReference(c);
        ++count;
    }

    static StringBuilder sb = new StringBuilder();
    private static string GetGameObjectPath(Component c)
    {
        sb.Clear();
        var obj = c.gameObject;
        sb.Append("/").Append(obj.name);
        while (obj.transform.parent != null)
        {
            obj = obj.transform.parent.gameObject;
            sb.Insert(0, "/").Insert(0, obj.name);
        }
        return sb.ToString();
    }

    static bool m_openCheck = false;
#if UNITY_EDITOR

    const string kModeMenu = "ZGame/ComponentRefUtil/内存泄漏检测开关";

    [UnityEditor.MenuItem(kModeMenu)]
    public static void ToggleSimulateAssetBundle()
    {
        m_openCheck = !m_openCheck;
    }

    [UnityEditor.MenuItem(kModeMenu, true)]
    public static bool ToggleSimulateAssetBundleValidate()
    {
        UnityEditor.Menu.SetChecked(kModeMenu, m_openCheck);
        return true;
    }

    [UnityEditor.MenuItem("ZGame/ComponentRefUtil/GetDependencies")]
    public static void ShowDepand()
    {
        var go = UnityEditor.Selection.activeObject;
        var dependencies = UnityEditor.AssetDatabase.GetDependencies(UnityEditor.AssetDatabase.GetAssetPath(go));

        Debug.Log("dependencies >>");
        foreach (var item in dependencies)
        {
            Debug.Log(item);
        }
    }

    static int s_PrintLog = 0;
    [UnityEditor.MenuItem("ZGame/ComponentRefUtil/PrintLog")]
    public static void PrintLog()
    {
        GC.Collect();
        int count = 0;
        StringBuilder sb = new StringBuilder();
        sb.Append("Index,ComponentType,GameObject,RefType\n");
        //sb.Append("#ComponentRefUtil#打印销毁了但还被引用的物件:\n");
        foreach (var kv in s_refDict)
        {
            if (kv.Value.IsAlive)
            {
                if (kv.Value.Target == null)
                {
                    sb.Append(kv.Key).Append("Target is null,\n");
                    //sb.AppendFormat("Target is null {0} \n", kv.Key);
                    ++count;
                    continue;
                }

                var w = kv.Value.Target as Component;
                if (w == null)
                {
                    sb.Append(kv.Key).Append("Component is null,\n");
                    //sb.AppendFormat("Component is null {0} \n", kv.Key);
                    ++count;
                    continue;
                }

                if (w.gameObject == null)
                {
                    sb.Append(kv.Key).Append("Component attached game object is null,\n");
                    //sb.AppendFormat("Component attached game object is null {0} \n", kv.Key);
                    ++count;
                }
            }
        }
        sb.Append("Count: " + count);
        string fileName = "D://销毁了但还被引用的物件_"+ s_PrintLog + ".csv";
        string content = sb.ToString();
        System.IO.File.WriteAllText(fileName, content);
        Debug.LogError(sb.ToString());
        Debug.Log("Save to " + fileName);

        //EmailConfig.Tos.Clear();
        //EmailConfig.Tos.AddRange(new List<string>()
        //{
        //    "<EMAIL>"
        //});
        //EmailUtil.SendMail("[Auto][JK] 扫描《销毁了但还被引用的物件》", content, fileName);
        //Debug.Log("Send Finish");
        ++s_PrintLog;
    }

    [UnityEditor.MenuItem("ZGame/ComponentRefUtil/清理单例")]
    public static void ClearSingleton()
    {
        var ass = Assembly.GetAssembly(typeof(ComponentRefUtil));
        var types = ass.GetTypes();
        for (int i = 0; i < types.Length; ++i)
        {
            var type = types[i];
            if (type.IsEnum)
                continue;
            if (type.FullName.StartsWith("GCloud."))
                continue;
            var statiicFields = type.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
            foreach (var staticField in statiicFields)
            {
                if (staticField.FieldType == type)
                {
                    if (type.ContainsGenericParameters || staticField.IsInitOnly)
                    {
                        Debug.Log(string.Format("静态类型: {0} 所属类: {1} 无法被清理| ContainsGenericParameters:{2} IsInitOnly:{3}", staticField.Name, type.Name, type.ContainsGenericParameters, staticField.IsInitOnly));
                        continue;
                    }

                    //var value = staticField.GetValue(null);
                    //if (value != null)
                    {
                        Debug.Log("Clear Type: " + type.FullName + " staticField.name: " + staticField.Name);
                        staticField.SetValue(null, null);
                    }
                }
            }
        }
    }

    static int s_FindAllStaticVarCount = 0;
    // 反射找出目前静态变量以及这个变量下面所有的成员变量
    [UnityEditor.MenuItem("ZGame/ComponentRefUtil/Find All Static Var")]
    public static void FindAllStaticVar()
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine("所有静态变量的分布");
        var ass = Assembly.GetAssembly(typeof(ComponentRefUtil));
        var types = ass.GetTypes();
        try
        {
            for (int i = 0; i < types.Length; ++i)
            {
                var type = types[i];
                if (type == typeof(ComponentRefUtil))
                    continue;
                var statiicFields = type.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
                foreach (var staticField in statiicFields)
                {
                    if (IsSimpleType(staticField.FieldType))
                        continue;
                    if (staticField.FieldType.ContainsGenericParameters || type.ContainsGenericParameters)
                    {
                        sb.AppendFormat("静态类型: {0} 所属类: {1} | {2} {3}\n", staticField.FieldType.Name, type.FullName, staticField.FieldType.ContainsGenericParameters, type.ContainsGenericParameters);
                        continue;
                    }
                    var value = staticField.GetValue(null);
                    if (value != null)
                    {
                        HashSet<object> objSet = new HashSet<object>();
                        ShowAllField(value, staticField.Name, type, 0, ref sb, ref objSet);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            sb.Append(ex.ToString());
        }

        string fileName = "D://静态变量快照_" + s_FindAllStaticVarCount + ".txt";
        string content = sb.ToString();
        System.IO.File.WriteAllText(fileName, content);
        Debug.LogError(sb.ToString());
        Debug.Log("Save to " + fileName);

        ++s_FindAllStaticVarCount;
    }

    public static void ShowMulticastDelegate(int depth, object obj, ref StringBuilder sb)
    {
        var t = typeof(MulticastDelegate);
        var delegatesField = t.GetField("delegates", BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
        var delegates = delegatesField.GetValue(obj) as Delegate[];
        if (delegates != null)
        {
            for (int i = 0; i < delegates.Length; ++i)
            {
                if (delegates[i].Target != null)
                {
                    for (int j = 0; j < depth; ++j)
                        sb.Append("\t");
                    sb.AppendFormat("目标: {0} 类型: {1}\n", delegates[i].Target, delegates[i].Target.GetType().Name);
                }
            }
        }
        var selfDelegate = obj as MulticastDelegate;
        if (selfDelegate != null && selfDelegate.Target != null)
        {
            for (int j = 0; j < depth; ++j)
                sb.Append("\t");
            sb.AppendFormat("自身目标: {0} 类型: {1}\n", selfDelegate.Target, selfDelegate.Target.GetType().Name);
        }
    }

    public static void ShowAllField(object obj, string fieldName, Type parentType, int depth, ref StringBuilder sb, ref HashSet<object> objSet)
    {
        if (obj == null)
            return;

        if (depth > 20)
            return;

        if (objSet.Contains(obj))
            return;
        objSet.Add(obj);

        var t = obj.GetType();
        if (t == typeof(System.Object) || TKFrameworkDelegateInterface.ComponentRefUtil_IsChessPlayerConfigOrDataBaseManagerType(t) || t == typeof(TKFrame.LoadedAsset)
            //|| t == typeof(TKFrame.Item.PopManager)
            || typeof(Wup.Jce.JceStruct).IsAssignableFrom(t))
            return;
        if (TKFrameworkDelegateInterface.ComponentRefUtil_IsMicroObjectOrCSOGameOrCPlayerType(t)
            || t.Name.Contains("BehaviorCollection")) // 这些不用展开了 
        {
            Tab(depth, ref sb);
            sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2} 泄漏点！！\n", fieldName, t.Name, parentType.FullName);
            return;
        }
        if (t == typeof(GameObject))
        {
            var go = obj as GameObject;
            if (go == null)
            {
                Tab(depth, ref sb);
                sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2} UI变量，泄漏点！！\n", fieldName, t.Name, parentType.FullName);
                return;
            }
        }
        if (typeof(Component).IsAssignableFrom(t))
        {
            var component = obj as Component;
            if (component == null)
            {
                Tab(depth, ref sb);
                sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2} UI变量，泄漏点！！\n", fieldName, t.Name, parentType.FullName);
                return;
            }
        }

        if (!typeof(Delegate).IsAssignableFrom(t))  // 委托不需要检测黑名单 
        {
            if (IsBackList(t))
                return;
        }

        var collection = obj as ICollection;
        if (collection != null && collection.Count == 0)
            return;
        if (t.IsArray)
        {
            var elementType = t.GetElementType();
            if (IsSimpleType(elementType))
                return;
        }
        else if (IsSimpleType(t))
            return;
        else if (t.BaseType == typeof(MulticastDelegate))
        {
            Tab(depth, ref sb);
            sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2} 多播委托\n", fieldName, t.Name, parentType.FullName);
            ShowMulticastDelegate(depth + 1, obj, ref sb);
            return;
        }
        else if (t.BaseType == typeof(Delegate))
        {
            Tab(depth, ref sb);
            sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2} 委托\n", fieldName, t.Name, parentType.FullName);
            var delegateObj = obj as Delegate;
            if (delegateObj != null && delegateObj.Target != null)
            {
                Tab(depth, ref sb);
                sb.AppendFormat("\t自身目标: {0} 类型: {1}\n", delegateObj.Target, delegateObj.Target.GetType().Name);
            }
            return;
        }
        else
        {
            var enumerable = obj as IEnumerable;
            if (enumerable != null)
            {
                Tab(depth, ref sb);
                sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2} 迭代器\n", fieldName, t.Name, parentType.FullName);
                var e = enumerable.GetEnumerator();
                while (e.MoveNext())
                {
                    if (IsSimpleType(obj.GetType()))
                        continue;
                    for (int i = 0; i < depth + 1; ++i)
                        sb.Append("\t");
                    sb.AppendFormat("迭代值: {0}\n", e.Current);
                    ShowAllField(e.Current, "值", obj.GetType(), depth + 2, ref sb, ref objSet);
                }
                return;
            }
        }

        Tab(depth, ref sb);
        sb.AppendFormat("变量名: {0} 类型: {1} 所属类: {2}\n", fieldName, t.Name, parentType.FullName);
        var fields = GetFullFields(t); // t.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
        foreach (var field in fields)
        {
            if (IsSimpleType(field.FieldType))
                continue;
            if (field.FieldType.IsArray)
            {
                var elementType = field.FieldType.GetElementType();
                if (elementType.IsValueType)
                    continue;
                if (elementType == typeof(String) || elementType == typeof(StringBuilder))
                    continue;
            }
            if (field.FieldType.ContainsGenericParameters || t.ContainsGenericParameters)
            {
                sb.AppendFormat("成员变量: {0} 所属类: {1} | {2} {3}\n", field.FieldType.Name, t.Name, field.FieldType.ContainsGenericParameters, t.ContainsGenericParameters);
                continue;
            }
            //Debug.Log(string.Format("field.type: {0} field.name:{1} type:{2}", field.FieldType.FullName, field.Name, t.FullName));
            if (field.FieldType.Name.Contains("*"))
                continue;
            var fieldValue = field.GetValue(obj);
            if (fieldValue != null)
            {
                Tab(depth + 1, ref sb);
                if (field.FieldType.BaseType == typeof(MulticastDelegate))
                {
                    sb.AppendFormat("成员变量: {0} 类型: {1} 多播委托\n", field.Name, field.FieldType.Name);
                    ShowMulticastDelegate(depth + 1, fieldValue, ref sb);
                }
                else if (field.FieldType.BaseType == typeof(Delegate))
                {
                    sb.AppendFormat("成员变量: {0} 类型: {1} 委托\n", field.Name, field.FieldType.Name);
                    var delegateObj = fieldValue as Delegate;
                    if (delegateObj != null && delegateObj.Target != null)
                    {
                        Tab(depth + 1, ref sb);
                        sb.AppendFormat("自身目标: {0} 类型: {1}\n", delegateObj.Target, delegateObj.Target.GetType().Name);
                    }
                    continue;
                }
                else if (field.FieldType.IsArray)
                {
                    sb.AppendFormat("成员变量: {0} 类型: {1} 数组 维度{2}\n", field.Name, field.FieldType.Name, field.FieldType.GetArrayRank());
                    if (field.FieldType.GetArrayRank() == 1)
                    {
                        IList list = (Array)fieldValue;
                        for (int i = 0; i < list.Count; i++)
                        {
                            object item = list[i];
                            if (item == null)
                                continue;
                            if (IsSimpleType(item.GetType()))
                                continue;
                            Tab(depth + 1, ref sb);
                            sb.AppendFormat("成员索引: {0} 值: {1}\n", i, item);
                            ShowAllField(item, "值", field.FieldType, depth + 2, ref sb, ref objSet);
                        }
                    }
                }
                else
                {
                    var enumerable = fieldValue as IEnumerable;
                    if (enumerable != null)
                    {
                        var dictEnumerable = enumerable as IDictionary;
                        if (dictEnumerable != null)
                        {
                            sb.AppendFormat("成员变量: {0} 类型: {1} 字典迭代器\n", field.Name, field.FieldType.Name);
                            IDictionaryEnumerator e = dictEnumerable.GetEnumerator();
                            while (e.MoveNext())
                            {
                                if (IsSimpleType(e.Key.GetType()) && IsSimpleType(e.Value.GetType()))
                                    continue;
                                Tab(depth + 1, ref sb);
                                sb.AppendFormat("迭代值: key:{0} value:{1}\n", e.Key, e.Value);
                                ShowAllField(e.Value, "值", field.FieldType, depth + 2, ref sb, ref objSet);
                            }
                        }
                        else
                        {
                            sb.AppendFormat("成员变量: {0} 类型: {1} 迭代器\n", field.Name, field.FieldType.Name);
                            var e = enumerable.GetEnumerator();
                            while (e.MoveNext())
                            {
                                if (e.Current is DictionaryEntry)
                                {
                                    var dictEntry = (DictionaryEntry)e.Current;
                                    if (IsSimpleType(dictEntry.Key.GetType()) && IsSimpleType(dictEntry.Value.GetType()))
                                        continue;
                                    Tab(depth + 1, ref sb);
                                    sb.AppendFormat("迭代值: key:{0} value:{1}\n", dictEntry.Key, dictEntry.Value);
                                    ShowAllField(dictEntry.Value, "值", field.FieldType, depth + 2, ref sb, ref objSet);
                                }
                                else
                                {
                                    if (IsSimpleType(e.Current.GetType()))
                                        continue;
                                    Tab(depth + 1, ref sb);
                                    sb.AppendFormat("迭代值: {0}\n", e.Current);
                                    ShowAllField(e.Current, "值", field.FieldType, depth + 2, ref sb, ref objSet);
                                }
                            }
                        }
                        continue;
                    }

                    sb.AppendFormat("成员变量: {0} 类型: {1}\n", field.Name, field.FieldType.Name);
                }

                ShowAllField(fieldValue, field.FieldType.Name, t, depth + 1, ref sb, ref objSet);
            }
        }
    }

    public static bool IsBackList(Type t)
    {
        if (t.FullName.StartsWith("System.") || t.FullName.StartsWith("UnityEngine.") || t.FullName.StartsWith("UnityEditor.") || t.FullName.StartsWith("XLua.") || t.FullName.StartsWith("LightProfiler.") || t.FullName.StartsWith("Lucifer.ActCore.Serialization."))
            return true;
        return false;
    }

    public static List<FieldInfo> GetFullFields(Type t)
    {
        List<FieldInfo> fields = new List<FieldInfo>();
        while(!IsBackList(t) && t != typeof(System.Object))
        {
            fields.AddRange(t.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic));
            t = t.BaseType;
        }
        return fields;
    }

    public static void Tab(int depth, ref StringBuilder sb)
    {
        for (int i = 0; i < depth; ++i)
            sb.Append("\t");
        if (depth == 0)
            sb.Append("静态变量根节点 -> ");
    }

    public static bool IsSimpleType(Type t)
    {
        if (t.IsValueType || t.IsEnum)
            return true;
        if (t == typeof(String) || t == typeof(StringBuilder))
            return true;
        return false;
    }
#endif
}