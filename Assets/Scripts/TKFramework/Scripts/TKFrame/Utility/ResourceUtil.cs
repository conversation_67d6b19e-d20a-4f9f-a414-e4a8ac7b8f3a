
#if ACGGAME_CLIENT
using System;
using UnityEngine;
using System.Collections;
using TKFrame;
using System.Collections.Generic;

/// <summary>
/// 此工具类是供游戏中加载资源使用的，不建议直接使用assetService.LoadAsset进行资源加载
/// </summary>
public partial class ResourceUtil
{ 
    /// <summary>
    /// 异步加载资源（有回调时直接返回）
    /// 资源绑定的是IReleaseList对象  目前包括（TKBehaviour和PopLayout）
    /// </summary>
    /// <returns></returns>
    public static LoadedAsset LoadAssetToReleaseList<T>(string bundlePath, string assetName,
        AssetLoadCallback<T> cb, IReleaseList container) where T : UnityEngine.Object
    {
        IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadAsset = assetService.LoadAsset<T>(bundlePath, assetName, cb, true);
        if (loadAsset != null)
        {
            if (container != null)
            {
                loadAsset.Retain();
                container.addToReleaseList(loadAsset);
            }
        }
        return loadAsset;
    }

    /// <summary>
    /// 异步加载资源（有回调时直接返回）
    /// 资源绑定的是IReleaseList对象  目前包括（TKBehaviour和PopLayout）
    /// </summary>
    /// <returns></returns>
    public static LoadedAsset LoadAsset<T>(string bundlePath, string assetName,
        AssetLoadCallback<T> cb, IReleaseList container = null, bool cacheable = true) where T : UnityEngine.Object
    {
        IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadAsset = assetService.LoadAsset<T>(bundlePath, assetName, cb, cacheable);
        if (loadAsset != null)
        {
            if (container != null)
            {
                loadAsset.Retain();
                container.addToReleaseList(loadAsset);
            }
        }
        return loadAsset;
    }

    ///同步加载资源  资源绑定的是IReleaseList对象  目前包括（TKBehaviour和PopLayout）
    ///注意：需使用方在读取本资源之前，确保相关AssetBundle加载完毕,使用while(assetServece.Loading)
    public static LoadedAsset LoadAssetToReleaseListSync<T>(string bundlePath, string assetName, IReleaseList container) where T : UnityEngine.Object
    {
        IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadAsset = assetService.LoadAssetSync<T>(bundlePath, assetName);
        if (loadAsset != null)
        {
            if (container != null)
            {
                loadAsset.Retain();
                container.addToReleaseList(loadAsset);
            }
        }
        return loadAsset;
    }

    //根据asset的路径和名字，获取asset
    public static LoadedAsset GetAssetByPathAndName<T>(string bundlePath, string assetName) where T : UnityEngine.Object
    {
        IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadAsset = assetService.GetLoadedAsset(bundlePath, assetName);
        return loadAsset;
    }

	/// <summary>
	/// 异步加载资源（分帧执行回调，避免主线程卡顿）
	/// cb: 应用层传入的call back函数
	/// outCb: 返回给应用层的call back函数，注意：如果应用层需要cancel掉还未执行的call back，这时传入给cancel就是这个值
	/// </summary>
	/// <returns></returns>
	public static LoadedAsset LoadAssetSmooth<T>(string bundlePath, string assetName, AssetLoadCallback<T> cb, 
		out AssetLoadCallback<T> outCb, IReleaseList container = null, bool isFirstLoad = false) where T : UnityEngine.Object
	{
		outCb = null;
		if (cb != null)
		{
			outCb = (obj, name) =>
			{

				string taskName = "Load Callback: " + name;
				SystemManager.getInstance().PostExpensiveTask(null, taskName, () =>
				{
					if (cb.Method.IsStatic || cb.Target != null)
					{
						cb(obj, name);
					}
				});
			};
		}
		IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadAsset = assetService.LoadAsset<T>(bundlePath, assetName, outCb, false, isFirstLoad);
		if (loadAsset != null)
		{
			if (container != null)
			{
				loadAsset.Retain();
				container.addToReleaseList(loadAsset);
			}
		}
		return loadAsset;
	}
    
    /// <summary>
    /// 异步加载资源（分帧执行回调，避免主线程卡顿）
    /// cb: 应用层传入的call back函数
    /// outCb: 返回给应用层的call back函数，注意：如果应用层需要cancel掉还未执行的call back，这时传入给cancel就是这个值
    /// </summary>
    /// <returns></returns>
    public static LoadedAsset LoadAssetSmoothBackground<T>(string bundlePath, string assetName, AssetLoadCallback<T> cb, 
        out AssetLoadCallback<T> outCb, IReleaseList container = null, bool isFirstLoad = false) where T : UnityEngine.Object
    {
        outCb = null;
        if (cb != null)
        {
            outCb = (obj, name) =>
            {

                string taskName = "Load Callback: " + name;
                SystemManager.getInstance().PostExpensiveTask(null, taskName, () =>
                {
                    if (cb.Method.IsStatic || cb.Target != null)
                    {
                        cb(obj, name);
                    }
                });
            };
        }
        IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadAsset = assetService.LoadAssetBackground<T>(bundlePath, assetName, outCb, isFirstLoad);
        if (loadAsset != null)
        {
            if (container != null)
            {
                loadAsset.Retain();
                container.addToReleaseList(loadAsset);
            }
        }
        return loadAsset;
    }

    /// <summary>
    /// 依赖AssetBundle加载完毕
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="assetBundlePath"></param>
    /// <param name="cb"></param>
    /// <returns></returns>
    public static List<LoadedAsset> LoadedAllAsset<T>(string assetBundlePath, AssetLoadCallback<T> cb, IReleaseList container) where T : UnityEngine.Object
    {
        List<LoadedAsset> assetList = new List<LoadedAsset>();
        string[] allAssetNames = null;
        string error;
        LoadedAssetBundle loadedAssetbundle = AssetBundleManager.GetLoadedAssetBundle(assetBundlePath, out error);

        if (loadedAssetbundle != null)
        {
#if ENABLE_ASSETBUNDLE_PATH_DELETE
            if (AssetBundle.GetAssetBundlePathDelete())
            {
                if (loadedAssetbundle.m_AssetBundle.isStreamedSceneAssetBundle)
                {
                    allAssetNames = loadedAssetbundle.m_AssetBundle.GetAllAssetNames();
                    if(allAssetNames != null)
                    {
                        for (int i = 0; i < allAssetNames.Length; i++)
                        {
                            allAssetNames[i] = AssetPathToName(allAssetNames[i]);
                        }
                    }
                }
                else
                {
                    allAssetNames = loadedAssetbundle.m_AssetBundle.GetAllAssetNames();
                }
            }
            else
#endif
            {
                allAssetNames = loadedAssetbundle.m_AssetBundle.GetAllAssetNames();
                if(allAssetNames != null)
                {
                    for (int i = 0; i < allAssetNames.Length; i++)
                    {
                        allAssetNames[i] = AssetPathToName(allAssetNames[i]);
                    }
                }
            }
        }
        
#if UNITY_EDITOR
        if(AssetBundleManager.SimulateAssetBundleInEditor)
        {
#if  ENABLE_ASSET_BUNDLE_EXTEND
            if (assetBundlePath.Contains(".unity3d") == false)
            {
                assetBundlePath += ".unity3d";
            }
#endif
            allAssetNames = UnityEditor.AssetDatabase.GetAssetPathsFromAssetBundle(assetBundlePath);
#if  ENABLE_ASSET_BUNDLE_EXTEND
            if (assetBundlePath.Contains(".unity3d") == true)
            {
                assetBundlePath = assetBundlePath.Replace(".unity3d", "") ;
            }
#endif
            for(int i = 0; i < allAssetNames.Length; i++)
            {
                allAssetNames[i] = AssetPathToName(allAssetNames[i]);
            }
        }
#endif
        IAssetService assetService = Services.GetService<IAssetService>();
        if (allAssetNames != null && allAssetNames.Length > 0)
        {
            for (int i = 0; i < allAssetNames.Length; i++)
			{
                LoadedAssetContainer assetContainer = assetService.GetLoadedAssetContainer(assetBundlePath);
                LoadedAsset loadedAsset = assetContainer.GetLoaedAsset(allAssetNames[i]);
                if(loadedAsset == null)
                {
                    loadedAsset = assetContainer.NewLoaedAsset(assetBundlePath, allAssetNames[i],false);
                }

                assetList.Add(loadedAsset);
                if (container != null)
                {
                    loadedAsset.Retain();
                    container.addToReleaseList(loadedAsset);
                }
            }
        }
        AssetBundleLoadAllCallback loadCallback = (assetBundleName, allAsset) =>
        {
            if(allAsset != null && allAsset.AssetArr != null)
            {
                for( int i = 0; i < allAsset.AssetArr.Length;i++ )
                {
                    T tAsset = allAsset.AssetArr[i] as T;
                    if(tAsset != null)
                    {
                       
                        LoadedAsset loadedAssset = assetList[i];
                        if (loadedAssset != null)
                        {
                            loadedAssset.Asset = tAsset;
                            loadedAssset.AssetName = tAsset.name;
                        }
                        cb(tAsset, assetBundlePath);
                    }
                }
            }
        };
        assetService.LoadAllAsset(assetBundlePath, loadCallback);
        return assetList;
    }

    private static string AssetPathToName(string assetPath)
    {
        string assetName = string.Empty;
        FastStringSplit splits = assetPath.BeginSplit('/');
        if (splits.Length > 0)
        {
            assetName = splits[splits.Length - 1];
            int index = assetName.LastIndexOf('.');
            if (index != -1)
                assetName = assetName.Substring(0, index);
        }
        splits.EndSplit();
        return assetName;
    }
}  
#endif

