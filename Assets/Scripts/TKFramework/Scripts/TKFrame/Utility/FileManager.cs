using System;
using System.Collections.Generic;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Threading;
using UnityEngine;
using UnityEngine.Networking;

namespace TKFrame
{
    public class FileManager : Singleton<FileManager>
    {
        #region Read Stream
        
        private TKDictionary<string, MemoryMappedFile> m_allFileStreams = new TKDictionary<string, MemoryMappedFile>();

        public bool CheckMMap(string path)
        {
            if (path == null)
                return false;
            
            CloseMMap(path);
            bool result = OpenMMap(path);
            CloseMMap(path);
            return result;
        }
        
        public bool OpenMMap(string path)
        {
            if (path == null)
                return false;

            MemoryMappedFile mmapFile = null;
            if (!m_allFileStreams.TryGetValue(path, out mmapFile))
            {
                try
                {
                    mmapFile = MemoryMappedFile.CreateFromFile(path, FileMode.Open);
                    using (Stream stream = mmapFile.CreateViewStream())
                    {
                        m_allFileStreams.Add(path, mmapFile);
                    }
                }
                catch (Exception e)
                {
                    Diagnostic.Log("Open MMap File Failed " + path);
                    Diagnostic.Log(e);
                    return false;
                }
            }

            return true;
        }

        public void CloseMMap(string path)
        {
            if (path == null)
                return;
            MemoryMappedFile mmapFile = null;
            if (m_allFileStreams.TryGetValue(path, out mmapFile))
            {
                mmapFile.Dispose();
                m_allFileStreams.Remove(path);
            }
        }

        public void CloseAllMMap()
        {
            foreach (MemoryMappedFile mmapFile in m_allFileStreams.Values)
            {
                mmapFile.Dispose();
            }
            m_allFileStreams.Clear();
        }

        public Stream GetReadStream(string path)
        {
            if (path == null)
                return null;
            
            MemoryMappedFile mmapFile = null;
            if (m_allFileStreams.TryGetValue(path, out mmapFile))
            {
                return mmapFile.CreateViewStream();
            }
#if ACGGAME_CLIENT
            return new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read);
#elif ACGGAME_AUTOFIGHT
            return new BufferedStream(new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read),16384);
#endif
        }

        public ReadOnlyNativeArrayStream GetReadStream(TextAsset textAsset)
        {
            if (textAsset == null)
                return null;

            return new ReadOnlyNativeArrayStream(textAsset);
        }

        public ReadOnlyNativeArrayStream GetReadStream(DownloadHandler downloadHandler)
        {
            if (downloadHandler == null)
                return null;

            return new ReadOnlyNativeArrayStream(downloadHandler);
        }
        
        public ReadOnlyNativeArrayStream GetReadStream(WWW www)
        {
            if (www == null)
                return null;

            return new ReadOnlyNativeArrayStream(www);
        }

        public MemoryStream GetReadStream(byte[] data)
        {
            if (data == null)
                return null;

            return new MemoryStream(data);
        }
        
        public static void CopyStream(Stream src, Stream dest)
        {
            byte[] buffer = BuffPool.Instance.Get();
            try
            {
                int readCount;
                do
                {
                    readCount = src.Read(buffer, 0, buffer.Length);
                    dest.Write(buffer, 0, readCount);
                } while (readCount > 0);
                dest.Flush();
            }
            catch (Exception e)
            {
                Diagnostic.Log(e);
            }
            finally
            {
                BuffPool.Instance.Recycle(buffer);
            }
        }
        
        #endregion
        
        #region Cache目录
        
        private string m_dir = string.Empty;
        private string m_oldDir = string.Empty;

        private string GetDirName()
        {
            if (string.IsNullOrEmpty(m_dir))
            {
                m_dir = PlayerPrefs.GetString("TmpDir", string.Empty);
                if (string.IsNullOrEmpty(m_dir))
                {
                    m_dir = Guid.NewGuid().ToString("D") + "/";
                    PlayerPrefs.SetString("TmpDir", m_dir);
                }
                Diagnostic.Log("Get TmpDir " + m_dir);
            }

            return m_dir;
        }

        public string GetOldTmpFile(string fileName)
        {
            if (!string.IsNullOrEmpty(m_oldDir))
            {
                string oldDir = TKApplication.StoragePath + m_oldDir;
                string oldFile = oldDir + "Temp_" + fileName + "_Cache.ini";
                return oldFile;
            }

            return string.Empty;
        }

        public string GetTmpFile(string fileName, bool isTmp = false)
        {
            if (!isTmp)
            {
                string dir = TKApplication.StoragePath + GetDirName();
                if (!Directory.Exists(dir))
                    Directory.CreateDirectory(dir);

                string oldFile = GetOldTmpFile(fileName);
                if (!string.IsNullOrEmpty(oldFile))
                {
                    if(File.Exists(oldFile))
                        File.Delete(oldFile);
                }

                return dir + "Temp_" + fileName + "_Cache.ini";
            }
            else
            {
                string dir = TKApplication.StoragePath + "TmpData/";
                if (!Directory.Exists(dir))
                    Directory.CreateDirectory(dir);
                
                string path = dir + "Temp_" + fileName + "_Cache.ini";
                if(File.Exists(path))
                    File.Delete(path);

                return path;
            }
        }

        public void RemoveOldTmpDir()
        {
            string oldDir = PlayerPrefs.GetString("OldTmpDir", string.Empty);
            if (!string.IsNullOrEmpty(oldDir))
            {
                string dir = TKApplication.StoragePath + oldDir;
                Diagnostic.Log("Delete Old Dir " + dir);
                if (Directory.Exists(dir))
                {
                    Directory.Delete(dir, true);
                }
                PlayerPrefs.SetString("OldTmpDir", string.Empty);
            }

            string newDir = PlayerPrefs.GetString("TmpDir", string.Empty);
            if(!string.IsNullOrEmpty(newDir))
                newDir = newDir.Substring(0, newDir.Length - 1);
            string[] dirs = Directory.GetDirectories(TKApplication.StoragePath);
            foreach (string dir in dirs)
            {
                string dirName = Path.GetFileNameWithoutExtension(dir);
                if(dirName == newDir)
                    continue;
                
                if (Guid.TryParse(dirName, out Guid result))
                {
                    Diagnostic.Log("Delete Other Old Dir " + dirName);
                    Directory.Delete(dir, true);
                }
            }
        }

        public void RenameTmpDir()
        {
            m_oldDir = GetDirName();
            while (m_oldDir == m_dir)
            {
                m_dir = Guid.NewGuid().ToString("D") + "/";
            }
            
            string dir = TKApplication.StoragePath + m_dir;
            if(Directory.Exists(dir))
                Directory.Delete(dir, true);
            
            PlayerPrefs.SetString("TmpDir", m_dir);
            PlayerPrefs.SetString("OldTmpDir", m_oldDir);
            Diagnostic.Log("Change TmpDir From " + m_oldDir + " To " + m_dir);
        }
        
        #endregion
    }
}


