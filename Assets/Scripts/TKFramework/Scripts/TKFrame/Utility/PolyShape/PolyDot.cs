using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TikiComm
{
    /// <summary>
    /// 显示多个坐标点
    /// </summary>
    public class PolyDot : MaskableGraphic
    {

        [SerializeField]
        private Vector2[] m_pointList;

        [SerializeField]
        Texture m_Texture;

        [SerializeField]
        private float m_pointWidth;

        //[SerializeField] Rect m_UVRect = new Rect(0f, 0f, 1f, 1f);

        /// <summary>
        /// Returns the texture used to draw this Graphic.
        /// </summary>
        public override Texture mainTexture
        {
            get
            {
                if (m_Texture == null)
                {
                    if (material != null && material.mainTexture != null)
                    {
                        return material.mainTexture;
                    }
                    return s_WhiteTexture;
                }

                return m_Texture;
            }
        }

        /// <summary>
        /// Texture to be used.
        /// </summary>
        public Texture texture
        {
            get
            {
                return m_Texture;
            }
            set
            {
                if (m_Texture == value)
                    return;

                m_Texture = value;
                SetVerticesDirty();
                SetMaterialDirty();
            }
        }

        private void MakePoint(int index, VertexHelper vh)
        {
            Vector2 point = m_pointList[index];
            Vector2 lb = new Vector2(point.x - m_pointWidth / 2f, point.y - m_pointWidth / 2);
            Vector2 rt = new Vector2(point.x + m_pointWidth / 2f, point.y + m_pointWidth / 2);

            var color32 = color;
            vh.AddVert(new Vector3(lb.x, lb.y), color32, new Vector2(0, 0));
            vh.AddVert(new Vector3(lb.x, rt.y), color32, new Vector2(0, 1));
            vh.AddVert(new Vector3(rt.x, rt.y), color32, new Vector2(1, 1));
            vh.AddVert(new Vector3(rt.x, lb.y), color32, new Vector2(1, 0));

            int start_tri_index = index * 4;

            vh.AddTriangle(0 + start_tri_index, 1 + start_tri_index, 2 + start_tri_index);
            vh.AddTriangle(2 + start_tri_index, 3 + start_tri_index, 0 + start_tri_index);
        }

        protected override void OnPopulateMesh(VertexHelper vh)
        {
            vh.Clear();

            if (m_pointList == null || m_pointList.Length == 0)
                return;

            for (int i = 0; i < m_pointList.Length; i++)
            {
                MakePoint(i, vh);
            }
        }
    }
}