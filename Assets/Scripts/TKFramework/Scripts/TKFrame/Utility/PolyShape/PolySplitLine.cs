using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TikiComm
{
    /// <summary>
    /// 显示多个分隔竖线
    /// </summary>
    public class PolySplitLine : MaskableGraphic
    {
        [SerializeField]
        private float m_Count = 0;

        [SerializeField]
        Texture m_Texture = null;

        [SerializeField]
        private float m_Width = 2;
		
		[SerializeField]
		private float m_Height = 5;
		//[SerializeField] Rect m_UVRect = new Rect(0f, 0f, 1f, 1f);

		/// <summary>
		/// Returns the texture used to draw this Graphic.
		/// </summary>
		public override Texture mainTexture
        {
            get
            {
                if (m_Texture == null)
                {
                    if (material != null && material.mainTexture != null)
                    {
                        return material.mainTexture;
                    }
                    return s_WhiteTexture;
                }

                return m_Texture;
            }
        }

        /// <summary>
        /// Texture to be used.
        /// </summary>
//         public Sprite texture
//         {
//             get
//             {
//                 return m_Texture;
//             }
//             set
//             {
//                 if (m_Texture == value)
//                     return;
// 
//                 m_Texture = value;
//                 SetVerticesDirty();
//                 SetMaterialDirty();
//             }
//         }

        private void MakePoint(Vector2 point, int index, float height, VertexHelper vh)
        {
            Vector2 lb = new Vector2(point.x - m_Width / 2f, point.y - height / 2);
            Vector2 rt = new Vector2(point.x + m_Width / 2f, point.y + height / 2);

            var color32 = color;
            vh.AddVert(new Vector3(lb.x, lb.y), color32, new Vector2(0, 0));
            vh.AddVert(new Vector3(lb.x, rt.y), color32, new Vector2(0, 1));
            vh.AddVert(new Vector3(rt.x, rt.y), color32, new Vector2(1, 1));
            vh.AddVert(new Vector3(rt.x, lb.y), color32, new Vector2(1, 0));

            int start_tri_index = index * 4;

            vh.AddTriangle(0 + start_tri_index, 1 + start_tri_index, 2 + start_tri_index);
            vh.AddTriangle(2 + start_tri_index, 3 + start_tri_index, 0 + start_tri_index);
        }

		public void SetProperties(float count)
		{
			// no change 
			if (System.Math.Abs(m_Count-count) < 0.000001f)
				return;

// 			if (material != null)
// 			{
// 				material.SetTexture("_MainTex", icon.texture);
// 				material.SetTextureOffset("_MainTex", new Vector2(icon.textureRect.x / icon.texture.width, icon.textureRect.y / icon.texture.height));
// 				material.SetTextureScale("_MainTex", new Vector2(icon.textureRect.width / icon.texture.width, icon.textureRect.height / icon.texture.height));
// 			}
			m_Count = count;
			SetVerticesDirty();
		}

        protected override void OnPopulateMesh(VertexHelper vh)
        {
            vh.Clear();

            if (m_Count < 1)
                return;

			Vector2 point;
			RectTransform rt = (RectTransform)gameObject.transform;
			int count = (int)m_Count;

			//check whether m_Count is int
			if (System.Math.Abs(m_Count - count) < 0.000001f)
				count -= 1;

            for (int i = 1; i < count+1; i++)
            {
				point = new Vector2(rt.rect.x + rt.rect.width*((float)i/m_Count), rt.rect.y+rt.rect.height/2);
                MakePoint(point, i-1, m_Height, vh);
            }
        }
    }
}