using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TKFrame
{
    public class CoroutineWait
    {
        public static WaitForEndOfFrame waitForEndFrame = new WaitForEndOfFrame();

        public static WaitForFixedUpdate waitForFixedUpdate = new WaitForFixedUpdate();

        public static WaitForSeconds WaitForSeconds_0 = new WaitForSeconds(0.0f);
        public static WaitForSeconds WaitForSeconds_0_05 = new WaitForSeconds(0.05f);
        public static WaitForSeconds WaitForSeconds_0_1 = new WaitForSeconds(0.1f);
        public static WaitForSeconds WaitForSeconds_0_2 = new WaitForSeconds(0.2f);
        public static WaitForSeconds WaitForSeconds_0_25 = new WaitForSeconds(0.25f);
        public static WaitForSeconds WaitForSeconds_0_3 = new WaitForSeconds(0.3f);
        public static WaitForSeconds WaitForSeconds_0_4 = new WaitForSeconds(0.4f);
        public static WaitForSeconds WaitForSeconds_0_5 = new WaitForSeconds(0.5f);
        public static WaitForSeconds WaitForSeconds_0_6 = new WaitForSeconds(0.6f);
        public static WaitForSeconds WaitForSeconds_0_7 = new WaitForSeconds(0.7f);
        public static WaitForSeconds WaitForSeconds_0_8 = new WaitForSeconds(0.8f);
        public static WaitForSeconds WaitForSeconds_0_9 = new WaitForSeconds(0.9f);
        public static WaitForSeconds WaitForSeconds_1_0 = new WaitForSeconds(1.0f);
        public static WaitForSeconds WaitForSeconds_1_1 = new WaitForSeconds(1.1f);
        public static WaitForSeconds WaitForSeconds_1_2 = new WaitForSeconds(1.2f);
        public static WaitForSeconds WaitForSeconds_1_3 = new WaitForSeconds(1.3f);
        public static WaitForSeconds WaitForSeconds_1_4 = new WaitForSeconds(1.4f);
        public static WaitForSeconds WaitForSeconds_1_5 = new WaitForSeconds(1.5f);
        public static WaitForSeconds WaitForSeconds_1_6 = new WaitForSeconds(1.6f);
        public static WaitForSeconds WaitForSeconds_1_7 = new WaitForSeconds(1.7f);
        public static WaitForSeconds WaitForSeconds_1_8 = new WaitForSeconds(1.8f);
        public static WaitForSeconds WaitForSeconds_1_9 = new WaitForSeconds(1.9f);
        public static WaitForSeconds WaitForSeconds_2_0 = new WaitForSeconds(2.0f);
        public static WaitForSeconds WaitForSeconds_2_5 = new WaitForSeconds(2.5f);
        public static WaitForSeconds WaitForSeconds_3_0 = new WaitForSeconds(3.0f);
        public static WaitForSeconds WaitForSeconds_3_8 = new WaitForSeconds(3.8f);
        public static WaitForSeconds WaitForSeconds_4_0 = new WaitForSeconds(4.0f);
        public static WaitForSeconds WaitForSeconds_4_5 = new WaitForSeconds(4.5f);
        public static WaitForSeconds WaitForSeconds_5_0 = new WaitForSeconds(5.0f);
        public static WaitForSeconds WaitForSeconds_6_0 = new WaitForSeconds(6.0f);
        public static WaitForSeconds WaitForSeconds_7_0 = new WaitForSeconds(7.0f);
        public static WaitForSeconds WaitForSeconds_10_0 = new WaitForSeconds(10.0f);
        

        public static WaitForSeconds GetWaitForSeconds(float seconds)
        {
            int time = Mathf.RoundToInt(seconds * 10000f);
            switch (time)
            {
                case 0    : return WaitForSeconds_0;
                case 500  : return WaitForSeconds_0_05;
                case 1000 : return WaitForSeconds_0_1;
                case 2000 : return WaitForSeconds_0_2;
                case 2500 : return WaitForSeconds_0_25;
                case 3000 : return WaitForSeconds_0_3;
                case 4000 : return WaitForSeconds_0_4;
                case 5000 : return WaitForSeconds_0_5;
                case 6000 : return WaitForSeconds_0_6;
                case 7000 : return WaitForSeconds_0_7;
                case 8000 : return WaitForSeconds_0_8;
                case 9000 : return WaitForSeconds_0_9;
                case 10000: return WaitForSeconds_1_0;
                case 11000: return WaitForSeconds_1_1;
                case 12000: return WaitForSeconds_1_2;
                case 13000: return WaitForSeconds_1_3;
                case 14000: return WaitForSeconds_1_4;
                case 15000: return WaitForSeconds_1_5;
                case 16000: return WaitForSeconds_1_6;
                case 17000: return WaitForSeconds_1_7;
                case 18000: return WaitForSeconds_1_8;
                case 19000: return WaitForSeconds_1_9;
                case 20000: return WaitForSeconds_2_0;
                case 25000: return WaitForSeconds_2_5;
                case 30000: return WaitForSeconds_3_0;
                case 38000: return WaitForSeconds_3_8;
                case 40000: return WaitForSeconds_4_0;
                case 45000: return WaitForSeconds_4_5;
                case 50000: return WaitForSeconds_5_0;
                case 60000: return WaitForSeconds_6_0;
                case 70000: return WaitForSeconds_7_0;
                case 100000: return WaitForSeconds_10_0;
            }

            return new WaitForSeconds(seconds);
        }
        
        public static WaitForSecondsRealtime WaitForSecondsRealtime_1_0 = new WaitForSecondsRealtime(1.0f);
        public static WaitForSecondsRealtime WaitForSecondsRealtime_3_0 = new WaitForSecondsRealtime(3.0f);
        public static WaitForSecondsRealtime WaitForSecondsRealtime_30_0 = new WaitForSecondsRealtime(30.0f);

        public static WaitForSecondsRealtime GetWaitForSecondsRealtime(float seconds)
        {
            int time = Mathf.RoundToInt(seconds * 10000f);
            switch (time)
            {
                case 10000: return WaitForSecondsRealtime_1_0;
                case 30000: return WaitForSecondsRealtime_3_0;
                case 300000: return WaitForSecondsRealtime_30_0;
            }
            return new WaitForSecondsRealtime(seconds);
        }
    }
}