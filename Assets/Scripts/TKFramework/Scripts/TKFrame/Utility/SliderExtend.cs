using System;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class SliderExtend : MonoBehaviour, IPointerUpHandler
{
    public Slider slider;
    private float lastValue = 0;
    private Action<float> action = null;

    public void SetCallBack(Action<float> cb, int sliderValue = 0)
    {
        if (sliderValue == 0)
            lastValue = slider.value;
        else
            lastValue = sliderValue;
        action = cb;
    }

    public void RefreshSliderValue(int sliderValue)
    {
        lastValue = sliderValue;
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        if (slider.value == lastValue)
            return;
        //Diagnostic.Log("SliderLuaExtend Value: " + slider.value);
        lastValue = slider.value;
        if (action != null)
        {
            action(slider.value);
        }
    }
}
