#if ACGGAME_CLIENT
using System;
using System.Collections.Generic;
using UnityEngine;

namespace TKFrame
{
    [Serializable]
    public class Pair<T1, T2> : TKObject
    {
        private static int s_HashWidth = 64536;
        [SerializeField]
        private T1 m_A = default(T1);
        [SerializeField]
        private T2 m_B = default(T2);
        public T1 A
        {
            get
            {
                return this.m_A;
            }
            set
            {
                this.m_A = value;
            }
        }
        public T2 B
        {
            get
            {
                return this.m_B;
            }
            set
            {
                this.m_B = value;
            }
        }
        public static int HashWidth
        {
            get
            {
                return Pair<T1, T2>.s_HashWidth;
            }
            set
            {
                Pair<T1, T2>.s_HashWidth = value;
            }
        }
        public static implicit operator bool(Pair<T1, T2> p)
        {
            return p != null;
        }
        public static bool operator ==(Pair<T1, T2> p1, Pair<T1, T2> p2)
        {
            if (p1 == null || p2 == null)
            {
                return p1 == p2;
            }
            return p1.Equals(p2);
        }
        public static bool operator !=(Pair<T1, T2> p1, Pair<T1, T2> p2)
        {
            if (p1 == null || p2 == null)
            {
                return p1 != p2;
            }
            return !p1.Equals(p2);
        }
        public Pair(T1 item1, T2 item2)
        {
            this.m_A = item1;
            this.m_B = item2;
        }
        public Pair(Pair<T1, T2> p)
        {
            this.m_A = p.m_A;
            this.m_B = p.m_B;
        }
        public Pair()
        {
        }
        public override bool Equals(object obj)
        {
            if (obj != null)
            {
                Pair<T1, T2> pair = obj as Pair<T1, T2>;
                if (pair != null)
                {
                    return this.m_A.Equals(pair.m_A) && this.m_B.Equals(pair.m_B);
                }
            }
            return false;
        }
        public override int GetHashCode()
        {
            int arg_19_0 = Pair<T1, T2>.s_HashWidth;
            T2 b = this.B;
            int arg_2E_0 = arg_19_0 * b.GetHashCode();
            T1 a = this.A;
            return arg_2E_0 + a.GetHashCode();
        }
        public override string ToString()
        {
            return string.Concat(new string[]
			{
				"(",
				this.m_A.ToString(),
				", ",
				this.m_B.ToString(),
				")"
			});
        }
    }

}

#endif