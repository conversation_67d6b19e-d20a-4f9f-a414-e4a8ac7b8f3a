using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using TKAction;
using TKFrame.Item;
using TKPlugins;
using UnityEngine;

namespace TKFrame
{

    /************************************************************************/
    /*游戏系统          														*/
    /* 1 管理游戏系统的初始化及生命周期										*/
    /* 2 提供各种基础服务接口，资源加载服务、界面切换服务等		            */
    /************************************************************************/
    public class SystemManager : TKBehaviour
    {
        //游戏系统实例
        private static SystemManager _Instance;

        private TaskQueue _taskQueue = new TaskQueue();
        //主线程ID
        public int ID_MainThread { get; set; }

        public static EventHandler<CoroutineExceptionEventArgs> exceptionHandler = null;

        #region 主线程任务队列 TaskInMainThread
        //主线程任务队列，并对添加/删除操作进行加锁同步
        public void AddTaskInMainThread(Action task)
        {
            TaskInMainThread += task;
        }
        public void RemoveTaskInMainThread(Action task)
        {
            TaskInMainThread -= task;
        }

        public event Action TaskInMainThread
        {
            // 对添加/删除操作进行加锁同步
            remove
            {
                lock (this)
                {
                    //Diagnostic.Warn("RemoveTaskFromMainThread");
                    if (_TaskInMainThread != null)
                        _TaskInMainThread -= value;
                }
            }
            add
            {
                lock (this)
                {
                    //Diagnostic.Warn("AddTaskToMainThread");
                    if (_TaskInMainThread == null)
                        _TaskInMainThread = value;
                    else
                        _TaskInMainThread += value;
                }
            }
        }
        private Action _TaskInMainThread;//存储主线程任务队列
        #endregion

        //管理-系统级弹出窗口
        public PopManager PopManager = new PopManager();
        static DateTime dateTime1970 = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        /// <summary>
        /// 获取当前系统自1970到现在的时间（单位秒）
        /// </summary>
        /// <returns></returns>
        public double GetCurrentClientTime()
        {
            var tick = (System.DateTime.Now - dateTime1970).Ticks;
            return new TimeSpan(tick).TotalSeconds;
        }

        /// <summary>
        /// 获取系统单实例
        /// </summary>
        public static SystemManager getInstance()
        {
            return _Instance;
        }

        /// <summary>
        /// 唤醒
        /// </summary>
        protected override void Awake()
        {
            _Instance = this;

            base.Awake();
            //记录主线程ID
            ID_MainThread = Thread.CurrentThread.ManagedThreadId;
        }

        /// <summary>
        /// 启动初始化
        /// </summary>
        protected override void Start()
        {
            base.Start();

            //启动初始化
            UnityCoroutine.StartCoroutine(this, StartGame());
        }

        /// <summary>
        /// 逻辑更新
        /// </summary>
        public virtual void Update()
        {
            //主线程执行托管函数
            Action taskList;
            lock (this)
            {
                taskList = _TaskInMainThread;//保存任务列表，防止中途被添加删除;
            }
            try
            {
                if (taskList != null)
                {
                    taskList();
                    TaskInMainThread -= taskList;
                }
            }
            catch (Exception e)
            {
                Diagnostic.Error("TaskInMainThread ERROR:" + e.ToString());
            }
        }

        public virtual void LateUpdate()
        {
            _taskQueue.LateUpdate();
        }

        /// <summary>
        /// 显示指定场景
        /// </summary>
        public static void showStage<T>(String sceneName, bool needLoadScene = true, Action<IStage> addStageCallBack = null, bool isGotoPrevious = false, bool bNeedLoading = false, Action LastStageReleaseCallBack = null, bool bReserveGI = false)
        {
            Diagnostic.Log("showStage<{0}>({1}, {2})", typeof(T).Name, sceneName, needLoadScene);
            //if (!TKFrameworkDelegateInterface.Build_ZGAME_RELEASE_ENV())
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                System.Diagnostics.StackFrame[] sfs = st.GetFrames();
                for (int u = 0; u < sfs.Length; ++u)
                {
                    if (u > 3) break;
                    System.Reflection.MethodBase mb = sfs[u].GetMethod();
                    Diagnostic.Log("[CALL STACK][{0}]: {1}.{2}", u, mb.DeclaringType.FullName, mb.Name);
                }
            }
            Type stageType = typeof(T);
            showStage(stageType, sceneName, needLoadScene, addStageCallBack, isGotoPrevious, bNeedLoading,
                LastStageReleaseCallBack, bReserveGI: bReserveGI);
        }

        /// <summary>
        /// 显示指定场景
        /// </summary>
        public static void showStage(Type stageType, String sceneName, bool needLoadScene = true, Action<IStage> addStageCallBack = null, bool isGotoPrevious = false, bool bNeedLoading = false, Action LastStageReleaseCallBack = null, string tkluaStageName = "", bool bReserveGI = false)
        {
            IStageRunner stageRuner = Services.GetService<IStageRunner>();
            if (stageRuner.CurrentStage != null)
            {
                if (stageRuner.CurrentStageName == sceneName)
                {
                    Diagnostic.Error("BreakShowStage reason: show same stage at current stage cur:{0}, aim:{1}, stageType:{2}", stageRuner.CurrentStageName, sceneName, stageType.Name);
                    return;
                }
            }
            stageRuner.ShowStage(stageType, sceneName, needLoadScene, addStageCallBack, isGotoPrevious, bNeedLoading, exceptionHandler, LastStageReleaseCallBack, tkluaStageName, bReserveGI);
        }

        /// <summary>
        /// 获取场景服务
        /// </summary>
        public IStage GetStage()
        {
            IStageRunner _stageRuner = Services.GetService<IStageRunner>();
            if (_stageRuner != null && _stageRuner.CurrentStage != null)
            {
                return _stageRuner.CurrentStage;
            }
            return null;
        }

        /// <summary>
        /// 启动游戏
        /// </summary>
        protected virtual IEnumerator StartGame()
        {
            Diagnostic.Warn("[Game Starting] wait for UnityEngine.Caching.ready :{0}", UnityEngine.Time.realtimeSinceStartup);
            while (!UnityEngine.Caching.ready)
            {
                yield return null;
            }

            Diagnostic.Warn("[Game Starting] UnityEngine.Caching.readyed :{0}", UnityEngine.Time.realtimeSinceStartup);

            //启动初始化
            yield return Initialize();
            //启动首界面
            yield return EnterFirstStage();
        }

        /// <summary>
        /// 启动初始化
        /// </summary>
        protected virtual IEnumerator Initialize()
        {
            //挂接TKAction动画机制：提供运动、特效等动画功能，类似cocos2d动画机制
            gameObject.AddComponent(typeof(TKActionBehaviour));

            //挂接资源服务：提供资源加载卸载等接口功能
            AssetService assetService = gameObject.AddComponent(typeof(AssetService)) as AssetService;
            yield return assetService.Initialize();
            Diagnostic.Log("SystemManager :assetService.Initialize");
            //挂接场景服务：提供场景加载卸载接口功能
            SceneLoader sceneLoader = gameObject.AddComponent(typeof(SceneLoader)) as SceneLoader;
            yield return sceneLoader.Initialize();
            Diagnostic.Log("SystemManager :sceneLoader.Initialize");
            //挂接事件服务：提供事件功能
            EventManager eventMgr = gameObject.AddComponent(typeof(EventManager)) as EventManager;
            yield return eventMgr.Initialize();
            if (EnableShaderManager())
            {
                //挂接Shader服务：提供各种Shader
                ShaderManager shaderMgr = gameObject.AddComponent(typeof(ShaderManager)) as ShaderManager;
                yield return shaderMgr.Initialize();
                Diagnostic.Log("SystemManager :shaderMgr.Initialize");
            }


            //挂接相机管理服务：提供相机管理
            TKCameraManager camMgr = gameObject.AddComponent(typeof(TKCameraManager)) as TKCameraManager;
            yield return camMgr.Initialize();

            //挂接时间控制服务：提供各种加速、减速等时间控制功能
            CustomTimeManager timeMgr = gameObject.AddComponent(typeof(CustomTimeManager)) as CustomTimeManager;
            yield return timeMgr.Initialize();

            //挂接场景切换服务：提供场景切换资源加载等功能
            StageRunner stageRunner = gameObject.AddComponent(typeof(StageRunner)) as StageRunner;
            yield return stageRunner.Initialize();
            Diagnostic.Log("SystemManager :stageRunner.Initialize");

            // Fake light manager provides global fake light which could be used by other shaders. This is more efficient compare to Unity built in lights.
            var flMgr = gameObject.AddComponent<FakeLightCurveManager>();
            yield return flMgr.Initialize();

        }

        public void PostExpensiveTask(UnityEngine.Object lifecircleObj, string taskName, Action taskAction, int priority = TaskQueue.PRIORITY_NORMAL)
        {
            if (Thread.CurrentThread.ManagedThreadId != ID_MainThread)
            {
                throw new Exception("PostExpensiveTask只能由主线程调用");
            }
            _taskQueue.PostTask(lifecircleObj, taskName, taskAction, priority);
        }

        /// <summary>
        /// 启动首界面(比如闪屏界面，由游戏子类指定)
        /// </summary>
        public virtual IEnumerator EnterFirstStage()
        {
            yield return null;
        }

        /// <summary>
        /// 开启Shader管理
        /// </summary>
        protected virtual bool EnableShaderManager()
        {
            return true;
        }

        /// <summary>
        /// 清除不使用的loaded Asset 资源。并调用Unity Unload Unused Asset等待调用完成
        /// </summary>
        /// <returns>The unused asset.</returns>
        /// <param name="ccache">If set to <c>true</c> ccache.</param>
        public virtual IEnumerator UnloadUnusedAsset(bool ccache)
        {
            IAssetService assetService = Services.GetService<IAssetService>();

            assetService.UnloadUnusedLoadedAsset();
            if (ccache)
            {
                assetService.ClearCacheAssets();
            }

            yield return null;
            UnityEngine.Profiling.Profiler.BeginSample("SystemManager.UnloadUnusedAsset.GC");
            GC.Collect();
            UnityEngine.Profiling.Profiler.EndSample();
            yield return null;
            assetService.RequestUnloadUnityUnusedAsset();

            Diagnostic.Log("SystemManager UnloadUnusedAsset begin");
            while (assetService.AssetUnloading)
            {
                yield return null;
            }
            Diagnostic.Log("SystemManager UnloadUnusedAsset end");
        }
    }
}
