using System.IO;
using TKPlugins;
using UnityEngine;

namespace TKFrame
{

    /// <summary>
    /// 应用程序类，存储应用程序相关信息及配置
    /// </summary>
    public class TKApplication : TKBehaviour
    {
        //安装包资源目录(只读)
		public static string AssetPath
        {
            get
            {
                return UnityEngine.Application.streamingAssetsPath;
            }
        }

        //内部存储目录(读写)，跟随程序应用删除而删除
        public static string DataPath
        {
            get
            {
                return UnityEngine.Application.persistentDataPath;//注意：在IOS上被iCloud自动备份
            }
        }

        //外部存储目录(读写)，不跟随程序应用删除而删除
        //private static string _storagePath;
        public static string StoragePath { get { return TKFrameConfig.Instance.AppStoragePath();}}
    }

}
