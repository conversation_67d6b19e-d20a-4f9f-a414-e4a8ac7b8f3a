using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using TKFrame;


[Serializable]
public class AssetBundleMapData: ISerializable
{
    public string BundleName;
    public string RealBundleName;
    public bool EtcVariant = false;
    public string EtcVariantBundleName;
    public bool HDVariant = false;
    public string HDVariantBundleName;
    
    
    
    
    [NonSerialized]
    public List<string> Groups;
    [NonSerialized]
    public List<string> AssetPaths = new List<string>();

    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        info.AddValue("BundleName", BundleName);
        info.AddValue("RealBundleName", RealBundleName);
        info.AddValue("EtcVariant", EtcVariant);
        info.AddValue("EtcVariantBundleName", EtcVariantBundleName);
        info.AddValue("HDVariant", HDVariant);
        info.AddValue("HDVariantBundleName", HDVariantBundleName);
    }

    public AssetBundleMapData()
    {
        
    }

    /// <summary>
    /// 自定义反序列化类
    /// </summary>
    /// <param name="info">Info.</param>
    /// <param name="context">Context.</param>
    public AssetBundleMapData(SerializationInfo info, StreamingContext context)
    {
        BundleName = info.GetString("BundleName");
        RealBundleName = info.GetString("RealBundleName");
        EtcVariant = info.GetBoolean("EtcVariant");
        EtcVariantBundleName = info.GetString("EtcVariantBundleName");
        HDVariant = info.GetBoolean("HDVariant");
        HDVariantBundleName = info.GetString("HDVariantBundleName");
    }
}

public class AssetBundleMap
{
    public TKDictionary<string, AssetBundleMapData> DataDict = new TKDictionary<string, AssetBundleMapData>();
    public List<AssetBundleMapData> DataList = new List<AssetBundleMapData>();
    Stream _stream;
    IFormatter _formatter;
    MemoryStream _memoryStream;
    //ApolloBufferWriter _Writer;
    //ApolloBufferReader _Reader;
    bool writer;
    private string _file;
    public AssetBundleMap(string file, bool write)
    {
        writer = write;
        _file = file;
        if (writer)
        {
            _stream = new FileStream(_file, FileMode.Create, FileAccess.Write, FileShare.Read);
            _memoryStream = new MemoryStream(2048);
            //_Writer = new ApolloBufferWriter(_memoryStream);
                
        }
        else
        {
            _stream = new FileStream(_file, FileMode.Open, FileAccess.Read, FileShare.Read);
        }
        
        _formatter = new BinaryFormatter();
        _formatter.Context = new StreamingContext(StreamingContextStates.All, _formatter);
    }

    public AssetBundleMap(byte[] bytes)
    {
        _stream = new MemoryStream(bytes);
        _formatter = new BinaryFormatter();
        _formatter.Context = new StreamingContext(StreamingContextStates.All, _formatter);
    }
    


    public void Read()
    {
        DataDict.Clear();
        int totalCount = 0;
        totalCount = Convert.ToInt32(_formatter.Deserialize(_stream));
        for (int i = 0; i < totalCount; i++)
        {
            AssetBundleMapData mapData = _formatter.Deserialize(_stream) as AssetBundleMapData;
            if (mapData != null)
            {
                DataDict.Add(mapData.BundleName, mapData);
                DataList.Add(mapData);
            }
        }
        _stream.Close();
    }

    public void Write()
    {
        int totalCount = DataList.Count;
        _formatter.Serialize(_stream, totalCount);
        for (int i = 0; i < totalCount; i++)
        {
            _formatter.Serialize(_stream, DataList[i]);
        }
        _stream.Flush();
        _memoryStream.Flush();
        _stream.Close();
        _memoryStream.Close();
    }
    
    
    
}
