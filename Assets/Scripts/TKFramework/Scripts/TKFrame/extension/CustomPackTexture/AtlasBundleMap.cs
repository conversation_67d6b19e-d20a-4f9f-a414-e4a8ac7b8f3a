using System;
using System.Collections.Generic;
using UnityEngine;


public class AssetMapData
{
    public string Path;
    public string BundleName;
    public bool HasSpriteTag;
}

/// <summary>
/// 未自动拆分重组前的原生的AssetBundle信息
/// </summary>
public class OriginAssetBundle
{
    public string AssetBundleName;

    public List<string> AllAssetList = new List<string>();


    public List<string> TextureAssetList = new List<string>();

    public bool OnlyHasTexture => AllAssetList.Count == TextureAssetList.Count;
    
    public List<string> DepTextureAssetList = new List<string>();
    
    
    
}
