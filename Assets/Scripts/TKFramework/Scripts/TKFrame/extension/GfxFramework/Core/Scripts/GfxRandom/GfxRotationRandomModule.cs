using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [Serializable]
    public class GfxRotationRandomModule : GfxRandomModuleBase
    {
        public GfxRandom x = new GfxRandom();
        public GfxRandom y = new GfxRandom();
        public GfxRandom z = new GfxRandom();

        protected Transform m_transform;
        protected Vector3 m_initEulerAngles;

        public override void Active(Transform transform)
        {
            base.Active(transform);

            m_transform = transform;
            m_initEulerAngles = transform.rotation.eulerAngles;
        }

        public override void Deactive()
        {
            base.Deactive();

            if (m_transform != null)
            {
                m_transform.rotation = Quaternion.Euler(m_initEulerAngles);
            }
        }

        public override void Random()
        {
            base.Random();

            if (x.enable || y.enable || z.enable)
            {
                if (m_transform != null)
                {
                    var position = m_initEulerAngles;
                    position.x += x.Random();
                    position.y += y.Random();
                    position.z += z.Random();
                    m_transform.rotation = Quaternion.Euler(position);
                }
            }
        }
    }
}
