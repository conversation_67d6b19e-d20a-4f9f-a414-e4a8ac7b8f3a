
namespace GfxFramework
{
    public enum GfxMaterialPropertyType
    {
        Color,
        Vetor4,
        Vetor3,
        Vetor2,
        Float,
    }

    public enum GfxAxis 
    {
        Forward,
        Back,
        Right,
        Left,
        Up,
        Down,
    }

    public enum GfxSortOrder
    {
        [EnumLabel("场景")]
        Scene = -2,
        //[EnumLabel("地面")]
        //Ground = -1,
        [EnumLabel("角色")]
        Default = 0,
        //[EnumLabel("前景")]
        //FrontGround = 1,
        [EnumLabel("UI")]
        UI = 2,
    }

    public enum GfxDynamicLodLevel
    {
        [EnumLabel("[不纳入动态LOD范围]")]
        NoReduceLod = 0,
        [EnumLabel("[到达阈值执行动态降低lod]")]
        ReduceLodWhenDynamic = 1,
        [EnumLabel("[固定时机降lod]")]
        ReduceLodWhenStart = 2,
    }

    public enum GfxLod
    {
        None    = 0,
        // SeaLow  = -1, // 海外极低版本， 因为海外版特效美术会自行制作替换，这里就不需要额外，共用low即可;
        Low     = 1,
        Middle  = 2,
        High    = 3,
        Ultra   = 4,
    }

    public enum GfxNodeType
    {
        Transform = 0,
        Renderer,
        ParticleSystem,
        Canvas,

    }

    public enum DynamicLodMode
    {
        // 关闭
        None,
        // 到达上限就降低lod
        Reduce,
        // 离开上限就恢复lod
        Increase,       
    }

    public interface IGfxSystem
    {
        void OnAwake(GfxLodSystem lodSystem);
        void Active(GfxRoot root);

        void OnExceLod(GfxLod lod);
        void Deactive();
        void Run(float time);

        void NotifyCameraChanged();
    }

    public static class GfxDef
    {
        public const int SortOrderOffset = 10000;
    }
}
