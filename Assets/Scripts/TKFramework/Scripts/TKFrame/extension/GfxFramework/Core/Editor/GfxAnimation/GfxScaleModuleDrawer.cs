using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;

namespace GfxFramework
{
    public class GfxScaleModuleDrawer : GfxModuleBaseDrawer
    {
        public GfxScaleModuleDrawer() : base("缩放动画")
        {

        }

        protected override void OnInspectorGUIImpl(GfxModuleBase module, Editor editor)
        {
            GfxScaleModule scaleModule = module as GfxScaleModule;
            if (scaleModule != null)
            {
                bool uniformScale = EditorGUILayout.Toggle("统一缩放", scaleModule.m_uniformScale);

                if (scaleModule.m_uniformScale != uniformScale)
                {
                    scaleModule.m_uniformScale = uniformScale;
                    if (scaleModule.m_uniformScale)
                    {
                        scaleModule.m_yCurve.m_enable = false;
                        scaleModule.m_zCurve.m_enable = false;
                    }

                    EditorUtility.SetDirty(editor.target);
                }

                if (scaleModule.m_uniformScale)
                {
                    GfxCurveDrawer.OnInspectorGUI("缩放", scaleModule.m_xCurve, editor);
                }
                else
                {
                    GfxCurveDrawer.OnInspectorGUI("X缩放", scaleModule.m_xCurve, editor);
                    GfxCurveDrawer.OnInspectorGUI("Y缩放", scaleModule.m_yCurve, editor);
                    GfxCurveDrawer.OnInspectorGUI("Z缩放", scaleModule.m_zCurve, editor);
                }
            }
        }
    }
}
