using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [Serializable]
    public class GfxMaterialColorCurve : GfxMaterialCurve
    {
        public Gradient m_colorCurve = new Gradient();

        public override void Evaluate(MaterialPropertyBlock propertyBlock, float phase, float totalTime)
        {
            Color c = m_colorCurve.Evaluate(phase);

            propertyBlock.SetColor(m_propertyId, c);
        }
    }
}
