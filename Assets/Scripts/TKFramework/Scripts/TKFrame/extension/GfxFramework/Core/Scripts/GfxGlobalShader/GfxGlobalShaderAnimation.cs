using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [DisallowMultipleComponent]
    public class GfxGlobalShaderAnimation : GfxBaseSystem
    {       
        public List<GfxGlobalShaderVar> m_globalVars = new List<GfxGlobalShaderVar>();

        protected override void Initialize()
        {
            base.Initialize();

            for (int i = 0; i < m_globalVars.Count; ++i)
            {
                m_globalVars[i].Initilize();
            }
        }
        public override void Active(GfxRoot root)
        {
            base.Active(root);
        }

        public override void Deactive()
        {
            base.Deactive();

            for (int i = 0; i < m_globalVars.Count; ++i)
            {
                m_globalVars[i].Deactive();
            }
        }

        protected override void RunTick(float phase, float lifeTime)
        {
            for (int i = 0; i < m_globalVars.Count; ++i)
            {
                m_globalVars[i].Evaluate(phase, lifeTime);
            }
        }

        public override void NotifyCameraChanged()
        {

        }
    }
}
