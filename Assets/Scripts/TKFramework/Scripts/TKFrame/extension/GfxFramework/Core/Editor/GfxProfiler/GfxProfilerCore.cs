using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    public class GfxProfilerCore
    {
        public List<GfxProfilerFrameData> frameDatas = new List<GfxProfilerFrameData>();

        public GfxProfilerStaticData staticData = new GfxProfilerStaticData();

        public void Init(GfxRoot gfxRoot)
        {
#if PARTICLESYSTEM_DATA
            gfxRoot.UnpackParticleData();
#endif
            Init(gfxRoot.transform);
        }

        public void Init(Transform root)
        {
            staticData.Analyze(root);
        }

        public void RecordFrameData()
        {

        }
    }
}
