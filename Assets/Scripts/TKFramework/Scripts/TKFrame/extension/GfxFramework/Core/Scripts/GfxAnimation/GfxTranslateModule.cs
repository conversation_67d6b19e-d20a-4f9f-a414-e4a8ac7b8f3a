using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [Serializable]
    public class GfxTranslateModule : GfxModuleBase
    {
        public bool m_worldSpace = true;

        public GfxCurve m_xCurve = new GfxCurve();
        public GfxCurve m_yCurve = new GfxCurve();
        public GfxCurve m_zCurve = new GfxCurve();

        private Vector3 m_initPos = Vector3.zero;
        public override void Initialize(GfxAnimation owner)
        {
            base.Initialize(owner);

            m_initPos = transform.localPosition;
        }

        public override void Active()
        {

        }

        public override void Deactive()
        {
            transform.localPosition = m_initPos;
        }

        protected override void TickImpl(float phase, float totalTime)
        {
            Vector3 translate = Vector3.zero;
            float val;

            if (m_xCurve.TryEvaluate(m_initPos.x, totalTime, phase, out val))
            {
                translate.x = val;
            }

            if (m_yCurve.TryEvaluate(m_initPos.y, totalTime, phase, out val))
            {
                translate.y = val;
            }

            if (m_zCurve.TryEvaluate(m_initPos.z, totalTime, phase, out val))
            {
                translate.z = val;
            }

            if (m_worldSpace)
            {
                transform.localPosition = m_initPos + translate;
            }
            else
            {
                transform.localPosition = m_initPos + transform.localRotation * translate;
            }
        }
    }
}
