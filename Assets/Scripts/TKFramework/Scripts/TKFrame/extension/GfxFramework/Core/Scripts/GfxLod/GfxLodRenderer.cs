using UnityEngine;

namespace GfxFramework
{
    public class GfxLodRenderer : GfxLodBase
    {
        public override GfxNodeType NodeType => GfxNodeType.Renderer;

        private Renderer m_renderer = null;

        private bool m_active = false;
        
        public GfxLodRenderer()
        {
        }
        
        public GfxLodRenderer(Renderer r)
        {
            m_renderer = r;
            m_active = r != null ? r.enabled : false;
        }

        public override bool IsActive()
        {
            //Renderer renderer = GetRenderer();
            //if (renderer != null)
            //{
            //    return renderer.enabled;
            //}

            return m_active;
        }

        public override void SetActive(bool active)
        {
            Renderer renderer = GetRenderer();
            if (renderer != null)
            {
                renderer.enabled = active;
            }
            m_active = active;
        }

        public virtual Renderer GetRenderer()
        {
            return m_renderer;
        }

        public override bool IsReady()
        {
            return GetRenderer() != null;
        }
    }
}
