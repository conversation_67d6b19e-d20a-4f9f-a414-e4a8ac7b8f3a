using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GfxFramework
{
    [Serializable]
    public class GfxTimeSystem
    {
        // 动画开始时间
        public float m_startTime = 0;
        // 动画结束时间
        public float m_endTime = 1;
        //动画循环
        public bool m_loop = false;
        // 时间缩放
        public float m_timeScale = 1.0f;

        protected int m_loopedCount = 0;
        // 当前播放百分比
        protected float m_phase = 0;
        // 播放时间
        protected float m_lifeTime = 0;
        // 播放时间倒数
        protected float m_invLifeTime = 0;

        public float Phase 
        { 
            get 
            { 
                return m_loopedCount + m_phase;
            }
        }

        public float LifeTime 
        { 
            get 
            { 
                if (m_lifeTime == 0)
                {
                    Initialize();
                }

                return m_lifeTime; 
            } 
        }

        public void Initialize()
        {
            m_lifeTime = m_endTime - m_startTime;
            if (m_lifeTime < 0.0001f)
            {
                m_lifeTime = 0.0001f;
            }

            m_invLifeTime = 1.0f / m_lifeTime;
        }

        public void SetEndTime(float endTime)
        {
            m_endTime = endTime;
            m_lifeTime = m_endTime - m_startTime;
            if (m_lifeTime < 0.0001f)
            {
                m_lifeTime = 0.0001f;
            }

            m_invLifeTime = 1.0f / m_lifeTime;
        }

        public void Active()
        {
            Clear();
        }

        public void Deactive()
        {
            Clear();
        }

        private void Clear()
        {
            m_phase = 0.0f;
            m_loopedCount = 0;
            m_lifeTime = 0;
        }

        public bool Run(float time)
        {
            bool needUpdate = (m_phase < 1.0f);

            m_phase = (time - m_startTime) * m_invLifeTime * m_timeScale - m_loopedCount;

            if (m_phase < 0.0f)
            {
                m_phase = 0.0f;
                return false;
            }
            else if (m_phase >= 1.0f)
            {
                if (!m_loop)
                {
                    m_phase = 1.0f;
                    return needUpdate;
                }
                else
                {
                    m_phase = 0.0f;
                    m_loopedCount += 1;
                }
            }

            return true;
        }
    }
}
