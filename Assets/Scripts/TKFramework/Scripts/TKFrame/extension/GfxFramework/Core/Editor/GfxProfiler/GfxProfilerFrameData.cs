using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GfxFramework
{
    public class GfxProfilerFrameData
    {
        public int batch;
        public int drawcall;
        public int particles;
        public int overdraw;
        public int pixelfill;
        public int triangles;

        public int ibUploadBytes;
        public int ibUploads;
        public int vboUploadBytes;
        public int vboUploads;

        public int usedTextureMemorySize;
        public int usedTextureCount;
        public int renderTextureBytes;
        public int renderTextureCount;
    }
}
