using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame;
using UnityEditor;
using UnityEngine;

namespace GfxFramework
{
    public class GfxDrawcallTool : EditorWindow
    {
        public class PSInfo
        {
            public ParticleSystemRenderer psr;
            public bool customFudge;
            public int sortingFudge;
        }

        public class OptimizeItem
        {
            public bool foldout = true;
            public Material mat;
            public List<PSInfo> psList = new List<PSInfo>();
            public int sortingFudge;
        }

        public GameObject gameObject;

        public List<OptimizeItem> optimizeList = new List<OptimizeItem>();

        private Vector2 scrollRect;

        public static void ShowWindow(GameObject go)
        {
            var w = GetWindow<GfxDrawcallTool>("Drawcall优化工具");
            w.Analyse(go);
            w.Show();
        }

        private void Analyse(GameObject go)
        {
            gameObject = go;

            optimizeList = AnalyseGameObject(go);
        }

        public static List<OptimizeItem> AnalyseGameObject(GameObject go)
        {
            var psrArr = go.GetComponentsInChildren<ParticleSystemRenderer>(true);

            TKDictionary<string, OptimizeItem> dict = new TKDictionary<string, OptimizeItem>();

            HashSet<Material> mats = new HashSet<Material>();
            for (int i = 0; i < psrArr.Length; ++i)
            {
                var psr = psrArr[i];

                mats.Clear();
                foreach (var mat in psr.sharedMaterials)
                {
                    if (mat != null && !mats.Contains(mat))
                        mats.Add(mat);
                }
                if (mats.Count != 1)        // 如果是0，就不需要优化；如果大于1，也没办法优化
                    continue;

                var omat = mats.ToList()[0];
                var matGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(omat));
                if (!dict.TryGetValue(matGuid, out OptimizeItem item))
                {
                    item = new OptimizeItem();
                    item.mat = omat;
                    dict.Add(matGuid, item);
                }

                PSInfo ps = new PSInfo();
                ps.psr = psr;
                ps.sortingFudge = (int)psr.sortingFudge;
                item.psList.Add(ps);
            }

            // 检查一下哪些psr的排序是一样的 取一样最多的作为这一组的sortingFudge，然后将其他数值的psr取消勾选
            TKDictionary<int, List<PSInfo>> fudgeDict = new TKDictionary<int, List<PSInfo>>();
            foreach (var item in dict)
            {
                fudgeDict.Clear();
                foreach (var ps in item.Value.psList)
                {
                    if (!fudgeDict.TryGetValue(ps.sortingFudge, out List<PSInfo> psList))
                    {
                        psList = new List<PSInfo>();
                        fudgeDict.Add(ps.sortingFudge, psList);
                    }
                    psList.Add(ps);
                }

                int maxCount = 0;
                int sortingFudge = 0;
                foreach (var fudgeItem in fudgeDict)
                {
                    if (fudgeItem.Value.Count > maxCount)
                    {
                        maxCount = fudgeItem.Value.Count;
                        sortingFudge = fudgeItem.Key;
                    }
                }

                foreach (var fudgeItem in fudgeDict)
                {
                    foreach (var psInfo in fudgeItem.Value)
                    {
                        psInfo.customFudge = fudgeItem.Key != sortingFudge;
                    }
                }

                item.Value.sortingFudge = sortingFudge;
            }

            List<OptimizeItem> list = new List<OptimizeItem>();
            foreach (var item in dict)
            {
                item.Value.foldout = item.Value.psList.Count > 1;
                list.Add(item.Value);
            }
            return list;
        }

        private void OnGUI()
        {
            EditorGUI.BeginChangeCheck();
            gameObject = EditorGUILayout.ObjectField("特效: ", gameObject, typeof(GameObject), true) as GameObject;
            if (EditorGUI.EndChangeCheck())
            {
                optimizeList = AnalyseGameObject(gameObject);
            }

            bool needOpt = false;
            scrollRect = EditorGUILayout.BeginScrollView(scrollRect);
            for (int i = 0; i < optimizeList.Count; ++i)
            {
                var item = optimizeList[i];
                //if (item.psList.Count > 1)
                {
                    if (item.psList.Count > 1)
                        GUI.backgroundColor = Color.green;
                    DrawOptimizeInfo(item);
                    if (item.psList.Count > 1)
                        GUI.backgroundColor = Color.white;
                    needOpt = true;
                }
            }
            if (!needOpt)
            {
                EditorGUILayout.LabelField("当前特效无需优化");
            }
            EditorGUILayout.EndScrollView();

            if (GUILayout.Button("自动优化（同屏少）"))
            {
                OptimizeDrawcallStaticLess(gameObject);
            }

            if (GUILayout.Button("自动优化(同屏多)"))
            {
                if (EditorUtility.DisplayDialog("提示", "这个特效在局内同时出现超过5个才需要点击这个（例如羁绊特效），如果特效同时出现数量少，请选择自动优化", "继续优化", "取消"))
                {
                    OptimizeDrawcallStatic(gameObject);
                }
                //DoAutoOptimize();
            }
        }

        /// <summary>
        /// 关注单个特效内部drawcall合批
        /// </summary>
        /// <param name="go"></param>
        public void OptimizeDrawcallStaticLess(GameObject go)
        {
            optimizeList.Sort(SortOrder2);
            var renderers = go.GetComponentsInChildren<Renderer>();
            int sortingOrder = renderers.Length / 2;
            for (int i = 0; i < optimizeList.Count; ++i)
            {
                var o = optimizeList[i];
                o.sortingFudge = --sortingOrder;
                foreach (var ps in o.psList)
                {
                    if (!ps.customFudge)
                    {
                        ps.sortingFudge = o.sortingFudge;
                        ps.psr.sortingFudge = o.sortingFudge;
                    }
                }
            }
        }

        private int SortOrder2(OptimizeItem l, OptimizeItem r)
        {
            if (l.psList[0].psr.sortingOrder != r.psList[0].psr.sortingOrder)
            {
                return l.psList[0].psr.sortingOrder - r.psList[0].psr.sortingOrder;
            }

            var rMat = r.mat;
            var lMat = l.mat;
            //if (rMat != null && rMat.HasProperty("_NormalFactor")
            //    && lMat != null && lMat.HasProperty("_NormalFactor"))
            //{
            //    var rDepth = rMat.GetFloat("_NormalFactor");
            //    var lDepth = lMat.GetFloat("_NormalFactor");
            //    if (rDepth != lDepth)
            //        return (int)((lDepth - rDepth) * 10000);
            //}

            int l_renderQ = l.mat != null ? l.mat.renderQueue : 0;
            int r_renderQ = r.mat != null ? r.mat.renderQueue : 0;
            if (l_renderQ != r_renderQ)
            {
                return l_renderQ - r_renderQ;
            }

            int l_sortFudge = l.sortingFudge;
            int r_sortFudge = r.sortingFudge;

            var mainCamera = Camera.main;
            if (mainCamera != null)
            {
                var l_distance = (mainCamera.transform.position - l.psList[0].psr.transform.position).magnitude - l_sortFudge;
                var r_distance = (mainCamera.transform.position - r.psList[0].psr.transform.position).magnitude - r_sortFudge;
                if (l_distance != r_distance)
                {
                    return l_distance - r_distance > 0 ? 1 : -1;
                }
            }

            if (r_sortFudge != l_sortFudge)
            {
                return r_sortFudge - l_sortFudge;
            }
            else
            {
                var l_shaderId = lMat != null && lMat.shader != null ? lMat.shader.GetInstanceID() : 0;
                var r_shaderId = rMat != null && rMat.shader != null ? rMat.shader.GetInstanceID() : 0;

                if (l_shaderId != r_shaderId)
                    return l_shaderId - r_shaderId;

                return (int)(/*GfxManager.*/GetSiblingIndex(r.psList[0].psr.transform) - /*GfxManager.*/GetSiblingIndex(l.psList[0].psr.transform));
            }
        }

        /// <summary>
        /// 关注多个相同特效合批
        /// </summary>
        /// <param name="go"></param>
        public void OptimizeDrawcallStatic(GameObject go)
        {
            var renderers = new List<Renderer>(go.GetComponentsInChildren<Renderer>());
            renderers.Sort(/*GfxManager.*/ParticleSystemSortOrder);
            string matGuid = string.Empty;
            int sortingOrder = -renderers.Count / 2;
            StringBuilder sb = new StringBuilder();
            var mainCamera = Camera.main;
            sb.AppendLine(mainCamera.name);
            sb.AppendLine("name\tsortingLayer\tsortingOrder\trenderQ\tsortFudge\tdepth\tdistance\tnewSortOrder");
            for (int i = 0; i < renderers.Count; ++i)
            {
                var r = renderers[i];
                string curMatGuid = string.Empty;
                bool forceBreak = false;
                foreach (var mat in r.sharedMaterials)
                {
                    if (mat == null)
                        continue;
                    if (string.IsNullOrEmpty(curMatGuid))
                        curMatGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(mat));
                    else if (curMatGuid != AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(mat)))
                        forceBreak = true;
                }
                if (matGuid != curMatGuid || forceBreak)
                {
                    matGuid = curMatGuid;
                    ++sortingOrder;
                }
                int sortFudge = (int)(r is ParticleSystemRenderer ? (r as ParticleSystemRenderer).sortingFudge : 0);
                var distance = (mainCamera.transform.position - r.transform.position).magnitude + sortFudge;
                var rMat = r.sharedMaterial;
                float depth = 0;
                if (rMat != null && rMat.HasProperty("_NormalFactor"))
                {
                    depth = rMat.GetFloat("_NormalFactor");
                    distance -= (depth * distance);
                }
                sb.AppendLine($"{r.name}\t\t{r.sortingLayerID}\t\t{r.sortingOrder}\t\t{r.sharedMaterial.renderQueue}\t\t{sortFudge}\t\t{depth}\t\t{distance}\t\t{sortingOrder}");

                renderers[i].sortingOrder = sortingOrder;
            }
            Debug.Log(sb.ToString());
        }

        /// <summary>
        /// For the particle system shared the identical hash and shared material, sort them by distance to camera only because other sorting factors are the same.
        /// </summary>
        /// <param name="l"></param>
        /// <param name="r"></param>
        /// <returns></returns>
        private static int ParticleSystemSimpleSortOrderUsingDistance(Renderer l, Renderer r)
        {
            int l_sortFudge = (int)(l is ParticleSystemRenderer ? (l as ParticleSystemRenderer).sortingFudge : 0);
            int r_sortFudge = (int)(r is ParticleSystemRenderer ? (r as ParticleSystemRenderer).sortingFudge : 0);

            var mainCamera = Camera.main;
            if (mainCamera != null)
            {
                var l_distance = (mainCamera.transform.position - l.transform.position).magnitude + l_sortFudge;
                var r_distance = (mainCamera.transform.position - r.transform.position).magnitude + r_sortFudge;

                var rMat = r.sharedMaterial;
                var lMat = l.sharedMaterial;
                if (rMat != null && rMat.HasProperty("_NormalFactor")
                    && lMat != null && lMat.HasProperty("_NormalFactor"))
                {
                    var rDepth = rMat.GetFloat("_NormalFactor");
                    var lDepth = lMat.GetFloat("_NormalFactor");
                    l_distance -= lDepth * l_distance;
                    r_distance -= rDepth * r_distance;
                }

                if (l_distance != r_distance)
                {
                    return (int)((r_distance - l_distance) * 10000);
                }
            }
            if (r_sortFudge != l_sortFudge)
            {
                return r_sortFudge - l_sortFudge;
            }
            else
            {
                var l_shaderId = l.sharedMaterial != null && l.sharedMaterial.shader != null ? l.sharedMaterial.shader.GetInstanceID() : 0;
                var r_shaderId = r.sharedMaterial != null && r.sharedMaterial.shader != null ? r.sharedMaterial.shader.GetInstanceID() : 0;

                if (l_shaderId != r_shaderId)
                    return l_shaderId - r_shaderId;

                return (int)(GetSiblingIndex(r.transform) - GetSiblingIndex(l.transform));
            }
        }

        /// <summary>
        /// 4层特效一般够用了
        /// </summary>
        /// <param name="transform"></param>
        /// <returns></returns>
        private static long GetSiblingIndex(Transform transform)
        {
            int siblingIndex = transform.GetSiblingIndex();

            if (transform.parent != null)
            {
                siblingIndex += (transform.parent.GetSiblingIndex() * 100);

                if (transform.parent.parent != null)
                {
                    siblingIndex += (transform.parent.parent.GetSiblingIndex() * 10000);

                    if (transform.parent.parent.parent != null)
                    {
                        siblingIndex += (transform.parent.parent.parent.GetSiblingIndex() * 1000000);
                    }
                }
            }

            return siblingIndex;
        }

        private static int ParticleSystemSortOrder(Renderer l, Renderer r)
        {
            if (l.sortingLayerID != r.sortingLayerID)
            {
                return l.sortingLayerID - r.sortingLayerID;
            }

            if (l.sortingOrder != r.sortingOrder)
            {
                return l.sortingOrder - r.sortingOrder;
            }

            int l_renderQ = l.sharedMaterial != null ? l.sharedMaterial.renderQueue : 0;
            int r_renderQ = r.sharedMaterial != null ? r.sharedMaterial.renderQueue : 0;
            if (l_renderQ != r_renderQ)
            {
                return l_renderQ - r_renderQ;
            }

            return ParticleSystemSimpleSortOrderUsingDistance(l, r);
        }

        private void DrawOptimizeInfo(OptimizeItem item)
        {
            item.foldout = GfxEditorUtility.GroupHeader(item.mat.name, true, item.foldout, Color.white, (_) =>
            {
                EditorGUI.BeginChangeCheck();
                item.sortingFudge = EditorGUILayout.IntField("sortingFudge: ", item.sortingFudge, GUILayout.Width(200));
                if (EditorGUI.EndChangeCheck())
                {
                    foreach (var ps in item.psList)
                    {
                        if (!ps.customFudge)
                        {
                            ps.sortingFudge = item.sortingFudge;
                            ps.psr.sortingFudge = item.sortingFudge;
                        }
                    }
                }
            });
            if (item.foldout)
            {
                GfxEditorUtility.BeginGroup(3);
                foreach (var ps in item.psList)
                {
                    GUILayout.BeginHorizontal();

                    ps.customFudge = EditorGUILayout.Toggle(ps.customFudge, GUILayout.Width(25));
                    EditorGUILayout.ObjectField(ps.psr, typeof(ParticleSystemRenderer), false);

                    GUI.enabled = ps.customFudge;
                    EditorGUI.BeginChangeCheck();
                    ps.sortingFudge = EditorGUILayout.IntField("sortingFudge: ", ps.sortingFudge, GUILayout.Width(200));
                    if (EditorGUI.EndChangeCheck())
                    {
                        if (ps.customFudge)
                        {
                            ps.psr.sortingFudge = item.sortingFudge;
                        }
                    }
                    GUI.enabled = true;

                    GUILayout.EndHorizontal();
                }

                GfxEditorUtility.EndGroup();
            }
        }
    }
}
