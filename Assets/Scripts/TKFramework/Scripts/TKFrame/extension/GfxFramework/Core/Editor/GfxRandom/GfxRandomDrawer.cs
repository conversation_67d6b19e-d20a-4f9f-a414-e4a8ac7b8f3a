using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace GfxFramework
{
    public class GfxRandomDrawer
    {
        public static void OnInspectorGUI(string title, GfxRandom random, Editor editor)
        {
            EditorGUILayout.BeginHorizontal();
            bool enable = GUILayout.Toggle(random.enable, title);

            //bool enable = EditorGUILayout.Toggle(title, random.enable, /*"label",*/ GUILayout.Width(30));
            if (random.enable != enable)
            {
                random.enable = enable;
                EditorUtility.SetDirty(editor.target);
            }

            if (random.enable)
            {
                //random.randomType = (GfxRandom.RandomType)EditorGUILayout.EnumPopup(random.randomType);
                if (random.randomType == GfxRandom.RandomType.Constant)
                {
                    var rect = EditorGUILayout.GetControlRect(false, 20f);

                    EditorGUI.LabelField(new Rect(rect.x, rect.y, 45, rect.height), "固定值:");
                    random.maxValue = EditorGUI.FloatField(new Rect(rect.x + rect.width - 60, rect.y, 60, rect.height), random.maxValue);
                    random.minValue = random.maxValue;
                }
                else if (random.randomType == GfxRandom.RandomType.Vague)
                {
                    var rect = EditorGUILayout.GetControlRect(false, 20f);

                    EditorGUI.LabelField(new Rect(rect.x, rect.y, 60, rect.height), "模糊范围:");
                    random.maxValue = EditorGUI.FloatField(new Rect(rect.x + rect.width - 60, rect.y, 60, rect.height), random.maxValue);
                    random.minValue = -random.maxValue;
                }
                else if (random.randomType == GfxRandom.RandomType.BetweenTwoValue)
                {
                    var rect = EditorGUILayout.GetControlRect(false, 20f);

                    EditorGUI.LabelField(new Rect(rect.x, rect.y, 45, rect.height), "最小值:");
                    random.minValue = EditorGUI.FloatField(new Rect(rect.x + 45, rect.y, 36, rect.height), random.minValue);
                    EditorGUI.LabelField(new Rect(rect.x + rect.width - 81, rect.y, 45, rect.height), "最大值:");
                    random.maxValue = EditorGUI.FloatField(new Rect(rect.x + rect.width - 36, rect.y, 36, rect.height), random.maxValue);
                    if (random.minValue > random.maxValue)
                    {
                        random.minValue = random.maxValue;
                    }
                }
                else if (random.randomType == GfxRandom.RandomType.SelectValue)
                {
                    var rect = EditorGUILayout.GetControlRect(false, 20f);

                    EditorGUI.LabelField(new Rect(rect.x, rect.y, 45, rect.height), "值1:");
                    random.minValue = EditorGUI.FloatField(new Rect(rect.x + 45, rect.y, 36, rect.height), random.minValue);
                    EditorGUI.LabelField(new Rect(rect.x + rect.width - 81, rect.y, 45, rect.height), "值2:");
                    random.maxValue = EditorGUI.FloatField(new Rect(rect.x + rect.width - 36, rect.y, 36, rect.height), random.maxValue);
                }

                GUIMMCurveStateList(random.randomType, (randomType) =>
                {
                    random.randomType = randomType;
                });
            }

            EditorGUILayout.EndHorizontal();
        }

        private static GUIStyle m_MinMaxCurveStateDropDown = "ShurikenDropdown";

        public static void GUIMMCurveStateList(GfxRandom.RandomType randomType, Action<GfxRandom.RandomType> onRandomTypeChanged)
        {
            if (EditorGUILayout.DropdownButton(GUIContent.none, FocusType.Passive, m_MinMaxCurveStateDropDown))
            {
                GUIContent[] texts = {   EditorGUIUtility.TrTextContent("固定值"),
                                         EditorGUIUtility.TrTextContent("模糊值"),
                                         EditorGUIUtility.TrTextContent("两个值之间随机"),
                                         EditorGUIUtility.TrTextContent("二选一") 
                                     };
                GfxRandom.RandomType[] states = {   GfxRandom.RandomType.Constant,
                                                    GfxRandom.RandomType.Vague,
                                                    GfxRandom.RandomType.BetweenTwoValue,
                                                    GfxRandom.RandomType.SelectValue 
                                                };

                GenericMenu menu = new GenericMenu();

                for (int i = 0; i < texts.Length; ++i)
                {
                    menu.AddItem(texts[i], randomType == states[i], (obj) => { onRandomTypeChanged((GfxRandom.RandomType)obj); }, states[i]);
                }

                var current = Event.current;

                menu.DropDown(new Rect(current.mousePosition.x, current.mousePosition.y, 0, 0));
                current.Use();
            }
        }
    }
}
