using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GfxFramework
{
    [Serializable]
    public class GfxScaleModule : GfxModuleBase
    {
        //统一缩放
        public bool m_uniformScale = true;             

        public GfxCurve m_xCurve = new GfxCurve();
        public GfxCurve m_yCurve = new GfxCurve();
        public GfxCurve m_zCurve = new GfxCurve();

        private Vector3 m_initScale;

        public override void Initialize(GfxAnimation owner)
        {
            base.Initialize(owner);

            m_initScale = transform.localScale;
        }

        public override void Active()
        {

        }

        public override void Deactive()
        {
            transform.localScale = m_initScale;
        }

        protected override void TickImpl(float phase, float totalTime)
        {
            if (m_uniformScale)
            {
                if (m_xCurve.TryEvaluate(1, totalTime, phase, out float val))
                {
                    transform.localScale = val * m_initScale;
                }
            }
            else
            {
                Vector3 scale = Vector3.one;
                float val;

                if (m_xCurve.TryEvaluate(1, totalTime, phase, out val))
                {
                    scale.x = val;
                }

                if (m_yCurve.TryEvaluate(1, totalTime, phase, out val))
                {
                    scale.y = val;
                }

                if (m_zCurve.TryEvaluate(1, totalTime, phase, out val))
                {
                    scale.z = val;
                }

                scale.x *= m_initScale.x;
                scale.y *= m_initScale.y;
                scale.z *= m_initScale.z;

                transform.localScale = scale;
            }
        }
    }
}
