using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using static EffectProfiler.ProfilerTableTreeView;

namespace EffectProfiler
{
    public class ProfilerUtil
    {
        public static void DrawTreeSeparator()
        {
            GUILayout.Space(1);

            if (Event.current.type == EventType.Repaint)
            {
                Texture2D tex = EditorGUIUtility.whiteTexture;
                Rect rect = GUILayoutUtility.GetLastRect();
                GUI.color = new Color(0f, 0f, 0f, 0.5f);
                GUI.DrawTexture(new Rect(rect.x, rect.yMin, 1, 17), tex);
                GUI.color = Color.white;
            }
        }
    }
}
