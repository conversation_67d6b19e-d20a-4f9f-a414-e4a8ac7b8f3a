using System;
using System.Collections;
using System.Collections.Generic;
using TKFrame;
using UnityEngine;

namespace GfxFramework
{
    public class GfxLodManager : GfxManager
    {
        private const int MAX_RESTORE_COUNT = 5;

        public GfxLodGroup StaticGroup { get; } = new GfxLodGroup(GfxLod.Low);
        public GfxLodGroup StaticReduceLodGroup { get; } = new GfxLodGroup(GfxLod.Low);
        public GfxLodGroup StaticLowLodGroup { get; } = new GfxLodGroup(GfxLod.Low);
        public GfxLodGroup DynamicReduceLodGroup { get; } = new GfxLodGroup(GfxLod.Low);
        public GfxLodGroup DynamicLowLodGroup { get; } = new GfxLodGroup(GfxLod.Low);

        private TKDictionary<string, GfxLodStrategy> m_strategyDict = new TKDictionary<string, GfxLodStrategy>();
        public TKDictionary<string, GfxLodStrategy> StrategyDict
        {
            get
            {
                return m_strategyDict; 
            }
        }

        // 仅供性能测试 运行时请勿通过这个接口开关，会导致报错
        public bool DynamicLodEnable
        {
            get; set;
        } = true;

        private bool m_lodDirty = false;
        private DynamicLodMode m_mode = DynamicLodMode.None;

        public override GfxLod Lod 
        { 
            get
            {
                return m_lod;
            }
            set
            {
                m_lod = value;
                if (m_lod == GfxLod.None)
                {
                    m_lod = GfxLod.Ultra;
                }

                StaticGroup.ChangeLod(m_lod);
                StaticReduceLodGroup.ChangeLod(m_lod);
                DynamicReduceLodGroup.ChangeLod(m_lod);

                foreach (var strategy in m_strategyDict)
                {
                    strategy.Value.ProcessTransformLod(m_lod);
                }
            }
        }

        public DynamicLodMode DLMode
        {
            get
            {
                return m_mode;
            }
        }

        public void SetLodDirty()
        {
            m_lodDirty = true;
        }

        public override void OnActive(GfxRoot gfx)
        {
            base.OnActive(gfx);

            if (!DynamicLodEnable)
            {
                return;
            }

            GfxLodStrategy strategy;
            string key = gfx.EffectName;

            if (!m_strategyDict.TryGetValue(key, out strategy))
            {
                strategy = new GfxLodStrategy(this.Lod, gfx.m_dynamicLevel);

                strategy.Name = key;
                m_strategyDict.Add(key, strategy);
            }

            if (!strategy.HasGroup())
            {
                if (gfx.m_dynamicLevel == GfxDynamicLodLevel.NoReduceLod)
                {
                    strategy.SetGroup(StaticGroup);
                }
                else if (gfx.m_dynamicLevel == GfxDynamicLodLevel.ReduceLodWhenDynamic)
                {
                    strategy.SetGroup(DynamicReduceLodGroup);
                }
                else if (gfx.m_dynamicLevel == GfxDynamicLodLevel.ReduceLodWhenStart)
                {
                    strategy.SetGroup(StaticReduceLodGroup);
                }
            }

            strategy.Active(gfx);

            if (Lod != GfxLod.Low && DLMode == DynamicLodMode.Reduce && strategy.Group == DynamicReduceLodGroup)
            {
                SetLodDirty();
            }
        }

        public override void OnDeactive(GfxRoot gfx)
        {
            base.OnDeactive(gfx);

            if (!DynamicLodEnable)
            {
                return;
            }

            GfxLodStrategy strategy;
            string key = gfx.EffectName;

            if (m_strategyDict.TryGetValue(key, out strategy))
            {
                strategy.Deactive(gfx);
            }
            else
            {
                throw new ArgumentNullException("[GfxLodManager.OnDeactive]can not find key " + key);
            }

            if (DLMode == DynamicLodMode.Increase && strategy.Group == DynamicReduceLodGroup && DynamicLowLodGroup.Count > 0)
            {
                SetLodDirty();
            }
        }

        protected override void OnPreUpdate()
        {
            base.OnPreUpdate();

            if (!DynamicLodEnable)
            {
                return;
            }

            if (m_lodDirty)
            {
                m_lodDirty = false;

                switch (DLMode)
                {
                    case DynamicLodMode.None:
                        RestoreAllLodSync();
                        break;
                    case DynamicLodMode.Reduce:
                        DynamicAllEffectLod();
                        break;
                    case DynamicLodMode.Increase:
                        RestoreAllLodAsync();
                        break;
                    default:
                        break;
                }
            }
        }

        #region Dynamic Lod

        public void SetDyniamicLodMode(DynamicLodMode dynamicLodMode)
        {
            if (!DynamicLodEnable)
            {
                return;
            }

            if (m_mode != dynamicLodMode)
            {
                m_mode = dynamicLodMode;

                DynamicReduceLodGroup.ClearUnuseStrategy();
                DynamicLowLodGroup.ClearUnuseStrategy();

                SetLodDirty();
            }
        }

        private void RestoreAllLodSync()
        {
            DynamicLowLodGroup.AllMoveTo(DynamicReduceLodGroup);
        }

        /// <summary>
        /// 恢复要逐步恢复 每次恢复五个 避免单帧过大
        /// </summary>
        private void RestoreAllLodAsync()
        {
            int lowLogGroupCount = DynamicLowLodGroup.Count;

            if (lowLogGroupCount > 0)
            {
                int restoreCount = lowLogGroupCount;

                if (restoreCount > MAX_RESTORE_COUNT)
                {
                    restoreCount = MAX_RESTORE_COUNT;
                    SetLodDirty();
                }

                DynamicLowLodGroup.MoveToGroup(DynamicReduceLodGroup, restoreCount);
            }
        }

        private void DynamicAllEffectLod()
        {
            if (Lod != GfxLod.Low)
            {
                // 到达了这个阈值就降低lod
                int maxLowLodCount = GetEffectLowLodCount(Lod);        
                int normalEffectCount = GfxCount - DynamicLowLodGroup.Count;

                if (normalEffectCount > maxLowLodCount)
                {
                    DynamicReduceLodGroup.MoveToGroup(DynamicLowLodGroup, normalEffectCount - maxLowLodCount);
                }
            }
        }

        /// <summary>
        /// 降低lod的阈值
        /// </summary>
        /// <param name="lod"></param>
        /// <returns></returns>
        private int GetEffectLowLodCount(GfxLod lod)
        {
            if (GameLOD.Instance.DevicePower > TKFrame.EDevicePower.EDP_Ultra)
                return int.MaxValue;

            int maxEffectCount = 150;

            switch (lod)
            {
                case GfxLod.None:
                    break;
                case GfxLod.Low:
                    // 低端机不需要降低阈值 因为他本来就是最低的了
                    maxEffectCount = 0; 
                    break;
                case GfxLod.Middle:
                    maxEffectCount = 60;
                    break;
                case GfxLod.High:
                    maxEffectCount = 100;
                    break;
                case GfxLod.Ultra:
                    maxEffectCount = 150;
                    break;
                default:
                    if (lod > GfxLod.Ultra)
                        maxEffectCount = int.MaxValue;
                    break;
            }

            return maxEffectCount;
        }

        #endregion

        #region Static Lod

        public void StaticReduceLod()
        {
            if (!DynamicLodEnable)
            {
                return;
            }

            StaticReduceLodGroup.AllMoveTo(StaticLowLodGroup);
        }

        public void RestoreStaticLod()
        {
            if (!DynamicLodEnable)
            {
                return;
            }

            StaticLowLodGroup.AllMoveTo(StaticReduceLodGroup);
        }

        #endregion
    }
}