using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;

namespace TKAction
{
    public class CCActionSpriteAlpha : CCActionInterval
    {
        float mFrom;
        float mTo;
        SpriteRenderer mRender;

        public CCActionSpriteAlpha(float duration, float from, float to, SpriteRenderer render)
        {
            InitWithDuration(duration);
            mFrom = from;
            mTo = to;
            mRender = render;
        }

        protected override bool InitWithDuration(float duration)
        {
            if (base.InitWithDuration(duration))
            {
                return true;
            }
            return false;
        }

        public override void Update(float time)
        {
            if (m_pTarget != null)
            {
                if (mRender != null)
                {
                    Color c = mRender.color;
                    c.a = Mathf.Lerp(mFrom, mTo, time);
                    mRender.color = c;
                }
            }
        }
    }
}
