#if TKF_ALL_EXTEND || TKFE_BS_DIFF//TKFrame Auto Gen
using System;
using System.IO;

namespace TKPlugins.BsDiff
{
    public class BsPatch
    {
        public static void Patch(string[] args)
        {
            // check for correct usage
            if (args ==null || args.Length < 3)
            {
                Console.Error.WriteLine("Patch args is NULL");
                return;
            }

            string oldFile = args[0];
            string newFile = args[1];
            string patchFile = args[2];
            //Console.Out.WriteLine("oldFile:"+ oldFile+ ", newFile:"+ newFile+ ", patchFile:"+ patchFile);

            try
            {
                using (FileStream input = new FileStream(oldFile, FileMode.Open, FileAccess.Read, FileShare.Read))
                using (FileStream output = new FileStream(newFile, FileMode.Create))
                    BinaryPatchUtility.Apply(input, () => new FileStream(patchFile, FileMode.Open, FileAccess.Read, FileShare.ReadWrite), output);
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError(e.Message);
            }
        }
    }
}
#endif //TKFrame Auto Gen
