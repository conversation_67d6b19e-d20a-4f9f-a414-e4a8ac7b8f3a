using System;
using System.Collections.Generic;
using TKPlugins;

namespace TKFrame
{
    /// <summary>
    /// 注册型对象，此类Object会添加到ObjectRegister对应的条目里面去。
    /// </summary>
    public class TKRegistObject : TKObject
    {

        /// <summary>
        /// 创建时把对象注册
        /// </summary>
        public TKRegistObject()
        {
            if (base.GetType().HasAttribute<ObjectRegistAttribute>())
            {
                if (ObjectRegistor.CmdObjsResistor != null)
                {
                    ObjectRegistor.CmdObjsResistor.AddInstance(this);
                }
            }
        }

    }
}
