using AOT;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using TKFrame;
using UnityEngine;
// im not sure if it is launched by unity 2017.3 but,,, no one will use versions older than 2017.3 to start this game, isn't it?
#if UNITY_2017_3_OR_NEWER
using UnityEngine.Profiling;
#endif

public class TKPlatform
{
#if UNITY_ANDROID
        static AndroidJavaClass ms_java_class;
        static AndroidJavaClass java_class
        {
            get
            {
                if (ms_java_class == null)
                {
                    //AndroidJavaClass只允许在主线程创建，应用层需要确保首次调用在主线程
                    ms_java_class = new AndroidJavaClass("com/tencent/tkframe/utils/TKFrameUtil");
                }
                return ms_java_class;
            }
        }

        static AndroidJavaObject ms_java_obj;
        static AndroidJavaObject java_obj
        {
            get
            {
                if (ms_java_obj == null)
                {
                    //AndroidJavaClass只允许在主线程创建，应用层需要确保首次调用在主线程
                    ms_java_obj = new AndroidJavaObject("com/tencent/tkframe/utils/TKFrameUtil");
                }
                return ms_java_obj;
            }
        }
#endif

#if UNITY_IOS
    #region DllImport
    [DllImport("__Internal")]
    private static extern void scheduleNotification(int notificationId, string title, string content, string strParams,long timeInMilliseconds);

    [DllImport("__Internal")]
    private static extern void unscheduleNotification(int notificationId);

    [DllImport("__Internal")]
    private static extern void initNativePush();

    [DllImport("__Internal")]
    private static extern string getApnInfo();

    [DllImport("__Internal")]
    private static extern void tkdll_share_file(string filePath);

    [DllImport("__Internal")]
    private static extern void _NativeShare_Share(string[] files, int filesCount, string subject, string text );

    [DllImport("__Internal")]
    private static extern float getBatteryLevel();

    [DllImport("__Internal")]
    private static extern int getBatteryState();

    [DllImport("__Internal")]
    private static extern float getSystemVersion();

    [DllImport("__Internal")]
    private static extern void openUrl(string url);

    [DllImport("__Internal")]
    private static extern int openInAppReview();

    [DllImport("__Internal")]
    private static extern void storeKeyChain(string service, string storeKey, string storeValue, string storeKeyText, string storeValueText);

    [DllImport("__Internal")]
    private static extern string loadKeyChain(string service, string storeKey);

    [DllImport("__Internal")]
    private static extern string deleteKeyChain(string service);

    [DllImport("__Internal")]
    private static extern void registerScreenCapturerCallback();

    [DllImport("__Internal")]
    private static extern void registerEarPhoneChangedCallback();

    [DllImport("__Internal")]
    private static extern void GetScreenshotInPNGFormat(ref IntPtr ptr, ref int size);

    [DllImport("__Internal")]
    private static extern void OnApplicationPauseTest();

    [DllImport("__Internal")]
    private static extern void PlaySystemSoundWithCompletion(int soundId);

    [DllImport ("__Internal")]	
    private static extern void __CopyTextToClipboard(string text);
    [DllImport ("__Internal")]
    private static extern string __GetClipboardText();
    [DllImport("__Internal")]
    private static extern long _GetFreeDiskSpace();
    [DllImport("__Internal")]
    private static extern int ZMShareAPP(byte[] bytes, int length,string title, string desc);
    [DllImport("__Internal")]
    private static extern int XHSShareAPP(byte[] bytes, int length,string title, string desc);
    [DllImport("__Internal")]
    private static extern int GetHeadsetStatus();
    [DllImport("__Internal")]
    private static extern void _OpenSysPermissionPanel();
    #endregion
#endif

#if UNITY_EDITOR
    private static string s_copyText;
#endif

    public enum BatteryState
    {
        Unknown = -1,
        Normal = 0,
        Recharging,
        Full,
    }

    public enum SystemSoundID
    {
        vibrate_slight1 = 1519,     //轻微震动
        vibrate_slight2 = 1520,     //轻微震动
        vibrate_slight3 = 1521,     //轻微震动
        vibrate = 4095,             //通用震动，500ms左右
    }

    /// <summary>
    ///-1: 未知
    ///0 : 未充电
    ///1 : 充电中
    ///2 : 电量已充满
    /// </summary>
    public static BatteryState GetBatteryState()
    {
        int state = 0;
#if UNITY_EDITOR
        
#elif UNITY_ANDROID

        JNIAttachThread();
        state = java_class.CallStatic<int>("GetBatteryState");
        JNIDetachThread();
        
#elif UNITY_IOS

        state = getBatteryState();
        
#endif

        return (BatteryState)(state);
    }

    /// <summary>
    ///返回剩余电量(0-100)
    /// <summary>
    public static int GetBatteryLevel()
    {
        var levelFloat = SystemInfo.batteryLevel * 100; 
        int level = Mathf.RoundToInt(levelFloat);
        
        return level;
    }
    
    /// <summary>
    /// 电池状态
    /// <summary>
    public static BatteryStatus GetBatteryChargeState()
    {
        return SystemInfo.batteryStatus;
    }

    /// <summary>
    /// 调用系统消息推送接口
    /// @param1 iNotificationId 通知ID，用于标识单条通知的唯一性
    /// @param2 pushTitle 通知的标题
    /// @param3 strPushMessage 通知的内容主体
    /// @param4 strParams 通知相关的参数信息，可以为空
    /// @param5 pushTimeInMillSeconds 通知倒计时时间，是当前需要通知的时间的格林尼治时间的毫秒数
    /// </summary>
    public static void ScheduleNativeNotification(int iNotificationId, string pushTitle, string strPushMessage, string strParams, long pushTimeInMillSeconds)
    {
#if UNITY_EDITOR
        
#elif  UNITY_ANDROID

        JNIAttachThread();
        java_class.CallStatic("ScheduleNotification", iNotificationId, pushTitle, strPushMessage, strParams, pushTimeInMillSeconds);
        JNIDetachThread();
        
#elif UNITY_IPHONE || UNITY_IOS

        scheduleNotification(iNotificationId, pushTitle, strPushMessage, strParams, pushTimeInMillSeconds);
        
#endif
    }

    /// <summary>
    /// 取消系统消息推送接口
    /// @param1 iNotificationId 通知ID，用于标识单条通知的唯一性
    /// </summary>
    public static void UnscheduleNativeNotification(int iNotificationId)
    {
#if UNITY_EDITOR
        
#elif UNITY_ANDROID

        JNIAttachThread();
        java_class.CallStatic("UnscheduleNotification", iNotificationId);
        JNIDetachThread();
        
#elif UNITY_IPHONE || UNITY_IOS

        unscheduleNotification(iNotificationId);
        
#endif
    }

    /// <summary>
    /// 更新当前进程的内存情况
    /// </summary>
    public static void UpdateMemInfo()
    {

#if UNITY_EDITOR
        
#elif UNITY_ANDROID

        JNIAttachThread();
        java_class.CallStatic("UpdateMemInfo");
        JNIDetachThread();
        
#endif
    }

    /// <summary>
    ///返回当前进程: 总的内存占用
    /// </summary>
    public static int GetTotalPssMemory()
    {
        int ret = 0;

#if UNITY_EDITOR
        
#if UNITY_2017_3_OR_NEWER
        ret = (int)Profiler.GetTotalAllocatedMemoryLong() / 1024;
#else
        ret = (int)Profiler.GetTotalAllocatedMemory() / 1024;
#endif
        
#elif  UNITY_ANDROID

        JNIAttachThread();
        ret = java_class.CallStatic<int>("GetTotalPssMemory"); 
        JNIDetachThread();
        
#endif

        return ret;
    }

    /// <summary>
    ///返回当前进程-其它内存占用(mono C#)
    ////// <summary>
    public static int GetOtherPssMemory()
    {
        int ret = 0;
#if UNITY_EDITOR
        
#if UNITY_2017_3_OR_NEWER
        ret = (int)Profiler.GetMonoHeapSizeLong() / 1024;
#else
        ret = (int)Profiler.GetMonoHeapSize() / 1024;
#endif
        
#elif UNITY_ANDROID

        JNIAttachThread();
        ret = java_class.CallStatic<int>("GetOtherPssMemory");
        JNIDetachThread();

#endif

        return ret;
    }

    /// <summary>
    /// 返回当前进程-Native内存占用(Unity中的纹理，模型等)
    /// </summary>
    /// 
    public static int GetNativePssMemory()
    {
        int ret = 0;
#if UNITY_EDITOR
        
#elif UNITY_ANDROID

        JNIAttachThread();
        ret = java_class.CallStatic<int>("GetNativePssMemory");  
        JNIDetachThread();
        
#endif

        return ret;
    }

    /// <summary>
    /// 初始化本地push服务，ios需要注册服务才能push
    /// </summary>
    public static void InitNativeNotification()
    {
#if (UNITY_IPHONE || UNITY_IOS) && !UNITY_EDITOR
        initNativePush();
#endif
    }

    /// <summary>
    /// 返回指定目录的剩余存储空间
    /// </summary>
    public static long GetAvailableSize(string path)
    {
        long ret = 0x7FFFFFFFL;

#if UNITY_ANDROID && !UNITY_EDITOR
        JNIAttachThread();
        ret = java_class.CallStatic<long>("GetAvailableSize", path);
        JNIDetachThread();
#elif UNITY_IPHONE || UNITY_IOS
        ret = 0x7FFFFFFFL;
#endif
        return ret;

    }

    /// <summary>
    /// 返回指定目录总在存储空间
    /// </summary>
    public static long GetTotalSize(string path)
    {
        long ret = 0x7FFFFFFFL;

#if UNITY_ANDROID
        JNIAttachThread();
        ret = java_class.CallStatic<long>("GetTotalSize", path);
        JNIDetachThread();
#elif UNITY_IPHONE || UNITY_IOS
        ret = 0x7FFFFFFFL;
#endif
        return ret;
    }

    /// <summary>
    /// 播放视频接口
    /// </summary>
    public static void PlayVideo(string path)
    {
#if UNITY_ANDROID
        JNIAttachThread();
        java_class.CallStatic("PlayVideo", path, null);
        JNIDetachThread();
#elif UNITY_IPHONE || UNITY_IOS

#endif
    }

    /// <summary>
    /// 是否正在播放视频
    /// </summary>
    public static bool IsPlayingVideo()
    {
        bool ret = false;

#if UNITY_ANDROID
        JNIAttachThread();
        ret = java_class.CallStatic<bool>("IsPlayingVideo");
        JNIDetachThread();
#elif UNITY_IPHONE || UNITY_IOS

#endif

        return ret;
    }

    /// <summary>
    /// 获取网络类型，如果是WIFI，返回MAC地址;如果是移动网络，返回网络类型名称，如:3GWAP,3GNET
    /// </summary>
    public static string GetNetWorkType()
    {
        string netWorkType = "UNKNOWN";

        //IOS平台获取网络类型
#if (UNITY_IPHONE || UNITY_IOS) && !UNITY_EDITOR
        netWorkType = getApnInfo();

#elif UNITY_ANDROID && !UNITY_EDITOR
        //Android平台获取网络类型
        JNIAttachThread();
        netWorkType = java_class.CallStatic<string>("GetApnInfo", 0);
        JNIDetachThread();
#else
        //PC平台获取网络类型
#if UNITY_2018_2_OR_NEWER
#else
        netWorkType = Network.player.ipAddress;
#endif
#endif

        return netWorkType;
    }

    /// <summary>
    /// 重启游戏
    /// </summary>
    public static void RestartApplication()
    {
#if UNITY_ANDROID
        JNIAttachThread();
        java_class.CallStatic("RestartApplication");
        JNIDetachThread();
#endif
    }

    /// <summary>
    /// 发送日志文件
    /// </summary>
    public static void SendShareFile(string filePath)
    {

#if UNITY_IPHONE || UNITY_IOS
        _NativeShare_Share(new string[] { filePath }, 1, "subject", "share");
#elif UNITY_ANDROID
        JNIAttachThread();
        java_class.CallStatic("ShareFile", filePath);
        JNIDetachThread();
#else
        Application.OpenURL("file:///" + filePath);
#endif
    }


    /// <summary>
    /// 打开应用评价
    /// </summary>
    /// <param name="appId"></param>
    /// <param name="inApp"></param>
    public static void OpenReview(string url, bool inApp)
    {
#if UNITY_IPHONE
        if (inApp)
        {
            float version = getSystemVersion();
            Diagnostic.Log("ios version: " + version);
            if (version >= 10.3f - 0.0001f)
            {
                //版本>10.3， 可以采用应用内打开
                Diagnostic.Log("openInAppReview");
                int ret = openInAppReview();
                Diagnostic.Log("openInAppReview: " + ret);
                return;
            }
        }

        //应用外打开
        //string url = "itms-apps://itunes.apple.com/app/id" + appId;

        Diagnostic.Log("OpenReview: " + url);

        openUrl(url);    
#endif
    }

    public static int TestOpenReview()
    {
#if UNITY_IPHONE
        int ret = openInAppReview();
        Diagnostic.Log("openInAppReview: "+ret);
        return ret;
#endif
        return -1;
    }

    public static void SaveInKeyChain(string service, string keyName, string keyText, string valueName, string valueText)
    {
#if UNITY_IPHONE
        Diagnostic.Log("SaveInKeyChain:start ");
        storeKeyChain(service, keyName, valueName, keyText, valueText);
        Diagnostic.Log("SaveInKeyChain:end ");
        return;
#endif
        return;
    }

    public static string GetValueFromKeChain(string service, string key)
    {
        string value = "";
#if UNITY_IPHONE
        Diagnostic.Log("GetValueFromKeChain: start");
        value = loadKeyChain(service, key);
        Diagnostic.Log("GetValueFromKeChain: end");
#endif
        return value;
    }

    public static void DeleteKeyChainData(string service)
    {
#if UNITY_IPHONE
        Diagnostic.Log("DeleteKeyChainData: start");
        var retStr = deleteKeyChain(service);
        Diagnostic.Log("DeleteKeyChainData: end:{0}", retStr);
#endif
    }
    public static void JNICall(Action call)
    {
        JNIAttachThread();
        try
        {
            call();
        }
        finally
        {
            JNIDetachThread();
        }
    }
    public static T JNICall<T>(Func<T> call)
    {
        JNIAttachThread();
        try
        {
            return call();
        }
        finally
        {
            JNIDetachThread();
        }
    }
    private static void JNIAttachThread()
    {
#if UNITY_ANDROID
        if (SystemManager.getInstance() != null)
        {
            var currentThreadID = System.Threading.Thread.CurrentThread.ManagedThreadId;
            var mainThreadID = SystemManager.getInstance().ID_MainThread;
            if (currentThreadID != mainThreadID)
                AndroidJNI.AttachCurrentThread();
        }
#endif
    }

    private static void JNIDetachThread()
    {
#if UNITY_ANDROID
        if (SystemManager.getInstance() != null)
        {
            var currentThreadID = System.Threading.Thread.CurrentThread.ManagedThreadId;
            var mainThreadID = SystemManager.getInstance().ID_MainThread;
            if (currentThreadID != mainThreadID)
                AndroidJNI.DetachCurrentThread();
        }
#endif
    }


    #region 截图监听
    //建个回调委托(用于获得函数指针)
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    internal delegate void ScreenCapturerCallBack();
    
    //实现回调方法
    public static Action OnScreenCapture;
    [MonoPInvokeCallback(typeof(ScreenCapturerCallBack))]
    private static void ScreenCapturerCallback()
    {
        Diagnostic.Log("TKPlatform.ScreenCapturerCallback");
        if (OnScreenCapture != null)
            OnScreenCapture();
    }

    public static void RegisterScreenCapturerCallback()
    {
#if UNITY_IOS && !UNITY_EDITOR
        Diagnostic.Log("TKPlatform.RegisterScreenCapturerCallback");
        //ScreenCapturerCallBack handler = new ScreenCapturerCallBack(ScreenCapturerCallback);
        //IntPtr ptr = Marshal.GetFunctionPointerForDelegate(handler);
        registerScreenCapturerCallback();
#endif
    }
    #endregion

    #region 耳机监听
    //建个回调委托(用于获得函数指针)
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    internal delegate void EarPhoneCallBack(bool isOn);
    
    //实现回调方法
    public static Action<bool> OnEarPhoneStatus;
    [MonoPInvokeCallback(typeof(EarPhoneCallBack))]
    private static void EarPhoneStatusCallBack(bool isOn)
    {
        Diagnostic.Log("TKPlatform.EarPhoneStatusCallBack");
        if (OnEarPhoneStatus != null)
            OnEarPhoneStatus(isOn);
    }

    //注册函数指针到oc
    public static void RegisterEarPhoneChangedCallback()
    {
#if UNITY_IOS && !UNITY_EDITOR
        Diagnostic.Log("TKPlatform.registerEarPhoneChangedCallback");
        //EarPhoneCallBack handler = new EarPhoneCallBack(EarPhoneStatusCallBack);
        //IntPtr ptr = Marshal.GetFunctionPointerForDelegate(handler);
        registerEarPhoneChangedCallback();
#endif
    }

    public static int ResetHeadsetStatus()
    {
#if UNITY_EDITOR
        return 0;
#endif
        int ret = -1;
#if UNITY_IPHONE || UNITY_IOS
        ret = GetHeadsetStatus();
#elif UNITY_ANDROID
        var activity = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        var currentActivity = activity.GetStatic<AndroidJavaObject>("currentActivity");
        var context = currentActivity.Call<AndroidJavaObject>("getApplicationContext");
        AndroidJavaClass javaClass = new AndroidJavaClass("com.Gradle.AndroidUtil");
        ret = javaClass.CallStatic<int>("getheadsetStatus", context);
        Diagnostic.Log("Android ResetHeadsetStatus Ret:{0}", ret);
#endif
        if (ret == 1 || ret == 2)
            TKFrameworkDelegateInterface.SetHeadPhoneMode(true);
        else
            TKFrameworkDelegateInterface.SetHeadPhoneMode(false);

        return ret;
    }

    #endregion

    #region 掌盟分享
    [LightProfiler.ProfilerAttribute]
    public static void SetZMShareApp(byte[] bytes,string imagePath,string title,string desc)
    {
#if UNITY_EDITOR
        return;
#endif
        int ret = -1;
#if UNITY_IPHONE || UNITY_IOS
        ret = ZMShareAPP(bytes,bytes.Length,title,desc);
#elif UNITY_ANDROID
        var activity = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        var currentActivity = activity.GetStatic<AndroidJavaObject>("currentActivity");
        var context = currentActivity.Call<AndroidJavaObject>("getApplicationContext");
        AndroidJavaClass javaClass = new AndroidJavaClass("com.Gradle.AndroidUtil");
        ret = javaClass.CallStatic<int>("SetZMShareAPP", context, imagePath, title, desc);
        Diagnostic.Log("Android ZMShare Ret:{0}", ret);
#endif
        if (ret == 1)
            UIOverlay.Instance.ShowCommonTips("用户未安装掌盟，无法分享");
        else if (ret == 2)
            UIOverlay.Instance.ShowCommonTips("用户掌盟版本过低，无法分享");

        return;
    }

    #endregion

    #region 掌盟分享
    [LightProfiler.ProfilerAttribute]
    public static void SetXhsShareApp(byte[] bytes, string imagePath, string title, string desc)
    {
#if UNITY_EDITOR
        return;
#endif
        int ret = -1;
#if UNITY_IPHONE || UNITY_IOS
        ret = XHSShareAPP(bytes,bytes.Length,title,desc);
#elif UNITY_ANDROID
        var activity = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        var currentActivity = activity.GetStatic<AndroidJavaObject>("currentActivity");
        var context = currentActivity.Call<AndroidJavaObject>("getApplicationContext");
        AndroidJavaClass javaClass = new AndroidJavaClass("com.Gradle.AndroidUtil");
        ret = javaClass.CallStatic<int>("SetXhsShareAPP", context, imagePath, title, desc);
        Diagnostic.Log("Android XhsShare Ret:{0}", ret);
#endif
        if (ret == 1)
            UIOverlay.Instance.ShowCommonTips("用户未安装小红书，无法分享");
        else if (ret == 2)
            UIOverlay.Instance.ShowCommonTips("用户小红书版本过低，无法分享");

        return;
    }

    #endregion

    public static byte[] GetScreenshotData()
    {
#if UNITY_IOS
        Diagnostic.Log("TKPlatform.GetScreenshotData");
        try
        {
			IntPtr ptr = IntPtr.Zero;
			int size = 0;
			GetScreenshotInPNGFormat(ref ptr, ref size);
            byte[] data = new byte[size];
            Marshal.Copy(ptr, data, 0, size);
			Marshal.FreeHGlobal(ptr);
            return data;
        }
        catch (Exception e)
        {
            Diagnostic.Log(e);
        }
#endif
        return null;
    }

    /// <summary>
    /// 手机震动
    /// Tips: 请通过DeviceVibrator.SetVibrator()调用, 应用层勿直接使用
    /// </summary>
    [LightProfiler.ProfilerAttribute]
    public static int SetVibrator()
    {
#if UNITY_EDITOR
        return 0;
#endif
        
#if UNITY_IPHONE || UNITY_IOS
        PlaySystemSoundWithCompletion((int)SystemSoundID.vibrate_slight1);
#elif UNITY_ANDROID
        JNIAttachThread();
        java_class.CallStatic("SetVibrator", new long[2] { 0, 30 }, -1);
        JNIDetachThread();
#endif
        return 0;
    }

    /// <summary>
    /// 手机震动 强震动
    /// Tips: 请通过DeviceVibrator.SetVibrator()调用, 应用层勿直接使用
    /// </summary>
    [LightProfiler.ProfilerAttribute]
    public static int SetMatchVibrator()
    {
#if UNITY_EDITOR
        return 0;
#endif

#if UNITY_IPHONE || UNITY_IOS
        PlaySystemSoundWithCompletion((int)SystemSoundID.vibrate_slight3);
#elif UNITY_ANDROID
        JNIAttachThread();
        java_class.CallStatic("SetVibrator", new long[2] { 0, 30 }, -1);
        JNIDetachThread();
#endif
        return 0;
    }

#if UNITY_IPHONE || UNITY_IOS
    public static void OnApplicationPauseGvoice()
    {
            OnApplicationPauseTest();
    }
#endif


    public static int IOSPlaySystemSoundWithCompletion(int soundId)
    {
#if UNITY_EDITOR
        
#elif  UNITY_IPHONE || UNITY_IOS
        PlaySystemSoundWithCompletion(soundId);
#endif
        return 0;
    }

    /// <summary>
    /// 设置安卓全屏显示
    /// </summary>
    /// <returns></returns>
#if UNITY_ANDROID
    public static void SetFullScreenOnAndroidP()
    {
        JNIAttachThread();
        java_class.CallStatic("SetFullScreenOnAndroidP");
        JNIDetachThread();
    }
#endif

    /// <summary>
    /// androidID
    /// </summary>
    /// <returns></returns>
    public static string android_id = "";
#if UNITY_ANDROID
    public static string GetAndroidID()
    {
        //if (string.IsNullOrEmpty(android_id))
        //{
        //    AndroidJavaClass up = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        //    AndroidJavaObject currentActivity = up.GetStatic<AndroidJavaObject>("currentActivity");
        //    AndroidJavaObject contentResolver = currentActivity.Call<AndroidJavaObject>("getContentResolver");
        //    AndroidJavaClass secure = new AndroidJavaClass("android.provider.Settings$Secure");

        //    android_id = secure.CallStatic<string>("getString", contentResolver, "android_id");
        //}
        return android_id;
    }
#endif

    /// <summary>
    /// 安卓源生方式获取屏幕刘海高度
    /// </summary>
    /// <returns></returns>
    public static int GetNotchSizeOnAndroid()
    {
        int notchSize = GetNotchSize();
        Diagnostic.Log("GetNotchSizeOnAndroid notchSize:" + notchSize);
        if (notchSize > 0)
        {
            // 因为游戏分辨率和设备分辨率可能不一致，所以这里要做下计算
            int height = GetScreenHeight();
            Diagnostic.Log("GetNotchSizeOnAndroid height:" + height);
            if (height > 0)
            {
                int rh = Screen.currentResolution.height;
                notchSize = Mathf.CeilToInt(notchSize * ((float)rh / height));
            }
        }
        return notchSize;
    }

    public static int GetNotchSize()
    {
        int notchHeight = 0;
#if UNITY_EDITOR
        
#elif  UNITY_ANDROID
        JNIAttachThread();
        notchHeight = java_class.CallStatic<int>("GetNotchSize");
        JNIDetachThread();
#endif
        return notchHeight;
    }

    public static int GetScreenHeight()
    {
        int notchHeight = 0;
#if UNITY_EDITOR
        
#elif UNITY_ANDROID
        JNIAttachThread();
        notchHeight = java_class.CallStatic<int>("GetScreenHeight");
        JNIDetachThread();
#endif
        return notchHeight;
    }

    public static void CopyToClipboard(string input)
    {
#if UNITY_EDITOR
        TextEditor t = new TextEditor();
        t.text = input;
        t.OnFocus();
        t.Copy();
        s_copyText = input;
#elif UNITY_IPHONE
        //IOS 暂时屏蔽复制接口
        __CopyTextToClipboard(input);
#elif UNITY_ANDROID
        JNIAttachThread();
        java_obj.Call("CopyTextToClipboard", input);
        JNIDetachThread();          
#endif
    }

    public static string getTextFromClipboard()
    {
        string text = "";
#if UNITY_EDITOR
        text = s_copyText;
#elif UNITY_IPHONE
        text = __GetClipboardText();
#elif UNITY_ANDROID
        JNIAttachThread();
        text = java_obj.Call<string>("getTextFromClipboard");
        JNIDetachThread();              
#endif
        return text;
    }

    public static int GetOSVersion()
    {
        int osVersion = 0;
#if UNITY_EDITOR
        
#elif UNITY_ANDROID
        JNIAttachThread();
        osVersion = java_class.CallStatic<int>("GetOSVersion");
        JNIDetachThread();
#endif
        return osVersion;
    }

    public static long GetFreeDiskSize()
    {
       long freeDiskSize = -1;
#if UNITY_EDITOR
        
#elif UNITY_IPHONE
      freeDiskSize = _GetFreeDiskSpace();
#elif UNITY_ANDROID
      JNIAttachThread();
      freeDiskSize = java_class.CallStatic<long>("GetFreeDiskSize");
      JNIDetachThread();
#endif
        return freeDiskSize;
    }

//    /// <summary>
//    /// 手机的IMEI（已废弃）
//    /// 国家规定不再允许收集IMEI
//    /// 
//    /// Android 6.0以上系统需要用户授予权限
//    /// iOS 5之后被禁止
//    /// </summary>
//    /// <seealso cref="https://iwiki.woa.com/pages/viewpage.action?pageId=516940261&from=iSearch"/>
//    public static string GetDeviceIMEI()
//    {
//        var str = "";

//#if UNITY_EDITOR

//#elif UNITY_ANDROID
//        JNIAttachThread();
//        // 有可能是因为权限问题未能得到，返回空
//        str =  java_class.CallStatic<string>("GetDeviceIMEI");
//        JNIDetachThread();
//#endif
//        return str;
//    }

    /// <summary>
    /// 注册网络环境变更的通知
    /// </summary>
    public static void RegisterNetworkReceiver()
    {
#if UNITY_ANDROID
        JNIAttachThread();
        java_class.CallStatic("RegisterNetworkReceiver");
        JNIDetachThread();
#endif
    }
    
    //打开系统权限页面
    public static void OpenSysPermissionPanel()
    {
/*#if UNITY_EDITOR
        return;
#endif*/
        Diagnostic.Log("OpenSysPermissionPanel...");
#if UNITY_IPHONE || UNITY_IOS
        _OpenSysPermissionPanel();
#elif UNITY_ANDROID
        var activity = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        var currentActivity = activity.GetStatic<AndroidJavaObject>("currentActivity");
        currentActivity.Call("OpenSysPermissionPanel");
#endif
    }
}

