using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using TKPlugins;
using UnityEngine.UI;
using ZGame;

namespace TKFrame.Item
{
    /// <summary>
    /// 对话框
    /// </summary>
    public class CreditScoreDialog : PopLayout
    {
        [UIObject("content")]
        private GameObject goContent { get; set; }

        [UIObject("cancelText")]
        private GameObject goCancelText { get; set; }

        [UIObject("confirmText")]
        private GameObject goConfirmText { get; set; }

        [UIObject("confirm")]
        private GameObject goConfirm { get; set; }

        [UIObject("cancel")]
        private GameObject goCancel { get; set; }

        [UIObject("Btn_Close")]
        private GameObject goBtn_Close { get; set; }




        //响应确认
        public Action confirmListener;

        //响应取消
        public Action cancelListener;

        //异常关闭响应
        public Action exceptionDisposeListener;

        //等待被设置的文本（加载未完成无法设置文本）
        private String text_WaitToSet;

        //超时取消时间
        private int delayTime = 0;

        //延迟可点击
        public int delayClickTime = 0;

        //只显示一个确认按钮（扩展）
        private bool only_confirm = false;
        // 是否显示右上角关闭按钮
        private bool showCancleBtn = false;

        // 按钮文字
        public string confirmString = null;
        public string cancelString = null;

        private Timer delayTimer = null;
        private Timer delayClickTimer = null;
        private HyperText _textItem;
        private string _textString;

        bool isConfirmDone = false;
        bool isCancelDone = false;
        bool needCancel = false;

        //初始化
        public CreditScoreDialog(string assetBundle, string resName,
            string id, bool mode, bool only_confirm = false, bool showCancleBtn = true, bool needCancel = false)
            : base(assetBundle, resName, id, mode)
        {
            
        }

        public CreditScoreDialog(System.Action loadCB = null, System.Action closeCB = null) : base("chessart/ui/dialog/dialog_creditscore", "Dialog_CreditScore", "", true)
        {           
            isBlurBackGround = true;
            HideWhenNewDialog = false;
        }

        //响应确认操作
        [CmdAttribute("confirm")]
        public void confirm()
        {
            //添加业务层响应
            if (confirmListener != null)
            {
                confirmListener();
                isConfirmDone = true;
                confirmListener = null;
            }

            dispose();
        }

        //响应取消操作
        [CmdAttribute("cancel")]
        public void cancel()
        {
            //添加业务层响应
            if (cancelListener != null)
            {
                cancelListener();
                isCancelDone = true;
                cancelListener = null;
            }
            else if (only_confirm && confirmListener != null)
            {
                //处理只有确认键时点上方X关闭
                confirmListener();
                isConfirmDone = true;
                confirmListener = null;
            }

            dispose();
        }

        public override void dispose()
        {
            //因异常原因（比如切场景）导致的dispose，如果有cancelcallback还是要调用一下
            if (!isConfirmDone && cancelListener != null && needCancel)
            {
                cancelListener();
                isCancelDone = true;
                cancelListener = null;
            }

            if (!isConfirmDone && !isCancelDone && exceptionDisposeListener != null)
            {
                exceptionDisposeListener();
                exceptionDisposeListener = null;
            }

            StopDelayTimer();
            _textItem = null;
            _textString = null;

            if (delayClickTimer != null)
            {
                delayClickTimer.StopTimer();
                delayClickTimer = null;
            }

            base.dispose();
        }

        private bool BackEvent()
        {
            this.dispose();
            return false;
        }

        //设置文本内容
        public void setText(String text)
        {
            if (IsInited)
            {
                setTextOnGameObject(text);
            }
            else
            {
                this.text_WaitToSet = text;
            }
        }

        public void setDelayTime(int time)
        {
            delayTime = time;
        }






        //设置文本(初始化完成后才能调用)
        private void setTextOnGameObject(String text)
        {
            text = "<color=#A09B8C>" + text + "</color>";
            text += "\n<url=https://gamecredit.qq.com/static/games/index.htm><color=#bc9d46>查看信用分详情</color></url> ";

            if (prefab_GameObject != null)
            {
                var goList = prefab_GameObject.GetComponent<GameObjectList>();
                if (goList != null)
                {
                    GameObject go = goList.Find("content");
                    _textItem = go.GetComponent<HyperText>();
                    if (_textItem != null && _textItem.text != text)
                    {
                        _textString = text;
                        if (delayTime > 0)
                        {
                            _textItem.text = text + "(" + delayTime + ")";
                        }
                        else
                        {
                            _textItem.text = text;
                        }
                    }
                }
            }
        }

        //通知界面加载完成
        public override void NotifyLoaded()
        {
            base.NotifyLoaded();
            //设置文本
            if (text_WaitToSet != null)
            {
                setTextOnGameObject(text_WaitToSet);
                text_WaitToSet = null;
            }

            var goList = prefab_GameObject.GetComponent<GameObjectList>();
            if (goList != null)
            {
                goConfirm.GetComponent<Button>().onClick.AddListener(OnClickConfirm);
            }

        }

        private void OnClickConfirm()
        {
            this.dispose();
        }

        private Color oldConfirmBtnColor;

        private void StartDelayTimer()
        {
            delayTimer = Timer.StartTimer(this.prefab_GameObject, OnTimer, 1.0f, true, true);
        }

        private void StartDelayClickTimer()
        {
            delayClickTimer = Timer.StartTimer(this.prefab_GameObject, OnDelayClickTimer, 1.0f, true, true);
        }

        private void OnTimer(object sender, TKEventArgs e)
        {
            delayTime -= e._countTimes;

            if (delayTime <= 0)
            {
                StopDelayTimer();
                cancel();
            }
            else if (_textItem != null && !string.IsNullOrEmpty(_textString))
            {
                _textItem.text = _textString + "(" + delayTime + ")";
            }
        }

        private void OnDelayClickTimer(object sender, TKEventArgs e)
        {
            delayClickTime -= e._countTimes;

            if (delayClickTime <= 0)
            {
                SetButtonGray(goConfirm.GetComponent<Button>(), false, true);
                goConfirm.GetComponentInChildren<Text>().text = string.IsNullOrEmpty(confirmString) ? "" : confirmString;
                goConfirm.GetComponentInChildren<Text>().color = oldConfirmBtnColor;
            }
            else if (_textItem != null && !string.IsNullOrEmpty(_textString))
            {
                goConfirm.GetComponentInChildren<Text>().text = string.IsNullOrEmpty(confirmString) ? "" : confirmString + "(" + delayClickTime + ")";
            }
        }

        public void StopDelayTimer()
        {
            if (delayTimer != null)
            {
                delayTimer.StopTimer();
                delayTimer = null;
            }
        }


        public void SetButtonGray(Button button, bool isGray, bool canClck)
        {
            button.GetComponent<Button>().interactable = false;
            Image[] images = button.GetComponentsInChildren<Image>();
            for (int i = 0; i < images.Length; i++)
            {
                TKFrameworkDelegateInterface.GameUtil_SetGrayUI(images[i], isGray);
                //GameUtil.SetGrayUI(images[i], isGray);
            }

            button.enabled = canClck;
            button.GetComponent<NonDrawingGraphic>().raycastTarget = canClck;

        }
    }
}
