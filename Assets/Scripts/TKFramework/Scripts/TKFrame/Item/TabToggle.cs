using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using TKFrame;

namespace TKFrame.Item
{
    public class TabToggle : TKUIBehaviour
    {
        #region inspector value
        /// <summary>
        /// 未选中名字
        /// </summary>
        [UIObjectAttribute("TextOff-Name")]
        public GameObject nameTextOffGo
        {
            get;
            set;
        }

        /// <summary>
        /// 选中名字
        /// </summary>
        [UIObjectAttribute("TextOn-Name")]
        public GameObject nameTextOnGo
        {
            get;
            set;
        }

        /// <summary>
        /// 小红点
        /// </summary>
        [UIObjectAttribute("RedPoint")]
        public GameObject redPointGo
        {
            get;
            set;
        }
        #endregion


        #region public functions
        /// <summary>
        /// 设置名字
        /// </summary>
        /// <param name="name"></param>
        public void SetName(string name)
        {
            nameTextOffGo.GetComponent<Text>().text = name;
            nameTextOnGo.GetComponent<Text>().text = name;
        }

        public string GetName()
        {
            string name = "";
            if ((nameTextOnGo != null) && (nameTextOnGo.GetComponent<Text>() != null))
                name = nameTextOnGo.GetComponent<Text>().text;

            return name;
        }

        /// <summary>
        /// 设置小红点是否显示
        /// </summary>
        /// <param name="name"></param>
        public void SetRedPointVisible(bool isVisible)
        {
            redPointGo.SetActive(isVisible);
        }
        #endregion
    }
}

