using System;
using System.Collections;
using System.Linq;
using System.Text;
using UnityEngine;
using TKPlugins;
using TKFrame;
using UnityEngine.UI;


namespace TKFrame.Item
{

    /// <summary>
    /// 带定时动画的PopLayout
    /// 可设置销毁延时,以供界面执行关闭动画
    /// </summary>
    [ObjectRegistAttribute]
    public class AnimatedPopLayout:PopLayout
    {
        /// <summary>
        /// 销毁延时,可以在动画_disposeAnimateDelegate回调时或者回调前设置
        /// </summary>
        public float DisposeDelay{get;set;}



        /// <summary>
        /// 关闭时动画代理,用于启动业务层弹框动画
        /// </summary>
        Action _disposeAnimateDelegate;

       
        public AnimatedPopLayout(bool mode = true, bool isSystemDialog = false)
            :base(mode,isSystemDialog)
        {
        }

        //构造
        public AnimatedPopLayout(string assetBundle, string resName,
            string id, bool mode, bool isSystemDialog=false)
            :base(assetBundle,resName,id,mode,isSystemDialog)
        {
        }
       
        /// <summary>
        /// 设置关闭时动画代理,用于启动业务层弹框动画
        /// *在资源未加载完毕之前关闭动画,则该动画代理将不会被调用
        /// </summary>
        /// <param name="theDelegate"></param>
        public void SetDisposeAnimateDelegate(Action theDelegate)
        {
            _disposeAnimateDelegate = theDelegate;
        }


       // public void SetDisp

        public override void dispose()
        {
            //先会调用动画启动代理,提供改变延时的机会
            if (_disposeAnimateDelegate != null)
            {
                _disposeAnimateDelegate();
            }

            if (DisposeDelay <= 0)
            {
                DisposeImmediate();
                return;
            }
            //还未创建好节点,则直接关闭
            if (Holder == null)
            {
                DisposeImmediate();
                return;
            }

            if (prefab_GameObject.activeInHierarchy)
            {
                //如果该弹出框携带半透明的遮罩背景
                if(isAlphaMask)
                {
                    AlphaBackGroundUtil.AlphaMaskDisposeAct(this);
                }
                Holder.StartCoroutine(delayDispose());
            }
        }

        private IEnumerator delayDispose()
        {
            yield return TKFrame.CoroutineWait.GetWaitForSeconds(DisposeDelay);
            DisposeImmediate();
        }
        /// <summary>
        /// 直接销毁当前popLayout
        /// </summary>
        public void DisposeImmediate()
        {
            base.dispose();
        }
    }
}
