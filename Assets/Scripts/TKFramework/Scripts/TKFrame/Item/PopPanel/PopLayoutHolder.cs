using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using TKPlugins;
using TKFrame;
using UnityEngine.UI;

namespace TKFrame.Item
{

    public class PopLayoutHolder: MonoBehaviour
    {
        PopLayout _layout;
        internal void SetPopLayout(PopLayout layout)
        {
            this._layout = layout;
        }
        public PopLayout GetPopLayout()
        {
            return _layout;
        }

        //private void OnEnable()
        //{
        //    Diagnostic.Log("{0}.OnEnable", gameObject.name);
        //}

        //private void OnDisable()
        //{
        //    Diagnostic.Log("{0}.OnDisable", gameObject.name);
        //}
    }
}
