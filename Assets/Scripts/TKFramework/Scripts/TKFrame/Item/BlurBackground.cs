using UnityEngine;
using System.Collections;
using UnityEngine.UI;
using TKFrame;
using System;
using System.Collections.Generic;

public class BlurBackground : MonoBehaviour
{

    //public GameObject targetCameraObj;

    private Transform m_tfTextureBlur;

    public delegate void BlurComplete(object obj);
    private BlurComplete m_delegateBlurComplete;
    private object m_param;
    private RenderTexture m_renderTexture = null;

    public bool customParam = false;
    [Range(0, 5)]
    public int downsample = 1;

    [Range(0.0f, 50.0f)]
    public float blurSize = 0.0f;

    [Range(1, 4)]
    public int blurIterations = 2;

    public Shader blurShader;

    private Material m_blurMaterial = null;

    private List<Camera> cameras = new List<Camera>();

    protected Material BlurMaterial
    {
        get
        {
            if (m_blurMaterial == null)
            {
                if (blurShader == null)
                {
                    Diagnostic.Log("blurShader == null");
                }
                else
                {
                    m_blurMaterial = new Material(blurShader);
                    if (m_blurMaterial != null)
                    {
                        m_blurMaterial.hideFlags = HideFlags.HideAndDontSave;
                    }
                    else
                    {
                        Diagnostic.Log("BlurBackground blurMaterial is null");
                    }
                }
            }
            return m_blurMaterial;
        }
    }

    void Awake()
    {
        m_tfTextureBlur = transform;
    }

    public void Init()
    {
        if (!customParam)
        {
            blurSize = 2.0f;
            blurIterations = 4;
        }
        IAssetService assetService = Services.GetService<IAssetService>();
        LoadedAsset loadedAsset = assetService.LoadAssetSync<Shader>("shaders/based_shaders", "MobileBlur");
        if (loadedAsset == null) throw new ArgumentNullException(nameof(loadedAsset));
        Shader blurshader = loadedAsset.GetAsset<Shader>();

        if (blurshader == null)
        {
            Diagnostic.Log("Init blurShader == null");
        }

        if (m_renderTexture == null)
        {
            m_renderTexture = RenderTexture.GetTemporary(Screen.width >> downsample, Screen.height >> downsample, 24, RenderTextureFormat.Default);
            m_renderTexture.name = "BlurBackGroundRT";
        }

        blurShader = blurshader;
    }

    //全局模糊调用的接口
    public void Blur()
    {
        if (m_renderTexture == null)
            Init();

        //找到当前所处的摄像机
        cameras.Clear();
        RawImage _rawImage = gameObject.GetComponent<RawImage>();
        if (_rawImage != null)
        {
            Canvas canvas = _rawImage.canvas;
            if (canvas != null && canvas.renderMode == RenderMode.ScreenSpaceCamera)
            {
                Camera curCamera = canvas.worldCamera;
                if (curCamera != null)
                {
                    for (int i = 0; i < Camera.allCameras.Length; i++)
                    {
                        if (Camera.allCameras[i].gameObject.activeSelf && curCamera.depth >= Camera.allCameras[i].depth)
                        {
                            if (Camera.allCameras[i].targetTexture == null)
                            {
                                cameras.Add(Camera.allCameras[i]);


                            }
                        }
                    }
                }
            }
        }

        //按照深度从小到大排序
        cameras.Sort((a, b) => a.depth.CompareTo(b.depth));

        // this will never happen, just for fixing codecc warning.
        if (m_renderTexture == null) return;

        var rtTemp = /*RenderTexture.GetTemporary(m_renderTexture.descriptor)*/m_renderTexture;
        rtTemp.DiscardContents();
        for (int j = 0; j < cameras.Count; j++)
        {
            Rect rect = cameras[j].rect;
            cameras[j].rect = new Rect(0, 0, 1f, 1f);
            cameras[j].targetTexture = rtTemp;
            // let first camera clear content to make sure that the rt has no junk data.
            if (j == 0)
            {
                var firstCamera = cameras[j];
                var clearFlags = firstCamera.clearFlags;
                var cameraBGColor = cameras[j].backgroundColor;
                firstCamera.clearFlags = CameraClearFlags.SolidColor;
                firstCamera.backgroundColor = Color.black;
                firstCamera.Render();
                firstCamera.clearFlags = clearFlags;
                firstCamera.backgroundColor = cameraBGColor;
            }
            else
                cameras[j].Render();

            cameras[j].targetTexture = null;
            cameras[j].rect = rect;
        }

        BlurBlit(ref rtTemp);
        //m_renderTexture.DiscardContents();
        //Graphics.Blit(rtTemp, m_renderTexture);
        //RenderTexture.ReleaseTemporary(rtTemp);

        RawImage rawImage = m_tfTextureBlur.gameObject.GetComponent<RawImage>();

        m_delegateBlurComplete?.Invoke(m_param);
        rawImage.texture = m_renderTexture;
    }

    public void BlurBlit(ref RenderTexture cameraRenderTexture)
    {
        float widthMod = 1.0f / (1.0f * (1 << downsample));
        if (BlurMaterial != null)
        {
            BlurMaterial.SetVector("_Parameter", new Vector4(blurSize * widthMod, -blurSize * widthMod, 0.0f, 0.0f));

            RenderTexture rt2 = RenderTexture.GetTemporary(cameraRenderTexture.width, cameraRenderTexture.height);
            rt2.name = "BlurSwapRT";
            for (int i = 0; i < blurIterations; i++)
            {
                float iterationOffs = (i * 1.0f);
                BlurMaterial.SetVector("_Parameter", new Vector4(blurSize * widthMod + iterationOffs, -blurSize * widthMod - iterationOffs, 0.0f, 0.0f));
                rt2.DiscardContents();
                // vertical
                Graphics.Blit(cameraRenderTexture, rt2, BlurMaterial, 1);
                cameraRenderTexture.DiscardContents();
                // horizontal
                Graphics.Blit(rt2, cameraRenderTexture, BlurMaterial, 2);
            }
            RenderTexture.ReleaseTemporary(rt2);
        }
        else
        {
            Diagnostic.Log("BlurBackground blur(cameraRenderTexture) blurMaterial is null ");
        }

        cameraRenderTexture.filterMode = FilterMode.Bilinear;
    }

    public void CleanUp()
    {
        if (m_renderTexture != null)
        {
            RenderTexture.ReleaseTemporary(m_renderTexture);
            m_renderTexture = null;
        }
    }

    private void OnDestroy()
    {
        CleanUp();
    }
}
