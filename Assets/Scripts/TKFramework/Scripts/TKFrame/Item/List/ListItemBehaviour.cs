using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using TKFrame;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace TKFrame.Item
{
    /// <summary>
    /// LIST 的Item项 事件处理类
    /// </summary>
    public class ListItemBehaviour : T<PERSON><PERSON>ehaviour, IDragHandler, IBeginDragHandler, IEndDragHandler, IPointerDownHandler, 
        IPointerUpHandler, IPointerClickHandler, IPointerEnterHandler
    {
        public Action<GameObject, PointerEventData> OnItemBeginDrag { get; set; }
        public Action<GameObject, PointerEventData> OnItemDrag { get; set; }
        public Action<GameObject, PointerEventData> OnItemEndDrag { get; set; }
        public Action<PointerEventData> OnItemPointEnter { get; set; }
        public Action<GameObject, PointerEventData> OnItemPointerDown { get; set; }
        public Action<PointerEventData> OnItemPointerUp { get; set; }
        public Action<PointerEventData> OnItemPointerClick { get; set; }
        public Action<PointerEventData> OnItemLongDown { get; set; }

        public ScrollRect ScrollRect { get; set; }

        //滑动的地板
        private GameObject Content;

        //短按到长按的间隔时间
        public float DEFAULT_CLICK_TIME = UIEventListenerMgr.LimitClickTime;

        //设备的分辨率
        private float _screenWidth = UIEventListenerMgr.ScreenWidth;
        private float _screenHeight = UIEventListenerMgr.ScreenHeight;


        //一次移动的距离是否超过限制
        public bool _isOutLimit;

        //开始滑动时的坐标点
        private Vector2 _beginDragPointer;

        //是否滑动的标记
        private bool _isDrag = false;

        //记录是否已经按下
        public bool _isPointerDown = false;
        //记录已经按下时间
        public float _pointerDownTimePassed;

        //按钮被按下之后的颜色
        private float _color = 0.78f;

        private PointerEventData _pointerEventData;

        public void LateUpdate()
        {
            if (_isPointerDown)
            {
                _pointerDownTimePassed += Time.deltaTime;
                if (_pointerDownTimePassed > DEFAULT_CLICK_TIME && !_isOutLimit && OnItemLongDown != null)
                {
                    OnItemLongDown(_pointerEventData);
                    _isPointerDown = false;                  
                }
                if(_pointerDownTimePassed > DEFAULT_CLICK_TIME)
                {
                    SetBtnPressColor(1f);
                }
            }
        }

        public void OnPointerEnter(PointerEventData eventData)
        {
            if (OnItemPointEnter != null)
            {
                OnItemPointEnter(eventData);
            }
        }

        public void OnPointerDown(PointerEventData eventData)
        {
            if (OnItemPointerDown != null)
            {
                OnItemPointerDown(this.gameObject,eventData);
            }
            _pointerDownTimePassed = 0.0f;
            _isPointerDown = true;
            _isOutLimit = false;
            _pointerEventData = eventData;
            if (ScrollRect != null)
            {
                ScrollRect.StopMovement();
            }
        }



        public void OnPointerUp(PointerEventData eventData)
        {
//             if (_isPointerDown)
//             {
                if (OnItemPointerUp != null)
                {
                    OnItemPointerUp(eventData);
                }
                _isPointerDown = false;
                SetBtnPressColor(_color);
            /*}*/
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            if (_pointerDownTimePassed < DEFAULT_CLICK_TIME && !_isDrag && OnItemPointerClick != null)
            {
                OnItemPointerClick(eventData);
            }

            _isPointerDown = false;

        }

        public void OnBeginDrag(PointerEventData eventData)
        {
            _pointerEventData = eventData;
            _isOutLimit = false;
            _isDrag = true;

            _beginDragPointer = eventData.position;

            if (OnItemBeginDrag != null)
            {
                OnItemBeginDrag(this.gameObject, eventData);
            }

			if (ScrollRect != null)
			{
            	ScrollRect.OnBeginDrag(eventData);
			}

            if(Content != null)
            {
                ListItemBehaviour itemBehaviour = Content.GetComponent<ListItemBehaviour>();
                if(itemBehaviour != null)
                {
                    if (itemBehaviour.OnItemBeginDrag != null)
                    {
                        itemBehaviour.OnItemBeginDrag(this.gameObject,eventData);
                    }
                }
            }
        }

        public void OnDrag(PointerEventData eventData)
        {
            _pointerEventData = eventData;

			if (ScrollRect != null)
			{
				ScrollRect.OnDrag(eventData);
			}

            if (!_isOutLimit && ((Math.Abs(eventData.position.x - _beginDragPointer.x) / _screenWidth > UIEventListenerMgr.LimitDragLenghtX) || (Math.Abs(eventData.position.y - _beginDragPointer.y) / _screenHeight > UIEventListenerMgr.LimitDragLenghtY)))
            {
                _isOutLimit = true;
                SetBtnPressColor(1f);
            }

            if (OnItemDrag != null)
            {
                OnItemDrag(this.gameObject, eventData);
            }

            if (Content != null)
            {
                ListItemBehaviour itemBehaviour = Content.GetComponent<ListItemBehaviour>();
                if (itemBehaviour != null)
                {
                    if (itemBehaviour.OnItemDrag != null)
                    {
                        itemBehaviour.OnItemDrag(this.gameObject, eventData);
                    }
                }
            }
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            _pointerEventData = eventData;
            
			if (ScrollRect != null)
			{
				ScrollRect.velocity = ScrollRect.velocity * 2f;
				ScrollRect.OnEndDrag(eventData);
			}

            //放开手的时候，不是长按，且移动的距离没有超过限制
            if (_pointerDownTimePassed < DEFAULT_CLICK_TIME && !_isOutLimit && OnItemPointerClick != null)
            {
                OnItemPointerClick(eventData);
                SetBtnPressColor(1f);
            }

            if (OnItemEndDrag != null)
            {
                OnItemEndDrag(this.gameObject, eventData);
            }

            if (Content != null)
            {
                ListItemBehaviour itemBehaviour = Content.GetComponent<ListItemBehaviour>();
                if (itemBehaviour != null)
                {
                    if (itemBehaviour.OnItemEndDrag != null)
                    {
                        itemBehaviour.OnItemEndDrag(this.gameObject, eventData);
                    }
                }
            }

            _isPointerDown = false;
            _isDrag = false;

            SetBtnPressColor(_color);
        }

        private void SetBtnPressColor(float value)
        {
            if (this != null && this.gameObject != null)
            {
                Button dd = this.gameObject.GetComponent<Button>();
                if (dd != null)
                {
                    ColorBlock cb = dd.colors;
                    if (dd.transition == UnityEngine.UI.Selectable.Transition.ColorTint)
                    {
                        cb.pressedColor = new Color(value, value, value, 1);
                        dd.colors = cb;
                    }

                }
            }      
        }

        public void SetScrollBG(GameObject go)
        {
            Content = go;
        }
		
    }
}
