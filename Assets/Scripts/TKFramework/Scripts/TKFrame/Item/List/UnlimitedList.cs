using UnityEngine;
using UnityEngine.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using TKFrame;

namespace TKFrame.Item
{
    /// <summary>
    /// 无限循环列表（支持不等高，宽的条目）
    /// </summary>
    public class UnlimitedList : TKUIBehaviour
    {

        //条目GameObject缓存池
        class ItemObjPool
        {
            List<GameObject> objList = new List<GameObject>();
            GameObject _temp;
            GameObject _root;

            public void Init(GameObject recyle_root, GameObject temp)
            {
                _root = recyle_root;
                _temp = temp;
                _temp.SetActive(false);
            }

            public void Recycle(GameObject obj)
            {
                obj.SetActive(false);
                obj.transform.SetParent(_root.transform, true);

                objList.Add(obj);
            }

            public GameObject Get()
            {
                GameObject obj;

                if (objList.Count == 0)
                {
                    obj = GameObject.Instantiate<GameObject>(_temp);
                    return obj;
                }

                obj = objList[0];
                objList.RemoveAt(0);

                obj.transform.localScale = _temp.transform.localScale;

                return obj;
            }
        }

        public enum Direction
        {
            Horizontal = 0,
            Vertical = 1,
        }

        public Direction _direction = Direction.Vertical;

        //间隔空隙
        public float _spacing;

        public GameObject ContentGo
        {
            get { return _content.gameObject; }
        }

        private RectTransform _content;

        private GameObject _cacheRoot;

        //条目的模版GameObject
        public GameObject _itemTemp;

        private int _itemCount
        {
            get { return _dataList.Count; }
        }

        //数据项列表
        private IList _dataList;

        private ItemObjPool _itemObjPool = new ItemObjPool();

        private float _viewSize = 0.0f;

        //视口大小
        public float ViewSize
        {
            get
            {
                return _viewSize;
            }
        }

        private float _contentSize = 0.0f;

        //内容总大小
        public float ContentSize
        {
            get
            {
                return _contentSize;
            }
        }

        private float _prePosition = 0.0f;

        private List<UnlimitedItem> _showItemList = new List<UnlimitedItem>();

        private int _min_show_index = -1;
        private int _max_show_index = -1;

        private int _directionFactor = 1;

        private float _original_content_pos;

        public float ContentPositon
        {
            get
            {
                if (_direction == Direction.Horizontal)
                    return _content.anchoredPosition.x;
                else
                    return _content.anchoredPosition.y;
            }
            set
            {
                Vector3 pos = _content.anchoredPosition;
                if (_direction == Direction.Horizontal)
                    _content.anchoredPosition = new Vector3(value, pos.y, pos.z);
                else
                    _content.anchoredPosition = new Vector3(pos.x, value, pos.z);
            }
        }

        public System.Func<object, float> _getItemSizeFun;

        object GetItemData(int index)
        {
            return _dataList[index];
        }

        Vector2 _tempOffsetMin;
        Vector2 _tempOffsetMax;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="temp"> 条目GameObject模版</param>
        /// <param name="spacing"> 条目间距</param>
        /// <param name="getItemSizeFun">计算条目大小的函数</param>
        /// <returns></returns>
        public bool Init(GameObject temp, float spacing, System.Func<object, float> getItemSizeFun)
        {
            _cacheRoot = new GameObject("cache_root");
            _cacheRoot.transform.SetParent(transform, false);

            var scroll_rect = GetComponent<ScrollRect>();
            _content = scroll_rect.content;

            if (_direction == Direction.Horizontal)
            {
                _viewSize = scroll_rect.viewport.rect.size.x;
                _directionFactor = 1;
                _original_content_pos = _content.anchoredPosition.x;
            }
            else
            {
                _viewSize = scroll_rect.viewport.rect.size.y;
                _directionFactor = -1;
                _original_content_pos = _content.anchoredPosition.y;
            }

            //Diagnostic.Log(gameObject.name + " view size: " + _viewSize);

            _itemTemp = temp;
            _itemTemp.SetActive(false);
            _spacing = spacing;
            if (_itemTemp.GetComponent<UnlimitedItem>() == null)
            {
                return false;
            }

            var rc = _itemTemp.transform as RectTransform;
            _tempOffsetMin = rc.offsetMin;
            _tempOffsetMax = rc.offsetMax;

            _getItemSizeFun = getItemSizeFun;

            //初始化GameObject缓存池
            _itemObjPool.Init(_cacheRoot, _itemTemp);

            return true;
        }

        public void ResetSize()
        {
            var scroll_rect = GetComponent<ScrollRect>();
            if (_direction == Direction.Horizontal)
            {
                _viewSize = scroll_rect.viewport.rect.size.x;
                _original_content_pos = _content.anchoredPosition.x;
            }
            else
            {
                _viewSize = scroll_rect.viewport.rect.size.y;
                _original_content_pos = _content.anchoredPosition.y;
            }

            var dataList = _dataList;

            ClearAll();

            SetItemDataList(dataList, true);
        }

        public void ClearAll()
        {
            _dataList = null;

            //回收之前的条目GameObject
            foreach (var item in _showItemList)
            {
                _itemObjPool.Recycle(item.gameObject);
            }
            _showItemList.Clear();

            _min_show_index = 0;
            _max_show_index = 0;
        }

        public void SetItemDataList(IList dataList, bool reset_position = false, bool fill_content = true)
        {
            ClearAll();

            _dataList = dataList;

            //计算滑动列表的总高度/宽度
            _contentSize = 0;
            for (int i = 0; i < _itemCount; i++)
            {
                if (i > 0)
                    _contentSize += _spacing;

                _contentSize += _getItemSizeFun(GetItemData(i));
            }

            if (fill_content)
            {
                if (reset_position)
                {
                    //重置content位置 
                    SetScrollPosition(_original_content_pos);
                }
                else
                {
                    SetScrollPosition(ContentPositon);
                }
            }

            if (_direction == Direction.Horizontal)
            {
                _content.sizeDelta = new Vector2(_contentSize, _content.sizeDelta.y);
            }
            else
            {
                _content.sizeDelta = new Vector2(_content.sizeDelta.x, _contentSize);
            }
        }

        public void SetScrollPosition(float pos)
        {
            //重置计算位置，保证在范围之内
            if (_direction == Direction.Horizontal)
            {
                if (_contentSize < _viewSize)
                {
                    pos = 0f;
                }
                else
                {
                    pos = Mathf.Clamp(pos, -(_contentSize - _viewSize), 0);
                }

                _content.anchoredPosition = new Vector2(pos, _content.anchoredPosition.y);
            }
            else
            {
                if (_contentSize < _viewSize)
                {
                    pos = 0f;
                }
                else
                {
                    pos = Mathf.Clamp(pos, 0, _contentSize - _viewSize);
                }

                _content.anchoredPosition = new Vector2(_content.anchoredPosition.x, pos);
            }

            float v_min_pos = -ContentPositon * _directionFactor;
            float v_max_pos = v_min_pos + _viewSize * _directionFactor;

            //回收之前的条目GameObject
            foreach (var item in _showItemList)
            {
                _itemObjPool.Recycle(item.gameObject);
            }
            _showItemList.Clear();

            //初始化视口范围内的条目
            float accum_position = 0;
            for (int i = 0; i < _itemCount; i++)
            {
                float item_size = _getItemSizeFun(GetItemData(i));

                if ((accum_position + item_size * _directionFactor) * _directionFactor > v_min_pos)
                {
                    if (accum_position * _directionFactor > v_max_pos * _directionFactor)
                    {
                        //如果已经超过视口的大小，就不再继续创建item
                        break;
                    }

                    var itemObj = _itemObjPool.Get();
                    AttachChild(itemObj);

                    var item = itemObj.GetComponent<UnlimitedItem>();
                    item.Init(i, GetItemData(i), this);
                    _showItemList.Add(item);
                    item.Position = accum_position;
                }

                accum_position += (item_size + _spacing) * _directionFactor;
            }

            _min_show_index = 0;
            _max_show_index = 0;

            if (_showItemList.Count > 0)
            {
                _min_show_index = _showItemList[0].Index;
                _max_show_index = _showItemList[_showItemList.Count - 1].Index;
            }

            _prePosition = ContentPositon;
        }

        private void AttachChild(GameObject itemObj)
        {
            itemObj.SetActive(true);
            itemObj.transform.SetParent(_content, false);
            itemObj.transform.localScale = _itemTemp.transform.localScale;
            RectTransform rc = itemObj.transform as RectTransform;
            rc.offsetMax = _tempOffsetMax;
            rc.offsetMin = _tempOffsetMin;
        }

        public void GoHead()
        {
            SetScrollPosition(0f);
        }

        public void GoTail()
        {
            float pos = Mathf.Max(_contentSize - _viewSize, 0f);
            SetScrollPosition(pos * _directionFactor * -1);
        }

        public void UpdateShow()
        {
            foreach (var item in _showItemList)
            {
                item.UpdateUI();
            }
        }

        public virtual void Update()
        {
            if (_dataList == null)
                return;

            if (_dataList.Count == 0)
                return;

            if (_showItemList.Count == 0)
            {
                SetScrollPosition(ContentPositon);
                return;
            }

            float content_pos = ContentPositon;
            if (content_pos * _directionFactor < _prePosition * _directionFactor)
            {
                //上翻, 左翻
                //if (_showItemList.Count>0)
                {
                    var min_item = _showItemList[0];
                    float in_page_pos = (content_pos + min_item.Position) * _directionFactor;
                    if (in_page_pos + _getItemSizeFun(min_item._baseItemData) < 0)
                    {
                        //去掉一条
                        min_item.Clear();
                        _itemObjPool.Recycle(min_item.gameObject);
                        _showItemList.RemoveAt(0);
                        _min_show_index++;
                    }
                }

                if (_max_show_index < _itemCount - 1)
                {
                    var max_item = _showItemList[_showItemList.Count - 1];
                    float in_page_pos = (content_pos + max_item.Position) * _directionFactor;
                    if (in_page_pos + _getItemSizeFun(max_item._baseItemData) < _viewSize)
                    {
                        //增加一条
                        var item_obj = _itemObjPool.Get();
                        var item = item_obj.GetComponent<UnlimitedItem>();
                        AttachChild(item_obj);

                        //item_obj.transform.SetParent(_content, false);
                        //item_obj.SetActive(true);
                        //item_obj.transform.localScale = _itemTemp.transform.localScale;
                        //RectTransform rc = item_obj.transform as RectTransform;
                        //rc.offsetMax = _tempOffsetMax;
                        //rc.offsetMin = _tempOffsetMin;

                        _max_show_index++;
                        item.Init(_max_show_index, GetItemData(_max_show_index), this);

                        //设置坐标
                        item.Position = max_item.Position + (_getItemSizeFun(max_item._baseItemData) + _spacing) * _directionFactor;

                        _showItemList.Add(item);
                    }
                }

                _prePosition = content_pos;
            }
            else if (content_pos * _directionFactor > _prePosition * _directionFactor)
            {
                //下翻， 右翻
                if (_showItemList.Count > 0)
                {
                    var max_item = _showItemList[_showItemList.Count - 1];
                    float in_page_pos = (content_pos + max_item.Position) * _directionFactor;
                    if (in_page_pos > _viewSize)
                    {
                        //去掉一条
                        max_item.Clear();
                        _itemObjPool.Recycle(max_item.gameObject);
                        _showItemList.RemoveAt(_showItemList.Count - 1);
                        _max_show_index--;
                    }
                }

                if (_min_show_index > 0)
                {
                    var min_item = _showItemList[0];
                    float in_page_pos = (content_pos + min_item.Position) * _directionFactor;
                    if (in_page_pos > 0)
                    {
                        //增加一条
                        var item_obj = _itemObjPool.Get();
                        var item = item_obj.GetComponent<UnlimitedItem>();
                        AttachChild(item_obj);

                        //item_obj.transform.SetParent(_content, false);
                        //item_obj.SetActive(true);
                        //item_obj.transform.localScale = _itemTemp.transform.localScale;
                        //RectTransform rc = item_obj.transform as RectTransform;
                        //rc.offsetMax = _tempOffsetMax;
                        //rc.offsetMin = _tempOffsetMin;

                        _min_show_index--;
                        item.Init(_min_show_index, GetItemData(_min_show_index), this);

                        //设置坐标
                        item.Position = min_item.Position - (_getItemSizeFun(item._baseItemData) + _spacing) * _directionFactor;

                        _showItemList.Insert(0, item);
                    }
                }

                _prePosition = content_pos;
            }
        }

        public UnlimitedItem GetLastItem()
        {
            UnlimitedItem item = null;
            if(_showItemList != null && _showItemList.Count > 0)
            {
                item = _showItemList[_showItemList.Count - 1];
            }
            return item;
        }
    }
}