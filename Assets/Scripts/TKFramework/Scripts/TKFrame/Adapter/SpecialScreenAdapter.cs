using System;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif
namespace TKFrame
{
    /// <summary>
    /// 特殊屏幕适配管理器
    /// </summary>
    public abstract class SpecialScreenAdapter : MonoBehaviour
    {
        /// <summary>
        /// DebugCanvas对象中模拟器容器节点（待移除）
        /// </summary>
        public const string node_screen_masks = "screen_masks";

        /// <summary>
        /// 业务层去实现判断刘海屏
        /// </summary>
        public static Func<string, SafeArea> GetPhoneSafeArea;

        /// <summary>
        /// 特殊屏幕机型定义
        /// </summary>
        public enum SpecialScreenType
        {
            Unknow = 0, //未知,会在获取的时候通过识别系统参数获取
            Normal = 1, //正常机型
            NotchPhone = 2, //刘海屏及2.1全面屏
        };

        /// <summary>
        /// 安全区域设定，目前设定了左右边距，可以动态扩展
        /// </summary>
        public struct SafeArea
        {
            public float top;
            public float bottom;
            public float left;
            public float right;
            public float offset;

            public bool isNotchPhone;

            public SafeArea(float top, float bottom, float left, float right)
            {
                this.top = top;
                this.bottom = bottom;
                this.left = left;
                this.right = right;
                this.isNotchPhone = false;
                this.offset = 0;
            }
        }

        public static string _deviceModel;

#if UNITY_EDITOR
        [UnityEditor.MenuItem("Tools/模拟屏幕测试/设置为IphoneX")]
        public static void SetIphoneX()
        {
            ShowDebugCanvasIphoneX(true);
            SetScreenType("iPhone11,8");
        }

        [UnityEditor.MenuItem("Tools/模拟屏幕测试/设置为普通屏幕")]
        public static void SetNormal()
        {
            ShowDebugCanvasIphoneX(false);
            SetScreenType("iPhone8");
        }

        public static void ShowDebugCanvasIphoneX(bool isShow)
        {
            Transform[] trs = TKFrameworkDelegateInterface.Get_Application_Root_Transform();
            if (trs != null)
            {
                //Transform[] trs = ZGame.App.Application.Root.GetComponentsInChildren<Transform>(true);
                foreach (Transform t in trs)
                {
                    if (t.name == "DebugCanvas")
                    {
                        t.gameObject.SetActive(isShow);
                    }
                    else if(t.name == "iPhone_X")
                    {
#if UNITY_EDITOR
                        RawImage rawImg = t.GetComponent<RawImage>();
                        if(rawImg != null)
                        {
                            rawImg.texture = AssetDatabase.LoadAssetAtPath<Texture>("Assets/Editor/iPhoneX/iPhoneX_screenLayout.png");
                        }
#endif
                    }
                }
            }
        }
#endif
        /// <summary>
        /// 设置指定的SpecialScreenType ， 会触发所有SpecialScreenAdapter刷新
        /// </summary>
        /// <param name="type"></param>
        public static void SetScreenType(string deviceModel)
        {
            _deviceModel = deviceModel;
            foreach (var item in FindObjectsOfType<SpecialScreenAdapter>())
            {
                item.ProcessSpecialScreen(deviceModel, Input.deviceOrientation);
            }
        }

        public DeviceOrientation _curDeviceOrientation;
        
        /// <summary>
        /// 在Awake的时候 如果是特殊屏幕则刷新
        /// </summary>
        protected void Awake()
        {
            if (string.IsNullOrEmpty(_deviceModel))
                _deviceModel = SystemInfo.deviceModel;

            //AdaptScreen(DeviceOrientationNotify.Instance.CurDeviceOrientation);
            if (DeviceOrientationNotify.Instance != null)
            {
                DeviceOrientationNotify.Instance.OnScreenAdapt += AdaptScreen;
            }
            ACGEventManager.Instance.AddEventListener("OnAndroidScreenSizeChange", OnAndroidScreenSizeChange);
        }

        protected void OnDestroy()
        {
            if (DeviceOrientationNotify.Instance != null)
            {
                DeviceOrientationNotify.Instance.OnScreenAdapt -= AdaptScreen;
            }
            ACGEventManager.Instance.RemoveEventListener("OnAndroidScreenSizeChange", OnAndroidScreenSizeChange);
        }

        protected void OnEnable()
        {
            if (DeviceOrientationNotify.Instance != null)
            {
                AdaptScreen(DeviceOrientationNotify.Instance.CurDeviceOrientation);
            }
        }

        public void AdaptScreen(DeviceOrientation Orientation)
        {
            ProcessSpecialScreen(_deviceModel, Orientation);
        }

        protected virtual void OnAndroidScreenSizeChange(GEvent e)
        {
            
        }

        static SafeArea s_defaultSafeArea = new SafeArea(0, 0, 0, 0);
        private void ProcessSpecialScreen(string deviceModel, DeviceOrientation orientation)
        {
#if UNITY_EDITOR
            orientation = DeviceOrientation.LandscapeLeft;
#endif
            SpecialScreenType screenType = SpecialScreenType.Normal;
          
            //查表
            SafeArea safeArea;
            if (GetPhoneSafeArea != null)
                safeArea = GetPhoneSafeArea(deviceModel);
            else
                safeArea = s_defaultSafeArea;

            //在表中
            if (safeArea.isNotchPhone)
            {
                screenType = SpecialScreenType.NotchPhone;
            }

            //只刘海一边留空
            _curDeviceOrientation = orientation;
            if (orientation == DeviceOrientation.LandscapeLeft)
            {
                safeArea.right = safeArea.offset;
            }
            else if (orientation == DeviceOrientation.LandscapeRight)
            {
                safeArea.left = safeArea.offset;
            }
            else
            {
                if (Screen.orientation == ScreenOrientation.LandscapeLeft)
                {
                    safeArea.right = safeArea.offset;
                }
                else if (Screen.orientation == ScreenOrientation.LandscapeRight)
                {
                    safeArea.left = safeArea.offset;
                }
            }

            ProcessSpecialScreen(screenType, safeArea);
        }

        /// <summary>
        /// 特殊屏幕的具体适配实现
        /// </summary>
        /// <param name="screenType"></param>
        public abstract void ProcessSpecialScreen(SpecialScreenType screenType,SafeArea safeArea);
    }
}
