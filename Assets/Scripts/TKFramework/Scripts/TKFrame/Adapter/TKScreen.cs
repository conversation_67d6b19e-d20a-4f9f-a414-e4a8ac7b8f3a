namespace TKFrame
{
    /// <summary>
    /// 记录当前屏幕的方向，为横屏或竖屏。
    /// </summary>
    public  static  class TKScreen
    {
        public enum TKScreenOrientation
        {
            Portrait,
            Landscape,
        };
        private static TKScreenOrientation _orientation = TKScreenOrientation.Landscape;
        public static TKScreenOrientation Orientation
        {
            get
            {
                return _orientation;
            }
            set
            {
                _orientation = value;
            }
        }


    }
}
