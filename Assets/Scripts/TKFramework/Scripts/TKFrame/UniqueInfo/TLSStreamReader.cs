using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using TKFrame;

namespace UniqueInfo
{
    public class TLSStreamReader
    {
        private ThreadLocal<FastBinaryReader> m_tlsReader = new ThreadLocal<FastBinaryReader>();
        private List<FastBinaryReader> m_binaryReaderList = new List<FastBinaryReader>();
        private object m_locker = new object();
        private string m_path;

        public void SetPath(string path)
        {
            m_path = path;
        }

        public FastBinaryReader GetReader()
        {
            if (string.IsNullOrEmpty(m_path))
                return null;
            
            FastBinaryReader br = m_tlsReader.Value;
            if (br == null || br.BaseStream == null || !br.BaseStream.CanRead)
            {
                FileManager fileManager = FileManager.Instance;
                fileManager.OpenMMap(m_path);
                Stream stream = fileManager.GetReadStream(m_path);
                CodingStream codingStream = new CodingStream(stream, CodingStream.CodingMethod.Decode);
                br = new FastBinaryReader(codingStream);
                m_tlsReader.Value = br;

                lock (m_locker)
                {
                    m_binaryReaderList.Add(br);
                }
            }

            return br;
        }

        public void Clear()
        {
            lock (m_locker)
            {
                foreach (FastBinaryReader br in m_binaryReaderList)
                {
                    br.Close();
                }
                m_binaryReaderList.Clear();
                
                FileManager.Instance.CloseMMap(m_path);
            }
        }
    }
}
