using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace TKFrame
{
    public sealed class TKDebugDisplay_Dictionary<K, V>
    {
        private IDictionary<K, V> dict; 
        
        public TKDebugDisplay_Dictionary(IDictionary<K, V> dictionary)
        {
            if (dictionary == null)
                throw new ArgumentNullException(nameof(dictionary));
 
            this.dict = dictionary;
        }
        
        [DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
        public KeyValuePair<K, V>[] Items   
        { 
            get {
                KeyValuePair<K, V>[] items = new KeyValuePair<K, V>[dict.Count];
                dict.CopyTo(items, 0);
                return items;
            }
        }
    }

    public sealed class TKDebugDisplay_TKArray<T>
    {
        private TKArray<T> array;
        
        public TKDebugDisplay_TKArray(TKArray<T> arr)
        {
            if (arr == null)
                throw new ArgumentNullException(nameof(arr));
 
            this.array = arr;
        }
        
        [DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
        public T[] Items   
        { 
            get 
            {
                T[] items = new T[array.Length];
                TKArray_Util.CopyTo(array, 0, items, 0, array.Length);
                return items;
            }
        }
    }
    
    public sealed class TKDebugDisplay_TKStack<T> 
    {
        private TKStack<T> stack; 
        
        public TKDebugDisplay_TKStack(TKStack<T> stack) 
        {
            if (stack == null) {
                throw new ArgumentNullException("stack");
            }
 
            this.stack = stack;
        }
       
        [DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
        public T[] Items   
        { 
            get 
            {
                return stack.ToArray();
            }
        }
    }            
}


