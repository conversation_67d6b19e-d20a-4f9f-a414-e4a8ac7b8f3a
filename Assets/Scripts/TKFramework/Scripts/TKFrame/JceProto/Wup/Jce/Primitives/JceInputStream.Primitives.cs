using System.IO;
using System;
using System.Collections.Generic;
using Wup;
using System.Text;
using System.Collections;
using ValueType = System.Single;
namespace Wup.Jce
{
    /**
     * 数据读取流
     * <AUTHOR>
     *
     */
    public partial class JceInputStream
    {

        public bool PrimitiveRead(bool b, int tag, bool isRequire)
        {
            byte c = Read((byte)0x0, tag, isRequire);
            return c != 0;
        }

        public char PrimitiveRead(char c, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        c = (char)0;
                        break;
                    case (byte)JceStructType.BYTE:
                        {
                            //BinaryReader br = new BinaryReader(ms);
                            c = (System.Char)br.ReadByte();
                            break;
                        }
                    case (byte)JceStructType.CHAR:
                        {
                            //BinaryReader br = new BinaryReader(ms);
                            br.Read(buf2, 0, 2);
                            c = (System.Char)ByteConverter.ReverseShort(buf2);
                            break;
                        }
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return c;
        }

        public byte PrimitiveRead(byte c, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        c = 0x0;
                        break;
                    case (byte)JceStructType.BYTE:
                        {
                            // BinaryReader br = new BinaryReader(ms);
                            c = br.ReadByte();
                            break;
                        }
                    default:
                        {
                            throw new JceDecodeException("type mismatch.");
                        }
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return c;
        }

        public short PrimitiveRead(short n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.BYTE:
                        {
                            //BinaryReader br = new BinaryReader(ms);
                            n = (System.SByte)br.ReadByte();
                            break;
                        }
                    case (byte)JceStructType.SHORT:
                        {
							//BinaryReader br = new BinaryReader(ms);
							br.Read(buf2, 0, 2);
							n = ByteConverter.ReverseShort(buf2);
                            break;
                        }
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }

        public ushort PrimitiveRead(ushort n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.BYTE:
                        {
                            n = br.ReadByte();
                            break;
                        }
                    case (byte)JceStructType.SHORT:
						{
							br.Read(buf2, 0, 2);
							n = ByteConverter.ReverseUShort(buf2);
                            break;
                        }
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }

        public int PrimitiveRead(int n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);


                //BinaryReader br = new BinaryReader(ms);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.BYTE:
                        n = (System.SByte)br.ReadByte();
                        break;
                    case (byte)JceStructType.SHORT:
						br.Read(buf2, 0, 2);
						n = ByteConverter.ReverseShort(buf2);
                        break;
                    case (byte)JceStructType.INT:
						br.Read(buf4, 0, 4);
						n = ByteConverter.ReverseInt32(buf4);
                        break;
                    case (byte)JceStructType.LONG:
						br.Read(buf4, 0, 4);
						n = (int)ByteConverter.ReverseInt32(buf4);
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }

        public uint PrimitiveRead(uint n, int tag, bool isRequire)
        {
            return (uint)PrimitiveRead((long)n, tag, isRequire);
            /*
            if (skipToTag(tag))
            {
				hd.clear();
                readHead(hd);

                //BinaryReader br = new BinaryReader(ms);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.BYTE:
                        n = br.ReadByte();
                        break;
                    case (byte)JceStructType.SHORT:
                        n = ByteConverter.ReverseEndian(br.ReadUInt16());
                        break;
                    case (byte)JceStructType.INT:
                        n = ByteConverter.ReverseEndian(br.ReadUInt32());
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
             */
        }

        public long PrimitiveRead(long n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.BYTE:
                        //using (BinaryReader br = new BinaryReader(ms))
                        {
                            n = (System.SByte)br.ReadByte();
                        }
                        break;
                    case (byte)JceStructType.SHORT:
						//using (BinaryReader br = new BinaryReader(ms))
						{
							br.Read(buf2, 0, 2);
							n = ByteConverter.ReverseShort(buf2);
                        }
                        break;
                    case (byte)JceStructType.INT:
						//using (BinaryReader br = new BinaryReader(ms))
						{
							br.Read(buf4, 0, 4);
							n = ByteConverter.ReverseInt32(buf4);
                        }
                        break;
                    case (byte)JceStructType.LONG:
						//using (BinaryReader br = new BinaryReader(ms))
						{
							br.Read(buf8, 0, 8);
							n = ByteConverter.ReverseLong(buf8);
                        }
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }

        public ulong PrimitiveRead(ulong n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.BYTE:
                        //using (BinaryReader br = new BinaryReader(ms))
                        {
                            n = br.ReadByte();
                        }
                        break;
                    case (byte)JceStructType.SHORT:
                        //using (BinaryReader br = new BinaryReader(ms))
                        {
							br.Read(buf2, 0, 2);
							n = ByteConverter.ReverseUShort(buf2);
                        }
                        break;
                    case (byte)JceStructType.INT:
						//using (BinaryReader br = new BinaryReader(ms))
						{
							br.Read(buf4, 0, 4);
							n = ByteConverter.ReverseUInt32(buf4);
                        }
                        break;
                    case (byte)JceStructType.LONG:
						//using (BinaryReader br = new BinaryReader(ms))
						{
							br.Read(buf8, 0, 8);
							n = ByteConverter.ReverseULong(buf8);
                        }
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }
        public float PrimitiveRead(float n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.FLOAT:
                        //using (BinaryReader br = new BinaryReader(ms))
                        {
                            //n = br.ReadSingle();
                            br.Read(buf4, 0, 4);
                            n = ByteConverter.ReverseFloat(buf4);
                        }
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }

        public double PrimitiveRead(double n, int tag, bool isRequire)
        {
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.ZERO_TAG:
                        n = 0;
                        break;
                    case (byte)JceStructType.FLOAT:
                        //using (BinaryReader br = new BinaryReader(ms))
                        {
                            //n = br.ReadSingle();
                            br.Read(buf4, 0, 4);
                            n = ByteConverter.ReverseFloat(buf4);
                        }
                        break;
                    case (byte)JceStructType.DOUBLE:
                        //using (BinaryReader br = new BinaryReader(ms))
                        {
                            //n = br.ReadDouble();
                            br.Read(buf8, 0, 8);
                            n = ByteConverter.ReverseDouble(buf8);
                        }
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return n;
        }



        public string PrimitiveRead(string s,int tag, bool isRequire)
        {
            s = null;
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.STRING1:
                        {
                            s = readString1();
                        }
                        break;
                    case (byte)JceStructType.STRING4:
                        {
                            s = readString4();
                        }
                        break;
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return s;
        }


        public byte[] PrimitiveRead(byte[] l, int tag, bool isRequire)
        {
            byte[] lr = null;
            if (skipToTag(tag))
            {
                HeadData hd = new HeadData();
                readHead(ref hd);
                switch (hd.type)
                {
                    case (byte)JceStructType.SIMPLE_LIST:
                        {
                            HeadData hh = new HeadData();
                            readHead(ref hh);
                            if (hh.type != (byte)JceStructType.BYTE)
                            {
                                throw new JceDecodeException("type mismatch, tag: " + tag + ", type: " + hd.type + ", " + hh.type);
                            }

                            int size = Read(0, 0, true);
                            if (size < 0)
                            {
                                throw new JceDecodeException("invalid size, tag: " + tag + ", type: " + hd.type + ", " + hh.type + ", size: " + size);
                            }

                            //lr = new byte[size];


                            try
                            {

                                //BinaryReader br = new BinaryReader(ms);
                                lr = br.ReadBytes(size);
                            }
                            catch (Exception e)
                            {
                                QTrace.Trace(e.Message);
                                return null;
                            }
                            break;
                        }
                    case (byte)JceStructType.LIST:
                        {
                            int size = Read(0, 0, true);
                            if (size < 0)
                                throw new JceDecodeException("size invalid: " + size);
                            lr = new byte[size];
                            for (int i = 0; i < size; ++i)
                                lr[i] = Read(lr[0], 0, true);
                            break;
                        }
                    default:
                        throw new JceDecodeException("type mismatch.");
                }
            }
            else if (isRequire)
            {
                throw new JceDecodeException("require field not exist.");
            }
            return lr;
        }

        public List<byte> PrimitiveRead(List<byte> list, int tag, bool isReuqire)
        {
            //return (List<ValueType>)Read((object)(new List<ValueType>()),tag, isReuqire);
            if (skipToTag(tag))
            {
				HeadData hd = new HeadData();
				HeadData hh = new HeadData();
				readHead(ref hd);
                readHead(ref hh);
                if (hh.type == (byte)JceStructType.ZERO_TAG)
                {
                    return new List<byte>();
                }
                int size = Read(0, 0, true);
                if (size < 0)
                {
                    throw new JceDecodeException("size invalid: " + size);
                }
                list = new List<byte>(size);
                byte[] lrtmp = br.ReadBytes(size);
                list.AddRange(lrtmp);

            }
            return list;
        }

    }
}