using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using TKFrame;
using Wup.Jce;

//namespace Wup.Jce
//{
public class JceUtil
    {

        /**
         * Constant to use in building the hashCode.
         */
        private static int iConstant = 37;

        /**
         * Running total of the hashCode.
         */
        private static int iTotal = 17;
        
        #region Equals

        public static bool Equals(bool l, bool r)
        {
            return l == r;
        }

        public static bool Equals(byte l, byte r)
        {
            return l == r;
        }

        public static bool Equals(char l, char r)
        {
            return l == r;
        }

        public static bool Equals(short l, short r)
        {
            return l == r;
        }

        public static bool Equals(int l, int r)
        {
            return l == r;
        }

        public static bool Equals(long l, long r)
        {
            return l == r;
        }

        public static bool Equals(float l, float r)
        {
            return l == r;
        }

        public static bool Equals(double l, double r)
        {
            return l == r;
        }

        public static new bool Equals(object l, object r)
        {
            return l.Equals(r);
        }
        
        #endregion
        
        #region compareTo

        public static int compareTo(bool l, bool r)
        {
            return (l ? 1 : 0) - (r ? 1 : 0);
        }

        public static int compareTo(byte l, byte r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo(char l, char r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo(short l, short r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo(int l, int r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo(long l, long r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo(float l, float r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo(double l, double r)
        {
            return l < r ? -1 : (l > r ? 1 : 0);
        }

        public static int compareTo<T>(T l, T r) where T : IComparable
        {
            return l.CompareTo(r);
        }

        public static int compareTo<T>(List<T> l, List<T> r) where T : IComparable
        {
            int n = 0;
            for (int i = 0, j = 0; i < l.Count && j < r.Count; i++, j++)
            {
                if (l[i] is IComparable && r[j] is IComparable)
                {
                    IComparable lc = (IComparable)l[i];
                    IComparable rc = (IComparable)r[j];
                    n = lc.CompareTo(rc);
                    if (n != 0)
                    {
                        return n;
                    }
                }
                else
                {
                    throw new Exception("Argument must be IComparable!");
                }
            }

            return compareTo(l.Count, r.Count);
        }

        public static int compareTo<T>(T[] l, T[] r) where T : IComparable
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = l[li].CompareTo(r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(bool[] l, bool[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(byte[] l, byte[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(char[] l, char[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(short[] l, short[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(int[] l, int[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(long[] l, long[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(float[] l, float[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }

        public static int compareTo(double[] l, double[] r)
        {
            for (int li = 0, ri = 0; li < l.Length && ri < r.Length; ++li, ++ri)
            {
                int n = compareTo(l[li], r[ri]);
                if (n != 0)
                    return n;
            }
            return compareTo(l.Length, r.Length);
        }
        
        #endregion
        
        #region hashCode

        public static int hashCode(bool o)
        {
            return iTotal * iConstant + (o ? 0 : 1);
        }

        public static int hashCode(bool[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + (array[i] ? 0 : 1);
                }
                return tempTotal;
            }
        }

        public static int hashCode(byte o)
        {
            return iTotal * iConstant + o;
        }

        public static int hashCode(byte[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + array[i];
                }
                return tempTotal;
            }
        }

        public static int hashCode(char o)
        {
            return iTotal * iConstant + o;
        }

        public static int hashCode(char[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + array[i];
                }
                return tempTotal;
            }
        }

        public static int hashCode(double o)
        {
            return hashCode(Convert.ToInt64(o));
        }

        public static int hashCode(double[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + ((int)(Convert.ToInt64(array[i]) ^ (Convert.ToInt64(array[i]) >> 32)));
                }
                return tempTotal;
            }
        }

        public static int hashCode(float o)
        {
            return iTotal * iConstant + Convert.ToInt32(o);
        }

        public static int hashCode(float[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + Convert.ToInt32(array[i]);
                }
                return tempTotal;
            }
        }

        public static int hashCode(short o)
        {
            return iTotal * iConstant + o;
        }

        public static int hashCode(short[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + array[i];
                }
                return tempTotal;
            }
        }


        public static int hashCode(int o)
        {
            return iTotal * iConstant + o;
        }

        public static int hashCode(int[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + array[i];
                }
                return tempTotal;
            }
        }

        public static int hashCode(long o)
        {
            return iTotal * iConstant + ((int)(o ^ (o >> 32)));
        }

        public static int hashCode(long[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + ((int)(array[i] ^ (array[i] >> 32)));
                }
                return tempTotal;
            }
        }

        public static int hashCode(JceStruct[] array)
        {
            if (array == null)
            {
                return iTotal * iConstant;
            }
            else
            {
                int tempTotal = iTotal;
                for (int i = 0; i < array.Length; i++)
                {
                    tempTotal = tempTotal * iConstant + (array[i].GetHashCode());
                }
                return tempTotal;
            }
        }


        public static int hashCode(object obj)
        {
            if (null == obj)
            {
                return iTotal * iConstant;
            }
            else
            {
                if (obj.GetType().IsArray)
                {
                    if (obj is long[])
                    {
                        return hashCode((long[])obj);
                    }
                    else if (obj is int[])
                    {
                        return hashCode((int[])obj);
                    }
                    else if (obj is short[])
                    {
                        return hashCode((short[])obj);
                    }
                    else if (obj is char[])
                    {
                        return hashCode((char[])obj);
                    }
                    else if (obj is byte[])
                    {
                        return hashCode((byte[])obj);
                    }
                    else if (obj is double[])
                    {
                        return hashCode((double[])obj);
                    }
                    else if (obj is float[])
                    {
                        return hashCode((float[])obj);
                    }
                    else if (obj is bool[])
                    {
                        return hashCode((bool[])obj);
                    }
                    else if (obj is JceStruct[])
                    {
                        return hashCode((JceStruct[])obj);
                    }
                    else
                    {
                        return hashCode((Object[])obj);
                    }
                }
                else if (obj is JceStruct)
                {
                    return obj.GetHashCode();
                }
                else
                {
                    return iTotal * iConstant + obj.GetHashCode();
                }
            }
        }
        
        #endregion

        #region DeepClone

        public static MemoryStream DeepClone(MemoryStream oldMs)
        {
            oldMs.Position = 0;
            int len = (int)oldMs.Length;
            MemoryStream newMs = new MemoryStream(len);
            CopyStream(oldMs, newMs, len);
            return newMs;
        }
        
        public static byte[] DeepClone(byte[] arr)
        {
            if (arr == null)
                return null;

            byte[] bytes = new byte[arr.Length];
            Array.Copy(arr, 0, bytes, 0, bytes.Length);
            return bytes;
        }

        public static JceStruct DeepClone(JceStruct prototype)
        {
            if (prototype == null)
                return null;

            return prototype.DeepClone();
        }
        
        #region Dictionary
        
        public static Dictionary<int, int> DeepClone(Dictionary<int, int> datas)
        {
            if (datas == null)
                return null;

            Dictionary<int, int> copyDatas = new Dictionary<int, int>(datas.Count);
            foreach (var pair in datas)
            {
                copyDatas.Add(pair.Key, pair.Value);
            }
            return copyDatas;
        }
        
        public static Dictionary<int, Dictionary<int, int>> DeepClone(Dictionary<int, Dictionary<int, int>> datas)
        {
            if (datas == null)
                return null;
                        
            Dictionary<int, Dictionary<int, int>> copyDatas = new Dictionary<int, Dictionary<int, int>>(datas.Count);
            foreach (var pair in datas)
            {
                Dictionary<int, int> copyData = DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }
            return copyDatas;
        }

        public static Dictionary<int, List<int>> DeepClone(Dictionary<int, List<int>> datas)
        {
            if (datas == null)
                return null;

            Dictionary<int, List<int>> copyDatas = new Dictionary<int, List<int>>(datas.Count);
            foreach (var pair in datas)
            {
                List<int> copyData = DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }

            return copyDatas;
        }
        
        public static Dictionary<int, List<T>> DeepClone<T>(Dictionary<int, List<T>> datas)  where T : JceStruct
        {
            if (datas == null)
                return null;

            Dictionary<int, List<T>> copyDatas = new Dictionary<int, List<T>>(datas.Count);
            foreach (var pair in datas)
            {
                List<T> copyData = DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }

            return copyDatas;
        }

        public static Dictionary<int, T> DeepClone<T>(Dictionary<int, T> datas)  where T : JceStruct
        {
            if (datas == null)
                return null;

            Dictionary<int, T> copyDatas = new Dictionary<int, T>(datas.Count);
            foreach (var pair in datas)
            {
                T copyData = (T)DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }
            return copyDatas;
        }
        
        #endregion
        
        #region TKDictionary
        
        public static TKDictionary<int, int> DeepClone(TKDictionary<int, int> datas)
        {
            if (datas == null)
                return null;

            TKDictionary<int, int> copyDatas = new TKDictionary<int, int>(datas.Count);
            foreach (var pair in datas)
            {
                copyDatas.Add(pair.Key, pair.Value);
            }
            return copyDatas;
        }
        
        public static TKDictionary<int, TKDictionary<int, int>> DeepClone(TKDictionary<int, TKDictionary<int, int>> datas)
        {
            if (datas == null)
                return null;
                        
            TKDictionary<int, TKDictionary<int, int>> copyDatas = new TKDictionary<int, TKDictionary<int, int>>(datas.Count);
            foreach (var pair in datas)
            {
                TKDictionary<int, int> copyData = DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }
            return copyDatas;
        }

        public static TKDictionary<int, List<int>> DeepClone(TKDictionary<int, List<int>> datas)
        {
            if (datas == null)
                return null;

            TKDictionary<int, List<int>> copyDatas = new TKDictionary<int, List<int>>(datas.Count);
            foreach (var pair in datas)
            {
                List<int> copyData = DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }

            return copyDatas;
        }
        
        public static TKDictionary<int, List<T>> DeepClone<T>(TKDictionary<int, List<T>> datas)  where T : JceStruct
        {
            if (datas == null)
                return null;

            TKDictionary<int, List<T>> copyDatas = new TKDictionary<int, List<T>>(datas.Count);
            foreach (var pair in datas)
            {
                List<T> copyData = DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }

            return copyDatas;
        }

        public static TKDictionary<int, T> DeepClone<T>(TKDictionary<int, T> datas)  where T : JceStruct
        {
            if (datas == null)
                return null;

            TKDictionary<int, T> copyDatas = new TKDictionary<int, T>(datas.Count);
            foreach (var pair in datas)
            {
                T copyData = (T)DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }
            return copyDatas;
        }
        
        public static TKDictionary<long, T> DeepClone<T>(TKDictionary<long, T> datas)  where T : JceStruct
        {
            if (datas == null)
                return null;

            TKDictionary<long, T> copyDatas = new TKDictionary<long, T>(datas.Count);
            foreach (var pair in datas)
            {
                T copyData = (T)DeepClone(pair.Value);
                copyDatas.Add(pair.Key, copyData);
            }
            return copyDatas;
        }
        
        #endregion
        
        #region List

        public static List<int> DeepClone(List<int> datas)
        {
            if (datas == null)
                return null;
            
            List<int> copyDatas = new List<int>(datas);
            return copyDatas;
        }

        public static List<List<int>> DeepClone(List<List<int>> datas)
        {
            if (datas == null)
                return null;

            List<List<int>> copyDatas = new List<List<int>>(datas.Count);
            foreach (List<int> data in datas)
            {
                List<int> copyData = DeepClone(data);
                copyDatas.Add(copyData);
            }

            return copyDatas;
        }
        
        public static List<T> DeepClone<T>(List<T> datas) where T : JceStruct
        {
            if (datas == null)
                return null;
            
            List<T> copyDatas = new List<T>(datas.Count);
            foreach (T data in datas)
            {
                T copyData = (T)DeepClone(data);
                copyDatas.Add(copyData);
            }
            return copyDatas;
        }
        
        #endregion

        public static object DeepClone(object prototype)
        {
            if (prototype == null)
            {
                return null;
            }
            var type = prototype.GetType();

            if (prototype is byte[])
            {
                var oldData = (byte[])prototype;
                var data = new byte[oldData.Length];
                Array.Copy(oldData, data, oldData.Length);
                return data;
            }
            else if (prototype is System.Collections.IList)
            {
                var o = (System.Collections.IList)Activator.CreateInstance(type);
                foreach (var v in (System.Collections.IList)prototype)
                {
                    o.Add(DeepClone(v));
                }
                return o;
            }
            else if (prototype is TKDictionary<int, int>)
            {
                TKDictionary<int, int> data = prototype as TKDictionary<int, int>;
                return DeepClone(data);
            }
            else if (prototype is TKDictionary<int, TKDictionary<int, int>>)
            {
                TKDictionary<int, TKDictionary<int, int>> data = prototype as TKDictionary<int, TKDictionary<int, int>>;
                TKDictionary<int, TKDictionary<int, int>> o = new TKDictionary<int, TKDictionary<int, int>>(data.Count);
                foreach (var pair in data)
                {
                    o.Add(pair.Key, DeepClone(pair.Value));
                }
                return o;
            }
            else if (prototype is Dictionary<int, int>)
            {
                Dictionary<int, int> data = prototype as Dictionary<int, int>;
                return DeepClone(data);
            }
            else if (prototype is Dictionary<int, Dictionary<int, int>>)
            {
                Dictionary<int, Dictionary<int, int>> data = prototype as Dictionary<int, Dictionary<int, int>>;
                Dictionary<int, Dictionary<int, int>> o = new Dictionary<int, Dictionary<int, int>>(data.Count);
                foreach (var pair in data)
                {
                    o.Add(pair.Key, DeepClone(pair.Value));
                }
                return o;
            }
            else if (prototype is System.Collections.IDictionary)
            {
                var o = (System.Collections.IDictionary)Activator.CreateInstance(type);
                var d = (System.Collections.IDictionary)prototype;
                var e = d.GetEnumerator();
                while (e.MoveNext())
                {
                    o[DeepClone(e.Key)] = DeepClone(e.Value);
                }
                return o;
            }
            else if (prototype is JceStruct)
            {
                return ((JceStruct)prototype).DeepClone();
            }
            else
            {
                return prototype;
            }
        }
        
        #endregion
        
        #region Buffer
        
        private static ThreadLocal<byte[]> bufferTLS = new ThreadLocal<byte[]>();

        public static byte[] GetBuffer(int size)
        {
            return GetBuffer(bufferTLS, size);
        }
        
        private static ThreadLocal<char[]> charBufferTLS = new ThreadLocal<char[]>();

        public static char[] GetCharBuffer(int size)
        {
            return GetBuffer(charBufferTLS, size);
        }

        private static T[] GetBuffer<T>(ThreadLocal<T[]> bufferTLS, int size) where T : unmanaged
        {
            T[] buffer = bufferTLS.Value;
            if (buffer == null)
            {
                buffer = new T[512];
                bufferTLS.Value = buffer;
            }

            int currentLen = buffer.Length;
            if (currentLen < size)
            {
                while (size > currentLen)
                {
                    currentLen *= 2;
                }

                buffer = new T[currentLen];
                bufferTLS.Value = buffer;
            }
            
            return buffer;
        }
        
        #endregion

        public static void CopyStream(Stream src, Stream dst, int len)
        {
            if (src == null || dst == null || len == 0)
                return;

            int buffLen = 512;
            byte[] buff = GetBuffer(buffLen);
            
            do
            {
                int readableCount = Math.Min(len, buffLen);
                int readCount = src.Read(buff, 0, readableCount);
                if (readCount == 0)
                    break;
                dst.Write(buff, 0, readCount);
                len -= readCount;
            } while (len > 0);
        }
        
        public static byte[] getJceBufArray(MemoryStream ms)
        {
            byte[] bytes = new byte[ms.Position];
            Array.Copy(ms.GetBuffer(), 0, bytes, 0, bytes.Length);
            return bytes;
        }

    }
//}