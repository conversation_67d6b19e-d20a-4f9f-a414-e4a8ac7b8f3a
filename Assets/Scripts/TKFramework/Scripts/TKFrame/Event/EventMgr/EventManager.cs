using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using TKFrame;

/// <summary>
/// 服务器需求可以使用客户端代码进行每场战斗的验证。
/// 为了防止每场战斗间的事件干扰，这里将事件管理做成了实例的方式;
/// 为了实现多线程跑逻辑代码，需要显示事件池和 逻辑事件池分离
/// 使用方式如:
/// 局内
///  MicroMgr.Instance.eventMgr.xxxx 局内渲染层的使用 
/// 自身是 worldunit ，使用 World.eventMgr/// 
/// 代码处在逻辑层中 ，但是发送的事件是显示相关的代码 那就需要 使用ViewCommonEventManager.Send 
/// 局外，纯显示层 ACGEventManager.Instance
/// TODO 大量的局外设置要改
/// </summary>
public class ACGEventManager
{
    class ACGEventCallBack : ObjectPoolTool.IPoolItem
    {
        public string type;
        public EventCallBack fn;
        //释放
        public void Release()
        {
            Reset();
        }

        public void Reset()
        {
            type = "";
            fn = null;
        }
    }


    private static ACGEventManager _Instance;
    public static ACGEventManager Instance
    {
        get
        {
            if (_Instance == null)
                _Instance = new ACGEventManager();
            return _Instance;
        }
    }

    public delegate void EventCallBack(GEvent e);
    private ObjectPool _pool = new ObjectPool(typeof(GEvent), null, 120, 1000);
    private ObjectPool _callBackPool = new ObjectPool(typeof(ACGEventCallBack), null, 100, 500);

    //回调列表字典
    //private TKDictionary<string, List<EventCallBack>> _callbackDict = new TKDictionary<string, List<EventCallBack>>();
    private TKDictionary<int, List<EventCallBack>> _callbackDict = new TKDictionary<int, List<EventCallBack>>();

    private int defaultNum = -9999;

    //待移除的回调列表
    private TKDictionary<int, List<ACGEventCallBack>> _waitRemoveEventDict = new TKDictionary<int, List<ACGEventCallBack>>();
    //当前事件状态
    private TKDictionary<int, bool> _eventStatusDict = new TKDictionary<int, bool>();

#if !JK_RELEASE
    public bool m_IsRecordEvent = false;
    private TKDictionary<string, int> m_DebugCallBackCount = new TKDictionary<string, int>();
    public void ClearDebugCallBackCount()
    {
        m_DebugCallBackCount.Clear();
    }
    public void DebugCallBack(string type)
    {
        if (m_DebugCallBackCount.Count > 0)
        {
            foreach (var item in m_DebugCallBackCount)
                TKFrame.Diagnostic.Error("ACGEventManager MayBe Has InGameEvt Not Remove " + type + " :" + item.Key + " Count:" + item.Value);
            //故意的
            //List<int> a = new List<int>();
            //int b = a[a.Count];
        }
        else
            TKFrame.Diagnostic.Log("ACGEventManager InGameEvt All Clear " + type);
    }
    private void AddDebugCallBack(string evt)
    {
        if (!m_IsRecordEvent)
            return;
        if (m_DebugCallBackCount.ContainsKey(evt))
            m_DebugCallBackCount[evt]++;
        else
            m_DebugCallBackCount.Add(evt, 1);
    }
    private void RemoveDebugCallBack(string evt, bool isAll)
    {
        if (!m_IsRecordEvent)
            return;
        if (isAll)
        {
            m_DebugCallBackCount.Remove(evt);
        }
        else
        {
            int cnt = 0;
            if (m_DebugCallBackCount.TryGetValue(evt, out cnt))
            {
                if (cnt > 0)
                {
                    m_DebugCallBackCount[evt]--;
                    cnt--;
                }
                if (cnt <= 0)
                    m_DebugCallBackCount.Remove(evt);
            }
        }
    }
#endif

    /// <summary>
    /// 派发事件
    /// </summary>
    /// <param name="type"></param>
    public void Send(string type)
    {
        SendEvent(CreateEvent(type));
    }

    /// <summary>
    /// 派发事件 object
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public void Send(string type, object value)
    {
        SendEvent(CreateEvent(type, value));
    }

    /// <summary>
    /// 派发事件 UnityEngine.Object
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>

#if !ACGGAME_CLIENT || LOGIC_THREAD
    public void Send(string type, UnityEngine4Server.Object value)
#else
    public void Send(string type, UnityEngine.Object value)
#endif
    {
        SendEvent(CreateEvent(type, value));
    }


    /// <summary>
    /// 派发事件 UnityEngine.Object
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
#if LOGIC_THREAD
    public void Send(string type, UnityEngine.Object value)
    {
        SendEvent(CreateEvent(type, value));
    }
#endif

    /// <summary>
    /// 派发事件 int
    /// </summary>
    /// <param name="type">事件名</param>
    public void Send(string type, int value)
    {
        SendEvent(CreateEvent(type, null, value));
    }

    /// <summary>
    /// 派发事件 bool
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public void Send(string type, bool value)
    {
        SendEvent(CreateEvent(type, null, defaultNum, value));
    }
    /// <summary>
    /// lua调用方法 方法
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public void SendBool(string type, bool value)
    {
        SendEvent(CreateEvent(type, null, defaultNum, value));
    }
    /// <summary>
    /// 派发事件 float
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public void Send(string type, float value)
    {
        SendEvent(CreateEvent(type, null, defaultNum, false, value));
    }

    /// <summary>
    /// 派发事件 ulong
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public void Send(string type, ulong value)
    {
        SendEvent(CreateEvent(type, null, defaultNum, false, 0, value));
    }

    /// <summary>
    /// 派发事件 ulong
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public void Send(string type, uint value)
    {
        SendEvent(CreateEvent(type, null, defaultNum, false, 0, 0, value));
    }

    public void Send(GEvent evt)
    {
        SendEvent(evt);
    }

    /// <summary>
    /// CreateEvent
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value1">object</param>
    /// <param name="value2">value2</param>
    /// <param name="value3">bool</param>
    /// <param name="value4">float</param>
    /// <returns></returns>
    public GEvent CreateEvent(string type, object value1 = null, int value2 = -99999,
        bool value3 = false, float value4 = 0f, ulong value5 = 0, uint value6 = 0)
    {
        GEvent evt = _pool.GetObject() as GEvent;
        evt.type = type;
        evt.objData = value1;
        evt.intData = value2;
        evt.boolData = value3;
        evt.floatData = value4;
        evt.ulongData = value5;
        evt.uintData = value6;
        return evt;
    }


    //添加监听事件
    public void AddEventListener(string type, EventCallBack fn)
    {
        var key = type.GetHashCode();
        List<EventCallBack> fnList = null;

        if (!_callbackDict.TryGetValue(key, out fnList))
        {
            fnList = new List<EventCallBack>(4);
            _callbackDict.Add(key, fnList);

            //if(!keyDict.ContainsKey(key)) keyDict.Add(key, type);
        }
        else if (fnList.Contains(fn))
        {
            return;
        }
        fnList.Add(fn);
#if !JK_RELEASE
        AddDebugCallBack(type);
#endif
    }

    //移除监听事件
    public void RemoveEventListener(string type, EventCallBack fn)
    {
        var key = type.GetHashCode();
        bool isRunning = false;
        if (_eventStatusDict.TryGetValue(key, out isRunning))
        {
            if (isRunning)
            {   //如果正在执行中，移除回调，对SendEvent中的for循环会有隐患。改为执行完回调后，在统一从等待队列中进行移除
                AddToWaitList(key, type,fn);

                //List<EventCallBack> fnLists = null;
                //if (_callbackDict.TryGetValue(key, out fnLists))
                //{
                //    Log.InfoError(string.Format("运行中被移除事件 eventtype:{0},count:{1}", type,fnLists.Count));
                //}
                return;
            }
        }

        //var key = type;
        List<EventCallBack> fnList = null;
        if (_callbackDict.TryGetValue(key, out fnList))
        {
            fnList.Remove(fn);
            if (fnList.Count <= 0)
            {
                _callbackDict.Remove(key);
            }
        }
#if !JK_RELEASE
        RemoveDebugCallBack(type, false);
#endif
    }

    /// <summary>
    /// 移除整个类型的监听事件
    /// !!!!!谨慎使用!!!!!
    /// </summary>
    public void RemoveEventListenerByType(string type)
    {
        var key = type.GetHashCode();
        _callbackDict.Remove(key);
#if !JK_RELEASE
        RemoveDebugCallBack(type, true);
#endif
    }

    //派发事件
    private void SendEvent(GEvent evt)
    {
        if (evt == null)
        {
            TKFrame.Diagnostic.Error("EventManager CreateEvent is null!!,请检查业务逻辑，避免大量执行Event！，池子不够用了。");
            return;
        }

        var key = evt.type.GetHashCode();

        //设置该类型事件运行标记
        if (!_eventStatusDict.ContainsKey(key))
        {
            _eventStatusDict[key] = false;
        }
        _eventStatusDict[key] = true;

        //var key = evt.type;
        List<EventCallBack> fnList = null;
        if (_callbackDict.TryGetValue(key, out fnList))
        {
            for (int i = fnList.Count - 1; i > -1; i--)
            {
                bool isForRun = true;
                var fn = fnList[i];

                List<ACGEventCallBack> waitList = null;
                if (_waitRemoveEventDict.TryGetValue(key, out waitList))
                {
                    for (int j = 0; j < waitList.Count; j++)
                    {
                        if (fn == waitList[j].fn)
                        {
                            TKFrame.Diagnostic.Error(string.Format("要执行的回调，已被加到移除列表，跳过不执行。请检查业务逻辑，一般只移除自身事件。type:{0}", waitList[j].type));
                            isForRun = false;
                            break;
                        }
                    }
                }

                if (!isForRun)
                {
                    continue;
                }

#if !ACGGAME_AUTOFIGHT
                try
                {
                    fn(evt);
                }
                catch (Exception ex)
                {
                    TKFrame.Diagnostic.Error(string.Format("EventManager SendEvent Exception {0}, {1}", evt.type, ex.ToString()));
                }
#else
                fn(evt);
#endif
            }
        }
        _pool.FreeObject(evt.GetHashCode());//扔回对象池
        _eventStatusDict[key] = false;

        //执行
        List<ACGEventCallBack> list = null;
        if (_waitRemoveEventDict.TryGetValue(key, out list))
        {
            for (int i = 0; i < list.Count; i++)
            {
                ACGEventCallBack cb = list[i];
                //扔回对象池
                RemoveEventListener(cb.type, cb.fn);
                _callBackPool.FreeObject(cb.GetHashCode());
                //Log.Error(string.Format("RemoveEventListener {0}", list[i].type));
                
            }
            //Log.Error(string.Format("-----------------  Clear WaitList type:{0}", evt.type));
            list.Clear();
        }
    }

    /// <summary>
    /// 添加到等待移除队列
    /// </summary>
    /// <param name="key"></param>
    /// <param name="cb"></param>
    private void AddToWaitList( int key , string type, EventCallBack fn)
    {
        List<ACGEventCallBack> result = null;
        if (!_waitRemoveEventDict.TryGetValue(key, out result))
        {
            result = new List<ACGEventCallBack>();
            _waitRemoveEventDict.Add(key, result);
        }

        ACGEventCallBack cb = _callBackPool.GetObject() as ACGEventCallBack;
        cb.type = type;
        cb.fn = fn;
        result.Add(cb);
    }
}
