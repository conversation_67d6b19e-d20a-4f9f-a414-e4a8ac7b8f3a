using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using UnityEditor;
using UnityEngine;

public class CheckUpdateResTool
{
    public class SubmitInfo
    {
        public int rev;
        public string author;
        public string date;
        public string msg;
        public List<string> files = new List<string>();
        public List<string> abPath = new List<string>();//需要计算的AB路径
        public List<FileInfo> directlyCalSize = new List<FileInfo>();//可直接计算大小 比如视频 音频
        public long allSize = 0;//该次提交影响的资源大小
    }

    private static int m_DiffSvn = -1;//从哪个svn之后开始Diff
    private static string m_DiffSvnType = "";
    private static string m_Svn = string.Empty;
    private static string m_SvnInfoSplit = string.Empty;
    private static List<string> m_ExceptionInfo = new List<string>();
    private static List<SubmitInfo> m_VecSubmitInfo = new List<SubmitInfo>();
    private static List<string> m_AlreadyHandlePath = new List<string>();
    private static Dictionary<string, HashSet<string>> m_AllAntiDependencies { get { return ReferenceFinder.data.antiDependenciesNonCached; } }

    private static readonly string m_CurWorkspace = "D:/JsonluanUse/p-b038dfc3e71f42caa0d2ec562dc78302/CheckUpdateRes/";//"G:/CheckUpdateRes/";
    private static readonly string m_CommonSvn = "http://tc-svn.tencent.com/ied/ied_tiki_rep/Z_Game_proj/release/JK_Client_ReleaseCommon/ZGame_Chess_Prj/Assets";
    private static readonly string m_OnlineSvn = "http://tc-svn.tencent.com/ied/ied_tiki_rep/Z_Game_proj/branches/JK_Client_ReleaseOnline/ZGame_Chess_Prj/Assets";
    private static readonly string m_SvnInfoXml = "svninfo.xml";
    private static readonly List<string> m_PathWhiteList = new List<string>() 
    {
        "Assets/Art_TFT_Raw/cfg",
        "Assets/Editor",
        "Assets/Scripts",
        "Assets/TKFramework",
        "Assets/XLua",
        "Assets/Resources",
        "Assets/ChessBattle",
        "Assets/ChessBattleData",
    };

    [MenuItem("InjectFix/测试依赖", false, 1)]
    public static void Find()
    {
        Debug.LogError("SetCfg Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        SetCfg();

        Debug.LogError("UpdateAssetsTemp Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        ReferenceFinderData.IsFromCheckUpdateResTool = true;
        ReferenceFinder.data.UpdateAssetsTemp();

        Debug.LogError("SetSvnType Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        SetSvnType();

        Debug.LogError("GenSvnSubmitInfoList Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        GenSvnSubmitInfoList();

        Debug.LogError("FindFileAssetBundle Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        FindFileAssetBundle();

        Debug.LogError("CalAssetBundleSize Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        CalAssetBundleSize();

        ReferenceFinderData.IsFromCheckUpdateResTool = false;
    }

    private static void SetCfg()
    {
        StreamReader sr = null;
        try
        {
            sr = new StreamReader(m_CurWorkspace + "cfg.txt");
            string line = sr.ReadLine();
            if (string.IsNullOrEmpty(line))
            {
                Debug.LogError("SetCfg Content Error!");
            }
            else
            {
                string[] arrLine = line.Split('-');
                m_DiffSvn = int.Parse(arrLine[0]);
                m_DiffSvnType = arrLine[1].Trim();
            }
        }
        catch (Exception e)
        {
            Debug.LogError(e.Message);
        }
        finally
        {
            if (sr != null)
                sr.Close();
        }
    }

    private static void SetSvnType()
    {
        if (m_DiffSvnType.Contains("ReleaseCommon"))
        {
            m_Svn = m_CommonSvn;
        }
        else if (m_DiffSvnType.Contains("ReleaseOnline"))
        {
            m_Svn = m_OnlineSvn;
        }
        else
        {
            Debug.LogError("DiffSvnType Error:" + m_DiffSvnType);
        }
        string toRemove = "http://tc-svn.tencent.com/ied/ied_tiki_rep";
        m_SvnInfoSplit = m_Svn.Replace(toRemove, "");
    }

    private static void GenSvnSubmitInfoList()
    {
        string cmd = "svn log -r " + m_DiffSvn + ":HEAD --verbose --xml " + m_Svn + " > " + m_SvnInfoXml;
        ProcessCommandSync(cmd, m_CurWorkspace);

        m_VecSubmitInfo.Clear();
        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.Load(m_CurWorkspace + "svninfo.xml");
        XmlNodeList logentryNodeList = xmlDoc.SelectNodes("//logentry");
        XmlNodeList authorNodeList = xmlDoc.SelectNodes("//author");
        XmlNodeList dateNodeList = xmlDoc.SelectNodes("//date");
        XmlNodeList msgNodeList = xmlDoc.SelectNodes("//msg");
        XmlNodeList pathNodeList = xmlDoc.SelectNodes("//paths");
        if (logentryNodeList != null &&
            authorNodeList != null &&
            dateNodeList != null &&
            msgNodeList != null &&
            pathNodeList != null)
        {
            int logentryNodeListCount = logentryNodeList.Count;
            int authorNodeListCount = authorNodeList.Count;
            int dateNodeListCount = dateNodeList.Count;
            int msgNodeListCount = msgNodeList.Count;
            int pathNodeListCount = pathNodeList.Count;
            if (logentryNodeListCount == authorNodeListCount &&
                authorNodeListCount == dateNodeListCount &&
                dateNodeListCount == msgNodeListCount &&
                msgNodeListCount == pathNodeListCount)
            {
                for (int i = 0; i < logentryNodeList.Count; i++)
                {
                    SubmitInfo si = new SubmitInfo();
                    si.rev = int.Parse(logentryNodeList[i].Attributes["revision"].Value);
                    si.author = authorNodeList[i].InnerText;
                    si.date = dateNodeList[i].InnerText;
                    si.msg = msgNodeList[i].InnerText;
                    string input = pathNodeList[i].InnerText;
                    string[] parts = input.Split(new[] { m_SvnInfoSplit }, StringSplitOptions.RemoveEmptyEntries);
                    string[] result = parts.Select(part => part).ToArray();
                    foreach (var item in result)
                        si.files.Add("Assets" + item);
                    m_VecSubmitInfo.Add(si);
                }
            }
        }
        Debug.Log("SubmitInfo Count:" + m_VecSubmitInfo.Count);
    }

    private static void FindFileAssetBundle()
    {
        foreach (var si in m_VecSubmitInfo)
        {
            si.abPath.Clear();
            si.directlyCalSize.Clear();
            si.allSize = 0;
            foreach (var file in si.files)
            {
                //在白名单的路径过滤掉 .cs .cs.meta .lua.txt .lua.txt.meta 过滤掉
                bool isInWhiteList = false;
                foreach (var whiteListItem in m_PathWhiteList)
                {
                    if (file.Contains(whiteListItem))
                    {
                        isInWhiteList = true;
                        break;
                    }
                }
                if (isInWhiteList)
                    continue;
                if (file.Contains(".cs") || file.Contains(".lua.txt"))
                    continue;

                //不存在的文件
                if (!File.Exists(Application.dataPath + "/../" + file))
                {
                    
                    Debug.LogError("File Not Exists:" + file);
                    continue;
                }

                //meta文件需要看正常文件所在的AssetBundle
                string filePath = file;
                if (file.Contains(".meta"))
                {
                    Debug.Log("File Meta:" + file);
                    filePath = file.Replace(".meta", "");                    
                }

                if (m_AlreadyHandlePath.Contains(filePath))
                    continue;
                m_AlreadyHandlePath.Add(filePath);

                //音频 视频
                if (filePath.Contains(".mp4") || filePath.Contains(".wem") || filePath.Contains(".bnk"))
                {
                    FileInfo fileInfo = new FileInfo(Application.dataPath + "/../" + filePath);
                    si.directlyCalSize.Add(fileInfo);
                    continue;
                }

                string ab = AssetDatabase.GetImplicitAssetBundleName(filePath);
                Debug.Log("File:" + filePath + " --- " + ab);

                if (!string.IsNullOrEmpty(ab))
                {
                    if (!si.abPath.Contains(ab))
                        si.abPath.Add(ab);
                }
                //如果是Prefab即使ab不为空也需要计算依赖的大小
                //非Prefab如果ab是空才计算依赖的大小
                if (filePath.Contains(".prefab") || string.IsNullOrEmpty(ab))
                {
                    string assetGuid = AssetDatabase.AssetPathToGUID(filePath);
                    HashSet<string> assetAntiDep = null;
                    m_AllAntiDependencies.TryGetValue(assetGuid, out assetAntiDep);
                    if (assetAntiDep != null)
                    {
                        if (assetAntiDep.Count > 4)
                            Debug.LogError("AssetAntiDep Count Is Over Five Null:" + filePath + " AntiDepCount:" + assetAntiDep.Count);

                        foreach (var item in assetAntiDep)
                        {
                            string antiDepAssetPath = AssetDatabase.GUIDToAssetPath(item);
                            string ab_Child1 = AssetDatabase.GetImplicitAssetBundleName(antiDepAssetPath);
                            if (!string.IsNullOrEmpty(ab_Child1))
                            {
                                if (!si.abPath.Contains(ab_Child1))
                                    si.abPath.Add(ab_Child1);
                            }
                            else
                            {
                                HashSet<string> assetAntiDep_Child1 = null;
                                m_AllAntiDependencies.TryGetValue(item, out assetAntiDep_Child1);
                                if (assetAntiDep_Child1 != null)
                                {
                                    foreach (var item_Child1 in assetAntiDep_Child1)
                                    {
                                        string antiDepAssetPath_Child1 = AssetDatabase.GUIDToAssetPath(item_Child1);
                                        string ab_Child2 = AssetDatabase.GetImplicitAssetBundleName(antiDepAssetPath_Child1);
                                        if (!string.IsNullOrEmpty(ab_Child2))
                                        {
                                            if (!si.abPath.Contains(ab_Child2))
                                                si.abPath.Add(ab_Child2);
                                        }
                                        else
                                        {
                                            Debug.LogError("AssetAntiDep Child2 AssetBundleName Is Null:" + item + " Dep:" + antiDepAssetPath_Child1);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        Debug.LogError("AssetAntiDep Is Null:" + filePath);
                    }
                }
            }
        }
    }

    private static void CalAssetBundleSize()
    {
        StreamWriter swAll = null;
        long fullSize = 0;
        try
        {
            swAll = new StreamWriter(m_CurWorkspace + "allresult.txt");
            swAll.WriteLine(m_DiffSvnType + " r" + m_DiffSvn + "-HEAD " + " 提交影响的资源大小：");
            swAll.WriteLine("-------------------------------------------------");
            foreach (var si in m_VecSubmitInfo)
            {
                if (si.directlyCalSize.Count <= 0 && si.abPath.Count <= 0)
                    continue;

                string title = si.author + " " + si.rev + " " + si.msg;
                Debug.Log("Subbmit:" + title);
                swAll.WriteLine("【" + title + "】");

                long curSubbmitSize = 0;
                foreach (var item in si.directlyCalSize)
                {
                    Debug.Log("DirectlyCalSize:" + item);
                    swAll.WriteLine("【单个资源大小：" + item.Length + "】" + item.FullName);
                    curSubbmitSize = curSubbmitSize + item.Length;
                }
                foreach (var item in si.abPath)
                {
                    string abPath = m_CurWorkspace + "Dolphion_AssetBundle/AssetBundles/Android/" + item + ".unity3d";
                    if (File.Exists(abPath))
                    {
                        FileInfo fileInfo = new FileInfo(abPath);
                        Debug.Log("AssetBundlePath:" + item + " Size:" + fileInfo.Length);
                        swAll.WriteLine("【单个资源大小：" + fileInfo.Length + "】" + item + ".unity3d");
                        curSubbmitSize = curSubbmitSize + fileInfo.Length;
                    }
                    else
                    {
                        Debug.Log("AssetBundlePath:" + item);
                        swAll.WriteLine("【新增资源】" + item + ".unity3d");
                    }
                }
                string strWarn = "";
                if (curSubbmitSize > 1024 * 1024 * 2)
                    strWarn = "!! 警告 这次提交影响AB超过2M !! ";
                double curSubbmitResult = ((double)curSubbmitSize) / ((double)(1024 * 1024));
                swAll.WriteLine("【" + strWarn + si.author + " " + si.rev + " 提交大小:" + curSubbmitResult.ToString("0.00") + "M】");
                swAll.WriteLine("-------------------------------------------------");

                si.allSize = curSubbmitSize;
                fullSize = fullSize + curSubbmitSize;
            }
            double result = ((double)fullSize) / ((double)(1024 * 1024));
            swAll.WriteLine("【以上提交影响的全部资源大小:" + result.ToString("0.00") + "M】");
        }
        catch (Exception e)
        {
            Debug.LogError(e.ToString());
        }
        finally
        {
            if (swAll != null)
            {
                swAll.Flush();
                swAll.Close();
            }
        }

        StreamWriter sw = null;
        try
        {
            sw = new StreamWriter(m_CurWorkspace + "result.txt");
            double result = ((double)fullSize) / ((double)(1024 * 1024));
            sw.WriteLine(m_DiffSvnType + " r" + m_DiffSvn + "-HEAD " + " 的提交影响总资源大小：" + result.ToString("0.00") + "M");
            sw.WriteLine("请注意！以下单次提交影响超过2M的资源：（详细信息请参考下方的文件）");
            foreach (var si in m_VecSubmitInfo)
            {
                if (si.directlyCalSize.Count <= 0 && si.abPath.Count <= 0)
                    continue;
                if (si.allSize <= 1024 * 1024 * 2)
                    continue;

                string title = si.author + " " + si.rev + " " + si.msg;
                double siResult = ((double)si.allSize) / ((double)(1024 * 1024));
                sw.WriteLine("【" + siResult.ToString("0.00") + "M " + title + "】包含：");

                int showCount = 2;
                int allCount = si.directlyCalSize.Count + si.abPath.Count;
                foreach (var item in si.directlyCalSize)
                {
                    if (showCount <= 0)
                        break;
                    showCount--;
                    sw.WriteLine(item.FullName);
                }
                foreach (var item in si.abPath)
                {
                    if (showCount <= 0)
                        break;
                    showCount--;
                    sw.WriteLine(item + ".unity3d");
                }
                if (allCount > 2)
                {
                    sw.WriteLine("等" + allCount + "个资源");
                }
                sw.WriteLine("-----");
            }
        }
        catch (Exception e)
        {
            Debug.LogError(e.ToString());
        }
        finally
        {
            if (sw != null)
            {
                sw.Flush();
                sw.Close();
            }
        }
    }

    private static string ProcessCommandSync(string cmd, string workDirectory)
    {
        System.Diagnostics.Process p = null;
        try
        {
            System.Diagnostics.ProcessStartInfo start = new System.Diagnostics.ProcessStartInfo("cmd.exe");
            start.Arguments = "/c";
            start.Arguments += (" \"" + cmd + " \"");
            start.CreateNoWindow = true;
            start.ErrorDialog = true;
            start.UseShellExecute = false;
            start.WorkingDirectory = workDirectory;

            if (start.UseShellExecute)
            {
                start.RedirectStandardOutput = false;
                start.RedirectStandardError = false;
                start.RedirectStandardInput = false;
            }
            else
            {
                start.RedirectStandardOutput = true;
                start.RedirectStandardError = true;
                start.RedirectStandardInput = true;
                start.StandardOutputEncoding = Encoding.UTF8;
                start.StandardErrorEncoding = Encoding.UTF8;
            }

            p = System.Diagnostics.Process.Start(start);
            p.ErrorDataReceived += delegate (object sender, System.Diagnostics.DataReceivedEventArgs e)
            {
                //Debug.LogError(e.Data);
            };
            p.OutputDataReceived += delegate (object sender, System.Diagnostics.DataReceivedEventArgs e)
            {
                //Debug.LogError(e.Data);
            };
            p.Exited += delegate (object sender, System.EventArgs e)
            {
                //Debug.LogError(e.ToString());
            };
            string standardOutputInfo = p.StandardOutput.ReadToEnd();
            return standardOutputInfo;
        }
        catch (Exception e)
        {
            m_ExceptionInfo.Add(e.ToString());
            Debug.LogError(e.ToString());
            if (p != null)
            {
                p.Close();
            }
        }
        return "";
    }
    #region 以下暂时废弃
    /*
    private static Dictionary<string, HashSet<string>> AllDependencies { get { return ReferenceFinder.data.dependenciesNonCached; } }
    private static string m_CommonSvn = "http://tc-svn.tencent.com/ied/ied_tiki_rep/Z_Game_proj/release/JK_Client_ReleaseCommon/ZGame_Chess_Prj/";
    private static string m_CurWorkspace = "D:/JsonluanUse/p-e92b989f155341e6adc91e9d020d1d88/";
    private static string m_DiffResFolder = m_CurWorkspace + "diff/AssetBundles/";
    private static string m_ManifestFolder = m_CurWorkspace + "Manifest/";
    private static Dictionary<string, long> m_DiffRes = new Dictionary<string, long>();
    private static Dictionary<string, List<string>> m_DiffManifestRes = new Dictionary<string, List<string>>();
    private static List<string> m_VecOneAssetDependenciesWithoutAB = new List<string>();
    private static List<string> m_NoneCommitButCanDiffAB = new List<string>();
    private static Dictionary<string, List<string>> m_LessCommitButCanDiffAB = new Dictionary<string, List<string>>();
    private static List<string> m_ExceptionInfo = new List<string>();
    private static readonly long m_BigAssetBundleSize = 1572864;//1.5M
    private static readonly int m_DiffSvn = 490520;//0.0.13.272 0.0.13.273

    [MenuItem("InjectFix/测试依赖", false, 1)]
    public static void FindAndroid()
    {
        FindImpl("Android");
    }

    private static void CollectAssets()
    {
        ReferenceFinder.data.CollectAssets(false);
    }

    public static void FindImpl(string platFrom)
    {
        try
        {
            m_ExceptionInfo.Clear();

            Debug.LogError("CollectAssets Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            CollectAssets();
            Debug.LogError("CollectAssets End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            Debug.LogError("FindDiffRes Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            m_DiffRes.Clear();
            string folderPath = m_DiffResFolder + platFrom;
            FindDiffRes(folderPath);
            Debug.LogError("FindDiffRes End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            Debug.LogError("FindDiffManifestRes Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            m_DiffManifestRes.Clear();
            foreach (var item in m_DiffRes)
            {
                string manifestPath = item.Key + ".manifest";
                m_DiffManifestRes.Add(item.Key, FindAllAssetsInManifest(manifestPath));
            }
            string resultFolder = m_CurWorkspace + "Result_" + platFrom;
            if (Directory.Exists(resultFolder))
                Directory.Delete(resultFolder, true);
            Directory.CreateDirectory(resultFolder);
            string manifestDepFile = resultFolder + "/DiffContainAssets.txt";
            WriteManifestDepFile(manifestDepFile);
            string bigAssetBundleFile = resultFolder + "/BigAssetBundle.txt";
            WriteBigAssetBundleFile(bigAssetBundleFile);
            foreach (var item in m_DiffManifestRes)
            {
                foreach (var subItem in item.Value)
                {
                    string[] arrSubItem = subItem.Replace(" ", "").Split('/');
                    Directory.CreateDirectory(resultFolder + "/" + item.Key + "/" + arrSubItem[arrSubItem.Length - 1]);
                }
            }
            Debug.LogError("FindDiffManifestRes End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            Debug.LogError("DiffAsset Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            DiffAsset(resultFolder);
            Debug.LogError("DiffAsset End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            Debug.LogError("WriteInfo Begin " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            StreamWriter sw = sw = new StreamWriter(resultFolder + "/NoneCommitButCanDiffAB.txt");
            sw.WriteLine("Error! Unexpected update resources. Need Check!");
            foreach (var item in m_NoneCommitButCanDiffAB)
                sw.WriteLine("-- " + item + " [" + m_DiffRes[item] + "]");
            sw.Flush();
            sw.Close();

            StreamWriter sw1 = new StreamWriter(resultFolder + "/LessCommitButCanDiffAB.txt");
            sw1.WriteLine("Warning! Possible need for optimization. Only one committed resource!");
            foreach (var item in m_LessCommitButCanDiffAB)
            {
                sw1.WriteLine("-- " + item.Key + " [" + m_DiffRes[item.Key] + "]");
                foreach (var sub in item.Value)
                {
                    sw1.WriteLine(sub);
                }
                sw1.WriteLine("==================================================");
                sw1.Write("");
            }
            sw1.Flush();
            sw1.Close();

            StreamWriter sw2 = new StreamWriter(resultFolder + "/ExceptionInfo.txt");
            foreach (var item in m_ExceptionInfo)
            {
                sw2.WriteLine(item);
                sw2.WriteLine("==================================================");
                sw2.Write("");
            }
            sw2.Flush();
            sw2.Close();

            try
            {
                File.Copy(resultFolder + "/BigAssetBundle.txt", m_CurWorkspace + "/BigAssetBundle.txt", true);
                File.Copy(resultFolder + "/NoneCommitButCanDiffAB.txt", m_CurWorkspace + "/NoneCommitButCanDiffAB.txt", true);
                File.Copy(resultFolder + "/LessCommitButCanDiffAB.txt", m_CurWorkspace + "/LessCommitButCanDiffAB.txt", true);
                File.Copy(resultFolder + "/ExceptionInfo.txt", m_CurWorkspace + "/ExceptionInfo.txt", true);
            }
            catch (Exception e)
            {
                //m_ExceptionInfo.Add(e.ToString());
                Debug.LogError(e.ToString());
            }
            Debug.LogError("WriteInfo End " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }
        catch (Exception e)
        {
            m_ExceptionInfo.Add(e.ToString());
            Debug.LogError(e.ToString());
        }
    }

    private static void DiffAsset(string resultFolder)
    {
        m_NoneCommitButCanDiffAB.Clear();
        m_LessCommitButCanDiffAB.Clear();
        foreach (var item in m_DiffManifestRes)
        {
            Debug.LogError("DiffAssetEach Begin " + item.Key + " " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            bool isAbHasCommit = false;
            int abHasCommitCount = 0;
            foreach (var subItem in item.Value)
            {
                string[] arrSubItem = subItem.Replace(" ", "").Split('/');
                m_VecOneAssetDependenciesWithoutAB.Clear();
                m_VecOneAssetDependenciesWithoutAB.Add(subItem);
                m_VecOneAssetDependenciesWithoutAB.Add(subItem + ".meta");
                GetOneAssetDependenciesWithoutAB(subItem);
                string curPath = resultFolder + "/" + item.Key + "/" + arrSubItem[arrSubItem.Length - 1];
                foreach (var oneAsset in m_VecOneAssetDependenciesWithoutAB)
                {
                    if (File.Exists(oneAsset))
                    {
                        string[] arrItem = oneAsset.Replace(" ", "").Split('/');
                        string cmd = "svn log -r" + m_DiffSvn + ":HEAD " + m_CommonSvn + oneAsset + " > " + arrItem[arrItem.Length - 1] + ".txt";
                        ProcessCommandSync(cmd, resultFolder);
                        string sourcePath = resultFolder + "/" + arrItem[arrItem.Length - 1] + ".txt";
                        string targetPath = curPath + "/" + arrItem[arrItem.Length - 1] + ".txt";
                        Debug.LogError("DiffAsset MoveFile From:" + sourcePath + " To:" + targetPath);
                        if (File.Exists(sourcePath))
                        {
                            try
                            {
                                File.Move(sourcePath, targetPath);
                            }
                            catch (Exception e)
                            {
                                m_ExceptionInfo.Add(e.ToString());
                                Debug.LogError(e.ToString());
                            }
                        }
                        else
                        {
                            m_ExceptionInfo.Add("DiffAsset Error MoveFile No tExists " + oneAsset);
                            Debug.LogError("DiffAsset Error MoveFile No tExists " + oneAsset);
                        }
                    }
                    else
                    {
                        m_ExceptionInfo.Add("DiffAsset Error File Not Exists " + oneAsset);
                        Debug.LogError("DiffAsset Error File Not Exists " + oneAsset);

                    }
                }     
            }

            string[] svnLogFiles = Directory.GetFiles(resultFolder + "/" + item.Key, "*.txt", SearchOption.AllDirectories);
            List<string> allLine = new List<string>();
            foreach (string file in svnLogFiles)
            {
                bool eachFileHasCommit = false;
                string fileName = Path.GetFileName(file);
                fileName = fileName.Substring(0, fileName.Length - 4);
                allLine.Add(fileName);
                StreamReader sr = new StreamReader(file, Encoding.GetEncoding("GB2312"));
                string line = sr.ReadLine();
                while ((line = sr.ReadLine()) != null)
                {
                    if (line.Contains(" | "))
                    {
                        eachFileHasCommit = true;
                        isAbHasCommit = true;
                    }
                    allLine.Add(line);
                }
                sr.Close();
                allLine.Add("================================");
                allLine.Add("");
                if (eachFileHasCommit)
                    abHasCommitCount++;
            }
            StreamWriter sw = null;
            try
            {
                sw = new StreamWriter(resultFolder + "/" + item.Key + "/SvnLog.txt");
                foreach (var line in allLine)
                {
                    sw.WriteLine(line);
                }
            }
            catch (Exception e)
            {
                m_ExceptionInfo.Add(e.ToString());
                Debug.LogError(e.ToString());
            }
            finally
            {
                if (sw != null)
                {
                    sw.Flush();
                    sw.Close();
                }
            }
            long size = m_DiffRes[item.Key];
            if (size > m_BigAssetBundleSize && abHasCommitCount <= 1)
            {
                m_LessCommitButCanDiffAB.Add(item.Key, item.Value);
            }
            if (!isAbHasCommit)
            {
                m_NoneCommitButCanDiffAB.Add(item.Key);
            }
            Debug.LogError("DiffAssetEach End " + item.Key + " " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }

    private static void GetOneAssetDependenciesWithoutAB(string assetPath)
    {
        string assetGuid = AssetDatabase.AssetPathToGUID(assetPath);
        HashSet<string> assetDep = null;
        AllDependencies.TryGetValue(assetGuid, out assetDep);
        if (assetDep != null)
        {
            foreach (var item in assetDep)
            {
                string depAssetPath = AssetDatabase.GUIDToAssetPath(item);
                string implicitAssetBundleName = AssetDatabase.GetImplicitAssetBundleName(depAssetPath);
                if (string.IsNullOrEmpty(implicitAssetBundleName))
                {
                    if (!m_VecOneAssetDependenciesWithoutAB.Contains(depAssetPath) &&
                        !(depAssetPath.EndsWith(".cs") || depAssetPath.EndsWith(".cs.meta")))
                    {
                        m_VecOneAssetDependenciesWithoutAB.Add(depAssetPath);
                        m_VecOneAssetDependenciesWithoutAB.Add(depAssetPath + ".meta");
                    }
                    GetOneAssetDependenciesWithoutAB(depAssetPath);
                }
            }
        }
    }

    private static void WriteBigAssetBundleFile(string filePath)
    {
        StreamWriter sw = null;
        try
        {
            sw = new StreamWriter(filePath);
            sw.WriteLine("Warning! Big assetbundle update. Over 1.5M");
            foreach (var item in m_DiffManifestRes)
            {
                long size = m_DiffRes[item.Key];
                if (size > m_BigAssetBundleSize)
                {
                    sw.WriteLine("-- " + item.Key + " [" + m_DiffRes[item.Key] + "]");
                }
            }
        }
        catch (Exception e)
        {
            m_ExceptionInfo.Add(e.ToString());
            Debug.LogError(e.ToString());
        }
        finally
        {
            if (sw != null)
            {
                sw.Flush();
                sw.Close();
            }
        }
    }

    private static void WriteManifestDepFile(string filePath)
    {
        StreamWriter sw = null;
        try
        {
            sw = new StreamWriter(filePath);
            foreach (var item in m_DiffManifestRes)
            {
                sw.WriteLine("-- " + item.Key + " [" + m_DiffRes[item.Key] + "]");
                foreach (var subItem in item.Value)
                {
                    sw.WriteLine("[" + subItem + "]");
                }
                sw.WriteLine("------------------------------------------------------------------------");
                sw.WriteLine("");
            }
        }
        catch (Exception e)
        {
            m_ExceptionInfo.Add(e.ToString());
            Debug.LogError(e.ToString());
        }
        finally
        {
            if (sw != null)
            {
                sw.Flush();
                sw.Close();
            }
        }
    }

    private static List<string> FindAllAssetsInManifest(string manifestPath)
    {
        List<string> assetList = new List<string>();
        string fullManifestPath = m_ManifestFolder + manifestPath;
        StreamReader sr = null;
        try
        {
            sr = new StreamReader(fullManifestPath);
            string line = sr.ReadLine();
            while ((line = sr.ReadLine()) != null)
            {
                if (line.Contains("- Assets/"))
                    assetList.Add(line.Substring(2, line.Length - 2));
            }
        }
        catch (Exception e)
        {
            m_ExceptionInfo.Add(e.ToString());
            Debug.LogError(e.Message);
        }
        finally
        {
            if (sr != null)
                sr.Close();
        }
        return assetList;
    }

    private static void FindDiffRes(string folderPath)
    {
        if (Directory.Exists(folderPath))
        {
            foreach (string temPath in Directory.GetFiles(folderPath))
            {
                if (temPath.Contains(".unity3d"))
                {
                    FileInfo fileInfo = new FileInfo(temPath);
                    long fileSize = fileInfo.Length;//字节数
                    string filePath = temPath.Split("/AssetBundles/")[1];
                    m_DiffRes.Add(filePath, fileSize);
                }
            }
            foreach (string subFolderPath in Directory.GetDirectories(folderPath))
            {
                FindDiffRes(subFolderPath);
            }
        }
    }
    */
    #endregion
}
