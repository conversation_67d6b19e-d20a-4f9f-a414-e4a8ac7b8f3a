#if TKF_EDITOR//TKFrame Auto Gen
using UnityEditor;
using UnityEngine;
using UnityEditor.UI;
using UnityEngine.UI;
using TKFrame.Item;
namespace TKFrame
{
    [CustomEditor(typeof(GraphicText))]
    [CanEditMultipleObjects]
    public class GraphicTextEditor : UnityEditor.UI.TextEditor
    {
        SerializedProperty m_Sprites;

        protected override void OnEnable()
        {
            base.OnEnable();
            m_Sprites = serializedObject.FindProperty("SpriteList");
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            EditorGUILayout.PropertyField(m_Sprites, true);

            serializedObject.ApplyModifiedProperties();
        }
    }
}
#endif //TKFrame Auto Gen
