using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using UnityEditor.AnimatedValues;
using System.Collections;

namespace TKPlugins
{
    [CustomEditor(typeof(MixText), false)]
    [CanEditMultipleObjects]
    public class MixTextEditor : UnityEditor.UI.TextEditor
    {
        SerializedProperty sprites;
        protected override void OnEnable()
        {
            base.OnEnable();

            sprites = serializedObject.FindProperty("m_sprites");
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            if (GUILayout.Button("Clear Unuse Image"))
            {
                foreach (Object target in this.targets)
                {
                    MixText text = target as MixText;
                    if (text != null)
                        text.ClearUnUsedInLineImage();
                }
            }

            EditorGUILayout.PropertyField(sprites, true);
            serializedObject.ApplyModifiedProperties();
            
            MixText mixtext = target as MixText;
            mixtext.imageScale = EditorGUILayout.FloatField("图片缩放", mixtext.imageScale);
        }
    }
}