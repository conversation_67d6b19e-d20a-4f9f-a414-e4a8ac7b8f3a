using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;
#if UNITY_EDITOR
namespace TKFrame.COSResource
{
    public class COSVideoTest : MonoBehaviour
    {
        public RawImage rawImage;
        public VideoPlayer videoPlayer;
        public Text videoText;
        public Button downloadButton;
        public Button playButton;
        public string videoPath = "s6_skill_akali.mp4";


        public void Start()
        {
            downloadButton.onClick.AddListener(OnClickDownload);
            playButton.onClick.AddListener(OnClickPlay);

            RenderTexture rt = new RenderTexture(512, 512, 0);
            rt.name = "Video_RT";
            rawImage.texture = rt;
            videoPlayer.targetTexture = rt;

            CheckVideo();
        }

        private void OnDestroy()
        {
            if (rawImage.texture != null)
                GameObject.Destroy(rawImage.texture);
        }

        private void CheckVideo()
        {
            playButton.gameObject.SetActive(false);
            downloadButton.gameObject.SetActive(false);
            videoText.gameObject.SetActive(false);

            COSVideoLoader.NeedUpdate(videoPath, (path, status, fileInfo) =>
            {
                if (status == COSFileStatus.NeedUpdate || status == COSFileStatus.NeedDownload)
                {
                    playButton.gameObject.SetActive(false);
                    downloadButton.gameObject.SetActive(true);

                    if (fileInfo != null)
                    {
                        videoText.gameObject.SetActive(true);
                        videoText.text = fileInfo.Size.ToString();
                    }
                }
                else if (status == COSFileStatus.Vaild)
                {
                    playButton.gameObject.SetActive(true);
                    downloadButton.gameObject.SetActive(false);

                    videoText.gameObject.SetActive(true);
                    videoText.text = fileInfo.Size.ToString();
                }
                else
                {
                    playButton.gameObject.SetActive(false);
                    downloadButton.gameObject.SetActive(false);

                    videoText.gameObject.SetActive(true);
                    videoText.text = $"File {path} NotExist";
                }


            });
        }

        private void OnClickDownload()
        {
            COSVideoLoader.DownloadVideo(videoPath, (path, cur, total) =>
            {
                videoText.text = string.Format("{0}/{1}", cur, total);
            }, (res) =>
            {
                if (res.success)
                {
                    downloadButton.gameObject.SetActive(false);
                    videoText.gameObject.SetActive(false);
                    playButton.gameObject.SetActive(true);
                }
                else
                {
                    CheckVideo();
                }
            }, 10, this);
        }

        private void OnClickPlay()
        {
            videoPlayer.url = COS.ResManager.FileManager.GetAbsolutePath(videoPath);
            videoPlayer.Play();
        }
    }
}
#endif