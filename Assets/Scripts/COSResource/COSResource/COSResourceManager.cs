using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKPlugins;
using UnityEngine;
using UnityEngine.Networking;

namespace TKFrame.COSResource
{
    public enum COSFileStatus
    {
        Vaild = 0,          // 可用的
        NeedUpdate = 1,     // 客户端版本是老的，需要更新
        NeedDownload = 2,   // 客户端版本不存在，需要下载
        NotExist = 3,       // 服务器上不存在
    }

    public enum COSInitStatus
    {
        NoInit,
        Initing,
        Success,
        CheckVersionFaild,
        CheckFilelistFaild,
    }

    public enum COSInitLocalFileListStatus
    {
        NoLoad,
        Loading,
        LoadFinished,
    }

    /// <summary>
    /// 资源管理器
    /// </summary>
    public class COSResourceManager : COSResourceManagerBase
    {
        private static string m_pathRootCache = string.Empty;

        private string m_environmentCache = string.Empty;
        private string m_resourceLevelCache = string.Empty;
        private string m_serverRootCache = string.Empty;

        public override string GetServerPathRoot()
        {
            if (string.IsNullOrEmpty(m_serverRootCache) || m_environmentCache != environment || m_resourceLevelCache != ResourceLevel)
            {
                m_serverRootCache = string.Format("resources/{0}/{1}/", environment, ResourceLevel);
                m_environmentCache = environment;
                m_resourceLevelCache = ResourceLevel;
            }
            return m_serverRootCache;
        }

        public override string GetClientPathRoot()
        {
            return GetPathRoot();
        }

        public override bool TryGetSvnCodeVersion(out string svnResVersionPath)
        {
            svnResVersionPath = string.Empty;
            return false;
        }

        public static string GetPathRoot()
        {
            if (string.IsNullOrEmpty(m_pathRootCache))
            {
                m_pathRootCache = string.Format("{0}/{1}/{2}/",
                                        GameEnvironment.AssetRootPath,                  // 根目录
                                        TKFrameConfig.Instance.COSResourceFolder(),     // COS子文件夹
                                        BaseLoader.GetPlatformFolderForAssetBundles()); // 平台
            }
            return m_pathRootCache;
        }
    }
}
