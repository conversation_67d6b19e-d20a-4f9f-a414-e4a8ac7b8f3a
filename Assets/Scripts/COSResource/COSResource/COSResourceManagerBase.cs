using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TKPlugins;
using UnityEngine;
using UnityEngine.Networking;

namespace TKFrame.COSResource
{
    public abstract class COSResourceManagerBase : TKBehaviour
    {
        public string gmVersion = "";
        public string gmEnv = "";
        // debug 开发测试
        // trunk 主干国内
        // cn 国内分支
        // sea   海外
        public string environment = "trunk";        // 国内主干
        public string resVersionPath = COSGlobalDefine.SERVER_VERSION_FILENAME;

        // 需要在InitResLevel函数中初始化的变量
        public string ResourceLevel = "normal";                    // 资源品质 low normal high
        // end 需要在InitEnv函数中初始化的变量

        /// <summary>
        /// 写死在版本里面，每次从主干拷贝到分支以后，分支的迭代号+1；不同迭代的版本从0开始计算，不同迭代的版本不互通
        /// 用于应对主干覆盖分支、分支还没上线之前的热更场景
        /// </summary>
        public int iterationId { get; private set; } = 0;
        public int resourceVersion { get; private set; } = 0;

        public COSInitStatus InitStatus { get; private set; } = COSInitStatus.NoInit;
        private Action<COSInitStatus> m_onInitFinished = null;

        private Action m_onVersionChanged;

        // 本地文件加载器
        public COSFileManager FileManager { get; } = new COSFileManager();
        // 下载文件管理器
        public COSDownloadManager DownloadManager { get; } = new COSDownloadManager();

        protected COSFileList m_networkFileList = new COSFileList();        // 服务器上的文件列表
        protected COSFileList m_localFileList = new COSFileList();          // 本地的文件列表

        protected TKDictionary<string, COSResourceRequest> m_requestDict = new TKDictionary<string, COSResourceRequest>();

#if UNITY_EDITOR
        public COSFileList NetworkFileList => m_networkFileList;
#endif

        protected override void Awake()
        {
            base.Awake();

            DownloadManager.Init();
            FileManager.Init();
            FileManager.Bind(this);
        }

        public void Display(ICOSConsole sb)
        {
            sb.AppendLine(string.Format("{0} iter: {1} ver: {2} env: {3} status: {4}", GetType().Name, iterationId, resourceVersion, environment, InitStatus));
            DownloadManager.Display(sb);
        }

        public abstract string GetServerPathRoot();
        public abstract string GetClientPathRoot();

        public abstract bool TryGetSvnCodeVersion(out string svnResVersionPath);

        protected override void OnDestroy()
        {
            base.OnDestroy();


            DownloadManager.ClearAll();
            FileManager.ClearAll();
            DownloadManager.UnInit();
            FileManager.UnInit();
        }

#if UNITY_EDITOR
        public void InitFirstVersion()
        {
            resourceVersion = 0;
            m_networkFileList.FileDict = new TKDictionary<string, COSFileInfo>();
            InitStatus = COSInitStatus.Success;
        }
#endif

        public void ClearCache()
        {
            PlayerPrefs.SetInt(COSGlobalDefine.LOCAL_VERSION_PREF_KEY + this.GetType().Name, -1);

            InitStatus = COSInitStatus.NoInit;
            m_localFileList.Clear();

            var localFileListPath = COSGlobalDefine.LOCAL_FILELIST_FILENAME;
            if (FileManager.IsFileExist(localFileListPath))
            {
                FileManager.DeleteFile(localFileListPath);
            }

            DownloadManager.ClearAll();
            FileManager.ClearAll();

            COSPool.Clear();
            COSBlockPool.Clear();

            FileManager.ClearAllCacheFiles();
        }

        public void AddVersionChangedCallback(Action callback)
        {
            m_onVersionChanged += callback;
        }

        public void RemoveVersionChangedCallback(Action callback)
        {
            m_onVersionChanged -= callback;
        }

        private void InitLocalFileList()
        {
            using (Stream dataStream = FileManager.LoadFileReadStreamSync(COSGlobalDefine.LOCAL_FILELIST_FILENAME))
            {
                m_localFileList.Clear();
                if (dataStream != null)
                {
                    try
                    {
                        m_localFileList.ReadFrom(dataStream);
                        COSLog.Log("[" + GetType() + ".InitLocalFileList] FileList Success {0}", m_localFileList.FileDict.Count);
                    }
                    catch (Exception ex)
                    {
                        m_localFileList.Clear();

                        COSLog.Error("[" + GetType() + ".InitLocalFileList] FileList Faild \r\n {0}", ex.ToString());
                    }
                }
            }
        }

        private void OnInitFinished(COSInitStatus status)
        {
            InitStatus = status;

            if (status == COSInitStatus.Success)
            { // 初始化成功 

                // 清理一下磁盘空间
                AutoClearDiskCache();

                List<string> removes = null;
                foreach (var requestPair in m_requestDict)
                {
                    if (!LoadResourceImpl(requestPair.Value))
                    {
                        if (removes == null)
                            removes = new List<string>();
                        removes.Add(requestPair.Key);
                        NotifyRequestFaild(requestPair.Value);
                        COSPool.Free(requestPair.Value);
                    }
                }
                if (removes != null)
                {
                    for (int i = 0; i < removes.Count; ++i)
                    {
                        m_requestDict.Remove(removes[i]);
                    }
                }
            }
            else
            {
                HandleRequestWhenInitFaild();
            }

            InvokeInitFinished();
        }

        private IEnumerator InvokeInitFinishedNextFrame(Action<COSInitStatus> onFinished)
        {
            yield return null;
            // 下一帧再判断一下 如果还是成功初始化的状态 则直接调用 否则就请求一下初始化 防止中间突然初始化状态改了
            if (InitStatus == COSInitStatus.Success)
                onFinished?.Invoke(InitStatus);
            else
                Init(onFinished);
        }

        private void InvokeInitFinished()
        {
            m_onInitFinished?.Invoke(InitStatus);
            m_onInitFinished = null;
        }

        private void HandleRequestWhenInitFaild()
        {
            // 优先判断本地是否已经下载了，如果已经下载，就使用直接用本地的，否则就通知加载失败
            List<string> removes = null;
            foreach (var requestPair in m_requestDict)
            {
                var request = requestPair.Value;
                if (!LoadResourceInLocalImpl(request))
                {
                    if (removes == null)
                        removes = new List<string>();
                    removes.Add(requestPair.Key);
                    NotifyRequestFaild(request);
                    COSPool.Free(request);
                }
            }
            if (removes != null)
            {
                for (int i = 0; i < removes.Count; ++i)
                {
                    m_requestDict.Remove(removes[i]);
                }
            }
        }

        private void NotifyRequestFaild(COSResourceRequest request)
        {
            COSResource resource = COSPool.Get<COSResource>();
            resource.Init(request.resName, null, null, false);
            try
            {
                request.OnFinished(resource);
            }
            catch (Exception ex)
            {
                COSLog.Error("[" + GetType() + ".LoadResource Faild] {0} onfinished callback faild! {1}", request.resName, ex.ToString());
            }
            COSPool.Free(resource);
        }

        // 下載SVN对应版本的COS版本完成
        private void OnDownloadSvnVersionFinished(string path, byte[] data)
        {
            // 如果没下成功 就下载普通版本的
            if (data == null)
            {
                COSLog.Log("[" + GetType() + ".Init] OnDownloadSvnVersionFinished faild! try download common version.");
                DownloadManager.DownloadVersion(resVersionPath, OnDownloadVersionFinished, 5, true);
                return;
            }

            OnDownloadVersionFinished(path, data);
        }

        private void OnDownloadVersionFinished(string path, byte[] data)
        {
            if (data == null)
            {
                COSLog.Error("[" + GetType() + ".Init Faild] networkResourceVersion parse faild!");
                OnInitFinished(COSInitStatus.CheckVersionFaild);
                return;
            }

            var versionContent = Encoding.UTF8.GetString(data);
            var versionContentArr = versionContent.BeginSplit('\n');
            if (versionContentArr.Length != 5)
            {
                COSLog.Error("[" + GetType() + ".Init Faild] networkResourceVersion parse faild! length: {0} error! \r\n{1}", versionContentArr.Length, versionContent);
                versionContentArr.EndSplit();
                OnInitFinished(COSInitStatus.CheckVersionFaild);
                return;
            }

            if (!versionContentArr.TryParse(0, out int networkResourceVersion))
            {
                COSLog.Error("[" + GetType() + ".Init Faild] networkResourceVersion parse faild! length: {0} error! \r\n{1} parse version faild", versionContentArr.Length, versionContent);
                versionContentArr.EndSplit();
                OnInitFinished(COSInitStatus.CheckVersionFaild);
                return;
            }

            if (!versionContentArr.TryParse(1, out int crc1))
            {
                COSLog.Error("[" + GetType() + ".Init Faild] networkResourceVersion parse faild! length: {0} error! \r\n{1} parse crc1 faild", versionContentArr.Length, versionContent);
                versionContentArr.EndSplit();
                OnInitFinished(COSInitStatus.CheckVersionFaild);
                return;
            }
            if (!versionContentArr.TryParse(2, out int crc2))
            {
                COSLog.Error("[" + GetType() + ".Init Faild] networkResourceVersion parse faild! length: {0} error! \r\n{1} parse crc2 faild", versionContentArr.Length, versionContent);
                versionContentArr.EndSplit();
                OnInitFinished(COSInitStatus.CheckVersionFaild);
                return;
            }
            if (!versionContentArr.TryParse(3, out int size))
            {
                COSLog.Error("[" + GetType() + ".Init Faild] networkResourceVersion parse faild! length: {0} error! \r\n{1} parse size faild", versionContentArr.Length, versionContent);
                versionContentArr.EndSplit();
                OnInitFinished(COSInitStatus.CheckVersionFaild);
                return;
            }
            string md5 = versionContentArr[4];

            var info = new COSFileInfo()
            {
                Md5 = md5,
                Size = size,
                Crc1 = (byte)crc1,
                Crc2 = (byte)crc2,
                ResType = COSResourceType.Default
            };

            string networkFileName = $"network.{ResourceLevel}.{environment}.{COSGlobalDefine.SERVER_FILELIST_FILENAME}";
            if (resourceVersion != networkResourceVersion || !FileManager.IsFileExist(networkFileName))
            {
                int lastResVer = resourceVersion;
                resourceVersion = networkResourceVersion;
                var fileListName = $"/index/{iterationId}/filelist/{resourceVersion}.{COSGlobalDefine.SERVER_FILELIST_FILENAME}";
                COSLog.Log("[" + GetType() + ".Init] start download ServerResourceFileList resourceVersion = {0} networkResourceVersion = {1} size = {2} md5 = {3} fileListName = {4}", lastResVer, networkResourceVersion, size, md5, fileListName);
                //string fileListName = $"{networkResourceVersion}.{COSGlobalDefine.SERVER_FILELIST_FILENAME}";
                DownloadManager.DownloadFile(fileListName, info, null, OnDownloadFileListFinished, GetBlockSize(), 5, true);
            }
            else
            {
                COSLog.Log("[" + GetType() + ".Init] start load ServerResourceFileList resourceVersion = {0} networkResourceVersion = {1} size = {2} md5 = {3}", resourceVersion, networkResourceVersion, size, md5);

                FileManager.LoadFile(networkFileName, info, OnLoadFileListFinished, 5);
            }
            versionContentArr.EndSplit();
        }

        public static byte[] Decompress(byte[] inBytes, int offset, int length)
        {
            byte[] result = null;
            MemoryStream compressedStream = new MemoryStream(inBytes, offset, length);
            using (MemoryStream outStream = new MemoryStream())
            {
                using (GZipStream Decompress = new GZipStream(compressedStream, CompressionMode.Decompress))
                {
                    Decompress.CopyTo(outStream);
                    result = outStream.ToArray();
                }
            }
            return result;
        }

        public int GetBlockSize()
        {
            // 限制资源包下载速度
            if (COS.SettingPrefs.GetInt(COSSettingPrefs.NETWORK_SPEED_LIMIT) == 1)
                return 1024;
            if (ResourceLevel == "low")
                return 10 * 1024;
            else if (ResourceLevel == "normal")
                return 50 * 1024;
            else
                return 100 * 1024;
        }

        private void OnDownloadFileListFinished(COSDownloadRequest request)
        {
            if (request.IsSuccess)
            {
                // 迁移到正式路径
                string networkFileName = $"network.{ResourceLevel}.{environment}.{COSGlobalDefine.SERVER_FILELIST_FILENAME}";
                string absolutePath = FileManager.GetAbsolutePath(networkFileName);
                if (File.Exists(absolutePath))
                    File.Delete(absolutePath);
                string srcPath = FileManager.GetAbsolutePath(request.RelativePath);
                File.Move(srcPath, absolutePath);

                FileManager.LoadFile(networkFileName, request.FileInfo, OnLoadFileListFinished, 5);

                m_onVersionChanged?.Invoke();
            }
            else
            {
                COSLog.Error("[" + GetType() + ".Init Faild] m_networkFileList download faild!");
                OnInitFinished(COSInitStatus.CheckFilelistFaild);
            }
        }

        private void OnLoadFileListFinished(COSFileRequest request)
        {
            var data = request.GetData();
            if (request.Length == 0)
            {
                COSLog.Error("[" + GetType() + ".Init Faild] m_networkFileList download faild!");
                OnInitFinished(COSInitStatus.CheckFilelistFaild);
                return;
            }

            m_networkFileList.Clear();

            try
            {
                using (Stream stream = TKFrame.FileManager.Instance.GetReadStream(data))
                {
                    using (GZipStream decompressStream = new GZipStream(stream, CompressionMode.Decompress))
                    {
                        m_networkFileList.ReadFrom(decompressStream);
                    }
                }

#if UNITY_EDITOR
                StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
                sb.AppendLine("path, type, md5, size, crc1, crc2");
                foreach (var item in m_networkFileList.FileDict)
                {
                    sb.AppendLine($"{item.Key}, {item.Value.ResType}, {item.Value.Md5}, {item.Value.Size}, {item.Value.Crc1}, {item.Value.Crc2}");
                }
                File.WriteAllText(request.GetClientPathRoot() + request.relativePath + ".csv", sb.ToString());
                MicroObjectPool<StringBuilder>.Release(sb);
#endif

                PlayerPrefs.SetInt(COSGlobalDefine.LOCAL_VERSION_PREF_KEY + this.GetType().Name, resourceVersion);
                OnInitFinished(COSInitStatus.Success);
                COSLog.Log("[" + GetType() + ".Init] FileList Success {0}", m_networkFileList.FileDict.Count);
            }
            catch (Exception ex)
            {
                COSLog.Error("[" + GetType() + ".Init Faild] m_networkFileList parse faild!\r\n{0}", ex.ToString());
                OnInitFinished(COSInitStatus.CheckFilelistFaild);
            }
        }

#if UNITY_EDITOR
        [UnityEditor.MenuItem("Assets/xiaobai/cos_unpack_filelist(zip)")]
        public static void UnpackLisZipt()
        {
            string path = UnityEditor.EditorUtility.OpenFilePanel("打开", Application.dataPath, "");
            if (string.IsNullOrEmpty(path))
                return;

            var data = File.ReadAllBytes(path);
            COSFileList fileList = new COSFileList();
            using (Stream stream = TKFrame.FileManager.Instance.GetReadStream(data))
            {
                using (GZipStream decompressStream = new GZipStream(stream, CompressionMode.Decompress))
                {
                    fileList.ReadFrom(decompressStream);
                }
            }
            StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
            sb.AppendLine("path, type, md5, size, crc1, crc2");
            foreach (var item in fileList.FileDict)
            {
                sb.AppendLine($"{item.Key}, {item.Value.ResType}, {item.Value.Md5}, {item.Value.Size}, {item.Value.Crc1}, {item.Value.Crc2}");
            }
            File.WriteAllText(path + ".csv", sb.ToString());
            MicroObjectPool<StringBuilder>.Release(sb);
        }
        [UnityEditor.MenuItem("Assets/xiaobai/cos_unpack_filelist")]
        public static void UnpackList()
        {
            string path = UnityEditor.EditorUtility.OpenFilePanel("打开", Application.dataPath, "");
            if (string.IsNullOrEmpty(path))
                return;

            var data = File.ReadAllBytes(path);
            COSFileList fileList = new COSFileList();
            using (Stream stream = TKFrame.FileManager.Instance.GetReadStream(data))
            {
                fileList.ReadFrom(stream);
            }
            StringBuilder sb = MicroObjectPool<StringBuilder>.Get();
            sb.AppendLine("path, type, md5, size, crc1, crc2");
            foreach (var item in fileList.FileDict)
            {
                sb.AppendLine($"{item.Key}, {item.Value.ResType}, {item.Value.Md5}, {item.Value.Size}, {item.Value.Crc1}, {item.Value.Crc2}");
            }
            File.WriteAllText(path + ".csv", sb.ToString());
            MicroObjectPool<StringBuilder>.Release(sb);
        }
#endif

        public void Uninit()
        {
            if (InitStatus == COSInitStatus.Success)
            {
                InitStatus = COSInitStatus.NoInit;

                DownloadManager.ClearAll();
                FileManager.ClearAll();

                COSLog.Log("uninit cos resource manager");
            }
        }

        public bool NeedAutoReinit()
        {
            return m_requestDict.Count > 0;
        }

        public bool Init(Action<COSInitStatus> onFinished)
        {
            if (InitStatus == COSInitStatus.Initing)
            {
                m_onInitFinished += onFinished;
                return false;
            }

            if (InitStatus == COSInitStatus.Success)
            {
                // 如果本地已经初始化了 下一帧直接给他返回
                StartCoroutine(InvokeInitFinishedNextFrame(onFinished));
                return false;
            }

            InitStatus = COSInitStatus.Initing;
            m_onInitFinished = onFinished;

            COSBlockPool.InitPool(GetBlockSize(), 32);

            // 初始化环境（主干、分支or海外）
            InitEnv();
            // 初始化资源分级（low、normal or high） 默认是normal，low和high需要特殊设置。
            InitResLevel();

            UnityWebRequest.ClearCookieCache();

            // 开始初始化迭代版本号
            StartCoroutine(InitVersion(OnInitVersionFinished));

            return true;
        }

        private void OnInitVersionFinished()
        {
            DownloadManager.Bind(this);

            int localResourceVersion = PlayerPrefs.GetInt(COSGlobalDefine.LOCAL_VERSION_PREF_KEY + this.GetType().Name);
            COSLog.Log("[{0}.Init] env: {1} version:{2} reslevel:{3} localResourceVersion = {4} iter: {5}", GetType().FullName, environment, resVersionPath, ResourceLevel, localResourceVersion, iterationId);
            resourceVersion = localResourceVersion;

            InitLocalFileList();

            if (TryGetSvnCodeVersion(out string svnResVersionPath))
            {
                DownloadManager.DownloadVersion(svnResVersionPath, OnDownloadSvnVersionFinished, 5, false);
            }
            else
            {
                DownloadManager.DownloadVersion(resVersionPath, OnDownloadVersionFinished, 5, true);
            }
        }

        private void InitEnv()
        {
            if (!string.IsNullOrEmpty(gmEnv))
            {
                environment = gmEnv;
                return;
            }
            environment = COSDelegateInterface.VersionInfo_COSEnv?.Invoke();// VersionInfo.COSEnv;
            if (string.IsNullOrEmpty(environment))
                environment = "trunk";
        }

        private System.Collections.IEnumerator InitVersion(Action onFinished)
        {
#if UNITY_ANDROID
            var path = Application.streamingAssetsPath + "/" + COSGlobalDefine.ITER_LOCAL_FILENAME;
#else
            var path = "file://" + Application.streamingAssetsPath + "/" + COSGlobalDefine.ITER_LOCAL_FILENAME;
#endif
            UnityWebRequest request = UnityWebRequest.Get(path);
            request.SendWebRequest();
            while (!request.isDone)
                yield return null;

            var iterContent = request.downloadHandler.text;
            if (int.TryParse(iterContent, out int iterId))
            {
                iterationId = iterId;
                Diagnostic.Log($"[{this.GetType()}.InitVersion] iterationId: {iterationId} from file");
            }
            else
            {
                iterationId = 0;
                Diagnostic.Warn($"[{this.GetType()}.InitVersion] iterationId: {iterationId} faild! file content: {iterContent}");
            }

            string version;
            if (!string.IsNullOrEmpty(gmVersion))
                version = gmVersion;
#if !JKCHESS_PBE // PBE包不用这么麻烦 不用发布
            else if (COSDelegateInterface.VersionInfo_VersionMode?.Invoke() == "release" || COSDelegateInterface.VersionInfo_VersionMode?.Invoke() == "prerelease")       // 正式环境需要区分版本号
            {
                version = "release/" + COSDelegateInterface.GameVersionModel_App_Version?.Invoke() + "/" + COSDelegateInterface.GameVersionModel_Resource_Version?.Invoke();
            }
#endif
            //else if (VersionInfo.VersionMode == "prerelease")       // 正式环境的预发布不需要区分版本号
            //{
            //    version = "prerelease";
            //}
            //else if (VersionInfo.VersionMode == "closedebug2" || VersionInfo.VersionMode == "closedebug")   // 内网环境不需要区分
            //{
            //    version = "closedebug";
            //}
            else // 其他版本都统一走主干
            {
                version = "trunk";
            }

            resVersionPath = $"index/{iterationId}/{version}_" + COSGlobalDefine.SERVER_VERSION_FILENAME;

            onFinished?.Invoke();

            //if (File.Exists(path))
            //{
            //    var iterContent = File.ReadAllText(path);
            //    if (int.TryParse(iterContent, out int iterId))
            //    {
            //        iterationId = iterId;
            //        Diagnostic.Log($"[{this.GetType()}.InitVersion] iterationId: {iterationId} from file");
            //    }
            //    else
            //    {
            //        iterationId = 0;
            //        Diagnostic.Warn($"[{this.GetType()}.InitVersion] iterationId: {iterationId} faild! file content: {iterContent}");
            //    }
            //}
            //else
            //{
            //    iterationId = 0;
            //    Diagnostic.Warn($"[{this.GetType()}.InitVersion] iterationId: {iterationId} faild! file not found in path: " + path);
            //    UIOverlay.Instance.ShowCommonTips(path);
            //}
        }

        public void SetResLevel(string resLevel)
        {
            if (resLevel != "low" && resLevel != "normal" && resLevel != "high")
                return;

            PlayerPrefs.SetString(COSGlobalDefine.CACHE_RES_LEVEL_KEY, resLevel);
            Uninit();
            ClearCache();
            Init(null);
        }

        protected bool NeedUseLowRes()
        {
            return !SystemInfo.SupportsTextureFormat(TextureFormat.ASTC_RGBA_8x8)
                && SystemInfo.SupportsTextureFormat(TextureFormat.ETC_RGB4);
        }

        public void InitResLevel()
        {
            if (this is COSResourceManager)
            {
                var resLevelPrefValue = PlayerPrefs.GetString(COSGlobalDefine.CACHE_RES_LEVEL_KEY, string.Empty);
                if (string.IsNullOrEmpty(resLevelPrefValue))
                {
                    // 自动设置 (不支持ASTC但是支持ETC的 用low，否则用normal，然后高清资源需要手动设置)
                    if (NeedUseLowRes())
                    {
                        ResourceLevel = "low";
                    }
                    else
                    {
                        ResourceLevel = "normal";
                    }
                }
                else
                {
                    ResourceLevel = resLevelPrefValue;
                }
            }
            else
            {
                ResourceLevel = "normal";
            }
        }

        public void MergeFileListToLocal(COSFileList fileList)
        {
            lock (m_localFileList)
            {
                foreach (var item in fileList.FileDict)
                {
                    if (!FileManager.IsFileExist(item.Key))
                    {
                        COSLog.Error("[" + GetType().Name + $"[MergeFileListToLocal]file: {item.Key} is not exist! merge faild!");
                        continue;
                    }
                    //if (COSGlobalDefine.IsZipFile(item.Key))
                    //{
                    //    COSLog.Log("[" + GetType().Name + $"[MergeFileListToLocal]file: {item.Key} is zip file! ingore.");
                    //    continue;
                    //}
                    if (!m_localFileList.FileDict.TryGetValue(item.Key, out COSFileInfo fileInfo))
                    {
                        fileInfo = new COSFileInfo();
                        m_localFileList.FileDict.Add(item.Key, fileInfo);
                    }
                    fileInfo.Md5 = item.Value.Md5;
                    fileInfo.Size = item.Value.Size;
                    fileInfo.Crc1 = item.Value.Crc1;
                    fileInfo.Crc2 = item.Value.Crc2;
                    fileInfo.ResType = item.Value.ResType;
                }
                COSLog.Log("[" + GetType().Name + $"[MergeFileListToLocal] count: " + fileList.FileDict.Count + ".");
            }
            SaveLocalFileList_InBackgroundThread();
        }

        #region 加载

        private void UpdateLocalFileList(string resourceName, COSFileInfo fileInfo)
        {
            //if (COSGlobalDefine.IsZipFile(resourceName))
            //{
            //    COSLog.Log("[" + GetType().Name + $"[UpdateLocalFileList]file: {resourceName} is zip file! ingore.");
            //    return;
            //}

            lock (m_localFileList)
            {
                if (m_localFileList.FileDict == null)
                    m_localFileList.FileDict = new TKDictionary<string, COSFileInfo>();
                m_localFileList.FileDict[resourceName] = fileInfo;

                //COSLog.Log("[" + GetType().Name + $"[UpdateLocalFileList] name: " + resourceName);
            }

            SaveLocalFileList_InBackgroundThread();
        }

        private AutoResetEvent m_saveLocalFileListEvent = new AutoResetEvent(false);
        private Thread m_saveLocalFileListThread = null;

        private COSFileList m_tmpSaveFileList = new COSFileList();
        private volatile bool m_IsThreadRunning = true;
        private void SaveLocalFileList_InBackgroundThread()
        {
            if (m_saveLocalFileListThread == null || m_saveLocalFileListThread.ThreadState == (ThreadState.Background | ThreadState.Stopped))
            {
                Thread thread = new Thread(SaveLocalFileList);
                if (GetType().Name.Contains("AB"))
                    thread.Name = "COS_ABResSave";
                else
                    thread.Name = "COS_ResSave";
                thread.IsBackground = true;
#if ENABLE_MANAGED_BIND_CORE
                ThreadHelperUtils.SetManagedThreadAffinity(thread, false);
#endif
                m_saveLocalFileListThread = thread;

                m_saveLocalFileListThread.Start();

                Diagnostic.Log(thread.Name + " Start Thread");
            }

            //Diagnostic.Log("[SaveLocalFileList_InBackgroundThread] m_localFileList: " + m_localFileList.FileDict.Count + " m_saveLocalFileListThread IsAlive: "
            //    + m_saveLocalFileListThread.IsAlive
            //     + " IsThreadPoolThread: " + m_saveLocalFileListThread.IsThreadPoolThread
            //      + " ManagedThreadId: " + m_saveLocalFileListThread.ManagedThreadId
            //       + " ThreadState: " + m_saveLocalFileListThread.ThreadState);
            m_saveLocalFileListEvent.Set();
        }

        private volatile bool ms_closeSubThread = false;

        public void CloseSaveThread()
        {
            if (m_saveLocalFileListThread != null)
            {
                ms_closeSubThread = true;
                m_saveLocalFileListEvent.Set();
            }
        }

        /// <summary>
        /// 这个序列化非常费时 特别是在大规模下载的时候 做成多线程异步进行
        /// </summary>
        private void SaveLocalFileList()
        {
            try
            {
                var filePath = FileManager.GetAbsolutePath(COSGlobalDefine.LOCAL_FILELIST_FILENAME);

                var dir = Path.GetDirectoryName(filePath);

                while (m_IsThreadRunning)
                {
                    m_saveLocalFileListEvent.WaitOne();

                    if (ms_closeSubThread)
                        break;

                    lock (m_localFileList)
                    {
                        if (m_tmpSaveFileList.FileDict != null)
                            m_tmpSaveFileList.FileDict.Clear();
                        else
                            m_tmpSaveFileList.FileDict = new TKDictionary<string, COSFileInfo>();
                        foreach (var item in m_localFileList.FileDict)
                        {
                            m_tmpSaveFileList.FileDict.Add(item.Key, item.Value);
                        }
                    }

                    if (!Directory.Exists(dir))
                        Directory.CreateDirectory(dir);
                    using (var localFileStream = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.Write))
                    {
                        localFileStream.Seek(0, SeekOrigin.Begin);
                        m_tmpSaveFileList.WriteTo(localFileStream);
                        m_tmpSaveFileList.FileDict.Clear();
                        localFileStream.Flush(true);
                    }

                    Thread.Sleep(1);
                }
            }
            catch (Exception ex)
            {
                Diagnostic.Warn(GetType().Name + ".SaveLocalFileList has exception: " + ex.ToString());
            }

            ms_closeSubThread = false;
            Diagnostic.Log(GetType().Name + ".SaveLocalFileList Thread stopped!");
        }

        private void OnNetResourceBackgroundDownloadFinished(COSDownloadRequest request)
        {
            if (request.IsSuccess)
            {
                UpdateLocalFileList(request.RelativePath, request.FileInfo);

                if (request.FileInfo != null)
                    COS.DiskSpaceManager.OnDownloadFinished(request.FileInfo.Size);
            }
        }

        private void OnNetResourceProgress(string path, int cur, int total)
        {
            if (m_requestDict.TryGetValue(path, out COSResourceRequest request))
            {
                request.OnProgress(path, cur, total);
            }
        }

        private void OnNetResourceLoadFinished(COSDownloadRequest request)
        {
            if (request.IsSuccess)
            {
                UpdateLocalFileList(request.RelativePath, request.FileInfo);

                if (request.FileInfo != null)
                    COS.DiskSpaceManager.OnDownloadFinished(request.FileInfo.Size);
            }

            if (m_requestDict.TryGetValue(request.RelativePath, out COSResourceRequest r))
            {
                if (r.loadMode == COSLoadMode.OnlyDownload)
                {
                    COSResource resource = COSPool.Get<COSResource>();
                    resource.Init(request.RelativePath, null, request.FileInfo, request.IsSuccess);
                    m_requestDict.Remove(request.RelativePath);
                    r.OnFinished(resource);
                    //r.onLoadFinished?.Invoke(resource);
                    COSPool.Free(r);
                    COSPool.Free(resource);
                }
                else
                {
                    FileManager.LoadFile(request.RelativePath, request.FileInfo, OnLocalResourceLoadFinished, request.Timeout);
                }
            }
        }

        private void OnLocalResourceLoadFinished(COSFileRequest request)
        {
            COSResource resource = COSPool.Get<COSResource>();
            resource.Init(request.relativePath, request.GetData(), request.info, request.IsSuccess, request.Length);
            try
            {
                if (m_requestDict.TryGetValue(request.relativePath, out COSResourceRequest r))
                {
                    m_requestDict.Remove(request.relativePath);
                    r.OnFinished(resource);
                    COSPool.Free(r);
                }
            }
            catch (Exception ex)
            {
                COSLog.Error("[" + GetType() + ".OnLocalResourceLoadFinished Faild] {0} onfinished callback faild! \r\n{1}", request.relativePath, ex.ToString());
            }
            COSPool.Free(resource);
        }

        public bool CanDownloadInBackground()
        {
            if (InitStatus != COSInitStatus.Success)
                return false;

            // 手机空间低于100M也不要缓存了
            if (!COS.DiskSpaceManager.CanCache())
                return false;

            if (COS.GetNetworkType() != NetworkReachability.ReachableViaLocalAreaNetwork)
                return false;

            if (DownloadManager.IsBusy())
                return false;

            return true;
        }

        public bool DownloadInBackground(string relativePath, Action<COSDownloadRequest> onFinished)
        {
            relativePath = COSFileManager.FormatFileName(relativePath);

            if (!CanDownloadInBackground())
                return false;

            var status = GetResourceStatus(relativePath, out COSFileInfo fileInfo);
            if (status != COSFileStatus.Vaild && fileInfo != null)
            {
                var r = DownloadManager.DownloadFile(relativePath, fileInfo, null, OnNetResourceBackgroundDownloadFinished, GetBlockSize(), 1, false);
                r.AddFinishedCallback(onFinished);
                return true;
            }
            return false;
        }

        protected bool LoadResourceInLocalImpl(COSResourceRequest request)
        {
            string resourceName = request.resName;
            COSLog.Log(string.Format("[" + GetType().Name + "] 使用本地模式加载 {0}", resourceName));
            var success = GetResourceStatusInLocal(resourceName, out COSFileInfo fileInfo);
            if (success && fileInfo != null && request.loadMode != COSLoadMode.OnlyDownload)
            {
                FileManager.LoadFile(resourceName, fileInfo, OnLocalResourceLoadFinished, request.maxTime);
                return true;
            }

            return false;
        }

        protected bool LoadResourceImpl(COSResourceRequest request)
        {
            string resourceName = request.resName;
            var status = GetResourceStatus(resourceName, out COSFileInfo fileInfo);
            if (status != COSFileStatus.Vaild && fileInfo != null)
            {
                if (request.loadMode != COSLoadMode.OnlyLoad)
                {
                    // 剩余空间需要在外部被正确设置
                    if (!COS.DiskSpaceManager.CanDownload(fileInfo.Size))
                    {
                        COSLog.Error(string.Format("[" + GetType().Name + "COS下载文件 {0} 失败，磁盘空间不足", resourceName));
                        //return false;
                    }

                    DownloadManager.DownloadFile(resourceName, fileInfo, OnNetResourceProgress, OnNetResourceLoadFinished, GetBlockSize(), request.maxTime, request.needPriorityRequest);
                    return true;
                }
                else
                {
                    return false;
                }

            }
            else if (fileInfo != null)
            {
                if (request.loadMode != COSLoadMode.OnlyDownload)
                {
                    FileManager.LoadFile(resourceName, fileInfo, OnLocalResourceLoadFinished, request.maxTime);
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                COSLog.Error("[" + GetType().Name + $"  Functoin:LoadResource failed status:{status} fileInfo is null:{fileInfo == null} resourceName:{resourceName}");
                return false;
            }
        }


        public bool CancelRequestResource(string resourceName, Action<string, int, int> onProgerss, Action<COSResource> onLoadFinished, object owner)
        {
            resourceName = COSFileManager.FormatFileName(resourceName);

            if (m_requestDict.TryGetValue(resourceName, out COSResourceRequest r))
            {
                if (r.RemoveRequest(onProgerss, onLoadFinished, owner))
                {
                    DownloadManager.Cancel(resourceName);
                    FileManager.Cancel(resourceName);
                    m_requestDict.Remove(resourceName);
                    COSPool.Free(r);
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 加载资源（自动判断从服务器还是客户端加载资源）
        /// </summary>
        /// <param name="resourceName">资源名</param>
        /// <param name="onLoadFinished">下载回调</param>
        /// <param name="onLoadFinished">加载成功回调</param>
        /// <param name="maxTime">加载失败超时时间（秒）</param>
        public void RequestResource(string resourceName, Action<string, int, int> onProgerss, Action<COSResource> onLoadFinished, object owner, COSLoadMode loadMode = COSLoadMode.DownloadAndLoad, int maxTime = 5, bool needPriorityRequest = true)
        {
            if (owner == null)
            {
                Diagnostic.Error("[{0}.RequestResource] resourceName: {1} owner is null. ingore", GetType().Name, resourceName);
                return;
            }

            // 这里规范一下文件名
            resourceName = COSFileManager.FormatFileName(resourceName);

            // 如果已经请求过了。只需要处理一下回调
            if (m_requestDict.TryGetValue(resourceName, out COSResourceRequest r))
            {
                r.AddRequest(onProgerss, onLoadFinished, owner);

                // 如果请求下载的过程中，新的请求变成了请求下载并且加载，那这里需要改变一下请求模式，让下载完成以后加载这个资源出来
                if (r.loadMode == COSLoadMode.OnlyDownload && loadMode == COSLoadMode.DownloadAndLoad)
                    r.loadMode = loadMode;


                // 如果是优先下载、加载 则需要通知下载器提高优先级
                if (needPriorityRequest)
                {
                    DownloadManager.TryInsertToPriorityRequestList(resourceName);
                }

                return;
            }

            // 如果还没请求过，则需要一个全新的请求。
            var request = COSPool.Get<COSResourceRequest>();
            request.Init(resourceName, onProgerss, onLoadFinished, loadMode, maxTime, needPriorityRequest, owner);

            if (InitStatus != COSInitStatus.Success)
            { // 还没初始化好 先加入队列中 等初始化好再加载
                if (InitStatus != COSInitStatus.Initing)
                    Init(null);
                m_requestDict.Add(resourceName, request);
            }
            else
            {
                // 实际调用LoadResourceImpl去做真实的请求
                if (LoadResourceImpl(request))
                {
                    TryCheckVersion();      // 加载图标的时候 顺手检测一下版本是否过时了 如果过时了 就无感刷新一下
                    m_requestDict.Add(resourceName, request);
                }
                else
                {
                    NotifyRequestFaild(request);
                    COSPool.Free(request);
                }
            }
        }

        //public int GetNeedDownloadSize()
        //{
        //    int size = 0;
        //    FetchNeedDownloadFile((_, info) =>
        //    {
        //        size += info.Size;
        //    });
        //    return size;
        //}

        public List<string> GetNeedUpdateFileList()
        {
            List<string> list = new List<string>();
            FetchNeedUpdateFile((name, _) =>
            {
                list.Add(name);
            });
            return list;
        }

        private void FetchNeedUpdateFile(Action<string, COSFileInfo> action)
        {
            if (m_networkFileList != null && m_networkFileList.FileDict != null)
            {
                foreach (var fileInfo in m_networkFileList.FileDict)
                {
                    if (fileInfo.Value == null)
                    {
                        continue;
                    }
                    if (GetResourceStatus(fileInfo.Key, out _) == COSFileStatus.NeedUpdate)
                    {
                        action?.Invoke(fileInfo.Key, fileInfo.Value);
                    }
                }
            }
        }

        public bool GetResourceStatusInLocal(string resourceName, out COSFileInfo fileInfo)
        {
            resourceName = COSFileManager.FormatFileName(resourceName);

            if (m_localFileList.FileDict != null
                   && m_localFileList.FileDict.TryGetValue(resourceName, out COSFileInfo localFileInfo)
                   && FileManager.IsFileExist(resourceName))
            {
                fileInfo = localFileInfo;
                return true;
            }

            fileInfo = null;
            return false;
        }

        public COSFileStatus GetResourceStatus(string resourceName, out COSFileInfo fileInfo, bool checkCorrect = true)
        {
            resourceName = COSFileManager.FormatFileName(resourceName);

            if (m_networkFileList.FileDict != null && m_networkFileList.FileDict.TryGetValue(resourceName, out COSFileInfo networkFileInfo))
            {
                if (m_localFileList.FileDict != null
                    && m_localFileList.FileDict.TryGetValue(resourceName, out COSFileInfo localFileInfo))
                {
                    if (checkCorrect && !FileManager.IsFileCorrect(resourceName, localFileInfo.Size))
                    {
                        fileInfo = networkFileInfo;
                        return COSFileStatus.NeedDownload;          // 客户端文件大小不对 需要下载
                    }
                    else
                    {
                        if (localFileInfo.Md5 == networkFileInfo.Md5)
                        {
                            fileInfo = localFileInfo;
                            return COSFileStatus.Vaild;
                        }
                        else
                        {
                            fileInfo = networkFileInfo;
                            return COSFileStatus.NeedUpdate;        // 客户端版本不对 需要更新
                        }
                    }
                   
                }
                fileInfo = networkFileInfo;
                return COSFileStatus.NeedDownload;          // 客户端版本不存在 需要下载
            }
            fileInfo = null;
            return COSFileStatus.NotExist;
        }

        public void NeedUpdate(string path, Action<string, COSFileStatus, COSFileInfo> onResult)
        {
            path = COSFileManager.FormatFileName(path);

            if (InitStatus != COSInitStatus.Success)
            {
                Init((status) =>
                {
                    if (status == COSInitStatus.Success)
                        onResult?.Invoke(path, GetResourceStatus(path, out COSFileInfo fileInfo), fileInfo);
                    else
                        onResult?.Invoke(path, COSFileStatus.NeedDownload, null);
                });
            }
            else
            {
                onResult?.Invoke(path, GetResourceStatus(path, out COSFileInfo fileInfo), fileInfo);
            }
        }

        public void Search(Predicate<string> condition, Action<List<string>> onResult)
        {
            if (InitStatus != COSInitStatus.Success)
            {
                Init((status) =>
                {
                    if (status == COSInitStatus.Success)
                    {
                        onResult?.Invoke(SearchImpl(condition));
                    }

                    else
                        onResult?.Invoke(null);
                });
            }
            else
            {
                onResult?.Invoke(SearchImpl(condition));
            }
        }

        private List<string> SearchImpl(Predicate<string> condition)
        {
            if (m_networkFileList == null || condition == null)
                return null;
            List<string> list = new List<string>();
            foreach (var item in m_networkFileList.FileDict)
            {
                if (condition(item.Key))
                {
                    list.Add(item.Key);
                }
            }
            return list;
        }

        #endregion


        public bool DeleteLocalCache(string resourceName)
        {
            if (m_localFileList != null && m_localFileList.FileDict != null)
            {
                if (m_localFileList.FileDict.ContainsKey(resourceName))
                {
                    lock (m_localFileList)
                        m_localFileList.FileDict.Remove(resourceName);

                    FileManager.DeleteFile(resourceName);

                    SaveLocalFileList_InBackgroundThread();


                    Diagnostic.Log("[" + GetType().Name + ".DeleteLocalCache] 删除本地缓存: " + resourceName);
                    return true;
                }
            }
            return false;
        }

        private void TryCheckVersion()
        {
            //if (Time.time - m_lastCheckVersionTime > COSGlobalDefine.VERSION_CHECK_INTERVAL)
            //{
            //    m_lastCheckVersionTime = Time.time;

            //    DownloadManager.DownloadVersion(resVersionPath, OnDownloadVersionFinished, 5);
            //}
        }

        public bool AutoClearDiskCache()
        {
            // 清理过期的文件
            if (m_localFileList != null && m_localFileList.FileDict != null && InitStatus == COSInitStatus.Success)
            {
                int deleteCount = 0;
                List<string> deleteList = null;
                foreach (var item in m_localFileList.FileDict)
                {
                    if (!m_networkFileList.FileDict.TryGetValue(item.Key, out COSFileInfo networkFileInfo)
                        || networkFileInfo.Md5 != item.Value.Md5 || networkFileInfo.Size != item.Value.Size
                        || networkFileInfo.Crc1 != item.Value.Crc1 || networkFileInfo.Crc2 != item.Value.Crc2)
                    {
                        if (networkFileInfo != null)
                            COSLog.Log($"[{GetType().Name}[AutoClearDiskCache] delete path: {item.Key} \nc_md5:{item.Value.Md5} s_md5:: {networkFileInfo.Md5}\nc_size:{item.Value.Size} s_size:{networkFileInfo.Size}\nc_crc1:{item.Value.Crc1} s_crc1:{networkFileInfo.Crc1}\nc_crc2:{item.Value.Crc2} s_crc2:{networkFileInfo.Crc2}");
                        else
                            COSLog.Log($"[{GetType().Name}[AutoClearDiskCache] delete path: {item.Key} networkFileInfo is null.");
                        FileManager.DeleteFile(item.Key);

                        if (deleteList == null)
                            deleteList = new List<string>();
                        deleteList.Add(item.Key);
                        ++deleteCount;
                    }
                }

                if (deleteList != null)
                {
                    lock (m_localFileList)
                    {
                        for (int i = 0; i < deleteList.Count; ++i)
                        {
                            m_localFileList.FileDict.Remove(deleteList[i]);
                        }
                    }
                    SaveLocalFileList_InBackgroundThread();
                }

                Diagnostic.Log("[" + GetType().Name + ".AutoClearDiskCache] 清理过期文件: " + deleteCount);
                return deleteCount > 0;
            }
            return false;
        }

        public void WriteVersionInfo(Stream writer)
        {
            // 先读resourceVersion，因为resourceVersion变化比较频繁，命中了就不用继续读下去了
            writer.WriteOptimizeInt(resourceVersion);
            writer.WriteOptimizeInt(iterationId);
            writer.WriteString(environment);
            writer.WriteString(resVersionPath);
            writer.WriteString(ResourceLevel);
        }

        public bool IsVersionChange(Stream reader)
        {
            int resVer = reader.ReadOptimizeInt();
            if (resVer != resourceVersion)
                return true;

            int iterId = reader.ReadOptimizeInt();
            if (iterId != iterationId)
                return true;

            string env = reader.ReadString();
            if (env != environment)
                return true;

            string resVerPath = reader.ReadString();
            if (resVerPath != resVersionPath)
                return true;

            string resLevel = reader.ReadString();
            if (resLevel != ResourceLevel)
                return true;

            return false;
        }
    }
}
