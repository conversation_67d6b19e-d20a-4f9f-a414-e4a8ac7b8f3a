using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TKFrame.COSResource
{
    public static class COSStringCache
    {
        private static TKDictionary<string, string> ms_fileNameDict = new TKDictionary<string, string>();

        private static TKDictionary<string, string> ms_remapAssetbundleDict = new TKDictionary<string, string>();

        private static string[] noSupportFileNameChars = new string[4] { " ", "’", "'", "`" };
        
        public static void ClearAllCache()
        {
            ms_fileNameDict.Clear();
            ms_remapAssetbundleDict.Clear();
        }

        /// <summary>
        /// 规范化文件名
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static string FormatFileName(string fileName)
        {
            if (ms_fileNameDict.TryGetValue(fileName, out string formatFileName))
                return formatFileName;

            for (int i = 0; i < noSupportFileNameChars.Length; ++i)
            {
                var c = noSupportFileNameChars[i];
                if (fileName.Contains(c))
                {
                    formatFileName = fileName.Replace(c, ""); // 不支持这种文件名
                    ms_fileNameDict.Add(fileName, formatFileName);
                    return formatFileName;
                }
            }

            return fileName;
        }

        /// <summary>
        /// 规范化AB名
        /// </summary>
        /// <param name="assetBundleName"></param>
        /// <returns></returns>
        public static string RemapAssetbundle(string assetBundleName)
        {
            if (ms_remapAssetbundleDict.TryGetValue(assetBundleName, out string remapAssetbundleName))
                return remapAssetbundleName;

            remapAssetbundleName = ToLower(assetBundleName);

            if (remapAssetbundleName.EndsWith(".unity3d"))
                remapAssetbundleName = remapAssetbundleName.Replace(".unity3d", ".cos");

            if (!remapAssetbundleName.EndsWith(".cos"))
                remapAssetbundleName += ".cos";

            if (!remapAssetbundleName.StartsWith("cos/"))
                remapAssetbundleName = "cos/" + remapAssetbundleName;

            ms_remapAssetbundleDict.Add(string.Intern(assetBundleName), string.Intern(remapAssetbundleName));
            return remapAssetbundleName;
        }

        public static bool HasUpperCase(string str)
        {
            if (string.IsNullOrEmpty(str))
                return false;
            for (int i = 0; i < str.Length; i++)
            {
                if (char.IsUpper(str[i]))
                    return true;
            }

            return false;
        }

        public static string ToLower(string str)
        {
            if (HasUpperCase(str))
            {
                //Diagnostic.Warn("[RenameAssetbundle] bundlePath has upper case! " + str);
                return str.ToLower();
            }

            return str;
        }
    }
}
