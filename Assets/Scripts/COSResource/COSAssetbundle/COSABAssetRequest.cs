using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame.COSResource
{
    public class COSABAssetRequest : ICOSPoolItem//, IReleaseList
    {
        public enum Status
        {
            None,
            Success,
            GetDownloaderFaild,
            CheckUpdateFaild,
            DownloadFaild,
            LoadAssetbundleFaild,
            LoadAssetFaild,
        }

        public string bundlePath;
        public string assetName;
        public List<IReleaseList> refList = new List<IReleaseList>();
        //private Action<string, string, Status, UnityEngine.Object> onFinished = null;

        //private float m_startLoadTime = 0.0f;       // 下载+加载时间
        //private float m_startRealLoadTime = 0.0f;   // 纯加载时间

        private AssetBundleRequest m_request = null;

        public Status status { get; private set; } = Status.None;
        public UnityEngine.Object[] Objects { get; private set; }
        //public List<IRefCounter> AutoReleaseList { get; set; }


        private Action<COSABAssetRequest.Status, COSBundleAsset> m_onFinished;

        public event Action<COSABAssetRequest.Status, COSBundleAsset> onFinished
        {
            add 
            { 
                m_onFinished = (Action<COSABAssetRequest.Status, COSBundleAsset>)Delegate.Combine(m_onFinished, value); 
            }
            remove
            {
                if (m_onFinished != null)
                {
                    m_onFinished = (Action<COSABAssetRequest.Status, COSBundleAsset>)Delegate.Remove(m_onFinished, value);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bundlePath"></param>
        /// <param name="assetName">如果传入的assetName是空的，说明需要加载所有的资源</param>
        public void SetPath(string bundlePath, string assetName)
        {
            this.bundlePath = bundlePath;
            this.assetName = assetName;
        }

        public void AddFinishedCallback(Action<Status, COSBundleAsset> onFinished)
        {
            this.onFinished += onFinished;
        }

        public void RemoveFinishedCallback(Action<Status, COSBundleAsset> onFinished)
        {
            this.onFinished -= onFinished;
        }

        public void ExceFinishedCallback(COSBundleAsset bundleAsset)
        {
            if (m_onFinished == null)
                return;

            //#if UNITY_EDITOR
            //            COSLog.Log($"[COSABAssetRequest.ExceFinishedCallback]bundlePath: {bundlePath} assetPath: {assetName}");
            //#endif
#if !JK_RELEASE
            UnityEngine.Profiling.Profiler.BeginSample("COSABAssetRequest.ExceFinishedCallback:" + assetName);
#endif
            COSFuncUtil.ExceFunc("COSABAssetRequest.ExceFinishedCallback", m_onFinished, status, bundleAsset);
#if !JK_RELEASE
            UnityEngine.Profiling.Profiler.EndSample();
#endif
            //onFinished?.Invoke(bundlePath, assetName, status, Object);
        }

        public void SetLoadFaild(Status status)
        {
            Diagnostic.Log("[xiaobai]load asset faild. assetbundle: {0} assetName: {1} status: {2} frame: {3}", bundlePath, assetName, status, Time.frameCount);
            this.status = status;
        }

        public void Load()
        {
            //#if UNITY_EDITOR
            //            COSLog.Log($"[COSABAssetRequest.Load]bundlePath: {bundlePath} assetPath: {assetName}");
            //#endif
            //m_startLoadTime = Time.time;
            //Diagnostic.Log("[xiaobai]load asset start. assetbundle: {0} assetName: {1} status: {2} frame: {3}", bundlePath, assetName, status, Time.frameCount);
            COS.ABManager.GetAssetBundleDownloader(bundlePath, OnGetDownloaderFirst);
        }

        private void OnGetDownloaderFirst(COSPackageDownloader downloader)
        {
//#if UNITY_EDITOR
//            COSLog.Log($"[COSABAssetRequest.OnGetDownloaderFirst]bundlePath: {bundlePath} assetPath: {assetName} downloader: {downloader != null}");
//#endif
            if (downloader != null)
            {
                OnGetSize(downloader.GetDownloadSize(), downloader.state == COSPackageDownloader.State.Inited);
            }
            else
            {
                SetLoadFaild(Status.GetDownloaderFaild);
            }
        }

        private void OnGetSize(int size, bool success)
        {
//#if UNITY_EDITOR
//            COSLog.Log($"[COSABAssetRequest.OnGetSize]bundlePath: {bundlePath} assetPath: {assetName} size: {size} success: {success}");
//#endif
            if (success)
            {
                if (size == 0)
                {
                    // 不需要更新 直接加载
                    TryLoadFromLocal();
                }
                else
                {
                    // 下载
                    TryDownload();
                }
            }
            else
            {
                // 检测更新失败
                SetLoadFaild(Status.CheckUpdateFaild);
            }
        }

        public void TryDownload()
        {
            //#if UNITY_EDITOR
            //            COSLog.Log($"[COSABAssetRequest.TryDownload]bundlePath: {bundlePath} assetPath: {assetName}");
            //#endif
            //Diagnostic.Log("[xiaobai]load asset from net. download first. assetbundle: {0} assetName: {1} status: {2} frame: {3}", bundlePath, assetName, status, Time.frameCount);
            COS.ABManager.GetAssetBundleDownloader(bundlePath, OnGetDownloader);
        }

        private void OnGetDownloader(COSPackageDownloader downloader)
        {
//#if UNITY_EDITOR
//            COSLog.Log($"[COSABAssetRequest.OnGetDownloader]bundlePath: {bundlePath} assetPath: {assetName}");
//#endif
            downloader.AddFinishedCallback(OnDownloadFinished);
            downloader.Download();
        }

        private void OnDownloadFinished(COSPackageDownloader downloader, bool success)
        {
//#if UNITY_EDITOR
//            COSLog.Log($"[COSABAssetRequest.OnDownloadFinished]bundlePath: {bundlePath} assetPath: {assetName} success: {success}");
//#endif

            if (success)
            {
                //Diagnostic.Log("[xiaobai]download asset finished. assetbundle: {0} assetName: {1} status: {2} frame: {3}", bundlePath, assetName, status, Time.frameCount);
                TryLoadFromLocal();
            }
            else
            {
                SetLoadFaild(Status.DownloadFaild);
            }
        }

        public void TryLoadFromLocal()
        {
            //#if UNITY_EDITOR
            //            COSLog.Log($"[COSABAssetRequest.TryLoadFromLocal]bundlePath: {bundlePath} assetPath: {assetName}");
            //#endif
            //m_startRealLoadTime = Time.time;
            //Diagnostic.Log("[xiaobai]load asset from local. assetbundle: {0} assetName: {1} status: {2} frame: {3}", bundlePath, assetName, status, Time.frameCount);
            COS.ABManager.LoadAssetBundle(bundlePath, OnAssetBundleLoaded/*, this*/); //TODO
        }

        private void OnAssetBundleLoaded(string abName, AssetBundle ab, bool success)
        {
            //float realLoadTime = Time.time - m_startRealLoadTime;
            //if (realLoadTime > 0.5f)
            //{
            //    Diagnostic.Warn($"[COSABAssetRequest.OnAssetBundleLoaded]bundlePath: {bundlePath} assetPath: {assetName} success: {success} realLoadTime: {realLoadTime}s loadTime: {Time.time - m_startLoadTime}s");
            //}
//#if UNITY_EDITOR
//            COSLog.Log($"[COSABAssetRequest.OnAssetBundleLoaded]bundlePath: {bundlePath} assetPath: {assetName} success: {success}");
//#endif
            if (success)
            {
                if (!string.IsNullOrEmpty(assetName))
                {
                    m_request = ab.LoadAssetAsync(assetName);
                    if (m_request == null)
                    {
                        var obj = ab.LoadAsset(assetName);
                        status = obj != null ? Status.Success : Status.LoadAssetFaild;
                        if (status == Status.Success)
                            Objects = new UnityEngine.Object[1] { obj };
                    }
                }
                else
                {
                    m_request = ab.LoadAllAssetsAsync();
                    if (m_request == null)
                    {
                        Objects = ab.LoadAllAssets();
                        status = Objects != null && Objects.Length != 0 ? Status.Success : Status.LoadAssetFaild;
                    }
                }
            }
            else
            {
                // 加载AB失败
                SetLoadFaild(Status.LoadAssetbundleFaild);
            }
        }

        public bool IsDone()
        {
            return status != Status.None;
        }

        public void RunTick()
        {
            if (m_request != null)
            {
                if (m_request.isDone)
                {
                    if (!string.IsNullOrEmpty(assetName))
                    {
                        var obj = m_request.asset;
                        if (obj == null)
                        {
                            var ab = COS.ABManager.GetLoadedAssetbundle(bundlePath);
                            if (ab != null)
                            {
                                obj = ab.LoadAsset(assetName);
                            }
                        }
                        m_request = null;
                        status = obj != null ? Status.Success : Status.LoadAssetFaild;
                        if (status == Status.Success)
                            Objects = new UnityEngine.Object[1] { obj };
                    }
                    else
                    {
                        Objects = m_request.allAssets;
                        if (Objects == null)
                        {
                            var ab = COS.ABManager.GetLoadedAssetbundle(bundlePath);
                            if (ab != null)
                            {
                                Objects = ab.LoadAllAssets();
                            }
                        }

                        m_request = null;
                        status = Objects != null ? Status.Success : Status.LoadAssetFaild;
                    }

                    //#if UNITY_EDITOR
                    //                    COSLog.Log($"[COSABAssetRequest.RunTick]bundlePath: {bundlePath} assetPath: {assetName} load done! status: {status}");
                    //#endif
                }
            }
        }

        public void Reset()
        {
            bundlePath = null;
            assetName = null;
            m_onFinished = null;
            refList.Clear();
            Objects = null;
            status = Status.None;

            //this.DisposeReleaseList();
        }
    }
}