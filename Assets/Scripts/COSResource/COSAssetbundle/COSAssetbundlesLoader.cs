using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame.COSResource
{
    public class COSAssetbundlesLoader : ICOSPoolItem
    {
        private string m_assetbundle;
        private List<string> m_allAssetbundles = new List<string>();

        private List<string> m_failds = new List<string>();
        private HashSet<string> m_loadings = new HashSet<string>();

        private Action<string, AssetBundle, bool> onFinished;

        private COSAssetbundleLoader m_loader;
        //private IReleaseList m_releaseList;

        public void Init(COSAssetbundleLoader loader, string assetbundle, string[] dependencys/*, IReleaseList releaseList*/)
        {
            m_loader = loader;
            //m_releaseList = releaseList;

            m_assetbundle = assetbundle;

            m_allAssetbundles.Clear();
            m_allAssetbundles.Add(assetbundle);
            if (dependencys != null)
                m_allAssetbundles.AddRange(dependencys);
        }

        public void AddFinishedCallback(Action<string, AssetBundle, bool> onFinished)
        {
            this.onFinished = onFinished;
        }

        public void Load()
        {
            var resManager = COS.ABManager.ResManager;
            if (resManager.InitStatus != COSInitStatus.Success)
            {
                resManager.Init((status) =>
                {
                    if (status == COSInitStatus.Success)
                    {
                        LoadImpl();
                    }
                    else
                    {
                        OnFinished(false);
                    }
                });
            }
            else
            {
                LoadImpl();
            }
        }

        private void OnFinished(bool success)
        {
            try
            {
                COSFuncUtil.ExceFunc("COSAssetbundleLoader.OnFinished", onFinished, m_assetbundle, m_loader.Get(m_assetbundle), success);
                //onFinished?.Invoke(m_assetbundle, m_loader.Get(m_assetbundle), success);
            }
            catch(Exception ex)
            {
                Diagnostic.Error("COS.AssetbundlesLoader.onFinished exception! " + ex.ToString());
            }
            finally
            {
                COSPool.Free(this);
            }
        }

        private void LoadImpl()
        {
#if UNITY_EDITOR
            COSLog.Log($"[COSAssetbundlesLoader.LoadImpl]m_assetbundle: {m_assetbundle}");
#endif

            var resManager = COS.ABManager.ResManager;
            for (int i = 0; i < m_allAssetbundles.Count; ++i)
            {
                var ab = m_allAssetbundles[i];
                var status = resManager.GetResourceStatus(ab, out COSFileInfo fileInfo);
                if (status == COSFileStatus.NeedUpdate || status == COSFileStatus.NeedDownload)
                {
                    OnFinished(false);
                    return;
                }
                else if (status == COSFileStatus.NotExist)
                {
                    OnFinished(false);
                    return;
                }

                //if (m_loader.Get(ab) == null)
                m_loadings.Add(ab);
            }

            if (m_loadings.Count > 0)
            {
                List<string> list = m_loadings.ToList();
                foreach (var ab in list)
                {
                    m_loader.Load(ab, OnLoadFinished);
                }
            }
            else
            {
                OnFinished(true);
            }
        }

        private void OnLoadFinished(string path, AssetBundle assetbundle)
        {
            m_loadings.Remove(path);
            if (assetbundle == null)
                m_failds.Add(path);

            if (m_loadings.Count == 0)
            {
                OnFinished(m_failds.Count == 0);
            }
        }

        public void Reset()
        {
            m_assetbundle = null;
            m_allAssetbundles.Clear();

            onFinished = null;

            m_loadings.Clear();
            m_failds.Clear();

            m_loader = null;
            //m_releaseList = null;
        }
    }
}
