using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;

namespace TKFrame.COSResource
{
    /// <summary>
    /// AB包管理器 
    /// 依赖加载等等
    /// </summary>
    public class COSAssetBundleManager : TKBehaviour
    {
        private COSAssetbundleLoader m_loader = new COSAssetbundleLoader();
        private COSABResourceManager m_resManager = null;

        private bool m_initializing = false;
        private Action<bool> m_onInitialized = null;
        private AssetBundleManifest m_manifest = null;

        private string m_manifestName = string.Empty;
        //private string m_pathRoot = string.Empty;

        public COSABResourceManager ResManager { get => m_resManager; }
        public bool IsInited { get => m_manifest != null; }
        public bool IsInitializing { get => m_initializing; }

        public string GetManifestName()
        {
            if (string.IsNullOrEmpty(m_manifestName))
                m_manifestName = GetPlatformFolderForAssetBundles();
            return m_manifestName;
        }

        protected override void Awake()
        {
            base.Awake();

            m_resManager = gameObject.AddComponent<COSABResourceManager>();
            m_loader.Init(m_resManager.GetClientPathRoot());
        }

        public void SetEnv(string env)
        {
            m_resManager.environment = env;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            m_loader.UnloadAllAssetbundle();
            m_loader.UnInit();

            if (m_manifest != null)
                Resources.UnloadAsset(m_manifest);
        }

        public void Uninitiaze()
        {
            m_loader.UnloadAllAssetbundle();
            if (m_manifest != null)
                Resources.UnloadAsset(m_manifest);
            m_manifest = null;
        }

        public void Initialize(Action<bool> onInitialized)
        {
            if (m_initializing)
            {
                m_onInitialized += onInitialized;
                return;
            }
            if (m_manifest != null)
            {
                onInitialized?.Invoke(true);
                return;
            }

            m_onInitialized += onInitialized;
            m_initializing = true;
            var manifestPath = GetManifestName();
            //改为 自己判断download
            m_resManager.NeedUpdate(manifestPath, OnManifestUpdateCallback);
        }

        private void OnManifestUpdateCallback(string path, COSFileStatus status, COSFileInfo info)
        {
            if (status == COSFileStatus.NeedUpdate || status == COSFileStatus.NeedDownload)
            {
                m_resManager.RequestResource(GetManifestName(), null, OnMainfestDownloadSuccess, COSLoadMode.OnlyDownload);
            }
            else if (status == COSFileStatus.Vaild)
            {
                LoadMainfestAB(path);
            }
            else
            {
                LoadMainfestFaild();
            }
        }

        private void OnMainfestDownloadSuccess(COSResource res)
        {
            if (res.success)
            {
                LoadMainfestAB(res.resName);
            }
            else
            {
                LoadMainfestFaild();
            }
        }

        private void LoadMainfestAB(string abName)
        {
            m_loader.Load(abName, OnLoadManifestAB/*, this*/);
        }

        private void OnLoadManifestAB(string abName, AssetBundle ab)
        {
            //m_manifest = ab?.LoadAsset<AssetBundleManifest>("AssetBundleManifest");
            ////StartCoroutine(LoadManifestAssetAsync(ab));

            //bool success = m_manifest != null;
            //if (success)
            //    COSLog.Log("COSAssetBundleManager.LoadMainfest success!");
            //else
            //    COSLog.Error("COSAssetBundleManager.LoadMainfest faild!");
            //m_loader.Unload(abName);

            //m_initializing = false;
            //m_onInitialized?.Invoke(success);
            //m_onInitialized = null;

            StartCoroutine(LoadManifestAssetAsync(abName, ab));
        }

        private IEnumerator LoadManifestAssetAsync(string abName, AssetBundle ab)
        {
            if (ab != null)
            {
                var req = ab.LoadAssetAsync<AssetBundleManifest>("AssetBundleManifest");
                while (!req.isDone)
                    yield return null;
                m_manifest = req.asset as AssetBundleManifest;
            }
            else
            {
                COSLog.Error("COSAssetBundleManager.LoadMainfest faild! ab == null");
            }

            bool success = m_manifest != null;
            if (success)
                COSLog.Log("COSAssetBundleManager.LoadMainfest success!");
            else
                COSLog.Error("COSAssetBundleManager.LoadMainfest faild!");
            m_loader.Unload(abName);

            m_initializing = false;
            m_onInitialized?.Invoke(success);
            m_onInitialized = null;
        }

        private void LoadMainfestFaild()
        {
            m_initializing = false;
            COSLog.Error("COSAssetBundleManager.initialize faild! download faild!");

            m_onInitialized?.Invoke(false);
            m_onInitialized = null;
        }

        public static string RemapAssetbundle(string assetBundleName)
        {
            return COSStringCache.RemapAssetbundle(assetBundleName);
        }

        /// <summary>
        /// 获取这个ab以及所有他依赖的ab
        /// </summary>
        /// <param name="assetBundleName"></param>
        /// <param name="assetbundleList"></param>
        public void GetAllAssetbundlePaths(string assetBundleName, ref HashSet<string> assetbundles, bool checkFileCorrect = true)
        {
            if (m_manifest == null)
            {
                Debug.LogError("Please initialize AssetBundleManifest by calling COSAssetBundleManager.Initialize()");
                return;
            }

            assetBundleName = RemapAssetbundle(assetBundleName);

            if (ResManager.GetResourceStatus(assetBundleName, out _, checkFileCorrect) != COSFileStatus.NotExist)
            {
                if (!assetbundles.Contains(assetBundleName))
                    assetbundles.Add(assetBundleName);

                string[] dependencies = m_manifest.GetAllDependencies(assetBundleName);
                for (int i = 0; i < dependencies.Length; ++i)
                {
                    if (ResManager.GetResourceStatus(dependencies[i], out _, checkFileCorrect) != COSFileStatus.NotExist)
                    {
                        if (!assetbundles.Contains(dependencies[i]))
                            assetbundles.Add(dependencies[i]);
                    }
                    else
                    {
                        Diagnostic.Warn("GetAllAssetbundlePaths path: {0} dependency: {1} not exist!", assetBundleName, dependencies[i]);
                    }
                }
            }
            else
            {
                Diagnostic.Warn("GetAllAssetbundlePaths path: {0} not exist!", assetBundleName);
            }
        }

        public void GetAssetBundleDownloader(string assetBundleName, Action<COSPackageDownloader> onGetDownloader)
        {
            assetBundleName = RemapAssetbundle(assetBundleName);

            if (m_manifest == null)
            {
                Initialize((success) =>
                {
                    if (success)
                    {
                        var downloader = GetAssetBundleDownloaderImpl(assetBundleName, onGetDownloader);
                    }
                    else
                    {
                        onGetDownloader?.Invoke(null);
                    }
                });
            }
            else
            {
                GetAssetBundleDownloaderImpl(assetBundleName, onGetDownloader);
            }
        }

        private COSPackageDownloader GetAssetBundleDownloaderImpl(string assetBundleName, Action<COSPackageDownloader> onGetDownloader)
        {
            COSPackageDownloader downloader = COSPool.Get<COSPackageDownloader>();
            downloader.SetResManager(m_resManager);
            HashSet<string> assets = new HashSet<string>();
            assets.Add(assetBundleName);
            string[] dependencies = m_manifest.GetAllDependencies(assetBundleName);
            for (int i = 0; i < dependencies.Length; ++i)
            {
                if (!assets.Contains(dependencies[i]))
                    assets.Add(dependencies[i]);
            }
            downloader.SetAssets(assets);
            downloader.Initialize(() =>
            {
                onGetDownloader?.Invoke(downloader);
            });
            return downloader;
        }

        public void LoadAssetBundle(string assetBundleName, Action<string, AssetBundle, bool> onFinished/*, IReleaseList releaseList*/)
        {
            assetBundleName = RemapAssetbundle(assetBundleName);
            if (m_manifest == null)
            {
                Initialize((success) =>
                {
                    if (success)
                    {
                        LoadAssetBundleImpl(assetBundleName, onFinished/*, releaseList*/);
                    }
                    else
                    {
                        onFinished?.Invoke(assetBundleName, null, false);
                    }
                });
            }
            else
            {
                LoadAssetBundleImpl(assetBundleName, onFinished/*, releaseList*/);
            }
        }

        // Load AssetBundle and its dependencies.
        private void LoadAssetBundleImpl(string assetBundleName, Action<string, AssetBundle, bool> onFinished)
        {
            if (m_manifest == null)
            {
                Debug.LogError("Please initialize AssetBundleManifest by calling COSAssetBundleManager.Initialize()");
                return;
            }

            // 注意 这里无需判断是否加载了 交给COSAssetbundleLoader去判断，否则引用计数的计算会漏掉
            //var ab = m_loader.Get(assetBundleName);
            //if (ab != null)
            //{
            //    onFinished?.Invoke(assetBundleName, ab, true);
            //    return;
            //}

            UnityEngine.Profiling.Profiler.BeginSample("COSAssetBundleManager.LoadAssetBundle:" + assetBundleName);

            COSAssetbundlesLoader loader = COSPool.Get<COSAssetbundlesLoader>();
            string[] dependencies = m_manifest.GetAllDependencies(assetBundleName);
            loader.Init(m_loader, assetBundleName, dependencies);
            loader.AddFinishedCallback(onFinished);
            loader.Load();

            UnityEngine.Profiling.Profiler.EndSample();
        }

        public AssetBundle LoadAssetbundleSync(string assetBundleName)
        {
            assetBundleName = RemapAssetbundle(assetBundleName);

            if (m_manifest == null)
            {
                Diagnostic.Error("[LoadAssetbundleSync] faild! init manifest first!");
                return null;
            }

            var ab = m_loader.Get(assetBundleName);
            if (ab == null)
                ab = m_loader.LoadSync(assetBundleName);
            return ab;
        }

        public AssetBundle GetLoadedAssetbundle(string assetBundleName)
        {
            if (m_manifest == null)
                return null;

            assetBundleName = RemapAssetbundle(assetBundleName);
            return m_loader.Get(assetBundleName);
        }

        public bool CheckExistAssetInLoadedAssetbundle(string assetBundleName, string assetName)
        {
            if (m_manifest == null)
                return false;

            assetBundleName = RemapAssetbundle(assetBundleName);
            return m_loader.CheckExistAsset(assetBundleName, assetName);
        }

        public void UnloadAssetBundle(string assetBundleName)
        {
            assetBundleName = RemapAssetbundle(assetBundleName);

            if (m_manifest == null)
            {
                Initialize((success) =>
                {
                    if (success)
                    {
                        UnloadAssetBundleImpl(assetBundleName);
                    }
                });
            }
            else
            {
                UnloadAssetBundleImpl(assetBundleName);
            }
        }

        public void UnloadAssetBundleImpl(string assetBundleName)
        {
            m_loader.Unload(assetBundleName);

            string[] dependencies = m_manifest.GetAllDependencies(assetBundleName);

            //Diagnostic.Log("COSAssetBundleManager.Unload ab: " + assetBundleName + "\ndependency: " + string.Join("\n", dependencies));

            for (int i = 0; i < dependencies.Length; ++i)
                m_loader.Unload(dependencies[i]);
        }

        public static string GetPlatformFolderForAssetBundles()
        {
#if UNITY_EDITOR
            return "cos_" + GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget).ToLower();
#else
			return "cos_" +GetPlatformFolderForAssetBundles(Application.platform).ToLower();
#endif
        }

#if UNITY_EDITOR
        public static string GetPlatformFolderForAssetBundles(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.Android:
                    return "Android";
                case BuildTarget.iOS:
                    return "iOS";
                //case BuildTarget.WebPlayer:     //Unity2018.2版本已经废弃
                //    return "WebPlayer";
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return "Windows";
                //case BuildTarget.StandaloneOSXIntel:
                //case BuildTarget.StandaloneOSXIntel64:
                //case BuildTarget.StandaloneOSXUniversal:  //Unity2018.2版本已经废弃
                case BuildTarget.StandaloneOSX:
                    return "OSX";
                // Add more build targets for your own.
                // If you add more targets, don't forget to add the same platforms to GetPlatformFolderForAssetBundles(RuntimePlatform) function.
                default:
                    return null;
            }
        }
#endif

        public static string GetPlatformFolderForAssetBundles(RuntimePlatform platform)
        {
            switch (platform)
            {
                case RuntimePlatform.Android:
                    return "Android";
                case RuntimePlatform.IPhonePlayer:
                    return "iOS";
                //case RuntimePlatform.WindowsWebPlayer:    //Unity2018.2版本已经废弃
                //case RuntimePlatform.OSXWebPlayer:        //Unity2018.2版本已经废弃
                //   return "WebPlayer";
                case RuntimePlatform.WindowsPlayer:
                    return "Windows";
                case RuntimePlatform.OSXPlayer:
                    return "OSX";
                // Add more build platform for your own.
                // If you add more platforms, don't forget to add the same targets to GetPlatformFolderForAssetBundles(BuildTarget) function.
                default:
                    return null;
            }
        }

        public void GetInfo(ICOSConsole sb)
        {
            m_loader.GetInfo(sb);
        }
    }
}
