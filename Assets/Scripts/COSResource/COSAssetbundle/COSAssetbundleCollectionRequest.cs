using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TKFrame.COSResource
{
    public class COSAssetbundleCollectionRequest
    {
        private string m_assetbundle;
        private List<string> m_allAssetbundles = new List<string>();
        private COSAssetbundleLoader m_loader;

        public void SetLoader(COSAssetbundleLoader loader)
        {
            m_loader = loader;
        }

        public void SetAssetbundle(string assetbundle, string[] dependencys)
        {
            m_assetbundle = assetbundle;

            m_allAssetbundles.Clear();
            m_allAssetbundles.Add(assetbundle);
            m_allAssetbundles.AddRange(dependencys);
        }

        public void SetOnProgressCallback()
        {

        }

        public void Load()
        {

        }
    }
}
