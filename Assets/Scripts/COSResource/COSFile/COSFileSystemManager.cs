using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace TKFrame.COSResource
{
    /// <summary>
    /// 文件读写 避免读写冲突
    /// </summary>
    public class COSFileSystemManager
    {
        private TKDictionary<string, COSFileSystem> m_fileSystemDict = new TKDictionary<string, COSFileSystem>();
        private List<string> m_freeFileSystems = new List<string>();
        private float m_checkFreeTime = 0;

        public void Awake()
        {
            COSUpdater.Instance.Regist(Tick);
        }

        public void OnDestroy()
        {
            if (COSUpdater.HasInstance())
                COSUpdater.Instance.UnRegist(Tick);

            lock (m_fileSystemDict)
            {
                foreach (var item in m_fileSystemDict)
                {
                    item.Value.CloseFileStream();
                }
                m_fileSystemDict.Clear();
            }
        }

        protected void Tick()
        {
            if (Time.time - m_checkFreeTime > 60 && m_fileSystemDict.Count > 16)
            {
                m_checkFreeTime = Time.time;
                AutoFreeUnuseFile();
            }
        }

        public COSFileSystem GetOrCreate(string path)
        {
            lock (m_fileSystemDict)
            {
                if (!m_fileSystemDict.TryGetValue(path, out COSFileSystem fs))
                {
                    fs = COSPool.Get<COSFileSystem>();
                    fs.SetPath(path, OnFileDelete);
                    m_fileSystemDict.Add(path, fs);
                }
                return fs;
            }
        }

        public bool CloseFileStream(string path)
        {
            COSFileSystem fs = null;
            lock (m_fileSystemDict)
            {
                m_fileSystemDict.TryGetValue(path, out fs);
            }
            if (fs != null)
            {
                fs.CloseFileStream();
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 用这个方法删除 效率更高
        /// </summary>
        /// <param name="path"></param>
        public bool DeleteFile(string path)
        {
            COSFileSystem fs = null;
            lock (m_fileSystemDict)
            {
                m_fileSystemDict.TryGetValue(path, out fs);
            }

            if (fs == null)
            {
                return COSFileSystem.DeleteFile(path);
            }
            else
            {
                return fs.DeleteFile();
            }
        }

        public void AutoFreeUnuseFile()
        {
            m_freeFileSystems.Clear();
            lock (m_fileSystemDict)
            {
                foreach (var fileSystemPair in m_fileSystemDict)
                {
                    if (!fileSystemPair.Value.IsOpenFileStream())
                    {
                        COSPool.Free(fileSystemPair.Value);
                        m_freeFileSystems.Add(fileSystemPair.Key);
                    }
                }
                for (int i = 0; i < m_freeFileSystems.Count; ++i)
                {
                    m_fileSystemDict.Remove(m_freeFileSystems[i]);
                }
            }
            m_freeFileSystems.Clear();
        }

        private void OnFileDelete(string path)
        {
            Free(path);
        }

        /// <summary>
        /// 用完外界可以主动调用一下free 释放文件handle
        /// </summary>
        /// <param name="path"></param>
        private bool Free(string path)
        {
            lock (m_fileSystemDict)
            {
                if (m_fileSystemDict.TryGetValue(path, out COSFileSystem fs))
                {
                    COSPool.Free(fs);
                    m_fileSystemDict.Remove(path);
                    return true;
                }
            }
            return false;
        }
    }
}
