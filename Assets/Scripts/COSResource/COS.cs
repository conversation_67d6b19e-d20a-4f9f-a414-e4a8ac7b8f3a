using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TKFrame.COSResource;
using UnityEngine;
using ZGameChess;

/// <summary>
/// 所有COS对外的单例集合
/// </summary>
public class COS
{
    private static GameObject m_cosGo = null;
    private static COSResourceManager m_resMgr = null;
    private static COSTextureLoader m_textureLoader = null;
    private static COSAtlasLoader m_atlasLoader = null;
    private static COSAssetBundleManager m_abMgr = null;
    private static COSDiskSpaceManager m_diskSpaceMgr = null;
    private static COSABAssetManager m_abAssetManager = null;
    private static COSFileSystemManager m_fileSystemManager = null;     // 文件资源读写管理器 避免读写冲突
    //private static COSPredownloadManager m_predownloadManager = null;       // 大厅wifi环境下预下载（千人千包）
    private static COSSettingPrefs m_settingPrefs = null;       // COS存储信息
    private static COSTaskManager m_taskManager = null;         // 延时任务管理器
    private static COSHttpClient m_httpClient = null;   // http client内存池
    private static COSHttpWebRequestManager m_httpWebRequestMgr = null;

    // 图标、图集、视频资源管理器 下载和下载的文件管理 版本管理等
    public static COSResourceManager ResManager
    {
        get
        {
            if (m_resMgr == null)
            {
                m_resMgr = TryGetComponent<COSResourceManager>();
                m_resMgr.AddVersionChangedCallback(TextureLoader.OnCOSVersionChanged);
            }
            return m_resMgr;
        }
    }

    // 图片加载器
    public static COSTextureLoader TextureLoader
    {
        get
        {
            if (m_textureLoader == null)
            {
                m_textureLoader = new COSTextureLoader();
                m_textureLoader.Init();
            }
            return m_textureLoader;
        }
    }
    // 图集加载器
    public static COSAtlasLoader AtlasLoader
    {
        get
        {
            if (m_atlasLoader == null)
                m_atlasLoader = new COSAtlasLoader();
            return m_atlasLoader;
        }
    }
    // AB资源加载器 AB资源的下载是在这个管理器内部自己执行的 和ResManager无关
    public static COSAssetBundleManager ABManager
    {
        get
        {
            if (m_abMgr == null)
            {
                m_abMgr = TryGetComponent<COSAssetBundleManager>();
            }
            return m_abMgr;
        }
    }
    public static COSABAssetManager ABAssetManager
    {
        get
        {
            if (m_abAssetManager == null)
            {
                m_abAssetManager = new COSABAssetManager();
                m_abAssetManager.Init();
            }
            return m_abAssetManager;
        }
    }

    // 磁盘空间管理工具
    public static COSDiskSpaceManager DiskSpaceManager
    {
        get
        {
            if (m_diskSpaceMgr == null)
                m_diskSpaceMgr = new COSDiskSpaceManager();
            return m_diskSpaceMgr;
        }
    }

    public static COSFileSystemManager FileSystemManager
    {
        get
        {
            if (m_fileSystemManager == null)
            {
                m_fileSystemManager = new COSFileSystemManager();
                m_fileSystemManager.Awake();
            }
            return m_fileSystemManager;
        }
    }

    public static COSHttpClient HttpClient
    {
        get
        {
            if (m_httpClient == null)
                m_httpClient = new COSHttpClient();
            return m_httpClient;
        }
    }

    public static COSHttpWebRequestManager HttpWebRequestManager
    {
        get
        {
            if (m_httpWebRequestMgr == null)
                m_httpWebRequestMgr = new COSHttpWebRequestManager();
            return m_httpWebRequestMgr;
        }
    }

    //public static COSPredownloadManager PredownloadManager
    //{
    //    get
    //    {
    //        if (m_predownloadManager == null)
    //            m_predownloadManager = new COSPredownloadManager();
    //        return m_predownloadManager;
    //    }
    //}

    public static COSSettingPrefs SettingPrefs
    {
        get
        {
            if (m_settingPrefs == null)
                m_settingPrefs = new COSSettingPrefs();
            return m_settingPrefs;
        }
    }

    public static COSTaskManager TaskManager
    {
        get
        {
            if (m_taskManager == null)
            {
                m_taskManager = new COSTaskManager();
                m_taskManager.Init();
            }
            return m_taskManager;
        }
    }

    public static NetworkReachability GetNetworkType()
    {
        if (SettingPrefs.GetInt(COSSettingPrefs.DISABLE_NETWORK_TAG) == 1)
            return NetworkReachability.NotReachable;
        else if (SettingPrefs.GetInt(COSSettingPrefs.FORCE_DATA_NETWORK_TAG) == 1)
            return NetworkReachability.ReachableViaCarrierDataNetwork;
        else
            return Application.internetReachability;
    }

    private static GameObject GetGameObject()
    {
        if (m_cosGo == null)
        {
            m_cosGo = new GameObject("COS-Root");
            GameObject.DontDestroyOnLoad(m_cosGo);
            COSDelegateInterface.SetDestoryCallback?.Invoke(m_cosGo, OnDestory);
            //var notify = m_cosGo.AddComponent<DestoryNotify>();
            //notify.OnDestoryCallback = OnDestory;
        }
        return m_cosGo;
    }

    private static T TryGetComponent<T>() where T : MonoBehaviour
    {
        var go = GetGameObject();
        var component = go.GetComponent<T>();
        if (component == null)
            component = go.AddComponent<T>();
        return component;
    }

    private static void OnDestory()
    {
        COSFileSystem.CloseChunkThread();
        COSFileSystem.CloseCrcThread();
        COS.SettingPrefs.CloseSaveThread();
        ResManager.CloseSaveThread();
        ABManager.ResManager.CloseSaveThread();

        if (m_httpClient != null)
            m_httpClient.Dispose();

        if (m_fileSystemManager != null)
            m_fileSystemManager.OnDestroy();
    }

    private static void Clear()
    {
        if (m_cosGo != null)
        {
            GameObject.Destroy(m_cosGo);
            m_cosGo = null;
            m_abMgr = null;
            m_resMgr = null;
        }
    }

    /// <summary>
    /// 重新初始化COS
    /// </summary>
    public static void ReinitCOS()
    {
        var cosResMgr = COS.ResManager;
        cosResMgr.Uninit();
        cosResMgr.ClearCache();
        var cosTextureMgr = COS.TextureLoader;
        cosTextureMgr.ClearCache();

        COS.AtlasLoader.ClearAllAtlas();

        var cosABAssetMgr = COS.ABAssetManager;
        cosABAssetMgr.UnloadAllAssetbundle();

        var cosABMgr = COS.ABManager;
        cosABMgr.Uninitiaze();

        cosABMgr.ResManager.Uninit();
        cosABMgr.ResManager.ClearCache();

        cosResMgr.Init(null);

        cosABMgr.Initialize(null);
    }
}
