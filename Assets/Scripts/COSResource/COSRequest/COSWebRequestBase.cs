using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TKFrame.COSResource
{
    public abstract class COSWebRequestBase : ICOSPoolItem
    {
        public const int MAX_RETRY_COUNT = 3;       // 最大重试次数

        protected string m_url;           // 服务器路径
        protected string m_localPath;     // 下载到本地的路径
        protected int m_rangeStart;
        protected int m_rangeEnd;
        protected int m_localTmpSize;     // 本地文件已缓存大小
        protected int m_timeout;
        protected int m_blockSize;

        protected long m_downloadedSize = 0;
        protected bool m_success = false;
        protected bool m_error = false;
        protected Action onProgress;

        protected int m_retryCount = MAX_RETRY_COUNT;

        protected volatile bool m_disposed = false;

        public string LocalPath
        {
            get
            {
                return m_localPath;
            }
        }

        // 已下载的大小
        public long DownloadedSize
        {
            get
            {
                return m_downloadedSize;
            }
        }

        // 要下载的大小
        public long DownloadSize
        {
            get
            {
                int downloadStart = m_rangeStart + m_localTmpSize;
                int downloadEnd = m_rangeEnd;
                return downloadEnd - downloadStart;
            }
        }

        public bool Success
        {
            get
            {
                return m_success;
            }
        }

        public bool IsFinished
        {
            get
            {
                return m_success || m_error;
            }
        }

        public void Init(string url, string localPath, int rangeStart, int rangeEnd, int localTmpSize, int timeout, int blockSize, Action onProgress)
        {
            m_url = url;
            m_localPath = localPath;
            m_rangeStart = rangeStart;
            m_rangeEnd = rangeEnd;
            m_localTmpSize = localTmpSize;
            m_timeout = timeout;
            m_blockSize = blockSize;
            m_downloadedSize = 0;
            m_success = false;
            m_error = false;

            this.onProgress = onProgress;

            m_disposed = false;
        }

        public abstract void SendWebRequest();
        public abstract void RunTick();
        protected abstract void OnPreRetry();
        protected void OnDownloadSuc()
        {
            m_success = true;
            m_error = false;
        }

        protected void OnDownloadFaild()
        {
            COSLog.Error($"[COSHttpWebRequest.OnDownloadFaild]m_request: {m_url} has error. retry: {m_retryCount}");

            // 尝试重新下载
            if (m_retryCount > 0)
            {
                // 下载超时不减少重试次数
                //if (m_request == null || m_request.error != "Request timeout")
                //    --m_retryCount;

                //ResetRequest();

                OnPreRetry();

                m_timeout *= 2;       // 超时时间翻个倍 因为可能是网速太慢
                SendWebRequest();
            }
            else
            {
                m_success = false;
                m_error = true;
            }
        }

        public virtual void Reset()
        {
            m_url = null;
            m_localPath = null;
            m_rangeStart = 0;
            m_rangeEnd = 0;
            m_localTmpSize = 0;
            m_timeout = 0;
            m_blockSize = 0;
            m_downloadedSize = 0;
            m_success = false;
            m_error = false;
            m_retryCount = MAX_RETRY_COUNT;

            onProgress = null;

            m_disposed = true;
        }
    }
}
