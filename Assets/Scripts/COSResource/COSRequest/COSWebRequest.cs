using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;

namespace TKFrame.COSResource
{
    public class COSWebRequest : COSWebRequestBase
    {
        private UnityWebRequest m_request;
        private static float ms_lastProgressRefershTime = 0f;

        public override void SendWebRequest()
        {
            var downloadSize = m_rangeEnd - m_rangeStart;
            if (m_localTmpSize < downloadSize && downloadSize > 0)
            {
                var downloadHandler = new COSDownloadHandler(COSBlockPool.Get(m_blockSize));
                downloadHandler.Init(m_localPath, downloadSize, OnProgress, OnFinished);
                var request = new UnityWebRequest(m_url, UnityWebRequest.kHttpVerbGET, downloadHandler, null);
                int downloadStart = m_rangeStart + m_localTmpSize;
                request.SetRequestHeader("Range", $"bytes={downloadStart}-{m_rangeEnd - 1}");
                request.timeout = m_timeout;
                request.SendWebRequest();
                m_request = request;
            }
            else
            {
                m_downloadedSize = downloadSize;
                m_success = true;
                m_error = false;
            }

            ms_lastProgressRefershTime = Time.realtimeSinceStartup;
        }

        private void OnProgress(long downloadedSize, long totalSize)
        {
            ms_lastProgressRefershTime = Time.realtimeSinceStartup;

            m_downloadedSize = downloadedSize;

            onProgress?.Invoke();
        }

        private void OnFinished(bool success)
        {
            if (success)
            {
                m_success = true;
                m_error = false;
            }
            else
            {
                OnDownloadFaild();
            }
        }

        public override void RunTick()
        {
            if (IsFinished)
                return;
            if (m_request != null)
            {
                if (m_request.isHttpError || m_request.isNetworkError)
                {
                    OnDownloadFaild();
                }
                else if (IsResponseDataTimeout())
                {
                    // 检查是否长时间下载无回包，如果长时间无回包则重新下载
                    COSLog.Log("[COSDownloadRequest.IsResponseDataTimeout] download time out! try restart. path: " + LocalPath + " server_path: " + m_request.url);
                    OnDownloadFaild();
                }
            }
        }

        protected override void OnPreRetry()
        {
            // 下载超时不减少重试次数
            if (m_request == null || m_request.error != "Request timeout")
                --m_retryCount;

            FreeDownloadHandler();
            ResetRequest();
        }

        private bool IsResponseDataTimeout()
        {
            bool recvDataTimeoutSwitch = COS.SettingPrefs.GetInt(COSSettingPrefs.COS_DOWNLOAD_RECV_DATA_TIMEOUT) == 1;
            if (recvDataTimeoutSwitch)
                return Time.realtimeSinceStartup - ms_lastProgressRefershTime > 5f;      // 超过5秒就表示长时间无数据回包
            else
                return false;
        }

        private void FreeDownloadHandler()
        {
            if (m_request != null)
            {
                var handler = m_request.downloadHandler as COSDownloadHandler;
                if (handler != null)
                {
                    handler.Reset();
                    COSBlockPool.Free(handler.Block);
                    handler.Dispose();
                }
            }
        }

        protected void ResetRequest()
        {
            if (m_request != null
                && !m_request.isDone
                && !m_request.isHttpError
                && !m_request.isNetworkError)
            {
                m_request.Abort();
            }
            m_request = null;
            m_success = false;
        }

        public override void Reset()
        {
            FreeDownloadHandler();
            ResetRequest();
            base.Reset();
        }
    }
}
