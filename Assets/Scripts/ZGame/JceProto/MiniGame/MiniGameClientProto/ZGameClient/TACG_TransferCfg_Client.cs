// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_TransferCfg_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sFlashLight = "";

        public string sFlashFollow = "";

        public string sAssetBundle = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sFlashLight, 1);
            _os.Write(sFlashFollow, 2);
            _os.Write(sAssetBundle, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sFlashLight = (string) _is.Read(sFlashLight, 1, false);

            sFlashFollow = (string) _is.Read(sFlashFollow, 2, false);

            sAssetBundle = (string) _is.Read(sAssetBundle, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sFlashLight, "sFlashLight");
            _ds.Display(sFlashFollow, "sFlashFollow");
            _ds.Display(sAssetBundle, "sAssetBundle");
        }

        public override void Clear()
        {
            iID = 0;
            sFlashLight = "";
            sFlashFollow = "";
            sAssetBundle = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_TransferCfg_Client();
            copied.iID = this.iID;
            copied.sFlashLight = this.sFlashLight;
            copied.sFlashFollow = this.sFlashFollow;
            copied.sAssetBundle = this.sAssetBundle;
            return copied;
        }
    }
}

