// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TReportNoviceGuideMissionCompleteRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public int iMissionId = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iMissionId, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iMissionId = (int) _is.Read(iMissionId, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iMissionId, "iMissionId");
        }

        public override void Clear()
        {
            iRet = 0;
            iMissionId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TReportNoviceGuideMissionCompleteRsp();
            copied.iRet = this.iRet;
            copied.iMissionId = this.iMissionId;
            return copied;
        }
    }
}

