// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyPlayBattleEnd : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        bool _bAllFinished = true;
        public bool bAllFinished
        {
            get
            {
                 return _bAllFinished;
            }
            set
            {
                _bAllFinished = value; 
            }
        }

        int _iMoney = 0;
        public int iMoney
        {
            get
            {
                 return _iMoney;
            }
            set
            {
                _iMoney = value; 
            }
        }

        int _iLife = 0;
        public int iLife
        {
            get
            {
                 return _iLife;
            }
            set
            {
                _iLife = value; 
            }
        }

        int _iTurnCount = 0;
        public int iTurnCount
        {
            get
            {
                 return _iTurnCount;
            }
            set
            {
                _iTurnCount = value; 
            }
        }

        int _iEnemyDeductLife = 0;
        public int iEnemyDeductLife
        {
            get
            {
                 return _iEnemyDeductLife;
            }
            set
            {
                _iEnemyDeductLife = value; 
            }
        }
        
        int _iWinnerID = 0;
        public int iWinnerID
        {
            get
            {
                return _iWinnerID;
            }
            set
            {
                _iWinnerID = value; 
            }
        }


        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(bAllFinished, 2);
            _os.Write(iMoney, 3);
            _os.Write(iLife, 4);
            _os.Write(iTurnCount, 5);
            _os.Write(iEnemyDeductLife, 6);
            _os.Write(iWinnerID, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            bAllFinished = (bool) _is.Read(bAllFinished, 2, false);

            iMoney = (int) _is.Read(iMoney, 3, false);

            iLife = (int) _is.Read(iLife, 4, false);

            iTurnCount = (int) _is.Read(iTurnCount, 5, false);

            iEnemyDeductLife = (int) _is.Read(iEnemyDeductLife, 6, false);
            
            iWinnerID = (int) _is.Read(iWinnerID, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(bAllFinished, "bAllFinished");
            _ds.Display(iMoney, "iMoney");
            _ds.Display(iLife, "iLife");
            _ds.Display(iTurnCount, "iTurnCount");
            _ds.Display(iEnemyDeductLife, "iEnemyDeductLife");
            _ds.Display(iWinnerID, "iWinnerID");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            bAllFinished = true;
            iMoney = 0;
            iLife = 0;
            iTurnCount = 0;
            iEnemyDeductLife = 0;
            iWinnerID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyPlayBattleEnd();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.bAllFinished = this.bAllFinished;
            copied.iMoney = this.iMoney;
            copied.iLife = this.iLife;
            copied.iTurnCount = this.iTurnCount;
            copied.iEnemyDeductLife = this.iEnemyDeductLife;
            copied.iWinnerID = this.iWinnerID;
            return copied;
        }
    }
}

