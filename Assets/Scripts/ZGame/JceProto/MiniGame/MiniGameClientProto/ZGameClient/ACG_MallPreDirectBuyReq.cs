// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_MallPreDirectBuyReq : Wup.Jce.JceStruct
    {
        public int iGoodsID = 0;

        public int iSingleCouponPrice = 0;

        public TMidasTokenInfo stMidasToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iGoodsID, 0);
            _os.Write(iSingleCouponPrice, 1);
            _os.Write(stMidasToken, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iGoodsID = (int) _is.Read(iGoodsID, 0, false);

            iSingleCouponPrice = (int) _is.Read(iSingleCouponPrice, 1, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iGoodsID, "iGoodsID");
            _ds.Display(iSingleCouponPrice, "iSingleCouponPrice");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            iGoodsID = 0;
            iSingleCouponPrice = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_MallPreDirectBuyReq();
            copied.iGoodsID = this.iGoodsID;
            copied.iSingleCouponPrice = this.iSingleCouponPrice;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

