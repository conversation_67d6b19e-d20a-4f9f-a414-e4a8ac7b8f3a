// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_BattleHeroData : Wup.Jce.JceStruct
    {
        public TAC_HeroEntity stHeroEntity { get; set; }

        int _iMergedData = 0;
        public int iMergedData
        {
            get
            {
                return _iMergedData;
            }
            set
            {
                _iMergedData = value;
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stHeroEntity, 0);
            _os.Write(iMergedData, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stHeroEntity = (TAC_HeroEntity)_is.Read(stHeroEntity, 0, false);

            iMergedData = (int)_is.Read(iMergedData, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stHeroEntity, "stHeroEntity");
            _ds.Display(iMergedData, "iMergedData");
        }

        public override void Clear()
        {
            stHeroEntity = null;
            iMergedData = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_BattleHeroData();
            copied.stHeroEntity = (TAC_HeroEntity)JceUtil.DeepClone(this.stHeroEntity);
            copied.iMergedData = this.iMergedData;
            return copied;
        }
    }
}

