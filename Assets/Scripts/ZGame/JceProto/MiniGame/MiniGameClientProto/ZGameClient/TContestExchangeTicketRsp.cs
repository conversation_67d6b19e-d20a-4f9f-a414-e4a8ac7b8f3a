// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TContestExchangeTicketRsp : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _iNum = 0;
        public int iNum
        {
            get
            {
                 return _iNum;
            }
            set
            {
                _iNum = value; 
            }
        }

        int _iContestID = 0;
        public int iContestID
        {
            get
            {
                 return _iContestID;
            }
            set
            {
                _iContestID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iNum, 1);
            _os.Write(iContestID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iNum = (int) _is.Read(iNum, 1, false);

            iContestID = (int) _is.Read(iContestID, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iNum, "iNum");
            _ds.Display(iContestID, "iContestID");
        }

        public override void Clear()
        {
            iRet = 0;
            iNum = 0;
            iContestID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TContestExchangeTicketRsp();
            copied.iRet = this.iRet;
            copied.iNum = this.iNum;
            copied.iContestID = this.iContestID;
            return copied;
        }
    }
}

