// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_MMR_Match_Rule_Server : Wup.Jce.JceStruct
    {
        public int iSceneType = 0;

        public string sMatchTimePeriod = "";

        public int iIntervalSeconds = 0;

        public int iExpandMMR = 0;

        public int iRankLevelLimit = 0;

        public int iID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iSceneType, 0);
            _os.Write(sMatchTimePeriod, 1);
            _os.Write(iIntervalSeconds, 2);
            _os.Write(iExpandMMR, 3);
            _os.Write(iRankLevelLimit, 4);
            _os.Write(iID, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iSceneType = (int) _is.Read(iSceneType, 0, false);

            sMatchTimePeriod = (string) _is.Read(sMatchTimePeriod, 1, false);

            iIntervalSeconds = (int) _is.Read(iIntervalSeconds, 2, false);

            iExpandMMR = (int) _is.Read(iExpandMMR, 3, false);

            iRankLevelLimit = (int) _is.Read(iRankLevelLimit, 4, false);

            iID = (int) _is.Read(iID, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(sMatchTimePeriod, "sMatchTimePeriod");
            _ds.Display(iIntervalSeconds, "iIntervalSeconds");
            _ds.Display(iExpandMMR, "iExpandMMR");
            _ds.Display(iRankLevelLimit, "iRankLevelLimit");
            _ds.Display(iID, "iID");
        }

        public override void Clear()
        {
            iSceneType = 0;
            sMatchTimePeriod = "";
            iIntervalSeconds = 0;
            iExpandMMR = 0;
            iRankLevelLimit = 0;
            iID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_MMR_Match_Rule_Server();
            copied.iSceneType = this.iSceneType;
            copied.sMatchTimePeriod = this.sMatchTimePeriod;
            copied.iIntervalSeconds = this.iIntervalSeconds;
            copied.iExpandMMR = this.iExpandMMR;
            copied.iRankLevelLimit = this.iRankLevelLimit;
            copied.iID = this.iID;
            return copied;
        }
    }
}

