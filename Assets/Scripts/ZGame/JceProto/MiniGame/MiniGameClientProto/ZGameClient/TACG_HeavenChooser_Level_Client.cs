// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HeavenChooser_Level_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iLevel = 0;

        public int iPlan = 0;

        public int iWeight1 = 0;

        public int iWeight2 = 0;

        public int iWeight3 = 0;

        public int iWeight4 = 0;

        public int iWeight5 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLevel, 1);
            _os.Write(iPlan, 2);
            _os.Write(iWeight1, 3);
            _os.Write(iWeight2, 4);
            _os.Write(iWeight3, 5);
            _os.Write(iWeight4, 6);
            _os.Write(iWeight5, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLevel = (int) _is.Read(iLevel, 1, false);

            iPlan = (int) _is.Read(iPlan, 2, false);

            iWeight1 = (int) _is.Read(iWeight1, 3, false);

            iWeight2 = (int) _is.Read(iWeight2, 4, false);

            iWeight3 = (int) _is.Read(iWeight3, 5, false);

            iWeight4 = (int) _is.Read(iWeight4, 6, false);

            iWeight5 = (int) _is.Read(iWeight5, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iPlan, "iPlan");
            _ds.Display(iWeight1, "iWeight1");
            _ds.Display(iWeight2, "iWeight2");
            _ds.Display(iWeight3, "iWeight3");
            _ds.Display(iWeight4, "iWeight4");
            _ds.Display(iWeight5, "iWeight5");
        }

        public override void Clear()
        {
            iID = 0;
            iLevel = 0;
            iPlan = 0;
            iWeight1 = 0;
            iWeight2 = 0;
            iWeight3 = 0;
            iWeight4 = 0;
            iWeight5 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HeavenChooser_Level_Client();
            copied.iID = this.iID;
            copied.iLevel = this.iLevel;
            copied.iPlan = this.iPlan;
            copied.iWeight1 = this.iWeight1;
            copied.iWeight2 = this.iWeight2;
            copied.iWeight3 = this.iWeight3;
            copied.iWeight4 = this.iWeight4;
            copied.iWeight5 = this.iWeight5;
            return copied;
        }
    }
}

