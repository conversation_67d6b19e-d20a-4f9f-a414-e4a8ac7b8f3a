// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TPlayerSeasonInfo : Wup.Jce.JceStruct
    {
        public int iSeasonId = 0;

        public int iMaxTier = 0;

        public int iTier = 0;

        public int iLP = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iSeasonId, 0);
            _os.Write(iMaxTier, 1);
            _os.Write(iTier, 2);
            _os.Write(iLP, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iSeasonId = (int) _is.Read(iSeasonId, 0, false);

            iMaxTier = (int) _is.Read(iMaxTier, 1, false);

            iTier = (int) _is.Read(iTier, 2, false);

            iLP = (int) _is.Read(iLP, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iSeasonId, "iSeasonId");
            _ds.Display(iMaxTier, "iMaxTier");
            _ds.Display(iTier, "iTier");
            _ds.Display(iLP, "iLP");
        }

        public override void Clear()
        {
            iSeasonId = 0;
            iMaxTier = 0;
            iTier = 0;
            iLP = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TPlayerSeasonInfo();
            copied.iSeasonId = this.iSeasonId;
            copied.iMaxTier = this.iMaxTier;
            copied.iTier = this.iTier;
            copied.iLP = this.iLP;
            return copied;
        }
    }
}

