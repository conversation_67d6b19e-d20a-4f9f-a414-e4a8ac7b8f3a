//所在的Excel 【Club.xlsm】
//�
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TClubGlobal_Client : Wup.Jce.JceStruct
    {
        public int iChatInterval = 0;

        public int iJoinClubInterval = 0;

        public int iWorldRecruitInterval = 0;

        public int iStage1ActiveScore = 0;

        public string sStage1Reward = "";

        public int iStage2ActiveScore = 0;

        public string sStage2Reward = "";

        public int iStage3ActiveScore = 0;

        public string sStage3Reward = "";

        public string sStage3ExtraRankReward = "";

        public int iStage3RewardMaxRank = 0;

        public int iPlayWithMemberActiveScore = 0;

        public int iMinActiveScoreToGetWeeklyReward = 0;

        public int iMaxMemberNum = 0;

        public int iMaxDeputyChiefNum = 0;

        public int iMaxAdminNum = 0;

        public int iMaxApplicationListNum = 0;

        public int iMaxChatRecordNum = 0;

        public int iMaxMemberOperationRecordNum = 0;

        public int iMinUpdateClubSettingRanking = 0;

        public int iMinUpdateApplicationRequirementsRanking = 0;

        public int iMinKickoutRanking = 0;

        public int iMinDealApplicationRanking = 0;

        public string sCreateConsmeItems = "";

        public int iMinSendRecruitMsgRanking = 0;

        public int iMaxRecruitMsgNum = 0;

        public string sRecruitDeclarationLength = "";

        public int iBeenInvitedInterval = 0;

        public int iSendInviteMsgInterval = 0;

        public int iCreateClubInterval = 0;

        public string sClubNameLength = "";

        public string sClubIntroductionLength = "";

        public string sApplyIntroductionLength = "";

        public int iApplicationExpiredDuration = 0;

        public int iMinRenameRanking = 0;

        public int iInvitationExpiredDuration = 0;

        public int iMaxInvitationListNum = 0;

        public int iRenameInterval = 0;

        public string sWeeklyActiveReportUpdateTime = "";

        public int iMinActiveScoreToGetRankWeeklyReward = 0;

        public int iFeedsSaveMaxNum = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iChatInterval, 0);
            _os.Write(iJoinClubInterval, 1);
            _os.Write(iWorldRecruitInterval, 2);
            _os.Write(iStage1ActiveScore, 3);
            _os.Write(sStage1Reward, 4);
            _os.Write(iStage2ActiveScore, 5);
            _os.Write(sStage2Reward, 6);
            _os.Write(iStage3ActiveScore, 7);
            _os.Write(sStage3Reward, 8);
            _os.Write(sStage3ExtraRankReward, 9);
            _os.Write(iStage3RewardMaxRank, 10);
            _os.Write(iPlayWithMemberActiveScore, 11);
            _os.Write(iMinActiveScoreToGetWeeklyReward, 12);
            _os.Write(iMaxMemberNum, 13);
            _os.Write(iMaxDeputyChiefNum, 14);
            _os.Write(iMaxAdminNum, 15);
            _os.Write(iMaxApplicationListNum, 16);
            _os.Write(iMaxChatRecordNum, 17);
            _os.Write(iMaxMemberOperationRecordNum, 18);
            _os.Write(iMinUpdateClubSettingRanking, 19);
            _os.Write(iMinUpdateApplicationRequirementsRanking, 20);
            _os.Write(iMinKickoutRanking, 21);
            _os.Write(iMinDealApplicationRanking, 22);
            _os.Write(sCreateConsmeItems, 23);
            _os.Write(iMinSendRecruitMsgRanking, 24);
            _os.Write(iMaxRecruitMsgNum, 25);
            _os.Write(sRecruitDeclarationLength, 26);
            _os.Write(iBeenInvitedInterval, 27);
            _os.Write(iSendInviteMsgInterval, 28);
            _os.Write(iCreateClubInterval, 29);
            _os.Write(sClubNameLength, 30);
            _os.Write(sClubIntroductionLength, 31);
            _os.Write(sApplyIntroductionLength, 32);
            _os.Write(iApplicationExpiredDuration, 33);
            _os.Write(iMinRenameRanking, 34);
            _os.Write(iInvitationExpiredDuration, 35);
            _os.Write(iMaxInvitationListNum, 36);
            _os.Write(iRenameInterval, 37);
            _os.Write(sWeeklyActiveReportUpdateTime, 38);
            _os.Write(iMinActiveScoreToGetRankWeeklyReward, 39);
            _os.Write(iFeedsSaveMaxNum, 40);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iChatInterval = (int) _is.Read(iChatInterval, 0, false);

            iJoinClubInterval = (int) _is.Read(iJoinClubInterval, 1, false);

            iWorldRecruitInterval = (int) _is.Read(iWorldRecruitInterval, 2, false);

            iStage1ActiveScore = (int) _is.Read(iStage1ActiveScore, 3, false);

            sStage1Reward = (string) _is.Read(sStage1Reward, 4, false);

            iStage2ActiveScore = (int) _is.Read(iStage2ActiveScore, 5, false);

            sStage2Reward = (string) _is.Read(sStage2Reward, 6, false);

            iStage3ActiveScore = (int) _is.Read(iStage3ActiveScore, 7, false);

            sStage3Reward = (string) _is.Read(sStage3Reward, 8, false);

            sStage3ExtraRankReward = (string) _is.Read(sStage3ExtraRankReward, 9, false);

            iStage3RewardMaxRank = (int) _is.Read(iStage3RewardMaxRank, 10, false);

            iPlayWithMemberActiveScore = (int) _is.Read(iPlayWithMemberActiveScore, 11, false);

            iMinActiveScoreToGetWeeklyReward = (int) _is.Read(iMinActiveScoreToGetWeeklyReward, 12, false);

            iMaxMemberNum = (int) _is.Read(iMaxMemberNum, 13, false);

            iMaxDeputyChiefNum = (int) _is.Read(iMaxDeputyChiefNum, 14, false);

            iMaxAdminNum = (int) _is.Read(iMaxAdminNum, 15, false);

            iMaxApplicationListNum = (int) _is.Read(iMaxApplicationListNum, 16, false);

            iMaxChatRecordNum = (int) _is.Read(iMaxChatRecordNum, 17, false);

            iMaxMemberOperationRecordNum = (int) _is.Read(iMaxMemberOperationRecordNum, 18, false);

            iMinUpdateClubSettingRanking = (int) _is.Read(iMinUpdateClubSettingRanking, 19, false);

            iMinUpdateApplicationRequirementsRanking = (int) _is.Read(iMinUpdateApplicationRequirementsRanking, 20, false);

            iMinKickoutRanking = (int) _is.Read(iMinKickoutRanking, 21, false);

            iMinDealApplicationRanking = (int) _is.Read(iMinDealApplicationRanking, 22, false);

            sCreateConsmeItems = (string) _is.Read(sCreateConsmeItems, 23, false);

            iMinSendRecruitMsgRanking = (int) _is.Read(iMinSendRecruitMsgRanking, 24, false);

            iMaxRecruitMsgNum = (int) _is.Read(iMaxRecruitMsgNum, 25, false);

            sRecruitDeclarationLength = (string) _is.Read(sRecruitDeclarationLength, 26, false);

            iBeenInvitedInterval = (int) _is.Read(iBeenInvitedInterval, 27, false);

            iSendInviteMsgInterval = (int) _is.Read(iSendInviteMsgInterval, 28, false);

            iCreateClubInterval = (int) _is.Read(iCreateClubInterval, 29, false);

            sClubNameLength = (string) _is.Read(sClubNameLength, 30, false);

            sClubIntroductionLength = (string) _is.Read(sClubIntroductionLength, 31, false);

            sApplyIntroductionLength = (string) _is.Read(sApplyIntroductionLength, 32, false);

            iApplicationExpiredDuration = (int) _is.Read(iApplicationExpiredDuration, 33, false);

            iMinRenameRanking = (int) _is.Read(iMinRenameRanking, 34, false);

            iInvitationExpiredDuration = (int) _is.Read(iInvitationExpiredDuration, 35, false);

            iMaxInvitationListNum = (int) _is.Read(iMaxInvitationListNum, 36, false);

            iRenameInterval = (int) _is.Read(iRenameInterval, 37, false);

            sWeeklyActiveReportUpdateTime = (string) _is.Read(sWeeklyActiveReportUpdateTime, 38, false);

            iMinActiveScoreToGetRankWeeklyReward = (int) _is.Read(iMinActiveScoreToGetRankWeeklyReward, 39, false);

            iFeedsSaveMaxNum = (int) _is.Read(iFeedsSaveMaxNum, 40, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iChatInterval, "iChatInterval");
            _ds.Display(iJoinClubInterval, "iJoinClubInterval");
            _ds.Display(iWorldRecruitInterval, "iWorldRecruitInterval");
            _ds.Display(iStage1ActiveScore, "iStage1ActiveScore");
            _ds.Display(sStage1Reward, "sStage1Reward");
            _ds.Display(iStage2ActiveScore, "iStage2ActiveScore");
            _ds.Display(sStage2Reward, "sStage2Reward");
            _ds.Display(iStage3ActiveScore, "iStage3ActiveScore");
            _ds.Display(sStage3Reward, "sStage3Reward");
            _ds.Display(sStage3ExtraRankReward, "sStage3ExtraRankReward");
            _ds.Display(iStage3RewardMaxRank, "iStage3RewardMaxRank");
            _ds.Display(iPlayWithMemberActiveScore, "iPlayWithMemberActiveScore");
            _ds.Display(iMinActiveScoreToGetWeeklyReward, "iMinActiveScoreToGetWeeklyReward");
            _ds.Display(iMaxMemberNum, "iMaxMemberNum");
            _ds.Display(iMaxDeputyChiefNum, "iMaxDeputyChiefNum");
            _ds.Display(iMaxAdminNum, "iMaxAdminNum");
            _ds.Display(iMaxApplicationListNum, "iMaxApplicationListNum");
            _ds.Display(iMaxChatRecordNum, "iMaxChatRecordNum");
            _ds.Display(iMaxMemberOperationRecordNum, "iMaxMemberOperationRecordNum");
            _ds.Display(iMinUpdateClubSettingRanking, "iMinUpdateClubSettingRanking");
            _ds.Display(iMinUpdateApplicationRequirementsRanking, "iMinUpdateApplicationRequirementsRanking");
            _ds.Display(iMinKickoutRanking, "iMinKickoutRanking");
            _ds.Display(iMinDealApplicationRanking, "iMinDealApplicationRanking");
            _ds.Display(sCreateConsmeItems, "sCreateConsmeItems");
            _ds.Display(iMinSendRecruitMsgRanking, "iMinSendRecruitMsgRanking");
            _ds.Display(iMaxRecruitMsgNum, "iMaxRecruitMsgNum");
            _ds.Display(sRecruitDeclarationLength, "sRecruitDeclarationLength");
            _ds.Display(iBeenInvitedInterval, "iBeenInvitedInterval");
            _ds.Display(iSendInviteMsgInterval, "iSendInviteMsgInterval");
            _ds.Display(iCreateClubInterval, "iCreateClubInterval");
            _ds.Display(sClubNameLength, "sClubNameLength");
            _ds.Display(sClubIntroductionLength, "sClubIntroductionLength");
            _ds.Display(sApplyIntroductionLength, "sApplyIntroductionLength");
            _ds.Display(iApplicationExpiredDuration, "iApplicationExpiredDuration");
            _ds.Display(iMinRenameRanking, "iMinRenameRanking");
            _ds.Display(iInvitationExpiredDuration, "iInvitationExpiredDuration");
            _ds.Display(iMaxInvitationListNum, "iMaxInvitationListNum");
            _ds.Display(iRenameInterval, "iRenameInterval");
            _ds.Display(sWeeklyActiveReportUpdateTime, "sWeeklyActiveReportUpdateTime");
            _ds.Display(iMinActiveScoreToGetRankWeeklyReward, "iMinActiveScoreToGetRankWeeklyReward");
            _ds.Display(iFeedsSaveMaxNum, "iFeedsSaveMaxNum");
        }

        public override void Clear()
        {
            iChatInterval = 0;
            iJoinClubInterval = 0;
            iWorldRecruitInterval = 0;
            iStage1ActiveScore = 0;
            sStage1Reward = "";
            iStage2ActiveScore = 0;
            sStage2Reward = "";
            iStage3ActiveScore = 0;
            sStage3Reward = "";
            sStage3ExtraRankReward = "";
            iStage3RewardMaxRank = 0;
            iPlayWithMemberActiveScore = 0;
            iMinActiveScoreToGetWeeklyReward = 0;
            iMaxMemberNum = 0;
            iMaxDeputyChiefNum = 0;
            iMaxAdminNum = 0;
            iMaxApplicationListNum = 0;
            iMaxChatRecordNum = 0;
            iMaxMemberOperationRecordNum = 0;
            iMinUpdateClubSettingRanking = 0;
            iMinUpdateApplicationRequirementsRanking = 0;
            iMinKickoutRanking = 0;
            iMinDealApplicationRanking = 0;
            sCreateConsmeItems = "";
            iMinSendRecruitMsgRanking = 0;
            iMaxRecruitMsgNum = 0;
            sRecruitDeclarationLength = "";
            iBeenInvitedInterval = 0;
            iSendInviteMsgInterval = 0;
            iCreateClubInterval = 0;
            sClubNameLength = "";
            sClubIntroductionLength = "";
            sApplyIntroductionLength = "";
            iApplicationExpiredDuration = 0;
            iMinRenameRanking = 0;
            iInvitationExpiredDuration = 0;
            iMaxInvitationListNum = 0;
            iRenameInterval = 0;
            sWeeklyActiveReportUpdateTime = "";
            iMinActiveScoreToGetRankWeeklyReward = 0;
            iFeedsSaveMaxNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TClubGlobal_Client();
            copied.iChatInterval = this.iChatInterval;
            copied.iJoinClubInterval = this.iJoinClubInterval;
            copied.iWorldRecruitInterval = this.iWorldRecruitInterval;
            copied.iStage1ActiveScore = this.iStage1ActiveScore;
            copied.sStage1Reward = this.sStage1Reward;
            copied.iStage2ActiveScore = this.iStage2ActiveScore;
            copied.sStage2Reward = this.sStage2Reward;
            copied.iStage3ActiveScore = this.iStage3ActiveScore;
            copied.sStage3Reward = this.sStage3Reward;
            copied.sStage3ExtraRankReward = this.sStage3ExtraRankReward;
            copied.iStage3RewardMaxRank = this.iStage3RewardMaxRank;
            copied.iPlayWithMemberActiveScore = this.iPlayWithMemberActiveScore;
            copied.iMinActiveScoreToGetWeeklyReward = this.iMinActiveScoreToGetWeeklyReward;
            copied.iMaxMemberNum = this.iMaxMemberNum;
            copied.iMaxDeputyChiefNum = this.iMaxDeputyChiefNum;
            copied.iMaxAdminNum = this.iMaxAdminNum;
            copied.iMaxApplicationListNum = this.iMaxApplicationListNum;
            copied.iMaxChatRecordNum = this.iMaxChatRecordNum;
            copied.iMaxMemberOperationRecordNum = this.iMaxMemberOperationRecordNum;
            copied.iMinUpdateClubSettingRanking = this.iMinUpdateClubSettingRanking;
            copied.iMinUpdateApplicationRequirementsRanking = this.iMinUpdateApplicationRequirementsRanking;
            copied.iMinKickoutRanking = this.iMinKickoutRanking;
            copied.iMinDealApplicationRanking = this.iMinDealApplicationRanking;
            copied.sCreateConsmeItems = this.sCreateConsmeItems;
            copied.iMinSendRecruitMsgRanking = this.iMinSendRecruitMsgRanking;
            copied.iMaxRecruitMsgNum = this.iMaxRecruitMsgNum;
            copied.sRecruitDeclarationLength = this.sRecruitDeclarationLength;
            copied.iBeenInvitedInterval = this.iBeenInvitedInterval;
            copied.iSendInviteMsgInterval = this.iSendInviteMsgInterval;
            copied.iCreateClubInterval = this.iCreateClubInterval;
            copied.sClubNameLength = this.sClubNameLength;
            copied.sClubIntroductionLength = this.sClubIntroductionLength;
            copied.sApplyIntroductionLength = this.sApplyIntroductionLength;
            copied.iApplicationExpiredDuration = this.iApplicationExpiredDuration;
            copied.iMinRenameRanking = this.iMinRenameRanking;
            copied.iInvitationExpiredDuration = this.iInvitationExpiredDuration;
            copied.iMaxInvitationListNum = this.iMaxInvitationListNum;
            copied.iRenameInterval = this.iRenameInterval;
            copied.sWeeklyActiveReportUpdateTime = this.sWeeklyActiveReportUpdateTime;
            copied.iMinActiveScoreToGetRankWeeklyReward = this.iMinActiveScoreToGetRankWeeklyReward;
            copied.iFeedsSaveMaxNum = this.iFeedsSaveMaxNum;
            return copied;
        }
    }
}

