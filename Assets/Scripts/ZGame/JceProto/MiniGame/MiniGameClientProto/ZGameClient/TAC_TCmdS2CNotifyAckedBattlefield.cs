// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyAckedBattlefield : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iMoney = 0;
        public int iMoney
        {
            get
            {
                 return _iMoney;
            }
            set
            {
                _iMoney = value; 
            }
        }

        int _iExp = 0;
        public int iExp
        {
            get
            {
                 return _iExp;
            }
            set
            {
                _iExp = value; 
            }
        }

        int _iLife = 0;
        public int iLife
        {
            get
            {
                 return _iLife;
            }
            set
            {
                _iLife = value; 
            }
        }

        int _iLevel = 0;
        public int iLevel
        {
            get
            {
                 return _iLevel;
            }
            set
            {
                _iLevel = value; 
            }
        }

        int _iCurPeople = 0;
        public int iCurPeople
        {
            get
            {
                 return _iCurPeople;
            }
            set
            {
                _iCurPeople = value; 
            }
        }

        int _iMaxPeople = 0;
        public int iMaxPeople
        {
            get
            {
                 return _iMaxPeople;
            }
            set
            {
                _iMaxPeople = value; 
            }
        }

        public System.Collections.Generic.List<TAC_BattleGroundHero> vecBattleGroundHero {get; set;} 

        public System.Collections.Generic.List<TAC_WaitHero> vecWaitHero {get; set;} 

        public System.Collections.Generic.List<TAC_RollHero> vecRollHero {get; set;} 

        public System.Collections.Generic.List<TAC_SellBattleGroundHero> vecSellHero {get; set;} 

        public System.Collections.Generic.List<TAC_HeroTypeInfo> vecHeroTypeInfo {get; set;} 

        int _iNextLeveExp = 0;
        public int iNextLeveExp
        {
            get
            {
                 return _iNextLeveExp;
            }
            set
            {
                _iNextLeveExp = value; 
            }
        }

        int _iStageStartTime = 0;
        public int iStageStartTime
        {
            get
            {
                 return _iStageStartTime;
            }
            set
            {
                _iStageStartTime = value; 
            }
        }

        public System.Collections.Generic.List<TAC_AutoMoveToBattleHero> vecAutoMoveToBattleHero {get; set;} 

        public System.Collections.Generic.List<TAC_HeroPromotionInfo> vecHeroPromotionInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iMoney, 1);
            _os.Write(iExp, 2);
            _os.Write(iLife, 3);
            _os.Write(iLevel, 4);
            _os.Write(iCurPeople, 5);
            _os.Write(iMaxPeople, 6);
            _os.Write(vecBattleGroundHero, 7);
            _os.Write(vecWaitHero, 8);
            _os.Write(vecRollHero, 9);
            _os.Write(vecSellHero, 10);
            _os.Write(vecHeroTypeInfo, 11);
            _os.Write(iNextLeveExp, 12);
            _os.Write(iStageStartTime, 13);
            _os.Write(vecAutoMoveToBattleHero, 14);
            _os.Write(vecHeroPromotionInfo, 15);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iMoney = (int) _is.Read(iMoney, 1, false);

            iExp = (int) _is.Read(iExp, 2, false);

            iLife = (int) _is.Read(iLife, 3, false);

            iLevel = (int) _is.Read(iLevel, 4, false);

            iCurPeople = (int) _is.Read(iCurPeople, 5, false);

            iMaxPeople = (int) _is.Read(iMaxPeople, 6, false);

            vecBattleGroundHero = (System.Collections.Generic.List<TAC_BattleGroundHero>) _is.Read(vecBattleGroundHero, 7, false);

            vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecWaitHero, 8, false);

            vecRollHero = (System.Collections.Generic.List<TAC_RollHero>) _is.Read(vecRollHero, 9, false);

            vecSellHero = (System.Collections.Generic.List<TAC_SellBattleGroundHero>) _is.Read(vecSellHero, 10, false);

            vecHeroTypeInfo = (System.Collections.Generic.List<TAC_HeroTypeInfo>) _is.Read(vecHeroTypeInfo, 11, false);

            iNextLeveExp = (int) _is.Read(iNextLeveExp, 12, false);

            iStageStartTime = (int) _is.Read(iStageStartTime, 13, false);

            vecAutoMoveToBattleHero = (System.Collections.Generic.List<TAC_AutoMoveToBattleHero>) _is.Read(vecAutoMoveToBattleHero, 14, false);

            vecHeroPromotionInfo = (System.Collections.Generic.List<TAC_HeroPromotionInfo>) _is.Read(vecHeroPromotionInfo, 15, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iMoney, "iMoney");
            _ds.Display(iExp, "iExp");
            _ds.Display(iLife, "iLife");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iCurPeople, "iCurPeople");
            _ds.Display(iMaxPeople, "iMaxPeople");
            _ds.Display(vecBattleGroundHero, "vecBattleGroundHero");
            _ds.Display(vecWaitHero, "vecWaitHero");
            _ds.Display(vecRollHero, "vecRollHero");
            _ds.Display(vecSellHero, "vecSellHero");
            _ds.Display(vecHeroTypeInfo, "vecHeroTypeInfo");
            _ds.Display(iNextLeveExp, "iNextLeveExp");
            _ds.Display(iStageStartTime, "iStageStartTime");
            _ds.Display(vecAutoMoveToBattleHero, "vecAutoMoveToBattleHero");
            _ds.Display(vecHeroPromotionInfo, "vecHeroPromotionInfo");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iMoney = 0;
            iExp = 0;
            iLife = 0;
            iLevel = 0;
            iCurPeople = 0;
            iMaxPeople = 0;
            vecBattleGroundHero = null;
            vecWaitHero = null;
            vecRollHero = null;
            vecSellHero = null;
            vecHeroTypeInfo = null;
            iNextLeveExp = 0;
            iStageStartTime = 0;
            vecAutoMoveToBattleHero = null;
            vecHeroPromotionInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyAckedBattlefield();
            copied.i8ChairID = this.i8ChairID;
            copied.iMoney = this.iMoney;
            copied.iExp = this.iExp;
            copied.iLife = this.iLife;
            copied.iLevel = this.iLevel;
            copied.iCurPeople = this.iCurPeople;
            copied.iMaxPeople = this.iMaxPeople;
            copied.vecBattleGroundHero = (System.Collections.Generic.List<TAC_BattleGroundHero>)JceUtil.DeepClone(this.vecBattleGroundHero);
            copied.vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecWaitHero);
            copied.vecRollHero = (System.Collections.Generic.List<TAC_RollHero>)JceUtil.DeepClone(this.vecRollHero);
            copied.vecSellHero = (System.Collections.Generic.List<TAC_SellBattleGroundHero>)JceUtil.DeepClone(this.vecSellHero);
            copied.vecHeroTypeInfo = (System.Collections.Generic.List<TAC_HeroTypeInfo>)JceUtil.DeepClone(this.vecHeroTypeInfo);
            copied.iNextLeveExp = this.iNextLeveExp;
            copied.iStageStartTime = this.iStageStartTime;
            copied.vecAutoMoveToBattleHero = (System.Collections.Generic.List<TAC_AutoMoveToBattleHero>)JceUtil.DeepClone(this.vecAutoMoveToBattleHero);
            copied.vecHeroPromotionInfo = (System.Collections.Generic.List<TAC_HeroPromotionInfo>)JceUtil.DeepClone(this.vecHeroPromotionInfo);
            return copied;
        }
    }
}

