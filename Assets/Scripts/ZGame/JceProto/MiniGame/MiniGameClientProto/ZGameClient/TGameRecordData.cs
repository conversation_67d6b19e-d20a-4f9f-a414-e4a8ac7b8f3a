// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGameRecordData : Wup.Jce.JceStruct
    {
        public int iPlayCount = 0;

        public int iWinCount = 0;

        public int iTopFourCount = 0;

        public int iRankPerOneMatch = 0;

        public int iRankPerRecentTenMatch = 0;

        public int iMaxConWinCount = 0;

        public int iFullHeathWinCount = 0;

        public int iLessHeathWinCount = 0;

        public int iLiveTurnScore = 0;

        public int iDamageScore = 0;

        public int iMoneyScore = 0;

        public int iThreeChessScore = 0;

        public int iFetterUsedScore = 0;

        public long lGetEquipmentCount = 0;

        public int iInBattleGold = 0;

        public int iRefreshCount = 0;

        public long lThreeChessCount = 0;

        public long lKillHeroCount = 0;

        public long lWinRoundCount = 0;

        public long lToPlayerDamageCount = 0;

        public int iKillCaptainCount = 0;

        public int iTopFourScore = 0;

        public int iChessWorthScore = 0;

        public int iRefreshScore = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iPlayCount, 0);
            _os.Write(iWinCount, 1);
            _os.Write(iTopFourCount, 2);
            _os.Write(iRankPerOneMatch, 3);
            _os.Write(iRankPerRecentTenMatch, 4);
            _os.Write(iMaxConWinCount, 5);
            _os.Write(iFullHeathWinCount, 6);
            _os.Write(iLessHeathWinCount, 7);
            _os.Write(iLiveTurnScore, 8);
            _os.Write(iDamageScore, 9);
            _os.Write(iMoneyScore, 10);
            _os.Write(iThreeChessScore, 11);
            _os.Write(iFetterUsedScore, 12);
            _os.Write(lGetEquipmentCount, 13);
            _os.Write(iInBattleGold, 14);
            _os.Write(iRefreshCount, 15);
            _os.Write(lThreeChessCount, 16);
            _os.Write(lKillHeroCount, 17);
            _os.Write(lWinRoundCount, 18);
            _os.Write(lToPlayerDamageCount, 19);
            _os.Write(iKillCaptainCount, 20);
            _os.Write(iTopFourScore, 21);
            _os.Write(iChessWorthScore, 22);
            _os.Write(iRefreshScore, 23);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iPlayCount = (int) _is.Read(iPlayCount, 0, false);

            iWinCount = (int) _is.Read(iWinCount, 1, false);

            iTopFourCount = (int) _is.Read(iTopFourCount, 2, false);

            iRankPerOneMatch = (int) _is.Read(iRankPerOneMatch, 3, false);

            iRankPerRecentTenMatch = (int) _is.Read(iRankPerRecentTenMatch, 4, false);

            iMaxConWinCount = (int) _is.Read(iMaxConWinCount, 5, false);

            iFullHeathWinCount = (int) _is.Read(iFullHeathWinCount, 6, false);

            iLessHeathWinCount = (int) _is.Read(iLessHeathWinCount, 7, false);

            iLiveTurnScore = (int) _is.Read(iLiveTurnScore, 8, false);

            iDamageScore = (int) _is.Read(iDamageScore, 9, false);

            iMoneyScore = (int) _is.Read(iMoneyScore, 10, false);

            iThreeChessScore = (int) _is.Read(iThreeChessScore, 11, false);

            iFetterUsedScore = (int) _is.Read(iFetterUsedScore, 12, false);

            lGetEquipmentCount = (long) _is.Read(lGetEquipmentCount, 13, false);

            iInBattleGold = (int) _is.Read(iInBattleGold, 14, false);

            iRefreshCount = (int) _is.Read(iRefreshCount, 15, false);

            lThreeChessCount = (long) _is.Read(lThreeChessCount, 16, false);

            lKillHeroCount = (long) _is.Read(lKillHeroCount, 17, false);

            lWinRoundCount = (long) _is.Read(lWinRoundCount, 18, false);

            lToPlayerDamageCount = (long) _is.Read(lToPlayerDamageCount, 19, false);

            iKillCaptainCount = (int) _is.Read(iKillCaptainCount, 20, false);

            iTopFourScore = (int) _is.Read(iTopFourScore, 21, false);

            iChessWorthScore = (int) _is.Read(iChessWorthScore, 22, false);

            iRefreshScore = (int) _is.Read(iRefreshScore, 23, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iPlayCount, "iPlayCount");
            _ds.Display(iWinCount, "iWinCount");
            _ds.Display(iTopFourCount, "iTopFourCount");
            _ds.Display(iRankPerOneMatch, "iRankPerOneMatch");
            _ds.Display(iRankPerRecentTenMatch, "iRankPerRecentTenMatch");
            _ds.Display(iMaxConWinCount, "iMaxConWinCount");
            _ds.Display(iFullHeathWinCount, "iFullHeathWinCount");
            _ds.Display(iLessHeathWinCount, "iLessHeathWinCount");
            _ds.Display(iLiveTurnScore, "iLiveTurnScore");
            _ds.Display(iDamageScore, "iDamageScore");
            _ds.Display(iMoneyScore, "iMoneyScore");
            _ds.Display(iThreeChessScore, "iThreeChessScore");
            _ds.Display(iFetterUsedScore, "iFetterUsedScore");
            _ds.Display(lGetEquipmentCount, "lGetEquipmentCount");
            _ds.Display(iInBattleGold, "iInBattleGold");
            _ds.Display(iRefreshCount, "iRefreshCount");
            _ds.Display(lThreeChessCount, "lThreeChessCount");
            _ds.Display(lKillHeroCount, "lKillHeroCount");
            _ds.Display(lWinRoundCount, "lWinRoundCount");
            _ds.Display(lToPlayerDamageCount, "lToPlayerDamageCount");
            _ds.Display(iKillCaptainCount, "iKillCaptainCount");
            _ds.Display(iTopFourScore, "iTopFourScore");
            _ds.Display(iChessWorthScore, "iChessWorthScore");
            _ds.Display(iRefreshScore, "iRefreshScore");
        }

        public override void Clear()
        {
            iPlayCount = 0;
            iWinCount = 0;
            iTopFourCount = 0;
            iRankPerOneMatch = 0;
            iRankPerRecentTenMatch = 0;
            iMaxConWinCount = 0;
            iFullHeathWinCount = 0;
            iLessHeathWinCount = 0;
            iLiveTurnScore = 0;
            iDamageScore = 0;
            iMoneyScore = 0;
            iThreeChessScore = 0;
            iFetterUsedScore = 0;
            lGetEquipmentCount = 0;
            iInBattleGold = 0;
            iRefreshCount = 0;
            lThreeChessCount = 0;
            lKillHeroCount = 0;
            lWinRoundCount = 0;
            lToPlayerDamageCount = 0;
            iKillCaptainCount = 0;
            iTopFourScore = 0;
            iChessWorthScore = 0;
            iRefreshScore = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGameRecordData();
            copied.iPlayCount = this.iPlayCount;
            copied.iWinCount = this.iWinCount;
            copied.iTopFourCount = this.iTopFourCount;
            copied.iRankPerOneMatch = this.iRankPerOneMatch;
            copied.iRankPerRecentTenMatch = this.iRankPerRecentTenMatch;
            copied.iMaxConWinCount = this.iMaxConWinCount;
            copied.iFullHeathWinCount = this.iFullHeathWinCount;
            copied.iLessHeathWinCount = this.iLessHeathWinCount;
            copied.iLiveTurnScore = this.iLiveTurnScore;
            copied.iDamageScore = this.iDamageScore;
            copied.iMoneyScore = this.iMoneyScore;
            copied.iThreeChessScore = this.iThreeChessScore;
            copied.iFetterUsedScore = this.iFetterUsedScore;
            copied.lGetEquipmentCount = this.lGetEquipmentCount;
            copied.iInBattleGold = this.iInBattleGold;
            copied.iRefreshCount = this.iRefreshCount;
            copied.lThreeChessCount = this.lThreeChessCount;
            copied.lKillHeroCount = this.lKillHeroCount;
            copied.lWinRoundCount = this.lWinRoundCount;
            copied.lToPlayerDamageCount = this.lToPlayerDamageCount;
            copied.iKillCaptainCount = this.iKillCaptainCount;
            copied.iTopFourScore = this.iTopFourScore;
            copied.iChessWorthScore = this.iChessWorthScore;
            copied.iRefreshScore = this.iRefreshScore;
            return copied;
        }
    }
}

