// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCancelTinyEquipmentRsp : Wup.Jce.JceStruct
    {
        public int iCancelType = 0;

        public int iRet = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iCancelType, 0);
            _os.Write(iRet, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iCancelType = (int) _is.Read(iCancelType, 0, false);

            iRet = (int) _is.Read(iRet, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iCancelType, "iCancelType");
            _ds.Display(iRet, "iRet");
        }

        public override void Clear()
        {
            iCancelType = 0;
            iRet = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCancelTinyEquipmentRsp();
            copied.iCancelType = this.iCancelType;
            copied.iRet = this.iRet;
            return copied;
        }
    }
}

