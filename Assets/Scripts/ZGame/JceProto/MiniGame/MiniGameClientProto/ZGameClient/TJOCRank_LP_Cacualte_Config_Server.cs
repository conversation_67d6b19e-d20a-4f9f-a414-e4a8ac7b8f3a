// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCRank_LP_Cacualte_Config_Server : Wup.Jce.JceStruct
    {
        public int iRankID = 0;

        public string sName = "";

        public int iMinContestLP = 0;

        public int iMaxContestLP = 0;

        public string sTarget_MMR = "";

        public string sMaxSkillFactor_LPLoss = "";

        public string sMinSkillFactor_LPLoss = "";

        public string sMaxSkillFactor_LPGain = "";

        public string sMinSkillFactor_LPGain = "";

        public string sIcon = "";

        public int iAvatarBoxID = 0;

        public string sLevelUpMovie = "";

        public string sLevelUpAudio = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRankID, 0);
            _os.Write(sName, 1);
            _os.Write(iMinContestLP, 2);
            _os.Write(iMaxContestLP, 3);
            _os.Write(sTarget_MMR, 4);
            _os.Write(sMaxSkillFactor_LPLoss, 5);
            _os.Write(sMinSkillFactor_LPLoss, 6);
            _os.Write(sMaxSkillFactor_LPGain, 7);
            _os.Write(sMinSkillFactor_LPGain, 8);
            _os.Write(sIcon, 9);
            _os.Write(iAvatarBoxID, 10);
            _os.Write(sLevelUpMovie, 11);
            _os.Write(sLevelUpAudio, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRankID = (int) _is.Read(iRankID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            iMinContestLP = (int) _is.Read(iMinContestLP, 2, false);

            iMaxContestLP = (int) _is.Read(iMaxContestLP, 3, false);

            sTarget_MMR = (string) _is.Read(sTarget_MMR, 4, false);

            sMaxSkillFactor_LPLoss = (string) _is.Read(sMaxSkillFactor_LPLoss, 5, false);

            sMinSkillFactor_LPLoss = (string) _is.Read(sMinSkillFactor_LPLoss, 6, false);

            sMaxSkillFactor_LPGain = (string) _is.Read(sMaxSkillFactor_LPGain, 7, false);

            sMinSkillFactor_LPGain = (string) _is.Read(sMinSkillFactor_LPGain, 8, false);

            sIcon = (string) _is.Read(sIcon, 9, false);

            iAvatarBoxID = (int) _is.Read(iAvatarBoxID, 10, false);

            sLevelUpMovie = (string) _is.Read(sLevelUpMovie, 11, false);

            sLevelUpAudio = (string) _is.Read(sLevelUpAudio, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRankID, "iRankID");
            _ds.Display(sName, "sName");
            _ds.Display(iMinContestLP, "iMinContestLP");
            _ds.Display(iMaxContestLP, "iMaxContestLP");
            _ds.Display(sTarget_MMR, "sTarget_MMR");
            _ds.Display(sMaxSkillFactor_LPLoss, "sMaxSkillFactor_LPLoss");
            _ds.Display(sMinSkillFactor_LPLoss, "sMinSkillFactor_LPLoss");
            _ds.Display(sMaxSkillFactor_LPGain, "sMaxSkillFactor_LPGain");
            _ds.Display(sMinSkillFactor_LPGain, "sMinSkillFactor_LPGain");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iAvatarBoxID, "iAvatarBoxID");
            _ds.Display(sLevelUpMovie, "sLevelUpMovie");
            _ds.Display(sLevelUpAudio, "sLevelUpAudio");
        }

        public override void Clear()
        {
            iRankID = 0;
            sName = "";
            iMinContestLP = 0;
            iMaxContestLP = 0;
            sTarget_MMR = "";
            sMaxSkillFactor_LPLoss = "";
            sMinSkillFactor_LPLoss = "";
            sMaxSkillFactor_LPGain = "";
            sMinSkillFactor_LPGain = "";
            sIcon = "";
            iAvatarBoxID = 0;
            sLevelUpMovie = "";
            sLevelUpAudio = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCRank_LP_Cacualte_Config_Server();
            copied.iRankID = this.iRankID;
            copied.sName = this.sName;
            copied.iMinContestLP = this.iMinContestLP;
            copied.iMaxContestLP = this.iMaxContestLP;
            copied.sTarget_MMR = this.sTarget_MMR;
            copied.sMaxSkillFactor_LPLoss = this.sMaxSkillFactor_LPLoss;
            copied.sMinSkillFactor_LPLoss = this.sMinSkillFactor_LPLoss;
            copied.sMaxSkillFactor_LPGain = this.sMaxSkillFactor_LPGain;
            copied.sMinSkillFactor_LPGain = this.sMinSkillFactor_LPGain;
            copied.sIcon = this.sIcon;
            copied.iAvatarBoxID = this.iAvatarBoxID;
            copied.sLevelUpMovie = this.sLevelUpMovie;
            copied.sLevelUpAudio = this.sLevelUpAudio;
            return copied;
        }
    }
}

