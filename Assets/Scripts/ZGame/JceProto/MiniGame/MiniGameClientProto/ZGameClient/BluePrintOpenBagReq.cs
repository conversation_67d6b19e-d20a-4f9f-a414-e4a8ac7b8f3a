// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BluePrintOpenBagReq : Wup.Jce.JceStruct
    {
        int _lotteryID = 0;
        public int lotteryID
        {
            get
            {
                 return _lotteryID;
            }
            set
            {
                _lotteryID = value; 
            }
        }

        int _bagIndex = 0;
        public int bagIndex
        {
            get
            {
                 return _bagIndex;
            }
            set
            {
                _bagIndex = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lotteryID, 0);
            _os.Write(bagIndex, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lotteryID = (int) _is.Read(lotteryID, 0, false);

            bagIndex = (int) _is.Read(bagIndex, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lotteryID, "lotteryID");
            _ds.Display(bagIndex, "bagIndex");
        }

        public override void Clear()
        {
            lotteryID = 0;
            bagIndex = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BluePrintOpenBagReq();
            copied.lotteryID = this.lotteryID;
            copied.bagIndex = this.bagIndex;
            return copied;
        }
    }
}

