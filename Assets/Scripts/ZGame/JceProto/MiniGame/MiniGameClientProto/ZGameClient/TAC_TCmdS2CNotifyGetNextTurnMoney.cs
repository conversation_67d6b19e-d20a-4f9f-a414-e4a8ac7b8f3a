// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyGetNextTurnMoney : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iTurnCount = 0;
        public int iTurnCount
        {
            get
            {
                 return _iTurnCount;
            }
            set
            {
                _iTurnCount = value; 
            }
        }

        public System.Collections.Generic.List<TAC_HeroValueChange> vecHeroValueChange {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(iTurnCount, 2);
            _os.Write(vecHeroValueChange, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            iTurnCount = (int) _is.Read(iTurnCount, 2, false);

            vecHeroValueChange = (System.Collections.Generic.List<TAC_HeroValueChange>) _is.Read(vecHeroValueChange, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iTurnCount, "iTurnCount");
            _ds.Display(vecHeroValueChange, "vecHeroValueChange");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            iTurnCount = 0;
            vecHeroValueChange = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyGetNextTurnMoney();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.iTurnCount = this.iTurnCount;
            copied.vecHeroValueChange = (System.Collections.Generic.List<TAC_HeroValueChange>)JceUtil.DeepClone(this.vecHeroValueChange);
            return copied;
        }
    }
}

