// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_UserLevel_Client : Wup.Jce.JceStruct
    {
        public int iLevel = 0;

        public int iExp = 0;

        public int iAvatarBoxID = 0;

        public int iItemID = 0;

        public int iItemCount = 0;

        public int iLevelDiff = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iLevel, 0);
            _os.Write(iExp, 1);
            _os.Write(iAvatarBoxID, 2);
            _os.Write(iItemID, 3);
            _os.Write(iItemCount, 4);
            _os.Write(iLevelDiff, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iLevel = (int) _is.Read(iLevel, 0, false);

            iExp = (int) _is.Read(iExp, 1, false);

            iAvatarBoxID = (int) _is.Read(iAvatarBoxID, 2, false);

            iItemID = (int) _is.Read(iItemID, 3, false);

            iItemCount = (int) _is.Read(iItemCount, 4, false);

            iLevelDiff = (int) _is.Read(iLevelDiff, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iExp, "iExp");
            _ds.Display(iAvatarBoxID, "iAvatarBoxID");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iItemCount, "iItemCount");
            _ds.Display(iLevelDiff, "iLevelDiff");
        }

        public override void Clear()
        {
            iLevel = 0;
            iExp = 0;
            iAvatarBoxID = 0;
            iItemID = 0;
            iItemCount = 0;
            iLevelDiff = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_UserLevel_Client();
            copied.iLevel = this.iLevel;
            copied.iExp = this.iExp;
            copied.iAvatarBoxID = this.iAvatarBoxID;
            copied.iItemID = this.iItemID;
            copied.iItemCount = this.iItemCount;
            copied.iLevelDiff = this.iLevelDiff;
            return copied;
        }
    }
}

