// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TurboRankDataResult : Wup.Jce.JceStruct
    {
        public int iBeforeLP = 0;

        public int iChangeLP = 0;

        public int iAfterLP = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iBeforeLP, 0);
            _os.Write(iChangeLP, 1);
            _os.Write(iAfterLP, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iBeforeLP = (int) _is.Read(iBeforeLP, 0, false);

            iChangeLP = (int) _is.Read(iChangeLP, 1, false);

            iAfterLP = (int) _is.Read(iAfterLP, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iBeforeLP, "iBeforeLP");
            _ds.Display(iChangeLP, "iChangeLP");
            _ds.Display(iAfterLP, "iAfterLP");
        }

        public override void Clear()
        {
            iBeforeLP = 0;
            iChangeLP = 0;
            iAfterLP = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TurboRankDataResult();
            copied.iBeforeLP = this.iBeforeLP;
            copied.iChangeLP = this.iChangeLP;
            copied.iAfterLP = this.iAfterLP;
            return copied;
        }
    }
}

