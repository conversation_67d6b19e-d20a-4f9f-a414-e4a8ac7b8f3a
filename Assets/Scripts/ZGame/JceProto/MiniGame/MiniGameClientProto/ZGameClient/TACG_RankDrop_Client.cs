//所在的Excel 【ACG_HundredReward.xlsm】
//*************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_RankDrop_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iColumn = 0;

        public int iRow = 0;

        public string sRank = "";

        public string sDropPoolList = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iColumn, 1);
            _os.Write(iRow, 2);
            _os.Write(sRank, 3);
            _os.Write(sDropPoolList, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iColumn = (int) _is.Read(iColumn, 1, false);

            iRow = (int) _is.Read(iRow, 2, false);

            sRank = (string) _is.Read(sRank, 3, false);

            sDropPoolList = (string) _is.Read(sDropPoolList, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iColumn, "iColumn");
            _ds.Display(iRow, "iRow");
            _ds.Display(sRank, "sRank");
            _ds.Display(sDropPoolList, "sDropPoolList");
        }

        public override void Clear()
        {
            iID = 0;
            iColumn = 0;
            iRow = 0;
            sRank = "";
            sDropPoolList = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_RankDrop_Client();
            copied.iID = this.iID;
            copied.iColumn = this.iColumn;
            copied.iRow = this.iRow;
            copied.sRank = this.sRank;
            copied.sDropPoolList = this.sDropPoolList;
            return copied;
        }
    }
}

