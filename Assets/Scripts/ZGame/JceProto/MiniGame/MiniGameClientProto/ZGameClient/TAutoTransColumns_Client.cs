//所在的Excel 【AATransColumns.xlsm】
//****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAutoTransColumns_Client : Wup.Jce.JceStruct
    {
        public int iIndexID = 0;

        public string sFileName = "";

        public string sSheetName = "";

        public string sColumns = "";

        public string sSpecificPath = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndexID, 0);
            _os.Write(sFileName, 1);
            _os.Write(sSheetName, 2);
            _os.Write(sColumns, 3);
            _os.Write(sSpecificPath, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndexID = (int) _is.Read(iIndexID, 0, false);

            sFileName = (string) _is.Read(sFileName, 1, false);

            sSheetName = (string) _is.Read(sSheetName, 2, false);

            sColumns = (string) _is.Read(sColumns, 3, false);

            sSpecificPath = (string) _is.Read(sSpecificPath, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndexID, "iIndexID");
            _ds.Display(sFileName, "sFileName");
            _ds.Display(sSheetName, "sSheetName");
            _ds.Display(sColumns, "sColumns");
            _ds.Display(sSpecificPath, "sSpecificPath");
        }

        public override void Clear()
        {
            iIndexID = 0;
            sFileName = "";
            sSheetName = "";
            sColumns = "";
            sSpecificPath = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAutoTransColumns_Client();
            copied.iIndexID = this.iIndexID;
            copied.sFileName = this.sFileName;
            copied.sSheetName = this.sSheetName;
            copied.sColumns = this.sColumns;
            copied.sSpecificPath = this.sSpecificPath;
            return copied;
        }
    }
}

