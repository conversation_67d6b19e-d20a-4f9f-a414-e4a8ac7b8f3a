// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TItemChangeNotify : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<int, ItemObject> mapChangeItems;

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<ItemObject>> mapChangeTempItems;

        public int iReason = 0;

        public System.Collections.Generic.List<TAC_ItemDecomposed> vecDecomposedItems;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapChangeItems, 0);
            _os.Write(mapChangeTempItems, 1);
            _os.Write(iReason, 2);
            _os.Write(vecDecomposedItems, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapChangeItems = (TKFrame.TKDictionary<int, ItemObject>) _is.Read(mapChangeItems, 0, false);

            mapChangeTempItems = (TKFrame.TKDictionary<int, System.Collections.Generic.List<ItemObject>>) _is.Read(mapChangeTempItems, 1, false);

            iReason = (int) _is.Read(iReason, 2, false);

            vecDecomposedItems = (System.Collections.Generic.List<TAC_ItemDecomposed>) _is.Read(vecDecomposedItems, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapChangeItems, "mapChangeItems");
            _ds.Display(mapChangeTempItems, "mapChangeTempItems");
            _ds.Display(iReason, "iReason");
            _ds.Display(vecDecomposedItems, "vecDecomposedItems");
        }

        public override void Clear()
        {
            mapChangeItems = null;
            mapChangeTempItems = null;
            iReason = 0;
            vecDecomposedItems = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TItemChangeNotify();
            copied.mapChangeItems = (TKFrame.TKDictionary<int, ItemObject>)JceUtil.DeepClone(this.mapChangeItems);
            copied.mapChangeTempItems = (TKFrame.TKDictionary<int, System.Collections.Generic.List<ItemObject>>)JceUtil.DeepClone(this.mapChangeTempItems);
            copied.iReason = this.iReason;
            copied.vecDecomposedItems = (System.Collections.Generic.List<TAC_ItemDecomposed>)JceUtil.DeepClone(this.vecDecomposedItems);
            return copied;
        }
    }
}

