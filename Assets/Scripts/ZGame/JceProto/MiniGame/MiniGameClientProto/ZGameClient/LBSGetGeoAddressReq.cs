// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class LBSGetGeoAddressReq : Wup.Jce.JceStruct
    {
        public GeoLocation geoLocation;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(geoLocation, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            geoLocation = (GeoLocation) _is.Read(geoLocation, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(geoLocation, "geoLocation");
        }

        public override void Clear()
        {
            geoLocation = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new LBSGetGeoAddressReq();
            copied.geoLocation = (GeoLocation)JceUtil.DeepClone(this.geoLocation);
            return copied;
        }
    }
}

