// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_WaitHeroPromotionInfo : Wup.Jce.JceStruct
    {
        public TAC_WaitHero stNewHero {get; set;} 

        public System.Collections.Generic.List<TAC_WaitHero> vecOriginalHero {get; set;} 

        public System.Collections.Generic.List<int> vecSurplusHero {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stNewHero, 0);
            _os.Write(vecOriginalHero, 1);
            _os.Write(vecSurplusHero, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stNewHero = (TAC_WaitHero) _is.Read(stNewHero, 0, false);

            vecOriginalHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecOriginalHero, 1, false);

            vecSurplusHero = (System.Collections.Generic.List<int>) _is.Read(vecSurplusHero, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stNewHero, "stNewHero");
            _ds.Display(vecOriginalHero, "vecOriginalHero");
            _ds.Display(vecSurplusHero, "vecSurplusHero");
        }

        public override void Clear()
        {
            stNewHero = null;
            vecOriginalHero = null;
            vecSurplusHero = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_WaitHeroPromotionInfo();
            copied.stNewHero = (TAC_WaitHero)JceUtil.DeepClone(this.stNewHero);
            copied.vecOriginalHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecOriginalHero);
            copied.vecSurplusHero = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecSurplusHero);
            return copied;
        }
    }
}

