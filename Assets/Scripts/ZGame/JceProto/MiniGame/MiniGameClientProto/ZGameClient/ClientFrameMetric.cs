// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClientFrameMetric : Wup.Jce.JceStruct
    {
        public int type = 0;

        public long value = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(type, 0);
            _os.Write(value, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            type = (int) _is.Read(type, 0, false);

            value = (long) _is.Read(value, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(type, "type");
            _ds.Display(value, "value");
        }

        public override void Clear()
        {
            type = 0;
            value = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClientFrameMetric();
            copied.type = this.type;
            copied.value = this.value;
            return copied;
        }
    }
}

