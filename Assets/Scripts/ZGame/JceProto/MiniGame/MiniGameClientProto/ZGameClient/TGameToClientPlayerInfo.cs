// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGameToClientPlayerInfo : Wup.Jce.JceStruct
    {
        public long lTotalGameNum = 0;

        /// <summary>
        /// 玩家段位信息(展示信息)
        /// </summary>
        public int iRankLevel = 0;

        /// <summary>
        /// 废弃!
        /// </summary>
        public int iWarmMatchType = 0;

        /// <summary>
        /// 当前赏金赛赛事ID，由GameSvr透传
        /// </summary>
        public int iBountyContestId = 0;

        /// <summary>
        /// 自定义玩法的setid，由客户端通过玩家选择带入
        /// </summary>
        public int iSetId = 0;

        /// <summary>
        /// 真实段位（如果本赛季未完成定级赛，那么是上个赛季的段位）
        /// </summary>
        public int iRealRankLevel = 0;

        /// <summary>
        /// 新手挑战任务ID列表
        /// </summary>
        public System.Collections.Generic.List<int> noviceTaskIDs;

        /// <summary>
        /// 废弃!
        /// </summary>
        public int newPlayerGameNum = 0;

        /// <summary>
        /// 是否展示阵容推荐
        /// </summary>
        public bool bShowLineUpRecommend = false;

        /// <summary>
        /// 阵容推荐结束时间
        /// </summary>
        public int iLineUpRecommendEndTime = 0;

        /// <summary>
        /// 阵容推荐结束剩余局数
        /// </summary>
        public int iLineUpRecommendEndBattleNum = 0;

        /// <summary>
        /// 新手温暖局任务信息
        /// </summary>
        public TGameToClientNewbeeMissionInfo stNewbeeMissionInfo;

        /// <summary>
        /// 是否强制弹出阵容推荐
        /// </summary>
        public bool bBombLineUpRecommend = false;

        /// <summary>
        /// 废弃!
        /// </summary>
        public float fWarmValue = 0;

        /// <summary>
        /// 当前模式是否开启温暖值系统
        /// </summary>
        public bool bOpenWarmSwitch = false;

        /// <summary>
        /// turbo排位数据
        /// </summary>
        public TurboRankInfo turboRankInfo;

        /// <summary>
        /// 废弃!
        /// </summary>
        public int warmMatchLevel = 0;

        /// <summary>
        /// 胜点
        /// </summary>
        public int iLP = 0;

        /// <summary>
        /// 阵容推荐相关
        /// </summary>
        public TKFrame.TKDictionary<int, string> lineUpMap;

        /// <summary>
        /// 温暖flag等级(如果大于0，表示触发了温暖匹配规则)
        /// </summary>
        public int warmFlag = 0;

        /// <summary>
        /// 赛季ID
        /// </summary>
        public int iSeasonID = 0;

        /// <summary>
        /// 使用中的英雄局内特效(包括皮肤, 播报)
        /// </summary>
        public HeroInGameEffectData heroInGameEffectData;

        /// <summary>
        /// 赛事排位数据
        /// </summary>
        public ContestRankInfo contestRankInfo;

        /// <summary>
        /// 客户端玩法配置ACG_Set表索引ID
        /// </summary>
        public int setIndex = 0;

        /// <summary>
        /// 愚人节恢复头像任务是否完成
        /// </summary>
        public bool bFoolDayTaskFinish = false;

        /// <summary>
        /// 个人名片背景id
        /// </summary>
        public int iBusinessCardBackgroundID = 0;

        /// <summary>
        /// 数平温暖局信息
        /// </summary>
        public WarmFlagByDataPlatformInfo warmFlagByDataPlatformInfo;

        /// <summary>
        /// 统计数据
        /// </summary>
        public TGameRecordData gameRecordData;

        /// <summary>
        /// 玩家展示的成就
        /// </summary>
        public System.Collections.Generic.List<int> achievementsToDisplay;

        /// <summary>
        /// 玩家拥有的棋盘数
        /// </summary>
        public int hasMapNum = 0;

        /// <summary>
        /// 玩家拥有的小小英雄数
        /// </summary>
        public int hasTinyNum = 0;

        /// <summary>
        /// 玩家愚人节活动数据
        /// </summary>
        public AprilFoolsActivityInfo aprilFoolsActivityInfo;

        /// <summary>
        /// 冲榜赛需要隐藏的数据，key为座位，value为需要隐藏的信息
        /// </summary>
        public TKFrame.TKDictionary<int, JOCHiddenInfo> jocInfoMap;

        /// <summary>
        /// Set.9 Legends id
        /// </summary>
        public int playbookId = 0;

        /// <summary>
        /// JOC战斗手册BP等级
        /// </summary>
        public int jocManualBPLevel = 0;

        /// <summary>
        /// JOC冲榜赛榜单排名
        /// </summary>
        public int jocRank = 0;

        /// <summary>
        /// 局内按钮id
        /// </summary>
        public int inGameButtonID = 0;

        /// <summary>
        /// 客户端透传信息
        /// </summary>
        public TClientPassThroughInfo clientPassThroughInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lTotalGameNum, 0);
            _os.Write(iRankLevel, 1);
            _os.Write(iWarmMatchType, 2);
            _os.Write(iBountyContestId, 5);
            _os.Write(iSetId, 6);
            _os.Write(iRealRankLevel, 7);
            _os.Write(noviceTaskIDs, 8);
            _os.Write(newPlayerGameNum, 11);
            _os.Write(bShowLineUpRecommend, 12);
            _os.Write(iLineUpRecommendEndTime, 13);
            _os.Write(iLineUpRecommendEndBattleNum, 14);
            _os.Write(stNewbeeMissionInfo, 15);
            _os.Write(bBombLineUpRecommend, 16);
            _os.Write(fWarmValue, 17);
            _os.Write(bOpenWarmSwitch, 18);
            _os.Write(turboRankInfo, 19);
            _os.Write(warmMatchLevel, 20);
            _os.Write(iLP, 21);
            _os.Write(lineUpMap, 22);
            _os.Write(warmFlag, 23);
            _os.Write(iSeasonID, 24);
            _os.Write(heroInGameEffectData, 27);
            _os.Write(contestRankInfo, 28);
            _os.Write(setIndex, 29);
            _os.Write(bFoolDayTaskFinish, 30);
            _os.Write(iBusinessCardBackgroundID, 31);
            _os.Write(warmFlagByDataPlatformInfo, 32);
            _os.Write(gameRecordData, 33);
            _os.Write(achievementsToDisplay, 34);
            _os.Write(hasMapNum, 35);
            _os.Write(hasTinyNum, 36);
            _os.Write(aprilFoolsActivityInfo, 37);
            _os.Write(jocInfoMap, 38);
            _os.Write(playbookId, 39);
            _os.Write(jocManualBPLevel, 40);
            _os.Write(jocRank, 41);
            _os.Write(inGameButtonID, 42);
            _os.Write(clientPassThroughInfo, 43);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lTotalGameNum = (long) _is.Read(lTotalGameNum, 0, false);

            iRankLevel = (int) _is.Read(iRankLevel, 1, false);

            iWarmMatchType = (int) _is.Read(iWarmMatchType, 2, false);

            iBountyContestId = (int) _is.Read(iBountyContestId, 5, false);

            iSetId = (int) _is.Read(iSetId, 6, false);

            iRealRankLevel = (int) _is.Read(iRealRankLevel, 7, false);

            noviceTaskIDs = (System.Collections.Generic.List<int>) _is.Read(noviceTaskIDs, 8, false);

            newPlayerGameNum = (int) _is.Read(newPlayerGameNum, 11, false);

            bShowLineUpRecommend = (bool) _is.Read(bShowLineUpRecommend, 12, false);

            iLineUpRecommendEndTime = (int) _is.Read(iLineUpRecommendEndTime, 13, false);

            iLineUpRecommendEndBattleNum = (int) _is.Read(iLineUpRecommendEndBattleNum, 14, false);

            stNewbeeMissionInfo = (TGameToClientNewbeeMissionInfo) _is.Read(stNewbeeMissionInfo, 15, false);

            bBombLineUpRecommend = (bool) _is.Read(bBombLineUpRecommend, 16, false);

            fWarmValue = (float) _is.Read(fWarmValue, 17, false);

            bOpenWarmSwitch = (bool) _is.Read(bOpenWarmSwitch, 18, false);

            turboRankInfo = (TurboRankInfo) _is.Read(turboRankInfo, 19, false);

            warmMatchLevel = (int) _is.Read(warmMatchLevel, 20, false);

            iLP = (int) _is.Read(iLP, 21, false);

            lineUpMap = (TKFrame.TKDictionary<int, string>) _is.Read(lineUpMap, 22, false);

            warmFlag = (int) _is.Read(warmFlag, 23, false);

            iSeasonID = (int) _is.Read(iSeasonID, 24, false);

            heroInGameEffectData = (HeroInGameEffectData) _is.Read(heroInGameEffectData, 27, false);

            contestRankInfo = (ContestRankInfo) _is.Read(contestRankInfo, 28, false);

            setIndex = (int) _is.Read(setIndex, 29, false);

            bFoolDayTaskFinish = (bool) _is.Read(bFoolDayTaskFinish, 30, false);

            iBusinessCardBackgroundID = (int) _is.Read(iBusinessCardBackgroundID, 31, false);

            warmFlagByDataPlatformInfo = (WarmFlagByDataPlatformInfo) _is.Read(warmFlagByDataPlatformInfo, 32, false);

            gameRecordData = (TGameRecordData) _is.Read(gameRecordData, 33, false);

            achievementsToDisplay = (System.Collections.Generic.List<int>) _is.Read(achievementsToDisplay, 34, false);

            hasMapNum = (int) _is.Read(hasMapNum, 35, false);

            hasTinyNum = (int) _is.Read(hasTinyNum, 36, false);

            aprilFoolsActivityInfo = (AprilFoolsActivityInfo) _is.Read(aprilFoolsActivityInfo, 37, false);

            jocInfoMap = (TKFrame.TKDictionary<int, JOCHiddenInfo>) _is.Read(jocInfoMap, 38, false);

            playbookId = (int) _is.Read(playbookId, 39, false);

            jocManualBPLevel = (int) _is.Read(jocManualBPLevel, 40, false);

            jocRank = (int) _is.Read(jocRank, 41, false);

            inGameButtonID = (int) _is.Read(inGameButtonID, 42, false);

            clientPassThroughInfo = (TClientPassThroughInfo) _is.Read(clientPassThroughInfo, 43, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lTotalGameNum, "lTotalGameNum");
            _ds.Display(iRankLevel, "iRankLevel");
            _ds.Display(iWarmMatchType, "iWarmMatchType");
            _ds.Display(iBountyContestId, "iBountyContestId");
            _ds.Display(iSetId, "iSetId");
            _ds.Display(iRealRankLevel, "iRealRankLevel");
            _ds.Display(noviceTaskIDs, "noviceTaskIDs");
            _ds.Display(newPlayerGameNum, "newPlayerGameNum");
            _ds.Display(bShowLineUpRecommend, "bShowLineUpRecommend");
            _ds.Display(iLineUpRecommendEndTime, "iLineUpRecommendEndTime");
            _ds.Display(iLineUpRecommendEndBattleNum, "iLineUpRecommendEndBattleNum");
            _ds.Display(stNewbeeMissionInfo, "stNewbeeMissionInfo");
            _ds.Display(bBombLineUpRecommend, "bBombLineUpRecommend");
            _ds.Display(fWarmValue, "fWarmValue");
            _ds.Display(bOpenWarmSwitch, "bOpenWarmSwitch");
            _ds.Display(turboRankInfo, "turboRankInfo");
            _ds.Display(warmMatchLevel, "warmMatchLevel");
            _ds.Display(iLP, "iLP");
            _ds.Display(lineUpMap, "lineUpMap");
            _ds.Display(warmFlag, "warmFlag");
            _ds.Display(iSeasonID, "iSeasonID");
            _ds.Display(heroInGameEffectData, "heroInGameEffectData");
            _ds.Display(contestRankInfo, "contestRankInfo");
            _ds.Display(setIndex, "setIndex");
            _ds.Display(bFoolDayTaskFinish, "bFoolDayTaskFinish");
            _ds.Display(iBusinessCardBackgroundID, "iBusinessCardBackgroundID");
            _ds.Display(warmFlagByDataPlatformInfo, "warmFlagByDataPlatformInfo");
            _ds.Display(gameRecordData, "gameRecordData");
            _ds.Display(achievementsToDisplay, "achievementsToDisplay");
            _ds.Display(hasMapNum, "hasMapNum");
            _ds.Display(hasTinyNum, "hasTinyNum");
            _ds.Display(aprilFoolsActivityInfo, "aprilFoolsActivityInfo");
            _ds.Display(jocInfoMap, "jocInfoMap");
            _ds.Display(playbookId, "playbookId");
            _ds.Display(jocManualBPLevel, "jocManualBPLevel");
            _ds.Display(jocRank, "jocRank");
            _ds.Display(inGameButtonID, "inGameButtonID");
            _ds.Display(clientPassThroughInfo, "clientPassThroughInfo");
        }

        public override void Clear()
        {
            lTotalGameNum = 0;
            iRankLevel = 0;
            iWarmMatchType = 0;
            iBountyContestId = 0;
            iSetId = 0;
            iRealRankLevel = 0;
            noviceTaskIDs = null;
            newPlayerGameNum = 0;
            bShowLineUpRecommend = false;
            iLineUpRecommendEndTime = 0;
            iLineUpRecommendEndBattleNum = 0;
            stNewbeeMissionInfo = null;
            bBombLineUpRecommend = false;
            fWarmValue = 0;
            bOpenWarmSwitch = false;
            turboRankInfo = null;
            warmMatchLevel = 0;
            iLP = 0;
            lineUpMap = null;
            warmFlag = 0;
            iSeasonID = 0;
            heroInGameEffectData = null;
            contestRankInfo = null;
            setIndex = 0;
            bFoolDayTaskFinish = false;
            iBusinessCardBackgroundID = 0;
            warmFlagByDataPlatformInfo = null;
            gameRecordData = null;
            achievementsToDisplay = null;
            hasMapNum = 0;
            hasTinyNum = 0;
            aprilFoolsActivityInfo = null;
            jocInfoMap = null;
            playbookId = 0;
            jocManualBPLevel = 0;
            jocRank = 0;
            inGameButtonID = 0;
            clientPassThroughInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGameToClientPlayerInfo();
            copied.lTotalGameNum = this.lTotalGameNum;
            copied.iRankLevel = this.iRankLevel;
            copied.iWarmMatchType = this.iWarmMatchType;
            copied.iBountyContestId = this.iBountyContestId;
            copied.iSetId = this.iSetId;
            copied.iRealRankLevel = this.iRealRankLevel;
            copied.noviceTaskIDs = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.noviceTaskIDs);
            copied.newPlayerGameNum = this.newPlayerGameNum;
            copied.bShowLineUpRecommend = this.bShowLineUpRecommend;
            copied.iLineUpRecommendEndTime = this.iLineUpRecommendEndTime;
            copied.iLineUpRecommendEndBattleNum = this.iLineUpRecommendEndBattleNum;
            copied.stNewbeeMissionInfo = (TGameToClientNewbeeMissionInfo)JceUtil.DeepClone(this.stNewbeeMissionInfo);
            copied.bBombLineUpRecommend = this.bBombLineUpRecommend;
            copied.fWarmValue = this.fWarmValue;
            copied.bOpenWarmSwitch = this.bOpenWarmSwitch;
            copied.turboRankInfo = (TurboRankInfo)JceUtil.DeepClone(this.turboRankInfo);
            copied.warmMatchLevel = this.warmMatchLevel;
            copied.iLP = this.iLP;
            copied.lineUpMap = (TKFrame.TKDictionary<int, string>)JceUtil.DeepClone(this.lineUpMap);
            copied.warmFlag = this.warmFlag;
            copied.iSeasonID = this.iSeasonID;
            copied.heroInGameEffectData = (HeroInGameEffectData)JceUtil.DeepClone(this.heroInGameEffectData);
            copied.contestRankInfo = (ContestRankInfo)JceUtil.DeepClone(this.contestRankInfo);
            copied.setIndex = this.setIndex;
            copied.bFoolDayTaskFinish = this.bFoolDayTaskFinish;
            copied.iBusinessCardBackgroundID = this.iBusinessCardBackgroundID;
            copied.warmFlagByDataPlatformInfo = (WarmFlagByDataPlatformInfo)JceUtil.DeepClone(this.warmFlagByDataPlatformInfo);
            copied.gameRecordData = (TGameRecordData)JceUtil.DeepClone(this.gameRecordData);
            copied.achievementsToDisplay = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.achievementsToDisplay);
            copied.hasMapNum = this.hasMapNum;
            copied.hasTinyNum = this.hasTinyNum;
            copied.aprilFoolsActivityInfo = (AprilFoolsActivityInfo)JceUtil.DeepClone(this.aprilFoolsActivityInfo);
            copied.jocInfoMap = (TKFrame.TKDictionary<int, JOCHiddenInfo>)JceUtil.DeepClone(this.jocInfoMap);
            copied.playbookId = this.playbookId;
            copied.jocManualBPLevel = this.jocManualBPLevel;
            copied.jocRank = this.jocRank;
            copied.inGameButtonID = this.inGameButtonID;
            copied.clientPassThroughInfo = (TClientPassThroughInfo)JceUtil.DeepClone(this.clientPassThroughInfo);
            return copied;
        }
    }
}

