// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class PveHeroAttr : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<int> equipIDs;

        public int location = 0;

        public int heroID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(equipIDs, 0);
            _os.Write(location, 1);
            _os.Write(heroID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            equipIDs = (System.Collections.Generic.List<int>) _is.Read(equipIDs, 0, false);

            location = (int) _is.Read(location, 1, false);

            heroID = (int) _is.Read(heroID, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(equipIDs, "equipIDs");
            _ds.Display(location, "location");
            _ds.Display(heroID, "heroID");
        }

        public override void Clear()
        {
            equipIDs = null;
            location = 0;
            heroID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new PveHeroAttr();
            copied.equipIDs = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.equipIDs);
            copied.location = this.location;
            copied.heroID = this.heroID;
            return copied;
        }
    }
}

