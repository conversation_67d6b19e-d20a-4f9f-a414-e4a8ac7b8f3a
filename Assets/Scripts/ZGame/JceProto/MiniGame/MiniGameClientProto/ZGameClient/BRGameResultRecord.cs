// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BRGameResultRecord : Wup.Jce.JceStruct
    {
        public int roundId = 0;

        public int lastRoundEndFrameId = 0;

        public System.Collections.Generic.List<RoundGroupData> roundGroupDataList;

        public TKFrame.TKDictionary<long, int> lifeMap;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(roundId, 0);
            _os.Write(lastRoundEndFrameId, 1);
            _os.Write(roundGroupDataList, 2);
            _os.Write(lifeMap, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            roundId = (int) _is.Read(roundId, 0, false);

            lastRoundEndFrameId = (int) _is.Read(lastRoundEndFrameId, 1, false);

            roundGroupDataList = (System.Collections.Generic.List<RoundGroupData>) _is.Read(roundGroupDataList, 2, false);

            lifeMap = (TKFrame.TKDictionary<long, int>) _is.Read(lifeMap, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(roundId, "roundId");
            _ds.Display(lastRoundEndFrameId, "lastRoundEndFrameId");
            _ds.Display(roundGroupDataList, "roundGroupDataList");
            _ds.Display(lifeMap, "lifeMap");
        }

        public override void Clear()
        {
            roundId = 0;
            lastRoundEndFrameId = 0;
            roundGroupDataList = null;
            lifeMap = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BRGameResultRecord();
            copied.roundId = this.roundId;
            copied.lastRoundEndFrameId = this.lastRoundEndFrameId;
            copied.roundGroupDataList = (System.Collections.Generic.List<RoundGroupData>)JceUtil.DeepClone(this.roundGroupDataList);
            copied.lifeMap = (TKFrame.TKDictionary<long, int>)JceUtil.DeepClone(this.lifeMap);
            return copied;
        }
    }
}

