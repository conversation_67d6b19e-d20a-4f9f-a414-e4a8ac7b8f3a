// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BeginBCRoguelikeReq : Wup.Jce.JceStruct
    {
        public int gameModuleID = 0;

        public int levelID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(gameModuleID, 0);
            _os.Write(levelID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            gameModuleID = (int) _is.Read(gameModuleID, 0, false);

            levelID = (int) _is.Read(levelID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(gameModuleID, "gameModuleID");
            _ds.Display(levelID, "levelID");
        }

        public override void Clear()
        {
            gameModuleID = 0;
            levelID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BeginBCRoguelikeReq();
            copied.gameModuleID = this.gameModuleID;
            copied.levelID = this.levelID;
            return copied;
        }
    }
}

