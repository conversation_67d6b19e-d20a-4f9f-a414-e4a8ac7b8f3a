// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJTechnicalChestInfo : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public int iShowItemID = 0;

        public int iDropID = 0;

        public int iCurrencyID = 0;

        public string iBeginTime = "";

        public string iEndTime = "";

        public int iBeginSec = 0;

        public int iEndSec = 0;

        public int iOpenCount1 = 0;

        public int iCurrencyNum1 = 0;

        public int iDiscountCurrencyNum1 = 0;

        public int iOpenCount2 = 0;

        public int iCurrencyNum2 = 0;

        public int iDiscountCurrencyNum2 = 0;

        public string sChestDesc = "";

        public string sRewardDesc = "";

        public int iPage = 0;

        public System.Collections.Generic.List<TRewardInJTechnicalChest> vecRewardList;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(iShowItemID, 2);
            _os.Write(iDropID, 3);
            _os.Write(iCurrencyID, 4);
            _os.Write(iBeginTime, 5);
            _os.Write(iEndTime, 6);
            _os.Write(iBeginSec, 7);
            _os.Write(iEndSec, 8);
            _os.Write(iOpenCount1, 9);
            _os.Write(iCurrencyNum1, 10);
            _os.Write(iDiscountCurrencyNum1, 11);
            _os.Write(iOpenCount2, 12);
            _os.Write(iCurrencyNum2, 13);
            _os.Write(iDiscountCurrencyNum2, 14);
            _os.Write(sChestDesc, 15);
            _os.Write(sRewardDesc, 16);
            _os.Write(iPage, 17);
            _os.Write(vecRewardList, 18);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            iShowItemID = (int) _is.Read(iShowItemID, 2, false);

            iDropID = (int) _is.Read(iDropID, 3, false);

            iCurrencyID = (int) _is.Read(iCurrencyID, 4, false);

            iBeginTime = (string) _is.Read(iBeginTime, 5, false);

            iEndTime = (string) _is.Read(iEndTime, 6, false);

            iBeginSec = (int) _is.Read(iBeginSec, 7, false);

            iEndSec = (int) _is.Read(iEndSec, 8, false);

            iOpenCount1 = (int) _is.Read(iOpenCount1, 9, false);

            iCurrencyNum1 = (int) _is.Read(iCurrencyNum1, 10, false);

            iDiscountCurrencyNum1 = (int) _is.Read(iDiscountCurrencyNum1, 11, false);

            iOpenCount2 = (int) _is.Read(iOpenCount2, 12, false);

            iCurrencyNum2 = (int) _is.Read(iCurrencyNum2, 13, false);

            iDiscountCurrencyNum2 = (int) _is.Read(iDiscountCurrencyNum2, 14, false);

            sChestDesc = (string) _is.Read(sChestDesc, 15, false);

            sRewardDesc = (string) _is.Read(sRewardDesc, 16, false);

            iPage = (int) _is.Read(iPage, 17, false);

            vecRewardList = (System.Collections.Generic.List<TRewardInJTechnicalChest>) _is.Read(vecRewardList, 18, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(iShowItemID, "iShowItemID");
            _ds.Display(iDropID, "iDropID");
            _ds.Display(iCurrencyID, "iCurrencyID");
            _ds.Display(iBeginTime, "iBeginTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(iBeginSec, "iBeginSec");
            _ds.Display(iEndSec, "iEndSec");
            _ds.Display(iOpenCount1, "iOpenCount1");
            _ds.Display(iCurrencyNum1, "iCurrencyNum1");
            _ds.Display(iDiscountCurrencyNum1, "iDiscountCurrencyNum1");
            _ds.Display(iOpenCount2, "iOpenCount2");
            _ds.Display(iCurrencyNum2, "iCurrencyNum2");
            _ds.Display(iDiscountCurrencyNum2, "iDiscountCurrencyNum2");
            _ds.Display(sChestDesc, "sChestDesc");
            _ds.Display(sRewardDesc, "sRewardDesc");
            _ds.Display(iPage, "iPage");
            _ds.Display(vecRewardList, "vecRewardList");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            iShowItemID = 0;
            iDropID = 0;
            iCurrencyID = 0;
            iBeginTime = "";
            iEndTime = "";
            iBeginSec = 0;
            iEndSec = 0;
            iOpenCount1 = 0;
            iCurrencyNum1 = 0;
            iDiscountCurrencyNum1 = 0;
            iOpenCount2 = 0;
            iCurrencyNum2 = 0;
            iDiscountCurrencyNum2 = 0;
            sChestDesc = "";
            sRewardDesc = "";
            iPage = 0;
            vecRewardList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJTechnicalChestInfo();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.iShowItemID = this.iShowItemID;
            copied.iDropID = this.iDropID;
            copied.iCurrencyID = this.iCurrencyID;
            copied.iBeginTime = this.iBeginTime;
            copied.iEndTime = this.iEndTime;
            copied.iBeginSec = this.iBeginSec;
            copied.iEndSec = this.iEndSec;
            copied.iOpenCount1 = this.iOpenCount1;
            copied.iCurrencyNum1 = this.iCurrencyNum1;
            copied.iDiscountCurrencyNum1 = this.iDiscountCurrencyNum1;
            copied.iOpenCount2 = this.iOpenCount2;
            copied.iCurrencyNum2 = this.iCurrencyNum2;
            copied.iDiscountCurrencyNum2 = this.iDiscountCurrencyNum2;
            copied.sChestDesc = this.sChestDesc;
            copied.sRewardDesc = this.sRewardDesc;
            copied.iPage = this.iPage;
            copied.vecRewardList = (System.Collections.Generic.List<TRewardInJTechnicalChest>)JceUtil.DeepClone(this.vecRewardList);
            return copied;
        }
    }
}

