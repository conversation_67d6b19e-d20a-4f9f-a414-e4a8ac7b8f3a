//所在的Excel 【ACG_Set.xlsm】
//***********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_SetController_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSet = 0;

        public int iPlanID = 0;

        public int iCtrlID = 0;

        public string sParams1 = "";

        public string sParams2 = "";

        public string sParams3 = "";

        public string sParams4 = "";

        public string sParams5 = "";

        public string sFetterInfo = "";

        public string sParams6 = "";

        public string sParams7 = "";

        public string sParams8 = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSet, 1);
            _os.Write(iPlanID, 2);
            _os.Write(iCtrlID, 3);
            _os.Write(sParams1, 4);
            _os.Write(sParams2, 5);
            _os.Write(sParams3, 6);
            _os.Write(sParams4, 7);
            _os.Write(sParams5, 8);
            _os.Write(sFetterInfo, 9);
            _os.Write(sParams6, 10);
            _os.Write(sParams7, 11);
            _os.Write(sParams8, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSet = (int) _is.Read(iSet, 1, false);

            iPlanID = (int) _is.Read(iPlanID, 2, false);

            iCtrlID = (int) _is.Read(iCtrlID, 3, false);

            sParams1 = (string) _is.Read(sParams1, 4, false);

            sParams2 = (string) _is.Read(sParams2, 5, false);

            sParams3 = (string) _is.Read(sParams3, 6, false);

            sParams4 = (string) _is.Read(sParams4, 7, false);

            sParams5 = (string) _is.Read(sParams5, 8, false);

            sFetterInfo = (string) _is.Read(sFetterInfo, 9, false);

            sParams6 = (string) _is.Read(sParams6, 10, false);

            sParams7 = (string) _is.Read(sParams7, 11, false);

            sParams8 = (string) _is.Read(sParams8, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSet, "iSet");
            _ds.Display(iPlanID, "iPlanID");
            _ds.Display(iCtrlID, "iCtrlID");
            _ds.Display(sParams1, "sParams1");
            _ds.Display(sParams2, "sParams2");
            _ds.Display(sParams3, "sParams3");
            _ds.Display(sParams4, "sParams4");
            _ds.Display(sParams5, "sParams5");
            _ds.Display(sFetterInfo, "sFetterInfo");
            _ds.Display(sParams6, "sParams6");
            _ds.Display(sParams7, "sParams7");
            _ds.Display(sParams8, "sParams8");
        }

        public override void Clear()
        {
            iID = 0;
            iSet = 0;
            iPlanID = 0;
            iCtrlID = 0;
            sParams1 = "";
            sParams2 = "";
            sParams3 = "";
            sParams4 = "";
            sParams5 = "";
            sFetterInfo = "";
            sParams6 = "";
            sParams7 = "";
            sParams8 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_SetController_Client();
            copied.iID = this.iID;
            copied.iSet = this.iSet;
            copied.iPlanID = this.iPlanID;
            copied.iCtrlID = this.iCtrlID;
            copied.sParams1 = this.sParams1;
            copied.sParams2 = this.sParams2;
            copied.sParams3 = this.sParams3;
            copied.sParams4 = this.sParams4;
            copied.sParams5 = this.sParams5;
            copied.sFetterInfo = this.sFetterInfo;
            copied.sParams6 = this.sParams6;
            copied.sParams7 = this.sParams7;
            copied.sParams8 = this.sParams8;
            return copied;
        }
    }
}

