// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardCollectCheckRedResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public bool isRed = true;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(isRed, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            isRed = (bool) _is.Read(isRed, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(isRed, "isRed");
        }

        public override void Clear()
        {
            err = 0;
            isRed = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardCollectCheckRedResp();
            copied.err = this.err;
            copied.isRed = this.isRed;
            return copied;
        }
    }
}

