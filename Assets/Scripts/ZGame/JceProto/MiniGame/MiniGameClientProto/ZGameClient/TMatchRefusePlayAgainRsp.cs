// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMatchRefusePlayAgainRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
        }

        public override void Clear()
        {
            ret = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMatchRefusePlayAgainRsp();
            copied.ret = this.ret;
            return copied;
        }
    }
}

