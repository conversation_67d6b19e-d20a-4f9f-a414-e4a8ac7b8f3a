//所在的Excel 【ACG_Equipment.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ExtraStealEquipRate_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iEquipID1 = 0;

        public int iEquipID2 = 0;

        public int iStealRate = 0;

        public int iPlanID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iEquipID1, 1);
            _os.Write(iEquipID2, 2);
            _os.Write(iStealRate, 3);
            _os.Write(iPlanID, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iEquipID1 = (int) _is.Read(iEquipID1, 1, false);

            iEquipID2 = (int) _is.Read(iEquipID2, 2, false);

            iStealRate = (int) _is.Read(iStealRate, 3, false);

            iPlanID = (int) _is.Read(iPlanID, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iEquipID1, "iEquipID1");
            _ds.Display(iEquipID2, "iEquipID2");
            _ds.Display(iStealRate, "iStealRate");
            _ds.Display(iPlanID, "iPlanID");
        }

        public override void Clear()
        {
            iID = 0;
            iEquipID1 = 0;
            iEquipID2 = 0;
            iStealRate = 0;
            iPlanID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ExtraStealEquipRate_Client();
            copied.iID = this.iID;
            copied.iEquipID1 = this.iEquipID1;
            copied.iEquipID2 = this.iEquipID2;
            copied.iStealRate = this.iStealRate;
            copied.iPlanID = this.iPlanID;
            return copied;
        }
    }
}

