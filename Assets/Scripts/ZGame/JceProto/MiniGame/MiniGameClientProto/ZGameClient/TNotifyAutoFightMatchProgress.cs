// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TNotifyAutoFightMatchProgress : Wup.Jce.JceStruct
    {
        int _iMatchedState = 0;
        public int iMatchedState
        {
            get
            {
                 return _iMatchedState;
            }
            set
            {
                _iMatchedState = value; 
            }
        }

        int _iMatchedUserCount = 0;
        public int iMatchedUserCount
        {
            get
            {
                 return _iMatchedUserCount;
            }
            set
            {
                _iMatchedUserCount = value; 
            }
        }

        int _iNeedUserCount = 0;
        public int iNeedUserCount
        {
            get
            {
                 return _iNeedUserCount;
            }
            set
            {
                _iNeedUserCount = value; 
            }
        }

        public System.Collections.Generic.List<TAutoFightMatchPlayerInfo> vecMatchPlayerInfo {get; set;} 

        int _iJoinScen = 0;
        public int iJoinScen
        {
            get
            {
                 return _iJoinScen;
            }
            set
            {
                _iJoinScen = value; 
            }
        }

        public byte[] vecBuff {get; set;} 

        string _strTips = "";
        public string strTips
        {
            get
            {
                 return _strTips;
            }
            set
            {
                _strTips = value; 
            }
        }

        int _iPlayMatchType = 0;
        public int iPlayMatchType
        {
            get
            {
                 return _iPlayMatchType;
            }
            set
            {
                _iPlayMatchType = value; 
            }
        }

        int _iPredictedMatchTime = 0;
        public int iPredictedMatchTime
        {
            get
            {
                 return _iPredictedMatchTime;
            }
            set
            {
                _iPredictedMatchTime = value; 
            }
        }

        int _iConfirmTime = 0;
        public int iConfirmTime
        {
            get
            {
                 return _iConfirmTime;
            }
            set
            {
                _iConfirmTime = value; 
            }
        }

        int _iElapsedTime = 0;
        public int iElapsedTime
        {
            get
            {
                 return _iElapsedTime;
            }
            set
            {
                _iElapsedTime = value; 
            }
        }

        public System.Collections.Generic.List<long> vecConflictUinList {get; set;} 

        int _iPVEAILevel = 0;
        public int iPVEAILevel
        {
            get
            {
                 return _iPVEAILevel;
            }
            set
            {
                _iPVEAILevel = value; 
            }
        }

        int _iSceneID = 0;
        public int iSceneID
        {
            get
            {
                 return _iSceneID;
            }
            set
            {
                _iSceneID = value; 
            }
        }

        int _iRoomType = 0;
        public int iRoomType
        {
            get
            {
                 return _iRoomType;
            }
            set
            {
                _iRoomType = value; 
            }
        }

        public System.Collections.Generic.List<TAutoFightMatchPlayerInfo> vecObserverPlayerInfo {get; set;} 

        int _iLimitUserCount = 0;
        public int iLimitUserCount
        {
            get
            {
                 return _iLimitUserCount;
            }
            set
            {
                _iLimitUserCount = value; 
            }
        }

        public TBountyInfo stBountyInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iMatchedState, 0);
            _os.Write(iMatchedUserCount, 1);
            _os.Write(iNeedUserCount, 2);
            _os.Write(vecMatchPlayerInfo, 3);
            _os.Write(iJoinScen, 4);
            _os.Write(vecBuff, 5);
            _os.Write(strTips, 6);
            _os.Write(iPlayMatchType, 7);
            _os.Write(iPredictedMatchTime, 8);
            _os.Write(iConfirmTime, 9);
            _os.Write(iElapsedTime, 10);
            _os.Write(vecConflictUinList, 11);
            _os.Write(iPVEAILevel, 12);
            _os.Write(iSceneID, 13);
            _os.Write(iRoomType, 14);
            _os.Write(vecObserverPlayerInfo, 15);
            _os.Write(iLimitUserCount, 16);
            _os.Write(stBountyInfo, 17);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iMatchedState = (int) _is.Read(iMatchedState, 0, false);

            iMatchedUserCount = (int) _is.Read(iMatchedUserCount, 1, false);

            iNeedUserCount = (int) _is.Read(iNeedUserCount, 2, false);

            vecMatchPlayerInfo = (System.Collections.Generic.List<TAutoFightMatchPlayerInfo>) _is.Read(vecMatchPlayerInfo, 3, false);

            iJoinScen = (int) _is.Read(iJoinScen, 4, false);

            vecBuff = (byte[]) _is.Read(vecBuff, 5, false);

            strTips = (string) _is.Read(strTips, 6, false);

            iPlayMatchType = (int) _is.Read(iPlayMatchType, 7, false);

            iPredictedMatchTime = (int) _is.Read(iPredictedMatchTime, 8, false);

            iConfirmTime = (int) _is.Read(iConfirmTime, 9, false);

            iElapsedTime = (int) _is.Read(iElapsedTime, 10, false);

            vecConflictUinList = (System.Collections.Generic.List<long>) _is.Read(vecConflictUinList, 11, false);

            iPVEAILevel = (int) _is.Read(iPVEAILevel, 12, false);

            iSceneID = (int) _is.Read(iSceneID, 13, false);

            iRoomType = (int) _is.Read(iRoomType, 14, false);

            vecObserverPlayerInfo = (System.Collections.Generic.List<TAutoFightMatchPlayerInfo>) _is.Read(vecObserverPlayerInfo, 15, false);

            iLimitUserCount = (int) _is.Read(iLimitUserCount, 16, false);

            stBountyInfo = (TBountyInfo) _is.Read(stBountyInfo, 17, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iMatchedState, "iMatchedState");
            _ds.Display(iMatchedUserCount, "iMatchedUserCount");
            _ds.Display(iNeedUserCount, "iNeedUserCount");
            _ds.Display(vecMatchPlayerInfo, "vecMatchPlayerInfo");
            _ds.Display(iJoinScen, "iJoinScen");
            _ds.Display(vecBuff, "vecBuff");
            _ds.Display(strTips, "strTips");
            _ds.Display(iPlayMatchType, "iPlayMatchType");
            _ds.Display(iPredictedMatchTime, "iPredictedMatchTime");
            _ds.Display(iConfirmTime, "iConfirmTime");
            _ds.Display(iElapsedTime, "iElapsedTime");
            _ds.Display(vecConflictUinList, "vecConflictUinList");
            _ds.Display(iPVEAILevel, "iPVEAILevel");
            _ds.Display(iSceneID, "iSceneID");
            _ds.Display(iRoomType, "iRoomType");
            _ds.Display(vecObserverPlayerInfo, "vecObserverPlayerInfo");
            _ds.Display(iLimitUserCount, "iLimitUserCount");
            _ds.Display(stBountyInfo, "stBountyInfo");
        }

        public override void Clear()
        {
            iMatchedState = 0;
            iMatchedUserCount = 0;
            iNeedUserCount = 0;
            vecMatchPlayerInfo = null;
            iJoinScen = 0;
            vecBuff = null;
            strTips = "";
            iPlayMatchType = 0;
            iPredictedMatchTime = 0;
            iConfirmTime = 0;
            iElapsedTime = 0;
            vecConflictUinList = null;
            iPVEAILevel = 0;
            iSceneID = 0;
            iRoomType = 0;
            vecObserverPlayerInfo = null;
            iLimitUserCount = 0;
            stBountyInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TNotifyAutoFightMatchProgress();
            copied.iMatchedState = this.iMatchedState;
            copied.iMatchedUserCount = this.iMatchedUserCount;
            copied.iNeedUserCount = this.iNeedUserCount;
            copied.vecMatchPlayerInfo = (System.Collections.Generic.List<TAutoFightMatchPlayerInfo>)JceUtil.DeepClone(this.vecMatchPlayerInfo);
            copied.iJoinScen = this.iJoinScen;
            copied.vecBuff = (byte[])JceUtil.DeepClone(this.vecBuff);
            copied.strTips = this.strTips;
            copied.iPlayMatchType = this.iPlayMatchType;
            copied.iPredictedMatchTime = this.iPredictedMatchTime;
            copied.iConfirmTime = this.iConfirmTime;
            copied.iElapsedTime = this.iElapsedTime;
            copied.vecConflictUinList = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.vecConflictUinList);
            copied.iPVEAILevel = this.iPVEAILevel;
            copied.iSceneID = this.iSceneID;
            copied.iRoomType = this.iRoomType;
            copied.vecObserverPlayerInfo = (System.Collections.Generic.List<TAutoFightMatchPlayerInfo>)JceUtil.DeepClone(this.vecObserverPlayerInfo);
            copied.iLimitUserCount = this.iLimitUserCount;
            copied.stBountyInfo = (TBountyInfo)JceUtil.DeepClone(this.stBountyInfo);
            return copied;
        }
    }
}

