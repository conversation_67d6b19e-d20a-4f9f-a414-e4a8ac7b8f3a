// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCardScoreAdvancedParam_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iReportType = 0;

        public int iStartValue = 0;

        public int iScore = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iReportType, 1);
            _os.Write(iStartValue, 2);
            _os.Write(iScore, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iReportType = (int) _is.Read(iReportType, 1, false);

            iStartValue = (int) _is.Read(iStartValue, 2, false);

            iScore = (int) _is.Read(iScore, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iReportType, "iReportType");
            _ds.Display(iStartValue, "iStartValue");
            _ds.Display(iScore, "iScore");
        }

        public override void Clear()
        {
            iID = 0;
            iReportType = 0;
            iStartValue = 0;
            iScore = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCardScoreAdvancedParam_Client();
            copied.iID = this.iID;
            copied.iReportType = this.iReportType;
            copied.iStartValue = this.iStartValue;
            copied.iScore = this.iScore;
            return copied;
        }
    }
}

