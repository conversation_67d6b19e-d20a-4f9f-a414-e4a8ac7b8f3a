// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetEnlightenRewardReq : Wup.Jce.JceStruct
    {
        int _iTimestamp = 0;
        public int iTimestamp
        {
            get
            {
                 return _iTimestamp;
            }
            set
            {
                _iTimestamp = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTimestamp, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTimestamp = (int) _is.Read(iTimestamp, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTimestamp, "iTimestamp");
        }

        public override void Clear()
        {
            iTimestamp = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetEnlightenRewardReq();
            copied.iTimestamp = this.iTimestamp;
            return copied;
        }
    }
}

