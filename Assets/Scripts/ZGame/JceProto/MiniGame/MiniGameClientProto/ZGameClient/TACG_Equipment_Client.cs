//所在的Excel 【ACG_Equipment.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Equipment_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public int iLevel = 0;

        public string sDesc = "";

        public string sIcon = "";

        public int iSynthesisID1 = 0;

        public int iSynthesisID2 = 0;

        public int iEffectType = 0;

        public int iWeights = 0;

        public string sEffectName = "";

        public int iEffectPos = 0;

        public int iEffectIsLoop = 0;

        public int iEffectBattleIsShow = 0;

        public int iPreviewEffectSelectTargetType = 0;

        public string sPreviewEffectSelectTargetParam = "";

        public int iPreviewEffectType = 0;

        public string sPreviewEffectParam = "";

        public int iPreviewEffectTime = 0;

        public int iUnique = 0;

        public int iAttribute1 = 0;

        public int iAttributeValue1 = 0;

        public int iAttributeValueType1 = 0;

        public int iAttribute2 = 0;

        public int iAttributeValue2 = 0;

        public int iAttributeValueType2 = 0;

        public int iAttribute3 = 0;

        public int iAttributeValue3 = 0;

        public int iAttributeValueType3 = 0;

        public int iAttribute4 = 0;

        public int iAttributeValue4 = 0;

        public int iAttributeValueType4 = 0;

        public int iBuff1 = 0;

        public int iBuffType1 = 0;

        public int iBuff2 = 0;

        public int iBuffType2 = 0;

        public int iBuff3 = 0;

        public int iBuffType3 = 0;

        public int iAttributeValueRevealType1 = 0;

        public int iAttributeValueRevealType2 = 0;

        public int iAttributeValueRevealType3 = 0;

        public int iAttributeValueRevealType4 = 0;

        public int iOnDeadTakeType = 0;

        public string sOnDeadTakeStrParam = "";

        public int iOnDeadSelectorType = 0;

        public string sOnDeadSelectorParams = "";

        public int iInitBuffsCount = 0;

        public int iEffectParams = 0;

        public int iEffectParams2 = 0;

        public int ishowBuffLayerInUI = 0;

        public int iIsSummonInherit = 0;

        public int iIsSameEquipCoverInitBuffsCount = 0;

        public int iBuffLayer1 = 0;

        public int iBuffLayer2 = 0;

        public int iBuffLayer3 = 0;

        public int iSetID = 0;

        public int iChangeScale = 0;

        public int iChangeScaleIsTween = 0;

        public int iChangeScaleTime = 0;

        public int iSoulEquip = 0;

        public int iPlanID = 0;

        public int iDynamicAttrType = 0;

        public int iDynamicAttrValue = 0;

        public int iDynamicAttrValueType = 0;

        public int iColorID = 0;

        public int iSynthesisID3 = 0;

        public int iSynthesisID4 = 0;

        public int iSynthesisID5 = 0;

        public int iSynthesisID6 = 0;

        public int iType = 0;

        public int iMappingID = 0;

        public int iDynamicAttrValueShowRate = 0;

        public int iSortID = 0;

        public int iPosAttr = 0;

        public int iSynthesisEquip = 0;

        public int iTftEquipId = 0;

        public int iFilterId = 0;

        public string sAttribute = "";

        public string sAttributeValue = "";

        public string sAttributeValueType = "";

        public string sAttributeValueRevealType = "";

        public string sIconAbPath = "";

        public string sTypeName = "";

        public int iBuffIsCD1 = 0;

        public int iBuffIsCD2 = 0;

        public int iBuffIsCD3 = 0;

        public int iOnDeadSelectorExtendParam = 0;

        public string sEffectAttribute = "";

        public string sEffectAttributeParams = "";

        public string sEffectBuffProtoId = "";

        public string sEffectParams = "";

        public int iIsNeedRefreshBuffAssist = 0;

        public string sLayerBuff = "";

        public int iLiveCounter = 0;

        public int iEquipState = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(iLevel, 2);
            _os.Write(sDesc, 3);
            _os.Write(sIcon, 4);
            _os.Write(iSynthesisID1, 6);
            _os.Write(iSynthesisID2, 7);
            _os.Write(iEffectType, 8);
            _os.Write(iWeights, 11);
            _os.Write(sEffectName, 14);
            _os.Write(iEffectPos, 15);
            _os.Write(iEffectIsLoop, 16);
            _os.Write(iEffectBattleIsShow, 17);
            _os.Write(iPreviewEffectSelectTargetType, 18);
            _os.Write(sPreviewEffectSelectTargetParam, 19);
            _os.Write(iPreviewEffectType, 20);
            _os.Write(sPreviewEffectParam, 21);
            _os.Write(iPreviewEffectTime, 22);
            _os.Write(iUnique, 23);
            _os.Write(iAttribute1, 24);
            _os.Write(iAttributeValue1, 25);
            _os.Write(iAttributeValueType1, 26);
            _os.Write(iAttribute2, 27);
            _os.Write(iAttributeValue2, 28);
            _os.Write(iAttributeValueType2, 29);
            _os.Write(iAttribute3, 30);
            _os.Write(iAttributeValue3, 31);
            _os.Write(iAttributeValueType3, 32);
            _os.Write(iAttribute4, 33);
            _os.Write(iAttributeValue4, 34);
            _os.Write(iAttributeValueType4, 35);
            _os.Write(iBuff1, 36);
            _os.Write(iBuffType1, 37);
            _os.Write(iBuff2, 38);
            _os.Write(iBuffType2, 39);
            _os.Write(iBuff3, 40);
            _os.Write(iBuffType3, 41);
            _os.Write(iAttributeValueRevealType1, 42);
            _os.Write(iAttributeValueRevealType2, 43);
            _os.Write(iAttributeValueRevealType3, 44);
            _os.Write(iAttributeValueRevealType4, 45);
            _os.Write(iOnDeadTakeType, 46);
            _os.Write(sOnDeadTakeStrParam, 47);
            _os.Write(iOnDeadSelectorType, 48);
            _os.Write(sOnDeadSelectorParams, 49);
            _os.Write(iInitBuffsCount, 50);
            _os.Write(iEffectParams, 51);
            _os.Write(iEffectParams2, 52);
            _os.Write(ishowBuffLayerInUI, 53);
            _os.Write(iIsSummonInherit, 54);
            _os.Write(iIsSameEquipCoverInitBuffsCount, 55);
            _os.Write(iBuffLayer1, 56);
            _os.Write(iBuffLayer2, 57);
            _os.Write(iBuffLayer3, 58);
            _os.Write(iSetID, 59);
            _os.Write(iChangeScale, 60);
            _os.Write(iChangeScaleIsTween, 61);
            _os.Write(iChangeScaleTime, 62);
            _os.Write(iSoulEquip, 63);
            _os.Write(iPlanID, 64);
            _os.Write(iDynamicAttrType, 65);
            _os.Write(iDynamicAttrValue, 66);
            _os.Write(iDynamicAttrValueType, 67);
            _os.Write(iColorID, 68);
            _os.Write(iSynthesisID3, 69);
            _os.Write(iSynthesisID4, 70);
            _os.Write(iSynthesisID5, 71);
            _os.Write(iSynthesisID6, 72);
            _os.Write(iType, 73);
            _os.Write(iMappingID, 74);
            _os.Write(iDynamicAttrValueShowRate, 75);
            _os.Write(iSortID, 76);
            _os.Write(iPosAttr, 77);
            _os.Write(iSynthesisEquip, 78);
            _os.Write(iTftEquipId, 79);
            _os.Write(iFilterId, 80);
            _os.Write(sAttribute, 81);
            _os.Write(sAttributeValue, 82);
            _os.Write(sAttributeValueType, 83);
            _os.Write(sAttributeValueRevealType, 84);
            _os.Write(sIconAbPath, 85);
            _os.Write(sTypeName, 86);
            _os.Write(iBuffIsCD1, 87);
            _os.Write(iBuffIsCD2, 88);
            _os.Write(iBuffIsCD3, 89);
            _os.Write(iOnDeadSelectorExtendParam, 90);
            _os.Write(sEffectAttribute, 91);
            _os.Write(sEffectAttributeParams, 92);
            _os.Write(sEffectBuffProtoId, 93);
            _os.Write(sEffectParams, 95);
            _os.Write(iIsNeedRefreshBuffAssist, 97);
            _os.Write(sLayerBuff, 98);
            _os.Write(iLiveCounter, 99);
            _os.Write(iEquipState, 100);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int)_is.Read(iID, 0, false);

            sName = (string)_is.Read(sName, 1, false);

            iLevel = (int)_is.Read(iLevel, 2, false);

            sDesc = (string)_is.Read(sDesc, 3, false);

            sIcon = (string)_is.Read(sIcon, 4, false);

            iSynthesisID1 = (int)_is.Read(iSynthesisID1, 6, false);

            iSynthesisID2 = (int)_is.Read(iSynthesisID2, 7, false);

            iEffectType = (int)_is.Read(iEffectType, 8, false);

            iWeights = (int)_is.Read(iWeights, 11, false);

            sEffectName = (string)_is.Read(sEffectName, 14, false);

            iEffectPos = (int)_is.Read(iEffectPos, 15, false);

            iEffectIsLoop = (int)_is.Read(iEffectIsLoop, 16, false);

            iEffectBattleIsShow = (int)_is.Read(iEffectBattleIsShow, 17, false);

            iPreviewEffectSelectTargetType = (int)_is.Read(iPreviewEffectSelectTargetType, 18, false);

            sPreviewEffectSelectTargetParam = (string)_is.Read(sPreviewEffectSelectTargetParam, 19, false);

            iPreviewEffectType = (int)_is.Read(iPreviewEffectType, 20, false);

            sPreviewEffectParam = (string)_is.Read(sPreviewEffectParam, 21, false);

            iPreviewEffectTime = (int)_is.Read(iPreviewEffectTime, 22, false);

            iUnique = (int)_is.Read(iUnique, 23, false);

            iAttribute1 = (int)_is.Read(iAttribute1, 24, false);

            iAttributeValue1 = (int)_is.Read(iAttributeValue1, 25, false);

            iAttributeValueType1 = (int)_is.Read(iAttributeValueType1, 26, false);

            iAttribute2 = (int)_is.Read(iAttribute2, 27, false);

            iAttributeValue2 = (int)_is.Read(iAttributeValue2, 28, false);

            iAttributeValueType2 = (int)_is.Read(iAttributeValueType2, 29, false);

            iAttribute3 = (int)_is.Read(iAttribute3, 30, false);

            iAttributeValue3 = (int)_is.Read(iAttributeValue3, 31, false);

            iAttributeValueType3 = (int)_is.Read(iAttributeValueType3, 32, false);

            iAttribute4 = (int)_is.Read(iAttribute4, 33, false);

            iAttributeValue4 = (int)_is.Read(iAttributeValue4, 34, false);

            iAttributeValueType4 = (int)_is.Read(iAttributeValueType4, 35, false);

            iBuff1 = (int)_is.Read(iBuff1, 36, false);

            iBuffType1 = (int)_is.Read(iBuffType1, 37, false);

            iBuff2 = (int)_is.Read(iBuff2, 38, false);

            iBuffType2 = (int)_is.Read(iBuffType2, 39, false);

            iBuff3 = (int)_is.Read(iBuff3, 40, false);

            iBuffType3 = (int)_is.Read(iBuffType3, 41, false);

            iAttributeValueRevealType1 = (int)_is.Read(iAttributeValueRevealType1, 42, false);

            iAttributeValueRevealType2 = (int)_is.Read(iAttributeValueRevealType2, 43, false);

            iAttributeValueRevealType3 = (int)_is.Read(iAttributeValueRevealType3, 44, false);

            iAttributeValueRevealType4 = (int)_is.Read(iAttributeValueRevealType4, 45, false);

            iOnDeadTakeType = (int)_is.Read(iOnDeadTakeType, 46, false);

            sOnDeadTakeStrParam = (string)_is.Read(sOnDeadTakeStrParam, 47, false);

            iOnDeadSelectorType = (int)_is.Read(iOnDeadSelectorType, 48, false);

            sOnDeadSelectorParams = (string)_is.Read(sOnDeadSelectorParams, 49, false);

            iInitBuffsCount = (int)_is.Read(iInitBuffsCount, 50, false);

            iEffectParams = (int)_is.Read(iEffectParams, 51, false);

            iEffectParams2 = (int)_is.Read(iEffectParams2, 52, false);

            ishowBuffLayerInUI = (int)_is.Read(ishowBuffLayerInUI, 53, false);

            iIsSummonInherit = (int)_is.Read(iIsSummonInherit, 54, false);

            iIsSameEquipCoverInitBuffsCount = (int)_is.Read(iIsSameEquipCoverInitBuffsCount, 55, false);

            iBuffLayer1 = (int)_is.Read(iBuffLayer1, 56, false);

            iBuffLayer2 = (int)_is.Read(iBuffLayer2, 57, false);

            iBuffLayer3 = (int)_is.Read(iBuffLayer3, 58, false);

            iSetID = (int)_is.Read(iSetID, 59, false);

            iChangeScale = (int)_is.Read(iChangeScale, 60, false);

            iChangeScaleIsTween = (int)_is.Read(iChangeScaleIsTween, 61, false);

            iChangeScaleTime = (int)_is.Read(iChangeScaleTime, 62, false);

            iSoulEquip = (int)_is.Read(iSoulEquip, 63, false);

            iPlanID = (int)_is.Read(iPlanID, 64, false);

            iDynamicAttrType = (int)_is.Read(iDynamicAttrType, 65, false);

            iDynamicAttrValue = (int)_is.Read(iDynamicAttrValue, 66, false);

            iDynamicAttrValueType = (int)_is.Read(iDynamicAttrValueType, 67, false);

            iColorID = (int)_is.Read(iColorID, 68, false);

            iSynthesisID3 = (int)_is.Read(iSynthesisID3, 69, false);

            iSynthesisID4 = (int)_is.Read(iSynthesisID4, 70, false);

            iSynthesisID5 = (int)_is.Read(iSynthesisID5, 71, false);

            iSynthesisID6 = (int)_is.Read(iSynthesisID6, 72, false);

            iType = (int)_is.Read(iType, 73, false);

            iMappingID = (int)_is.Read(iMappingID, 74, false);

            iDynamicAttrValueShowRate = (int)_is.Read(iDynamicAttrValueShowRate, 75, false);

            iSortID = (int)_is.Read(iSortID, 76, false);

            iPosAttr = (int)_is.Read(iPosAttr, 77, false);

            iSynthesisEquip = (int)_is.Read(iSynthesisEquip, 78, false);

            iTftEquipId = (int)_is.Read(iTftEquipId, 79, false);

            iFilterId = (int)_is.Read(iFilterId, 80, false);

            sAttribute = (string)_is.Read(sAttribute, 81, false);

            sAttributeValue = (string)_is.Read(sAttributeValue, 82, false);

            sAttributeValueType = (string)_is.Read(sAttributeValueType, 83, false);

            sAttributeValueRevealType = (string)_is.Read(sAttributeValueRevealType, 84, false);

            sIconAbPath = (string)_is.Read(sIconAbPath, 85, false);

            sTypeName = (string)_is.Read(sTypeName, 86, false);

            iBuffIsCD1 = (int)_is.Read(iBuffIsCD1, 87, false);

            iBuffIsCD2 = (int)_is.Read(iBuffIsCD2, 88, false);

            iBuffIsCD3 = (int)_is.Read(iBuffIsCD3, 89, false);

            iOnDeadSelectorExtendParam = (int)_is.Read(iOnDeadSelectorExtendParam, 90, false);

            sEffectAttribute = (string)_is.Read(sEffectAttribute, 91, false);

            sEffectAttributeParams = (string)_is.Read(sEffectAttributeParams, 92, false);

            sEffectBuffProtoId = (string)_is.Read(sEffectBuffProtoId, 93, false);

            sEffectParams = (string)_is.Read(sEffectParams, 95, false);

            iIsNeedRefreshBuffAssist = (int)_is.Read(iIsNeedRefreshBuffAssist, 97, false);

            sLayerBuff = (string)_is.Read(sLayerBuff, 98, false);

            iLiveCounter = (int)_is.Read(iLiveCounter, 99, false);

            iEquipState = (int)_is.Read(iEquipState, 100, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iSynthesisID1, "iSynthesisID1");
            _ds.Display(iSynthesisID2, "iSynthesisID2");
            _ds.Display(iEffectType, "iEffectType");
            _ds.Display(iWeights, "iWeights");
            _ds.Display(sEffectName, "sEffectName");
            _ds.Display(iEffectPos, "iEffectPos");
            _ds.Display(iEffectIsLoop, "iEffectIsLoop");
            _ds.Display(iEffectBattleIsShow, "iEffectBattleIsShow");
            _ds.Display(iPreviewEffectSelectTargetType, "iPreviewEffectSelectTargetType");
            _ds.Display(sPreviewEffectSelectTargetParam, "sPreviewEffectSelectTargetParam");
            _ds.Display(iPreviewEffectType, "iPreviewEffectType");
            _ds.Display(sPreviewEffectParam, "sPreviewEffectParam");
            _ds.Display(iPreviewEffectTime, "iPreviewEffectTime");
            _ds.Display(iUnique, "iUnique");
            _ds.Display(iAttribute1, "iAttribute1");
            _ds.Display(iAttributeValue1, "iAttributeValue1");
            _ds.Display(iAttributeValueType1, "iAttributeValueType1");
            _ds.Display(iAttribute2, "iAttribute2");
            _ds.Display(iAttributeValue2, "iAttributeValue2");
            _ds.Display(iAttributeValueType2, "iAttributeValueType2");
            _ds.Display(iAttribute3, "iAttribute3");
            _ds.Display(iAttributeValue3, "iAttributeValue3");
            _ds.Display(iAttributeValueType3, "iAttributeValueType3");
            _ds.Display(iAttribute4, "iAttribute4");
            _ds.Display(iAttributeValue4, "iAttributeValue4");
            _ds.Display(iAttributeValueType4, "iAttributeValueType4");
            _ds.Display(iBuff1, "iBuff1");
            _ds.Display(iBuffType1, "iBuffType1");
            _ds.Display(iBuff2, "iBuff2");
            _ds.Display(iBuffType2, "iBuffType2");
            _ds.Display(iBuff3, "iBuff3");
            _ds.Display(iBuffType3, "iBuffType3");
            _ds.Display(iAttributeValueRevealType1, "iAttributeValueRevealType1");
            _ds.Display(iAttributeValueRevealType2, "iAttributeValueRevealType2");
            _ds.Display(iAttributeValueRevealType3, "iAttributeValueRevealType3");
            _ds.Display(iAttributeValueRevealType4, "iAttributeValueRevealType4");
            _ds.Display(iOnDeadTakeType, "iOnDeadTakeType");
            _ds.Display(sOnDeadTakeStrParam, "sOnDeadTakeStrParam");
            _ds.Display(iOnDeadSelectorType, "iOnDeadSelectorType");
            _ds.Display(sOnDeadSelectorParams, "sOnDeadSelectorParams");
            _ds.Display(iInitBuffsCount, "iInitBuffsCount");
            _ds.Display(iEffectParams, "iEffectParams");
            _ds.Display(iEffectParams2, "iEffectParams2");
            _ds.Display(ishowBuffLayerInUI, "ishowBuffLayerInUI");
            _ds.Display(iIsSummonInherit, "iIsSummonInherit");
            _ds.Display(iIsSameEquipCoverInitBuffsCount, "iIsSameEquipCoverInitBuffsCount");
            _ds.Display(iBuffLayer1, "iBuffLayer1");
            _ds.Display(iBuffLayer2, "iBuffLayer2");
            _ds.Display(iBuffLayer3, "iBuffLayer3");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iChangeScale, "iChangeScale");
            _ds.Display(iChangeScaleIsTween, "iChangeScaleIsTween");
            _ds.Display(iChangeScaleTime, "iChangeScaleTime");
            _ds.Display(iSoulEquip, "iSoulEquip");
            _ds.Display(iPlanID, "iPlanID");
            _ds.Display(iDynamicAttrType, "iDynamicAttrType");
            _ds.Display(iDynamicAttrValue, "iDynamicAttrValue");
            _ds.Display(iDynamicAttrValueType, "iDynamicAttrValueType");
            _ds.Display(iColorID, "iColorID");
            _ds.Display(iSynthesisID3, "iSynthesisID3");
            _ds.Display(iSynthesisID4, "iSynthesisID4");
            _ds.Display(iSynthesisID5, "iSynthesisID5");
            _ds.Display(iSynthesisID6, "iSynthesisID6");
            _ds.Display(iType, "iType");
            _ds.Display(iMappingID, "iMappingID");
            _ds.Display(iDynamicAttrValueShowRate, "iDynamicAttrValueShowRate");
            _ds.Display(iSortID, "iSortID");
            _ds.Display(iPosAttr, "iPosAttr");
            _ds.Display(iSynthesisEquip, "iSynthesisEquip");
            _ds.Display(iTftEquipId, "iTftEquipId");
            _ds.Display(iFilterId, "iFilterId");
            _ds.Display(sAttribute, "sAttribute");
            _ds.Display(sAttributeValue, "sAttributeValue");
            _ds.Display(sAttributeValueType, "sAttributeValueType");
            _ds.Display(sAttributeValueRevealType, "sAttributeValueRevealType");
            _ds.Display(sIconAbPath, "sIconAbPath");
            _ds.Display(sTypeName, "sTypeName");
            _ds.Display(iBuffIsCD1, "iBuffIsCD1");
            _ds.Display(iBuffIsCD2, "iBuffIsCD2");
            _ds.Display(iBuffIsCD3, "iBuffIsCD3");
            _ds.Display(iOnDeadSelectorExtendParam, "iOnDeadSelectorExtendParam");
            _ds.Display(sEffectAttribute, "sEffectAttribute");
            _ds.Display(sEffectAttributeParams, "sEffectAttributeParams");
            _ds.Display(sEffectBuffProtoId, "sEffectBuffProtoId");
            _ds.Display(sEffectParams, "sEffectParams");
            _ds.Display(iIsNeedRefreshBuffAssist, "iIsNeedRefreshBuffAssist");
            _ds.Display(sLayerBuff, "sLayerBuff");
            _ds.Display(iLiveCounter, "iLiveCounter");
            _ds.Display(iEquipState, "iEquipState");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            iLevel = 0;
            sDesc = "";
            sIcon = "";
            iSynthesisID1 = 0;
            iSynthesisID2 = 0;
            iEffectType = 0;
            iWeights = 0;
            sEffectName = "";
            iEffectPos = 0;
            iEffectIsLoop = 0;
            iEffectBattleIsShow = 0;
            iPreviewEffectSelectTargetType = 0;
            sPreviewEffectSelectTargetParam = "";
            iPreviewEffectType = 0;
            sPreviewEffectParam = "";
            iPreviewEffectTime = 0;
            iUnique = 0;
            iAttribute1 = 0;
            iAttributeValue1 = 0;
            iAttributeValueType1 = 0;
            iAttribute2 = 0;
            iAttributeValue2 = 0;
            iAttributeValueType2 = 0;
            iAttribute3 = 0;
            iAttributeValue3 = 0;
            iAttributeValueType3 = 0;
            iAttribute4 = 0;
            iAttributeValue4 = 0;
            iAttributeValueType4 = 0;
            iBuff1 = 0;
            iBuffType1 = 0;
            iBuff2 = 0;
            iBuffType2 = 0;
            iBuff3 = 0;
            iBuffType3 = 0;
            iAttributeValueRevealType1 = 0;
            iAttributeValueRevealType2 = 0;
            iAttributeValueRevealType3 = 0;
            iAttributeValueRevealType4 = 0;
            iOnDeadTakeType = 0;
            sOnDeadTakeStrParam = "";
            iOnDeadSelectorType = 0;
            sOnDeadSelectorParams = "";
            iInitBuffsCount = 0;
            iEffectParams = 0;
            iEffectParams2 = 0;
            ishowBuffLayerInUI = 0;
            iIsSummonInherit = 0;
            iIsSameEquipCoverInitBuffsCount = 0;
            iBuffLayer1 = 0;
            iBuffLayer2 = 0;
            iBuffLayer3 = 0;
            iSetID = 0;
            iChangeScale = 0;
            iChangeScaleIsTween = 0;
            iChangeScaleTime = 0;
            iSoulEquip = 0;
            iPlanID = 0;
            iDynamicAttrType = 0;
            iDynamicAttrValue = 0;
            iDynamicAttrValueType = 0;
            iColorID = 0;
            iSynthesisID3 = 0;
            iSynthesisID4 = 0;
            iSynthesisID5 = 0;
            iSynthesisID6 = 0;
            iType = 0;
            iMappingID = 0;
            iDynamicAttrValueShowRate = 0;
            iSortID = 0;
            iPosAttr = 0;
            iSynthesisEquip = 0;
            iTftEquipId = 0;
            iFilterId = 0;
            sAttribute = "";
            sAttributeValue = "";
            sAttributeValueType = "";
            sAttributeValueRevealType = "";
            sIconAbPath = "";
            sTypeName = "";
            iBuffIsCD1 = 0;
            iBuffIsCD2 = 0;
            iBuffIsCD3 = 0;
            iOnDeadSelectorExtendParam = 0;
            sEffectAttribute = "";
            sEffectAttributeParams = "";
            sEffectBuffProtoId = "";
            sEffectParams = "";
            iIsNeedRefreshBuffAssist = 0;
            sLayerBuff = "";
            iLiveCounter = 0;
            iEquipState = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Equipment_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.iLevel = this.iLevel;
            copied.sDesc = this.sDesc;
            copied.sIcon = this.sIcon;
            copied.iSynthesisID1 = this.iSynthesisID1;
            copied.iSynthesisID2 = this.iSynthesisID2;
            copied.iEffectType = this.iEffectType;
            copied.iWeights = this.iWeights;
            copied.sEffectName = this.sEffectName;
            copied.iEffectPos = this.iEffectPos;
            copied.iEffectIsLoop = this.iEffectIsLoop;
            copied.iEffectBattleIsShow = this.iEffectBattleIsShow;
            copied.iPreviewEffectSelectTargetType = this.iPreviewEffectSelectTargetType;
            copied.sPreviewEffectSelectTargetParam = this.sPreviewEffectSelectTargetParam;
            copied.iPreviewEffectType = this.iPreviewEffectType;
            copied.sPreviewEffectParam = this.sPreviewEffectParam;
            copied.iPreviewEffectTime = this.iPreviewEffectTime;
            copied.iUnique = this.iUnique;
            copied.iAttribute1 = this.iAttribute1;
            copied.iAttributeValue1 = this.iAttributeValue1;
            copied.iAttributeValueType1 = this.iAttributeValueType1;
            copied.iAttribute2 = this.iAttribute2;
            copied.iAttributeValue2 = this.iAttributeValue2;
            copied.iAttributeValueType2 = this.iAttributeValueType2;
            copied.iAttribute3 = this.iAttribute3;
            copied.iAttributeValue3 = this.iAttributeValue3;
            copied.iAttributeValueType3 = this.iAttributeValueType3;
            copied.iAttribute4 = this.iAttribute4;
            copied.iAttributeValue4 = this.iAttributeValue4;
            copied.iAttributeValueType4 = this.iAttributeValueType4;
            copied.iBuff1 = this.iBuff1;
            copied.iBuffType1 = this.iBuffType1;
            copied.iBuff2 = this.iBuff2;
            copied.iBuffType2 = this.iBuffType2;
            copied.iBuff3 = this.iBuff3;
            copied.iBuffType3 = this.iBuffType3;
            copied.iAttributeValueRevealType1 = this.iAttributeValueRevealType1;
            copied.iAttributeValueRevealType2 = this.iAttributeValueRevealType2;
            copied.iAttributeValueRevealType3 = this.iAttributeValueRevealType3;
            copied.iAttributeValueRevealType4 = this.iAttributeValueRevealType4;
            copied.iOnDeadTakeType = this.iOnDeadTakeType;
            copied.sOnDeadTakeStrParam = this.sOnDeadTakeStrParam;
            copied.iOnDeadSelectorType = this.iOnDeadSelectorType;
            copied.sOnDeadSelectorParams = this.sOnDeadSelectorParams;
            copied.iInitBuffsCount = this.iInitBuffsCount;
            copied.iEffectParams = this.iEffectParams;
            copied.iEffectParams2 = this.iEffectParams2;
            copied.ishowBuffLayerInUI = this.ishowBuffLayerInUI;
            copied.iIsSummonInherit = this.iIsSummonInherit;
            copied.iIsSameEquipCoverInitBuffsCount = this.iIsSameEquipCoverInitBuffsCount;
            copied.iBuffLayer1 = this.iBuffLayer1;
            copied.iBuffLayer2 = this.iBuffLayer2;
            copied.iBuffLayer3 = this.iBuffLayer3;
            copied.iSetID = this.iSetID;
            copied.iChangeScale = this.iChangeScale;
            copied.iChangeScaleIsTween = this.iChangeScaleIsTween;
            copied.iChangeScaleTime = this.iChangeScaleTime;
            copied.iSoulEquip = this.iSoulEquip;
            copied.iPlanID = this.iPlanID;
            copied.iDynamicAttrType = this.iDynamicAttrType;
            copied.iDynamicAttrValue = this.iDynamicAttrValue;
            copied.iDynamicAttrValueType = this.iDynamicAttrValueType;
            copied.iColorID = this.iColorID;
            copied.iSynthesisID3 = this.iSynthesisID3;
            copied.iSynthesisID4 = this.iSynthesisID4;
            copied.iSynthesisID5 = this.iSynthesisID5;
            copied.iSynthesisID6 = this.iSynthesisID6;
            copied.iType = this.iType;
            copied.iMappingID = this.iMappingID;
            copied.iDynamicAttrValueShowRate = this.iDynamicAttrValueShowRate;
            copied.iSortID = this.iSortID;
            copied.iPosAttr = this.iPosAttr;
            copied.iSynthesisEquip = this.iSynthesisEquip;
            copied.iTftEquipId = this.iTftEquipId;
            copied.iFilterId = this.iFilterId;
            copied.sAttribute = this.sAttribute;
            copied.sAttributeValue = this.sAttributeValue;
            copied.sAttributeValueType = this.sAttributeValueType;
            copied.sAttributeValueRevealType = this.sAttributeValueRevealType;
            copied.sIconAbPath = this.sIconAbPath;
            copied.sTypeName = this.sTypeName;
            copied.iBuffIsCD1 = this.iBuffIsCD1;
            copied.iBuffIsCD2 = this.iBuffIsCD2;
            copied.iBuffIsCD3 = this.iBuffIsCD3;
            copied.iOnDeadSelectorExtendParam = this.iOnDeadSelectorExtendParam;
            copied.sEffectAttribute = this.sEffectAttribute;
            copied.sEffectAttributeParams = this.sEffectAttributeParams;
            copied.sEffectBuffProtoId = this.sEffectBuffProtoId;
            copied.sEffectParams = this.sEffectParams;
            copied.iIsNeedRefreshBuffAssist = this.iIsNeedRefreshBuffAssist;
            copied.sLayerBuff = this.sLayerBuff;
            copied.iLiveCounter = this.iLiveCounter;
            copied.iEquipState = this.iEquipState;
            return copied;
        }
    }
}

