// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TQueryDirectBuyReq : Wup.Jce.JceStruct
    {
        public string sBillNo = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(sBillNo, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            sBillNo = (string) _is.Read(sBillNo, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(sBillNo, "sBillNo");
        }

        public override void Clear()
        {
            sBillNo = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TQueryDirectBuyReq();
            copied.sBillNo = this.sBillNo;
            return copied;
        }
    }
}

