// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetButlerActivityListRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<ButlerActivity> activities;

        public TKFrame.TKDictionary<long, TButlerActivityData> activityData;

        public TKFrame.TKDictionary<int, int> items;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(activities, 1);
            _os.Write(activityData, 2);
            _os.Write(items, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            activities = (System.Collections.Generic.List<ButlerActivity>) _is.Read(activities, 1, false);

            activityData = (TKFrame.TKDictionary<long, TButlerActivityData>) _is.Read(activityData, 2, false);

            items = (TKFrame.TKDictionary<int, int>) _is.Read(items, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(activities, "activities");
            _ds.Display(activityData, "activityData");
            _ds.Display(items, "items");
        }

        public override void Clear()
        {
            iRet = 0;
            activities = null;
            activityData = null;
            items = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetButlerActivityListRsp();
            copied.iRet = this.iRet;
            copied.activities = (System.Collections.Generic.List<ButlerActivity>)JceUtil.DeepClone(this.activities);
            copied.activityData = (TKFrame.TKDictionary<long, TButlerActivityData>)JceUtil.DeepClone(this.activityData);
            copied.items = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.items);
            return copied;
        }
    }
}

