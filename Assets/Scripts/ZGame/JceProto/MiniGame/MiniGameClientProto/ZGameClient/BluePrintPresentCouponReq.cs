// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BluePrintPresentCouponReq : Wup.Jce.JceStruct
    {
        int _lotteryID = 0;
        public int lotteryID
        {
            get
            {
                 return _lotteryID;
            }
            set
            {
                _lotteryID = value; 
            }
        }

        public TUserID target {get; set;} 

        int _presentCnt = 0;
        public int presentCnt
        {
            get
            {
                 return _presentCnt;
            }
            set
            {
                _presentCnt = value; 
            }
        }

        string _targetNick = "";
        public string targetNick
        {
            get
            {
                 return _targetNick;
            }
            set
            {
                _targetNick = value; 
            }
        }

        string _sMsdkToken = "";
        public string sMsdkToken
        {
            get
            {
                 return _sMsdkToken;
            }
            set
            {
                _sMsdkToken = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lotteryID, 0);
            _os.Write(target, 1);
            _os.Write(presentCnt, 2);
            _os.Write(targetNick, 3);
            _os.Write(sMsdkToken, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lotteryID = (int) _is.Read(lotteryID, 0, false);

            target = (TUserID) _is.Read(target, 1, false);

            presentCnt = (int) _is.Read(presentCnt, 2, false);

            targetNick = (string) _is.Read(targetNick, 3, false);

            sMsdkToken = (string) _is.Read(sMsdkToken, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lotteryID, "lotteryID");
            _ds.Display(target, "target");
            _ds.Display(presentCnt, "presentCnt");
            _ds.Display(targetNick, "targetNick");
            _ds.Display(sMsdkToken, "sMsdkToken");
        }

        public override void Clear()
        {
            lotteryID = 0;
            target = null;
            presentCnt = 0;
            targetNick = "";
            sMsdkToken = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BluePrintPresentCouponReq();
            copied.lotteryID = this.lotteryID;
            copied.target = (TUserID)JceUtil.DeepClone(this.target);
            copied.presentCnt = this.presentCnt;
            copied.targetNick = this.targetNick;
            copied.sMsdkToken = this.sMsdkToken;
            return copied;
        }
    }
}

