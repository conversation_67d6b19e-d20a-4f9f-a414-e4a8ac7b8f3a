//所在的Excel 【ACG_HeroVoice.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HeroVioce_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sEnterSound = "";

        public string sWinSound = "";

        public string sItemSound = "";

        public string sFightSound = "";

        public string sTeammateSoundID1 = "";

        public string sTeammateSound1 = "";

        public string sEnemySoundID1 = "";

        public string sEnemySound1 = "";

        public string sTeammateSoundID2 = "";

        public string sTeammateSound2 = "";

        public string sEnemySoundID2 = "";

        public string sEnemySound2 = "";

        public string sTeammateSoundID3 = "";

        public string sTeammateSound3 = "";

        public string sEnemySoundID3 = "";

        public string sEnemySound3 = "";

        public int iSet = 0;

        public string sSellSound = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sEnterSound, 1);
            _os.Write(sWinSound, 2);
            _os.Write(sItemSound, 3);
            _os.Write(sFightSound, 4);
            _os.Write(sTeammateSoundID1, 5);
            _os.Write(sTeammateSound1, 6);
            _os.Write(sEnemySoundID1, 7);
            _os.Write(sEnemySound1, 8);
            _os.Write(sTeammateSoundID2, 9);
            _os.Write(sTeammateSound2, 10);
            _os.Write(sEnemySoundID2, 11);
            _os.Write(sEnemySound2, 12);
            _os.Write(sTeammateSoundID3, 13);
            _os.Write(sTeammateSound3, 14);
            _os.Write(sEnemySoundID3, 15);
            _os.Write(sEnemySound3, 16);
            _os.Write(iSet, 17);
            _os.Write(sSellSound, 18);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sEnterSound = (string) _is.Read(sEnterSound, 1, false);

            sWinSound = (string) _is.Read(sWinSound, 2, false);

            sItemSound = (string) _is.Read(sItemSound, 3, false);

            sFightSound = (string) _is.Read(sFightSound, 4, false);

            sTeammateSoundID1 = (string) _is.Read(sTeammateSoundID1, 5, false);

            sTeammateSound1 = (string) _is.Read(sTeammateSound1, 6, false);

            sEnemySoundID1 = (string) _is.Read(sEnemySoundID1, 7, false);

            sEnemySound1 = (string) _is.Read(sEnemySound1, 8, false);

            sTeammateSoundID2 = (string) _is.Read(sTeammateSoundID2, 9, false);

            sTeammateSound2 = (string) _is.Read(sTeammateSound2, 10, false);

            sEnemySoundID2 = (string) _is.Read(sEnemySoundID2, 11, false);

            sEnemySound2 = (string) _is.Read(sEnemySound2, 12, false);

            sTeammateSoundID3 = (string) _is.Read(sTeammateSoundID3, 13, false);

            sTeammateSound3 = (string) _is.Read(sTeammateSound3, 14, false);

            sEnemySoundID3 = (string) _is.Read(sEnemySoundID3, 15, false);

            sEnemySound3 = (string) _is.Read(sEnemySound3, 16, false);

            iSet = (int) _is.Read(iSet, 17, false);

            sSellSound = (string) _is.Read(sSellSound, 18, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sEnterSound, "sEnterSound");
            _ds.Display(sWinSound, "sWinSound");
            _ds.Display(sItemSound, "sItemSound");
            _ds.Display(sFightSound, "sFightSound");
            _ds.Display(sTeammateSoundID1, "sTeammateSoundID1");
            _ds.Display(sTeammateSound1, "sTeammateSound1");
            _ds.Display(sEnemySoundID1, "sEnemySoundID1");
            _ds.Display(sEnemySound1, "sEnemySound1");
            _ds.Display(sTeammateSoundID2, "sTeammateSoundID2");
            _ds.Display(sTeammateSound2, "sTeammateSound2");
            _ds.Display(sEnemySoundID2, "sEnemySoundID2");
            _ds.Display(sEnemySound2, "sEnemySound2");
            _ds.Display(sTeammateSoundID3, "sTeammateSoundID3");
            _ds.Display(sTeammateSound3, "sTeammateSound3");
            _ds.Display(sEnemySoundID3, "sEnemySoundID3");
            _ds.Display(sEnemySound3, "sEnemySound3");
            _ds.Display(iSet, "iSet");
            _ds.Display(sSellSound, "sSellSound");
        }

        public override void Clear()
        {
            iID = 0;
            sEnterSound = "";
            sWinSound = "";
            sItemSound = "";
            sFightSound = "";
            sTeammateSoundID1 = "";
            sTeammateSound1 = "";
            sEnemySoundID1 = "";
            sEnemySound1 = "";
            sTeammateSoundID2 = "";
            sTeammateSound2 = "";
            sEnemySoundID2 = "";
            sEnemySound2 = "";
            sTeammateSoundID3 = "";
            sTeammateSound3 = "";
            sEnemySoundID3 = "";
            sEnemySound3 = "";
            iSet = 0;
            sSellSound = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HeroVioce_Client();
            copied.iID = this.iID;
            copied.sEnterSound = this.sEnterSound;
            copied.sWinSound = this.sWinSound;
            copied.sItemSound = this.sItemSound;
            copied.sFightSound = this.sFightSound;
            copied.sTeammateSoundID1 = this.sTeammateSoundID1;
            copied.sTeammateSound1 = this.sTeammateSound1;
            copied.sEnemySoundID1 = this.sEnemySoundID1;
            copied.sEnemySound1 = this.sEnemySound1;
            copied.sTeammateSoundID2 = this.sTeammateSoundID2;
            copied.sTeammateSound2 = this.sTeammateSound2;
            copied.sEnemySoundID2 = this.sEnemySoundID2;
            copied.sEnemySound2 = this.sEnemySound2;
            copied.sTeammateSoundID3 = this.sTeammateSoundID3;
            copied.sTeammateSound3 = this.sTeammateSound3;
            copied.sEnemySoundID3 = this.sEnemySoundID3;
            copied.sEnemySound3 = this.sEnemySound3;
            copied.iSet = this.iSet;
            copied.sSellSound = this.sSellSound;
            return copied;
        }
    }
}

