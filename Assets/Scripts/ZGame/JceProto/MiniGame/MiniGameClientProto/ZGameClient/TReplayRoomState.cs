// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TReplayRoomState : Wup.Jce.JceStruct
    {
        public bool bIsInRoom = false;

        public int iRoomID = 0;

        public int iScene = 0;

        public string roomObj = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(bIsInRoom, 0);
            _os.Write(iRoomID, 2);
            _os.Write(iScene, 3);
            _os.Write(roomObj, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            bIsInRoom = (bool) _is.Read(bIsInRoom, 0, false);

            iRoomID = (int) _is.Read(iRoomID, 2, false);

            iScene = (int) _is.Read(iScene, 3, false);

            roomObj = (string) _is.Read(roomObj, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(bIsInRoom, "bIsInRoom");
            _ds.Display(iRoomID, "iRoomID");
            _ds.Display(iScene, "iScene");
            _ds.Display(roomObj, "roomObj");
        }

        public override void Clear()
        {
            bIsInRoom = false;
            iRoomID = 0;
            iScene = 0;
            roomObj = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TReplayRoomState();
            copied.bIsInRoom = this.bIsInRoom;
            copied.iRoomID = this.iRoomID;
            copied.iScene = this.iScene;
            copied.roomObj = this.roomObj;
            return copied;
        }
    }
}

