// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TinyMapChange : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<int> vecAddId;

        public System.Collections.Generic.List<TAC_TinyMapSingleData> vecChangeData;

        public System.Collections.Generic.List<int> vecDelId;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(vecAddId, 0);
            _os.Write(vecChangeData, 1);
            _os.Write(vecDelId, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            vecAddId = (System.Collections.Generic.List<int>) _is.Read(vecAddId, 0, false);

            vecChangeData = (System.Collections.Generic.List<TAC_TinyMapSingleData>) _is.Read(vecChangeData, 1, false);

            vecDelId = (System.Collections.Generic.List<int>) _is.Read(vecDelId, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(vecAddId, "vecAddId");
            _ds.Display(vecChangeData, "vecChangeData");
            _ds.Display(vecDelId, "vecDelId");
        }

        public override void Clear()
        {
            vecAddId = null;
            vecChangeData = null;
            vecDelId = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TinyMapChange();
            copied.vecAddId = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecAddId);
            copied.vecChangeData = (System.Collections.Generic.List<TAC_TinyMapSingleData>)JceUtil.DeepClone(this.vecChangeData);
            copied.vecDelId = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecDelId);
            return copied;
        }
    }
}

