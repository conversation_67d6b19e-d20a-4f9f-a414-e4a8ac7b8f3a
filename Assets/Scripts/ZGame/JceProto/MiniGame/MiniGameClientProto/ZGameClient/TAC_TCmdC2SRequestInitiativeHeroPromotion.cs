// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdC2SRequestInitiativeHeroPromotion : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iHeroConfID = 0;
        public int iHeroConfID
        {
            get
            {
                 return _iHeroConfID;
            }
            set
            {
                _iHeroConfID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iHeroConfID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iHeroConfID = (int) _is.Read(iHeroConfID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iHeroConfID, "iHeroConfID");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iHeroConfID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdC2SRequestInitiativeHeroPromotion();
            copied.i8ChairID = this.i8ChairID;
            copied.iHeroConfID = this.iHeroConfID;
            return copied;
        }
    }
}

