// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyDownHero : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public TAC_BattleGroundHero stBattleGroundHero {get; set;} 

        public TAC_WaitPos stWaitPos {get; set;} 

        int _iCurPeople = 0;
        public int iCurPeople
        {
            get
            {
                 return _iCurPeople;
            }
            set
            {
                _iCurPeople = value; 
            }
        }

        int _iWaitAreaType = 0;
        public int iWaitAreaType
        {
            get
            {
                 return _iWaitAreaType;
            }
            set
            {
                _iWaitAreaType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(stBattleGroundHero, 2);
            _os.Write(stWaitPos, 3);
            _os.Write(iCurPeople, 4);
            _os.Write(iWaitAreaType, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            stBattleGroundHero = (TAC_BattleGroundHero) _is.Read(stBattleGroundHero, 2, false);

            stWaitPos = (TAC_WaitPos) _is.Read(stWaitPos, 3, false);

            iCurPeople = (int) _is.Read(iCurPeople, 4, false);

            iWaitAreaType = (int) _is.Read(iWaitAreaType, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(stBattleGroundHero, "stBattleGroundHero");
            _ds.Display(stWaitPos, "stWaitPos");
            _ds.Display(iCurPeople, "iCurPeople");
            _ds.Display(iWaitAreaType, "iWaitAreaType");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            stBattleGroundHero = null;
            stWaitPos = null;
            iCurPeople = 0;
            iWaitAreaType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyDownHero();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.stBattleGroundHero = (TAC_BattleGroundHero)JceUtil.DeepClone(this.stBattleGroundHero);
            copied.stWaitPos = (TAC_WaitPos)JceUtil.DeepClone(this.stWaitPos);
            copied.iCurPeople = this.iCurPeople;
            copied.iWaitAreaType = this.iWaitAreaType;
            return copied;
        }
    }
}

