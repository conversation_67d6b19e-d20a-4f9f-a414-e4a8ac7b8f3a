// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ContestClientSingle : Wup.Jce.JceStruct
    {
        public int id = 0;

        public int weight = 0;

        public string name = "";

        public int mallID = 0;

        public int BPID = 0;

        public int rewardID = 0;

        public int tokenID = 0;

        public ContestClientOpenTime openTime;

        public TKFrame.TKDictionary<int, ContestClientRoom> roomCfg;

        public TKFrame.TKDictionary<int, SubContestClient> subContestCfg;

        public int LP = 0;

        public int maxLP = 0;

        public string desc = "";

        public string scoreName = "";

        public int score = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(id, 0);
            _os.Write(weight, 1);
            _os.Write(name, 2);
            _os.Write(mallID, 3);
            _os.Write(BPID, 4);
            _os.Write(rewardID, 5);
            _os.Write(tokenID, 6);
            _os.Write(openTime, 7);
            _os.Write(roomCfg, 9);
            _os.Write(subContestCfg, 10);
            _os.Write(LP, 11);
            _os.Write(maxLP, 12);
            _os.Write(desc, 13);
            _os.Write(scoreName, 14);
            _os.Write(score, 15);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            id = (int) _is.Read(id, 0, false);

            weight = (int) _is.Read(weight, 1, false);

            name = (string) _is.Read(name, 2, false);

            mallID = (int) _is.Read(mallID, 3, false);

            BPID = (int) _is.Read(BPID, 4, false);

            rewardID = (int) _is.Read(rewardID, 5, false);

            tokenID = (int) _is.Read(tokenID, 6, false);

            openTime = (ContestClientOpenTime) _is.Read(openTime, 7, false);

            roomCfg = (TKFrame.TKDictionary<int, ContestClientRoom>) _is.Read(roomCfg, 9, false);

            subContestCfg = (TKFrame.TKDictionary<int, SubContestClient>) _is.Read(subContestCfg, 10, false);

            LP = (int) _is.Read(LP, 11, false);

            maxLP = (int) _is.Read(maxLP, 12, false);

            desc = (string) _is.Read(desc, 13, false);

            scoreName = (string) _is.Read(scoreName, 14, false);

            score = (int) _is.Read(score, 15, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(id, "id");
            _ds.Display(weight, "weight");
            _ds.Display(name, "name");
            _ds.Display(mallID, "mallID");
            _ds.Display(BPID, "BPID");
            _ds.Display(rewardID, "rewardID");
            _ds.Display(tokenID, "tokenID");
            _ds.Display(openTime, "openTime");
            _ds.Display(roomCfg, "roomCfg");
            _ds.Display(subContestCfg, "subContestCfg");
            _ds.Display(LP, "LP");
            _ds.Display(maxLP, "maxLP");
            _ds.Display(desc, "desc");
            _ds.Display(scoreName, "scoreName");
            _ds.Display(score, "score");
        }

        public override void Clear()
        {
            id = 0;
            weight = 0;
            name = "";
            mallID = 0;
            BPID = 0;
            rewardID = 0;
            tokenID = 0;
            openTime = null;
            roomCfg = null;
            subContestCfg = null;
            LP = 0;
            maxLP = 0;
            desc = "";
            scoreName = "";
            score = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ContestClientSingle();
            copied.id = this.id;
            copied.weight = this.weight;
            copied.name = this.name;
            copied.mallID = this.mallID;
            copied.BPID = this.BPID;
            copied.rewardID = this.rewardID;
            copied.tokenID = this.tokenID;
            copied.openTime = (ContestClientOpenTime)JceUtil.DeepClone(this.openTime);
            copied.roomCfg = (TKFrame.TKDictionary<int, ContestClientRoom>)JceUtil.DeepClone(this.roomCfg);
            copied.subContestCfg = (TKFrame.TKDictionary<int, SubContestClient>)JceUtil.DeepClone(this.subContestCfg);
            copied.LP = this.LP;
            copied.maxLP = this.maxLP;
            copied.desc = this.desc;
            copied.scoreName = this.scoreName;
            copied.score = this.score;
            return copied;
        }
    }
}

