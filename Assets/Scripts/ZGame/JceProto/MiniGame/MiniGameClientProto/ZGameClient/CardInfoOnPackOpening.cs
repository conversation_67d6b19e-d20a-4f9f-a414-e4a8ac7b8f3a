// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardInfoOnPackOpening : Wup.Jce.JceStruct
    {
        int _cardID = 0;
        public int cardID
        {
            get
            {
                 return _cardID;
            }
            set
            {
                _cardID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(cardID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            cardID = (int) _is.Read(cardID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(cardID, "cardID");
        }

        public override void Clear()
        {
            cardID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardInfoOnPackOpening();
            copied.cardID = this.cardID;
            return copied;
        }
    }
}

