// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetWinFetterTeamRecordRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public int iSeasonID = 0;

        public int iCurSeasonID = 0;

        public int iMaxSeasonViewNum = 0;

        public TKFrame.TKDictionary<int, TWinFetterTeamRecord> mapWinFetterTeamRecord;

        public TUserID stInquireUserID;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iSeasonID, 1);
            _os.Write(iCurSeasonID, 2);
            _os.Write(iMaxSeasonViewNum, 3);
            _os.Write(mapWinFetterTeamRecord, 4);
            _os.Write(stInquireUserID, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iSeasonID = (int) _is.Read(iSeasonID, 1, false);

            iCurSeasonID = (int) _is.Read(iCurSeasonID, 2, false);

            iMaxSeasonViewNum = (int) _is.Read(iMaxSeasonViewNum, 3, false);

            mapWinFetterTeamRecord = (TKFrame.TKDictionary<int, TWinFetterTeamRecord>) _is.Read(mapWinFetterTeamRecord, 4, false);

            stInquireUserID = (TUserID) _is.Read(stInquireUserID, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iSeasonID, "iSeasonID");
            _ds.Display(iCurSeasonID, "iCurSeasonID");
            _ds.Display(iMaxSeasonViewNum, "iMaxSeasonViewNum");
            _ds.Display(mapWinFetterTeamRecord, "mapWinFetterTeamRecord");
            _ds.Display(stInquireUserID, "stInquireUserID");
        }

        public override void Clear()
        {
            iRet = 0;
            iSeasonID = 0;
            iCurSeasonID = 0;
            iMaxSeasonViewNum = 0;
            mapWinFetterTeamRecord = null;
            stInquireUserID = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetWinFetterTeamRecordRsp();
            copied.iRet = this.iRet;
            copied.iSeasonID = this.iSeasonID;
            copied.iCurSeasonID = this.iCurSeasonID;
            copied.iMaxSeasonViewNum = this.iMaxSeasonViewNum;
            copied.mapWinFetterTeamRecord = (TKFrame.TKDictionary<int, TWinFetterTeamRecord>)JceUtil.DeepClone(this.mapWinFetterTeamRecord);
            copied.stInquireUserID = (TUserID)JceUtil.DeepClone(this.stInquireUserID);
            return copied;
        }
    }
}

