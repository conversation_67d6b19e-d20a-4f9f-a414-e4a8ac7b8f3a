// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TSchedule_Server : Wup.Jce.JceStruct
    {
        public int iScheduleID = 0;

        public string sJumpParams1 = "";

        public string sJumpParams2 = "";

        public int iShowOrder = 0;

        public string sTitle = "";

        public string sBrief = "";

        public int iType = 0;

        public int iActivityID = 0;

        public int iItemID = 0;

        public int iItemCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iScheduleID, 0);
            _os.Write(sJumpParams1, 1);
            _os.Write(sJumpParams2, 2);
            _os.Write(iShowOrder, 3);
            _os.Write(sTitle, 4);
            _os.Write(sBrief, 5);
            _os.Write(iType, 6);
            _os.Write(iActivityID, 7);
            _os.Write(iItemID, 8);
            _os.Write(iItemCount, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iScheduleID = (int) _is.Read(iScheduleID, 0, false);

            sJumpParams1 = (string) _is.Read(sJumpParams1, 1, false);

            sJumpParams2 = (string) _is.Read(sJumpParams2, 2, false);

            iShowOrder = (int) _is.Read(iShowOrder, 3, false);

            sTitle = (string) _is.Read(sTitle, 4, false);

            sBrief = (string) _is.Read(sBrief, 5, false);

            iType = (int) _is.Read(iType, 6, false);

            iActivityID = (int) _is.Read(iActivityID, 7, false);

            iItemID = (int) _is.Read(iItemID, 8, false);

            iItemCount = (int) _is.Read(iItemCount, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iScheduleID, "iScheduleID");
            _ds.Display(sJumpParams1, "sJumpParams1");
            _ds.Display(sJumpParams2, "sJumpParams2");
            _ds.Display(iShowOrder, "iShowOrder");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(sBrief, "sBrief");
            _ds.Display(iType, "iType");
            _ds.Display(iActivityID, "iActivityID");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iItemCount, "iItemCount");
        }

        public override void Clear()
        {
            iScheduleID = 0;
            sJumpParams1 = "";
            sJumpParams2 = "";
            iShowOrder = 0;
            sTitle = "";
            sBrief = "";
            iType = 0;
            iActivityID = 0;
            iItemID = 0;
            iItemCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TSchedule_Server();
            copied.iScheduleID = this.iScheduleID;
            copied.sJumpParams1 = this.sJumpParams1;
            copied.sJumpParams2 = this.sJumpParams2;
            copied.iShowOrder = this.iShowOrder;
            copied.sTitle = this.sTitle;
            copied.sBrief = this.sBrief;
            copied.iType = this.iType;
            copied.iActivityID = this.iActivityID;
            copied.iItemID = this.iItemID;
            copied.iItemCount = this.iItemCount;
            return copied;
        }
    }
}

