// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyDropHero : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public System.Collections.Generic.List<TAC_WaitHero> vecWaitHero {get; set;} 

        public ACG_MonsterDropItem stMonsterDropItem {get; set;} 

        public System.Collections.Generic.List<TAC_WaitHero> vecSortedWaitHero {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(vecWaitHero, 2);
            _os.Write(stMonsterDropItem, 3);
            _os.Write(vecSortedWaitHero, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecWaitHero, 2, false);

            stMonsterDropItem = (ACG_MonsterDropItem) _is.Read(stMonsterDropItem, 3, false);

            vecSortedWaitHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecSortedWaitHero, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(vecWaitHero, "vecWaitHero");
            _ds.Display(stMonsterDropItem, "stMonsterDropItem");
            _ds.Display(vecSortedWaitHero, "vecSortedWaitHero");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            vecWaitHero = null;
            stMonsterDropItem = null;
            vecSortedWaitHero = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyDropHero();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecWaitHero);
            copied.stMonsterDropItem = (ACG_MonsterDropItem)JceUtil.DeepClone(this.stMonsterDropItem);
            copied.vecSortedWaitHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecSortedWaitHero);
            return copied;
        }
    }
}

