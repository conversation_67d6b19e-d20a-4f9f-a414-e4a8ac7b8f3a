// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCommonBPTaskRefreshConfig_Server : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iBPID = 0;

        public int iTaskType = 0;

        public int iRefreshLimitType = 0;

        public int iRefreshLimitMax = 0;

        public int iCostItemID = 0;

        public int iCostItemIDCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iBPID, 1);
            _os.Write(iTaskType, 2);
            _os.Write(iRefreshLimitType, 3);
            _os.Write(iRefreshLimitMax, 4);
            _os.Write(iCostItemID, 5);
            _os.Write(iCostItemIDCount, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iBPID = (int) _is.Read(iBPID, 1, false);

            iTaskType = (int) _is.Read(iTaskType, 2, false);

            iRefreshLimitType = (int) _is.Read(iRefreshLimitType, 3, false);

            iRefreshLimitMax = (int) _is.Read(iRefreshLimitMax, 4, false);

            iCostItemID = (int) _is.Read(iCostItemID, 5, false);

            iCostItemIDCount = (int) _is.Read(iCostItemIDCount, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iBPID, "iBPID");
            _ds.Display(iTaskType, "iTaskType");
            _ds.Display(iRefreshLimitType, "iRefreshLimitType");
            _ds.Display(iRefreshLimitMax, "iRefreshLimitMax");
            _ds.Display(iCostItemID, "iCostItemID");
            _ds.Display(iCostItemIDCount, "iCostItemIDCount");
        }

        public override void Clear()
        {
            iIndex = 0;
            iBPID = 0;
            iTaskType = 0;
            iRefreshLimitType = 0;
            iRefreshLimitMax = 0;
            iCostItemID = 0;
            iCostItemIDCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCommonBPTaskRefreshConfig_Server();
            copied.iIndex = this.iIndex;
            copied.iBPID = this.iBPID;
            copied.iTaskType = this.iTaskType;
            copied.iRefreshLimitType = this.iRefreshLimitType;
            copied.iRefreshLimitMax = this.iRefreshLimitMax;
            copied.iCostItemID = this.iCostItemID;
            copied.iCostItemIDCount = this.iCostItemIDCount;
            return copied;
        }
    }
}

