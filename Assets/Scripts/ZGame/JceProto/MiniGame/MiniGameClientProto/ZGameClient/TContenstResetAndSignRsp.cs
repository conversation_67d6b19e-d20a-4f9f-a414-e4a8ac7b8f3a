// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TContenstResetAndSignRsp : Wup.Jce.JceStruct
    {
        int _iResult = 0;
        public int iResult
        {
            get
            {
                 return _iResult;
            }
            set
            {
                _iResult = value; 
            }
        }

        int _iItemId = 0;
        public int iItemId
        {
            get
            {
                 return _iItemId;
            }
            set
            {
                _iItemId = value; 
            }
        }

        int _iItemCount = 0;
        public int iItemCount
        {
            get
            {
                 return _iItemCount;
            }
            set
            {
                _iItemCount = value; 
            }
        }

        int _iSignNum = 0;
        public int iSignNum
        {
            get
            {
                 return _iSignNum;
            }
            set
            {
                _iSignNum = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iResult, 0);
            _os.Write(iItemId, 1);
            _os.Write(iItemCount, 2);
            _os.Write(iSignNum, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iResult = (int) _is.Read(iResult, 0, false);

            iItemId = (int) _is.Read(iItemId, 1, false);

            iItemCount = (int) _is.Read(iItemCount, 2, false);

            iSignNum = (int) _is.Read(iSignNum, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iResult, "iResult");
            _ds.Display(iItemId, "iItemId");
            _ds.Display(iItemCount, "iItemCount");
            _ds.Display(iSignNum, "iSignNum");
        }

        public override void Clear()
        {
            iResult = 0;
            iItemId = 0;
            iItemCount = 0;
            iSignNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TContenstResetAndSignRsp();
            copied.iResult = this.iResult;
            copied.iItemId = this.iItemId;
            copied.iItemCount = this.iItemCount;
            copied.iSignNum = this.iSignNum;
            return copied;
        }
    }
}

