// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class InGameEffectList : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<InGameEffect> inGameEffectList;

        public int cardScore = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(inGameEffectList, 0);
            _os.Write(cardScore, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            inGameEffectList = (System.Collections.Generic.List<InGameEffect>) _is.Read(inGameEffectList, 0, false);

            cardScore = (int) _is.Read(cardScore, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(inGameEffectList, "inGameEffectList");
            _ds.Display(cardScore, "cardScore");
        }

        public override void Clear()
        {
            inGameEffectList = null;
            cardScore = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new InGameEffectList();
            copied.inGameEffectList = (System.Collections.Generic.List<InGameEffect>)JceUtil.DeepClone(this.inGameEffectList);
            copied.cardScore = this.cardScore;
            return copied;
        }
    }
}

