// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyAutoSortWaitHero : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public System.Collections.Generic.List<TAC_WaitHero> vecWaitHero {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(vecWaitHero, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecWaitHero, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(vecWaitHero, "vecWaitHero");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            vecWaitHero = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyAutoSortWaitHero();
            copied.i8ChairID = this.i8ChairID;
            copied.vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecWaitHero);
            return copied;
        }
    }
}

