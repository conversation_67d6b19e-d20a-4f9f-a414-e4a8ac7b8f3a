//所在的Excel 【ACG_MatchingLoadTips.xlsm】
//**********************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_MatchingLoadTips_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iMinLevel = 0;

        public int iMaxLevel = 0;

        public int iMinRankLv = 0;

        public int iMaxRankLv = 0;

        public string sTips = "";

        public int iSetNum = 0;

        public string sBackGround = "";

        public string sScene = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iMinLevel, 1);
            _os.Write(iMaxLevel, 2);
            _os.Write(iMinRankLv, 3);
            _os.Write(iMaxRankLv, 4);
            _os.Write(sTips, 5);
            _os.Write(iSetNum, 6);
            _os.Write(sBackGround, 7);
            _os.Write(sScene, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iMinLevel = (int) _is.Read(iMinLevel, 1, false);

            iMaxLevel = (int) _is.Read(iMaxLevel, 2, false);

            iMinRankLv = (int) _is.Read(iMinRankLv, 3, false);

            iMaxRankLv = (int) _is.Read(iMaxRankLv, 4, false);

            sTips = (string) _is.Read(sTips, 5, false);

            iSetNum = (int) _is.Read(iSetNum, 6, false);

            sBackGround = (string) _is.Read(sBackGround, 7, false);

            sScene = (string) _is.Read(sScene, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iMinLevel, "iMinLevel");
            _ds.Display(iMaxLevel, "iMaxLevel");
            _ds.Display(iMinRankLv, "iMinRankLv");
            _ds.Display(iMaxRankLv, "iMaxRankLv");
            _ds.Display(sTips, "sTips");
            _ds.Display(iSetNum, "iSetNum");
            _ds.Display(sBackGround, "sBackGround");
            _ds.Display(sScene, "sScene");
        }

        public override void Clear()
        {
            iID = 0;
            iMinLevel = 0;
            iMaxLevel = 0;
            iMinRankLv = 0;
            iMaxRankLv = 0;
            sTips = "";
            iSetNum = 0;
            sBackGround = "";
            sScene = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_MatchingLoadTips_Client();
            copied.iID = this.iID;
            copied.iMinLevel = this.iMinLevel;
            copied.iMaxLevel = this.iMaxLevel;
            copied.iMinRankLv = this.iMinRankLv;
            copied.iMaxRankLv = this.iMaxRankLv;
            copied.sTips = this.sTips;
            copied.iSetNum = this.iSetNum;
            copied.sBackGround = this.sBackGround;
            copied.sScene = this.sScene;
            return copied;
        }
    }
}

