//所在的Excel 【ACG_Mall.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Mall_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iItemId = 0;

        public int iItemNum = 0;

        public int iCurrencyType1 = 0;

        public int iCurrencyNum1 = 0;

        public int iCurrencyType2 = 0;

        public int iCurrencyNum2 = 0;

        public int iLimitType = 0;

        public int iLimitNum = 0;

        public int iNewTag = 0;

        public int iHotTag = 0;

        public int iPage = 0;

        public string sPoster = "";

        public string sBeginTime = "";

        public string sEndTime = "";

        public int iBeginSec = 0;

        public int iEndSec = 0;

        public int iDiscountCurrencyNum1 = 0;

        public string sJumpParamFroGet = "";

        public int iDiscountCurrencyNum2 = 0;

        public string sDiscountBeginTime = "";

        public string sDiscountEndTime = "";

        public int iDiscountBeginSec = 0;

        public int iDiscountEndSec = 0;

        public string sConditionHaveItem = "";

        public int iSort = 0;

        public string sConditionHideItem = "";

        public string sNewTagBeginTime = "";

        public string sNewTagEndTime = "";

        public int iNewTagBeginSec = 0;

        public int iNewTagEndSec = 0;

        public string sHotTagBeginTime = "";

        public string sHotTagEndTime = "";

        public int iHotTagBeginSec = 0;

        public int iHotTagEndSec = 0;

        public int iCanGiveGift = 0;

        public int iCanForceGiveGift = 0;

        public int iIsRedDotRemind = 0;

        public string sClientExtensions = "";

        public int iGiveGiftConditionType = 0;

        public int iRedDotMark = 0;

        public int iForbidVariablePrice = 0;

        public int iReflectPackId = 0;

        public int iPromoCode = 0;

        public string sLimitTimeTagBeginTime = "";

        public string sLimitTimeTagEndTime = "";

        public int iLimitTimeTagBeginSec = 0;

        public int iLimitTimeTagEndSec = 0;

        public string sExternalJump = "";

        public string sExternalJumpBeginTime = "";

        public string sExternalJumpEndTime = "";

        public int iExternalJumpBeginSec = 0;

        public int iExternalJumpEndSec = 0;

        public int iCanBuyHavingItem = 0;

        public int iPageType = 0;

        public int iCoupon = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iItemId, 1);
            _os.Write(iItemNum, 2);
            _os.Write(iCurrencyType1, 5);
            _os.Write(iCurrencyNum1, 6);
            _os.Write(iCurrencyType2, 7);
            _os.Write(iCurrencyNum2, 8);
            _os.Write(iLimitType, 9);
            _os.Write(iLimitNum, 10);
            _os.Write(iNewTag, 11);
            _os.Write(iHotTag, 12);
            _os.Write(iPage, 13);
            _os.Write(sPoster, 17);
            _os.Write(sBeginTime, 18);
            _os.Write(sEndTime, 19);
            _os.Write(iBeginSec, 20);
            _os.Write(iEndSec, 21);
            _os.Write(iDiscountCurrencyNum1, 22);
            _os.Write(sJumpParamFroGet, 27);
            _os.Write(iDiscountCurrencyNum2, 29);
            _os.Write(sDiscountBeginTime, 30);
            _os.Write(sDiscountEndTime, 31);
            _os.Write(iDiscountBeginSec, 32);
            _os.Write(iDiscountEndSec, 33);
            _os.Write(sConditionHaveItem, 34);
            _os.Write(iSort, 35);
            _os.Write(sConditionHideItem, 36);
            _os.Write(sNewTagBeginTime, 37);
            _os.Write(sNewTagEndTime, 38);
            _os.Write(iNewTagBeginSec, 39);
            _os.Write(iNewTagEndSec, 40);
            _os.Write(sHotTagBeginTime, 41);
            _os.Write(sHotTagEndTime, 42);
            _os.Write(iHotTagBeginSec, 43);
            _os.Write(iHotTagEndSec, 44);
            _os.Write(iCanGiveGift, 45);
            _os.Write(iCanForceGiveGift, 46);
            _os.Write(iIsRedDotRemind, 47);
            _os.Write(sClientExtensions, 48);
            _os.Write(iGiveGiftConditionType, 49);
            _os.Write(iRedDotMark, 50);
            _os.Write(iForbidVariablePrice, 51);
            _os.Write(iReflectPackId, 52);
            _os.Write(iPromoCode, 53);
            _os.Write(sLimitTimeTagBeginTime, 54);
            _os.Write(sLimitTimeTagEndTime, 55);
            _os.Write(iLimitTimeTagBeginSec, 56);
            _os.Write(iLimitTimeTagEndSec, 57);
            _os.Write(sExternalJump, 58);
            _os.Write(sExternalJumpBeginTime, 59);
            _os.Write(sExternalJumpEndTime, 60);
            _os.Write(iExternalJumpBeginSec, 61);
            _os.Write(iExternalJumpEndSec, 62);
            _os.Write(iCanBuyHavingItem, 63);
            _os.Write(iPageType, 64);
            _os.Write(iCoupon, 65);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iItemId = (int) _is.Read(iItemId, 1, false);

            iItemNum = (int) _is.Read(iItemNum, 2, false);

            iCurrencyType1 = (int) _is.Read(iCurrencyType1, 5, false);

            iCurrencyNum1 = (int) _is.Read(iCurrencyNum1, 6, false);

            iCurrencyType2 = (int) _is.Read(iCurrencyType2, 7, false);

            iCurrencyNum2 = (int) _is.Read(iCurrencyNum2, 8, false);

            iLimitType = (int) _is.Read(iLimitType, 9, false);

            iLimitNum = (int) _is.Read(iLimitNum, 10, false);

            iNewTag = (int) _is.Read(iNewTag, 11, false);

            iHotTag = (int) _is.Read(iHotTag, 12, false);

            iPage = (int) _is.Read(iPage, 13, false);

            sPoster = (string) _is.Read(sPoster, 17, false);

            sBeginTime = (string) _is.Read(sBeginTime, 18, false);

            sEndTime = (string) _is.Read(sEndTime, 19, false);

            iBeginSec = (int) _is.Read(iBeginSec, 20, false);

            iEndSec = (int) _is.Read(iEndSec, 21, false);

            iDiscountCurrencyNum1 = (int) _is.Read(iDiscountCurrencyNum1, 22, false);

            sJumpParamFroGet = (string) _is.Read(sJumpParamFroGet, 27, false);

            iDiscountCurrencyNum2 = (int) _is.Read(iDiscountCurrencyNum2, 29, false);

            sDiscountBeginTime = (string) _is.Read(sDiscountBeginTime, 30, false);

            sDiscountEndTime = (string) _is.Read(sDiscountEndTime, 31, false);

            iDiscountBeginSec = (int) _is.Read(iDiscountBeginSec, 32, false);

            iDiscountEndSec = (int) _is.Read(iDiscountEndSec, 33, false);

            sConditionHaveItem = (string) _is.Read(sConditionHaveItem, 34, false);

            iSort = (int) _is.Read(iSort, 35, false);

            sConditionHideItem = (string) _is.Read(sConditionHideItem, 36, false);

            sNewTagBeginTime = (string) _is.Read(sNewTagBeginTime, 37, false);

            sNewTagEndTime = (string) _is.Read(sNewTagEndTime, 38, false);

            iNewTagBeginSec = (int) _is.Read(iNewTagBeginSec, 39, false);

            iNewTagEndSec = (int) _is.Read(iNewTagEndSec, 40, false);

            sHotTagBeginTime = (string) _is.Read(sHotTagBeginTime, 41, false);

            sHotTagEndTime = (string) _is.Read(sHotTagEndTime, 42, false);

            iHotTagBeginSec = (int) _is.Read(iHotTagBeginSec, 43, false);

            iHotTagEndSec = (int) _is.Read(iHotTagEndSec, 44, false);

            iCanGiveGift = (int) _is.Read(iCanGiveGift, 45, false);

            iCanForceGiveGift = (int) _is.Read(iCanForceGiveGift, 46, false);

            iIsRedDotRemind = (int) _is.Read(iIsRedDotRemind, 47, false);

            sClientExtensions = (string) _is.Read(sClientExtensions, 48, false);

            iGiveGiftConditionType = (int) _is.Read(iGiveGiftConditionType, 49, false);

            iRedDotMark = (int) _is.Read(iRedDotMark, 50, false);

            iForbidVariablePrice = (int) _is.Read(iForbidVariablePrice, 51, false);

            iReflectPackId = (int) _is.Read(iReflectPackId, 52, false);

            iPromoCode = (int) _is.Read(iPromoCode, 53, false);

            sLimitTimeTagBeginTime = (string) _is.Read(sLimitTimeTagBeginTime, 54, false);

            sLimitTimeTagEndTime = (string) _is.Read(sLimitTimeTagEndTime, 55, false);

            iLimitTimeTagBeginSec = (int) _is.Read(iLimitTimeTagBeginSec, 56, false);

            iLimitTimeTagEndSec = (int) _is.Read(iLimitTimeTagEndSec, 57, false);

            sExternalJump = (string) _is.Read(sExternalJump, 58, false);

            sExternalJumpBeginTime = (string) _is.Read(sExternalJumpBeginTime, 59, false);

            sExternalJumpEndTime = (string) _is.Read(sExternalJumpEndTime, 60, false);

            iExternalJumpBeginSec = (int) _is.Read(iExternalJumpBeginSec, 61, false);

            iExternalJumpEndSec = (int) _is.Read(iExternalJumpEndSec, 62, false);

            iCanBuyHavingItem = (int) _is.Read(iCanBuyHavingItem, 63, false);

            iPageType = (int) _is.Read(iPageType, 64, false);

            iCoupon = (int) _is.Read(iCoupon, 65, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iItemId, "iItemId");
            _ds.Display(iItemNum, "iItemNum");
            _ds.Display(iCurrencyType1, "iCurrencyType1");
            _ds.Display(iCurrencyNum1, "iCurrencyNum1");
            _ds.Display(iCurrencyType2, "iCurrencyType2");
            _ds.Display(iCurrencyNum2, "iCurrencyNum2");
            _ds.Display(iLimitType, "iLimitType");
            _ds.Display(iLimitNum, "iLimitNum");
            _ds.Display(iNewTag, "iNewTag");
            _ds.Display(iHotTag, "iHotTag");
            _ds.Display(iPage, "iPage");
            _ds.Display(sPoster, "sPoster");
            _ds.Display(sBeginTime, "sBeginTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(iBeginSec, "iBeginSec");
            _ds.Display(iEndSec, "iEndSec");
            _ds.Display(iDiscountCurrencyNum1, "iDiscountCurrencyNum1");
            _ds.Display(sJumpParamFroGet, "sJumpParamFroGet");
            _ds.Display(iDiscountCurrencyNum2, "iDiscountCurrencyNum2");
            _ds.Display(sDiscountBeginTime, "sDiscountBeginTime");
            _ds.Display(sDiscountEndTime, "sDiscountEndTime");
            _ds.Display(iDiscountBeginSec, "iDiscountBeginSec");
            _ds.Display(iDiscountEndSec, "iDiscountEndSec");
            _ds.Display(sConditionHaveItem, "sConditionHaveItem");
            _ds.Display(iSort, "iSort");
            _ds.Display(sConditionHideItem, "sConditionHideItem");
            _ds.Display(sNewTagBeginTime, "sNewTagBeginTime");
            _ds.Display(sNewTagEndTime, "sNewTagEndTime");
            _ds.Display(iNewTagBeginSec, "iNewTagBeginSec");
            _ds.Display(iNewTagEndSec, "iNewTagEndSec");
            _ds.Display(sHotTagBeginTime, "sHotTagBeginTime");
            _ds.Display(sHotTagEndTime, "sHotTagEndTime");
            _ds.Display(iHotTagBeginSec, "iHotTagBeginSec");
            _ds.Display(iHotTagEndSec, "iHotTagEndSec");
            _ds.Display(iCanGiveGift, "iCanGiveGift");
            _ds.Display(iCanForceGiveGift, "iCanForceGiveGift");
            _ds.Display(iIsRedDotRemind, "iIsRedDotRemind");
            _ds.Display(sClientExtensions, "sClientExtensions");
            _ds.Display(iGiveGiftConditionType, "iGiveGiftConditionType");
            _ds.Display(iRedDotMark, "iRedDotMark");
            _ds.Display(iForbidVariablePrice, "iForbidVariablePrice");
            _ds.Display(iReflectPackId, "iReflectPackId");
            _ds.Display(iPromoCode, "iPromoCode");
            _ds.Display(sLimitTimeTagBeginTime, "sLimitTimeTagBeginTime");
            _ds.Display(sLimitTimeTagEndTime, "sLimitTimeTagEndTime");
            _ds.Display(iLimitTimeTagBeginSec, "iLimitTimeTagBeginSec");
            _ds.Display(iLimitTimeTagEndSec, "iLimitTimeTagEndSec");
            _ds.Display(sExternalJump, "sExternalJump");
            _ds.Display(sExternalJumpBeginTime, "sExternalJumpBeginTime");
            _ds.Display(sExternalJumpEndTime, "sExternalJumpEndTime");
            _ds.Display(iExternalJumpBeginSec, "iExternalJumpBeginSec");
            _ds.Display(iExternalJumpEndSec, "iExternalJumpEndSec");
            _ds.Display(iCanBuyHavingItem, "iCanBuyHavingItem");
            _ds.Display(iPageType, "iPageType");
            _ds.Display(iCoupon, "iCoupon");
        }

        public override void Clear()
        {
            iID = 0;
            iItemId = 0;
            iItemNum = 0;
            iCurrencyType1 = 0;
            iCurrencyNum1 = 0;
            iCurrencyType2 = 0;
            iCurrencyNum2 = 0;
            iLimitType = 0;
            iLimitNum = 0;
            iNewTag = 0;
            iHotTag = 0;
            iPage = 0;
            sPoster = "";
            sBeginTime = "";
            sEndTime = "";
            iBeginSec = 0;
            iEndSec = 0;
            iDiscountCurrencyNum1 = 0;
            sJumpParamFroGet = "";
            iDiscountCurrencyNum2 = 0;
            sDiscountBeginTime = "";
            sDiscountEndTime = "";
            iDiscountBeginSec = 0;
            iDiscountEndSec = 0;
            sConditionHaveItem = "";
            iSort = 0;
            sConditionHideItem = "";
            sNewTagBeginTime = "";
            sNewTagEndTime = "";
            iNewTagBeginSec = 0;
            iNewTagEndSec = 0;
            sHotTagBeginTime = "";
            sHotTagEndTime = "";
            iHotTagBeginSec = 0;
            iHotTagEndSec = 0;
            iCanGiveGift = 0;
            iCanForceGiveGift = 0;
            iIsRedDotRemind = 0;
            sClientExtensions = "";
            iGiveGiftConditionType = 0;
            iRedDotMark = 0;
            iForbidVariablePrice = 0;
            iReflectPackId = 0;
            iPromoCode = 0;
            sLimitTimeTagBeginTime = "";
            sLimitTimeTagEndTime = "";
            iLimitTimeTagBeginSec = 0;
            iLimitTimeTagEndSec = 0;
            sExternalJump = "";
            sExternalJumpBeginTime = "";
            sExternalJumpEndTime = "";
            iExternalJumpBeginSec = 0;
            iExternalJumpEndSec = 0;
            iCanBuyHavingItem = 0;
            iPageType = 0;
            iCoupon = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Mall_Client();
            copied.iID = this.iID;
            copied.iItemId = this.iItemId;
            copied.iItemNum = this.iItemNum;
            copied.iCurrencyType1 = this.iCurrencyType1;
            copied.iCurrencyNum1 = this.iCurrencyNum1;
            copied.iCurrencyType2 = this.iCurrencyType2;
            copied.iCurrencyNum2 = this.iCurrencyNum2;
            copied.iLimitType = this.iLimitType;
            copied.iLimitNum = this.iLimitNum;
            copied.iNewTag = this.iNewTag;
            copied.iHotTag = this.iHotTag;
            copied.iPage = this.iPage;
            copied.sPoster = this.sPoster;
            copied.sBeginTime = this.sBeginTime;
            copied.sEndTime = this.sEndTime;
            copied.iBeginSec = this.iBeginSec;
            copied.iEndSec = this.iEndSec;
            copied.iDiscountCurrencyNum1 = this.iDiscountCurrencyNum1;
            copied.sJumpParamFroGet = this.sJumpParamFroGet;
            copied.iDiscountCurrencyNum2 = this.iDiscountCurrencyNum2;
            copied.sDiscountBeginTime = this.sDiscountBeginTime;
            copied.sDiscountEndTime = this.sDiscountEndTime;
            copied.iDiscountBeginSec = this.iDiscountBeginSec;
            copied.iDiscountEndSec = this.iDiscountEndSec;
            copied.sConditionHaveItem = this.sConditionHaveItem;
            copied.iSort = this.iSort;
            copied.sConditionHideItem = this.sConditionHideItem;
            copied.sNewTagBeginTime = this.sNewTagBeginTime;
            copied.sNewTagEndTime = this.sNewTagEndTime;
            copied.iNewTagBeginSec = this.iNewTagBeginSec;
            copied.iNewTagEndSec = this.iNewTagEndSec;
            copied.sHotTagBeginTime = this.sHotTagBeginTime;
            copied.sHotTagEndTime = this.sHotTagEndTime;
            copied.iHotTagBeginSec = this.iHotTagBeginSec;
            copied.iHotTagEndSec = this.iHotTagEndSec;
            copied.iCanGiveGift = this.iCanGiveGift;
            copied.iCanForceGiveGift = this.iCanForceGiveGift;
            copied.iIsRedDotRemind = this.iIsRedDotRemind;
            copied.sClientExtensions = this.sClientExtensions;
            copied.iGiveGiftConditionType = this.iGiveGiftConditionType;
            copied.iRedDotMark = this.iRedDotMark;
            copied.iForbidVariablePrice = this.iForbidVariablePrice;
            copied.iReflectPackId = this.iReflectPackId;
            copied.iPromoCode = this.iPromoCode;
            copied.sLimitTimeTagBeginTime = this.sLimitTimeTagBeginTime;
            copied.sLimitTimeTagEndTime = this.sLimitTimeTagEndTime;
            copied.iLimitTimeTagBeginSec = this.iLimitTimeTagBeginSec;
            copied.iLimitTimeTagEndSec = this.iLimitTimeTagEndSec;
            copied.sExternalJump = this.sExternalJump;
            copied.sExternalJumpBeginTime = this.sExternalJumpBeginTime;
            copied.sExternalJumpEndTime = this.sExternalJumpEndTime;
            copied.iExternalJumpBeginSec = this.iExternalJumpBeginSec;
            copied.iExternalJumpEndSec = this.iExternalJumpEndSec;
            copied.iCanBuyHavingItem = this.iCanBuyHavingItem;
            copied.iPageType = this.iPageType;
            copied.iCoupon = this.iCoupon;
            return copied;
        }
    }
}

