// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetUserMatchBanInfoReq : Wup.Jce.JceStruct
    {
        long _lUin = 0;
        public long lUin
        {
            get
            {
                 return _lUin;
            }
            set
            {
                _lUin = value; 
            }
        }

        long _iSceneID = 0;
        public long iSceneID
        {
            get
            {
                 return _iSceneID;
            }
            set
            {
                _iSceneID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lUin, 0);
            _os.Write(iSceneID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lUin = (long) _is.Read(lUin, 0, false);

            iSceneID = (long) _is.Read(iSceneID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lUin, "lUin");
            _ds.Display(iSceneID, "iSceneID");
        }

        public override void Clear()
        {
            lUin = 0;
            iSceneID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetUserMatchBanInfoReq();
            copied.lUin = this.lUin;
            copied.iSceneID = this.iSceneID;
            return copied;
        }
    }
}

