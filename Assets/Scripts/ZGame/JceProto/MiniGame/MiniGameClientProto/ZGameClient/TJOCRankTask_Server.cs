// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCRankTask_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iJOCModuleID = 0;

        public string sTaskName = "";

        public string sTaskDes = "";

        public string sProgressTarget = "";

        public int iTargetType = 0;

        public string sCondition = "";

        public int iItemID1 = 0;

        public int iItemCount1 = 0;

        public int iAchivementID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iJOCModuleID, 1);
            _os.Write(sTaskName, 2);
            _os.Write(sTaskDes, 3);
            _os.Write(sProgressTarget, 4);
            _os.Write(iTargetType, 5);
            _os.Write(sCondition, 6);
            _os.Write(iItemID1, 7);
            _os.Write(iItemCount1, 8);
            _os.Write(iAchivementID, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iJOCModuleID = (int) _is.Read(iJOCModuleID, 1, false);

            sTaskName = (string) _is.Read(sTaskName, 2, false);

            sTaskDes = (string) _is.Read(sTaskDes, 3, false);

            sProgressTarget = (string) _is.Read(sProgressTarget, 4, false);

            iTargetType = (int) _is.Read(iTargetType, 5, false);

            sCondition = (string) _is.Read(sCondition, 6, false);

            iItemID1 = (int) _is.Read(iItemID1, 7, false);

            iItemCount1 = (int) _is.Read(iItemCount1, 8, false);

            iAchivementID = (int) _is.Read(iAchivementID, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iJOCModuleID, "iJOCModuleID");
            _ds.Display(sTaskName, "sTaskName");
            _ds.Display(sTaskDes, "sTaskDes");
            _ds.Display(sProgressTarget, "sProgressTarget");
            _ds.Display(iTargetType, "iTargetType");
            _ds.Display(sCondition, "sCondition");
            _ds.Display(iItemID1, "iItemID1");
            _ds.Display(iItemCount1, "iItemCount1");
            _ds.Display(iAchivementID, "iAchivementID");
        }

        public override void Clear()
        {
            iID = 0;
            iJOCModuleID = 0;
            sTaskName = "";
            sTaskDes = "";
            sProgressTarget = "";
            iTargetType = 0;
            sCondition = "";
            iItemID1 = 0;
            iItemCount1 = 0;
            iAchivementID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCRankTask_Server();
            copied.iID = this.iID;
            copied.iJOCModuleID = this.iJOCModuleID;
            copied.sTaskName = this.sTaskName;
            copied.sTaskDes = this.sTaskDes;
            copied.sProgressTarget = this.sProgressTarget;
            copied.iTargetType = this.iTargetType;
            copied.sCondition = this.sCondition;
            copied.iItemID1 = this.iItemID1;
            copied.iItemCount1 = this.iItemCount1;
            copied.iAchivementID = this.iAchivementID;
            return copied;
        }
    }
}

