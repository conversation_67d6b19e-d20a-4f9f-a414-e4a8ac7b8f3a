// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TPandoraNotify : Wup.Jce.JceStruct
    {
        public bool enabled = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(enabled, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            enabled = (bool) _is.Read(enabled, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(enabled, "enabled");
        }

        public override void Clear()
        {
            enabled = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TPandoraNotify();
            copied.enabled = this.enabled;
            return copied;
        }
    }
}

