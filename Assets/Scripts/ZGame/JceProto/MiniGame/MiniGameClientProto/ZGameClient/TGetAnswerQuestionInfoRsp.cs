// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetAnswerQuestionInfoRsp : Wup.Jce.JceStruct
    {
        public int iResult = 0;

        public System.Collections.Generic.List<TAnswerQuestionInfo> vecAnswerQuestionInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iResult, 0);
            _os.Write(vecAnswerQuestionInfo, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iResult = (int) _is.Read(iResult, 0, false);

            vecAnswerQuestionInfo = (System.Collections.Generic.List<TAnswerQuestionInfo>) _is.Read(vecAnswerQuestionInfo, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iResult, "iResult");
            _ds.Display(vecAnswerQuestionInfo, "vecAnswerQuestionInfo");
        }

        public override void Clear()
        {
            iResult = 0;
            vecAnswerQuestionInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetAnswerQuestionInfoRsp();
            copied.iResult = this.iResult;
            copied.vecAnswerQuestionInfo = (System.Collections.Generic.List<TAnswerQuestionInfo>)JceUtil.DeepClone(this.vecAnswerQuestionInfo);
            return copied;
        }
    }
}

