// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GetTinyOriginTaskRewardReq : Wup.Jce.JceStruct
    {
        int _iTaskID = 0;
        public int iTaskID
        {
            get
            {
                 return _iTaskID;
            }
            set
            {
                _iTaskID = value; 
            }
        }

        public TMidasTokenInfo stMidasToken {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTaskID, 0);
            _os.Write(stMidasToken, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTaskID = (int) _is.Read(iTaskID, 0, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTaskID, "iTaskID");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            iTaskID = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GetTinyOriginTaskRewardReq();
            copied.iTaskID = this.iTaskID;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

