// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ScheduleDailyFirstWin : Wup.Jce.JceStruct
    {
        public int iScheduleID = 0;

        public int iLastUpdateTimeStamp = 0;

        public int iCodeDown = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iScheduleID, 0);
            _os.Write(iLastUpdateTimeStamp, 1);
            _os.Write(iCodeDown, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iScheduleID = (int) _is.Read(iScheduleID, 0, false);

            iLastUpdateTimeStamp = (int) _is.Read(iLastUpdateTimeStamp, 1, false);

            iCodeDown = (int) _is.Read(iCodeDown, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iScheduleID, "iScheduleID");
            _ds.Display(iLastUpdateTimeStamp, "iLastUpdateTimeStamp");
            _ds.Display(iCodeDown, "iCodeDown");
        }

        public override void Clear()
        {
            iScheduleID = 0;
            iLastUpdateTimeStamp = 0;
            iCodeDown = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ScheduleDailyFirstWin();
            copied.iScheduleID = this.iScheduleID;
            copied.iLastUpdateTimeStamp = this.iLastUpdateTimeStamp;
            copied.iCodeDown = this.iCodeDown;
            return copied;
        }
    }
}

