// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMainHallBanner_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iType = 0;

        public string sPictureUrl = "";

        public string sJumpParam = "";

        public int iPriority = 0;

        public int iShowDuration = 0;

        public int iBeginTime = 0;

        public int iEndTime = 0;

        public int iItemID = 0;

        public int iGoodsID = 0;

        public string sText = "";

        public int iPandoraID = 0;

        public int iChannelListType = 0;

        public string sChannelList = "";

        public int iMinClientVerion = 0;

        public int iMaxClientVersion = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iType, 1);
            _os.Write(sPictureUrl, 2);
            _os.Write(sJumpParam, 3);
            _os.Write(iPriority, 4);
            _os.Write(iShowDuration, 5);
            _os.Write(iBeginTime, 6);
            _os.Write(iEndTime, 7);
            _os.Write(iItemID, 8);
            _os.Write(iGoodsID, 9);
            _os.Write(sText, 10);
            _os.Write(iPandoraID, 11);
            _os.Write(iChannelListType, 12);
            _os.Write(sChannelList, 13);
            _os.Write(iMinClientVerion, 14);
            _os.Write(iMaxClientVersion, 15);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iType = (int) _is.Read(iType, 1, false);

            sPictureUrl = (string) _is.Read(sPictureUrl, 2, false);

            sJumpParam = (string) _is.Read(sJumpParam, 3, false);

            iPriority = (int) _is.Read(iPriority, 4, false);

            iShowDuration = (int) _is.Read(iShowDuration, 5, false);

            iBeginTime = (int) _is.Read(iBeginTime, 6, false);

            iEndTime = (int) _is.Read(iEndTime, 7, false);

            iItemID = (int) _is.Read(iItemID, 8, false);

            iGoodsID = (int) _is.Read(iGoodsID, 9, false);

            sText = (string) _is.Read(sText, 10, false);

            iPandoraID = (int) _is.Read(iPandoraID, 11, false);

            iChannelListType = (int) _is.Read(iChannelListType, 12, false);

            sChannelList = (string) _is.Read(sChannelList, 13, false);

            iMinClientVerion = (int) _is.Read(iMinClientVerion, 14, false);

            iMaxClientVersion = (int) _is.Read(iMaxClientVersion, 15, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iType, "iType");
            _ds.Display(sPictureUrl, "sPictureUrl");
            _ds.Display(sJumpParam, "sJumpParam");
            _ds.Display(iPriority, "iPriority");
            _ds.Display(iShowDuration, "iShowDuration");
            _ds.Display(iBeginTime, "iBeginTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iGoodsID, "iGoodsID");
            _ds.Display(sText, "sText");
            _ds.Display(iPandoraID, "iPandoraID");
            _ds.Display(iChannelListType, "iChannelListType");
            _ds.Display(sChannelList, "sChannelList");
            _ds.Display(iMinClientVerion, "iMinClientVerion");
            _ds.Display(iMaxClientVersion, "iMaxClientVersion");
        }

        public override void Clear()
        {
            iID = 0;
            iType = 0;
            sPictureUrl = "";
            sJumpParam = "";
            iPriority = 0;
            iShowDuration = 0;
            iBeginTime = 0;
            iEndTime = 0;
            iItemID = 0;
            iGoodsID = 0;
            sText = "";
            iPandoraID = 0;
            iChannelListType = 0;
            sChannelList = "";
            iMinClientVerion = 0;
            iMaxClientVersion = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMainHallBanner_Server();
            copied.iID = this.iID;
            copied.iType = this.iType;
            copied.sPictureUrl = this.sPictureUrl;
            copied.sJumpParam = this.sJumpParam;
            copied.iPriority = this.iPriority;
            copied.iShowDuration = this.iShowDuration;
            copied.iBeginTime = this.iBeginTime;
            copied.iEndTime = this.iEndTime;
            copied.iItemID = this.iItemID;
            copied.iGoodsID = this.iGoodsID;
            copied.sText = this.sText;
            copied.iPandoraID = this.iPandoraID;
            copied.iChannelListType = this.iChannelListType;
            copied.sChannelList = this.sChannelList;
            copied.iMinClientVerion = this.iMinClientVerion;
            copied.iMaxClientVersion = this.iMaxClientVersion;
            return copied;
        }
    }
}

