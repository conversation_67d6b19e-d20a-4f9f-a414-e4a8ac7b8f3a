// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TQuizEventSubmitReq : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iResult = 0;

        public TMidasTokenInfo stMidasToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iResult, 1);
            _os.Write(stMidasToken, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iResult = (int) _is.Read(iResult, 1, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iResult, "iResult");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            iID = 0;
            iResult = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TQuizEventSubmitReq();
            copied.iID = this.iID;
            copied.iResult = this.iResult;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

