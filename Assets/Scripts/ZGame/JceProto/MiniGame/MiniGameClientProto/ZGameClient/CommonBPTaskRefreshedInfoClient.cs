// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CommonBPTaskRefreshedInfoClient : Wup.Jce.JceStruct
    {
        public int iRefreshedCount = 0;

        public long lRefreshedCountUpdateTime = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRefreshedCount, 0);
            _os.Write(lRefreshedCountUpdateTime, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRefreshedCount = (int) _is.Read(iRefreshedCount, 0, false);

            lRefreshedCountUpdateTime = (long) _is.Read(lRefreshedCountUpdateTime, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRefreshedCount, "iRefreshedCount");
            _ds.Display(lRefreshedCountUpdateTime, "lRefreshedCountUpdateTime");
        }

        public override void Clear()
        {
            iRefreshedCount = 0;
            lRefreshedCountUpdateTime = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CommonBPTaskRefreshedInfoClient();
            copied.iRefreshedCount = this.iRefreshedCount;
            copied.lRefreshedCountUpdateTime = this.lRefreshedCountUpdateTime;
            return copied;
        }
    }
}

