// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMakeupWeeklyLoginReq : Wup.Jce.JceStruct
    {
        int _iWeekIndex = 0;
        public int iWeekIndex
        {
            get
            {
                 return _iWeekIndex;
            }
            set
            {
                _iWeekIndex = value; 
            }
        }

        public TMidasTokenInfo stMidasToken {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iWeekIndex, 0);
            _os.Write(stMidasToken, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iWeekIndex = (int) _is.Read(iWeekIndex, 0, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iWeekIndex, "iWeekIndex");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            iWeekIndex = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMakeupWeeklyLoginReq();
            copied.iWeekIndex = this.iWeekIndex;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

