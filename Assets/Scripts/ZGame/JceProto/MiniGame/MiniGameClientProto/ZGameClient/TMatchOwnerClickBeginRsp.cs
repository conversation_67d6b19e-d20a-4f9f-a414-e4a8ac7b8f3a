// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMatchOwnerClickBeginRsp : Wup.Jce.JceStruct
    {
        public short nResultID = 0;

        public long lBanUin = 0;

        public string sBanNickName = "";

        public int iLeftBanSeconds = 0;

        public System.Collections.Generic.List<long> vecConflictUinList;

        public System.Collections.Generic.List<TMatchBanPlayerInfo> vecBanPlayerInfo;

        public System.Collections.Generic.List<long> fightLogicVersionConflictUins;

        public System.Collections.Generic.List<long> rankDataSeasonConflictUins;

        public System.Collections.Generic.List<long> setIndexConflictUins;

        public System.Collections.Generic.List<long> offlinePlayerUins;

        public System.Collections.Generic.List<long> notInMatchRoomPlayerUins;

        public int warmFlag = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(lBanUin, 1);
            _os.Write(sBanNickName, 2);
            _os.Write(iLeftBanSeconds, 3);
            _os.Write(vecConflictUinList, 4);
            _os.Write(vecBanPlayerInfo, 5);
            _os.Write(fightLogicVersionConflictUins, 6);
            _os.Write(rankDataSeasonConflictUins, 7);
            _os.Write(setIndexConflictUins, 8);
            _os.Write(offlinePlayerUins, 9);
            _os.Write(notInMatchRoomPlayerUins, 10);
            _os.Write(warmFlag, 11);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (short) _is.Read(nResultID, 0, false);

            lBanUin = (long) _is.Read(lBanUin, 1, false);

            sBanNickName = (string) _is.Read(sBanNickName, 2, false);

            iLeftBanSeconds = (int) _is.Read(iLeftBanSeconds, 3, false);

            vecConflictUinList = (System.Collections.Generic.List<long>) _is.Read(vecConflictUinList, 4, false);

            vecBanPlayerInfo = (System.Collections.Generic.List<TMatchBanPlayerInfo>) _is.Read(vecBanPlayerInfo, 5, false);

            fightLogicVersionConflictUins = (System.Collections.Generic.List<long>) _is.Read(fightLogicVersionConflictUins, 6, false);

            rankDataSeasonConflictUins = (System.Collections.Generic.List<long>) _is.Read(rankDataSeasonConflictUins, 7, false);

            setIndexConflictUins = (System.Collections.Generic.List<long>) _is.Read(setIndexConflictUins, 8, false);

            offlinePlayerUins = (System.Collections.Generic.List<long>) _is.Read(offlinePlayerUins, 9, false);

            notInMatchRoomPlayerUins = (System.Collections.Generic.List<long>) _is.Read(notInMatchRoomPlayerUins, 10, false);

            warmFlag = (int) _is.Read(warmFlag, 11, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(lBanUin, "lBanUin");
            _ds.Display(sBanNickName, "sBanNickName");
            _ds.Display(iLeftBanSeconds, "iLeftBanSeconds");
            _ds.Display(vecConflictUinList, "vecConflictUinList");
            _ds.Display(vecBanPlayerInfo, "vecBanPlayerInfo");
            _ds.Display(fightLogicVersionConflictUins, "fightLogicVersionConflictUins");
            _ds.Display(rankDataSeasonConflictUins, "rankDataSeasonConflictUins");
            _ds.Display(setIndexConflictUins, "setIndexConflictUins");
            _ds.Display(offlinePlayerUins, "offlinePlayerUins");
            _ds.Display(notInMatchRoomPlayerUins, "notInMatchRoomPlayerUins");
            _ds.Display(warmFlag, "warmFlag");
        }

        public override void Clear()
        {
            nResultID = 0;
            lBanUin = 0;
            sBanNickName = "";
            iLeftBanSeconds = 0;
            vecConflictUinList = null;
            vecBanPlayerInfo = null;
            fightLogicVersionConflictUins = null;
            rankDataSeasonConflictUins = null;
            setIndexConflictUins = null;
            offlinePlayerUins = null;
            notInMatchRoomPlayerUins = null;
            warmFlag = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMatchOwnerClickBeginRsp();
            copied.nResultID = this.nResultID;
            copied.lBanUin = this.lBanUin;
            copied.sBanNickName = this.sBanNickName;
            copied.iLeftBanSeconds = this.iLeftBanSeconds;
            copied.vecConflictUinList = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.vecConflictUinList);
            copied.vecBanPlayerInfo = (System.Collections.Generic.List<TMatchBanPlayerInfo>)JceUtil.DeepClone(this.vecBanPlayerInfo);
            copied.fightLogicVersionConflictUins = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.fightLogicVersionConflictUins);
            copied.rankDataSeasonConflictUins = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.rankDataSeasonConflictUins);
            copied.setIndexConflictUins = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.setIndexConflictUins);
            copied.offlinePlayerUins = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.offlinePlayerUins);
            copied.notInMatchRoomPlayerUins = (System.Collections.Generic.List<long>)JceUtil.DeepClone(this.notInMatchRoomPlayerUins);
            copied.warmFlag = this.warmFlag;
            return copied;
        }
    }
}

