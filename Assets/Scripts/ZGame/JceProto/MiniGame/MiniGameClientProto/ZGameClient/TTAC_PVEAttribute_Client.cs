//所在的Excel 【TAC_PVE.xlsm】
//***********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_PVEAttribute_Client : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iValuePercent = 0;
        public int iValuePercent
        {
            get
            {
                 return _iValuePercent;
            }
            set
            {
                _iValuePercent = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iValuePercent, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iValuePercent = (int) _is.Read(iValuePercent, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iValuePercent, "iValuePercent");
        }

        public override void Clear()
        {
            iID = 0;
            iValuePercent = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_PVEAttribute_Client();
            copied.iID = this.iID;
            copied.iValuePercent = this.iValuePercent;
            return copied;
        }
    }
}

