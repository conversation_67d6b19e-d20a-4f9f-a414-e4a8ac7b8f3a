// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TChat_QuickChat_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sContent = "";

        public string sVoiceResource = "";

        public int iIsForbid = 0;

        public string sAnchorName = "";

        public int iFilterType = 0;

        public int iAudioLenInMilliSecond = 0;

        public int iIsDefault = 0;

        public int iDefaultPos = 0;

        public int iShowCtrl = 0;

        public int iTagType1 = 0;

        public string sTagContent1 = "";

        public string sTagColor1 = "";

        public int iTagType2 = 0;

        public string sTagContent2 = "";

        public string sTagColor2 = "";

        public string sAudioBankHall = "";

        public string sAudioBankSelf = "";

        public string sAudioBankEnemy = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sContent, 1);
            _os.Write(sVoiceResource, 2);
            _os.Write(iIsForbid, 3);
            _os.Write(sAnchorName, 4);
            _os.Write(iFilterType, 10);
            _os.Write(iAudioLenInMilliSecond, 13);
            _os.Write(iIsDefault, 14);
            _os.Write(iDefaultPos, 15);
            _os.Write(iShowCtrl, 16);
            _os.Write(iTagType1, 17);
            _os.Write(sTagContent1, 18);
            _os.Write(sTagColor1, 19);
            _os.Write(iTagType2, 20);
            _os.Write(sTagContent2, 21);
            _os.Write(sTagColor2, 22);
            _os.Write(sAudioBankHall, 24);
            _os.Write(sAudioBankSelf, 25);
            _os.Write(sAudioBankEnemy, 26);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sContent = (string) _is.Read(sContent, 1, false);

            sVoiceResource = (string) _is.Read(sVoiceResource, 2, false);

            iIsForbid = (int) _is.Read(iIsForbid, 3, false);

            sAnchorName = (string) _is.Read(sAnchorName, 4, false);

            iFilterType = (int) _is.Read(iFilterType, 10, false);

            iAudioLenInMilliSecond = (int) _is.Read(iAudioLenInMilliSecond, 13, false);

            iIsDefault = (int) _is.Read(iIsDefault, 14, false);

            iDefaultPos = (int) _is.Read(iDefaultPos, 15, false);

            iShowCtrl = (int) _is.Read(iShowCtrl, 16, false);

            iTagType1 = (int) _is.Read(iTagType1, 17, false);

            sTagContent1 = (string) _is.Read(sTagContent1, 18, false);

            sTagColor1 = (string) _is.Read(sTagColor1, 19, false);

            iTagType2 = (int) _is.Read(iTagType2, 20, false);

            sTagContent2 = (string) _is.Read(sTagContent2, 21, false);

            sTagColor2 = (string) _is.Read(sTagColor2, 22, false);

            sAudioBankHall = (string) _is.Read(sAudioBankHall, 24, false);

            sAudioBankSelf = (string) _is.Read(sAudioBankSelf, 25, false);

            sAudioBankEnemy = (string) _is.Read(sAudioBankEnemy, 26, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sContent, "sContent");
            _ds.Display(sVoiceResource, "sVoiceResource");
            _ds.Display(iIsForbid, "iIsForbid");
            _ds.Display(sAnchorName, "sAnchorName");
            _ds.Display(iFilterType, "iFilterType");
            _ds.Display(iAudioLenInMilliSecond, "iAudioLenInMilliSecond");
            _ds.Display(iIsDefault, "iIsDefault");
            _ds.Display(iDefaultPos, "iDefaultPos");
            _ds.Display(iShowCtrl, "iShowCtrl");
            _ds.Display(iTagType1, "iTagType1");
            _ds.Display(sTagContent1, "sTagContent1");
            _ds.Display(sTagColor1, "sTagColor1");
            _ds.Display(iTagType2, "iTagType2");
            _ds.Display(sTagContent2, "sTagContent2");
            _ds.Display(sTagColor2, "sTagColor2");
            _ds.Display(sAudioBankHall, "sAudioBankHall");
            _ds.Display(sAudioBankSelf, "sAudioBankSelf");
            _ds.Display(sAudioBankEnemy, "sAudioBankEnemy");
        }

        public override void Clear()
        {
            iID = 0;
            sContent = "";
            sVoiceResource = "";
            iIsForbid = 0;
            sAnchorName = "";
            iFilterType = 0;
            iAudioLenInMilliSecond = 0;
            iIsDefault = 0;
            iDefaultPos = 0;
            iShowCtrl = 0;
            iTagType1 = 0;
            sTagContent1 = "";
            sTagColor1 = "";
            iTagType2 = 0;
            sTagContent2 = "";
            sTagColor2 = "";
            sAudioBankHall = "";
            sAudioBankSelf = "";
            sAudioBankEnemy = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TChat_QuickChat_Client();
            copied.iID = this.iID;
            copied.sContent = this.sContent;
            copied.sVoiceResource = this.sVoiceResource;
            copied.iIsForbid = this.iIsForbid;
            copied.sAnchorName = this.sAnchorName;
            copied.iFilterType = this.iFilterType;
            copied.iAudioLenInMilliSecond = this.iAudioLenInMilliSecond;
            copied.iIsDefault = this.iIsDefault;
            copied.iDefaultPos = this.iDefaultPos;
            copied.iShowCtrl = this.iShowCtrl;
            copied.iTagType1 = this.iTagType1;
            copied.sTagContent1 = this.sTagContent1;
            copied.sTagColor1 = this.sTagColor1;
            copied.iTagType2 = this.iTagType2;
            copied.sTagContent2 = this.sTagContent2;
            copied.sTagColor2 = this.sTagColor2;
            copied.sAudioBankHall = this.sAudioBankHall;
            copied.sAudioBankSelf = this.sAudioBankSelf;
            copied.sAudioBankEnemy = this.sAudioBankEnemy;
            return copied;
        }
    }
}

