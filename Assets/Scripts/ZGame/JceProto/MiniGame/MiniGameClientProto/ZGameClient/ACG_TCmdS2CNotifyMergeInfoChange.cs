// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyMergeInfoChange : Wup.Jce.JceStruct
    {
        int _iChairID = 0;
        public int iChairID
        {
            get
            {
                 return _iChairID;
            }
            set
            {
                _iChairID = value; 
            }
        }

        int _mergeCfgId = 0;
        public int mergeCfgId
        {
            get
            {
                 return _mergeCfgId;
            }
            set
            {
                _mergeCfgId = value; 
            }
        }

        public System.Collections.Generic.List<int> mergeHeroEntityIds {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iChairID, 0);
            _os.Write(mergeCfgId, 1);
            _os.Write(mergeHeroEntityIds, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iChairID = (int) _is.Read(iChairID, 0, false);

            mergeCfgId = (int) _is.Read(mergeCfgId, 1, false);

            mergeHeroEntityIds = (System.Collections.Generic.List<int>) _is.Read(mergeHeroEntityIds, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iChairID, "iChairID");
            _ds.Display(mergeCfgId, "mergeCfgId");
            _ds.Display(mergeHeroEntityIds, "mergeHeroEntityIds");
        }

        public override void Clear()
        {
            iChairID = 0;
            mergeCfgId = 0;
            mergeHeroEntityIds = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyMergeInfoChange();
            copied.iChairID = this.iChairID;
            copied.mergeCfgId = this.mergeCfgId;
            copied.mergeHeroEntityIds = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.mergeHeroEntityIds);
            return copied;
        }
    }
}

