// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendSysNearbyReq : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<string> vecOpenID;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(vecOpenID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            vecOpenID = (System.Collections.Generic.List<string>) _is.Read(vecOpenID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(vecOpenID, "vecOpenID");
        }

        public override void Clear()
        {
            vecOpenID = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendSysNearbyReq();
            copied.vecOpenID = (System.Collections.Generic.List<string>)JceUtil.DeepClone(this.vecOpenID);
            return copied;
        }
    }
}

