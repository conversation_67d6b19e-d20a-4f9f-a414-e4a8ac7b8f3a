//所在的Excel 【ACG_Camera.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Camera_Client : Wup.Jce.JceStruct
    {
        public int iConfigID = 0;

        public string sConfigName = "";

        public int iVersion = 0;

        public float fFOV = 0;

        public float fPitch = 0;

        public float fZoomSpeed = 0;

        public float fPanSpeed = 0;

        public float fZoomInDistance = 0;

        public float fZoomOutDistance = 0;

        public float fZoomSmoothTime = 0;

        public float fPanSmoothTime = 0;

        public float fReboundTime = 0;

        public float fReboundZoomFactor = 0;

        public float fReboundPanFactorX = 0;

        public float fReboundPanFactorY = 0;

        public float fMaxOffsetX = 0;

        public float fMaxOffsetY = 0;

        public float fDistanceToCenter = 0;

        public float fCenterOffsetX = 0;

        public float fCenterOffsetZ = 0;

        public float fResetPanOffsetX = 0;

        public float fResetPanOffsetY = 0;

        public float fAspectRatio = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iConfigID, 0);
            _os.Write(sConfigName, 1);
            _os.Write(iVersion, 2);
            _os.Write(fFOV, 3);
            _os.Write(fPitch, 4);
            _os.Write(fZoomSpeed, 5);
            _os.Write(fPanSpeed, 6);
            _os.Write(fZoomInDistance, 7);
            _os.Write(fZoomOutDistance, 8);
            _os.Write(fZoomSmoothTime, 9);
            _os.Write(fPanSmoothTime, 10);
            _os.Write(fReboundTime, 11);
            _os.Write(fReboundZoomFactor, 12);
            _os.Write(fReboundPanFactorX, 13);
            _os.Write(fReboundPanFactorY, 14);
            _os.Write(fMaxOffsetX, 15);
            _os.Write(fMaxOffsetY, 16);
            _os.Write(fDistanceToCenter, 17);
            _os.Write(fCenterOffsetX, 18);
            _os.Write(fCenterOffsetZ, 19);
            _os.Write(fResetPanOffsetX, 20);
            _os.Write(fResetPanOffsetY, 21);
            _os.Write(fAspectRatio, 22);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iConfigID = (int) _is.Read(iConfigID, 0, false);

            sConfigName = (string) _is.Read(sConfigName, 1, false);

            iVersion = (int) _is.Read(iVersion, 2, false);

            fFOV = (float) _is.Read(fFOV, 3, false);

            fPitch = (float) _is.Read(fPitch, 4, false);

            fZoomSpeed = (float) _is.Read(fZoomSpeed, 5, false);

            fPanSpeed = (float) _is.Read(fPanSpeed, 6, false);

            fZoomInDistance = (float) _is.Read(fZoomInDistance, 7, false);

            fZoomOutDistance = (float) _is.Read(fZoomOutDistance, 8, false);

            fZoomSmoothTime = (float) _is.Read(fZoomSmoothTime, 9, false);

            fPanSmoothTime = (float) _is.Read(fPanSmoothTime, 10, false);

            fReboundTime = (float) _is.Read(fReboundTime, 11, false);

            fReboundZoomFactor = (float) _is.Read(fReboundZoomFactor, 12, false);

            fReboundPanFactorX = (float) _is.Read(fReboundPanFactorX, 13, false);

            fReboundPanFactorY = (float) _is.Read(fReboundPanFactorY, 14, false);

            fMaxOffsetX = (float) _is.Read(fMaxOffsetX, 15, false);

            fMaxOffsetY = (float) _is.Read(fMaxOffsetY, 16, false);

            fDistanceToCenter = (float) _is.Read(fDistanceToCenter, 17, false);

            fCenterOffsetX = (float) _is.Read(fCenterOffsetX, 18, false);

            fCenterOffsetZ = (float) _is.Read(fCenterOffsetZ, 19, false);

            fResetPanOffsetX = (float) _is.Read(fResetPanOffsetX, 20, false);

            fResetPanOffsetY = (float) _is.Read(fResetPanOffsetY, 21, false);

            fAspectRatio = (float) _is.Read(fAspectRatio, 22, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iConfigID, "iConfigID");
            _ds.Display(sConfigName, "sConfigName");
            _ds.Display(iVersion, "iVersion");
            _ds.Display(fFOV, "fFOV");
            _ds.Display(fPitch, "fPitch");
            _ds.Display(fZoomSpeed, "fZoomSpeed");
            _ds.Display(fPanSpeed, "fPanSpeed");
            _ds.Display(fZoomInDistance, "fZoomInDistance");
            _ds.Display(fZoomOutDistance, "fZoomOutDistance");
            _ds.Display(fZoomSmoothTime, "fZoomSmoothTime");
            _ds.Display(fPanSmoothTime, "fPanSmoothTime");
            _ds.Display(fReboundTime, "fReboundTime");
            _ds.Display(fReboundZoomFactor, "fReboundZoomFactor");
            _ds.Display(fReboundPanFactorX, "fReboundPanFactorX");
            _ds.Display(fReboundPanFactorY, "fReboundPanFactorY");
            _ds.Display(fMaxOffsetX, "fMaxOffsetX");
            _ds.Display(fMaxOffsetY, "fMaxOffsetY");
            _ds.Display(fDistanceToCenter, "fDistanceToCenter");
            _ds.Display(fCenterOffsetX, "fCenterOffsetX");
            _ds.Display(fCenterOffsetZ, "fCenterOffsetZ");
            _ds.Display(fResetPanOffsetX, "fResetPanOffsetX");
            _ds.Display(fResetPanOffsetY, "fResetPanOffsetY");
            _ds.Display(fAspectRatio, "fAspectRatio");
        }

        public override void Clear()
        {
            iConfigID = 0;
            sConfigName = "";
            iVersion = 0;
            fFOV = 0;
            fPitch = 0;
            fZoomSpeed = 0;
            fPanSpeed = 0;
            fZoomInDistance = 0;
            fZoomOutDistance = 0;
            fZoomSmoothTime = 0;
            fPanSmoothTime = 0;
            fReboundTime = 0;
            fReboundZoomFactor = 0;
            fReboundPanFactorX = 0;
            fReboundPanFactorY = 0;
            fMaxOffsetX = 0;
            fMaxOffsetY = 0;
            fDistanceToCenter = 0;
            fCenterOffsetX = 0;
            fCenterOffsetZ = 0;
            fResetPanOffsetX = 0;
            fResetPanOffsetY = 0;
            fAspectRatio = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Camera_Client();
            copied.iConfigID = this.iConfigID;
            copied.sConfigName = this.sConfigName;
            copied.iVersion = this.iVersion;
            copied.fFOV = this.fFOV;
            copied.fPitch = this.fPitch;
            copied.fZoomSpeed = this.fZoomSpeed;
            copied.fPanSpeed = this.fPanSpeed;
            copied.fZoomInDistance = this.fZoomInDistance;
            copied.fZoomOutDistance = this.fZoomOutDistance;
            copied.fZoomSmoothTime = this.fZoomSmoothTime;
            copied.fPanSmoothTime = this.fPanSmoothTime;
            copied.fReboundTime = this.fReboundTime;
            copied.fReboundZoomFactor = this.fReboundZoomFactor;
            copied.fReboundPanFactorX = this.fReboundPanFactorX;
            copied.fReboundPanFactorY = this.fReboundPanFactorY;
            copied.fMaxOffsetX = this.fMaxOffsetX;
            copied.fMaxOffsetY = this.fMaxOffsetY;
            copied.fDistanceToCenter = this.fDistanceToCenter;
            copied.fCenterOffsetX = this.fCenterOffsetX;
            copied.fCenterOffsetZ = this.fCenterOffsetZ;
            copied.fResetPanOffsetX = this.fResetPanOffsetX;
            copied.fResetPanOffsetY = this.fResetPanOffsetY;
            copied.fAspectRatio = this.fAspectRatio;
            return copied;
        }
    }
}

