// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAllGameCfgClientExt3 : Wup.Jce.JceStruct
    {
        public UniqueInfo.ConfigHashMap<int, TACGFetters_Client> mapACGFetters_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Hero_Client> mapACG_Hero_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_SpecName_Client> mapACG_SpecName_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_ClassName_Client> mapACG_ClassName_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Summon_Client> mapACG_Summon_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_BattleMergeHero_Client> mapACG_BattleMergeHero_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Glutton_Client> mapACG_Glutton_Client;

        public UniqueInfo.ConfigHashMap<int, TACGBuffProto_Client> mapACGBuffProto_Client;

        public UniqueInfo.ConfigHashMap<int, TACGBuff_Client> mapACGBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACGDebuffGroup_Client> mapACGDebuffGroup_Client;

        public UniqueInfo.ConfigHashMap<int, TACGHeavenBuff_Client> mapACGHeavenBuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_CombatEffect_Client> mapACG_CombatEffect_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Equipment_Client> mapACG_Equipment_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_EquipmentRandom_Client> mapACG_EquipmentRandom_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Attribute_Client> mapACG_Attribute_Client;

        public UniqueInfo.ConfigHashMap<int, TACGSkill_Client> mapACGSkill_Client;

        public UniqueInfo.ConfigHashMap<int, TACGSkillDurationTarget_Client> mapACGSkillDurationTarget_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HABuff_Client> mapACG_HABuff_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_HAHeroBasicConfig_Client> mapACG_HAHeroBasicConfig_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_Galaxy_Client> mapACG_Galaxy_Client;

        public UniqueInfo.ConfigHashMap<int, TACG_GalaxyViewConfig_Client> mapACG_GalaxyViewConfig_Client;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapACGFetters_Client, 0);
            _os.Write(mapACG_Hero_Client, 1);
            _os.Write(mapACG_SpecName_Client, 2);
            _os.Write(mapACG_ClassName_Client, 3);
            _os.Write(mapACG_Summon_Client, 4);
            _os.Write(mapACG_BattleMergeHero_Client, 5);
            _os.Write(mapACG_Glutton_Client, 6);
            _os.Write(mapACGBuffProto_Client, 7);
            _os.Write(mapACGBuff_Client, 8);
            _os.Write(mapACGDebuffGroup_Client, 9);
            _os.Write(mapACGHeavenBuff_Client, 10);
            _os.Write(mapACG_CombatEffect_Client, 11);
            _os.Write(mapACG_Equipment_Client, 12);
            _os.Write(mapACG_EquipmentRandom_Client, 13);
            _os.Write(mapACG_Attribute_Client, 14);
            _os.Write(mapACGSkill_Client, 15);
            _os.Write(mapACGSkillDurationTarget_Client, 16);
            _os.Write(mapACG_HABuff_Client, 17);
            _os.Write(mapACG_HAHeroBasicConfig_Client, 18);
            _os.Write(mapACG_Galaxy_Client, 19);
            _os.Write(mapACG_GalaxyViewConfig_Client, 20);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapACGFetters_Client = (UniqueInfo.ConfigHashMap<int, TACGFetters_Client>) _is.Read(mapACGFetters_Client, 0, false);

            mapACG_Hero_Client = (UniqueInfo.ConfigHashMap<int, TACG_Hero_Client>) _is.Read(mapACG_Hero_Client, 1, false);

            mapACG_SpecName_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpecName_Client>) _is.Read(mapACG_SpecName_Client, 2, false);

            mapACG_ClassName_Client = (UniqueInfo.ConfigHashMap<int, TACG_ClassName_Client>) _is.Read(mapACG_ClassName_Client, 3, false);

            mapACG_Summon_Client = (UniqueInfo.ConfigHashMap<int, TACG_Summon_Client>) _is.Read(mapACG_Summon_Client, 4, false);

            mapACG_BattleMergeHero_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleMergeHero_Client>) _is.Read(mapACG_BattleMergeHero_Client, 5, false);

            mapACG_Glutton_Client = (UniqueInfo.ConfigHashMap<int, TACG_Glutton_Client>) _is.Read(mapACG_Glutton_Client, 6, false);

            mapACGBuffProto_Client = (UniqueInfo.ConfigHashMap<int, TACGBuffProto_Client>) _is.Read(mapACGBuffProto_Client, 7, false);

            mapACGBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGBuff_Client>) _is.Read(mapACGBuff_Client, 8, false);

            mapACGDebuffGroup_Client = (UniqueInfo.ConfigHashMap<int, TACGDebuffGroup_Client>) _is.Read(mapACGDebuffGroup_Client, 9, false);

            mapACGHeavenBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGHeavenBuff_Client>) _is.Read(mapACGHeavenBuff_Client, 10, false);

            mapACG_CombatEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_CombatEffect_Client>) _is.Read(mapACG_CombatEffect_Client, 11, false);

            mapACG_Equipment_Client = (UniqueInfo.ConfigHashMap<int, TACG_Equipment_Client>) _is.Read(mapACG_Equipment_Client, 12, false);

            mapACG_EquipmentRandom_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentRandom_Client>) _is.Read(mapACG_EquipmentRandom_Client, 13, false);

            mapACG_Attribute_Client = (UniqueInfo.ConfigHashMap<int, TACG_Attribute_Client>) _is.Read(mapACG_Attribute_Client, 14, false);

            mapACGSkill_Client = (UniqueInfo.ConfigHashMap<int, TACGSkill_Client>) _is.Read(mapACGSkill_Client, 15, false);

            mapACGSkillDurationTarget_Client = (UniqueInfo.ConfigHashMap<int, TACGSkillDurationTarget_Client>) _is.Read(mapACGSkillDurationTarget_Client, 16, false);

            mapACG_HABuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABuff_Client>) _is.Read(mapACG_HABuff_Client, 17, false);

            mapACG_HAHeroBasicConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HAHeroBasicConfig_Client>) _is.Read(mapACG_HAHeroBasicConfig_Client, 18, false);

            mapACG_Galaxy_Client = (UniqueInfo.ConfigHashMap<int, TACG_Galaxy_Client>) _is.Read(mapACG_Galaxy_Client, 19, false);

            mapACG_GalaxyViewConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GalaxyViewConfig_Client>) _is.Read(mapACG_GalaxyViewConfig_Client, 20, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapACGFetters_Client, "mapACGFetters_Client");
            _ds.Display(mapACG_Hero_Client, "mapACG_Hero_Client");
            _ds.Display(mapACG_SpecName_Client, "mapACG_SpecName_Client");
            _ds.Display(mapACG_ClassName_Client, "mapACG_ClassName_Client");
            _ds.Display(mapACG_Summon_Client, "mapACG_Summon_Client");
            _ds.Display(mapACG_BattleMergeHero_Client, "mapACG_BattleMergeHero_Client");
            _ds.Display(mapACG_Glutton_Client, "mapACG_Glutton_Client");
            _ds.Display(mapACGBuffProto_Client, "mapACGBuffProto_Client");
            _ds.Display(mapACGBuff_Client, "mapACGBuff_Client");
            _ds.Display(mapACGDebuffGroup_Client, "mapACGDebuffGroup_Client");
            _ds.Display(mapACGHeavenBuff_Client, "mapACGHeavenBuff_Client");
            _ds.Display(mapACG_CombatEffect_Client, "mapACG_CombatEffect_Client");
            _ds.Display(mapACG_Equipment_Client, "mapACG_Equipment_Client");
            _ds.Display(mapACG_EquipmentRandom_Client, "mapACG_EquipmentRandom_Client");
            _ds.Display(mapACG_Attribute_Client, "mapACG_Attribute_Client");
            _ds.Display(mapACGSkill_Client, "mapACGSkill_Client");
            _ds.Display(mapACGSkillDurationTarget_Client, "mapACGSkillDurationTarget_Client");
            _ds.Display(mapACG_HABuff_Client, "mapACG_HABuff_Client");
            _ds.Display(mapACG_HAHeroBasicConfig_Client, "mapACG_HAHeroBasicConfig_Client");
            _ds.Display(mapACG_Galaxy_Client, "mapACG_Galaxy_Client");
            _ds.Display(mapACG_GalaxyViewConfig_Client, "mapACG_GalaxyViewConfig_Client");
        }

        public override void Clear()
        {
            if (mapACGFetters_Client != null) mapACGFetters_Client.Clear();
            if (mapACG_Hero_Client != null) mapACG_Hero_Client.Clear();
            if (mapACG_SpecName_Client != null) mapACG_SpecName_Client.Clear();
            if (mapACG_ClassName_Client != null) mapACG_ClassName_Client.Clear();
            if (mapACG_Summon_Client != null) mapACG_Summon_Client.Clear();
            if (mapACG_BattleMergeHero_Client != null) mapACG_BattleMergeHero_Client.Clear();
            if (mapACG_Glutton_Client != null) mapACG_Glutton_Client.Clear();
            if (mapACGBuffProto_Client != null) mapACGBuffProto_Client.Clear();
            if (mapACGBuff_Client != null) mapACGBuff_Client.Clear();
            if (mapACGDebuffGroup_Client != null) mapACGDebuffGroup_Client.Clear();
            if (mapACGHeavenBuff_Client != null) mapACGHeavenBuff_Client.Clear();
            if (mapACG_CombatEffect_Client != null) mapACG_CombatEffect_Client.Clear();
            if (mapACG_Equipment_Client != null) mapACG_Equipment_Client.Clear();
            if (mapACG_EquipmentRandom_Client != null) mapACG_EquipmentRandom_Client.Clear();
            if (mapACG_Attribute_Client != null) mapACG_Attribute_Client.Clear();
            if (mapACGSkill_Client != null) mapACGSkill_Client.Clear();
            if (mapACGSkillDurationTarget_Client != null) mapACGSkillDurationTarget_Client.Clear();
            if (mapACG_HABuff_Client != null) mapACG_HABuff_Client.Clear();
            if (mapACG_HAHeroBasicConfig_Client != null) mapACG_HAHeroBasicConfig_Client.Clear();
            if (mapACG_Galaxy_Client != null) mapACG_Galaxy_Client.Clear();
            if (mapACG_GalaxyViewConfig_Client != null) mapACG_GalaxyViewConfig_Client.Clear();
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAllGameCfgClientExt3();
            copied.mapACGFetters_Client = (UniqueInfo.ConfigHashMap<int, TACGFetters_Client>)JceUtil.DeepClone(this.mapACGFetters_Client);
            copied.mapACG_Hero_Client = (UniqueInfo.ConfigHashMap<int, TACG_Hero_Client>)JceUtil.DeepClone(this.mapACG_Hero_Client);
            copied.mapACG_SpecName_Client = (UniqueInfo.ConfigHashMap<int, TACG_SpecName_Client>)JceUtil.DeepClone(this.mapACG_SpecName_Client);
            copied.mapACG_ClassName_Client = (UniqueInfo.ConfigHashMap<int, TACG_ClassName_Client>)JceUtil.DeepClone(this.mapACG_ClassName_Client);
            copied.mapACG_Summon_Client = (UniqueInfo.ConfigHashMap<int, TACG_Summon_Client>)JceUtil.DeepClone(this.mapACG_Summon_Client);
            copied.mapACG_BattleMergeHero_Client = (UniqueInfo.ConfigHashMap<int, TACG_BattleMergeHero_Client>)JceUtil.DeepClone(this.mapACG_BattleMergeHero_Client);
            copied.mapACG_Glutton_Client = (UniqueInfo.ConfigHashMap<int, TACG_Glutton_Client>)JceUtil.DeepClone(this.mapACG_Glutton_Client);
            copied.mapACGBuffProto_Client = (UniqueInfo.ConfigHashMap<int, TACGBuffProto_Client>)JceUtil.DeepClone(this.mapACGBuffProto_Client);
            copied.mapACGBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGBuff_Client>)JceUtil.DeepClone(this.mapACGBuff_Client);
            copied.mapACGDebuffGroup_Client = (UniqueInfo.ConfigHashMap<int, TACGDebuffGroup_Client>)JceUtil.DeepClone(this.mapACGDebuffGroup_Client);
            copied.mapACGHeavenBuff_Client = (UniqueInfo.ConfigHashMap<int, TACGHeavenBuff_Client>)JceUtil.DeepClone(this.mapACGHeavenBuff_Client);
            copied.mapACG_CombatEffect_Client = (UniqueInfo.ConfigHashMap<int, TACG_CombatEffect_Client>)JceUtil.DeepClone(this.mapACG_CombatEffect_Client);
            copied.mapACG_Equipment_Client = (UniqueInfo.ConfigHashMap<int, TACG_Equipment_Client>)JceUtil.DeepClone(this.mapACG_Equipment_Client);
            copied.mapACG_EquipmentRandom_Client = (UniqueInfo.ConfigHashMap<int, TACG_EquipmentRandom_Client>)JceUtil.DeepClone(this.mapACG_EquipmentRandom_Client);
            copied.mapACG_Attribute_Client = (UniqueInfo.ConfigHashMap<int, TACG_Attribute_Client>)JceUtil.DeepClone(this.mapACG_Attribute_Client);
            copied.mapACGSkill_Client = (UniqueInfo.ConfigHashMap<int, TACGSkill_Client>)JceUtil.DeepClone(this.mapACGSkill_Client);
            copied.mapACGSkillDurationTarget_Client = (UniqueInfo.ConfigHashMap<int, TACGSkillDurationTarget_Client>)JceUtil.DeepClone(this.mapACGSkillDurationTarget_Client);
            copied.mapACG_HABuff_Client = (UniqueInfo.ConfigHashMap<int, TACG_HABuff_Client>)JceUtil.DeepClone(this.mapACG_HABuff_Client);
            copied.mapACG_HAHeroBasicConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_HAHeroBasicConfig_Client>)JceUtil.DeepClone(this.mapACG_HAHeroBasicConfig_Client);
            copied.mapACG_Galaxy_Client = (UniqueInfo.ConfigHashMap<int, TACG_Galaxy_Client>)JceUtil.DeepClone(this.mapACG_Galaxy_Client);
            copied.mapACG_GalaxyViewConfig_Client = (UniqueInfo.ConfigHashMap<int, TACG_GalaxyViewConfig_Client>)JceUtil.DeepClone(this.mapACG_GalaxyViewConfig_Client);
            return copied;
        }
    }
}

