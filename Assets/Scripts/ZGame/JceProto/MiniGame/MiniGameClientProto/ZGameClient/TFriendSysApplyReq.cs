// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendSysApplyReq : Wup.Jce.JceStruct
    {
        public TUserID target;

        public int iFromChannel = 0;

        public string ext1 = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(target, 0);
            _os.Write(iFromChannel, 1);
            _os.Write(ext1, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            target = (TUserID) _is.Read(target, 0, false);

            iFromChannel = (int) _is.Read(iFromChannel, 1, false);

            ext1 = (string) _is.Read(ext1, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(target, "target");
            _ds.Display(iFromChannel, "iFromChannel");
            _ds.Display(ext1, "ext1");
        }

        public override void Clear()
        {
            target = null;
            iFromChannel = 0;
            ext1 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendSysApplyReq();
            copied.target = (TUserID)JceUtil.DeepClone(this.target);
            copied.iFromChannel = this.iFromChannel;
            copied.ext1 = this.ext1;
            return copied;
        }
    }
}

