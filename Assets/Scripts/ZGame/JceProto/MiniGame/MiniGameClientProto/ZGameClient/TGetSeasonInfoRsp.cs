// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetSeasonInfoRsp : Wup.Jce.JceStruct
    {
        public short nResultID = 0;

        public TSeasonInfo stSeasonInfo;

        public TSeasonInfo stNextSeasonInfo;

        public TKFrame.TKDictionary<int, TSeasonInfo> mapTimeLimitSeason;

        public TSeasonInfo stPreSeasonInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(stSeasonInfo, 1);
            _os.Write(stNextSeasonInfo, 2);
            _os.Write(mapTimeLimitSeason, 3);
            _os.Write(stPreSeasonInfo, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (short) _is.Read(nResultID, 0, false);

            stSeasonInfo = (TSeasonInfo) _is.Read(stSeasonInfo, 1, false);

            stNextSeasonInfo = (TSeasonInfo) _is.Read(stNextSeasonInfo, 2, false);

            mapTimeLimitSeason = (TKFrame.TKDictionary<int, TSeasonInfo>) _is.Read(mapTimeLimitSeason, 3, false);

            stPreSeasonInfo = (TSeasonInfo) _is.Read(stPreSeasonInfo, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(stSeasonInfo, "stSeasonInfo");
            _ds.Display(stNextSeasonInfo, "stNextSeasonInfo");
            _ds.Display(mapTimeLimitSeason, "mapTimeLimitSeason");
            _ds.Display(stPreSeasonInfo, "stPreSeasonInfo");
        }

        public override void Clear()
        {
            nResultID = 0;
            stSeasonInfo = null;
            stNextSeasonInfo = null;
            mapTimeLimitSeason = null;
            stPreSeasonInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetSeasonInfoRsp();
            copied.nResultID = this.nResultID;
            copied.stSeasonInfo = (TSeasonInfo)JceUtil.DeepClone(this.stSeasonInfo);
            copied.stNextSeasonInfo = (TSeasonInfo)JceUtil.DeepClone(this.stNextSeasonInfo);
            copied.mapTimeLimitSeason = (TKFrame.TKDictionary<int, TSeasonInfo>)JceUtil.DeepClone(this.mapTimeLimitSeason);
            copied.stPreSeasonInfo = (TSeasonInfo)JceUtil.DeepClone(this.stPreSeasonInfo);
            return copied;
        }
    }
}

