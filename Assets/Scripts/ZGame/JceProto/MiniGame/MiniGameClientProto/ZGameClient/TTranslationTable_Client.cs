//所在的Excel 【AATranslation.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTranslationTable_Client : Wup.Jce.JceStruct
    {
        public int iIndexID = 0;

        public string sKey = "";

        public string sChn = "";

        public string sEnglish = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndexID, 0);
            _os.Write(sKey, 1);
            _os.Write(sChn, 2);
            _os.Write(sEnglish, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndexID = (int) _is.Read(iIndexID, 0, false);

            sKey = (string) _is.Read(sKey, 1, false);

            sChn = (string) _is.Read(sChn, 2, false);

            sEnglish = (string) _is.Read(sEnglish, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndexID, "iIndexID");
            _ds.Display(sKey, "sKey");
            _ds.Display(sChn, "sChn");
            _ds.Display(sEnglish, "sEnglish");
        }

        public override void Clear()
        {
            iIndexID = 0;
            sKey = "";
            sChn = "";
            sEnglish = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTranslationTable_Client();
            copied.iIndexID = this.iIndexID;
            copied.sKey = this.sKey;
            copied.sChn = this.sChn;
            copied.sEnglish = this.sEnglish;
            return copied;
        }
    }
}

