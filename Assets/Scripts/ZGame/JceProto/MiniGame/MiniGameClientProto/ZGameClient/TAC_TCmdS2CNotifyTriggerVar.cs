// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyTriggerVar : Wup.Jce.JceStruct
    {
        public int i8ChairID = 0;

        public int iVarIndex = 0;

        public int iVarValue = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iVarIndex, 1);
            _os.Write(iVarValue, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iVarIndex = (int) _is.Read(iVarIndex, 1, false);

            iVarValue = (int) _is.Read(iVarValue, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iVarIndex, "iVarIndex");
            _ds.Display(iVarValue, "iVarValue");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iVarIndex = 0;
            iVarValue = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyTriggerVar();
            copied.i8ChairID = this.i8ChairID;
            copied.iVarIndex = this.iVarIndex;
            copied.iVarValue = this.iVarValue;
            return copied;
        }
    }
}

