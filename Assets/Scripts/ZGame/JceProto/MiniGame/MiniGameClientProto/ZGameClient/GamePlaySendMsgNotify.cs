// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GamePlaySendMsgNotify : Wup.Jce.JceStruct
    {
        public string content = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(content, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            content = (string) _is.Read(content, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(content, "content");
        }

        public override void Clear()
        {
            content = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GamePlaySendMsgNotify();
            copied.content = this.content;
            return copied;
        }
    }
}

