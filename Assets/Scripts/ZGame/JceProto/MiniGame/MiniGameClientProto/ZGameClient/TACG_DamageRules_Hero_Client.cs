//所在的Excel 【ACG_DamageRules.xlsm】
//***************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_DamageRules_Hero_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sID = "";

        public int iHeroType = 0;

        public int iStar = 0;

        public int iQuality = 0;

        public int iDamageNum = 0;

        public int iDamageRate = 0;

        public int iDamageRuleType = 0;

        public string sDamageStr = "";

        public int iDamageRule = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sID, 1);
            _os.Write(iHeroType, 2);
            _os.Write(iStar, 3);
            _os.Write(iQuality, 4);
            _os.Write(iDamageNum, 5);
            _os.Write(iDamageRate, 6);
            _os.Write(iDamageRuleType, 9);
            _os.Write(sDamageStr, 10);
            _os.Write(iDamageRule, 11);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sID = (string) _is.Read(sID, 1, false);

            iHeroType = (int) _is.Read(iHeroType, 2, false);

            iStar = (int) _is.Read(iStar, 3, false);

            iQuality = (int) _is.Read(iQuality, 4, false);

            iDamageNum = (int) _is.Read(iDamageNum, 5, false);

            iDamageRate = (int) _is.Read(iDamageRate, 6, false);

            iDamageRuleType = (int) _is.Read(iDamageRuleType, 9, false);

            sDamageStr = (string) _is.Read(sDamageStr, 10, false);

            iDamageRule = (int) _is.Read(iDamageRule, 11, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sID, "sID");
            _ds.Display(iHeroType, "iHeroType");
            _ds.Display(iStar, "iStar");
            _ds.Display(iQuality, "iQuality");
            _ds.Display(iDamageNum, "iDamageNum");
            _ds.Display(iDamageRate, "iDamageRate");
            _ds.Display(iDamageRuleType, "iDamageRuleType");
            _ds.Display(sDamageStr, "sDamageStr");
            _ds.Display(iDamageRule, "iDamageRule");
        }

        public override void Clear()
        {
            iID = 0;
            sID = "";
            iHeroType = 0;
            iStar = 0;
            iQuality = 0;
            iDamageNum = 0;
            iDamageRate = 0;
            iDamageRuleType = 0;
            sDamageStr = "";
            iDamageRule = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_DamageRules_Hero_Client();
            copied.iID = this.iID;
            copied.sID = this.sID;
            copied.iHeroType = this.iHeroType;
            copied.iStar = this.iStar;
            copied.iQuality = this.iQuality;
            copied.iDamageNum = this.iDamageNum;
            copied.iDamageRate = this.iDamageRate;
            copied.iDamageRuleType = this.iDamageRuleType;
            copied.sDamageStr = this.sDamageStr;
            copied.iDamageRule = this.iDamageRule;
            return copied;
        }
    }
}

