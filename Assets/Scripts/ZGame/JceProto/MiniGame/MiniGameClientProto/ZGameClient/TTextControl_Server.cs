// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTextControl_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sText = "";

        public string sStartTime = "";

        public int iStartTime = 0;

        public string sOverTime = "";

        public int iOverTime = 0;

        public int iTextWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sText, 1);
            _os.Write(sStartTime, 2);
            _os.Write(iStartTime, 3);
            _os.Write(sOverTime, 4);
            _os.Write(iOverTime, 5);
            _os.Write(iTextWeight, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sText = (string) _is.Read(sText, 1, false);

            sStartTime = (string) _is.Read(sStartTime, 2, false);

            iStartTime = (int) _is.Read(iStartTime, 3, false);

            sOverTime = (string) _is.Read(sOverTime, 4, false);

            iOverTime = (int) _is.Read(iOverTime, 5, false);

            iTextWeight = (int) _is.Read(iTextWeight, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sText, "sText");
            _ds.Display(sStartTime, "sStartTime");
            _ds.Display(iStartTime, "iStartTime");
            _ds.Display(sOverTime, "sOverTime");
            _ds.Display(iOverTime, "iOverTime");
            _ds.Display(iTextWeight, "iTextWeight");
        }

        public override void Clear()
        {
            iID = 0;
            sText = "";
            sStartTime = "";
            iStartTime = 0;
            sOverTime = "";
            iOverTime = 0;
            iTextWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTextControl_Server();
            copied.iID = this.iID;
            copied.sText = this.sText;
            copied.sStartTime = this.sStartTime;
            copied.iStartTime = this.iStartTime;
            copied.sOverTime = this.sOverTime;
            copied.iOverTime = this.iOverTime;
            copied.iTextWeight = this.iTextWeight;
            return copied;
        }
    }
}

