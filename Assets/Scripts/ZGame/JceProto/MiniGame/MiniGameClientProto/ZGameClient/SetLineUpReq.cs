// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class SetLineUpReq : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<int, string> lineUpMap;

        public TKFrame.TKDictionary<int, TKFrame.TKDictionary<int, string>> setLineUpMap;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lineUpMap, 0);
            _os.Write(setLineUpMap, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lineUpMap = (TKFrame.TKDictionary<int, string>) _is.Read(lineUpMap, 0, false);

            setLineUpMap = (TKFrame.TKDictionary<int, TKFrame.TKDictionary<int, string>>) _is.Read(setLineUpMap, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lineUpMap, "lineUpMap");
            _ds.Display(setLineUpMap, "setLineUpMap");
        }

        public override void Clear()
        {
            lineUpMap = null;
            setLineUpMap = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new SetLineUpReq();
            copied.lineUpMap = (TKFrame.TKDictionary<int, string>)JceUtil.DeepClone(this.lineUpMap);
            copied.setLineUpMap = (TKFrame.TKDictionary<int, TKFrame.TKDictionary<int, string>>)JceUtil.DeepClone(this.setLineUpMap);
            return copied;
        }
    }
}

