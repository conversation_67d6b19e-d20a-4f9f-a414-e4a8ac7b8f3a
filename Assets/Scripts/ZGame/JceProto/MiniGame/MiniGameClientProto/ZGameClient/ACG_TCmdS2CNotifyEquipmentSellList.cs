// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyEquipmentSellList : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _iPlayerID = 0;
        public int iPlayerID
        {
            get
            {
                 return _iPlayerID;
            }
            set
            {
                _iPlayerID = value; 
            }
        }

        public TKFrame.TKDictionary<int, EquipmentSellItem> mapSellList {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iPlayerID, 1);
            _os.Write(mapSellList, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iPlayerID = (int) _is.Read(iPlayerID, 1, false);

            mapSellList = (TKFrame.TKDictionary<int, EquipmentSellItem>) _is.Read(mapSellList, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iPlayerID, "iPlayerID");
            _ds.Display(mapSellList, "mapSellList");
        }

        public override void Clear()
        {
            iRet = 0;
            iPlayerID = 0;
            mapSellList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyEquipmentSellList();
            copied.iRet = this.iRet;
            copied.iPlayerID = this.iPlayerID;
            copied.mapSellList = (TKFrame.TKDictionary<int, EquipmentSellItem>)JceUtil.DeepClone(this.mapSellList);
            return copied;
        }
    }
}

