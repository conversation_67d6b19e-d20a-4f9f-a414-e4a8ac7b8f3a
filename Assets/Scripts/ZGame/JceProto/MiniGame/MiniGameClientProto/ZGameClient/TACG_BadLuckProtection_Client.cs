// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_BadLuckProtection_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iPlan = 0;

        public int iLevel = 0;

        public int iQuality = 0;

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iPlan, 1);
            _os.Write(iLevel, 2);
            _os.Write(iQuality, 3);
            _os.Write(iWeight, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iPlan = (int) _is.Read(iPlan, 1, false);

            iLevel = (int) _is.Read(iLevel, 2, false);

            iQuality = (int) _is.Read(iQuality, 3, false);

            iWeight = (int) _is.Read(iWeight, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iPlan, "iPlan");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iQuality, "iQuality");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iPlan = 0;
            iLevel = 0;
            iQuality = 0;
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_BadLuckProtection_Client();
            copied.iID = this.iID;
            copied.iPlan = this.iPlan;
            copied.iLevel = this.iLevel;
            copied.iQuality = this.iQuality;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

