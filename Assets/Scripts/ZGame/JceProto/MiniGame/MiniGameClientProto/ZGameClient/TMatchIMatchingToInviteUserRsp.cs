// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMatchIMatchingToInviteUserRsp : Wup.Jce.JceStruct
    {
        public short nResultID = 0;

        public int iLeftBanSeconds = 0;

        public TPlayerId stInviteJoinPlayerId;

        public TClientForbidInfo stForbidInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(iLeftBanSeconds, 1);
            _os.Write(stInviteJoinPlayerId, 2);
            _os.Write(stForbidInfo, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (short) _is.Read(nResultID, 0, false);

            iLeftBanSeconds = (int) _is.Read(iLeftBanSeconds, 1, false);

            stInviteJoinPlayerId = (TPlayerId) _is.Read(stInviteJoinPlayerId, 2, false);

            stForbidInfo = (TClientForbidInfo) _is.Read(stForbidInfo, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(iLeftBanSeconds, "iLeftBanSeconds");
            _ds.Display(stInviteJoinPlayerId, "stInviteJoinPlayerId");
            _ds.Display(stForbidInfo, "stForbidInfo");
        }

        public override void Clear()
        {
            nResultID = 0;
            iLeftBanSeconds = 0;
            stInviteJoinPlayerId = null;
            stForbidInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMatchIMatchingToInviteUserRsp();
            copied.nResultID = this.nResultID;
            copied.iLeftBanSeconds = this.iLeftBanSeconds;
            copied.stInviteJoinPlayerId = (TPlayerId)JceUtil.DeepClone(this.stInviteJoinPlayerId);
            copied.stForbidInfo = (TClientForbidInfo)JceUtil.DeepClone(this.stForbidInfo);
            return copied;
        }
    }
}

