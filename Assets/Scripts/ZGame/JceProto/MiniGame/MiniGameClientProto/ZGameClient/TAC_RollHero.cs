// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_RollHero : Wup.Jce.JceStruct
    {
        public TAC_BattleGroundHero stBattleGroundHero {get; set;} 

        public TAC_WaitPos stWaitPos {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stBattleGroundHero, 0);
            _os.Write(stWaitPos, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stBattleGroundHero = (TAC_BattleGroundHero) _is.Read(stBattleGroundHero, 0, false);

            stWaitPos = (TAC_WaitPos) _is.Read(stWaitPos, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stBattleGroundHero, "stBattleGroundHero");
            _ds.Display(stWaitPos, "stWaitPos");
        }

        public override void Clear()
        {
            stBattleGroundHero = null;
            stWaitPos = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_RollHero();
            copied.stBattleGroundHero = (TAC_BattleGroundHero)JceUtil.DeepClone(this.stBattleGroundHero);
            copied.stWaitPos = (TAC_WaitPos)JceUtil.DeepClone(this.stWaitPos);
            return copied;
        }
    }
}

