// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class RTVoiceReq : Wup.Jce.JceStruct
    {
        public SendAudio audioData;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(audioData, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            audioData = (SendAudio) _is.Read(audioData, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(audioData, "audioData");
        }

        public override void Clear()
        {
            audioData = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new RTVoiceReq();
            copied.audioData = (SendAudio)JceUtil.DeepClone(this.audioData);
            return copied;
        }
    }
}

