// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TGetNoviceGuideGiftStatusBatchRsp : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        public TKFrame.TKDictionary<int, bool> mGetStatus {get; set;} 

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>> mGiftInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(mGetStatus, 1);
            _os.Write(mGiftInfo, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            mGetStatus = (TKFrame.TKDictionary<int, bool>) _is.Read(mGetStatus, 1, false);

            mGiftInfo = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>) _is.Read(mGiftInfo, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(mGetStatus, "mGetStatus");
            _ds.Display(mGiftInfo, "mGiftInfo");
        }

    }
}

