//所在的Excel 【ACG_HeroVoice.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_TeamLeaderVioce_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public string sTouchSound = "";

        public string sFightSound = "";

        public string sWinSound = "";

        public string sLoseSound = "";

        public string sClick01Sound = "";

        public string sClick02Sound = "";

        public string sClick03Sound = "";

        public string sVictorySound = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sTouchSound, 2);
            _os.Write(sFightSound, 3);
            _os.Write(sWinSound, 4);
            _os.Write(sLoseSound, 5);
            _os.Write(sClick01Sound, 6);
            _os.Write(sClick02Sound, 7);
            _os.Write(sClick03Sound, 8);
            _os.Write(sVictorySound, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sTouchSound = (string) _is.Read(sTouchSound, 2, false);

            sFightSound = (string) _is.Read(sFightSound, 3, false);

            sWinSound = (string) _is.Read(sWinSound, 4, false);

            sLoseSound = (string) _is.Read(sLoseSound, 5, false);

            sClick01Sound = (string) _is.Read(sClick01Sound, 6, false);

            sClick02Sound = (string) _is.Read(sClick02Sound, 7, false);

            sClick03Sound = (string) _is.Read(sClick03Sound, 8, false);

            sVictorySound = (string) _is.Read(sVictorySound, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sTouchSound, "sTouchSound");
            _ds.Display(sFightSound, "sFightSound");
            _ds.Display(sWinSound, "sWinSound");
            _ds.Display(sLoseSound, "sLoseSound");
            _ds.Display(sClick01Sound, "sClick01Sound");
            _ds.Display(sClick02Sound, "sClick02Sound");
            _ds.Display(sClick03Sound, "sClick03Sound");
            _ds.Display(sVictorySound, "sVictorySound");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sTouchSound = "";
            sFightSound = "";
            sWinSound = "";
            sLoseSound = "";
            sClick01Sound = "";
            sClick02Sound = "";
            sClick03Sound = "";
            sVictorySound = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_TeamLeaderVioce_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sTouchSound = this.sTouchSound;
            copied.sFightSound = this.sFightSound;
            copied.sWinSound = this.sWinSound;
            copied.sLoseSound = this.sLoseSound;
            copied.sClick01Sound = this.sClick01Sound;
            copied.sClick02Sound = this.sClick02Sound;
            copied.sClick03Sound = this.sClick03Sound;
            copied.sVictorySound = this.sVictorySound;
            return copied;
        }
    }
}

