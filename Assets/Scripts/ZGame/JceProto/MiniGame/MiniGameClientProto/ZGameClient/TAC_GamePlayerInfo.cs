// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_GamePlayerInfo : Wup.Jce.JceStruct
    {
        int _i8ChairID = 255;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iMoney = 0;
        public int iMoney
        {
            get
            {
                 return _iMoney;
            }
            set
            {
                _iMoney = value; 
            }
        }

        int _iExp = 0;
        public int iExp
        {
            get
            {
                 return _iExp;
            }
            set
            {
                _iExp = value; 
            }
        }

        int _iLife = 0;
        public int iLife
        {
            get
            {
                 return _iLife;
            }
            set
            {
                _iLife = value; 
            }
        }

        int _iLevel = 0;
        public int iLevel
        {
            get
            {
                 return _iLevel;
            }
            set
            {
                _iLevel = value; 
            }
        }

        int _iCurPeople = 0;
        public int iCurPeople
        {
            get
            {
                 return _iCurPeople;
            }
            set
            {
                _iCurPeople = value; 
            }
        }

        int _iMaxPeople = 0;
        public int iMaxPeople
        {
            get
            {
                 return _iMaxPeople;
            }
            set
            {
                _iMaxPeople = value; 
            }
        }

        public System.Collections.Generic.List<TAC_BattleGroundHero> vecBattleGroundHero {get; set;} 

        public System.Collections.Generic.List<TAC_WaitHero> vecWaitHero {get; set;} 

        public System.Collections.Generic.List<TAC_BuyHero> vecBuyHero {get; set;} 

        int _iNextLeveExp = 0;
        public int iNextLeveExp
        {
            get
            {
                 return _iNextLeveExp;
            }
            set
            {
                _iNextLeveExp = value; 
            }
        }

        int _iInitLife = 0;
        public int iInitLife
        {
            get
            {
                 return _iInitLife;
            }
            set
            {
                _iInitLife = value; 
            }
        }

        public System.Collections.Generic.List<TAC_HeroTypeInfo> vecHeroTypeInfo {get; set;} 

        int _iRank = 0;
        public int iRank
        {
            get
            {
                 return _iRank;
            }
            set
            {
                _iRank = value; 
            }
        }

        int _iRefreshStatus = 0;
        public int iRefreshStatus
        {
            get
            {
                 return _iRefreshStatus;
            }
            set
            {
                _iRefreshStatus = value; 
            }
        }

        int _iAutoUpStatus = 0;
        public int iAutoUpStatus
        {
            get
            {
                 return _iAutoUpStatus;
            }
            set
            {
                _iAutoUpStatus = value; 
            }
        }

        int _iWinCount = 0;
        public int iWinCount
        {
            get
            {
                 return _iWinCount;
            }
            set
            {
                _iWinCount = value; 
            }
        }

        int _iLoseCount = 0;
        public int iLoseCount
        {
            get
            {
                 return _iLoseCount;
            }
            set
            {
                _iLoseCount = value; 
            }
        }

        int _iAutoWaitPromotionStatus = 0;
        public int iAutoWaitPromotionStatus
        {
            get
            {
                 return _iAutoWaitPromotionStatus;
            }
            set
            {
                _iAutoWaitPromotionStatus = value; 
            }
        }

        public TAC_AllFetterSkillInfo stAllFetterSkillInfo {get; set;} 

        int _iEnemyPlayerId = -1;
        public int iEnemyPlayerId
        {
            get
            {
                 return _iEnemyPlayerId;
            }
            set
            {
                _iEnemyPlayerId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iMoney, 1);
            _os.Write(iExp, 2);
            _os.Write(iLife, 3);
            _os.Write(iLevel, 4);
            _os.Write(iCurPeople, 5);
            _os.Write(iMaxPeople, 6);
            _os.Write(vecBattleGroundHero, 7);
            _os.Write(vecWaitHero, 8);
            _os.Write(vecBuyHero, 9);
            _os.Write(iNextLeveExp, 10);
            _os.Write(iInitLife, 11);
            _os.Write(vecHeroTypeInfo, 12);
            _os.Write(iRank, 13);
            _os.Write(iRefreshStatus, 14);
            _os.Write(iAutoUpStatus, 15);
            _os.Write(iWinCount, 16);
            _os.Write(iLoseCount, 17);
            _os.Write(iAutoWaitPromotionStatus, 18);
            _os.Write(stAllFetterSkillInfo, 19);
            _os.Write(iEnemyPlayerId, 20);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iMoney = (int) _is.Read(iMoney, 1, false);

            iExp = (int) _is.Read(iExp, 2, false);

            iLife = (int) _is.Read(iLife, 3, false);

            iLevel = (int) _is.Read(iLevel, 4, false);

            iCurPeople = (int) _is.Read(iCurPeople, 5, false);

            iMaxPeople = (int) _is.Read(iMaxPeople, 6, false);

            vecBattleGroundHero = (System.Collections.Generic.List<TAC_BattleGroundHero>) _is.Read(vecBattleGroundHero, 7, false);

            vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecWaitHero, 8, false);

            vecBuyHero = (System.Collections.Generic.List<TAC_BuyHero>) _is.Read(vecBuyHero, 9, false);

            iNextLeveExp = (int) _is.Read(iNextLeveExp, 10, false);

            iInitLife = (int) _is.Read(iInitLife, 11, false);

            vecHeroTypeInfo = (System.Collections.Generic.List<TAC_HeroTypeInfo>) _is.Read(vecHeroTypeInfo, 12, false);

            iRank = (int) _is.Read(iRank, 13, false);

            iRefreshStatus = (int) _is.Read(iRefreshStatus, 14, false);

            iAutoUpStatus = (int) _is.Read(iAutoUpStatus, 15, false);

            iWinCount = (int) _is.Read(iWinCount, 16, false);

            iLoseCount = (int) _is.Read(iLoseCount, 17, false);

            iAutoWaitPromotionStatus = (int) _is.Read(iAutoWaitPromotionStatus, 18, false);

            stAllFetterSkillInfo = (TAC_AllFetterSkillInfo) _is.Read(stAllFetterSkillInfo, 19, false);

            iEnemyPlayerId = (int) _is.Read(iEnemyPlayerId, 20, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iMoney, "iMoney");
            _ds.Display(iExp, "iExp");
            _ds.Display(iLife, "iLife");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iCurPeople, "iCurPeople");
            _ds.Display(iMaxPeople, "iMaxPeople");
            _ds.Display(vecBattleGroundHero, "vecBattleGroundHero");
            _ds.Display(vecWaitHero, "vecWaitHero");
            _ds.Display(vecBuyHero, "vecBuyHero");
            _ds.Display(iNextLeveExp, "iNextLeveExp");
            _ds.Display(iInitLife, "iInitLife");
            _ds.Display(vecHeroTypeInfo, "vecHeroTypeInfo");
            _ds.Display(iRank, "iRank");
            _ds.Display(iRefreshStatus, "iRefreshStatus");
            _ds.Display(iAutoUpStatus, "iAutoUpStatus");
            _ds.Display(iWinCount, "iWinCount");
            _ds.Display(iLoseCount, "iLoseCount");
            _ds.Display(iAutoWaitPromotionStatus, "iAutoWaitPromotionStatus");
            _ds.Display(stAllFetterSkillInfo, "stAllFetterSkillInfo");
            _ds.Display(iEnemyPlayerId, "iEnemyPlayerId");
        }

        public override void Clear()
        {
            i8ChairID = 255;
            iMoney = 0;
            iExp = 0;
            iLife = 0;
            iLevel = 0;
            iCurPeople = 0;
            iMaxPeople = 0;
            vecBattleGroundHero = null;
            vecWaitHero = null;
            vecBuyHero = null;
            iNextLeveExp = 0;
            iInitLife = 0;
            vecHeroTypeInfo = null;
            iRank = 0;
            iRefreshStatus = 0;
            iAutoUpStatus = 0;
            iWinCount = 0;
            iLoseCount = 0;
            iAutoWaitPromotionStatus = 0;
            stAllFetterSkillInfo = null;
            iEnemyPlayerId = -1;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_GamePlayerInfo();
            copied.i8ChairID = this.i8ChairID;
            copied.iMoney = this.iMoney;
            copied.iExp = this.iExp;
            copied.iLife = this.iLife;
            copied.iLevel = this.iLevel;
            copied.iCurPeople = this.iCurPeople;
            copied.iMaxPeople = this.iMaxPeople;
            copied.vecBattleGroundHero = (System.Collections.Generic.List<TAC_BattleGroundHero>)JceUtil.DeepClone(this.vecBattleGroundHero);
            copied.vecWaitHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecWaitHero);
            copied.vecBuyHero = (System.Collections.Generic.List<TAC_BuyHero>)JceUtil.DeepClone(this.vecBuyHero);
            copied.iNextLeveExp = this.iNextLeveExp;
            copied.iInitLife = this.iInitLife;
            copied.vecHeroTypeInfo = (System.Collections.Generic.List<TAC_HeroTypeInfo>)JceUtil.DeepClone(this.vecHeroTypeInfo);
            copied.iRank = this.iRank;
            copied.iRefreshStatus = this.iRefreshStatus;
            copied.iAutoUpStatus = this.iAutoUpStatus;
            copied.iWinCount = this.iWinCount;
            copied.iLoseCount = this.iLoseCount;
            copied.iAutoWaitPromotionStatus = this.iAutoWaitPromotionStatus;
            copied.stAllFetterSkillInfo = (TAC_AllFetterSkillInfo)JceUtil.DeepClone(this.stAllFetterSkillInfo);
            copied.iEnemyPlayerId = this.iEnemyPlayerId;
            return copied;
        }
    }
}

