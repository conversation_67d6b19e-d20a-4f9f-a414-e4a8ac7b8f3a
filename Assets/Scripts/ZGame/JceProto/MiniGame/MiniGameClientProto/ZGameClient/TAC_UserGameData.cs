// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_UserGameData : Wup.Jce.JceStruct
    {
        public TABLE_USER_INFO tableUserData {get; set;} 

        public TAC_GamePlayerInfo stPlayerInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(tableUserData, 0);
            _os.Write(stPlayerInfo, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            tableUserData = (TABLE_USER_INFO) _is.Read(tableUserData, 0, false);

            stPlayerInfo = (TAC_GamePlayerInfo) _is.Read(stPlayerInfo, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(tableUserData, "tableUserData");
            _ds.Display(stPlayerInfo, "stPlayerInfo");
        }

        public override void Clear()
        {
            tableUserData = null;
            stPlayerInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_UserGameData();
            copied.tableUserData = (TABLE_USER_INFO)JceUtil.DeepClone(this.tableUserData);
            copied.stPlayerInfo = (TAC_GamePlayerInfo)JceUtil.DeepClone(this.stPlayerInfo);
            return copied;
        }
    }
}

