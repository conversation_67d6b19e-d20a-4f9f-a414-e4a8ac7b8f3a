// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Quest_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iStage = 0;
        public int iStage
        {
            get
            {
                 return _iStage;
            }
            set
            {
                _iStage = value; 
            }
        }

        int _iRound = 0;
        public int iRound
        {
            get
            {
                 return _iRound;
            }
            set
            {
                _iRound = value; 
            }
        }

        int _iRoundType = 0;
        public int iRoundType
        {
            get
            {
                 return _iRoundType;
            }
            set
            {
                _iRoundType = value; 
            }
        }

        int _iReadyTime = 0;
        public int iReadyTime
        {
            get
            {
                 return _iReadyTime;
            }
            set
            {
                _iReadyTime = value; 
            }
        }

        int _iFightTime = 0;
        public int iFightTime
        {
            get
            {
                 return _iFightTime;
            }
            set
            {
                _iFightTime = value; 
            }
        }

        int _iSceneType = 0;
        public int iSceneType
        {
            get
            {
                 return _iSceneType;
            }
            set
            {
                _iSceneType = value; 
            }
        }

        int _iTurnCount = 0;
        public int iTurnCount
        {
            get
            {
                 return _iTurnCount;
            }
            set
            {
                _iTurnCount = value; 
            }
        }

        int _iFightOverTime = 0;
        public int iFightOverTime
        {
            get
            {
                 return _iFightOverTime;
            }
            set
            {
                _iFightOverTime = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iStage, 1);
            _os.Write(iRound, 2);
            _os.Write(iRoundType, 3);
            _os.Write(iReadyTime, 4);
            _os.Write(iFightTime, 5);
            _os.Write(iSceneType, 6);
            _os.Write(iTurnCount, 7);
            _os.Write(iFightOverTime, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iStage = (int) _is.Read(iStage, 1, false);

            iRound = (int) _is.Read(iRound, 2, false);

            iRoundType = (int) _is.Read(iRoundType, 3, false);

            iReadyTime = (int) _is.Read(iReadyTime, 4, false);

            iFightTime = (int) _is.Read(iFightTime, 5, false);

            iSceneType = (int) _is.Read(iSceneType, 6, false);

            iTurnCount = (int) _is.Read(iTurnCount, 7, false);

            iFightOverTime = (int) _is.Read(iFightOverTime, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iStage, "iStage");
            _ds.Display(iRound, "iRound");
            _ds.Display(iRoundType, "iRoundType");
            _ds.Display(iReadyTime, "iReadyTime");
            _ds.Display(iFightTime, "iFightTime");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(iTurnCount, "iTurnCount");
            _ds.Display(iFightOverTime, "iFightOverTime");
        }

    }
}

