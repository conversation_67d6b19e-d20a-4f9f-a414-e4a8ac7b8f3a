// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TDualPlaySwitchTeamReq : Wup.Jce.JceStruct
    {
        int _strPassword = 0;
        public int strPassword
        {
            get
            {
                 return _strPassword;
            }
            set
            {
                _strPassword = value; 
            }
        }

        int _targetSeatId = 0;
        public int targetSeatId
        {
            get
            {
                 return _targetSeatId;
            }
            set
            {
                _targetSeatId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(strPassword, 1);
            _os.Write(targetSeatId, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            strPassword = (int) _is.Read(strPassword, 1, false);

            targetSeatId = (int) _is.Read(targetSeatId, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(strPassword, "strPassword");
            _ds.Display(targetSeatId, "targetSeatId");
        }

        public override void Clear()
        {
            strPassword = 0;
            targetSeatId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TDualPlaySwitchTeamReq();
            copied.strPassword = this.strPassword;
            copied.targetSeatId = this.targetSeatId;
            return copied;
        }
    }
}

