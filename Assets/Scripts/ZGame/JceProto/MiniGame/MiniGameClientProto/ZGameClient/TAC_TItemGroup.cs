// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
using ZGameClient;
namespace ZGameMail
{

    public sealed class TAC_TItemGroup : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<TItemInfo> vecItemInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(vecItemInfo, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            vecItemInfo = (System.Collections.Generic.List<TItemInfo>) _is.Read(vecItemInfo, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(vecItemInfo, "vecItemInfo");
        }

    }
}

