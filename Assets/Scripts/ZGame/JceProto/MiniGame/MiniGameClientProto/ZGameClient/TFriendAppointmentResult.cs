// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendAppointmentResult : Wup.Jce.JceStruct
    {
        public TPlayerId stLauncher;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stLauncher, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stLauncher = (TPlayerId) _is.Read(stLauncher, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stLauncher, "stLauncher");
        }

        public override void Clear()
        {
            stLauncher = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendAppointmentResult();
            copied.stLauncher = (TPlayerId)JceUtil.DeepClone(this.stLauncher);
            return copied;
        }
    }
}

