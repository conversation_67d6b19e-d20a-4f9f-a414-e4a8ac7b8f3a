// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_SceneToPlayerNum_Client : Wup.Jce.JceStruct
    {
        public int iMode = 0;

        public int iPlayerNum = 0;

        public int iPlayerNumInMatchRoom = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iMode, 0);
            _os.Write(iPlayerNum, 1);
            _os.Write(iPlayerNumInMatchRoom, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iMode = (int) _is.Read(iMode, 0, false);

            iPlayerNum = (int) _is.Read(iPlayerNum, 1, false);

            iPlayerNumInMatchRoom = (int) _is.Read(iPlayerNumInMatchRoom, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iMode, "iMode");
            _ds.Display(iPlayerNum, "iPlayerNum");
            _ds.Display(iPlayerNumInMatchRoom, "iPlayerNumInMatchRoom");
        }

        public override void Clear()
        {
            iMode = 0;
            iPlayerNum = 0;
            iPlayerNumInMatchRoom = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_SceneToPlayerNum_Client();
            copied.iMode = this.iMode;
            copied.iPlayerNum = this.iPlayerNum;
            copied.iPlayerNumInMatchRoom = this.iPlayerNumInMatchRoom;
            return copied;
        }
    }
}

