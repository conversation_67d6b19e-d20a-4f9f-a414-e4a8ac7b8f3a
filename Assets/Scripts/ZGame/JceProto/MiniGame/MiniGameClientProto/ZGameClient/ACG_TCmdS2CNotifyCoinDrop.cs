// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyCoinDrop : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _iPlayerID = 0;
        public int iPlayerID
        {
            get
            {
                 return _iPlayerID;
            }
            set
            {
                _iPlayerID = value; 
            }
        }

        public System.Collections.Generic.List<ACG_DropItemCoin> vecAddDropItemCoin {get; set;} 

        public System.Collections.Generic.List<ACG_DropItemCoin> vecDropItemCoin {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iPlayerID, 1);
            _os.Write(vecAddDropItemCoin, 2);
            _os.Write(vecDropItemCoin, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iPlayerID = (int) _is.Read(iPlayerID, 1, false);

            vecAddDropItemCoin = (System.Collections.Generic.List<ACG_DropItemCoin>) _is.Read(vecAddDropItemCoin, 2, false);

            vecDropItemCoin = (System.Collections.Generic.List<ACG_DropItemCoin>) _is.Read(vecDropItemCoin, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iPlayerID, "iPlayerID");
            _ds.Display(vecAddDropItemCoin, "vecAddDropItemCoin");
            _ds.Display(vecDropItemCoin, "vecDropItemCoin");
        }

        public override void Clear()
        {
            iRet = 0;
            iPlayerID = 0;
            vecAddDropItemCoin = null;
            vecDropItemCoin = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyCoinDrop();
            copied.iRet = this.iRet;
            copied.iPlayerID = this.iPlayerID;
            copied.vecAddDropItemCoin = (System.Collections.Generic.List<ACG_DropItemCoin>)JceUtil.DeepClone(this.vecAddDropItemCoin);
            copied.vecDropItemCoin = (System.Collections.Generic.List<ACG_DropItemCoin>)JceUtil.DeepClone(this.vecDropItemCoin);
            return copied;
        }
    }
}

