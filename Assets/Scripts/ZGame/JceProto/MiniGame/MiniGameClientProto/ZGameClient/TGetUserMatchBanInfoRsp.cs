// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetUserMatchBanInfoRsp : Wup.Jce.JceStruct
    {
        int _iResultID = 0;
        public int iResultID
        {
            get
            {
                 return _iResultID;
            }
            set
            {
                _iResultID = value; 
            }
        }

        int _iLeftBanSeconds = 0;
        public int iLeftBanSeconds
        {
            get
            {
                 return _iLeftBanSeconds;
            }
            set
            {
                _iLeftBanSeconds = value; 
            }
        }

        public TClientForbidInfo stForbidInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iResultID, 0);
            _os.Write(iLeftBanSeconds, 1);
            _os.Write(stForbidInfo, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iResultID = (int) _is.Read(iResultID, 0, false);

            iLeftBanSeconds = (int) _is.Read(iLeftBanSeconds, 1, false);

            stForbidInfo = (TClientForbidInfo) _is.Read(stForbidInfo, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iResultID, "iResultID");
            _ds.Display(iLeftBanSeconds, "iLeftBanSeconds");
            _ds.Display(stForbidInfo, "stForbidInfo");
        }

        public override void Clear()
        {
            iResultID = 0;
            iLeftBanSeconds = 0;
            stForbidInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetUserMatchBanInfoRsp();
            copied.iResultID = this.iResultID;
            copied.iLeftBanSeconds = this.iLeftBanSeconds;
            copied.stForbidInfo = (TClientForbidInfo)JceUtil.DeepClone(this.stForbidInfo);
            return copied;
        }
    }
}

