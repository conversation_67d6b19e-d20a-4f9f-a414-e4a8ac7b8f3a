// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetSeasonTaskRewardReq : Wup.Jce.JceStruct
    {
        public int iTaskID = 0;

        public TMidasTokenInfo stMidasToken;

        public int sceneType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTaskID, 0);
            _os.Write(stMidasToken, 1);
            _os.Write(sceneType, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTaskID = (int) _is.Read(iTaskID, 0, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 1, false);

            sceneType = (int) _is.Read(sceneType, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTaskID, "iTaskID");
            _ds.Display(stMidasToken, "stMidasToken");
            _ds.Display(sceneType, "sceneType");
        }

        public override void Clear()
        {
            iTaskID = 0;
            stMidasToken = null;
            sceneType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetSeasonTaskRewardReq();
            copied.iTaskID = this.iTaskID;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            copied.sceneType = this.sceneType;
            return copied;
        }
    }
}

