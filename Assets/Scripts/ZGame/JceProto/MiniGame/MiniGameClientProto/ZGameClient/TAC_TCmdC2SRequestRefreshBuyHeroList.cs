// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdC2SRequestRefreshBuyHeroList : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        string _sClientMsg = "";
        public string sClientMsg
        {
            get
            {
                 return _sClientMsg;
            }
            set
            {
                _sClientMsg = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(sClientMsg, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            sClientMsg = (string) _is.Read(sClientMsg, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(sClientMsg, "sClientMsg");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            sClientMsg = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdC2SRequestRefreshBuyHeroList();
            copied.i8ChairID = this.i8ChairID;
            copied.sClientMsg = this.sClientMsg;
            return copied;
        }
    }
}

