// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TNotifyPlayerLifeInfo : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iLife = 0;
        public int iLife
        {
            get
            {
                 return _iLife;
            }
            set
            {
                _iLife = value; 
            }
        }

        int _iDeductLife = 0;
        public int iDeductLife
        {
            get
            {
                 return _iDeductLife;
            }
            set
            {
                _iDeductLife = value; 
            }
        }

        int _iDeductExtraLife = 0;
        public int iDeductExtraLife
        {
            get
            {
                 return _iDeductExtraLife;
            }
            set
            {
                _iDeductExtraLife = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> mapHeroDeductLife {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(iLife, 1);
            _os.Write(iDeductLife, 2);
            _os.Write(iDeductExtraLife, 3);
            _os.Write(mapHeroDeductLife, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            iLife = (int) _is.Read(iLife, 1, false);

            iDeductLife = (int) _is.Read(iDeductLife, 2, false);

            iDeductExtraLife = (int) _is.Read(iDeductExtraLife, 3, false);

            mapHeroDeductLife = (TKFrame.TKDictionary<int, int>) _is.Read(mapHeroDeductLife, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iLife, "iLife");
            _ds.Display(iDeductLife, "iDeductLife");
            _ds.Display(iDeductExtraLife, "iDeductExtraLife");
            _ds.Display(mapHeroDeductLife, "mapHeroDeductLife");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            iLife = 0;
            iDeductLife = 0;
            iDeductExtraLife = 0;
            mapHeroDeductLife = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TNotifyPlayerLifeInfo();
            copied.i8ChairID = this.i8ChairID;
            copied.iLife = this.iLife;
            copied.iDeductLife = this.iDeductLife;
            copied.iDeductExtraLife = this.iDeductExtraLife;
            copied.mapHeroDeductLife = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapHeroDeductLife);
            return copied;
        }
    }
}

