// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMatchIAgreeUserApplyJoinRsp : Wup.Jce.JceStruct
    {
        public short nResultID = 0;

        public bool bAgreeApply = true;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(nResultID, 0);
            _os.Write(bAgreeApply, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            nResultID = (short) _is.Read(nResultID, 0, false);

            bAgreeApply = (bool) _is.Read(bAgreeApply, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(nResultID, "nResultID");
            _ds.Display(bAgreeApply, "bAgreeApply");
        }

        public override void Clear()
        {
            nResultID = 0;
            bAgreeApply = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMatchIAgreeUserApplyJoinRsp();
            copied.nResultID = this.nResultID;
            copied.bAgreeApply = this.bAgreeApply;
            return copied;
        }
    }
}

