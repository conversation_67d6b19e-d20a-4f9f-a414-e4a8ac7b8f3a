// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClubUseRenameCardReq : Wup.Jce.JceStruct
    {
        public string newClubName = "";

        public TMidasTokenInfo midasToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(newClubName, 0);
            _os.Write(midasToken, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            newClubName = (string) _is.Read(newClubName, 0, false);

            midasToken = (TMidasTokenInfo) _is.Read(midasToken, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(newClubName, "newClubName");
            _ds.Display(midasToken, "midasToken");
        }

        public override void Clear()
        {
            newClubName = "";
            midasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClubUseRenameCardReq();
            copied.newClubName = this.newClubName;
            copied.midasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.midasToken);
            return copied;
        }
    }
}

