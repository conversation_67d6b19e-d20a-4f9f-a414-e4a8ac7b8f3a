// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardCollectGetTaskReq : Wup.Jce.JceStruct
    {
        public int setID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(setID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            setID = (int) _is.Read(setID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(setID, "setID");
        }

        public override void Clear()
        {
            setID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardCollectGetTaskReq();
            copied.setID = this.setID;
            return copied;
        }
    }
}

