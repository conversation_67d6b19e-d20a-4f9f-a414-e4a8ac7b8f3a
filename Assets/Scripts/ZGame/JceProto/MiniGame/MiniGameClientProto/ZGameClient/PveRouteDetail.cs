// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class PveRouteDetail : Wup.Jce.JceStruct
    {
        public TBCRoguelikeGlobal_Server globalConf;

        public string homeHistoryLineup = "";

        public int state = 0;

        public int routeID = 0;

        public System.Collections.Generic.List<PveLevelDetail> levelDetails;

        public int challengingLevelID = 0;

        public System.Collections.Generic.List<TBCRogueLikePVEConfig_Server> pveConf;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(globalConf, 0);
            _os.Write(homeHistoryLineup, 2);
            _os.Write(state, 3);
            _os.Write(routeID, 4);
            _os.Write(levelDetails, 5);
            _os.Write(challengingLevelID, 6);
            _os.Write(pveConf, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            globalConf = (TBCRoguelikeGlobal_Server) _is.Read(globalConf, 0, false);

            homeHistoryLineup = (string) _is.Read(homeHistoryLineup, 2, false);

            state = (int) _is.Read(state, 3, false);

            routeID = (int) _is.Read(routeID, 4, false);

            levelDetails = (System.Collections.Generic.List<PveLevelDetail>) _is.Read(levelDetails, 5, false);

            challengingLevelID = (int) _is.Read(challengingLevelID, 6, false);

            pveConf = (System.Collections.Generic.List<TBCRogueLikePVEConfig_Server>) _is.Read(pveConf, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(globalConf, "globalConf");
            _ds.Display(homeHistoryLineup, "homeHistoryLineup");
            _ds.Display(state, "state");
            _ds.Display(routeID, "routeID");
            _ds.Display(levelDetails, "levelDetails");
            _ds.Display(challengingLevelID, "challengingLevelID");
            _ds.Display(pveConf, "pveConf");
        }

        public override void Clear()
        {
            globalConf = null;
            homeHistoryLineup = "";
            state = 0;
            routeID = 0;
            levelDetails = null;
            challengingLevelID = 0;
            pveConf = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new PveRouteDetail();
            copied.globalConf = (TBCRoguelikeGlobal_Server)JceUtil.DeepClone(this.globalConf);
            copied.homeHistoryLineup = this.homeHistoryLineup;
            copied.state = this.state;
            copied.routeID = this.routeID;
            copied.levelDetails = (System.Collections.Generic.List<PveLevelDetail>)JceUtil.DeepClone(this.levelDetails);
            copied.challengingLevelID = this.challengingLevelID;
            copied.pveConf = (System.Collections.Generic.List<TBCRogueLikePVEConfig_Server>)JceUtil.DeepClone(this.pveConf);
            return copied;
        }
    }
}

