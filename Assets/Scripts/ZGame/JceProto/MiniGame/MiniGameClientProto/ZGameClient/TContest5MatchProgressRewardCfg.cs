// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TContest5MatchProgressRewardCfg : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>> mapRewards {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapRewards, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapRewards = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>) _is.Read(mapRewards, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapRewards, "mapRewards");
        }

        public override void Clear()
        {
            mapRewards = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TContest5MatchProgressRewardCfg();
            copied.mapRewards = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>)JceUtil.DeepClone(this.mapRewards);
            return copied;
        }
    }
}

