// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GetNoviceTaskStatusReq : Wup.Jce.JceStruct
    {
        long _id = 0;
        public long id
        {
            get
            {
                 return _id;
            }
            set
            {
                _id = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(id, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            id = (long) _is.Read(id, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(id, "id");
        }

        public override void Clear()
        {
            id = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GetNoviceTaskStatusReq();
            copied.id = this.id;
            return copied;
        }
    }
}

