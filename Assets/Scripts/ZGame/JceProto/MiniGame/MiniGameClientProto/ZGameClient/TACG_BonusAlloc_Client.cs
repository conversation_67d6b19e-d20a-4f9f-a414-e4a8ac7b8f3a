// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_BonusAlloc_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iLootPlan = 0;

        public int iDiffCount = 0;

        public int iWeight = 0;

        public string sBonusReward = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLootPlan, 1);
            _os.Write(iDiffCount, 2);
            _os.Write(iWeight, 3);
            _os.Write(sBonusReward, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLootPlan = (int) _is.Read(iLootPlan, 1, false);

            iDiffCount = (int) _is.Read(iDiffCount, 2, false);

            iWeight = (int) _is.Read(iWeight, 3, false);

            sBonusReward = (string) _is.Read(sBonusReward, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLootPlan, "iLootPlan");
            _ds.Display(iDiffCount, "iDiffCount");
            _ds.Display(iWeight, "iWeight");
            _ds.Display(sBonusReward, "sBonusReward");
        }

        public override void Clear()
        {
            iID = 0;
            iLootPlan = 0;
            iDiffCount = 0;
            iWeight = 0;
            sBonusReward = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_BonusAlloc_Client();
            copied.iID = this.iID;
            copied.iLootPlan = this.iLootPlan;
            copied.iDiffCount = this.iDiffCount;
            copied.iWeight = this.iWeight;
            copied.sBonusReward = this.sBonusReward;
            return copied;
        }
    }
}

