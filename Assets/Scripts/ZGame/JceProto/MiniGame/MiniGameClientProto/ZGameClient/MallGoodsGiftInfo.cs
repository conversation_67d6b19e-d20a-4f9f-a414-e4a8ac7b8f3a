// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class MallGoodsGiftInfo : Wup.Jce.JceStruct
    {
        public ACG_GoodsDiscountInfo stDiscountInfo;

        public System.Collections.Generic.List<int> vecTagId;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stDiscountInfo, 3);
            _os.Write(vecTagId, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stDiscountInfo = (ACG_GoodsDiscountInfo) _is.Read(stDiscountInfo, 3, false);

            vecTagId = (System.Collections.Generic.List<int>) _is.Read(vecTagId, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stDiscountInfo, "stDiscountInfo");
            _ds.Display(vecTagId, "vecTagId");
        }

        public override void Clear()
        {
            stDiscountInfo = null;
            vecTagId = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new MallGoodsGiftInfo();
            copied.stDiscountInfo = (ACG_GoodsDiscountInfo)JceUtil.DeepClone(this.stDiscountInfo);
            copied.vecTagId = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.vecTagId);
            return copied;
        }
    }
}

