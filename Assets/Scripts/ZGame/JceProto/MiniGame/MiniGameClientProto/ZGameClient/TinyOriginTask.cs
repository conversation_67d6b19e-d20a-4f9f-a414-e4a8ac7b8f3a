// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TinyOriginTask : Wup.Jce.JceStruct
    {
        int _iTaskID = 0;
        public int iTaskID
        {
            get
            {
                 return _iTaskID;
            }
            set
            {
                _iTaskID = value; 
            }
        }

        int _iStatus = 0;
        public int iStatus
        {
            get
            {
                 return _iStatus;
            }
            set
            {
                _iStatus = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> progress {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTaskID, 0);
            _os.Write(iStatus, 1);
            _os.Write(progress, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTaskID = (int) _is.Read(iTaskID, 0, false);

            iStatus = (int) _is.Read(iStatus, 1, false);

            progress = (TKFrame.TKDictionary<int, int>) _is.Read(progress, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTaskID, "iTaskID");
            _ds.Display(iStatus, "iStatus");
            _ds.Display(progress, "progress");
        }

        public override void Clear()
        {
            iTaskID = 0;
            iStatus = 0;
            progress = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TinyOriginTask();
            copied.iTaskID = this.iTaskID;
            copied.iStatus = this.iStatus;
            copied.progress = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.progress);
            return copied;
        }
    }
}

