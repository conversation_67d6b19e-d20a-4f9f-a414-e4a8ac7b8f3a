// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyEquipEquipment : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _iPlayerID = 0;
        public int iPlayerID
        {
            get
            {
                 return _iPlayerID;
            }
            set
            {
                _iPlayerID = value; 
            }
        }

        public TAC_HeroEntity stHeroEntity {get; set;} 

        public ACG_Equipment heroAddEquipment {get; set;} 

        public ACG_Equipment heroRemoveEquipment {get; set;} 

        public ACG_Equipment playerRemoveEquipment {get; set;} 

        public System.Collections.Generic.List<ACG_Equipment> vecEquipment {get; set;} 

        public ACG_StealEquipmentInfo stealEquipment {get; set;} 

        bool _bNeedPlayEquipEffect = true;
        public bool bNeedPlayEquipEffect
        {
            get
            {
                 return _bNeedPlayEquipEffect;
            }
            set
            {
                _bNeedPlayEquipEffect = value; 
            }
        }

        bool _bSynthesisPhantom = false;
        public bool bSynthesisPhantom
        {
            get
            {
                 return _bSynthesisPhantom;
            }
            set
            {
                _bSynthesisPhantom = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iPlayerID, 1);
            _os.Write(stHeroEntity, 2);
            _os.Write(heroAddEquipment, 3);
            _os.Write(heroRemoveEquipment, 4);
            _os.Write(playerRemoveEquipment, 5);
            _os.Write(vecEquipment, 6);
            _os.Write(stealEquipment, 7);
            _os.Write(bNeedPlayEquipEffect, 8);
            _os.Write(bSynthesisPhantom, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iPlayerID = (int) _is.Read(iPlayerID, 1, false);

            stHeroEntity = (TAC_HeroEntity) _is.Read(stHeroEntity, 2, false);

            heroAddEquipment = (ACG_Equipment) _is.Read(heroAddEquipment, 3, false);

            heroRemoveEquipment = (ACG_Equipment) _is.Read(heroRemoveEquipment, 4, false);

            playerRemoveEquipment = (ACG_Equipment) _is.Read(playerRemoveEquipment, 5, false);

            vecEquipment = (System.Collections.Generic.List<ACG_Equipment>) _is.Read(vecEquipment, 6, false);

            stealEquipment = (ACG_StealEquipmentInfo) _is.Read(stealEquipment, 7, false);

            bNeedPlayEquipEffect = (bool) _is.Read(bNeedPlayEquipEffect, 8, false);

            bSynthesisPhantom = (bool) _is.Read(bSynthesisPhantom, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iPlayerID, "iPlayerID");
            _ds.Display(stHeroEntity, "stHeroEntity");
            _ds.Display(heroAddEquipment, "heroAddEquipment");
            _ds.Display(heroRemoveEquipment, "heroRemoveEquipment");
            _ds.Display(playerRemoveEquipment, "playerRemoveEquipment");
            _ds.Display(vecEquipment, "vecEquipment");
            _ds.Display(stealEquipment, "stealEquipment");
            _ds.Display(bNeedPlayEquipEffect, "bNeedPlayEquipEffect");
            _ds.Display(bSynthesisPhantom, "bSynthesisPhantom");
        }

        public override void Clear()
        {
            iRet = 0;
            iPlayerID = 0;
            stHeroEntity = null;
            heroAddEquipment = null;
            heroRemoveEquipment = null;
            playerRemoveEquipment = null;
            vecEquipment = null;
            stealEquipment = null;
            bNeedPlayEquipEffect = true;
            bSynthesisPhantom = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyEquipEquipment();
            copied.iRet = this.iRet;
            copied.iPlayerID = this.iPlayerID;
            copied.stHeroEntity = (TAC_HeroEntity)JceUtil.DeepClone(this.stHeroEntity);
            copied.heroAddEquipment = (ACG_Equipment)JceUtil.DeepClone(this.heroAddEquipment);
            copied.heroRemoveEquipment = (ACG_Equipment)JceUtil.DeepClone(this.heroRemoveEquipment);
            copied.playerRemoveEquipment = (ACG_Equipment)JceUtil.DeepClone(this.playerRemoveEquipment);
            copied.vecEquipment = (System.Collections.Generic.List<ACG_Equipment>)JceUtil.DeepClone(this.vecEquipment);
            copied.stealEquipment = (ACG_StealEquipmentInfo)JceUtil.DeepClone(this.stealEquipment);
            copied.bNeedPlayEquipEffect = this.bNeedPlayEquipEffect;
            copied.bSynthesisPhantom = this.bSynthesisPhantom;
            return copied;
        }
    }
}

