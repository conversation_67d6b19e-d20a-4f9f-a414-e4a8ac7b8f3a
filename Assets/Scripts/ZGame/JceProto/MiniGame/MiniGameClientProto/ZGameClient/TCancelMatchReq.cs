// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCancelMatchReq : Wup.Jce.JceStruct
    {
        public int iScene = 0;

        public string strPassword = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iScene, 0);
            _os.Write(strPassword, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iScene = (int) _is.Read(iScene, 0, false);

            strPassword = (string) _is.Read(strPassword, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iScene, "iScene");
            _ds.Display(strPassword, "strPassword");
        }

        public override void Clear()
        {
            iScene = 0;
            strPassword = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCancelMatchReq();
            copied.iScene = this.iScene;
            copied.strPassword = this.strPassword;
            return copied;
        }
    }
}

