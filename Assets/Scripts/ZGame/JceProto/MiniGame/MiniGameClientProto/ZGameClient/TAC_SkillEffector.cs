// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_SkillEffector : Wup.Jce.JceStruct
    {
        int _iEffectID = 0;
        public int iEffectID
        {
            get
            {
                 return _iEffectID;
            }
            set
            {
                _iEffectID = value; 
            }
        }

        public System.Collections.Generic.List<TAC_Effector> vectTarget {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iEffectID, 0);
            _os.Write(vectTarget, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iEffectID = (int) _is.Read(iEffectID, 0, false);

            vectTarget = (System.Collections.Generic.List<TAC_Effector>) _is.Read(vectTarget, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iEffectID, "iEffectID");
            _ds.Display(vectTarget, "vectTarget");
        }

        public override void Clear()
        {
            iEffectID = 0;
            vectTarget = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_SkillEffector();
            copied.iEffectID = this.iEffectID;
            copied.vectTarget = (System.Collections.Generic.List<TAC_Effector>)JceUtil.DeepClone(this.vectTarget);
            return copied;
        }
    }
}

