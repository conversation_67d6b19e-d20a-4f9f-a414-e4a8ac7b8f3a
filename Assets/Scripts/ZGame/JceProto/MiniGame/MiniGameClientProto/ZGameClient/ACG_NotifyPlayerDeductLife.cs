// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_NotifyPlayerDeductLife : Wup.Jce.JceStruct
    {
        int _iChairID = 0;
        public int iChairID
        {
            get
            {
                 return _iChairID;
            }
            set
            {
                _iChairID = value; 
            }
        }

        int _iLife = 0;
        public int iLife
        {
            get
            {
                 return _iLife;
            }
            set
            {
                _iLife = value; 
            }
        }

        int _iDeductLife = 0;
        public int iDeductLife
        {
            get
            {
                 return _iDeductLife;
            }
            set
            {
                _iDeductLife = value; 
            }
        }

        int _iAllDeductLife = 0;
        public int iAllDeductLife
        {
            get
            {
                 return _iAllDeductLife;
            }
            set
            {
                _iAllDeductLife = value; 
            }
        }

        bool _IsPlayerDeduct = true;
        public bool IsPlayerDeduct
        {
            get
            {
                 return _IsPlayerDeduct;
            }
            set
            {
                _IsPlayerDeduct = value; 
            }
        }

        bool _IsFinish = true;
        public bool IsFinish
        {
            get
            {
                 return _IsFinish;
            }
            set
            {
                _IsFinish = value; 
            }
        }

        bool _PlayDeductAni = true;
        public bool PlayDeductAni
        {
            get
            {
                 return _PlayDeductAni;
            }
            set
            {
                _PlayDeductAni = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iChairID, 0);
            _os.Write(iLife, 1);
            _os.Write(iDeductLife, 2);
            _os.Write(iAllDeductLife, 3);
            _os.Write(IsPlayerDeduct, 4);
            _os.Write(IsFinish, 5);
            _os.Write(PlayDeductAni, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iChairID = (int) _is.Read(iChairID, 0, false);

            iLife = (int) _is.Read(iLife, 1, false);

            iDeductLife = (int) _is.Read(iDeductLife, 2, false);

            iAllDeductLife = (int) _is.Read(iAllDeductLife, 3, false);

            IsPlayerDeduct = (bool) _is.Read(IsPlayerDeduct, 4, false);

            IsFinish = (bool) _is.Read(IsFinish, 5, false);

            PlayDeductAni = (bool) _is.Read(PlayDeductAni, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iChairID, "iChairID");
            _ds.Display(iLife, "iLife");
            _ds.Display(iDeductLife, "iDeductLife");
            _ds.Display(iAllDeductLife, "iAllDeductLife");
            _ds.Display(IsPlayerDeduct, "IsPlayerDeduct");
            _ds.Display(IsFinish, "IsFinish");
            _ds.Display(PlayDeductAni, "PlayDeductAni");
        }

        public override void Clear()
        {
            iChairID = 0;
            iLife = 0;
            iDeductLife = 0;
            iAllDeductLife = 0;
            IsPlayerDeduct = true;
            IsFinish = true;
            PlayDeductAni = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_NotifyPlayerDeductLife();
            copied.iChairID = this.iChairID;
            copied.iLife = this.iLife;
            copied.iDeductLife = this.iDeductLife;
            copied.iAllDeductLife = this.iAllDeductLife;
            copied.IsPlayerDeduct = this.IsPlayerDeduct;
            copied.IsFinish = this.IsFinish;
            copied.PlayDeductAni = this.PlayDeductAni;
            return copied;
        }
    }
}

