// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_CelebrateEffectInBattle_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iItemID = 0;

        public string scelebrateEffectName = "";

        public int iShareBtn = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iItemID, 1);
            _os.Write(scelebrateEffectName, 2);
            _os.Write(iShareBtn, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iItemID = (int) _is.Read(iItemID, 1, false);

            scelebrateEffectName = (string) _is.Read(scelebrateEffectName, 2, false);

            iShareBtn = (int) _is.Read(iShareBtn, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(scelebrateEffectName, "scelebrateEffectName");
            _ds.Display(iShareBtn, "iShareBtn");
        }

        public override void Clear()
        {
            iID = 0;
            iItemID = 0;
            scelebrateEffectName = "";
            iShareBtn = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_CelebrateEffectInBattle_Client();
            copied.iID = this.iID;
            copied.iItemID = this.iItemID;
            copied.scelebrateEffectName = this.scelebrateEffectName;
            copied.iShareBtn = this.iShareBtn;
            return copied;
        }
    }
}

