// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_CommonGuideFlow_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sFlowPart1 = "";

        public int iTagIndex1 = 0;

        public string sFlowPart2 = "";

        public int iTagIndex2 = 0;

        public string sExtraCfg = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sFlowPart1, 1);
            _os.Write(iTagIndex1, 2);
            _os.Write(sFlowPart2, 3);
            _os.Write(iTagIndex2, 4);
            _os.Write(sExtraCfg, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sFlowPart1 = (string) _is.Read(sFlowPart1, 1, false);

            iTagIndex1 = (int) _is.Read(iTagIndex1, 2, false);

            sFlowPart2 = (string) _is.Read(sFlowPart2, 3, false);

            iTagIndex2 = (int) _is.Read(iTagIndex2, 4, false);

            sExtraCfg = (string) _is.Read(sExtraCfg, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sFlowPart1, "sFlowPart1");
            _ds.Display(iTagIndex1, "iTagIndex1");
            _ds.Display(sFlowPart2, "sFlowPart2");
            _ds.Display(iTagIndex2, "iTagIndex2");
            _ds.Display(sExtraCfg, "sExtraCfg");
        }

        public override void Clear()
        {
            iID = 0;
            sFlowPart1 = "";
            iTagIndex1 = 0;
            sFlowPart2 = "";
            iTagIndex2 = 0;
            sExtraCfg = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_CommonGuideFlow_Client();
            copied.iID = this.iID;
            copied.sFlowPart1 = this.sFlowPart1;
            copied.iTagIndex1 = this.iTagIndex1;
            copied.sFlowPart2 = this.sFlowPart2;
            copied.iTagIndex2 = this.iTagIndex2;
            copied.sExtraCfg = this.sExtraCfg;
            return copied;
        }
    }
}

