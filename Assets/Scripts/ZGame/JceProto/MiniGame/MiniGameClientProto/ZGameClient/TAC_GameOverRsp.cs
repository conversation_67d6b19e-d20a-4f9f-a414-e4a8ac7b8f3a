// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_GameOverRsp : Wup.Jce.JceStruct
    {
        public TAC_GameResultData result;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(result, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            result = (TAC_GameResultData) _is.Read(result, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(result, "result");
        }

        public override void Clear()
        {
            result = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_GameOverRsp();
            copied.result = (TAC_GameResultData)JceUtil.DeepClone(this.result);
            return copied;
        }
    }
}

