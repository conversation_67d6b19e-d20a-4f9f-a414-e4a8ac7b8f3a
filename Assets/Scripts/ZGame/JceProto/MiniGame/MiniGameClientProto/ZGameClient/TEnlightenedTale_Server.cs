// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TEnlightenedTale_Server : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iActivityID = 0;

        public int iOncePrice = 0;

        public int iTenPrice = 0;

        public string sOnceCount = "";

        public string sTenCount = "";

        public int iOnceCountPrice = 0;

        public int iTenCountPrice = 0;

        public int iMallID = 0;

        public string sCount2PrizePoolID = "";

        public int iCoin = 0;

        public int iBigPrize = 0;

        public string sPrizePoolName = "";

        public string sPrizePoolIcon = "";

        public string sCoinJump = "";

        public int iFragment = 0;

        public int iBigPrize1 = 0;

        public int iCoupon = 0;

        public int iDiscount = 0;

        public int iPresentCoupon = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iActivityID, 1);
            _os.Write(iOncePrice, 2);
            _os.Write(iTenPrice, 3);
            _os.Write(sOnceCount, 4);
            _os.Write(sTenCount, 5);
            _os.Write(iOnceCountPrice, 6);
            _os.Write(iTenCountPrice, 7);
            _os.Write(iMallID, 8);
            _os.Write(sCount2PrizePoolID, 9);
            _os.Write(iCoin, 10);
            _os.Write(iBigPrize, 11);
            _os.Write(sPrizePoolName, 12);
            _os.Write(sPrizePoolIcon, 13);
            _os.Write(sCoinJump, 14);
            _os.Write(iFragment, 15);
            _os.Write(iBigPrize1, 16);
            _os.Write(iCoupon, 17);
            _os.Write(iDiscount, 18);
            _os.Write(iPresentCoupon, 19);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iActivityID = (int) _is.Read(iActivityID, 1, false);

            iOncePrice = (int) _is.Read(iOncePrice, 2, false);

            iTenPrice = (int) _is.Read(iTenPrice, 3, false);

            sOnceCount = (string) _is.Read(sOnceCount, 4, false);

            sTenCount = (string) _is.Read(sTenCount, 5, false);

            iOnceCountPrice = (int) _is.Read(iOnceCountPrice, 6, false);

            iTenCountPrice = (int) _is.Read(iTenCountPrice, 7, false);

            iMallID = (int) _is.Read(iMallID, 8, false);

            sCount2PrizePoolID = (string) _is.Read(sCount2PrizePoolID, 9, false);

            iCoin = (int) _is.Read(iCoin, 10, false);

            iBigPrize = (int) _is.Read(iBigPrize, 11, false);

            sPrizePoolName = (string) _is.Read(sPrizePoolName, 12, false);

            sPrizePoolIcon = (string) _is.Read(sPrizePoolIcon, 13, false);

            sCoinJump = (string) _is.Read(sCoinJump, 14, false);

            iFragment = (int) _is.Read(iFragment, 15, false);

            iBigPrize1 = (int) _is.Read(iBigPrize1, 16, false);

            iCoupon = (int) _is.Read(iCoupon, 17, false);

            iDiscount = (int) _is.Read(iDiscount, 18, false);

            iPresentCoupon = (int) _is.Read(iPresentCoupon, 19, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iActivityID, "iActivityID");
            _ds.Display(iOncePrice, "iOncePrice");
            _ds.Display(iTenPrice, "iTenPrice");
            _ds.Display(sOnceCount, "sOnceCount");
            _ds.Display(sTenCount, "sTenCount");
            _ds.Display(iOnceCountPrice, "iOnceCountPrice");
            _ds.Display(iTenCountPrice, "iTenCountPrice");
            _ds.Display(iMallID, "iMallID");
            _ds.Display(sCount2PrizePoolID, "sCount2PrizePoolID");
            _ds.Display(iCoin, "iCoin");
            _ds.Display(iBigPrize, "iBigPrize");
            _ds.Display(sPrizePoolName, "sPrizePoolName");
            _ds.Display(sPrizePoolIcon, "sPrizePoolIcon");
            _ds.Display(sCoinJump, "sCoinJump");
            _ds.Display(iFragment, "iFragment");
            _ds.Display(iBigPrize1, "iBigPrize1");
            _ds.Display(iCoupon, "iCoupon");
            _ds.Display(iDiscount, "iDiscount");
            _ds.Display(iPresentCoupon, "iPresentCoupon");
        }

        public override void Clear()
        {
            iIndex = 0;
            iActivityID = 0;
            iOncePrice = 0;
            iTenPrice = 0;
            sOnceCount = "";
            sTenCount = "";
            iOnceCountPrice = 0;
            iTenCountPrice = 0;
            iMallID = 0;
            sCount2PrizePoolID = "";
            iCoin = 0;
            iBigPrize = 0;
            sPrizePoolName = "";
            sPrizePoolIcon = "";
            sCoinJump = "";
            iFragment = 0;
            iBigPrize1 = 0;
            iCoupon = 0;
            iDiscount = 0;
            iPresentCoupon = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TEnlightenedTale_Server();
            copied.iIndex = this.iIndex;
            copied.iActivityID = this.iActivityID;
            copied.iOncePrice = this.iOncePrice;
            copied.iTenPrice = this.iTenPrice;
            copied.sOnceCount = this.sOnceCount;
            copied.sTenCount = this.sTenCount;
            copied.iOnceCountPrice = this.iOnceCountPrice;
            copied.iTenCountPrice = this.iTenCountPrice;
            copied.iMallID = this.iMallID;
            copied.sCount2PrizePoolID = this.sCount2PrizePoolID;
            copied.iCoin = this.iCoin;
            copied.iBigPrize = this.iBigPrize;
            copied.sPrizePoolName = this.sPrizePoolName;
            copied.sPrizePoolIcon = this.sPrizePoolIcon;
            copied.sCoinJump = this.sCoinJump;
            copied.iFragment = this.iFragment;
            copied.iBigPrize1 = this.iBigPrize1;
            copied.iCoupon = this.iCoupon;
            copied.iDiscount = this.iDiscount;
            copied.iPresentCoupon = this.iPresentCoupon;
            return copied;
        }
    }
}

