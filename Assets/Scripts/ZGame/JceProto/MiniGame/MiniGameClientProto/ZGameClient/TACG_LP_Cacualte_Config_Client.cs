//所在的Excel 【ACG_Match.xlsm】
//*********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_LP_Cacualte_Config_Client : Wup.Jce.JceStruct
    {
        public int iRankID = 0;

        public string sName = "";

        public string sTarget_MMR = "";

        public string sMaxSkillFactor_LPLoss = "";

        public string sMinSkillFactor_LPLoss = "";

        public string sMaxSkillFactor_LPGain = "";

        public string sMinSkillFactor_LPGain = "";

        public int iMinMatchRankID = 0;

        public int iMaxMatchRankID = 0;

        public int iTierEdgeFlag = 0;

        public string sIcon = "";

        public int iRoomMaxMemberCount = 0;

        public int iBigRankID = 0;

        public string sminiIcon = "";

        public string sShowName = "";

        public int iAvatarBoxID = 0;

        public string sLevelUpMovie = "";

        public string sLevelUpAudio = "";

        public string sSegmentRateFinishAudio = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRankID, 0);
            _os.Write(sName, 1);
            _os.Write(sTarget_MMR, 2);
            _os.Write(sMaxSkillFactor_LPLoss, 3);
            _os.Write(sMinSkillFactor_LPLoss, 4);
            _os.Write(sMaxSkillFactor_LPGain, 5);
            _os.Write(sMinSkillFactor_LPGain, 6);
            _os.Write(iMinMatchRankID, 7);
            _os.Write(iMaxMatchRankID, 8);
            _os.Write(iTierEdgeFlag, 9);
            _os.Write(sIcon, 10);
            _os.Write(iRoomMaxMemberCount, 11);
            _os.Write(iBigRankID, 12);
            _os.Write(sminiIcon, 13);
            _os.Write(sShowName, 14);
            _os.Write(iAvatarBoxID, 15);
            _os.Write(sLevelUpMovie, 16);
            _os.Write(sLevelUpAudio, 17);
            _os.Write(sSegmentRateFinishAudio, 18);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRankID = (int) _is.Read(iRankID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sTarget_MMR = (string) _is.Read(sTarget_MMR, 2, false);

            sMaxSkillFactor_LPLoss = (string) _is.Read(sMaxSkillFactor_LPLoss, 3, false);

            sMinSkillFactor_LPLoss = (string) _is.Read(sMinSkillFactor_LPLoss, 4, false);

            sMaxSkillFactor_LPGain = (string) _is.Read(sMaxSkillFactor_LPGain, 5, false);

            sMinSkillFactor_LPGain = (string) _is.Read(sMinSkillFactor_LPGain, 6, false);

            iMinMatchRankID = (int) _is.Read(iMinMatchRankID, 7, false);

            iMaxMatchRankID = (int) _is.Read(iMaxMatchRankID, 8, false);

            iTierEdgeFlag = (int) _is.Read(iTierEdgeFlag, 9, false);

            sIcon = (string) _is.Read(sIcon, 10, false);

            iRoomMaxMemberCount = (int) _is.Read(iRoomMaxMemberCount, 11, false);

            iBigRankID = (int) _is.Read(iBigRankID, 12, false);

            sminiIcon = (string) _is.Read(sminiIcon, 13, false);

            sShowName = (string) _is.Read(sShowName, 14, false);

            iAvatarBoxID = (int) _is.Read(iAvatarBoxID, 15, false);

            sLevelUpMovie = (string) _is.Read(sLevelUpMovie, 16, false);

            sLevelUpAudio = (string) _is.Read(sLevelUpAudio, 17, false);

            sSegmentRateFinishAudio = (string) _is.Read(sSegmentRateFinishAudio, 18, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRankID, "iRankID");
            _ds.Display(sName, "sName");
            _ds.Display(sTarget_MMR, "sTarget_MMR");
            _ds.Display(sMaxSkillFactor_LPLoss, "sMaxSkillFactor_LPLoss");
            _ds.Display(sMinSkillFactor_LPLoss, "sMinSkillFactor_LPLoss");
            _ds.Display(sMaxSkillFactor_LPGain, "sMaxSkillFactor_LPGain");
            _ds.Display(sMinSkillFactor_LPGain, "sMinSkillFactor_LPGain");
            _ds.Display(iMinMatchRankID, "iMinMatchRankID");
            _ds.Display(iMaxMatchRankID, "iMaxMatchRankID");
            _ds.Display(iTierEdgeFlag, "iTierEdgeFlag");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iRoomMaxMemberCount, "iRoomMaxMemberCount");
            _ds.Display(iBigRankID, "iBigRankID");
            _ds.Display(sminiIcon, "sminiIcon");
            _ds.Display(sShowName, "sShowName");
            _ds.Display(iAvatarBoxID, "iAvatarBoxID");
            _ds.Display(sLevelUpMovie, "sLevelUpMovie");
            _ds.Display(sLevelUpAudio, "sLevelUpAudio");
            _ds.Display(sSegmentRateFinishAudio, "sSegmentRateFinishAudio");
        }

        public override void Clear()
        {
            iRankID = 0;
            sName = "";
            sTarget_MMR = "";
            sMaxSkillFactor_LPLoss = "";
            sMinSkillFactor_LPLoss = "";
            sMaxSkillFactor_LPGain = "";
            sMinSkillFactor_LPGain = "";
            iMinMatchRankID = 0;
            iMaxMatchRankID = 0;
            iTierEdgeFlag = 0;
            sIcon = "";
            iRoomMaxMemberCount = 0;
            iBigRankID = 0;
            sminiIcon = "";
            sShowName = "";
            iAvatarBoxID = 0;
            sLevelUpMovie = "";
            sLevelUpAudio = "";
            sSegmentRateFinishAudio = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_LP_Cacualte_Config_Client();
            copied.iRankID = this.iRankID;
            copied.sName = this.sName;
            copied.sTarget_MMR = this.sTarget_MMR;
            copied.sMaxSkillFactor_LPLoss = this.sMaxSkillFactor_LPLoss;
            copied.sMinSkillFactor_LPLoss = this.sMinSkillFactor_LPLoss;
            copied.sMaxSkillFactor_LPGain = this.sMaxSkillFactor_LPGain;
            copied.sMinSkillFactor_LPGain = this.sMinSkillFactor_LPGain;
            copied.iMinMatchRankID = this.iMinMatchRankID;
            copied.iMaxMatchRankID = this.iMaxMatchRankID;
            copied.iTierEdgeFlag = this.iTierEdgeFlag;
            copied.sIcon = this.sIcon;
            copied.iRoomMaxMemberCount = this.iRoomMaxMemberCount;
            copied.iBigRankID = this.iBigRankID;
            copied.sminiIcon = this.sminiIcon;
            copied.sShowName = this.sShowName;
            copied.iAvatarBoxID = this.iAvatarBoxID;
            copied.sLevelUpMovie = this.sLevelUpMovie;
            copied.sLevelUpAudio = this.sLevelUpAudio;
            copied.sSegmentRateFinishAudio = this.sSegmentRateFinishAudio;
            return copied;
        }
    }
}

