//所在的Excel 【ACG_Buff.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACGDebuffGroup_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iAttributeType = 0;

        public int iIsOrigValue = 0;

        public int iIsSortBigger = 0;

        public int iIsAddCalIfAttrScale = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iAttributeType, 1);
            _os.Write(iIsOrigValue, 2);
            _os.Write(iIsSortBigger, 3);
            _os.Write(iIsAddCalIfAttrScale, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iAttributeType = (int) _is.Read(iAttributeType, 1, false);

            iIsOrigValue = (int) _is.Read(iIsOrigValue, 2, false);

            iIsSortBigger = (int) _is.Read(iIsSortBigger, 3, false);

            iIsAddCalIfAttrScale = (int) _is.Read(iIsAddCalIfAttrScale, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iAttributeType, "iAttributeType");
            _ds.Display(iIsOrigValue, "iIsOrigValue");
            _ds.Display(iIsSortBigger, "iIsSortBigger");
            _ds.Display(iIsAddCalIfAttrScale, "iIsAddCalIfAttrScale");
        }

        public override void Clear()
        {
            iID = 0;
            iAttributeType = 0;
            iIsOrigValue = 0;
            iIsSortBigger = 0;
            iIsAddCalIfAttrScale = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACGDebuffGroup_Client();
            copied.iID = this.iID;
            copied.iAttributeType = this.iAttributeType;
            copied.iIsOrigValue = this.iIsOrigValue;
            copied.iIsSortBigger = this.iIsSortBigger;
            copied.iIsAddCalIfAttrScale = this.iIsAddCalIfAttrScale;
            return copied;
        }
    }
}

