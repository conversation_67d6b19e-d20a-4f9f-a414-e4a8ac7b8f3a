// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TABLE_USER_INFO : Wup.Jce.JceStruct
    {
        long _m_lUin = 0;
        public long m_lUin
        {
            get
            {
                 return _m_lUin;
            }
            set
            {
                _m_lUin = value; 
            }
        }

        short _m_nPlayerID = 0;
        public short m_nPlayerID
        {
            get
            {
                 return _m_nPlayerID;
            }
            set
            {
                _m_nPlayerID = value; 
            }
        }

        byte _m_nChair = 0;
        public byte m_nChair
        {
            get
            {
                 return _m_nChair;
            }
            set
            {
                _m_nChair = value; 
            }
        }

        short _m_nState = 0;
        public short m_nState
        {
            get
            {
                 return _m_nState;
            }
            set
            {
                _m_nState = value; 
            }
        }

        int _m_iFaceID = 0;
        public int m_iFaceID
        {
            get
            {
                 return _m_iFaceID;
            }
            set
            {
                _m_iFaceID = value; 
            }
        }

        string _m_strUserName = "";
        public string m_strUserName
        {
            get
            {
                 return _m_strUserName;
            }
            set
            {
                _m_strUserName = value; 
            }
        }

        long _iHeroId = 0;
        public long iHeroId
        {
            get
            {
                 return _iHeroId;
            }
            set
            {
                _iHeroId = value; 
            }
        }

        int _m_iOpenPlatType = 0;
        public int m_iOpenPlatType
        {
            get
            {
                 return _m_iOpenPlatType;
            }
            set
            {
                _m_iOpenPlatType = value; 
            }
        }

        int _m_iMobilePlatType = 0;
        public int m_iMobilePlatType
        {
            get
            {
                 return _m_iMobilePlatType;
            }
            set
            {
                _m_iMobilePlatType = value; 
            }
        }

        int _m_iZoneID = 0;
        public int m_iZoneID
        {
            get
            {
                 return _m_iZoneID;
            }
            set
            {
                _m_iZoneID = value; 
            }
        }

        string _m_sOpenID = "";
        public string m_sOpenID
        {
            get
            {
                 return _m_sOpenID;
            }
            set
            {
                _m_sOpenID = value; 
            }
        }

        int _m_iELOPoint = 0;
        public int m_iELOPoint
        {
            get
            {
                 return _m_iELOPoint;
            }
            set
            {
                _m_iELOPoint = value; 
            }
        }

        long _m_iDailyRewardCoin = 0;
        public long m_iDailyRewardCoin
        {
            get
            {
                 return _m_iDailyRewardCoin;
            }
            set
            {
                _m_iDailyRewardCoin = value; 
            }
        }

        int _m_iPlayMatchType = 0;
        public int m_iPlayMatchType
        {
            get
            {
                 return _m_iPlayMatchType;
            }
            set
            {
                _m_iPlayMatchType = value; 
            }
        }

        int _m_iTeamID = -1;
        public int m_iTeamID
        {
            get
            {
                 return _m_iTeamID;
            }
            set
            {
                _m_iTeamID = value; 
            }
        }

        string _m_sFaceUrl = "";
        public string m_sFaceUrl
        {
            get
            {
                 return _m_sFaceUrl;
            }
            set
            {
                _m_sFaceUrl = value; 
            }
        }

        public ProtoCaptainInfo stUsedCaptainInfo {get; set;} 

        int _m_iSession = 0;
        public int m_iSession
        {
            get
            {
                 return _m_iSession;
            }
            set
            {
                _m_iSession = value; 
            }
        }

        int _m_iSegmentID = 0;
        public int m_iSegmentID
        {
            get
            {
                 return _m_iSegmentID;
            }
            set
            {
                _m_iSegmentID = value; 
            }
        }

        int _m_iExp = 0;
        public int m_iExp
        {
            get
            {
                 return _m_iExp;
            }
            set
            {
                _m_iExp = value; 
            }
        }

        bool _m_isProtected = true;
        public bool m_isProtected
        {
            get
            {
                 return _m_isProtected;
            }
            set
            {
                _m_isProtected = value; 
            }
        }

        int _m_iWarmMatchType = 0;
        public int m_iWarmMatchType
        {
            get
            {
                 return _m_iWarmMatchType;
            }
            set
            {
                _m_iWarmMatchType = value; 
            }
        }

        int _m_iAutoPickGoldCoins = 0;
        public int m_iAutoPickGoldCoins
        {
            get
            {
                 return _m_iAutoPickGoldCoins;
            }
            set
            {
                _m_iAutoPickGoldCoins = value; 
            }
        }

        int _m_iAutoSortWaitHero = 0;
        public int m_iAutoSortWaitHero
        {
            get
            {
                 return _m_iAutoSortWaitHero;
            }
            set
            {
                _m_iAutoSortWaitHero = value; 
            }
        }

        int _m_iWarmValue = 0;
        public int m_iWarmValue
        {
            get
            {
                 return _m_iWarmValue;
            }
            set
            {
                _m_iWarmValue = value; 
            }
        }

        bool _m_bOpenWarmSwitch = true;
        public bool m_bOpenWarmSwitch
        {
            get
            {
                 return _m_bOpenWarmSwitch;
            }
            set
            {
                _m_bOpenWarmSwitch = value; 
            }
        }

        bool _m_bJoyStickSwitch = true;
        public bool m_bJoyStickSwitch
        {
            get
            {
                 return _m_bJoyStickSwitch;
            }
            set
            {
                _m_bJoyStickSwitch = value; 
            }
        }

        public System.Collections.Generic.List<ProtoInGameEffectInfo> vecUsedInGameEffect {get; set;} 

        int _m_iAvatarBoxId = 0;
        public int m_iAvatarBoxId
        {
            get
            {
                 return _m_iAvatarBoxId;
            }
            set
            {
                _m_iAvatarBoxId = value; 
            }
        }

        int _m_iBusinessCardBackgroundID = 0;
        public int m_iBusinessCardBackgroundID
        {
            get
            {
                 return _m_iBusinessCardBackgroundID;
            }
            set
            {
                _m_iBusinessCardBackgroundID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(m_lUin, 0);
            _os.Write(m_nPlayerID, 1);
            _os.Write(m_nChair, 2);
            _os.Write(m_nState, 3);
            _os.Write(m_iFaceID, 4);
            _os.Write(m_strUserName, 5);
            _os.Write(iHeroId, 6);
            _os.Write(m_iOpenPlatType, 7);
            _os.Write(m_iMobilePlatType, 8);
            _os.Write(m_iZoneID, 9);
            _os.Write(m_sOpenID, 10);
            _os.Write(m_iELOPoint, 11);
            _os.Write(m_iDailyRewardCoin, 12);
            _os.Write(m_iPlayMatchType, 13);
            _os.Write(m_iTeamID, 14);
            _os.Write(m_sFaceUrl, 15);
            _os.Write(stUsedCaptainInfo, 16);
            _os.Write(m_iSession, 17);
            _os.Write(m_iSegmentID, 18);
            _os.Write(m_iExp, 19);
            _os.Write(m_isProtected, 20);
            _os.Write(m_iWarmMatchType, 21);
            _os.Write(m_iAutoPickGoldCoins, 22);
            _os.Write(m_iAutoSortWaitHero, 23);
            _os.Write(m_iWarmValue, 24);
            _os.Write(m_bOpenWarmSwitch, 25);
            _os.Write(m_bJoyStickSwitch, 26);
            _os.Write(vecUsedInGameEffect, 27);
            _os.Write(m_iAvatarBoxId, 28);
            _os.Write(m_iBusinessCardBackgroundID, 29);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            m_lUin = (long) _is.Read(m_lUin, 0, false);

            m_nPlayerID = (short) _is.Read(m_nPlayerID, 1, false);

            m_nChair = (byte) _is.Read(m_nChair, 2, false);

            m_nState = (short) _is.Read(m_nState, 3, false);

            m_iFaceID = (int) _is.Read(m_iFaceID, 4, false);

            m_strUserName = (string) _is.Read(m_strUserName, 5, false);

            iHeroId = (long) _is.Read(iHeroId, 6, false);

            m_iOpenPlatType = (int) _is.Read(m_iOpenPlatType, 7, false);

            m_iMobilePlatType = (int) _is.Read(m_iMobilePlatType, 8, false);

            m_iZoneID = (int) _is.Read(m_iZoneID, 9, false);

            m_sOpenID = (string) _is.Read(m_sOpenID, 10, false);

            m_iELOPoint = (int) _is.Read(m_iELOPoint, 11, false);

            m_iDailyRewardCoin = (long) _is.Read(m_iDailyRewardCoin, 12, false);

            m_iPlayMatchType = (int) _is.Read(m_iPlayMatchType, 13, false);

            m_iTeamID = (int) _is.Read(m_iTeamID, 14, false);

            m_sFaceUrl = (string) _is.Read(m_sFaceUrl, 15, false);

            stUsedCaptainInfo = (ProtoCaptainInfo) _is.Read(stUsedCaptainInfo, 16, false);

            m_iSession = (int) _is.Read(m_iSession, 17, false);

            m_iSegmentID = (int) _is.Read(m_iSegmentID, 18, false);

            m_iExp = (int) _is.Read(m_iExp, 19, false);

            m_isProtected = (bool) _is.Read(m_isProtected, 20, false);

            m_iWarmMatchType = (int) _is.Read(m_iWarmMatchType, 21, false);

            m_iAutoPickGoldCoins = (int) _is.Read(m_iAutoPickGoldCoins, 22, false);

            m_iAutoSortWaitHero = (int) _is.Read(m_iAutoSortWaitHero, 23, false);

            m_iWarmValue = (int) _is.Read(m_iWarmValue, 24, false);

            m_bOpenWarmSwitch = (bool) _is.Read(m_bOpenWarmSwitch, 25, false);

            m_bJoyStickSwitch = (bool) _is.Read(m_bJoyStickSwitch, 26, false);

            vecUsedInGameEffect = (System.Collections.Generic.List<ProtoInGameEffectInfo>) _is.Read(vecUsedInGameEffect, 27, false);

            m_iAvatarBoxId = (int) _is.Read(m_iAvatarBoxId, 28, false);

            m_iBusinessCardBackgroundID = (int) _is.Read(m_iBusinessCardBackgroundID, 29, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(m_lUin, "m_lUin");
            _ds.Display(m_nPlayerID, "m_nPlayerID");
            _ds.Display(m_nChair, "m_nChair");
            _ds.Display(m_nState, "m_nState");
            _ds.Display(m_iFaceID, "m_iFaceID");
            _ds.Display(m_strUserName, "m_strUserName");
            _ds.Display(iHeroId, "iHeroId");
            _ds.Display(m_iOpenPlatType, "m_iOpenPlatType");
            _ds.Display(m_iMobilePlatType, "m_iMobilePlatType");
            _ds.Display(m_iZoneID, "m_iZoneID");
            _ds.Display(m_sOpenID, "m_sOpenID");
            _ds.Display(m_iELOPoint, "m_iELOPoint");
            _ds.Display(m_iDailyRewardCoin, "m_iDailyRewardCoin");
            _ds.Display(m_iPlayMatchType, "m_iPlayMatchType");
            _ds.Display(m_iTeamID, "m_iTeamID");
            _ds.Display(m_sFaceUrl, "m_sFaceUrl");
            _ds.Display(stUsedCaptainInfo, "stUsedCaptainInfo");
            _ds.Display(m_iSession, "m_iSession");
            _ds.Display(m_iSegmentID, "m_iSegmentID");
            _ds.Display(m_iExp, "m_iExp");
            _ds.Display(m_isProtected, "m_isProtected");
            _ds.Display(m_iWarmMatchType, "m_iWarmMatchType");
            _ds.Display(m_iAutoPickGoldCoins, "m_iAutoPickGoldCoins");
            _ds.Display(m_iAutoSortWaitHero, "m_iAutoSortWaitHero");
            _ds.Display(m_iWarmValue, "m_iWarmValue");
            _ds.Display(m_bOpenWarmSwitch, "m_bOpenWarmSwitch");
            _ds.Display(m_bJoyStickSwitch, "m_bJoyStickSwitch");
            _ds.Display(vecUsedInGameEffect, "vecUsedInGameEffect");
            _ds.Display(m_iAvatarBoxId, "m_iAvatarBoxId");
            _ds.Display(m_iBusinessCardBackgroundID, "m_iBusinessCardBackgroundID");
        }

        public override void Clear()
        {
            m_lUin = 0;
            m_nPlayerID = 0;
            m_nChair = 0;
            m_nState = 0;
            m_iFaceID = 0;
            m_strUserName = "";
            iHeroId = 0;
            m_iOpenPlatType = 0;
            m_iMobilePlatType = 0;
            m_iZoneID = 0;
            m_sOpenID = "";
            m_iELOPoint = 0;
            m_iDailyRewardCoin = 0;
            m_iPlayMatchType = 0;
            m_iTeamID = -1;
            m_sFaceUrl = "";
            stUsedCaptainInfo = null;
            m_iSession = 0;
            m_iSegmentID = 0;
            m_iExp = 0;
            m_isProtected = true;
            m_iWarmMatchType = 0;
            m_iAutoPickGoldCoins = 0;
            m_iAutoSortWaitHero = 0;
            m_iWarmValue = 0;
            m_bOpenWarmSwitch = true;
            m_bJoyStickSwitch = true;
            vecUsedInGameEffect = null;
            m_iAvatarBoxId = 0;
            m_iBusinessCardBackgroundID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TABLE_USER_INFO();
            copied.m_lUin = this.m_lUin;
            copied.m_nPlayerID = this.m_nPlayerID;
            copied.m_nChair = this.m_nChair;
            copied.m_nState = this.m_nState;
            copied.m_iFaceID = this.m_iFaceID;
            copied.m_strUserName = this.m_strUserName;
            copied.iHeroId = this.iHeroId;
            copied.m_iOpenPlatType = this.m_iOpenPlatType;
            copied.m_iMobilePlatType = this.m_iMobilePlatType;
            copied.m_iZoneID = this.m_iZoneID;
            copied.m_sOpenID = this.m_sOpenID;
            copied.m_iELOPoint = this.m_iELOPoint;
            copied.m_iDailyRewardCoin = this.m_iDailyRewardCoin;
            copied.m_iPlayMatchType = this.m_iPlayMatchType;
            copied.m_iTeamID = this.m_iTeamID;
            copied.m_sFaceUrl = this.m_sFaceUrl;
            copied.stUsedCaptainInfo = (ProtoCaptainInfo)JceUtil.DeepClone(this.stUsedCaptainInfo);
            copied.m_iSession = this.m_iSession;
            copied.m_iSegmentID = this.m_iSegmentID;
            copied.m_iExp = this.m_iExp;
            copied.m_isProtected = this.m_isProtected;
            copied.m_iWarmMatchType = this.m_iWarmMatchType;
            copied.m_iAutoPickGoldCoins = this.m_iAutoPickGoldCoins;
            copied.m_iAutoSortWaitHero = this.m_iAutoSortWaitHero;
            copied.m_iWarmValue = this.m_iWarmValue;
            copied.m_bOpenWarmSwitch = this.m_bOpenWarmSwitch;
            copied.m_bJoyStickSwitch = this.m_bJoyStickSwitch;
            copied.vecUsedInGameEffect = (System.Collections.Generic.List<ProtoInGameEffectInfo>)JceUtil.DeepClone(this.vecUsedInGameEffect);
            copied.m_iAvatarBoxId = this.m_iAvatarBoxId;
            copied.m_iBusinessCardBackgroundID = this.m_iBusinessCardBackgroundID;
            return copied;
        }
    }
}

