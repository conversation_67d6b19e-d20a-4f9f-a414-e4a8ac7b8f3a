// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualBulletScreenGetReq : Wup.Jce.JceStruct
    {
        public bool isOpenBulletScreen = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(isOpenBulletScreen, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            isOpenBulletScreen = (bool) _is.Read(isOpenBulletScreen, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(isOpenBulletScreen, "isOpenBulletScreen");
        }

        public override void Clear()
        {
            isOpenBulletScreen = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualBulletScreenGetReq();
            copied.isOpenBulletScreen = this.isOpenBulletScreen;
            return copied;
        }
    }
}

