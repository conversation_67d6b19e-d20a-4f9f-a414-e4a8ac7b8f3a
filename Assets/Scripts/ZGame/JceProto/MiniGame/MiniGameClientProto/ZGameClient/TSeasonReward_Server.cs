// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TSeasonReward_Server : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iSeasonID = 0;

        public string sTaskDes = "";

        public int iBigTier = 0;

        public int iItemID = 0;

        public int iItemCount = 0;

        public int iIsPreview = 0;

        public int iMaxTierItemID = 0;

        public int iMaxTierItemCount = 0;

        public string sTaskTitle = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iSeasonID, 1);
            _os.Write(sTaskDes, 2);
            _os.Write(iBigTier, 3);
            _os.Write(iItemID, 4);
            _os.Write(iItemCount, 5);
            _os.Write(iIsPreview, 6);
            _os.Write(iMaxTierItemID, 7);
            _os.Write(iMaxTierItemCount, 8);
            _os.Write(sTaskTitle, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iSeasonID = (int) _is.Read(iSeasonID, 1, false);

            sTaskDes = (string) _is.Read(sTaskDes, 2, false);

            iBigTier = (int) _is.Read(iBigTier, 3, false);

            iItemID = (int) _is.Read(iItemID, 4, false);

            iItemCount = (int) _is.Read(iItemCount, 5, false);

            iIsPreview = (int) _is.Read(iIsPreview, 6, false);

            iMaxTierItemID = (int) _is.Read(iMaxTierItemID, 7, false);

            iMaxTierItemCount = (int) _is.Read(iMaxTierItemCount, 8, false);

            sTaskTitle = (string) _is.Read(sTaskTitle, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iSeasonID, "iSeasonID");
            _ds.Display(sTaskDes, "sTaskDes");
            _ds.Display(iBigTier, "iBigTier");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iItemCount, "iItemCount");
            _ds.Display(iIsPreview, "iIsPreview");
            _ds.Display(iMaxTierItemID, "iMaxTierItemID");
            _ds.Display(iMaxTierItemCount, "iMaxTierItemCount");
            _ds.Display(sTaskTitle, "sTaskTitle");
        }

        public override void Clear()
        {
            iIndex = 0;
            iSeasonID = 0;
            sTaskDes = "";
            iBigTier = 0;
            iItemID = 0;
            iItemCount = 0;
            iIsPreview = 0;
            iMaxTierItemID = 0;
            iMaxTierItemCount = 0;
            sTaskTitle = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TSeasonReward_Server();
            copied.iIndex = this.iIndex;
            copied.iSeasonID = this.iSeasonID;
            copied.sTaskDes = this.sTaskDes;
            copied.iBigTier = this.iBigTier;
            copied.iItemID = this.iItemID;
            copied.iItemCount = this.iItemCount;
            copied.iIsPreview = this.iIsPreview;
            copied.iMaxTierItemID = this.iMaxTierItemID;
            copied.iMaxTierItemCount = this.iMaxTierItemCount;
            copied.sTaskTitle = this.sTaskTitle;
            return copied;
        }
    }
}

