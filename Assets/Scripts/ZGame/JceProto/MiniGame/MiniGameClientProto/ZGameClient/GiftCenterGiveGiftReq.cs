// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterGiveGiftReq : Wup.Jce.JceStruct
    {
        public ACG_TMallBuyReq stBuyInfo;

        public TUserID stReceiverUserID;

        public string sMessage = "";

        public bool bForceGive = false;

        public string sMsdkToken = "";

        public bool bSendMarquee = true;

        public string sPassword = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stBuyInfo, 0);
            _os.Write(stReceiverUserID, 1);
            _os.Write(sMessage, 2);
            _os.Write(bForceGive, 3);
            _os.Write(sMsdkToken, 4);
            _os.Write(bSendMarquee, 5);
            _os.Write(sPassword, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stBuyInfo = (ACG_TMallBuyReq) _is.Read(stBuyInfo, 0, false);

            stReceiverUserID = (TUserID) _is.Read(stReceiverUserID, 1, false);

            sMessage = (string) _is.Read(sMessage, 2, false);

            bForceGive = (bool) _is.Read(bForceGive, 3, false);

            sMsdkToken = (string) _is.Read(sMsdkToken, 4, false);

            bSendMarquee = (bool) _is.Read(bSendMarquee, 5, false);

            sPassword = (string) _is.Read(sPassword, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stBuyInfo, "stBuyInfo");
            _ds.Display(stReceiverUserID, "stReceiverUserID");
            _ds.Display(sMessage, "sMessage");
            _ds.Display(bForceGive, "bForceGive");
            _ds.Display(sMsdkToken, "sMsdkToken");
            _ds.Display(bSendMarquee, "bSendMarquee");
            _ds.Display(sPassword, "sPassword");
        }

        public override void Clear()
        {
            stBuyInfo = null;
            stReceiverUserID = null;
            sMessage = "";
            bForceGive = false;
            sMsdkToken = "";
            bSendMarquee = true;
            sPassword = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterGiveGiftReq();
            copied.stBuyInfo = (ACG_TMallBuyReq)JceUtil.DeepClone(this.stBuyInfo);
            copied.stReceiverUserID = (TUserID)JceUtil.DeepClone(this.stReceiverUserID);
            copied.sMessage = this.sMessage;
            copied.bForceGive = this.bForceGive;
            copied.sMsdkToken = this.sMsdkToken;
            copied.bSendMarquee = this.bSendMarquee;
            copied.sPassword = this.sPassword;
            return copied;
        }
    }
}

