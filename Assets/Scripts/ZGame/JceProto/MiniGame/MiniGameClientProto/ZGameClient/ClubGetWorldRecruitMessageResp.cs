// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClubGetWorldRecruitMessageResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public System.Collections.Generic.List<ClubWorldRecruitMsg> recruitMsgList;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(recruitMsgList, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            recruitMsgList = (System.Collections.Generic.List<ClubWorldRecruitMsg>) _is.Read(recruitMsgList, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(recruitMsgList, "recruitMsgList");
        }

        public override void Clear()
        {
            err = 0;
            recruitMsgList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClubGetWorldRecruitMessageResp();
            copied.err = this.err;
            copied.recruitMsgList = (System.Collections.Generic.List<ClubWorldRecruitMsg>)JceUtil.DeepClone(this.recruitMsgList);
            return copied;
        }
    }
}

