//所在的Excel 【ACG_Skill.xlsm】
//*********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACGSkill_Client : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 索引ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 技能名称
        /// </summary>
        public string sName = "";

        /// <summary>
        /// 技能描述
        /// </summary>
        public string sDesc = "";

        /// <summary>
        /// 目标选择类型
        /// </summary>
        public int iSelectorType = 0;

        /// <summary>
        /// 目标选择参数
        /// </summary>
        public string sSelectorParam = "";

        /// <summary>
        /// 冷却时间
        /// </summary>
        public int iCD = 0;

        /// <summary>
        /// 技能持续时间
        /// </summary>
        public int iSkillDuration = 0;

        /// <summary>
        /// 技能伤害基础值
        /// </summary>
        public int iSkillDamage = 0;

        /// <summary>
        /// 技能额外暴击率
        /// </summary>
        public int iSkillCrit = 0;

        /// <summary>
        /// 技能额外暴击伤害
        /// </summary>
        public int iSkillCritDamage = 0;

        /// <summary>
        /// 附加属性伤害类型
        /// </summary>
        public int iAddSkillDamageAttrType = 0;

        /// <summary>
        /// 附加属性伤害万分比
        /// </summary>
        public int iAddSkillDamageAttrPercent = 0;

        /// <summary>
        /// 施法类型
        /// </summary>
        public int iCastType = 0;

        /// <summary>
        /// 伤害类型
        /// </summary>
        public int iHurtType = 0;

        /// <summary>
        /// 改变技能条件类型
        /// </summary>
        public int iChangeCond1Type = 0;

        /// <summary>
        /// 改变技能条件参数
        /// </summary>
        public string sChangeCond1Param = "";

        /// <summary>
        /// 改变技能id
        /// </summary>
        public int iChangeCond1SkillId = 0;

        /// <summary>
        /// 改变技能条件类型
        /// </summary>
        public int iChangeCond2Type = 0;

        /// <summary>
        /// 改变技能条件参数
        /// </summary>
        public string sChangeCond2Param = "";

        /// <summary>
        /// 改变技能id
        /// </summary>
        public int iChangeCond2SkillId = 0;

        /// <summary>
        /// 消耗类型
        /// </summary>
        public int iExpend1Type = 0;

        /// <summary>
        /// 消耗参数1
        /// </summary>
        public int iExpend1P1 = 0;

        /// <summary>
        /// 消耗参数2
        /// </summary>
        public int iExpend1P2 = 0;

        /// <summary>
        /// 消耗类型
        /// </summary>
        public int iExpend2Type = 0;

        /// <summary>
        /// 消耗参数1
        /// </summary>
        public int iExpend2P1 = 0;

        /// <summary>
        /// 消耗参数2
        /// </summary>
        public int iExpend2P2 = 0;

        /// <summary>
        /// 条件类型
        /// </summary>
        public int iCondition1Type = 0;

        /// <summary>
        /// 条件参数1
        /// </summary>
        public int iCondition1P1 = 0;

        /// <summary>
        /// 条件参数2
        /// </summary>
        public int iCondition1P2 = 0;

        /// <summary>
        /// 条件类型
        /// </summary>
        public int iCondition2Type = 0;

        /// <summary>
        /// 条件参数1
        /// </summary>
        public int iCondition2P1 = 0;

        /// <summary>
        /// 条件参数2
        /// </summary>
        public int iCondition2P2 = 0;

        /// <summary>
        /// 是否展示魔法值
        /// </summary>
        public int iMagicShow = 0;

        /// <summary>
        /// 是否展示技能描述
        /// </summary>
        public int iSkillcShow = 0;

        /// <summary>
        /// 技能图标
        /// </summary>
        public string sIcon = "";

        /// <summary>
        /// 技能简要说明
        /// </summary>
        public string sBrief = "";

        /// <summary>
        /// 技能类型
        /// </summary>
        public int iSkillType = 0;

        /// <summary>
        /// 附加buffid
        /// </summary>
        public int iBuffId = 0;

        /// <summary>
        /// 客户端展示标签名称
        /// </summary>
        public string sShowTagName = "";

        /// <summary>
        /// 客户端展示标签类型
        /// </summary>
        public int iShowTagType = 0;

        /// <summary>
        /// 技能描述2
        /// </summary>
        public string sDesc2 = "";

        /// <summary>
        /// 技能简要说明里面的数值
        /// </summary>
        public string sBriefValue = "";

        /// <summary>
        /// 最终伤害百分比回血
        /// </summary>
        public int iDamageReplyHpPercent = 0;

        /// <summary>
        /// 致死后改变属性千分比
        /// </summary>
        public int iDeadlyStrikeChangePercent = 0;

        /// <summary>
        /// 致死后改变属性类型
        /// </summary>
        public int iDeadlyStrikeChangeAttrType = 0;

        /// <summary>
        /// 致死后改变属性数值
        /// </summary>
        public int iDeadlyStrikeChangeValue = 0;

        /// <summary>
        /// 是否使用伤害来源技能
        /// </summary>
        public int iIsUseHurtSkill = 0;

        /// <summary>
        /// 技能回血
        /// </summary>
        public int iReplyHp = 0;

        /// <summary>
        /// 技能回蓝
        /// </summary>
        public int iReplyMp = 0;

        /// <summary>
        /// 技能回血值数值类型
        /// </summary>
        public int iReplyHpValueType = 0;

        /// <summary>
        /// 技能回蓝值类型
        /// </summary>
        public int iReplyMpValueType = 0;

        /// <summary>
        /// 技能回血特效
        /// </summary>
        public string sReplyHpEffect = "";

        /// <summary>
        /// 技能回血特效挂点
        /// </summary>
        public int iReplyHpEffectPos = 0;

        /// <summary>
        /// 技能斩杀血量百分比
        /// </summary>
        public int iSeckillHpPercent = 0;

        /// <summary>
        /// 是否法术加成
        /// </summary>
        public int iIsAPAdd = 0;

        /// <summary>
        /// 是否增加等同于当前攻击力的伤害值
        /// </summary>
        public int iIsADAdd = 0;

        /// <summary>
        /// 回血是否法术加成
        /// </summary>
        public int iReplyHpIsAPAdd = 0;

        /// <summary>
        /// 伤害倍率
        /// </summary>
        public int iSkillDamageRate = 0;

        /// <summary>
        /// 附加目标属性伤害类型
        /// </summary>
        public int iAddSkillDamageTargetAttrType = 0;

        /// <summary>
        /// 附加目标属性伤害万分比
        /// </summary>
        public int iAddSkillDamageTargetAttrPercent = 0;

        /// <summary>
        /// 附加属性伤害是否ap加成
        /// </summary>
        public int iAddSkillDamageIsAddAp = 0;

        /// <summary>
        /// 附加目标属性伤害是否ap加成
        /// </summary>
        public int iAddSkillDamageTargetIsAddAp = 0;

        /// <summary>
        /// 受控状态是否释放
        /// </summary>
        public int iIsCanPlayOnUnControl = 0;

        /// <summary>
        /// 技能伤害基础值表达式
        /// </summary>
        public string sSkillDamageStr = "";

        /// <summary>
        /// 是否使用持续目标
        /// </summary>
        public int iIsUseDurationTarget = 0;

        /// <summary>
        /// 是否检测技能免疫
        /// </summary>
        public int iIsCheckSkillImmune = 0;

        /// <summary>
        /// 是否飘字累加
        /// </summary>
        public int iIsTextAdd = 0;

        /// <summary>
        /// 是否飘字缓动
        /// </summary>
        public int iIsTextTween = 0;

        /// <summary>
        /// 组id
        /// </summary>
        public int iGroupId = 0;

        /// <summary>
        /// SetId
        /// </summary>
        public int iSetId = 0;

        /// <summary>
        /// 致死后改变属性是否持续
        /// </summary>
        public int iDeadlyStrikeChangeAttrIsWhole = 0;

        /// <summary>
        /// 是否附加通用暴击
        /// </summary>
        public int iIsAddCommonCrit = 0;

        /// <summary>
        /// 伤害记录标志
        /// </summary>
        public int iDamageRecordMask = 0;

        /// <summary>
        /// 固定附加伤害（不参与ap加成）
        /// </summary>
        public int iAddFixedValue = 0;

        /// <summary>
        /// 是否飘字累加区别英雄
        /// </summary>
        public int iIsTextAddDiffHero = 0;

        /// <summary>
        /// 是否忽略固定数值伤害减免
        /// </summary>
        public int iIsIgnoreCalFixedHurtReduce = 0;

        /// <summary>
        /// 是否能击杀
        /// </summary>
        public int iIsCanKill = 0;

        /// <summary>
        /// 致死后改变属性限制次数
        /// </summary>
        public int iDeadlyStrikeChangeNumLimit = 0;

        /// <summary>
        /// 公共技能是否检测技能免疫
        /// </summary>
        public int iIsCommonSkillCheckSkillImmune = 0;

        /// <summary>
        /// 技能层数数据来源
        /// </summary>
        public int iSkillShowNumSource = 0;

        /// <summary>
        /// 技能层数数据来源参数
        /// </summary>
        public int iSkillShowNumSourceParam1 = 0;

        /// <summary>
        /// 技能层数数据来源参数
        /// </summary>
        public int iSkillShowNumSourceParam2 = 0;

        /// <summary>
        /// 目标选择扩展参数
        /// </summary>
        public int iSelectorExtendParam = 0;

        /// <summary>
        /// 当非英雄攻击，对应的攻击来源
        /// </summary>
        public int iNotHeroAttackType = 0;

        /// <summary>
        /// 永久buff记录
        /// </summary>
        public string sBuffRecord = "";

        /// <summary>
        /// 技能段数（持续伤害用）
        /// </summary>
        public int iSkillSectionNum = 0;

        /// <summary>
        /// 无视抗性数值
        /// </summary>
        public int iIgnoreResistanceValue = 0;

        /// <summary>
        /// 是否无视抗性百分比
        /// </summary>
        public int iIsIgnoreResistancePercent = 0;

        /// <summary>
        /// 技能补充描述
        /// </summary>
        public int iLiveCounter = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sDesc, 2);
            _os.Write(iSelectorType, 3);
            _os.Write(sSelectorParam, 5);
            _os.Write(iCD, 6);
            _os.Write(iSkillDuration, 7);
            _os.Write(iSkillDamage, 9);
            _os.Write(iSkillCrit, 10);
            _os.Write(iSkillCritDamage, 11);
            _os.Write(iAddSkillDamageAttrType, 12);
            _os.Write(iAddSkillDamageAttrPercent, 13);
            _os.Write(iCastType, 14);
            _os.Write(iHurtType, 15);
            _os.Write(iChangeCond1Type, 16);
            _os.Write(sChangeCond1Param, 17);
            _os.Write(iChangeCond1SkillId, 18);
            _os.Write(iChangeCond2Type, 19);
            _os.Write(sChangeCond2Param, 20);
            _os.Write(iChangeCond2SkillId, 21);
            _os.Write(iExpend1Type, 22);
            _os.Write(iExpend1P1, 23);
            _os.Write(iExpend1P2, 24);
            _os.Write(iExpend2Type, 25);
            _os.Write(iExpend2P1, 26);
            _os.Write(iExpend2P2, 27);
            _os.Write(iCondition1Type, 28);
            _os.Write(iCondition1P1, 29);
            _os.Write(iCondition1P2, 30);
            _os.Write(iCondition2Type, 31);
            _os.Write(iCondition2P1, 32);
            _os.Write(iCondition2P2, 33);
            _os.Write(iMagicShow, 34);
            _os.Write(iSkillcShow, 35);
            _os.Write(sIcon, 36);
            _os.Write(sBrief, 37);
            _os.Write(iSkillType, 38);
            _os.Write(iBuffId, 39);
            _os.Write(sShowTagName, 40);
            _os.Write(iShowTagType, 41);
            _os.Write(sDesc2, 42);
            _os.Write(sBriefValue, 43);
            _os.Write(iDamageReplyHpPercent, 44);
            _os.Write(iDeadlyStrikeChangePercent, 45);
            _os.Write(iDeadlyStrikeChangeAttrType, 46);
            _os.Write(iDeadlyStrikeChangeValue, 47);
            _os.Write(iIsUseHurtSkill, 48);
            _os.Write(iReplyHp, 49);
            _os.Write(iReplyMp, 50);
            _os.Write(iReplyHpValueType, 51);
            _os.Write(iReplyMpValueType, 52);
            _os.Write(sReplyHpEffect, 53);
            _os.Write(iReplyHpEffectPos, 54);
            _os.Write(iSeckillHpPercent, 55);
            _os.Write(iIsAPAdd, 56);
            _os.Write(iIsADAdd, 57);
            _os.Write(iReplyHpIsAPAdd, 58);
            _os.Write(iSkillDamageRate, 59);
            _os.Write(iAddSkillDamageTargetAttrType, 64);
            _os.Write(iAddSkillDamageTargetAttrPercent, 65);
            _os.Write(iAddSkillDamageIsAddAp, 66);
            _os.Write(iAddSkillDamageTargetIsAddAp, 67);
            _os.Write(iIsCanPlayOnUnControl, 68);
            _os.Write(sSkillDamageStr, 69);
            _os.Write(iIsUseDurationTarget, 70);
            _os.Write(iIsCheckSkillImmune, 71);
            _os.Write(iIsTextAdd, 72);
            _os.Write(iIsTextTween, 73);
            _os.Write(iGroupId, 74);
            _os.Write(iSetId, 75);
            _os.Write(iDeadlyStrikeChangeAttrIsWhole, 76);
            _os.Write(iIsAddCommonCrit, 77);
            _os.Write(iDamageRecordMask, 78);
            _os.Write(iAddFixedValue, 79);
            _os.Write(iIsTextAddDiffHero, 80);
            _os.Write(iIsIgnoreCalFixedHurtReduce, 81);
            _os.Write(iIsCanKill, 82);
            _os.Write(iDeadlyStrikeChangeNumLimit, 83);
            _os.Write(iIsCommonSkillCheckSkillImmune, 84);
            _os.Write(iSkillShowNumSource, 85);
            _os.Write(iSkillShowNumSourceParam1, 86);
            _os.Write(iSkillShowNumSourceParam2, 87);
            _os.Write(iSelectorExtendParam, 88);
            _os.Write(iNotHeroAttackType, 89);
            _os.Write(sBuffRecord, 90);
            _os.Write(iSkillSectionNum, 91);
            _os.Write(iIgnoreResistanceValue, 92);
            _os.Write(iIsIgnoreResistancePercent, 93);
            _os.Write(iLiveCounter, 95);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            iSelectorType = (int) _is.Read(iSelectorType, 3, false);

            sSelectorParam = (string) _is.Read(sSelectorParam, 5, false);

            iCD = (int) _is.Read(iCD, 6, false);

            iSkillDuration = (int) _is.Read(iSkillDuration, 7, false);

            iSkillDamage = (int) _is.Read(iSkillDamage, 9, false);

            iSkillCrit = (int) _is.Read(iSkillCrit, 10, false);

            iSkillCritDamage = (int) _is.Read(iSkillCritDamage, 11, false);

            iAddSkillDamageAttrType = (int) _is.Read(iAddSkillDamageAttrType, 12, false);

            iAddSkillDamageAttrPercent = (int) _is.Read(iAddSkillDamageAttrPercent, 13, false);

            iCastType = (int) _is.Read(iCastType, 14, false);

            iHurtType = (int) _is.Read(iHurtType, 15, false);

            iChangeCond1Type = (int) _is.Read(iChangeCond1Type, 16, false);

            sChangeCond1Param = (string) _is.Read(sChangeCond1Param, 17, false);

            iChangeCond1SkillId = (int) _is.Read(iChangeCond1SkillId, 18, false);

            iChangeCond2Type = (int) _is.Read(iChangeCond2Type, 19, false);

            sChangeCond2Param = (string) _is.Read(sChangeCond2Param, 20, false);

            iChangeCond2SkillId = (int) _is.Read(iChangeCond2SkillId, 21, false);

            iExpend1Type = (int) _is.Read(iExpend1Type, 22, false);

            iExpend1P1 = (int) _is.Read(iExpend1P1, 23, false);

            iExpend1P2 = (int) _is.Read(iExpend1P2, 24, false);

            iExpend2Type = (int) _is.Read(iExpend2Type, 25, false);

            iExpend2P1 = (int) _is.Read(iExpend2P1, 26, false);

            iExpend2P2 = (int) _is.Read(iExpend2P2, 27, false);

            iCondition1Type = (int) _is.Read(iCondition1Type, 28, false);

            iCondition1P1 = (int) _is.Read(iCondition1P1, 29, false);

            iCondition1P2 = (int) _is.Read(iCondition1P2, 30, false);

            iCondition2Type = (int) _is.Read(iCondition2Type, 31, false);

            iCondition2P1 = (int) _is.Read(iCondition2P1, 32, false);

            iCondition2P2 = (int) _is.Read(iCondition2P2, 33, false);

            iMagicShow = (int) _is.Read(iMagicShow, 34, false);

            iSkillcShow = (int) _is.Read(iSkillcShow, 35, false);

            sIcon = (string) _is.Read(sIcon, 36, false);

            sBrief = (string) _is.Read(sBrief, 37, false);

            iSkillType = (int) _is.Read(iSkillType, 38, false);

            iBuffId = (int) _is.Read(iBuffId, 39, false);

            sShowTagName = (string) _is.Read(sShowTagName, 40, false);

            iShowTagType = (int) _is.Read(iShowTagType, 41, false);

            sDesc2 = (string) _is.Read(sDesc2, 42, false);

            sBriefValue = (string) _is.Read(sBriefValue, 43, false);

            iDamageReplyHpPercent = (int) _is.Read(iDamageReplyHpPercent, 44, false);

            iDeadlyStrikeChangePercent = (int) _is.Read(iDeadlyStrikeChangePercent, 45, false);

            iDeadlyStrikeChangeAttrType = (int) _is.Read(iDeadlyStrikeChangeAttrType, 46, false);

            iDeadlyStrikeChangeValue = (int) _is.Read(iDeadlyStrikeChangeValue, 47, false);

            iIsUseHurtSkill = (int) _is.Read(iIsUseHurtSkill, 48, false);

            iReplyHp = (int) _is.Read(iReplyHp, 49, false);

            iReplyMp = (int) _is.Read(iReplyMp, 50, false);

            iReplyHpValueType = (int) _is.Read(iReplyHpValueType, 51, false);

            iReplyMpValueType = (int) _is.Read(iReplyMpValueType, 52, false);

            sReplyHpEffect = (string) _is.Read(sReplyHpEffect, 53, false);

            iReplyHpEffectPos = (int) _is.Read(iReplyHpEffectPos, 54, false);

            iSeckillHpPercent = (int) _is.Read(iSeckillHpPercent, 55, false);

            iIsAPAdd = (int) _is.Read(iIsAPAdd, 56, false);

            iIsADAdd = (int) _is.Read(iIsADAdd, 57, false);

            iReplyHpIsAPAdd = (int) _is.Read(iReplyHpIsAPAdd, 58, false);

            iSkillDamageRate = (int) _is.Read(iSkillDamageRate, 59, false);

            iAddSkillDamageTargetAttrType = (int) _is.Read(iAddSkillDamageTargetAttrType, 64, false);

            iAddSkillDamageTargetAttrPercent = (int) _is.Read(iAddSkillDamageTargetAttrPercent, 65, false);

            iAddSkillDamageIsAddAp = (int) _is.Read(iAddSkillDamageIsAddAp, 66, false);

            iAddSkillDamageTargetIsAddAp = (int) _is.Read(iAddSkillDamageTargetIsAddAp, 67, false);

            iIsCanPlayOnUnControl = (int) _is.Read(iIsCanPlayOnUnControl, 68, false);

            sSkillDamageStr = (string) _is.Read(sSkillDamageStr, 69, false);

            iIsUseDurationTarget = (int) _is.Read(iIsUseDurationTarget, 70, false);

            iIsCheckSkillImmune = (int) _is.Read(iIsCheckSkillImmune, 71, false);

            iIsTextAdd = (int) _is.Read(iIsTextAdd, 72, false);

            iIsTextTween = (int) _is.Read(iIsTextTween, 73, false);

            iGroupId = (int) _is.Read(iGroupId, 74, false);

            iSetId = (int) _is.Read(iSetId, 75, false);

            iDeadlyStrikeChangeAttrIsWhole = (int) _is.Read(iDeadlyStrikeChangeAttrIsWhole, 76, false);

            iIsAddCommonCrit = (int) _is.Read(iIsAddCommonCrit, 77, false);

            iDamageRecordMask = (int) _is.Read(iDamageRecordMask, 78, false);

            iAddFixedValue = (int) _is.Read(iAddFixedValue, 79, false);

            iIsTextAddDiffHero = (int) _is.Read(iIsTextAddDiffHero, 80, false);

            iIsIgnoreCalFixedHurtReduce = (int) _is.Read(iIsIgnoreCalFixedHurtReduce, 81, false);

            iIsCanKill = (int) _is.Read(iIsCanKill, 82, false);

            iDeadlyStrikeChangeNumLimit = (int) _is.Read(iDeadlyStrikeChangeNumLimit, 83, false);

            iIsCommonSkillCheckSkillImmune = (int) _is.Read(iIsCommonSkillCheckSkillImmune, 84, false);

            iSkillShowNumSource = (int) _is.Read(iSkillShowNumSource, 85, false);

            iSkillShowNumSourceParam1 = (int) _is.Read(iSkillShowNumSourceParam1, 86, false);

            iSkillShowNumSourceParam2 = (int) _is.Read(iSkillShowNumSourceParam2, 87, false);

            iSelectorExtendParam = (int) _is.Read(iSelectorExtendParam, 88, false);

            iNotHeroAttackType = (int) _is.Read(iNotHeroAttackType, 89, false);

            sBuffRecord = (string) _is.Read(sBuffRecord, 90, false);

            iSkillSectionNum = (int) _is.Read(iSkillSectionNum, 91, false);

            iIgnoreResistanceValue = (int) _is.Read(iIgnoreResistanceValue, 92, false);

            iIsIgnoreResistancePercent = (int) _is.Read(iIsIgnoreResistancePercent, 93, false);

            iLiveCounter = (int) _is.Read(iLiveCounter, 95, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iSelectorType, "iSelectorType");
            _ds.Display(sSelectorParam, "sSelectorParam");
            _ds.Display(iCD, "iCD");
            _ds.Display(iSkillDuration, "iSkillDuration");
            _ds.Display(iSkillDamage, "iSkillDamage");
            _ds.Display(iSkillCrit, "iSkillCrit");
            _ds.Display(iSkillCritDamage, "iSkillCritDamage");
            _ds.Display(iAddSkillDamageAttrType, "iAddSkillDamageAttrType");
            _ds.Display(iAddSkillDamageAttrPercent, "iAddSkillDamageAttrPercent");
            _ds.Display(iCastType, "iCastType");
            _ds.Display(iHurtType, "iHurtType");
            _ds.Display(iChangeCond1Type, "iChangeCond1Type");
            _ds.Display(sChangeCond1Param, "sChangeCond1Param");
            _ds.Display(iChangeCond1SkillId, "iChangeCond1SkillId");
            _ds.Display(iChangeCond2Type, "iChangeCond2Type");
            _ds.Display(sChangeCond2Param, "sChangeCond2Param");
            _ds.Display(iChangeCond2SkillId, "iChangeCond2SkillId");
            _ds.Display(iExpend1Type, "iExpend1Type");
            _ds.Display(iExpend1P1, "iExpend1P1");
            _ds.Display(iExpend1P2, "iExpend1P2");
            _ds.Display(iExpend2Type, "iExpend2Type");
            _ds.Display(iExpend2P1, "iExpend2P1");
            _ds.Display(iExpend2P2, "iExpend2P2");
            _ds.Display(iCondition1Type, "iCondition1Type");
            _ds.Display(iCondition1P1, "iCondition1P1");
            _ds.Display(iCondition1P2, "iCondition1P2");
            _ds.Display(iCondition2Type, "iCondition2Type");
            _ds.Display(iCondition2P1, "iCondition2P1");
            _ds.Display(iCondition2P2, "iCondition2P2");
            _ds.Display(iMagicShow, "iMagicShow");
            _ds.Display(iSkillcShow, "iSkillcShow");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sBrief, "sBrief");
            _ds.Display(iSkillType, "iSkillType");
            _ds.Display(iBuffId, "iBuffId");
            _ds.Display(sShowTagName, "sShowTagName");
            _ds.Display(iShowTagType, "iShowTagType");
            _ds.Display(sDesc2, "sDesc2");
            _ds.Display(sBriefValue, "sBriefValue");
            _ds.Display(iDamageReplyHpPercent, "iDamageReplyHpPercent");
            _ds.Display(iDeadlyStrikeChangePercent, "iDeadlyStrikeChangePercent");
            _ds.Display(iDeadlyStrikeChangeAttrType, "iDeadlyStrikeChangeAttrType");
            _ds.Display(iDeadlyStrikeChangeValue, "iDeadlyStrikeChangeValue");
            _ds.Display(iIsUseHurtSkill, "iIsUseHurtSkill");
            _ds.Display(iReplyHp, "iReplyHp");
            _ds.Display(iReplyMp, "iReplyMp");
            _ds.Display(iReplyHpValueType, "iReplyHpValueType");
            _ds.Display(iReplyMpValueType, "iReplyMpValueType");
            _ds.Display(sReplyHpEffect, "sReplyHpEffect");
            _ds.Display(iReplyHpEffectPos, "iReplyHpEffectPos");
            _ds.Display(iSeckillHpPercent, "iSeckillHpPercent");
            _ds.Display(iIsAPAdd, "iIsAPAdd");
            _ds.Display(iIsADAdd, "iIsADAdd");
            _ds.Display(iReplyHpIsAPAdd, "iReplyHpIsAPAdd");
            _ds.Display(iSkillDamageRate, "iSkillDamageRate");
            _ds.Display(iAddSkillDamageTargetAttrType, "iAddSkillDamageTargetAttrType");
            _ds.Display(iAddSkillDamageTargetAttrPercent, "iAddSkillDamageTargetAttrPercent");
            _ds.Display(iAddSkillDamageIsAddAp, "iAddSkillDamageIsAddAp");
            _ds.Display(iAddSkillDamageTargetIsAddAp, "iAddSkillDamageTargetIsAddAp");
            _ds.Display(iIsCanPlayOnUnControl, "iIsCanPlayOnUnControl");
            _ds.Display(sSkillDamageStr, "sSkillDamageStr");
            _ds.Display(iIsUseDurationTarget, "iIsUseDurationTarget");
            _ds.Display(iIsCheckSkillImmune, "iIsCheckSkillImmune");
            _ds.Display(iIsTextAdd, "iIsTextAdd");
            _ds.Display(iIsTextTween, "iIsTextTween");
            _ds.Display(iGroupId, "iGroupId");
            _ds.Display(iSetId, "iSetId");
            _ds.Display(iDeadlyStrikeChangeAttrIsWhole, "iDeadlyStrikeChangeAttrIsWhole");
            _ds.Display(iIsAddCommonCrit, "iIsAddCommonCrit");
            _ds.Display(iDamageRecordMask, "iDamageRecordMask");
            _ds.Display(iAddFixedValue, "iAddFixedValue");
            _ds.Display(iIsTextAddDiffHero, "iIsTextAddDiffHero");
            _ds.Display(iIsIgnoreCalFixedHurtReduce, "iIsIgnoreCalFixedHurtReduce");
            _ds.Display(iIsCanKill, "iIsCanKill");
            _ds.Display(iDeadlyStrikeChangeNumLimit, "iDeadlyStrikeChangeNumLimit");
            _ds.Display(iIsCommonSkillCheckSkillImmune, "iIsCommonSkillCheckSkillImmune");
            _ds.Display(iSkillShowNumSource, "iSkillShowNumSource");
            _ds.Display(iSkillShowNumSourceParam1, "iSkillShowNumSourceParam1");
            _ds.Display(iSkillShowNumSourceParam2, "iSkillShowNumSourceParam2");
            _ds.Display(iSelectorExtendParam, "iSelectorExtendParam");
            _ds.Display(iNotHeroAttackType, "iNotHeroAttackType");
            _ds.Display(sBuffRecord, "sBuffRecord");
            _ds.Display(iSkillSectionNum, "iSkillSectionNum");
            _ds.Display(iIgnoreResistanceValue, "iIgnoreResistanceValue");
            _ds.Display(iIsIgnoreResistancePercent, "iIsIgnoreResistancePercent");
            _ds.Display(iLiveCounter, "iLiveCounter");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sDesc = "";
            iSelectorType = 0;
            sSelectorParam = "";
            iCD = 0;
            iSkillDuration = 0;
            iSkillDamage = 0;
            iSkillCrit = 0;
            iSkillCritDamage = 0;
            iAddSkillDamageAttrType = 0;
            iAddSkillDamageAttrPercent = 0;
            iCastType = 0;
            iHurtType = 0;
            iChangeCond1Type = 0;
            sChangeCond1Param = "";
            iChangeCond1SkillId = 0;
            iChangeCond2Type = 0;
            sChangeCond2Param = "";
            iChangeCond2SkillId = 0;
            iExpend1Type = 0;
            iExpend1P1 = 0;
            iExpend1P2 = 0;
            iExpend2Type = 0;
            iExpend2P1 = 0;
            iExpend2P2 = 0;
            iCondition1Type = 0;
            iCondition1P1 = 0;
            iCondition1P2 = 0;
            iCondition2Type = 0;
            iCondition2P1 = 0;
            iCondition2P2 = 0;
            iMagicShow = 0;
            iSkillcShow = 0;
            sIcon = "";
            sBrief = "";
            iSkillType = 0;
            iBuffId = 0;
            sShowTagName = "";
            iShowTagType = 0;
            sDesc2 = "";
            sBriefValue = "";
            iDamageReplyHpPercent = 0;
            iDeadlyStrikeChangePercent = 0;
            iDeadlyStrikeChangeAttrType = 0;
            iDeadlyStrikeChangeValue = 0;
            iIsUseHurtSkill = 0;
            iReplyHp = 0;
            iReplyMp = 0;
            iReplyHpValueType = 0;
            iReplyMpValueType = 0;
            sReplyHpEffect = "";
            iReplyHpEffectPos = 0;
            iSeckillHpPercent = 0;
            iIsAPAdd = 0;
            iIsADAdd = 0;
            iReplyHpIsAPAdd = 0;
            iSkillDamageRate = 0;
            iAddSkillDamageTargetAttrType = 0;
            iAddSkillDamageTargetAttrPercent = 0;
            iAddSkillDamageIsAddAp = 0;
            iAddSkillDamageTargetIsAddAp = 0;
            iIsCanPlayOnUnControl = 0;
            sSkillDamageStr = "";
            iIsUseDurationTarget = 0;
            iIsCheckSkillImmune = 0;
            iIsTextAdd = 0;
            iIsTextTween = 0;
            iGroupId = 0;
            iSetId = 0;
            iDeadlyStrikeChangeAttrIsWhole = 0;
            iIsAddCommonCrit = 0;
            iDamageRecordMask = 0;
            iAddFixedValue = 0;
            iIsTextAddDiffHero = 0;
            iIsIgnoreCalFixedHurtReduce = 0;
            iIsCanKill = 0;
            iDeadlyStrikeChangeNumLimit = 0;
            iIsCommonSkillCheckSkillImmune = 0;
            iSkillShowNumSource = 0;
            iSkillShowNumSourceParam1 = 0;
            iSkillShowNumSourceParam2 = 0;
            iSelectorExtendParam = 0;
            iNotHeroAttackType = 0;
            sBuffRecord = "";
            iSkillSectionNum = 0;
            iIgnoreResistanceValue = 0;
            iIsIgnoreResistancePercent = 0;
            iLiveCounter = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACGSkill_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sDesc = this.sDesc;
            copied.iSelectorType = this.iSelectorType;
            copied.sSelectorParam = this.sSelectorParam;
            copied.iCD = this.iCD;
            copied.iSkillDuration = this.iSkillDuration;
            copied.iSkillDamage = this.iSkillDamage;
            copied.iSkillCrit = this.iSkillCrit;
            copied.iSkillCritDamage = this.iSkillCritDamage;
            copied.iAddSkillDamageAttrType = this.iAddSkillDamageAttrType;
            copied.iAddSkillDamageAttrPercent = this.iAddSkillDamageAttrPercent;
            copied.iCastType = this.iCastType;
            copied.iHurtType = this.iHurtType;
            copied.iChangeCond1Type = this.iChangeCond1Type;
            copied.sChangeCond1Param = this.sChangeCond1Param;
            copied.iChangeCond1SkillId = this.iChangeCond1SkillId;
            copied.iChangeCond2Type = this.iChangeCond2Type;
            copied.sChangeCond2Param = this.sChangeCond2Param;
            copied.iChangeCond2SkillId = this.iChangeCond2SkillId;
            copied.iExpend1Type = this.iExpend1Type;
            copied.iExpend1P1 = this.iExpend1P1;
            copied.iExpend1P2 = this.iExpend1P2;
            copied.iExpend2Type = this.iExpend2Type;
            copied.iExpend2P1 = this.iExpend2P1;
            copied.iExpend2P2 = this.iExpend2P2;
            copied.iCondition1Type = this.iCondition1Type;
            copied.iCondition1P1 = this.iCondition1P1;
            copied.iCondition1P2 = this.iCondition1P2;
            copied.iCondition2Type = this.iCondition2Type;
            copied.iCondition2P1 = this.iCondition2P1;
            copied.iCondition2P2 = this.iCondition2P2;
            copied.iMagicShow = this.iMagicShow;
            copied.iSkillcShow = this.iSkillcShow;
            copied.sIcon = this.sIcon;
            copied.sBrief = this.sBrief;
            copied.iSkillType = this.iSkillType;
            copied.iBuffId = this.iBuffId;
            copied.sShowTagName = this.sShowTagName;
            copied.iShowTagType = this.iShowTagType;
            copied.sDesc2 = this.sDesc2;
            copied.sBriefValue = this.sBriefValue;
            copied.iDamageReplyHpPercent = this.iDamageReplyHpPercent;
            copied.iDeadlyStrikeChangePercent = this.iDeadlyStrikeChangePercent;
            copied.iDeadlyStrikeChangeAttrType = this.iDeadlyStrikeChangeAttrType;
            copied.iDeadlyStrikeChangeValue = this.iDeadlyStrikeChangeValue;
            copied.iIsUseHurtSkill = this.iIsUseHurtSkill;
            copied.iReplyHp = this.iReplyHp;
            copied.iReplyMp = this.iReplyMp;
            copied.iReplyHpValueType = this.iReplyHpValueType;
            copied.iReplyMpValueType = this.iReplyMpValueType;
            copied.sReplyHpEffect = this.sReplyHpEffect;
            copied.iReplyHpEffectPos = this.iReplyHpEffectPos;
            copied.iSeckillHpPercent = this.iSeckillHpPercent;
            copied.iIsAPAdd = this.iIsAPAdd;
            copied.iIsADAdd = this.iIsADAdd;
            copied.iReplyHpIsAPAdd = this.iReplyHpIsAPAdd;
            copied.iSkillDamageRate = this.iSkillDamageRate;
            copied.iAddSkillDamageTargetAttrType = this.iAddSkillDamageTargetAttrType;
            copied.iAddSkillDamageTargetAttrPercent = this.iAddSkillDamageTargetAttrPercent;
            copied.iAddSkillDamageIsAddAp = this.iAddSkillDamageIsAddAp;
            copied.iAddSkillDamageTargetIsAddAp = this.iAddSkillDamageTargetIsAddAp;
            copied.iIsCanPlayOnUnControl = this.iIsCanPlayOnUnControl;
            copied.sSkillDamageStr = this.sSkillDamageStr;
            copied.iIsUseDurationTarget = this.iIsUseDurationTarget;
            copied.iIsCheckSkillImmune = this.iIsCheckSkillImmune;
            copied.iIsTextAdd = this.iIsTextAdd;
            copied.iIsTextTween = this.iIsTextTween;
            copied.iGroupId = this.iGroupId;
            copied.iSetId = this.iSetId;
            copied.iDeadlyStrikeChangeAttrIsWhole = this.iDeadlyStrikeChangeAttrIsWhole;
            copied.iIsAddCommonCrit = this.iIsAddCommonCrit;
            copied.iDamageRecordMask = this.iDamageRecordMask;
            copied.iAddFixedValue = this.iAddFixedValue;
            copied.iIsTextAddDiffHero = this.iIsTextAddDiffHero;
            copied.iIsIgnoreCalFixedHurtReduce = this.iIsIgnoreCalFixedHurtReduce;
            copied.iIsCanKill = this.iIsCanKill;
            copied.iDeadlyStrikeChangeNumLimit = this.iDeadlyStrikeChangeNumLimit;
            copied.iIsCommonSkillCheckSkillImmune = this.iIsCommonSkillCheckSkillImmune;
            copied.iSkillShowNumSource = this.iSkillShowNumSource;
            copied.iSkillShowNumSourceParam1 = this.iSkillShowNumSourceParam1;
            copied.iSkillShowNumSourceParam2 = this.iSkillShowNumSourceParam2;
            copied.iSelectorExtendParam = this.iSelectorExtendParam;
            copied.iNotHeroAttackType = this.iNotHeroAttackType;
            copied.sBuffRecord = this.sBuffRecord;
            copied.iSkillSectionNum = this.iSkillSectionNum;
            copied.iIgnoreResistanceValue = this.iIgnoreResistanceValue;
            copied.iIsIgnoreResistancePercent = this.iIsIgnoreResistancePercent;
            copied.iLiveCounter = this.iLiveCounter;
            return copied;
        }
    }
}

