//所在的Excel 【ACG_BattlePass.xlsm】
//****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TEnlighten_Client : Wup.Jce.JceStruct
    {
        public int iRank = 0;

        public int iEnergy = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRank, 0);
            _os.Write(iEnergy, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRank = (int) _is.Read(iRank, 0, false);

            iEnergy = (int) _is.Read(iEnergy, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRank, "iRank");
            _ds.Display(iEnergy, "iEnergy");
        }

        public override void Clear()
        {
            iRank = 0;
            iEnergy = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TEnlighten_Client();
            copied.iRank = this.iRank;
            copied.iEnergy = this.iEnergy;
            return copied;
        }
    }
}

