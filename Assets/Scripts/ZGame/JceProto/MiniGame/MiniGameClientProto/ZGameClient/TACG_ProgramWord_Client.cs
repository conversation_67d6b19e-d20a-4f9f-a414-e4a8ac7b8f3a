//所在的Excel 【ACG_HandbookRule.xlsm】
//**************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ProgramWord_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iNumber = 0;

        public string sTitle = "";

        public string sText = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iNumber, 1);
            _os.Write(sTitle, 2);
            _os.Write(sText, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iNumber = (int) _is.Read(iNumber, 1, false);

            sTitle = (string) _is.Read(sTitle, 2, false);

            sText = (string) _is.Read(sText, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iNumber, "iNumber");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(sText, "sText");
        }

        public override void Clear()
        {
            iID = 0;
            iNumber = 0;
            sTitle = "";
            sText = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ProgramWord_Client();
            copied.iID = this.iID;
            copied.iNumber = this.iNumber;
            copied.sTitle = this.sTitle;
            copied.sText = this.sText;
            return copied;
        }
    }
}

