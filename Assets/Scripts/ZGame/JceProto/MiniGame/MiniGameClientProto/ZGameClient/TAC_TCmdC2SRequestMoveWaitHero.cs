// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdC2SRequestMoveWaitHero : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public TAC_WaitHero stWaitHero {get; set;} 

        public TAC_WaitPos stWaitPos {get; set;} 

        int _iSourceWaitAreaType = 0;
        public int iSourceWaitAreaType
        {
            get
            {
                 return _iSourceWaitAreaType;
            }
            set
            {
                _iSourceWaitAreaType = value; 
            }
        }

        int _iTargetWaitAreaType = 0;
        public int iTargetWaitAreaType
        {
            get
            {
                 return _iTargetWaitAreaType;
            }
            set
            {
                _iTargetWaitAreaType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(stWaitHero, 1);
            _os.Write(stWaitPos, 2);
            _os.Write(iSourceWaitAreaType, 3);
            _os.Write(iTargetWaitAreaType, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            stWaitHero = (TAC_WaitHero) _is.Read(stWaitHero, 1, false);

            stWaitPos = (TAC_WaitPos) _is.Read(stWaitPos, 2, false);

            iSourceWaitAreaType = (int) _is.Read(iSourceWaitAreaType, 3, false);

            iTargetWaitAreaType = (int) _is.Read(iTargetWaitAreaType, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(stWaitHero, "stWaitHero");
            _ds.Display(stWaitPos, "stWaitPos");
            _ds.Display(iSourceWaitAreaType, "iSourceWaitAreaType");
            _ds.Display(iTargetWaitAreaType, "iTargetWaitAreaType");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            stWaitHero = null;
            stWaitPos = null;
            iSourceWaitAreaType = 0;
            iTargetWaitAreaType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdC2SRequestMoveWaitHero();
            copied.i8ChairID = this.i8ChairID;
            copied.stWaitHero = (TAC_WaitHero)JceUtil.DeepClone(this.stWaitHero);
            copied.stWaitPos = (TAC_WaitPos)JceUtil.DeepClone(this.stWaitPos);
            copied.iSourceWaitAreaType = this.iSourceWaitAreaType;
            copied.iTargetWaitAreaType = this.iTargetWaitAreaType;
            return copied;
        }
    }
}

