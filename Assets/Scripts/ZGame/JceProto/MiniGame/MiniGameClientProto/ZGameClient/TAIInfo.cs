// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAIInfo : Wup.Jce.JceStruct
    {
        public long lUin = 0;

        public string sHeadUrl = "";

        public string sNickName = "";

        public int iAIDegree = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lUin, 0);
            _os.Write(sHeadUrl, 1);
            _os.Write(sNickName, 2);
            _os.Write(iAIDegree, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lUin = (long) _is.Read(lUin, 0, false);

            sHeadUrl = (string) _is.Read(sHeadUrl, 1, false);

            sNickName = (string) _is.Read(sNickName, 2, false);

            iAIDegree = (int) _is.Read(iAIDegree, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lUin, "lUin");
            _ds.Display(sHeadUrl, "sHeadUrl");
            _ds.Display(sNickName, "sNickName");
            _ds.Display(iAIDegree, "iAIDegree");
        }

        public override void Clear()
        {
            lUin = 0;
            sHeadUrl = "";
            sNickName = "";
            iAIDegree = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAIInfo();
            copied.lUin = this.lUin;
            copied.sHeadUrl = this.sHeadUrl;
            copied.sNickName = this.sNickName;
            copied.iAIDegree = this.iAIDegree;
            return copied;
        }
    }
}

