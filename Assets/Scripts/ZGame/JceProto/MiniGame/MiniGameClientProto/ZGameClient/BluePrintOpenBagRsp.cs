// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BluePrintOpenBagRsp : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _lotteryID = 0;
        public int lotteryID
        {
            get
            {
                 return _lotteryID;
            }
            set
            {
                _lotteryID = value; 
            }
        }

        int _bagIndex = 0;
        public int bagIndex
        {
            get
            {
                 return _bagIndex;
            }
            set
            {
                _bagIndex = value; 
            }
        }

        public TItemInfo itemInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(lotteryID, 1);
            _os.Write(bagIndex, 2);
            _os.Write(itemInfo, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            lotteryID = (int) _is.Read(lotteryID, 1, false);

            bagIndex = (int) _is.Read(bagIndex, 2, false);

            itemInfo = (TItemInfo) _is.Read(itemInfo, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(lotteryID, "lotteryID");
            _ds.Display(bagIndex, "bagIndex");
            _ds.Display(itemInfo, "itemInfo");
        }

        public override void Clear()
        {
            iRet = 0;
            lotteryID = 0;
            bagIndex = 0;
            itemInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BluePrintOpenBagRsp();
            copied.iRet = this.iRet;
            copied.lotteryID = this.lotteryID;
            copied.bagIndex = this.bagIndex;
            copied.itemInfo = (TItemInfo)JceUtil.DeepClone(this.itemInfo);
            return copied;
        }
    }
}

