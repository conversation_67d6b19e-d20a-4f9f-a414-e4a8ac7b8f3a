// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_CommonTag_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iViewID = 0;

        public int iSubViewID = 0;

        public int iTagID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iViewID, 1);
            _os.Write(iSubViewID, 2);
            _os.Write(iTagID, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iViewID = (int) _is.Read(iViewID, 1, false);

            iSubViewID = (int) _is.Read(iSubViewID, 2, false);

            iTagID = (int) _is.Read(iTagID, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iViewID, "iViewID");
            _ds.Display(iSubViewID, "iSubViewID");
            _ds.Display(iTagID, "iTagID");
        }

        public override void Clear()
        {
            iID = 0;
            iViewID = 0;
            iSubViewID = 0;
            iTagID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_CommonTag_Client();
            copied.iID = this.iID;
            copied.iViewID = this.iViewID;
            copied.iSubViewID = this.iSubViewID;
            copied.iTagID = this.iTagID;
            return copied;
        }
    }
}

