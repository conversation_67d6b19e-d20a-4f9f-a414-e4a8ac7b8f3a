//所在的Excel 【ACG_CombatEffect.xlsm】
//**************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_PrimeData_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int isetID = 0;

        public string sEffectLevel = "";

        public int iEquipCfg = 0;

        public int iCheckType = 0;

        public int iCheckID = 0;

        public int iAreaError = 0;

        public int iTimingError = 0;

        public int iHeroError = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(isetID, 1);
            _os.Write(sEffectLevel, 2);
            _os.Write(iEquipCfg, 3);
            _os.Write(iCheckType, 4);
            _os.Write(iCheckID, 5);
            _os.Write(iAreaError, 6);
            _os.Write(iTimingError, 7);
            _os.Write(iHeroError, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            isetID = (int) _is.Read(isetID, 1, false);

            sEffectLevel = (string) _is.Read(sEffectLevel, 2, false);

            iEquipCfg = (int) _is.Read(iEquipCfg, 3, false);

            iCheckType = (int) _is.Read(iCheckType, 4, false);

            iCheckID = (int) _is.Read(iCheckID, 5, false);

            iAreaError = (int) _is.Read(iAreaError, 6, false);

            iTimingError = (int) _is.Read(iTimingError, 7, false);

            iHeroError = (int) _is.Read(iHeroError, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(isetID, "isetID");
            _ds.Display(sEffectLevel, "sEffectLevel");
            _ds.Display(iEquipCfg, "iEquipCfg");
            _ds.Display(iCheckType, "iCheckType");
            _ds.Display(iCheckID, "iCheckID");
            _ds.Display(iAreaError, "iAreaError");
            _ds.Display(iTimingError, "iTimingError");
            _ds.Display(iHeroError, "iHeroError");
        }

        public override void Clear()
        {
            iID = 0;
            isetID = 0;
            sEffectLevel = "";
            iEquipCfg = 0;
            iCheckType = 0;
            iCheckID = 0;
            iAreaError = 0;
            iTimingError = 0;
            iHeroError = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_PrimeData_Client();
            copied.iID = this.iID;
            copied.isetID = this.isetID;
            copied.sEffectLevel = this.sEffectLevel;
            copied.iEquipCfg = this.iEquipCfg;
            copied.iCheckType = this.iCheckType;
            copied.iCheckID = this.iCheckID;
            copied.iAreaError = this.iAreaError;
            copied.iTimingError = this.iTimingError;
            copied.iHeroError = this.iHeroError;
            return copied;
        }
    }
}

