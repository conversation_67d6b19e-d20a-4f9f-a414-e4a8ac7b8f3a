// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ApexTierPromptNotify : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<int, ApexTierPromptNotifyData> sceneType2Data;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(sceneType2Data, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            sceneType2Data = (TKFrame.TKDictionary<int, ApexTierPromptNotifyData>) _is.Read(sceneType2Data, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(sceneType2Data, "sceneType2Data");
        }

        public override void Clear()
        {
            sceneType2Data = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ApexTierPromptNotify();
            copied.sceneType2Data = (TKFrame.TKDictionary<int, ApexTierPromptNotifyData>)JceUtil.DeepClone(this.sceneType2Data);
            return copied;
        }
    }
}

