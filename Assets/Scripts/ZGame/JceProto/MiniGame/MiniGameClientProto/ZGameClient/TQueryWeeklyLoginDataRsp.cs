// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TQueryWeeklyLoginDataRsp : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        public TWeeklyLogin_Server config {get; set;} 

        public TKFrame.TKDictionary<int, int> mapSignStatus {get; set;} 

        bool _bRewardFetched = false;
        public bool bRewardFetched
        {
            get
            {
                 return _bRewardFetched;
            }
            set
            {
                _bRewardFetched = value; 
            }
        }

        public TActivity_Server stActivityConf {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(config, 1);
            _os.Write(mapSignStatus, 2);
            _os.Write(bRewardFetched, 3);
            _os.Write(stActivityConf, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            config = (TWeeklyLogin_Server) _is.Read(config, 1, false);

            mapSignStatus = (TKFrame.TKDictionary<int, int>) _is.Read(mapSignStatus, 2, false);

            bRewardFetched = (bool) _is.Read(bRewardFetched, 3, false);

            stActivityConf = (TActivity_Server) _is.Read(stActivityConf, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(config, "config");
            _ds.Display(mapSignStatus, "mapSignStatus");
            _ds.Display(bRewardFetched, "bRewardFetched");
            _ds.Display(stActivityConf, "stActivityConf");
        }

        public override void Clear()
        {
            iRet = 0;
            config = null;
            mapSignStatus = null;
            bRewardFetched = false;
            stActivityConf = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TQueryWeeklyLoginDataRsp();
            copied.iRet = this.iRet;
            copied.config = (TWeeklyLogin_Server)JceUtil.DeepClone(this.config);
            copied.mapSignStatus = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapSignStatus);
            copied.bRewardFetched = this.bRewardFetched;
            copied.stActivityConf = (TActivity_Server)JceUtil.DeepClone(this.stActivityConf);
            return copied;
        }
    }
}

