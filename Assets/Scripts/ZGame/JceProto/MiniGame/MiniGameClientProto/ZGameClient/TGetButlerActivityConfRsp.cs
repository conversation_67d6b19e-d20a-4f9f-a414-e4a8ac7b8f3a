// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetButlerActivityConfRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<TBulterActivityConf> vecConf;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecConf, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecConf = (System.Collections.Generic.List<TBulterActivityConf>) _is.Read(vecConf, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecConf, "vecConf");
        }

        public override void Clear()
        {
            iRet = 0;
            vecConf = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetButlerActivityConfRsp();
            copied.iRet = this.iRet;
            copied.vecConf = (System.Collections.Generic.List<TBulterActivityConf>)JceUtil.DeepClone(this.vecConf);
            return copied;
        }
    }
}

