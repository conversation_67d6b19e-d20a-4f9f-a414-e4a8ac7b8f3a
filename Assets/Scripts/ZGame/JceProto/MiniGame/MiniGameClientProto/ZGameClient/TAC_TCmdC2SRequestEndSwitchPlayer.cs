// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdC2SRequestEndSwitchPlayer : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
        }

        public override void Clear()
        {
            i8ChairID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdC2SRequestEndSwitchPlayer();
            copied.i8ChairID = this.i8ChairID;
            return copied;
        }
    }
}

