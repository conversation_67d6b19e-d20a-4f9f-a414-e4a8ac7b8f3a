//所在的Excel 【ACG_BattleTipsHelper.xlsm】
//**********************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_BattleTipsSwitch_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sText = "";

        public int iPlanID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sText, 1);
            _os.Write(iPlanID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sText = (string) _is.Read(sText, 1, false);

            iPlanID = (int) _is.Read(iPlanID, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sText, "sText");
            _ds.Display(iPlanID, "iPlanID");
        }

        public override void Clear()
        {
            iID = 0;
            sText = "";
            iPlanID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_BattleTipsSwitch_Client();
            copied.iID = this.iID;
            copied.sText = this.sText;
            copied.iPlanID = this.iPlanID;
            return copied;
        }
    }
}

