//所在的Excel 【ModuleControl.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TModuleSwitch_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iUserLabel = 0;

        public string sCloseBeginTime = "";

        public string sCloseEndTime = "";

        public string sName = "";

        public int iServerID = 0;

        public int iType = 0;

        public int iTierLimitMin = 0;

        public int iTierLimitMax = 0;

        public int iLevelLimit = 0;

        public int iUnLockLevel = 0;

        public int iUnLockTipStatus = 0;

        public string sTips = "";

        public string sIcon = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iUserLabel, 1);
            _os.Write(sCloseBeginTime, 2);
            _os.Write(sCloseEndTime, 3);
            _os.Write(sName, 4);
            _os.Write(iServerID, 5);
            _os.Write(iType, 6);
            _os.Write(iTierLimitMin, 7);
            _os.Write(iTierLimitMax, 8);
            _os.Write(iLevelLimit, 9);
            _os.Write(iUnLockLevel, 10);
            _os.Write(iUnLockTipStatus, 11);
            _os.Write(sTips, 12);
            _os.Write(sIcon, 13);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iUserLabel = (int) _is.Read(iUserLabel, 1, false);

            sCloseBeginTime = (string) _is.Read(sCloseBeginTime, 2, false);

            sCloseEndTime = (string) _is.Read(sCloseEndTime, 3, false);

            sName = (string) _is.Read(sName, 4, false);

            iServerID = (int) _is.Read(iServerID, 5, false);

            iType = (int) _is.Read(iType, 6, false);

            iTierLimitMin = (int) _is.Read(iTierLimitMin, 7, false);

            iTierLimitMax = (int) _is.Read(iTierLimitMax, 8, false);

            iLevelLimit = (int) _is.Read(iLevelLimit, 9, false);

            iUnLockLevel = (int) _is.Read(iUnLockLevel, 10, false);

            iUnLockTipStatus = (int) _is.Read(iUnLockTipStatus, 11, false);

            sTips = (string) _is.Read(sTips, 12, false);

            sIcon = (string) _is.Read(sIcon, 13, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iUserLabel, "iUserLabel");
            _ds.Display(sCloseBeginTime, "sCloseBeginTime");
            _ds.Display(sCloseEndTime, "sCloseEndTime");
            _ds.Display(sName, "sName");
            _ds.Display(iServerID, "iServerID");
            _ds.Display(iType, "iType");
            _ds.Display(iTierLimitMin, "iTierLimitMin");
            _ds.Display(iTierLimitMax, "iTierLimitMax");
            _ds.Display(iLevelLimit, "iLevelLimit");
            _ds.Display(iUnLockLevel, "iUnLockLevel");
            _ds.Display(iUnLockTipStatus, "iUnLockTipStatus");
            _ds.Display(sTips, "sTips");
            _ds.Display(sIcon, "sIcon");
        }

        public override void Clear()
        {
            iID = 0;
            iUserLabel = 0;
            sCloseBeginTime = "";
            sCloseEndTime = "";
            sName = "";
            iServerID = 0;
            iType = 0;
            iTierLimitMin = 0;
            iTierLimitMax = 0;
            iLevelLimit = 0;
            iUnLockLevel = 0;
            iUnLockTipStatus = 0;
            sTips = "";
            sIcon = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TModuleSwitch_Client();
            copied.iID = this.iID;
            copied.iUserLabel = this.iUserLabel;
            copied.sCloseBeginTime = this.sCloseBeginTime;
            copied.sCloseEndTime = this.sCloseEndTime;
            copied.sName = this.sName;
            copied.iServerID = this.iServerID;
            copied.iType = this.iType;
            copied.iTierLimitMin = this.iTierLimitMin;
            copied.iTierLimitMax = this.iTierLimitMax;
            copied.iLevelLimit = this.iLevelLimit;
            copied.iUnLockLevel = this.iUnLockLevel;
            copied.iUnLockTipStatus = this.iUnLockTipStatus;
            copied.sTips = this.sTips;
            copied.sIcon = this.sIcon;
            return copied;
        }
    }
}

