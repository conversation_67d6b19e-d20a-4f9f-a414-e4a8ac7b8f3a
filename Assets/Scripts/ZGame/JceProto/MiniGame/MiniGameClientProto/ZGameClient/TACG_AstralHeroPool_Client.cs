// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_AstralHeroPool_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iQuality = 0;

        public string sHeroPool = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iQuality, 1);
            _os.Write(sHeroPool, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iQuality = (int) _is.Read(iQuality, 1, false);

            sHeroPool = (string) _is.Read(sHeroPool, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iQuality, "iQuality");
            _ds.Display(sHeroPool, "sHeroPool");
        }

        public override void Clear()
        {
            iID = 0;
            iQuality = 0;
            sHeroPool = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_AstralHeroPool_Client();
            copied.iID = this.iID;
            copied.iQuality = this.iQuality;
            copied.sHeroPool = this.sHeroPool;
            return copied;
        }
    }
}

