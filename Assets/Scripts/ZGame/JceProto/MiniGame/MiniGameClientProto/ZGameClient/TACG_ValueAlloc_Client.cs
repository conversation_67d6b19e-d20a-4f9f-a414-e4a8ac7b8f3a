// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ValueAlloc_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iLootPlan = 0;

        public int iValue = 0;

        public string sStageIDs = "";

        public int iCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLootPlan, 1);
            _os.Write(iValue, 2);
            _os.Write(sStageIDs, 3);
            _os.Write(iCount, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLootPlan = (int) _is.Read(iLootPlan, 1, false);

            iValue = (int) _is.Read(iValue, 2, false);

            sStageIDs = (string) _is.Read(sStageIDs, 3, false);

            iCount = (int) _is.Read(iCount, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLootPlan, "iLootPlan");
            _ds.Display(iValue, "iValue");
            _ds.Display(sStageIDs, "sStageIDs");
            _ds.Display(iCount, "iCount");
        }

        public override void Clear()
        {
            iID = 0;
            iLootPlan = 0;
            iValue = 0;
            sStageIDs = "";
            iCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ValueAlloc_Client();
            copied.iID = this.iID;
            copied.iLootPlan = this.iLootPlan;
            copied.iValue = this.iValue;
            copied.sStageIDs = this.sStageIDs;
            copied.iCount = this.iCount;
            return copied;
        }
    }
}

