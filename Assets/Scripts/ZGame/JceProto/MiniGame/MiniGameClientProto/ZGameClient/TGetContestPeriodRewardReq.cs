// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetContestPeriodRewardReq : Wup.Jce.JceStruct
    {
        public int iContestID = 0;

        public TMidasTokenInfo stMidasToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iContestID, 0);
            _os.Write(stMidasToken, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iContestID = (int) _is.Read(iContestID, 0, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iContestID, "iContestID");
            _ds.Display(stMidasToken, "stMidasToken");
        }

        public override void Clear()
        {
            iContestID = 0;
            stMidasToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetContestPeriodRewardReq();
            copied.iContestID = this.iContestID;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            return copied;
        }
    }
}

