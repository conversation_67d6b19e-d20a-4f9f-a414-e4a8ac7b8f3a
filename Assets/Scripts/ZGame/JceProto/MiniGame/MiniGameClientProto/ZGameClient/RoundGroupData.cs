// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class RoundGroupData : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<long, RoundPlayerData> roundPlayerDataList;

        public int iRandomSeed = 0;

        public System.Collections.Generic.List<OpponentMatch> opponentMatch;

        public int groupID = 0;

        public System.Collections.Generic.List<OpponentMatch> allOpponentMatch;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(roundPlayerDataList, 0);
            _os.Write(iRandomSeed, 1);
            _os.Write(opponentMatch, 2);
            _os.Write(groupID, 3);
            _os.Write(allOpponentMatch, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            roundPlayerDataList = (TKFrame.TKDictionary<long, RoundPlayerData>) _is.Read(roundPlayerDataList, 0, false);

            iRandomSeed = (int) _is.Read(iRandomSeed, 1, false);

            opponentMatch = (System.Collections.Generic.List<OpponentMatch>) _is.Read(opponentMatch, 2, false);

            groupID = (int) _is.Read(groupID, 3, false);

            allOpponentMatch = (System.Collections.Generic.List<OpponentMatch>) _is.Read(allOpponentMatch, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(roundPlayerDataList, "roundPlayerDataList");
            _ds.Display(iRandomSeed, "iRandomSeed");
            _ds.Display(opponentMatch, "opponentMatch");
            _ds.Display(groupID, "groupID");
            _ds.Display(allOpponentMatch, "allOpponentMatch");
        }

        public override void Clear()
        {
            roundPlayerDataList = null;
            iRandomSeed = 0;
            opponentMatch = null;
            groupID = 0;
            allOpponentMatch = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new RoundGroupData();
            copied.roundPlayerDataList = (TKFrame.TKDictionary<long, RoundPlayerData>)JceUtil.DeepClone(this.roundPlayerDataList);
            copied.iRandomSeed = this.iRandomSeed;
            copied.opponentMatch = (System.Collections.Generic.List<OpponentMatch>)JceUtil.DeepClone(this.opponentMatch);
            copied.groupID = this.groupID;
            copied.allOpponentMatch = (System.Collections.Generic.List<OpponentMatch>)JceUtil.DeepClone(this.allOpponentMatch);
            return copied;
        }
    }
}

