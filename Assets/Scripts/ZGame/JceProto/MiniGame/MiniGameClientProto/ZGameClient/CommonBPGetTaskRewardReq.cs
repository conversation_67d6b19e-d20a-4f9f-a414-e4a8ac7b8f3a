// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CommonBPGetTaskRewardReq : Wup.Jce.JceStruct
    {
        public int iBPID = 0;

        public int iIndex = 0;

        public TMidasTokenInfo stMidasToken;

        public int iTaskType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iBPID, 0);
            _os.Write(iIndex, 1);
            _os.Write(stMidasToken, 2);
            _os.Write(iTaskType, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iBPID = (int) _is.Read(iBPID, 0, false);

            iIndex = (int) _is.Read(iIndex, 1, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 2, false);

            iTaskType = (int) _is.Read(iTaskType, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iBPID, "iBPID");
            _ds.Display(iIndex, "iIndex");
            _ds.Display(stMidasToken, "stMidasToken");
            _ds.Display(iTaskType, "iTaskType");
        }

        public override void Clear()
        {
            iBPID = 0;
            iIndex = 0;
            stMidasToken = null;
            iTaskType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CommonBPGetTaskRewardReq();
            copied.iBPID = this.iBPID;
            copied.iIndex = this.iIndex;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            copied.iTaskType = this.iTaskType;
            return copied;
        }
    }
}

