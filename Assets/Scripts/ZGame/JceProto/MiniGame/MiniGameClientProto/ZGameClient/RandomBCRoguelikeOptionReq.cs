// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class RandomBCRoguelikeOptionReq : Wup.Jce.JceStruct
    {
        public int optionType = 0;

        public int gameModuleID = 0;

        public int levelID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(optionType, 0);
            _os.Write(gameModuleID, 1);
            _os.Write(levelID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            optionType = (int) _is.Read(optionType, 0, false);

            gameModuleID = (int) _is.Read(gameModuleID, 1, false);

            levelID = (int) _is.Read(levelID, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(optionType, "optionType");
            _ds.Display(gameModuleID, "gameModuleID");
            _ds.Display(levelID, "levelID");
        }

        public override void Clear()
        {
            optionType = 0;
            gameModuleID = 0;
            levelID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new RandomBCRoguelikeOptionReq();
            copied.optionType = this.optionType;
            copied.gameModuleID = this.gameModuleID;
            copied.levelID = this.levelID;
            return copied;
        }
    }
}

