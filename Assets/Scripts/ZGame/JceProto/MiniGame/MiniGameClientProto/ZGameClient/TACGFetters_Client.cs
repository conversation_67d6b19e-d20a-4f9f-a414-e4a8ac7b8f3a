//所在的Excel 【ACG_Buff.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACGFetters_Client : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 索引ID
        /// </summary>
        public int iID = 0;

        /// <summary>
        /// 羁绊等级
        /// </summary>
        public int iLevel = 0;

        /// <summary>
        /// 羁绊类型
        /// </summary>
        public int iType = 0;

        /// <summary>
        /// 羁绊检测id
        /// </summary>
        public int icheckId = 0;

        /// <summary>
        /// 检测数量
        /// </summary>
        public int icheckNum = 0;

        /// <summary>
        /// 检测判断符号
        /// </summary>
        public int icheckOpt = 0;

        /// <summary>
        /// 目标阵营
        /// </summary>
        public int iTargetCamp = 0;

        /// <summary>
        /// 目标选择类型
        /// </summary>
        public int iTargetType = 0;

        /// <summary>
        /// 目标选择参数
        /// </summary>
        public int iTargetParam = 0;

        /// <summary>
        /// 准备阶段目标选择类型
        /// </summary>
        public int iReadyTimeTargetType = 0;

        /// <summary>
        /// 准备阶段目标选择参数
        /// </summary>
        public int iReadyTimeTargetParam = 0;

        /// <summary>
        /// 目标英雄类型过滤
        /// </summary>
        public int iTargetHeroFilter1 = 0;

        /// <summary>
        /// 目标英雄类型过滤
        /// </summary>
        public int iTargetHeroFilter2 = 0;

        /// <summary>
        /// 名称
        /// </summary>
        public string sName = "";

        /// <summary>
        /// 前置描述
        /// </summary>
        public string sPreDesc = "";

        /// <summary>
        /// 描述数值
        /// </summary>
        public string sDescValue = "";

        /// <summary>
        /// 描述
        /// </summary>
        public string sDesc = "";

        /// <summary>
        /// tips描述
        /// </summary>
        public string sTipsDesc = "";

        /// <summary>
        /// 是否准备阶段播特效
        /// </summary>
        public int iIsPlayEffInReady = 0;

        /// <summary>
        /// 是否战斗开始播特效
        /// </summary>
        public int iIsPlayEffOnStart = 0;

        /// <summary>
        /// 特效
        /// </summary>
        public string sEffectName1 = "";

        /// <summary>
        /// 特效挂点
        /// </summary>
        public int iEffectPos1 = 0;

        /// <summary>
        /// 是否持续特效
        /// </summary>
        public int iEffectIsLoop1 = 0;

        /// <summary>
        /// 特效
        /// </summary>
        public string sEffectName2 = "";

        /// <summary>
        /// 特效挂点
        /// </summary>
        public int iEffectPos2 = 0;

        /// <summary>
        /// 是否持续特效
        /// </summary>
        public int iEffectIsLoop2 = 0;

        /// <summary>
        /// 特效
        /// </summary>
        public string sEffectName3 = "";

        /// <summary>
        /// 特效挂点
        /// </summary>
        public int iEffectPos3 = 0;

        /// <summary>
        /// 是否持续特效
        /// </summary>
        public int iEffectIsLoop3 = 0;

        /// <summary>
        /// buff添加类型
        /// </summary>
        public int iBuffAddType1 = 0;

        /// <summary>
        /// BuffId
        /// </summary>
        public int iBuffId1 = 0;

        /// <summary>
        /// buff添加类型
        /// </summary>
        public int iBuffAddType2 = 0;

        /// <summary>
        /// BuffId
        /// </summary>
        public int iBuffId2 = 0;

        /// <summary>
        /// buff添加类型
        /// </summary>
        public int iBuffAddType3 = 0;

        /// <summary>
        /// BuffId
        /// </summary>
        public int iBuffId3 = 0;

        /// <summary>
        /// 非buff特殊功能类型
        /// </summary>
        public int iMoreFuncType = 0;

        /// <summary>
        /// 非buff特殊功能参数
        /// </summary>
        public int iMoreFuncParam = 0;

        /// <summary>
        /// 激活音效
        /// </summary>
        public string sActiveSound = "";

        /// <summary>
        /// 材质特效
        /// </summary>
        public string sMatShaderName = "";

        /// <summary>
        /// 非buff特殊功能参数
        /// </summary>
        public string sMoreFuncStrParam = "";

        /// <summary>
        /// 是否一直刷新buff
        /// </summary>
        public int iIsAlwaysRefeshBuff = 0;

        /// <summary>
        /// 是否激活必现播特效
        /// </summary>
        public int iIsPlayInActive = 0;

        /// <summary>
        /// 预览效果选择目标类型
        /// </summary>
        public int iPreviewEffectSelectTargetType = 0;

        /// <summary>
        /// 预览效果选择目标参数
        /// </summary>
        public string sPreviewEffectSelectTargetParam = "";

        /// <summary>
        /// 预览效果作用类型
        /// </summary>
        public int iPreviewEffectType = 0;

        /// <summary>
        /// 预览效果作用参数
        /// </summary>
        public string sPreviewEffectParam = "";

        /// <summary>
        /// 预览效果作用时间（毫秒）
        /// </summary>
        public int iPreviewEffectTime = 0;

        /// <summary>
        /// 特效
        /// </summary>
        public string sPreviewEffectName = "";

        /// <summary>
        /// 特效挂点
        /// </summary>
        public int iPreviewEffectPos = 0;

        /// <summary>
        /// 是否持续特效
        /// </summary>
        public int iPreviewEffectIsLoop = 0;

        /// <summary>
        /// 赛季
        /// </summary>
        public int iSet = 0;

        /// <summary>
        /// 特效是否绑定scale
        /// </summary>
        public int iPreviewEffectIsBindScale = 0;

        /// <summary>
        /// 特效是否绑定scale
        /// </summary>
        public int iEffectIsBindScale1 = 0;

        /// <summary>
        /// 特效是否绑定scale
        /// </summary>
        public int iEffectIsBindScale2 = 0;

        /// <summary>
        /// 特效是否绑定scale
        /// </summary>
        public int iEffectIsBindScale3 = 0;

        /// <summary>
        /// 非buff特殊功能参数
        /// </summary>
        public int iMoreFuncParam2 = 0;

        /// <summary>
        /// 改变羁绊预览目标时机类型
        /// </summary>
        public int iChangeFetterPriewTargetTime = 0;

        /// <summary>
        /// 羁绊预览效果显示时机类型
        /// </summary>
        public int iFetterPriewShowTime = 0;

        /// <summary>
        /// 是否一直刷新预览效果
        /// </summary>
        public int iIsAlwaysRefreshFetterPriew = 0;

        /// <summary>
        /// 预览效果作用参数
        /// </summary>
        public int iPreviewEffectIntParam1 = 0;

        /// <summary>
        /// 预览效果作用参数
        /// </summary>
        public int iPreviewEffectIntParam2 = 0;

        /// <summary>
        /// 预览buff
        /// </summary>
        public int iPreviewEffectBuff1 = 0;

        /// <summary>
        /// 预览buff
        /// </summary>
        public int iPreviewEffectBuff2 = 0;

        /// <summary>
        /// 是否使用
        /// </summary>
        public int iIsUse = 0;

        /// <summary>
        /// 是否只有预览目标添加buff
        /// </summary>
        public int iIsOnlyPriewTargetAddBuff = 0;

        /// <summary>
        /// 特殊功能是否准备阶段记录激活状态
        /// </summary>
        public int iIsNeedRecordReadyStage = 0;

        /// <summary>
        /// 激活音效
        /// </summary>
        public string sActiveBank = "";

        /// <summary>
        /// buff添加类型
        /// </summary>
        public int iBuffAddType4 = 0;

        /// <summary>
        /// BuffId
        /// </summary>
        public int iBuffId4 = 0;

        /// <summary>
        /// buff添加类型
        /// </summary>
        public int iBuffAddType5 = 0;

        /// <summary>
        /// BuffId
        /// </summary>
        public int iBuffId5 = 0;

        /// <summary>
        /// 激活音效
        /// </summary>
        public string sActiveHeroVoice = "";

        /// <summary>
        /// 检测前提条件类型
        /// </summary>
        public int icheckCondType = 0;

        /// <summary>
        /// 检测条件参数
        /// </summary>
        public int icheckCondParam = 0;

        /// <summary>
        /// 是否双人支援时刷新buff（默认刷新）
        /// </summary>
        public int iIsNeedRefreshBuffAssist = 0;

        /// <summary>
        /// 特殊功能在战斗阶段是否需要刷新状态
        /// </summary>
        public int iIsNeedRefreshOnBattle = 0;

        /// <summary>
        /// 实时计数器(对应一些羁绊特殊描述)
        /// </summary>
        public int iLiveCounter = 0;

        /// <summary>
        /// 额外描述
        /// </summary>
        public string sExtraDesc = "";

        /// <summary>
        /// 激活羁绊时隐藏模型时间
        /// </summary>
        public int iHideBodyTime = 0;

        /// <summary>
        /// 特效1类型
        /// </summary>
        public int iEffect1Type = 0;

        /// <summary>
        /// 特效1参数
        /// </summary>
        public string sEffect1Param = "";

        /// <summary>
        /// 特效2类型
        /// </summary>
        public int iEffect2Type = 0;

        /// <summary>
        /// 特效2参数
        /// </summary>
        public string sEffect2Param = "";

        /// <summary>
        /// 特效3类型
        /// </summary>
        public int iEffect3Type = 0;

        /// <summary>
        /// 特效3参数
        /// </summary>
        public string sEffect3Param = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLevel, 1);
            _os.Write(iType, 2);
            _os.Write(icheckId, 3);
            _os.Write(icheckNum, 4);
            _os.Write(icheckOpt, 5);
            _os.Write(iTargetCamp, 6);
            _os.Write(iTargetType, 7);
            _os.Write(iTargetParam, 8);
            _os.Write(iReadyTimeTargetType, 9);
            _os.Write(iReadyTimeTargetParam, 10);
            _os.Write(iTargetHeroFilter1, 11);
            _os.Write(iTargetHeroFilter2, 12);
            _os.Write(sName, 13);
            _os.Write(sPreDesc, 14);
            _os.Write(sDescValue, 15);
            _os.Write(sDesc, 16);
            _os.Write(sTipsDesc, 17);
            _os.Write(iIsPlayEffInReady, 18);
            _os.Write(iIsPlayEffOnStart, 19);
            _os.Write(sEffectName1, 20);
            _os.Write(iEffectPos1, 21);
            _os.Write(iEffectIsLoop1, 22);
            _os.Write(sEffectName2, 23);
            _os.Write(iEffectPos2, 24);
            _os.Write(iEffectIsLoop2, 25);
            _os.Write(sEffectName3, 26);
            _os.Write(iEffectPos3, 27);
            _os.Write(iEffectIsLoop3, 28);
            _os.Write(iBuffAddType1, 29);
            _os.Write(iBuffId1, 30);
            _os.Write(iBuffAddType2, 31);
            _os.Write(iBuffId2, 32);
            _os.Write(iBuffAddType3, 33);
            _os.Write(iBuffId3, 34);
            _os.Write(iMoreFuncType, 35);
            _os.Write(iMoreFuncParam, 36);
            _os.Write(sActiveSound, 37);
            _os.Write(sMatShaderName, 38);
            _os.Write(sMoreFuncStrParam, 39);
            _os.Write(iIsAlwaysRefeshBuff, 40);
            _os.Write(iIsPlayInActive, 41);
            _os.Write(iPreviewEffectSelectTargetType, 42);
            _os.Write(sPreviewEffectSelectTargetParam, 43);
            _os.Write(iPreviewEffectType, 44);
            _os.Write(sPreviewEffectParam, 45);
            _os.Write(iPreviewEffectTime, 46);
            _os.Write(sPreviewEffectName, 47);
            _os.Write(iPreviewEffectPos, 48);
            _os.Write(iPreviewEffectIsLoop, 49);
            _os.Write(iSet, 50);
            _os.Write(iPreviewEffectIsBindScale, 52);
            _os.Write(iEffectIsBindScale1, 53);
            _os.Write(iEffectIsBindScale2, 54);
            _os.Write(iEffectIsBindScale3, 55);
            _os.Write(iMoreFuncParam2, 56);
            _os.Write(iChangeFetterPriewTargetTime, 58);
            _os.Write(iFetterPriewShowTime, 59);
            _os.Write(iIsAlwaysRefreshFetterPriew, 60);
            _os.Write(iPreviewEffectIntParam1, 65);
            _os.Write(iPreviewEffectIntParam2, 66);
            _os.Write(iPreviewEffectBuff1, 67);
            _os.Write(iPreviewEffectBuff2, 68);
            _os.Write(iIsUse, 69);
            _os.Write(iIsOnlyPriewTargetAddBuff, 70);
            _os.Write(iIsNeedRecordReadyStage, 74);
            _os.Write(sActiveBank, 75);
            _os.Write(iBuffAddType4, 76);
            _os.Write(iBuffId4, 77);
            _os.Write(iBuffAddType5, 78);
            _os.Write(iBuffId5, 79);
            _os.Write(sActiveHeroVoice, 82);
            _os.Write(icheckCondType, 83);
            _os.Write(icheckCondParam, 84);
            _os.Write(iIsNeedRefreshBuffAssist, 85);
            _os.Write(iIsNeedRefreshOnBattle, 86);
            _os.Write(iLiveCounter, 87);
            _os.Write(sExtraDesc, 88);
            _os.Write(iHideBodyTime, 89);
            _os.Write(iEffect1Type, 99);
            _os.Write(sEffect1Param, 100);
            _os.Write(iEffect2Type, 101);
            _os.Write(sEffect2Param, 102);
            _os.Write(iEffect3Type, 103);
            _os.Write(sEffect3Param, 104);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLevel = (int) _is.Read(iLevel, 1, false);

            iType = (int) _is.Read(iType, 2, false);

            icheckId = (int) _is.Read(icheckId, 3, false);

            icheckNum = (int) _is.Read(icheckNum, 4, false);

            icheckOpt = (int) _is.Read(icheckOpt, 5, false);

            iTargetCamp = (int) _is.Read(iTargetCamp, 6, false);

            iTargetType = (int) _is.Read(iTargetType, 7, false);

            iTargetParam = (int) _is.Read(iTargetParam, 8, false);

            iReadyTimeTargetType = (int) _is.Read(iReadyTimeTargetType, 9, false);

            iReadyTimeTargetParam = (int) _is.Read(iReadyTimeTargetParam, 10, false);

            iTargetHeroFilter1 = (int) _is.Read(iTargetHeroFilter1, 11, false);

            iTargetHeroFilter2 = (int) _is.Read(iTargetHeroFilter2, 12, false);

            sName = (string) _is.Read(sName, 13, false);

            sPreDesc = (string) _is.Read(sPreDesc, 14, false);

            sDescValue = (string) _is.Read(sDescValue, 15, false);

            sDesc = (string) _is.Read(sDesc, 16, false);

            sTipsDesc = (string) _is.Read(sTipsDesc, 17, false);

            iIsPlayEffInReady = (int) _is.Read(iIsPlayEffInReady, 18, false);

            iIsPlayEffOnStart = (int) _is.Read(iIsPlayEffOnStart, 19, false);

            sEffectName1 = (string) _is.Read(sEffectName1, 20, false);

            iEffectPos1 = (int) _is.Read(iEffectPos1, 21, false);

            iEffectIsLoop1 = (int) _is.Read(iEffectIsLoop1, 22, false);

            sEffectName2 = (string) _is.Read(sEffectName2, 23, false);

            iEffectPos2 = (int) _is.Read(iEffectPos2, 24, false);

            iEffectIsLoop2 = (int) _is.Read(iEffectIsLoop2, 25, false);

            sEffectName3 = (string) _is.Read(sEffectName3, 26, false);

            iEffectPos3 = (int) _is.Read(iEffectPos3, 27, false);

            iEffectIsLoop3 = (int) _is.Read(iEffectIsLoop3, 28, false);

            iBuffAddType1 = (int) _is.Read(iBuffAddType1, 29, false);

            iBuffId1 = (int) _is.Read(iBuffId1, 30, false);

            iBuffAddType2 = (int) _is.Read(iBuffAddType2, 31, false);

            iBuffId2 = (int) _is.Read(iBuffId2, 32, false);

            iBuffAddType3 = (int) _is.Read(iBuffAddType3, 33, false);

            iBuffId3 = (int) _is.Read(iBuffId3, 34, false);

            iMoreFuncType = (int) _is.Read(iMoreFuncType, 35, false);

            iMoreFuncParam = (int) _is.Read(iMoreFuncParam, 36, false);

            sActiveSound = (string) _is.Read(sActiveSound, 37, false);

            sMatShaderName = (string) _is.Read(sMatShaderName, 38, false);

            sMoreFuncStrParam = (string) _is.Read(sMoreFuncStrParam, 39, false);

            iIsAlwaysRefeshBuff = (int) _is.Read(iIsAlwaysRefeshBuff, 40, false);

            iIsPlayInActive = (int) _is.Read(iIsPlayInActive, 41, false);

            iPreviewEffectSelectTargetType = (int) _is.Read(iPreviewEffectSelectTargetType, 42, false);

            sPreviewEffectSelectTargetParam = (string) _is.Read(sPreviewEffectSelectTargetParam, 43, false);

            iPreviewEffectType = (int) _is.Read(iPreviewEffectType, 44, false);

            sPreviewEffectParam = (string) _is.Read(sPreviewEffectParam, 45, false);

            iPreviewEffectTime = (int) _is.Read(iPreviewEffectTime, 46, false);

            sPreviewEffectName = (string) _is.Read(sPreviewEffectName, 47, false);

            iPreviewEffectPos = (int) _is.Read(iPreviewEffectPos, 48, false);

            iPreviewEffectIsLoop = (int) _is.Read(iPreviewEffectIsLoop, 49, false);

            iSet = (int) _is.Read(iSet, 50, false);

            iPreviewEffectIsBindScale = (int) _is.Read(iPreviewEffectIsBindScale, 52, false);

            iEffectIsBindScale1 = (int) _is.Read(iEffectIsBindScale1, 53, false);

            iEffectIsBindScale2 = (int) _is.Read(iEffectIsBindScale2, 54, false);

            iEffectIsBindScale3 = (int) _is.Read(iEffectIsBindScale3, 55, false);

            iMoreFuncParam2 = (int) _is.Read(iMoreFuncParam2, 56, false);

            iChangeFetterPriewTargetTime = (int) _is.Read(iChangeFetterPriewTargetTime, 58, false);

            iFetterPriewShowTime = (int) _is.Read(iFetterPriewShowTime, 59, false);

            iIsAlwaysRefreshFetterPriew = (int) _is.Read(iIsAlwaysRefreshFetterPriew, 60, false);

            iPreviewEffectIntParam1 = (int) _is.Read(iPreviewEffectIntParam1, 65, false);

            iPreviewEffectIntParam2 = (int) _is.Read(iPreviewEffectIntParam2, 66, false);

            iPreviewEffectBuff1 = (int) _is.Read(iPreviewEffectBuff1, 67, false);

            iPreviewEffectBuff2 = (int) _is.Read(iPreviewEffectBuff2, 68, false);

            iIsUse = (int) _is.Read(iIsUse, 69, false);

            iIsOnlyPriewTargetAddBuff = (int) _is.Read(iIsOnlyPriewTargetAddBuff, 70, false);

            iIsNeedRecordReadyStage = (int) _is.Read(iIsNeedRecordReadyStage, 74, false);

            sActiveBank = (string) _is.Read(sActiveBank, 75, false);

            iBuffAddType4 = (int) _is.Read(iBuffAddType4, 76, false);

            iBuffId4 = (int) _is.Read(iBuffId4, 77, false);

            iBuffAddType5 = (int) _is.Read(iBuffAddType5, 78, false);

            iBuffId5 = (int) _is.Read(iBuffId5, 79, false);

            sActiveHeroVoice = (string) _is.Read(sActiveHeroVoice, 82, false);

            icheckCondType = (int) _is.Read(icheckCondType, 83, false);

            icheckCondParam = (int) _is.Read(icheckCondParam, 84, false);

            iIsNeedRefreshBuffAssist = (int) _is.Read(iIsNeedRefreshBuffAssist, 85, false);

            iIsNeedRefreshOnBattle = (int) _is.Read(iIsNeedRefreshOnBattle, 86, false);

            iLiveCounter = (int) _is.Read(iLiveCounter, 87, false);

            sExtraDesc = (string) _is.Read(sExtraDesc, 88, false);

            iHideBodyTime = (int) _is.Read(iHideBodyTime, 89, false);

            iEffect1Type = (int) _is.Read(iEffect1Type, 99, false);

            sEffect1Param = (string) _is.Read(sEffect1Param, 100, false);

            iEffect2Type = (int) _is.Read(iEffect2Type, 101, false);

            sEffect2Param = (string) _is.Read(sEffect2Param, 102, false);

            iEffect3Type = (int) _is.Read(iEffect3Type, 103, false);

            sEffect3Param = (string) _is.Read(sEffect3Param, 104, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iType, "iType");
            _ds.Display(icheckId, "icheckId");
            _ds.Display(icheckNum, "icheckNum");
            _ds.Display(icheckOpt, "icheckOpt");
            _ds.Display(iTargetCamp, "iTargetCamp");
            _ds.Display(iTargetType, "iTargetType");
            _ds.Display(iTargetParam, "iTargetParam");
            _ds.Display(iReadyTimeTargetType, "iReadyTimeTargetType");
            _ds.Display(iReadyTimeTargetParam, "iReadyTimeTargetParam");
            _ds.Display(iTargetHeroFilter1, "iTargetHeroFilter1");
            _ds.Display(iTargetHeroFilter2, "iTargetHeroFilter2");
            _ds.Display(sName, "sName");
            _ds.Display(sPreDesc, "sPreDesc");
            _ds.Display(sDescValue, "sDescValue");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sTipsDesc, "sTipsDesc");
            _ds.Display(iIsPlayEffInReady, "iIsPlayEffInReady");
            _ds.Display(iIsPlayEffOnStart, "iIsPlayEffOnStart");
            _ds.Display(sEffectName1, "sEffectName1");
            _ds.Display(iEffectPos1, "iEffectPos1");
            _ds.Display(iEffectIsLoop1, "iEffectIsLoop1");
            _ds.Display(sEffectName2, "sEffectName2");
            _ds.Display(iEffectPos2, "iEffectPos2");
            _ds.Display(iEffectIsLoop2, "iEffectIsLoop2");
            _ds.Display(sEffectName3, "sEffectName3");
            _ds.Display(iEffectPos3, "iEffectPos3");
            _ds.Display(iEffectIsLoop3, "iEffectIsLoop3");
            _ds.Display(iBuffAddType1, "iBuffAddType1");
            _ds.Display(iBuffId1, "iBuffId1");
            _ds.Display(iBuffAddType2, "iBuffAddType2");
            _ds.Display(iBuffId2, "iBuffId2");
            _ds.Display(iBuffAddType3, "iBuffAddType3");
            _ds.Display(iBuffId3, "iBuffId3");
            _ds.Display(iMoreFuncType, "iMoreFuncType");
            _ds.Display(iMoreFuncParam, "iMoreFuncParam");
            _ds.Display(sActiveSound, "sActiveSound");
            _ds.Display(sMatShaderName, "sMatShaderName");
            _ds.Display(sMoreFuncStrParam, "sMoreFuncStrParam");
            _ds.Display(iIsAlwaysRefeshBuff, "iIsAlwaysRefeshBuff");
            _ds.Display(iIsPlayInActive, "iIsPlayInActive");
            _ds.Display(iPreviewEffectSelectTargetType, "iPreviewEffectSelectTargetType");
            _ds.Display(sPreviewEffectSelectTargetParam, "sPreviewEffectSelectTargetParam");
            _ds.Display(iPreviewEffectType, "iPreviewEffectType");
            _ds.Display(sPreviewEffectParam, "sPreviewEffectParam");
            _ds.Display(iPreviewEffectTime, "iPreviewEffectTime");
            _ds.Display(sPreviewEffectName, "sPreviewEffectName");
            _ds.Display(iPreviewEffectPos, "iPreviewEffectPos");
            _ds.Display(iPreviewEffectIsLoop, "iPreviewEffectIsLoop");
            _ds.Display(iSet, "iSet");
            _ds.Display(iPreviewEffectIsBindScale, "iPreviewEffectIsBindScale");
            _ds.Display(iEffectIsBindScale1, "iEffectIsBindScale1");
            _ds.Display(iEffectIsBindScale2, "iEffectIsBindScale2");
            _ds.Display(iEffectIsBindScale3, "iEffectIsBindScale3");
            _ds.Display(iMoreFuncParam2, "iMoreFuncParam2");
            _ds.Display(iChangeFetterPriewTargetTime, "iChangeFetterPriewTargetTime");
            _ds.Display(iFetterPriewShowTime, "iFetterPriewShowTime");
            _ds.Display(iIsAlwaysRefreshFetterPriew, "iIsAlwaysRefreshFetterPriew");
            _ds.Display(iPreviewEffectIntParam1, "iPreviewEffectIntParam1");
            _ds.Display(iPreviewEffectIntParam2, "iPreviewEffectIntParam2");
            _ds.Display(iPreviewEffectBuff1, "iPreviewEffectBuff1");
            _ds.Display(iPreviewEffectBuff2, "iPreviewEffectBuff2");
            _ds.Display(iIsUse, "iIsUse");
            _ds.Display(iIsOnlyPriewTargetAddBuff, "iIsOnlyPriewTargetAddBuff");
            _ds.Display(iIsNeedRecordReadyStage, "iIsNeedRecordReadyStage");
            _ds.Display(sActiveBank, "sActiveBank");
            _ds.Display(iBuffAddType4, "iBuffAddType4");
            _ds.Display(iBuffId4, "iBuffId4");
            _ds.Display(iBuffAddType5, "iBuffAddType5");
            _ds.Display(iBuffId5, "iBuffId5");
            _ds.Display(sActiveHeroVoice, "sActiveHeroVoice");
            _ds.Display(icheckCondType, "icheckCondType");
            _ds.Display(icheckCondParam, "icheckCondParam");
            _ds.Display(iIsNeedRefreshBuffAssist, "iIsNeedRefreshBuffAssist");
            _ds.Display(iIsNeedRefreshOnBattle, "iIsNeedRefreshOnBattle");
            _ds.Display(iLiveCounter, "iLiveCounter");
            _ds.Display(sExtraDesc, "sExtraDesc");
            _ds.Display(iHideBodyTime, "iHideBodyTime");
            _ds.Display(iEffect1Type, "iEffect1Type");
            _ds.Display(sEffect1Param, "sEffect1Param");
            _ds.Display(iEffect2Type, "iEffect2Type");
            _ds.Display(sEffect2Param, "sEffect2Param");
            _ds.Display(iEffect3Type, "iEffect3Type");
            _ds.Display(sEffect3Param, "sEffect3Param");
        }

        public override void Clear()
        {
            iID = 0;
            iLevel = 0;
            iType = 0;
            icheckId = 0;
            icheckNum = 0;
            icheckOpt = 0;
            iTargetCamp = 0;
            iTargetType = 0;
            iTargetParam = 0;
            iReadyTimeTargetType = 0;
            iReadyTimeTargetParam = 0;
            iTargetHeroFilter1 = 0;
            iTargetHeroFilter2 = 0;
            sName = "";
            sPreDesc = "";
            sDescValue = "";
            sDesc = "";
            sTipsDesc = "";
            iIsPlayEffInReady = 0;
            iIsPlayEffOnStart = 0;
            sEffectName1 = "";
            iEffectPos1 = 0;
            iEffectIsLoop1 = 0;
            sEffectName2 = "";
            iEffectPos2 = 0;
            iEffectIsLoop2 = 0;
            sEffectName3 = "";
            iEffectPos3 = 0;
            iEffectIsLoop3 = 0;
            iBuffAddType1 = 0;
            iBuffId1 = 0;
            iBuffAddType2 = 0;
            iBuffId2 = 0;
            iBuffAddType3 = 0;
            iBuffId3 = 0;
            iMoreFuncType = 0;
            iMoreFuncParam = 0;
            sActiveSound = "";
            sMatShaderName = "";
            sMoreFuncStrParam = "";
            iIsAlwaysRefeshBuff = 0;
            iIsPlayInActive = 0;
            iPreviewEffectSelectTargetType = 0;
            sPreviewEffectSelectTargetParam = "";
            iPreviewEffectType = 0;
            sPreviewEffectParam = "";
            iPreviewEffectTime = 0;
            sPreviewEffectName = "";
            iPreviewEffectPos = 0;
            iPreviewEffectIsLoop = 0;
            iSet = 0;
            iPreviewEffectIsBindScale = 0;
            iEffectIsBindScale1 = 0;
            iEffectIsBindScale2 = 0;
            iEffectIsBindScale3 = 0;
            iMoreFuncParam2 = 0;
            iChangeFetterPriewTargetTime = 0;
            iFetterPriewShowTime = 0;
            iIsAlwaysRefreshFetterPriew = 0;
            iPreviewEffectIntParam1 = 0;
            iPreviewEffectIntParam2 = 0;
            iPreviewEffectBuff1 = 0;
            iPreviewEffectBuff2 = 0;
            iIsUse = 0;
            iIsOnlyPriewTargetAddBuff = 0;
            iIsNeedRecordReadyStage = 0;
            sActiveBank = "";
            iBuffAddType4 = 0;
            iBuffId4 = 0;
            iBuffAddType5 = 0;
            iBuffId5 = 0;
            sActiveHeroVoice = "";
            icheckCondType = 0;
            icheckCondParam = 0;
            iIsNeedRefreshBuffAssist = 0;
            iIsNeedRefreshOnBattle = 0;
            iLiveCounter = 0;
            sExtraDesc = "";
            iHideBodyTime = 0;
            iEffect1Type = 0;
            sEffect1Param = "";
            iEffect2Type = 0;
            sEffect2Param = "";
            iEffect3Type = 0;
            sEffect3Param = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACGFetters_Client();
            copied.iID = this.iID;
            copied.iLevel = this.iLevel;
            copied.iType = this.iType;
            copied.icheckId = this.icheckId;
            copied.icheckNum = this.icheckNum;
            copied.icheckOpt = this.icheckOpt;
            copied.iTargetCamp = this.iTargetCamp;
            copied.iTargetType = this.iTargetType;
            copied.iTargetParam = this.iTargetParam;
            copied.iReadyTimeTargetType = this.iReadyTimeTargetType;
            copied.iReadyTimeTargetParam = this.iReadyTimeTargetParam;
            copied.iTargetHeroFilter1 = this.iTargetHeroFilter1;
            copied.iTargetHeroFilter2 = this.iTargetHeroFilter2;
            copied.sName = this.sName;
            copied.sPreDesc = this.sPreDesc;
            copied.sDescValue = this.sDescValue;
            copied.sDesc = this.sDesc;
            copied.sTipsDesc = this.sTipsDesc;
            copied.iIsPlayEffInReady = this.iIsPlayEffInReady;
            copied.iIsPlayEffOnStart = this.iIsPlayEffOnStart;
            copied.sEffectName1 = this.sEffectName1;
            copied.iEffectPos1 = this.iEffectPos1;
            copied.iEffectIsLoop1 = this.iEffectIsLoop1;
            copied.sEffectName2 = this.sEffectName2;
            copied.iEffectPos2 = this.iEffectPos2;
            copied.iEffectIsLoop2 = this.iEffectIsLoop2;
            copied.sEffectName3 = this.sEffectName3;
            copied.iEffectPos3 = this.iEffectPos3;
            copied.iEffectIsLoop3 = this.iEffectIsLoop3;
            copied.iBuffAddType1 = this.iBuffAddType1;
            copied.iBuffId1 = this.iBuffId1;
            copied.iBuffAddType2 = this.iBuffAddType2;
            copied.iBuffId2 = this.iBuffId2;
            copied.iBuffAddType3 = this.iBuffAddType3;
            copied.iBuffId3 = this.iBuffId3;
            copied.iMoreFuncType = this.iMoreFuncType;
            copied.iMoreFuncParam = this.iMoreFuncParam;
            copied.sActiveSound = this.sActiveSound;
            copied.sMatShaderName = this.sMatShaderName;
            copied.sMoreFuncStrParam = this.sMoreFuncStrParam;
            copied.iIsAlwaysRefeshBuff = this.iIsAlwaysRefeshBuff;
            copied.iIsPlayInActive = this.iIsPlayInActive;
            copied.iPreviewEffectSelectTargetType = this.iPreviewEffectSelectTargetType;
            copied.sPreviewEffectSelectTargetParam = this.sPreviewEffectSelectTargetParam;
            copied.iPreviewEffectType = this.iPreviewEffectType;
            copied.sPreviewEffectParam = this.sPreviewEffectParam;
            copied.iPreviewEffectTime = this.iPreviewEffectTime;
            copied.sPreviewEffectName = this.sPreviewEffectName;
            copied.iPreviewEffectPos = this.iPreviewEffectPos;
            copied.iPreviewEffectIsLoop = this.iPreviewEffectIsLoop;
            copied.iSet = this.iSet;
            copied.iPreviewEffectIsBindScale = this.iPreviewEffectIsBindScale;
            copied.iEffectIsBindScale1 = this.iEffectIsBindScale1;
            copied.iEffectIsBindScale2 = this.iEffectIsBindScale2;
            copied.iEffectIsBindScale3 = this.iEffectIsBindScale3;
            copied.iMoreFuncParam2 = this.iMoreFuncParam2;
            copied.iChangeFetterPriewTargetTime = this.iChangeFetterPriewTargetTime;
            copied.iFetterPriewShowTime = this.iFetterPriewShowTime;
            copied.iIsAlwaysRefreshFetterPriew = this.iIsAlwaysRefreshFetterPriew;
            copied.iPreviewEffectIntParam1 = this.iPreviewEffectIntParam1;
            copied.iPreviewEffectIntParam2 = this.iPreviewEffectIntParam2;
            copied.iPreviewEffectBuff1 = this.iPreviewEffectBuff1;
            copied.iPreviewEffectBuff2 = this.iPreviewEffectBuff2;
            copied.iIsUse = this.iIsUse;
            copied.iIsOnlyPriewTargetAddBuff = this.iIsOnlyPriewTargetAddBuff;
            copied.iIsNeedRecordReadyStage = this.iIsNeedRecordReadyStage;
            copied.sActiveBank = this.sActiveBank;
            copied.iBuffAddType4 = this.iBuffAddType4;
            copied.iBuffId4 = this.iBuffId4;
            copied.iBuffAddType5 = this.iBuffAddType5;
            copied.iBuffId5 = this.iBuffId5;
            copied.sActiveHeroVoice = this.sActiveHeroVoice;
            copied.icheckCondType = this.icheckCondType;
            copied.icheckCondParam = this.icheckCondParam;
            copied.iIsNeedRefreshBuffAssist = this.iIsNeedRefreshBuffAssist;
            copied.iIsNeedRefreshOnBattle = this.iIsNeedRefreshOnBattle;
            copied.iLiveCounter = this.iLiveCounter;
            copied.sExtraDesc = this.sExtraDesc;
            copied.iHideBodyTime = this.iHideBodyTime;
            copied.iEffect1Type = this.iEffect1Type;
            copied.sEffect1Param = this.sEffect1Param;
            copied.iEffect2Type = this.iEffect2Type;
            copied.sEffect2Param = this.sEffect2Param;
            copied.iEffect3Type = this.iEffect3Type;
            copied.sEffect3Param = this.sEffect3Param;
            return copied;
        }
    }
}

