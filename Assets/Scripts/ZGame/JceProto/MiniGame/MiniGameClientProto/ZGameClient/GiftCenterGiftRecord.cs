// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterGiftRecord : Wup.Jce.JceStruct
    {
        public long lRecordID = 0;

        public TUserID stUserID;

        public int iGoodsID = 0;

        public int iGoodsNum = 0;

        public TItemInfo stItemInfo;

        public string sMessage = "";

        public int iRecordTime = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lRecordID, 0);
            _os.Write(stUserID, 1);
            _os.Write(iGoodsID, 2);
            _os.Write(iGoodsNum, 3);
            _os.Write(stItemInfo, 4);
            _os.Write(sMessage, 5);
            _os.Write(iRecordTime, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lRecordID = (long) _is.Read(lRecordID, 0, false);

            stUserID = (TUserID) _is.Read(stUserID, 1, false);

            iGoodsID = (int) _is.Read(iGoodsID, 2, false);

            iGoodsNum = (int) _is.Read(iGoodsNum, 3, false);

            stItemInfo = (TItemInfo) _is.Read(stItemInfo, 4, false);

            sMessage = (string) _is.Read(sMessage, 5, false);

            iRecordTime = (int) _is.Read(iRecordTime, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lRecordID, "lRecordID");
            _ds.Display(stUserID, "stUserID");
            _ds.Display(iGoodsID, "iGoodsID");
            _ds.Display(iGoodsNum, "iGoodsNum");
            _ds.Display(stItemInfo, "stItemInfo");
            _ds.Display(sMessage, "sMessage");
            _ds.Display(iRecordTime, "iRecordTime");
        }

        public override void Clear()
        {
            lRecordID = 0;
            stUserID = null;
            iGoodsID = 0;
            iGoodsNum = 0;
            stItemInfo = null;
            sMessage = "";
            iRecordTime = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterGiftRecord();
            copied.lRecordID = this.lRecordID;
            copied.stUserID = (TUserID)JceUtil.DeepClone(this.stUserID);
            copied.iGoodsID = this.iGoodsID;
            copied.iGoodsNum = this.iGoodsNum;
            copied.stItemInfo = (TItemInfo)JceUtil.DeepClone(this.stItemInfo);
            copied.sMessage = this.sMessage;
            copied.iRecordTime = this.iRecordTime;
            return copied;
        }
    }
}

