// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyEquipFetter : Wup.Jce.JceStruct
    {
        int _playerId = 0;
        public int playerId
        {
            get
            {
                 return _playerId;
            }
            set
            {
                _playerId = value; 
            }
        }

        bool _isAddFetter = true;
        public bool isAddFetter
        {
            get
            {
                 return _isAddFetter;
            }
            set
            {
                _isAddFetter = value; 
            }
        }

        int _heroEntityId = 0;
        public int heroEntityId
        {
            get
            {
                 return _heroEntityId;
            }
            set
            {
                _heroEntityId = value; 
            }
        }

        int _fetterType = 0;
        public int fetterType
        {
            get
            {
                 return _fetterType;
            }
            set
            {
                _fetterType = value; 
            }
        }

        int _fetterId = 0;
        public int fetterId
        {
            get
            {
                 return _fetterId;
            }
            set
            {
                _fetterId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(playerId, 0);
            _os.Write(isAddFetter, 1);
            _os.Write(heroEntityId, 2);
            _os.Write(fetterType, 3);
            _os.Write(fetterId, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            playerId = (int) _is.Read(playerId, 0, false);

            isAddFetter = (bool) _is.Read(isAddFetter, 1, false);

            heroEntityId = (int) _is.Read(heroEntityId, 2, false);

            fetterType = (int) _is.Read(fetterType, 3, false);

            fetterId = (int) _is.Read(fetterId, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(playerId, "playerId");
            _ds.Display(isAddFetter, "isAddFetter");
            _ds.Display(heroEntityId, "heroEntityId");
            _ds.Display(fetterType, "fetterType");
            _ds.Display(fetterId, "fetterId");
        }

        public override void Clear()
        {
            playerId = 0;
            isAddFetter = true;
            heroEntityId = 0;
            fetterType = 0;
            fetterId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyEquipFetter();
            copied.playerId = this.playerId;
            copied.isAddFetter = this.isAddFetter;
            copied.heroEntityId = this.heroEntityId;
            copied.fetterType = this.fetterType;
            copied.fetterId = this.fetterId;
            return copied;
        }
    }
}

