// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TNotifyPlayerDiffVersion : Wup.Jce.JceStruct
    {
        public TUserID stUserID {get; set;} 

        string _strNickName = "";
        public string strNickName
        {
            get
            {
                 return _strNickName;
            }
            set
            {
                _strNickName = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stUserID, 0);
            _os.Write(strNickName, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stUserID = (TUserID) _is.Read(stUserID, 0, false);

            strNickName = (string) _is.Read(strNickName, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stUserID, "stUserID");
            _ds.Display(strNickName, "strNickName");
        }

        public override void Clear()
        {
            stUserID = null;
            strNickName = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TNotifyPlayerDiffVersion();
            copied.stUserID = (TUserID)JceUtil.DeepClone(this.stUserID);
            copied.strNickName = this.strNickName;
            return copied;
        }
    }
}

