// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TZoneRankClub : Wup.Jce.JceStruct
    {
        public long clubId = 0;

        public string name = "";

        public int curMemberNum = 0;

        public int maxMemberNum = 0;

        public int iconId = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(clubId, 0);
            _os.Write(name, 1);
            _os.Write(curMemberNum, 2);
            _os.Write(maxMemberNum, 3);
            _os.Write(iconId, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            clubId = (long) _is.Read(clubId, 0, false);

            name = (string) _is.Read(name, 1, false);

            curMemberNum = (int) _is.Read(curMemberNum, 2, false);

            maxMemberNum = (int) _is.Read(maxMemberNum, 3, false);

            iconId = (int) _is.Read(iconId, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(clubId, "clubId");
            _ds.Display(name, "name");
            _ds.Display(curMemberNum, "curMemberNum");
            _ds.Display(maxMemberNum, "maxMemberNum");
            _ds.Display(iconId, "iconId");
        }

        public override void Clear()
        {
            clubId = 0;
            name = "";
            curMemberNum = 0;
            maxMemberNum = 0;
            iconId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TZoneRankClub();
            copied.clubId = this.clubId;
            copied.name = this.name;
            copied.curMemberNum = this.curMemberNum;
            copied.maxMemberNum = this.maxMemberNum;
            copied.iconId = this.iconId;
            return copied;
        }
    }
}

