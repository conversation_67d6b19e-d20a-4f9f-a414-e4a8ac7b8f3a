//所在的Excel 【ACG_GuideConfig.xlsm】
//***************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_GuideLabel_Client : Wup.Jce.JceStruct
    {
        public int iLabelID = 0;

        public int iTipsPoolID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iLabelID, 0);
            _os.Write(iTipsPoolID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iLabelID = (int) _is.Read(iLabelID, 0, false);

            iTipsPoolID = (int) _is.Read(iTipsPoolID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iLabelID, "iLabelID");
            _ds.Display(iTipsPoolID, "iTipsPoolID");
        }

        public override void Clear()
        {
            iLabelID = 0;
            iTipsPoolID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_GuideLabel_Client();
            copied.iLabelID = this.iLabelID;
            copied.iTipsPoolID = this.iTipsPoolID;
            return copied;
        }
    }
}

