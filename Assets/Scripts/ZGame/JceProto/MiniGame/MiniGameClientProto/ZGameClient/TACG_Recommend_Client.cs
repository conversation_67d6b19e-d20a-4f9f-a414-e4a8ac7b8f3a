//所在的Excel 【ACG_Mall.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Recommend_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sPicUrl = "";

        public int iIsDirectBuy = 0;

        public string sDescName = "";

        public string sDescContent = "";

        public int iGoodsID = 0;

        public int iActivityID = 0;

        public int iAreaID = 0;

        public int iPriority = 0;

        public string sBeginTime = "";

        public string sEndTime = "";

        public int iBeginSec = 0;

        public int iEndSec = 0;

        public string sJumpParam = "";

        public int iRefelectItemId = 0;

        public int iBannerType = 0;

        public int iMainBannerItem = 0;

        public string sBannerItems = "";

        public string sExtraItems = "";

        public int iMinClientVersion = 0;

        public int iMaxClientVersion = 0;

        public string sBannerTitle = "";

        public string sFetterShow = "";

        public string sTags = "";

        public string sRoundPlay = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sPicUrl, 1);
            _os.Write(iIsDirectBuy, 3);
            _os.Write(sDescName, 4);
            _os.Write(sDescContent, 5);
            _os.Write(iGoodsID, 6);
            _os.Write(iActivityID, 7);
            _os.Write(iAreaID, 8);
            _os.Write(iPriority, 9);
            _os.Write(sBeginTime, 10);
            _os.Write(sEndTime, 11);
            _os.Write(iBeginSec, 12);
            _os.Write(iEndSec, 13);
            _os.Write(sJumpParam, 14);
            _os.Write(iRefelectItemId, 15);
            _os.Write(iBannerType, 16);
            _os.Write(iMainBannerItem, 17);
            _os.Write(sBannerItems, 18);
            _os.Write(sExtraItems, 19);
            _os.Write(iMinClientVersion, 20);
            _os.Write(iMaxClientVersion, 21);
            _os.Write(sBannerTitle, 22);
            _os.Write(sFetterShow, 23);
            _os.Write(sTags, 24);
            _os.Write(sRoundPlay, 25);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sPicUrl = (string) _is.Read(sPicUrl, 1, false);

            iIsDirectBuy = (int) _is.Read(iIsDirectBuy, 3, false);

            sDescName = (string) _is.Read(sDescName, 4, false);

            sDescContent = (string) _is.Read(sDescContent, 5, false);

            iGoodsID = (int) _is.Read(iGoodsID, 6, false);

            iActivityID = (int) _is.Read(iActivityID, 7, false);

            iAreaID = (int) _is.Read(iAreaID, 8, false);

            iPriority = (int) _is.Read(iPriority, 9, false);

            sBeginTime = (string) _is.Read(sBeginTime, 10, false);

            sEndTime = (string) _is.Read(sEndTime, 11, false);

            iBeginSec = (int) _is.Read(iBeginSec, 12, false);

            iEndSec = (int) _is.Read(iEndSec, 13, false);

            sJumpParam = (string) _is.Read(sJumpParam, 14, false);

            iRefelectItemId = (int) _is.Read(iRefelectItemId, 15, false);

            iBannerType = (int) _is.Read(iBannerType, 16, false);

            iMainBannerItem = (int) _is.Read(iMainBannerItem, 17, false);

            sBannerItems = (string) _is.Read(sBannerItems, 18, false);

            sExtraItems = (string) _is.Read(sExtraItems, 19, false);

            iMinClientVersion = (int) _is.Read(iMinClientVersion, 20, false);

            iMaxClientVersion = (int) _is.Read(iMaxClientVersion, 21, false);

            sBannerTitle = (string) _is.Read(sBannerTitle, 22, false);

            sFetterShow = (string) _is.Read(sFetterShow, 23, false);

            sTags = (string) _is.Read(sTags, 24, false);

            sRoundPlay = (string) _is.Read(sRoundPlay, 25, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sPicUrl, "sPicUrl");
            _ds.Display(iIsDirectBuy, "iIsDirectBuy");
            _ds.Display(sDescName, "sDescName");
            _ds.Display(sDescContent, "sDescContent");
            _ds.Display(iGoodsID, "iGoodsID");
            _ds.Display(iActivityID, "iActivityID");
            _ds.Display(iAreaID, "iAreaID");
            _ds.Display(iPriority, "iPriority");
            _ds.Display(sBeginTime, "sBeginTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(iBeginSec, "iBeginSec");
            _ds.Display(iEndSec, "iEndSec");
            _ds.Display(sJumpParam, "sJumpParam");
            _ds.Display(iRefelectItemId, "iRefelectItemId");
            _ds.Display(iBannerType, "iBannerType");
            _ds.Display(iMainBannerItem, "iMainBannerItem");
            _ds.Display(sBannerItems, "sBannerItems");
            _ds.Display(sExtraItems, "sExtraItems");
            _ds.Display(iMinClientVersion, "iMinClientVersion");
            _ds.Display(iMaxClientVersion, "iMaxClientVersion");
            _ds.Display(sBannerTitle, "sBannerTitle");
            _ds.Display(sFetterShow, "sFetterShow");
            _ds.Display(sTags, "sTags");
            _ds.Display(sRoundPlay, "sRoundPlay");
        }

        public override void Clear()
        {
            iID = 0;
            sPicUrl = "";
            iIsDirectBuy = 0;
            sDescName = "";
            sDescContent = "";
            iGoodsID = 0;
            iActivityID = 0;
            iAreaID = 0;
            iPriority = 0;
            sBeginTime = "";
            sEndTime = "";
            iBeginSec = 0;
            iEndSec = 0;
            sJumpParam = "";
            iRefelectItemId = 0;
            iBannerType = 0;
            iMainBannerItem = 0;
            sBannerItems = "";
            sExtraItems = "";
            iMinClientVersion = 0;
            iMaxClientVersion = 0;
            sBannerTitle = "";
            sFetterShow = "";
            sTags = "";
            sRoundPlay = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Recommend_Client();
            copied.iID = this.iID;
            copied.sPicUrl = this.sPicUrl;
            copied.iIsDirectBuy = this.iIsDirectBuy;
            copied.sDescName = this.sDescName;
            copied.sDescContent = this.sDescContent;
            copied.iGoodsID = this.iGoodsID;
            copied.iActivityID = this.iActivityID;
            copied.iAreaID = this.iAreaID;
            copied.iPriority = this.iPriority;
            copied.sBeginTime = this.sBeginTime;
            copied.sEndTime = this.sEndTime;
            copied.iBeginSec = this.iBeginSec;
            copied.iEndSec = this.iEndSec;
            copied.sJumpParam = this.sJumpParam;
            copied.iRefelectItemId = this.iRefelectItemId;
            copied.iBannerType = this.iBannerType;
            copied.iMainBannerItem = this.iMainBannerItem;
            copied.sBannerItems = this.sBannerItems;
            copied.sExtraItems = this.sExtraItems;
            copied.iMinClientVersion = this.iMinClientVersion;
            copied.iMaxClientVersion = this.iMaxClientVersion;
            copied.sBannerTitle = this.sBannerTitle;
            copied.sFetterShow = this.sFetterShow;
            copied.sTags = this.sTags;
            copied.sRoundPlay = this.sRoundPlay;
            return copied;
        }
    }
}

