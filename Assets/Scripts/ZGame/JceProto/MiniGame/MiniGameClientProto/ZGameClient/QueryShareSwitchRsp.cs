// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class QueryShareSwitchRsp : Wup.Jce.JceStruct
    {
        public int iResult = 0;

        public System.Collections.Generic.List<TSocial_Server> shareList;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iResult, 0);
            _os.Write(shareList, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iResult = (int) _is.Read(iResult, 0, false);

            shareList = (System.Collections.Generic.List<TSocial_Server>) _is.Read(shareList, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iResult, "iResult");
            _ds.Display(shareList, "shareList");
        }

        public override void Clear()
        {
            iResult = 0;
            shareList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new QueryShareSwitchRsp();
            copied.iResult = this.iResult;
            copied.shareList = (System.Collections.Generic.List<TSocial_Server>)JceUtil.DeepClone(this.shareList);
            return copied;
        }
    }
}

