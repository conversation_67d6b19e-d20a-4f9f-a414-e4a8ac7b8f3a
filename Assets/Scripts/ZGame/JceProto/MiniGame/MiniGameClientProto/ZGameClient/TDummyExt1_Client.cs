// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TDummyExt1_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iDummyField1 = 0;

        public string sDummyField2 = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iDummyField1, 1);
            _os.Write(sDummyField2, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iDummyField1 = (int) _is.Read(iDummyField1, 1, false);

            sDummyField2 = (string) _is.Read(sDummyField2, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iDummyField1, "iDummyField1");
            _ds.Display(sDummyField2, "sDummyField2");
        }

        public override void Clear()
        {
            iID = 0;
            iDummyField1 = 0;
            sDummyField2 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TDummyExt1_Client();
            copied.iID = this.iID;
            copied.iDummyField1 = this.iDummyField1;
            copied.sDummyField2 = this.sDummyField2;
            return copied;
        }
    }
}

