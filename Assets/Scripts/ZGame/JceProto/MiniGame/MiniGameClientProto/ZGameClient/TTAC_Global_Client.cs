//所在的Excel 【ACG_Global.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Global_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sParam1 = "";

        public string sParam2 = "";

        public string sParam3 = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sParam1, 1);
            _os.Write(sParam2, 2);
            _os.Write(sParam3, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sParam1 = (string) _is.Read(sParam1, 1, false);

            sParam2 = (string) _is.Read(sParam2, 2, false);

            sParam3 = (string) _is.Read(sParam3, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sParam1, "sParam1");
            _ds.Display(sParam2, "sParam2");
            _ds.Display(sParam3, "sParam3");
        }

        public override void Clear()
        {
            iID = 0;
            sParam1 = "";
            sParam2 = "";
            sParam3 = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_Global_Client();
            copied.iID = this.iID;
            copied.sParam1 = this.sParam1;
            copied.sParam2 = this.sParam2;
            copied.sParam3 = this.sParam3;
            return copied;
        }
    }
}

