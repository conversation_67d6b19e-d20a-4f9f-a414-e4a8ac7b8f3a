//所在的Excel 【ACG_Buff.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACGHeavenBuff_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSet = 0;

        public int iFuncType = 0;

        public int iFuncParam = 0;

        public string sFuncStrParam = "";

        public int iTargetCamp = 0;

        public int iTargetType = 0;

        public int iTargetParam1 = 0;

        public int iTargetParam2 = 0;

        public int iTargetParam3 = 0;

        public int iReadyTimeTargetType = 0;

        public int iReadyTimeTargetParam1 = 0;

        public int iReadyTimeTargetParam2 = 0;

        public int iReadyTimeTargetParam3 = 0;

        public int iTargetHeroFilter1 = 0;

        public int iTargetHeroFilter2 = 0;

        public string sActiveSound = "";

        public int iIsPlayInActive = 0;

        public int iIsPlayEffInReady = 0;

        public int iIsPlayEffOnStart = 0;

        public string sMatShaderName = "";

        public string sEffectName1 = "";

        public int iEffectPos1 = 0;

        public int iEffectIsLoop1 = 0;

        public string sEffectName2 = "";

        public int iEffectPos2 = 0;

        public int iEffectIsLoop2 = 0;

        public int iBuffId1 = 0;

        public int iBuffId2 = 0;

        public int iBuffId3 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSet, 1);
            _os.Write(iFuncType, 5);
            _os.Write(iFuncParam, 6);
            _os.Write(sFuncStrParam, 7);
            _os.Write(iTargetCamp, 8);
            _os.Write(iTargetType, 9);
            _os.Write(iTargetParam1, 10);
            _os.Write(iTargetParam2, 11);
            _os.Write(iTargetParam3, 12);
            _os.Write(iReadyTimeTargetType, 13);
            _os.Write(iReadyTimeTargetParam1, 14);
            _os.Write(iReadyTimeTargetParam2, 15);
            _os.Write(iReadyTimeTargetParam3, 16);
            _os.Write(iTargetHeroFilter1, 17);
            _os.Write(iTargetHeroFilter2, 18);
            _os.Write(sActiveSound, 19);
            _os.Write(iIsPlayInActive, 22);
            _os.Write(iIsPlayEffInReady, 23);
            _os.Write(iIsPlayEffOnStart, 24);
            _os.Write(sMatShaderName, 25);
            _os.Write(sEffectName1, 26);
            _os.Write(iEffectPos1, 27);
            _os.Write(iEffectIsLoop1, 28);
            _os.Write(sEffectName2, 29);
            _os.Write(iEffectPos2, 30);
            _os.Write(iEffectIsLoop2, 31);
            _os.Write(iBuffId1, 32);
            _os.Write(iBuffId2, 33);
            _os.Write(iBuffId3, 34);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSet = (int) _is.Read(iSet, 1, false);

            iFuncType = (int) _is.Read(iFuncType, 5, false);

            iFuncParam = (int) _is.Read(iFuncParam, 6, false);

            sFuncStrParam = (string) _is.Read(sFuncStrParam, 7, false);

            iTargetCamp = (int) _is.Read(iTargetCamp, 8, false);

            iTargetType = (int) _is.Read(iTargetType, 9, false);

            iTargetParam1 = (int) _is.Read(iTargetParam1, 10, false);

            iTargetParam2 = (int) _is.Read(iTargetParam2, 11, false);

            iTargetParam3 = (int) _is.Read(iTargetParam3, 12, false);

            iReadyTimeTargetType = (int) _is.Read(iReadyTimeTargetType, 13, false);

            iReadyTimeTargetParam1 = (int) _is.Read(iReadyTimeTargetParam1, 14, false);

            iReadyTimeTargetParam2 = (int) _is.Read(iReadyTimeTargetParam2, 15, false);

            iReadyTimeTargetParam3 = (int) _is.Read(iReadyTimeTargetParam3, 16, false);

            iTargetHeroFilter1 = (int) _is.Read(iTargetHeroFilter1, 17, false);

            iTargetHeroFilter2 = (int) _is.Read(iTargetHeroFilter2, 18, false);

            sActiveSound = (string) _is.Read(sActiveSound, 19, false);

            iIsPlayInActive = (int) _is.Read(iIsPlayInActive, 22, false);

            iIsPlayEffInReady = (int) _is.Read(iIsPlayEffInReady, 23, false);

            iIsPlayEffOnStart = (int) _is.Read(iIsPlayEffOnStart, 24, false);

            sMatShaderName = (string) _is.Read(sMatShaderName, 25, false);

            sEffectName1 = (string) _is.Read(sEffectName1, 26, false);

            iEffectPos1 = (int) _is.Read(iEffectPos1, 27, false);

            iEffectIsLoop1 = (int) _is.Read(iEffectIsLoop1, 28, false);

            sEffectName2 = (string) _is.Read(sEffectName2, 29, false);

            iEffectPos2 = (int) _is.Read(iEffectPos2, 30, false);

            iEffectIsLoop2 = (int) _is.Read(iEffectIsLoop2, 31, false);

            iBuffId1 = (int) _is.Read(iBuffId1, 32, false);

            iBuffId2 = (int) _is.Read(iBuffId2, 33, false);

            iBuffId3 = (int) _is.Read(iBuffId3, 34, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSet, "iSet");
            _ds.Display(iFuncType, "iFuncType");
            _ds.Display(iFuncParam, "iFuncParam");
            _ds.Display(sFuncStrParam, "sFuncStrParam");
            _ds.Display(iTargetCamp, "iTargetCamp");
            _ds.Display(iTargetType, "iTargetType");
            _ds.Display(iTargetParam1, "iTargetParam1");
            _ds.Display(iTargetParam2, "iTargetParam2");
            _ds.Display(iTargetParam3, "iTargetParam3");
            _ds.Display(iReadyTimeTargetType, "iReadyTimeTargetType");
            _ds.Display(iReadyTimeTargetParam1, "iReadyTimeTargetParam1");
            _ds.Display(iReadyTimeTargetParam2, "iReadyTimeTargetParam2");
            _ds.Display(iReadyTimeTargetParam3, "iReadyTimeTargetParam3");
            _ds.Display(iTargetHeroFilter1, "iTargetHeroFilter1");
            _ds.Display(iTargetHeroFilter2, "iTargetHeroFilter2");
            _ds.Display(sActiveSound, "sActiveSound");
            _ds.Display(iIsPlayInActive, "iIsPlayInActive");
            _ds.Display(iIsPlayEffInReady, "iIsPlayEffInReady");
            _ds.Display(iIsPlayEffOnStart, "iIsPlayEffOnStart");
            _ds.Display(sMatShaderName, "sMatShaderName");
            _ds.Display(sEffectName1, "sEffectName1");
            _ds.Display(iEffectPos1, "iEffectPos1");
            _ds.Display(iEffectIsLoop1, "iEffectIsLoop1");
            _ds.Display(sEffectName2, "sEffectName2");
            _ds.Display(iEffectPos2, "iEffectPos2");
            _ds.Display(iEffectIsLoop2, "iEffectIsLoop2");
            _ds.Display(iBuffId1, "iBuffId1");
            _ds.Display(iBuffId2, "iBuffId2");
            _ds.Display(iBuffId3, "iBuffId3");
        }

        public override void Clear()
        {
            iID = 0;
            iSet = 0;
            iFuncType = 0;
            iFuncParam = 0;
            sFuncStrParam = "";
            iTargetCamp = 0;
            iTargetType = 0;
            iTargetParam1 = 0;
            iTargetParam2 = 0;
            iTargetParam3 = 0;
            iReadyTimeTargetType = 0;
            iReadyTimeTargetParam1 = 0;
            iReadyTimeTargetParam2 = 0;
            iReadyTimeTargetParam3 = 0;
            iTargetHeroFilter1 = 0;
            iTargetHeroFilter2 = 0;
            sActiveSound = "";
            iIsPlayInActive = 0;
            iIsPlayEffInReady = 0;
            iIsPlayEffOnStart = 0;
            sMatShaderName = "";
            sEffectName1 = "";
            iEffectPos1 = 0;
            iEffectIsLoop1 = 0;
            sEffectName2 = "";
            iEffectPos2 = 0;
            iEffectIsLoop2 = 0;
            iBuffId1 = 0;
            iBuffId2 = 0;
            iBuffId3 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACGHeavenBuff_Client();
            copied.iID = this.iID;
            copied.iSet = this.iSet;
            copied.iFuncType = this.iFuncType;
            copied.iFuncParam = this.iFuncParam;
            copied.sFuncStrParam = this.sFuncStrParam;
            copied.iTargetCamp = this.iTargetCamp;
            copied.iTargetType = this.iTargetType;
            copied.iTargetParam1 = this.iTargetParam1;
            copied.iTargetParam2 = this.iTargetParam2;
            copied.iTargetParam3 = this.iTargetParam3;
            copied.iReadyTimeTargetType = this.iReadyTimeTargetType;
            copied.iReadyTimeTargetParam1 = this.iReadyTimeTargetParam1;
            copied.iReadyTimeTargetParam2 = this.iReadyTimeTargetParam2;
            copied.iReadyTimeTargetParam3 = this.iReadyTimeTargetParam3;
            copied.iTargetHeroFilter1 = this.iTargetHeroFilter1;
            copied.iTargetHeroFilter2 = this.iTargetHeroFilter2;
            copied.sActiveSound = this.sActiveSound;
            copied.iIsPlayInActive = this.iIsPlayInActive;
            copied.iIsPlayEffInReady = this.iIsPlayEffInReady;
            copied.iIsPlayEffOnStart = this.iIsPlayEffOnStart;
            copied.sMatShaderName = this.sMatShaderName;
            copied.sEffectName1 = this.sEffectName1;
            copied.iEffectPos1 = this.iEffectPos1;
            copied.iEffectIsLoop1 = this.iEffectIsLoop1;
            copied.sEffectName2 = this.sEffectName2;
            copied.iEffectPos2 = this.iEffectPos2;
            copied.iEffectIsLoop2 = this.iEffectIsLoop2;
            copied.iBuffId1 = this.iBuffId1;
            copied.iBuffId2 = this.iBuffId2;
            copied.iBuffId3 = this.iBuffId3;
            return copied;
        }
    }
}

