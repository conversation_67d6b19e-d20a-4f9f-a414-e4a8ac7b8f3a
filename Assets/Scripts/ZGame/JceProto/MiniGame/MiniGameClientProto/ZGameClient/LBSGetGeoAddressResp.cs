// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class LBSGetGeoAddressResp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public GeoAddress geoAddress;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(geoAddress, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            geoAddress = (Geo<PERSON>ddress) _is.Read(geoAddress, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(geoAddress, "geoAddress");
        }

        public override void Clear()
        {
            ret = 0;
            geoAddress = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new LBSGetGeoAddressResp();
            copied.ret = this.ret;
            copied.geoAddress = (GeoAddress)JceUtil.DeepClone(this.geoAddress);
            return copied;
        }
    }
}

