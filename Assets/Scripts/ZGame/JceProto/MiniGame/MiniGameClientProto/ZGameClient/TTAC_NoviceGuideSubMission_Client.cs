//所在的Excel 【TAC_NoviceGuide.xlsm】
//***************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_NoviceGuideSubMission_Client : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        string _sDesc = "";
        public string sDesc
        {
            get
            {
                 return _sDesc;
            }
            set
            {
                _sDesc = value; 
            }
        }

        int _iTargerCount = 0;
        public int iTargerCount
        {
            get
            {
                 return _iTargerCount;
            }
            set
            {
                _iTargerCount = value; 
            }
        }

        int _iTargetId = 0;
        public int iTargetId
        {
            get
            {
                 return _iTargetId;
            }
            set
            {
                _iTargetId = value; 
            }
        }

        int _iClassTargerCount = 0;
        public int iClassTargerCount
        {
            get
            {
                 return _iClassTargerCount;
            }
            set
            {
                _iClassTargerCount = value; 
            }
        }

        int _iClassTargetId = 0;
        public int iClassTargetId
        {
            get
            {
                 return _iClassTargetId;
            }
            set
            {
                _iClassTargetId = value; 
            }
        }

        int _iCoinNum = 0;
        public int iCoinNum
        {
            get
            {
                 return _iCoinNum;
            }
            set
            {
                _iCoinNum = value; 
            }
        }

        int _i3StarLevelUpHeroId = 0;
        public int i3StarLevelUpHeroId
        {
            get
            {
                 return _i3StarLevelUpHeroId;
            }
            set
            {
                _i3StarLevelUpHeroId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iType, 1);
            _os.Write(sDesc, 2);
            _os.Write(iTargerCount, 3);
            _os.Write(iTargetId, 4);
            _os.Write(iClassTargerCount, 5);
            _os.Write(iClassTargetId, 6);
            _os.Write(iCoinNum, 7);
            _os.Write(i3StarLevelUpHeroId, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iType = (int) _is.Read(iType, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            iTargerCount = (int) _is.Read(iTargerCount, 3, false);

            iTargetId = (int) _is.Read(iTargetId, 4, false);

            iClassTargerCount = (int) _is.Read(iClassTargerCount, 5, false);

            iClassTargetId = (int) _is.Read(iClassTargetId, 6, false);

            iCoinNum = (int) _is.Read(iCoinNum, 7, false);

            i3StarLevelUpHeroId = (int) _is.Read(i3StarLevelUpHeroId, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iType, "iType");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iTargerCount, "iTargerCount");
            _ds.Display(iTargetId, "iTargetId");
            _ds.Display(iClassTargerCount, "iClassTargerCount");
            _ds.Display(iClassTargetId, "iClassTargetId");
            _ds.Display(iCoinNum, "iCoinNum");
            _ds.Display(i3StarLevelUpHeroId, "i3StarLevelUpHeroId");
        }

        public override void Clear()
        {
            iID = 0;
            iType = 0;
            sDesc = "";
            iTargerCount = 0;
            iTargetId = 0;
            iClassTargerCount = 0;
            iClassTargetId = 0;
            iCoinNum = 0;
            i3StarLevelUpHeroId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_NoviceGuideSubMission_Client();
            copied.iID = this.iID;
            copied.iType = this.iType;
            copied.sDesc = this.sDesc;
            copied.iTargerCount = this.iTargerCount;
            copied.iTargetId = this.iTargetId;
            copied.iClassTargerCount = this.iClassTargerCount;
            copied.iClassTargetId = this.iClassTargetId;
            copied.iCoinNum = this.iCoinNum;
            copied.i3StarLevelUpHeroId = this.i3StarLevelUpHeroId;
            return copied;
        }
    }
}

