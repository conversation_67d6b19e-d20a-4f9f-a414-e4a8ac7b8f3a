// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCManual_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public int iMinClientVersion = 0;

        public int iMaxClientVersion = 0;

        public string sBeginTime = "";

        public string sEndTime = "";

        public int iBeginTime = 0;

        public int iEndTime = 0;

        public int iCommonBPID = 0;

        public string sVoteBeginTime = "";

        public string sVoteEndTime = "";

        public int iVoteBeginTime = 0;

        public int iVoteEndTime = 0;

        public int iCommonMallID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(iMinClientVersion, 2);
            _os.Write(iMaxClientVersion, 3);
            _os.Write(sBeginTime, 4);
            _os.Write(sEndTime, 5);
            _os.Write(iBeginTime, 6);
            _os.Write(iEndTime, 7);
            _os.Write(iCommonBPID, 8);
            _os.Write(sVoteBeginTime, 13);
            _os.Write(sVoteEndTime, 14);
            _os.Write(iVoteBeginTime, 15);
            _os.Write(iVoteEndTime, 16);
            _os.Write(iCommonMallID, 17);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int)_is.Read(iID, 0, false);

            sName = (string)_is.Read(sName, 1, false);

            iMinClientVersion = (int)_is.Read(iMinClientVersion, 2, false);

            iMaxClientVersion = (int)_is.Read(iMaxClientVersion, 3, false);

            sBeginTime = (string)_is.Read(sBeginTime, 4, false);

            sEndTime = (string)_is.Read(sEndTime, 5, false);

            iBeginTime = (int)_is.Read(iBeginTime, 6, false);

            iEndTime = (int)_is.Read(iEndTime, 7, false);

            iCommonBPID = (int)_is.Read(iCommonBPID, 8, false);

            sVoteBeginTime = (string)_is.Read(sVoteBeginTime, 13, false);

            sVoteEndTime = (string)_is.Read(sVoteEndTime, 14, false);

            iVoteBeginTime = (int)_is.Read(iVoteBeginTime, 15, false);

            iVoteEndTime = (int)_is.Read(iVoteEndTime, 16, false);

            iCommonMallID = (int)_is.Read(iCommonMallID, 17, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(iMinClientVersion, "iMinClientVersion");
            _ds.Display(iMaxClientVersion, "iMaxClientVersion");
            _ds.Display(sBeginTime, "sBeginTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(iBeginTime, "iBeginTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(iCommonBPID, "iCommonBPID");
            _ds.Display(sVoteBeginTime, "sVoteBeginTime");
            _ds.Display(sVoteEndTime, "sVoteEndTime");
            _ds.Display(iVoteBeginTime, "iVoteBeginTime");
            _ds.Display(iVoteEndTime, "iVoteEndTime");
            _ds.Display(iCommonMallID, "iCommonMallID");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            iMinClientVersion = 0;
            iMaxClientVersion = 0;
            sBeginTime = "";
            sEndTime = "";
            iBeginTime = 0;
            iEndTime = 0;
            iCommonBPID = 0;
            sVoteBeginTime = "";
            sVoteEndTime = "";
            iVoteBeginTime = 0;
            iVoteEndTime = 0;
            iCommonMallID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCManual_Server();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.iMinClientVersion = this.iMinClientVersion;
            copied.iMaxClientVersion = this.iMaxClientVersion;
            copied.sBeginTime = this.sBeginTime;
            copied.sEndTime = this.sEndTime;
            copied.iBeginTime = this.iBeginTime;
            copied.iEndTime = this.iEndTime;
            copied.iCommonBPID = this.iCommonBPID;
            copied.sVoteBeginTime = this.sVoteBeginTime;
            copied.sVoteEndTime = this.sVoteEndTime;
            copied.iVoteBeginTime = this.iVoteBeginTime;
            copied.iVoteEndTime = this.iVoteEndTime;
            copied.iCommonMallID = this.iCommonMallID;
            return copied;
        }
    }
}

