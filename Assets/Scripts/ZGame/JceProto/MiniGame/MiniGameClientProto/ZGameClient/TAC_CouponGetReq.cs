// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_CouponGetReq : Wup.Jce.JceStruct
    {
        public TMidasTokenInfo stToken;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stToken, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stToken = (TMidasTokenInfo) _is.Read(stToken, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stToken, "stToken");
        }

        public override void Clear()
        {
            stToken = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_CouponGetReq();
            copied.stToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stToken);
            return copied;
        }
    }
}

