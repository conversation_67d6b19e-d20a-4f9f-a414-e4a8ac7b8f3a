// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_BattleResult : Wup.Jce.JceStruct
    {
        int _iPlayerID = 0;
        public int iPlayerID
        {
            get
            {
                 return _iPlayerID;
            }
            set
            {
                _iPlayerID = value; 
            }
        }

        int _iMoneyEarned = 0;
        public int iMoneyEarned
        {
            get
            {
                 return _iMoneyEarned;
            }
            set
            {
                _iMoneyEarned = value; 
            }
        }

        int _iMoneyLeft = 0;
        public int iMoneyLeft
        {
            get
            {
                 return _iMoneyLeft;
            }
            set
            {
                _iMoneyLeft = value; 
            }
        }

        int _iPlayerHpLost = 0;
        public int iPlayerHpLost
        {
            get
            {
                 return _iPlayerHpLost;
            }
            set
            {
                _iPlayerHpLost = value; 
            }
        }

        int _iPlayerHpLeft = 0;
        public int iPlayerHpLeft
        {
            get
            {
                 return _iPlayerHpLeft;
            }
            set
            {
                _iPlayerHpLeft = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> mMoneyEarnedInfo {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iPlayerID, 0);
            _os.Write(iMoneyEarned, 1);
            _os.Write(iMoneyLeft, 2);
            _os.Write(iPlayerHpLost, 3);
            _os.Write(iPlayerHpLeft, 4);
            _os.Write(mMoneyEarnedInfo, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iPlayerID = (int) _is.Read(iPlayerID, 0, false);

            iMoneyEarned = (int) _is.Read(iMoneyEarned, 1, false);

            iMoneyLeft = (int) _is.Read(iMoneyLeft, 2, false);

            iPlayerHpLost = (int) _is.Read(iPlayerHpLost, 3, false);

            iPlayerHpLeft = (int) _is.Read(iPlayerHpLeft, 4, false);

            mMoneyEarnedInfo = (TKFrame.TKDictionary<int, int>) _is.Read(mMoneyEarnedInfo, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iPlayerID, "iPlayerID");
            _ds.Display(iMoneyEarned, "iMoneyEarned");
            _ds.Display(iMoneyLeft, "iMoneyLeft");
            _ds.Display(iPlayerHpLost, "iPlayerHpLost");
            _ds.Display(iPlayerHpLeft, "iPlayerHpLeft");
            _ds.Display(mMoneyEarnedInfo, "mMoneyEarnedInfo");
        }

        public override void Clear()
        {
            iPlayerID = 0;
            iMoneyEarned = 0;
            iMoneyLeft = 0;
            iPlayerHpLost = 0;
            iPlayerHpLeft = 0;
            mMoneyEarnedInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_BattleResult();
            copied.iPlayerID = this.iPlayerID;
            copied.iMoneyEarned = this.iMoneyEarned;
            copied.iMoneyLeft = this.iMoneyLeft;
            copied.iPlayerHpLost = this.iPlayerHpLost;
            copied.iPlayerHpLeft = this.iPlayerHpLeft;
            copied.mMoneyEarnedInfo = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mMoneyEarnedInfo);
            return copied;
        }
    }
}

