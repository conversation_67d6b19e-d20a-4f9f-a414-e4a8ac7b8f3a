// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GamePlaySendMsgRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public string content = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(content, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            content = (string) _is.Read(content, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(content, "content");
        }

        public override void Clear()
        {
            iRet = 0;
            content = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GamePlaySendMsgRsp();
            copied.iRet = this.iRet;
            copied.content = this.content;
            return copied;
        }
    }
}

