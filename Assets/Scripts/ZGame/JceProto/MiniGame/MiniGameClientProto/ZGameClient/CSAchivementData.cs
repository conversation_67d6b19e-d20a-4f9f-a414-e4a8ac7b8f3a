// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CSAchivementData : Wup.Jce.JceStruct
    {
        public ACG_TaskInfo stTaskInfo;

        public bool bIsRead = true;

        public int iHideType = 0;

        public int iPossessRate = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stTaskInfo, 0);
            _os.Write(bIsRead, 1);
            _os.Write(iHideType, 2);
            _os.Write(iPossessRate, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stTaskInfo = (ACG_TaskInfo) _is.Read(stTaskInfo, 0, false);

            bIsRead = (bool) _is.Read(bIsRead, 1, false);

            iHideType = (int) _is.Read(iHideType, 2, false);

            iPossessRate = (int) _is.Read(iPossessRate, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stTaskInfo, "stTaskInfo");
            _ds.Display(bIsRead, "bIsRead");
            _ds.Display(iHideType, "iHideType");
            _ds.Display(iPossessRate, "iPossessRate");
        }

        public override void Clear()
        {
            stTaskInfo = null;
            bIsRead = true;
            iHideType = 0;
            iPossessRate = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CSAchivementData();
            copied.stTaskInfo = (ACG_TaskInfo)JceUtil.DeepClone(this.stTaskInfo);
            copied.bIsRead = this.bIsRead;
            copied.iHideType = this.iHideType;
            copied.iPossessRate = this.iPossessRate;
            return copied;
        }
    }
}

