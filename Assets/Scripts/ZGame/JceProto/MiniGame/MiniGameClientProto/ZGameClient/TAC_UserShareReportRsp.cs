// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_UserShareReportRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public bool bHasSendShareDropMail = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(bHasSendShareDropMail, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            bHasSendShareDropMail = (bool) _is.Read(bHasSendShareDropMail, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(bHasSendShareDropMail, "bHasSendShareDropMail");
        }

        public override void Clear()
        {
            iRet = 0;
            bHasSendShareDropMail = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_UserShareReportRsp();
            copied.iRet = this.iRet;
            copied.bHasSendShareDropMail = this.bHasSendShareDropMail;
            return copied;
        }
    }
}

