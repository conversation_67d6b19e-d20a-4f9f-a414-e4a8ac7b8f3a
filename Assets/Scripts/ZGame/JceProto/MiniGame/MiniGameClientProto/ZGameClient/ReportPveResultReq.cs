// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ReportPveResultReq : Wup.Jce.JceStruct
    {
        public int gameModuleID = 0;

        public int levelID = 0;

        public bool isSucc = false;

        public int challengeDuration = 0;

        public string currentLineup = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(gameModuleID, 0);
            _os.Write(levelID, 1);
            _os.Write(isSucc, 2);
            _os.Write(challengeDuration, 3);
            _os.Write(currentLineup, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            gameModuleID = (int) _is.Read(gameModuleID, 0, false);

            levelID = (int) _is.Read(levelID, 1, false);

            isSucc = (bool) _is.Read(isSucc, 2, false);

            challengeDuration = (int) _is.Read(challengeDuration, 3, false);

            currentLineup = (string) _is.Read(currentLineup, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(gameModuleID, "gameModuleID");
            _ds.Display(levelID, "levelID");
            _ds.Display(isSucc, "isSucc");
            _ds.Display(challengeDuration, "challengeDuration");
            _ds.Display(currentLineup, "currentLineup");
        }

        public override void Clear()
        {
            gameModuleID = 0;
            levelID = 0;
            isSucc = false;
            challengeDuration = 0;
            currentLineup = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ReportPveResultReq();
            copied.gameModuleID = this.gameModuleID;
            copied.levelID = this.levelID;
            copied.isSucc = this.isSucc;
            copied.challengeDuration = this.challengeDuration;
            copied.currentLineup = this.currentLineup;
            return copied;
        }
    }
}

