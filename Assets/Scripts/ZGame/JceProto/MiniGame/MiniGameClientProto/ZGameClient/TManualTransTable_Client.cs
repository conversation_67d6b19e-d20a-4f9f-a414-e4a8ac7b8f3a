//所在的Excel 【AATransColumns.xlsm】
//****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TManualTransTable_Client : Wup.Jce.JceStruct
    {
        public int iIndexID = 0;

        public string sCNName = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndexID, 0);
            _os.Write(sCNName, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndexID = (int) _is.Read(iIndexID, 0, false);

            sCNName = (string) _is.Read(sCNName, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndexID, "iIndexID");
            _ds.Display(sCNName, "sCNName");
        }

        public override void Clear()
        {
            iIndexID = 0;
            sCNName = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TManualTransTable_Client();
            copied.iIndexID = this.iIndexID;
            copied.sCNName = this.sCNName;
            return copied;
        }
    }
}

