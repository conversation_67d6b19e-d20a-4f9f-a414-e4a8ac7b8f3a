// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterGetDetailRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<GiftCenterRecvGiftRecord> vecRecvGift;

        public System.Collections.Generic.List<GiftCenterGiveGiftRecord> vecGiveGift;

        public System.Collections.Generic.List<GiftCenterWantGiftRecord> vecWantGift;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecRecvGift, 1);
            _os.Write(vecGiveGift, 2);
            _os.Write(vecWantGift, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecRecvGift = (System.Collections.Generic.List<GiftCenterRecvGiftRecord>) _is.Read(vecRecvGift, 1, false);

            vecGiveGift = (System.Collections.Generic.List<GiftCenterGiveGiftRecord>) _is.Read(vecGiveGift, 2, false);

            vecWantGift = (System.Collections.Generic.List<GiftCenterWantGiftRecord>) _is.Read(vecWantGift, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecRecvGift, "vecRecvGift");
            _ds.Display(vecGiveGift, "vecGiveGift");
            _ds.Display(vecWantGift, "vecWantGift");
        }

        public override void Clear()
        {
            iRet = 0;
            vecRecvGift = null;
            vecGiveGift = null;
            vecWantGift = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterGetDetailRsp();
            copied.iRet = this.iRet;
            copied.vecRecvGift = (System.Collections.Generic.List<GiftCenterRecvGiftRecord>)JceUtil.DeepClone(this.vecRecvGift);
            copied.vecGiveGift = (System.Collections.Generic.List<GiftCenterGiveGiftRecord>)JceUtil.DeepClone(this.vecGiveGift);
            copied.vecWantGift = (System.Collections.Generic.List<GiftCenterWantGiftRecord>)JceUtil.DeepClone(this.vecWantGift);
            return copied;
        }
    }
}

