// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TModeNameConfig_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSeasonId = 0;

        public int iModeID = 0;

        public string sDesc = "";

        public string sRank = "";

        public int iSetID = 0;

        public int iACGSetIndex = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSeasonId, 1);
            _os.Write(iModeID, 2);
            _os.Write(sDesc, 3);
            _os.Write(sRank, 4);
            _os.Write(iSetID, 5);
            _os.Write(iACGSetIndex, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSeasonId = (int) _is.Read(iSeasonId, 1, false);

            iModeID = (int) _is.Read(iModeID, 2, false);

            sDesc = (string) _is.Read(sDesc, 3, false);

            sRank = (string) _is.Read(sRank, 4, false);

            iSetID = (int) _is.Read(iSetID, 5, false);

            iACGSetIndex = (int) _is.Read(iACGSetIndex, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSeasonId, "iSeasonId");
            _ds.Display(iModeID, "iModeID");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sRank, "sRank");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iACGSetIndex, "iACGSetIndex");
        }

        public override void Clear()
        {
            iID = 0;
            iSeasonId = 0;
            iModeID = 0;
            sDesc = "";
            sRank = "";
            iSetID = 0;
            iACGSetIndex = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TModeNameConfig_Client();
            copied.iID = this.iID;
            copied.iSeasonId = this.iSeasonId;
            copied.iModeID = this.iModeID;
            copied.sDesc = this.sDesc;
            copied.sRank = this.sRank;
            copied.iSetID = this.iSetID;
            copied.iACGSetIndex = this.iACGSetIndex;
            return copied;
        }
    }
}

