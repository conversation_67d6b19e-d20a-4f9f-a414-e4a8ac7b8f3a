// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCardBroadcastItem_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSetID = 0;

        public int iStartID = 0;

        public int iEndID = 0;

        public string sIcon = "";

        public string sItemName = "";

        public string sGetWay = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSetID, 1);
            _os.Write(iStartID, 2);
            _os.Write(iEndID, 3);
            _os.Write(sIcon, 4);
            _os.Write(sItemName, 5);
            _os.Write(sGetWay, 6);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSetID = (int) _is.Read(iSetID, 1, false);

            iStartID = (int) _is.Read(iStartID, 2, false);

            iEndID = (int) _is.Read(iEndID, 3, false);

            sIcon = (string) _is.Read(sIcon, 4, false);

            sItemName = (string) _is.Read(sItemName, 5, false);

            sGetWay = (string) _is.Read(sGetWay, 6, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iStartID, "iStartID");
            _ds.Display(iEndID, "iEndID");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(sItemName, "sItemName");
            _ds.Display(sGetWay, "sGetWay");
        }

        public override void Clear()
        {
            iID = 0;
            iSetID = 0;
            iStartID = 0;
            iEndID = 0;
            sIcon = "";
            sItemName = "";
            sGetWay = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCardBroadcastItem_Client();
            copied.iID = this.iID;
            copied.iSetID = this.iSetID;
            copied.iStartID = this.iStartID;
            copied.iEndID = this.iEndID;
            copied.sIcon = this.sIcon;
            copied.sItemName = this.sItemName;
            copied.sGetWay = this.sGetWay;
            return copied;
        }
    }
}

