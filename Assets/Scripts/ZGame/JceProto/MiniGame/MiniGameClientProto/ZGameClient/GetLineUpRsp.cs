// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GetLineUpRsp : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<int, string> lineUpMap;

        public TKFrame.TKDictionary<int, TKFrame.TKDictionary<int, string>> setLineUpMap;

        public TKFrame.TKDictionary<int, TSetConfig_Server> setConfigMap;

        public int maxLineUpSize = 0;

        public int curLineUpSize = 0;

        public bool isPrivilege = true;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lineUpMap, 0);
            _os.Write(setLineUpMap, 1);
            _os.Write(setConfigMap, 2);
            _os.Write(maxLineUpSize, 3);
            _os.Write(curLineUpSize, 4);
            _os.Write(isPrivilege, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lineUpMap = (TKFrame.TKDictionary<int, string>) _is.Read(lineUpMap, 0, false);

            setLineUpMap = (TKFrame.TKDictionary<int, TKFrame.TKDictionary<int, string>>) _is.Read(setLineUpMap, 1, false);

            setConfigMap = (TKFrame.TKDictionary<int, TSetConfig_Server>) _is.Read(setConfigMap, 2, false);

            maxLineUpSize = (int) _is.Read(maxLineUpSize, 3, false);

            curLineUpSize = (int) _is.Read(curLineUpSize, 4, false);

            isPrivilege = (bool) _is.Read(isPrivilege, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lineUpMap, "lineUpMap");
            _ds.Display(setLineUpMap, "setLineUpMap");
            _ds.Display(setConfigMap, "setConfigMap");
            _ds.Display(maxLineUpSize, "maxLineUpSize");
            _ds.Display(curLineUpSize, "curLineUpSize");
            _ds.Display(isPrivilege, "isPrivilege");
        }

        public override void Clear()
        {
            lineUpMap = null;
            setLineUpMap = null;
            setConfigMap = null;
            maxLineUpSize = 0;
            curLineUpSize = 0;
            isPrivilege = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GetLineUpRsp();
            copied.lineUpMap = (TKFrame.TKDictionary<int, string>)JceUtil.DeepClone(this.lineUpMap);
            copied.setLineUpMap = (TKFrame.TKDictionary<int, TKFrame.TKDictionary<int, string>>)JceUtil.DeepClone(this.setLineUpMap);
            copied.setConfigMap = (TKFrame.TKDictionary<int, TSetConfig_Server>)JceUtil.DeepClone(this.setConfigMap);
            copied.maxLineUpSize = this.maxLineUpSize;
            copied.curLineUpSize = this.curLineUpSize;
            copied.isPrivilege = this.isPrivilege;
            return copied;
        }
    }
}

