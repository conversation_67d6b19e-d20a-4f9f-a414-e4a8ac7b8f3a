// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TFriendStateInfo : Wup.Jce.JceStruct
    {
        public TUserID uid {get; set;} 

        int _nGameStatus = 0;
        public int nGameStatus
        {
            get
            {
                 return _nGameStatus;
            }
            set
            {
                _nGameStatus = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(uid, 0);
            _os.Write(nGameStatus, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            uid = (TUserID) _is.Read(uid, 0, false);

            nGameStatus = (int) _is.Read(nGameStatus, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(uid, "uid");
            _ds.Display(nGameStatus, "nGameStatus");
        }

        public override void Clear()
        {
            uid = null;
            nGameStatus = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TFriendStateInfo();
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            copied.nGameStatus = this.nGameStatus;
            return copied;
        }
    }
}

