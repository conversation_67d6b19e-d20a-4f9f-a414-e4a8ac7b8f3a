// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TPlayerId : Wup.Jce.JceStruct
    {
        public long lUin = 0;

        public int iOpenPlatType = 0;

        public int iMobilePlatType = 0;

        public int iZoneID = 0;

        public int iChallengeAreaId = 0;

        public int iScene = 0;

        public string sOpenID = "";

        public string sNickName = "";

        public string sFaceUrl = "";

        public int iGender = 0;

        public int iHeaderIcon = 0;

        public int iAvatarBoxID = 0;

        public string sPlatformOpenID = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lUin, 0);
            _os.Write(iOpenPlatType, 1);
            _os.Write(iMobilePlatType, 2);
            _os.Write(iZoneID, 3);
            _os.Write(iChallengeAreaId, 4);
            _os.Write(iScene, 5);
            _os.Write(sOpenID, 6);
            _os.Write(sNickName, 7);
            _os.Write(sFaceUrl, 8);
            _os.Write(iGender, 9);
            _os.Write(iHeaderIcon, 10);
            _os.Write(iAvatarBoxID, 11);
            _os.Write(sPlatformOpenID, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lUin = (long) _is.Read(lUin, 0, false);

            iOpenPlatType = (int) _is.Read(iOpenPlatType, 1, false);

            iMobilePlatType = (int) _is.Read(iMobilePlatType, 2, false);

            iZoneID = (int) _is.Read(iZoneID, 3, false);

            iChallengeAreaId = (int) _is.Read(iChallengeAreaId, 4, false);

            iScene = (int) _is.Read(iScene, 5, false);

            sOpenID = (string) _is.Read(sOpenID, 6, false);

            sNickName = (string) _is.Read(sNickName, 7, false);

            sFaceUrl = (string) _is.Read(sFaceUrl, 8, false);

            iGender = (int) _is.Read(iGender, 9, false);

            iHeaderIcon = (int) _is.Read(iHeaderIcon, 10, false);

            iAvatarBoxID = (int) _is.Read(iAvatarBoxID, 11, false);

            sPlatformOpenID = (string) _is.Read(sPlatformOpenID, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lUin, "lUin");
            _ds.Display(iOpenPlatType, "iOpenPlatType");
            _ds.Display(iMobilePlatType, "iMobilePlatType");
            _ds.Display(iZoneID, "iZoneID");
            _ds.Display(iChallengeAreaId, "iChallengeAreaId");
            _ds.Display(iScene, "iScene");
            _ds.Display(sOpenID, "sOpenID");
            _ds.Display(sNickName, "sNickName");
            _ds.Display(sFaceUrl, "sFaceUrl");
            _ds.Display(iGender, "iGender");
            _ds.Display(iHeaderIcon, "iHeaderIcon");
            _ds.Display(iAvatarBoxID, "iAvatarBoxID");
            _ds.Display(sPlatformOpenID, "sPlatformOpenID");
        }

        public override void Clear()
        {
            lUin = 0;
            iOpenPlatType = 0;
            iMobilePlatType = 0;
            iZoneID = 0;
            iChallengeAreaId = 0;
            iScene = 0;
            sOpenID = "";
            sNickName = "";
            sFaceUrl = "";
            iGender = 0;
            iHeaderIcon = 0;
            iAvatarBoxID = 0;
            sPlatformOpenID = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TPlayerId();
            copied.lUin = this.lUin;
            copied.iOpenPlatType = this.iOpenPlatType;
            copied.iMobilePlatType = this.iMobilePlatType;
            copied.iZoneID = this.iZoneID;
            copied.iChallengeAreaId = this.iChallengeAreaId;
            copied.iScene = this.iScene;
            copied.sOpenID = this.sOpenID;
            copied.sNickName = this.sNickName;
            copied.sFaceUrl = this.sFaceUrl;
            copied.iGender = this.iGender;
            copied.iHeaderIcon = this.iHeaderIcon;
            copied.iAvatarBoxID = this.iAvatarBoxID;
            copied.sPlatformOpenID = this.sPlatformOpenID;
            return copied;
        }
    }
}

