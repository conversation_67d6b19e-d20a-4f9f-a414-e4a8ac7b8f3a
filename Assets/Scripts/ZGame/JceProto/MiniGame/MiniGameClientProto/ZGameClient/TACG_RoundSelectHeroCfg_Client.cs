// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_RoundSelectHeroCfg_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iHeroNum1 = 0;

        public int iHeroNum2 = 0;

        public int iHeroNum3 = 0;

        public int iHeroNum4 = 0;

        public int iHeroNum5 = 0;

        public string sHeroStar1 = "";

        public string sHeroStar2 = "";

        public string sHeroStar3 = "";

        public string sHeroStar4 = "";

        public string sHeroStar5 = "";

        public int iType = 0;

        public string sExtraParams = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iHeroNum1, 1);
            _os.Write(iHeroNum2, 2);
            _os.Write(iHeroNum3, 3);
            _os.Write(iHeroNum4, 4);
            _os.Write(iHeroNum5, 5);
            _os.Write(sHeroStar1, 6);
            _os.Write(sHeroStar2, 7);
            _os.Write(sHeroStar3, 8);
            _os.Write(sHeroStar4, 9);
            _os.Write(sHeroStar5, 10);
            _os.Write(iType, 11);
            _os.Write(sExtraParams, 12);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iHeroNum1 = (int) _is.Read(iHeroNum1, 1, false);

            iHeroNum2 = (int) _is.Read(iHeroNum2, 2, false);

            iHeroNum3 = (int) _is.Read(iHeroNum3, 3, false);

            iHeroNum4 = (int) _is.Read(iHeroNum4, 4, false);

            iHeroNum5 = (int) _is.Read(iHeroNum5, 5, false);

            sHeroStar1 = (string) _is.Read(sHeroStar1, 6, false);

            sHeroStar2 = (string) _is.Read(sHeroStar2, 7, false);

            sHeroStar3 = (string) _is.Read(sHeroStar3, 8, false);

            sHeroStar4 = (string) _is.Read(sHeroStar4, 9, false);

            sHeroStar5 = (string) _is.Read(sHeroStar5, 10, false);

            iType = (int) _is.Read(iType, 11, false);

            sExtraParams = (string) _is.Read(sExtraParams, 12, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iHeroNum1, "iHeroNum1");
            _ds.Display(iHeroNum2, "iHeroNum2");
            _ds.Display(iHeroNum3, "iHeroNum3");
            _ds.Display(iHeroNum4, "iHeroNum4");
            _ds.Display(iHeroNum5, "iHeroNum5");
            _ds.Display(sHeroStar1, "sHeroStar1");
            _ds.Display(sHeroStar2, "sHeroStar2");
            _ds.Display(sHeroStar3, "sHeroStar3");
            _ds.Display(sHeroStar4, "sHeroStar4");
            _ds.Display(sHeroStar5, "sHeroStar5");
            _ds.Display(iType, "iType");
            _ds.Display(sExtraParams, "sExtraParams");
        }

        public override void Clear()
        {
            iID = 0;
            iHeroNum1 = 0;
            iHeroNum2 = 0;
            iHeroNum3 = 0;
            iHeroNum4 = 0;
            iHeroNum5 = 0;
            sHeroStar1 = "";
            sHeroStar2 = "";
            sHeroStar3 = "";
            sHeroStar4 = "";
            sHeroStar5 = "";
            iType = 0;
            sExtraParams = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_RoundSelectHeroCfg_Client();
            copied.iID = this.iID;
            copied.iHeroNum1 = this.iHeroNum1;
            copied.iHeroNum2 = this.iHeroNum2;
            copied.iHeroNum3 = this.iHeroNum3;
            copied.iHeroNum4 = this.iHeroNum4;
            copied.iHeroNum5 = this.iHeroNum5;
            copied.sHeroStar1 = this.sHeroStar1;
            copied.sHeroStar2 = this.sHeroStar2;
            copied.sHeroStar3 = this.sHeroStar3;
            copied.sHeroStar4 = this.sHeroStar4;
            copied.sHeroStar5 = this.sHeroStar5;
            copied.iType = this.iType;
            copied.sExtraParams = this.sExtraParams;
            return copied;
        }
    }
}

