// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CardCollectGetSetCollectionBySetIDResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public int setID = 0;

        public TKFrame.TKDictionary<int, CardData> cardDataPerHero;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(setID, 1);
            _os.Write(cardDataPerHero, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            setID = (int) _is.Read(setID, 1, false);

            cardDataPerHero = (TKFrame.TKDictionary<int, CardData>) _is.Read(cardDataPerHero, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(setID, "setID");
            _ds.Display(cardDataPerHero, "cardDataPerHero");
        }

        public override void Clear()
        {
            err = 0;
            setID = 0;
            cardDataPerHero = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CardCollectGetSetCollectionBySetIDResp();
            copied.err = this.err;
            copied.setID = this.setID;
            copied.cardDataPerHero = (TKFrame.TKDictionary<int, CardData>)JceUtil.DeepClone(this.cardDataPerHero);
            return copied;
        }
    }
}

