// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_MallEggInfo : Wup.Jce.JceStruct
    {
        public int iItemId = 0;

        public int iOwnedItemNum = 0;

        public int iLeftNum = 0;

        public ACG_MallGoodsInfo stMallGoodsInfo;

        public int iBatchOpenCount = 0;

        public System.Collections.Generic.List<TItemInfo> vecDropInfo;

        public string strEggName = "";

        public string strEggDesc = "";

        public string strNoticeName = "";

        public string strNoticeDesc = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iItemId, 0);
            _os.Write(iOwnedItemNum, 1);
            _os.Write(iLeftNum, 2);
            _os.Write(stMallGoodsInfo, 3);
            _os.Write(iBatchOpenCount, 4);
            _os.Write(vecDropInfo, 5);
            _os.Write(strEggName, 6);
            _os.Write(strEggDesc, 7);
            _os.Write(strNoticeName, 8);
            _os.Write(strNoticeDesc, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iItemId = (int) _is.Read(iItemId, 0, false);

            iOwnedItemNum = (int) _is.Read(iOwnedItemNum, 1, false);

            iLeftNum = (int) _is.Read(iLeftNum, 2, false);

            stMallGoodsInfo = (ACG_MallGoodsInfo) _is.Read(stMallGoodsInfo, 3, false);

            iBatchOpenCount = (int) _is.Read(iBatchOpenCount, 4, false);

            vecDropInfo = (System.Collections.Generic.List<TItemInfo>) _is.Read(vecDropInfo, 5, false);

            strEggName = (string) _is.Read(strEggName, 6, false);

            strEggDesc = (string) _is.Read(strEggDesc, 7, false);

            strNoticeName = (string) _is.Read(strNoticeName, 8, false);

            strNoticeDesc = (string) _is.Read(strNoticeDesc, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iItemId, "iItemId");
            _ds.Display(iOwnedItemNum, "iOwnedItemNum");
            _ds.Display(iLeftNum, "iLeftNum");
            _ds.Display(stMallGoodsInfo, "stMallGoodsInfo");
            _ds.Display(iBatchOpenCount, "iBatchOpenCount");
            _ds.Display(vecDropInfo, "vecDropInfo");
            _ds.Display(strEggName, "strEggName");
            _ds.Display(strEggDesc, "strEggDesc");
            _ds.Display(strNoticeName, "strNoticeName");
            _ds.Display(strNoticeDesc, "strNoticeDesc");
        }

        public override void Clear()
        {
            iItemId = 0;
            iOwnedItemNum = 0;
            iLeftNum = 0;
            stMallGoodsInfo = null;
            iBatchOpenCount = 0;
            vecDropInfo = null;
            strEggName = "";
            strEggDesc = "";
            strNoticeName = "";
            strNoticeDesc = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_MallEggInfo();
            copied.iItemId = this.iItemId;
            copied.iOwnedItemNum = this.iOwnedItemNum;
            copied.iLeftNum = this.iLeftNum;
            copied.stMallGoodsInfo = (ACG_MallGoodsInfo)JceUtil.DeepClone(this.stMallGoodsInfo);
            copied.iBatchOpenCount = this.iBatchOpenCount;
            copied.vecDropInfo = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.vecDropInfo);
            copied.strEggName = this.strEggName;
            copied.strEggDesc = this.strEggDesc;
            copied.strNoticeName = this.strNoticeName;
            copied.strNoticeDesc = this.strNoticeDesc;
            return copied;
        }
    }
}

