// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class MainHallTopEntranceData : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<TMainHallTopEntrance> vecTopEntrance;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(vecTopEntrance, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            vecTopEntrance = (System.Collections.Generic.List<TMainHallTopEntrance>) _is.Read(vecTopEntrance, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(vecTopEntrance, "vecTopEntrance");
        }

        public override void Clear()
        {
            vecTopEntrance = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new MainHallTopEntranceData();
            copied.vecTopEntrance = (System.Collections.Generic.List<TMainHallTopEntrance>)JceUtil.DeepClone(this.vecTopEntrance);
            return copied;
        }
    }
}

