// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class SetQuickChatReq : Wup.Jce.JceStruct
    {
        public TKFrame.TKDictionary<int, int> mapEquip;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(mapEquip, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            mapEquip = (TKFrame.TKDictionary<int, int>) _is.Read(mapEquip, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(mapEquip, "mapEquip");
        }

        public override void Clear()
        {
            mapEquip = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new SetQuickChatReq();
            copied.mapEquip = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapEquip);
            return copied;
        }
    }
}

