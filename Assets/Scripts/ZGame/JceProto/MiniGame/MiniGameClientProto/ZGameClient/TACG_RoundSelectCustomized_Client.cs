//所在的Excel 【ACG_HeroCustomized.xlsm】
//************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_RoundSelectCustomized_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSceneID = 0;

        public int iRoundSelectID = 0;

        public string sRoundSelectHeroId = "";

        public string sRoundSelectEquipmentId = "";

        public string sRoundSelectHeroPos = "";

        public string sRotationSpeed = "";

        public int iSetID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSceneID, 1);
            _os.Write(iRoundSelectID, 2);
            _os.Write(sRoundSelectHeroId, 3);
            _os.Write(sRoundSelectEquipmentId, 4);
            _os.Write(sRoundSelectHeroPos, 5);
            _os.Write(sRotationSpeed, 6);
            _os.Write(iSetID, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSceneID = (int) _is.Read(iSceneID, 1, false);

            iRoundSelectID = (int) _is.Read(iRoundSelectID, 2, false);

            sRoundSelectHeroId = (string) _is.Read(sRoundSelectHeroId, 3, false);

            sRoundSelectEquipmentId = (string) _is.Read(sRoundSelectEquipmentId, 4, false);

            sRoundSelectHeroPos = (string) _is.Read(sRoundSelectHeroPos, 5, false);

            sRotationSpeed = (string) _is.Read(sRotationSpeed, 6, false);

            iSetID = (int) _is.Read(iSetID, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSceneID, "iSceneID");
            _ds.Display(iRoundSelectID, "iRoundSelectID");
            _ds.Display(sRoundSelectHeroId, "sRoundSelectHeroId");
            _ds.Display(sRoundSelectEquipmentId, "sRoundSelectEquipmentId");
            _ds.Display(sRoundSelectHeroPos, "sRoundSelectHeroPos");
            _ds.Display(sRotationSpeed, "sRotationSpeed");
            _ds.Display(iSetID, "iSetID");
        }

        public override void Clear()
        {
            iID = 0;
            iSceneID = 0;
            iRoundSelectID = 0;
            sRoundSelectHeroId = "";
            sRoundSelectEquipmentId = "";
            sRoundSelectHeroPos = "";
            sRotationSpeed = "";
            iSetID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_RoundSelectCustomized_Client();
            copied.iID = this.iID;
            copied.iSceneID = this.iSceneID;
            copied.iRoundSelectID = this.iRoundSelectID;
            copied.sRoundSelectHeroId = this.sRoundSelectHeroId;
            copied.sRoundSelectEquipmentId = this.sRoundSelectEquipmentId;
            copied.sRoundSelectHeroPos = this.sRoundSelectHeroPos;
            copied.sRotationSpeed = this.sRotationSpeed;
            copied.iSetID = this.iSetID;
            return copied;
        }
    }
}

