//所在的Excel 【ErrorCode.xlsm】
//*********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_LoadingTips_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sTipsDes = "";

        public string sRankID = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sTipsDes, 1);
            _os.Write(sRankID, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sTipsDes = (string) _is.Read(sTipsDes, 1, false);

            sRankID = (string) _is.Read(sRankID, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sTipsDes, "sTipsDes");
            _ds.Display(sRankID, "sRankID");
        }

        public override void Clear()
        {
            iID = 0;
            sTipsDes = "";
            sRankID = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_LoadingTips_Client();
            copied.iID = this.iID;
            copied.sTipsDes = this.sTipsDes;
            copied.sRankID = this.sRankID;
            return copied;
        }
    }
}

