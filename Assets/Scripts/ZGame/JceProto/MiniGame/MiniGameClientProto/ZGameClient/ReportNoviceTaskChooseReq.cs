// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ReportNoviceTaskChooseReq : Wup.Jce.JceStruct
    {
        int _taskId = 0;
        public int taskId
        {
            get
            {
                 return _taskId;
            }
            set
            {
                _taskId = value; 
            }
        }

        long _id = 0;
        public long id
        {
            get
            {
                 return _id;
            }
            set
            {
                _id = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(taskId, 0);
            _os.Write(id, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            taskId = (int) _is.Read(taskId, 0, false);

            id = (long) _is.Read(id, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(taskId, "taskId");
            _ds.Display(id, "id");
        }

        public override void Clear()
        {
            taskId = 0;
            id = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ReportNoviceTaskChooseReq();
            copied.taskId = this.taskId;
            copied.id = this.id;
            return copied;
        }
    }
}

