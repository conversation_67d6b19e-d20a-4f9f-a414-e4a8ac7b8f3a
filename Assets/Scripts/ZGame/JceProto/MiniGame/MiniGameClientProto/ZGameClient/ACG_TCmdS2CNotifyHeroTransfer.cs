// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyHeroTransfer : Wup.Jce.JceStruct
    {
        int _iChairID = 0;
        public int iChairID
        {
            get
            {
                 return _iChairID;
            }
            set
            {
                _iChairID = value; 
            }
        }

        int _iTransferType = 0;
        public int iTransferType
        {
            get
            {
                 return _iTransferType;
            }
            set
            {
                _iTransferType = value; 
            }
        }

        int _iCheckHeroId = 0;
        public int iCheckHeroId
        {
            get
            {
                 return _iCheckHeroId;
            }
            set
            {
                _iCheckHeroId = value; 
            }
        }

        int _iTransferInterval = 0;
        public int iTransferInterval
        {
            get
            {
                 return _iTransferInterval;
            }
            set
            {
                _iTransferInterval = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> heroTransferList {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iChairID, 0);
            _os.Write(iTransferType, 1);
            _os.Write(iCheckHeroId, 2);
            _os.Write(iTransferInterval, 3);
            _os.Write(heroTransferList, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iChairID = (int) _is.Read(iChairID, 0, false);

            iTransferType = (int) _is.Read(iTransferType, 1, false);

            iCheckHeroId = (int) _is.Read(iCheckHeroId, 2, false);

            iTransferInterval = (int) _is.Read(iTransferInterval, 3, false);

            heroTransferList = (TKFrame.TKDictionary<int, int>) _is.Read(heroTransferList, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iChairID, "iChairID");
            _ds.Display(iTransferType, "iTransferType");
            _ds.Display(iCheckHeroId, "iCheckHeroId");
            _ds.Display(iTransferInterval, "iTransferInterval");
            _ds.Display(heroTransferList, "heroTransferList");
        }

        public override void Clear()
        {
            iChairID = 0;
            iTransferType = 0;
            iCheckHeroId = 0;
            iTransferInterval = 0;
            heroTransferList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyHeroTransfer();
            copied.iChairID = this.iChairID;
            copied.iTransferType = this.iTransferType;
            copied.iCheckHeroId = this.iCheckHeroId;
            copied.iTransferInterval = this.iTransferInterval;
            copied.heroTransferList = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.heroTransferList);
            return copied;
        }
    }
}

