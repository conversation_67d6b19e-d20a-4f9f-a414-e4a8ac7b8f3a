// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ChessPieceHurtEffectClass_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sName = "";

        public string sDecs = "";

        public int iPriority = 0;

        public string sChessPieceHurtEffectList = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sName, 1);
            _os.Write(sDecs, 2);
            _os.Write(iPriority, 3);
            _os.Write(sChessPieceHurtEffectList, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sName = (string) _is.Read(sName, 1, false);

            sDecs = (string) _is.Read(sDecs, 2, false);

            iPriority = (int) _is.Read(iPriority, 3, false);

            sChessPieceHurtEffectList = (string) _is.Read(sChessPieceHurtEffectList, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sName, "sName");
            _ds.Display(sDecs, "sDecs");
            _ds.Display(iPriority, "iPriority");
            _ds.Display(sChessPieceHurtEffectList, "sChessPieceHurtEffectList");
        }

        public override void Clear()
        {
            iID = 0;
            sName = "";
            sDecs = "";
            iPriority = 0;
            sChessPieceHurtEffectList = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ChessPieceHurtEffectClass_Client();
            copied.iID = this.iID;
            copied.sName = this.sName;
            copied.sDecs = this.sDecs;
            copied.iPriority = this.iPriority;
            copied.sChessPieceHurtEffectList = this.sChessPieceHurtEffectList;
            return copied;
        }
    }
}

