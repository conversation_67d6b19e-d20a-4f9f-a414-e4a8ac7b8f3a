// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_RegainData : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        public System.Collections.Generic.List<ACG_RegainPlayerData> vecRegainPlayerDatas {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecRegainPlayerDatas, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecRegainPlayerDatas = (System.Collections.Generic.List<ACG_RegainPlayerData>) _is.Read(vecRegainPlayerDatas, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecRegainPlayerDatas, "vecRegainPlayerDatas");
        }

        public override void Clear()
        {
            iRet = 0;
            vecRegainPlayerDatas = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_RegainData();
            copied.iRet = this.iRet;
            copied.vecRegainPlayerDatas = (System.Collections.Generic.List<ACG_RegainPlayerData>)JceUtil.DeepClone(this.vecRegainPlayerDatas);
            return copied;
        }
    }
}

