// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_GetUserProfileInfoReq : Wup.Jce.JceStruct
    {
        public TUserID stInquireUserID;

        public int iType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stInquireUserID, 0);
            _os.Write(iType, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stInquireUserID = (TUserID) _is.Read(stInquireUserID, 0, false);

            iType = (int) _is.Read(iType, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stInquireUserID, "stInquireUserID");
            _ds.Display(iType, "iType");
        }

        public override void Clear()
        {
            stInquireUserID = null;
            iType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_GetUserProfileInfoReq();
            copied.stInquireUserID = (TUserID)JceUtil.DeepClone(this.stInquireUserID);
            copied.iType = this.iType;
            return copied;
        }
    }
}

