// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CasualSeasonTaskInfo : Wup.Jce.JceStruct
    {
        public int taskID = 0;

        public TKFrame.TKDictionary<int, int> mapProgress;

        public int status = 0;

        public System.Collections.Generic.List<TItemInfo> vecItems;

        public string taskDes = "";

        public int tier = 0;

        public string taskTitle = "";

        public string progressTarget = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(taskID, 0);
            _os.Write(mapProgress, 1);
            _os.Write(status, 2);
            _os.Write(vecItems, 3);
            _os.Write(taskDes, 4);
            _os.Write(tier, 5);
            _os.Write(taskTitle, 6);
            _os.Write(progressTarget, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            taskID = (int) _is.Read(taskID, 0, false);

            mapProgress = (TKFrame.TKDictionary<int, int>) _is.Read(mapProgress, 1, false);

            status = (int) _is.Read(status, 2, false);

            vecItems = (System.Collections.Generic.List<TItemInfo>) _is.Read(vecItems, 3, false);

            taskDes = (string) _is.Read(taskDes, 4, false);

            tier = (int) _is.Read(tier, 5, false);

            taskTitle = (string) _is.Read(taskTitle, 6, false);

            progressTarget = (string) _is.Read(progressTarget, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(taskID, "taskID");
            _ds.Display(mapProgress, "mapProgress");
            _ds.Display(status, "status");
            _ds.Display(vecItems, "vecItems");
            _ds.Display(taskDes, "taskDes");
            _ds.Display(tier, "tier");
            _ds.Display(taskTitle, "taskTitle");
            _ds.Display(progressTarget, "progressTarget");
        }

        public override void Clear()
        {
            taskID = 0;
            mapProgress = null;
            status = 0;
            vecItems = null;
            taskDes = "";
            tier = 0;
            taskTitle = "";
            progressTarget = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CasualSeasonTaskInfo();
            copied.taskID = this.taskID;
            copied.mapProgress = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapProgress);
            copied.status = this.status;
            copied.vecItems = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.vecItems);
            copied.taskDes = this.taskDes;
            copied.tier = this.tier;
            copied.taskTitle = this.taskTitle;
            copied.progressTarget = this.progressTarget;
            return copied;
        }
    }
}

