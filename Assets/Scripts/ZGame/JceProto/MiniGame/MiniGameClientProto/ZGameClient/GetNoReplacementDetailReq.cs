// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GetNoReplacementDetailReq : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<int> lotteryIDList;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lotteryIDList, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lotteryIDList = (System.Collections.Generic.List<int>) _is.Read(lotteryIDList, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lotteryIDList, "lotteryIDList");
        }

        public override void Clear()
        {
            lotteryIDList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GetNoReplacementDetailReq();
            copied.lotteryIDList = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.lotteryIDList);
            return copied;
        }
    }
}

