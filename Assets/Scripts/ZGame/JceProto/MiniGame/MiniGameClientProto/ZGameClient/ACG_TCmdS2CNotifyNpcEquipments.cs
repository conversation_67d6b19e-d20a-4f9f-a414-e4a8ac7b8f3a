// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyNpcEquipments : Wup.Jce.JceStruct
    {
        int _iChairID = 0;
        public int iChairID
        {
            get
            {
                 return _iChairID;
            }
            set
            {
                _iChairID = value; 
            }
        }

        int _iEntityID = 0;
        public int iEntityID
        {
            get
            {
                 return _iEntityID;
            }
            set
            {
                _iEntityID = value; 
            }
        }

        public System.Collections.Generic.List<ACG_Equipment> npcEquipments {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iChairID, 0);
            _os.Write(iEntityID, 1);
            _os.Write(npcEquipments, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iChairID = (int) _is.Read(iChairID, 0, false);

            iEntityID = (int) _is.Read(iEntityID, 1, false);

            npcEquipments = (System.Collections.Generic.List<ACG_Equipment>) _is.Read(npcEquipments, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iChairID, "iChairID");
            _ds.Display(iEntityID, "iEntityID");
            _ds.Display(npcEquipments, "npcEquipments");
        }

        public override void Clear()
        {
            iChairID = 0;
            iEntityID = 0;
            npcEquipments = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyNpcEquipments();
            copied.iChairID = this.iChairID;
            copied.iEntityID = this.iEntityID;
            copied.npcEquipments = (System.Collections.Generic.List<ACG_Equipment>)JceUtil.DeepClone(this.npcEquipments);
            return copied;
        }
    }
}

