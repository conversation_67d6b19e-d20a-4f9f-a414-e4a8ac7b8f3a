// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class BusinessCardDataClient : Wup.Jce.JceStruct
    {
        public BusinessCardDataSingleClient stBackground;

        public BusinessCardDataSingleClient stFrame;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(stBackground, 0);
            _os.Write(stFrame, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            stBackground = (BusinessCardDataSingleClient) _is.Read(stBackground, 0, false);

            stFrame = (BusinessCardDataSingleClient) _is.Read(stFrame, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(stBackground, "stBackground");
            _ds.Display(stFrame, "stFrame");
        }

        public override void Clear()
        {
            stBackground = null;
            stFrame = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new BusinessCardDataClient();
            copied.stBackground = (BusinessCardDataSingleClient)JceUtil.DeepClone(this.stBackground);
            copied.stFrame = (BusinessCardDataSingleClient)JceUtil.DeepClone(this.stFrame);
            return copied;
        }
    }
}

