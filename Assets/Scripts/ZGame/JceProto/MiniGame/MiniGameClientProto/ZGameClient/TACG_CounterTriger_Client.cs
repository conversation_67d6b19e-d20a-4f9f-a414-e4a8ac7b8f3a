//所在的Excel 【ACG_BattleTipsHelper.xlsm】
//**********************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_CounterTriger_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iTriggerID = 0;

        public int iConditionID = 0;

        public int iTipsID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iTriggerID, 1);
            _os.Write(iConditionID, 2);
            _os.Write(iTipsID, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iTriggerID = (int) _is.Read(iTriggerID, 1, false);

            iConditionID = (int) _is.Read(iConditionID, 2, false);

            iTipsID = (int) _is.Read(iTipsID, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iTriggerID, "iTriggerID");
            _ds.Display(iConditionID, "iConditionID");
            _ds.Display(iTipsID, "iTipsID");
        }

        public override void Clear()
        {
            iID = 0;
            iTriggerID = 0;
            iConditionID = 0;
            iTipsID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_CounterTriger_Client();
            copied.iID = this.iID;
            copied.iTriggerID = this.iTriggerID;
            copied.iConditionID = this.iConditionID;
            copied.iTipsID = this.iTipsID;
            return copied;
        }
    }
}

