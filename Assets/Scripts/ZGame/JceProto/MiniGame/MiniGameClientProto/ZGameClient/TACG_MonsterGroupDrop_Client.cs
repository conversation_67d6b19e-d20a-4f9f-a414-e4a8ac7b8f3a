//所在的Excel 【ACG_Drop.xlsm】
//**********************************
// This file was generated by a TAF parser!
// Generated from `CGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_MonsterGroupDrop_Client : Wup.Jce.JceStruct
    {
        int _igroupID = 0;
        public int igroupID
        {
            get
            {
                 return _igroupID;
            }
            set
            {
                _igroupID = value; 
            }
        }

        string _sCoinDrops = "";
        public string sCoinDrops
        {
            get
            {
                 return _sCoinDrops;
            }
            set
            {
                _sCoinDrops = value; 
            }
        }

        string _sExpDrops = "";
        public string sExpDrops
        {
            get
            {
                 return _sExpDrops;
            }
            set
            {
                _sExpDrops = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(igroupID, 0);
            _os.Write(sCoinDrops, 1);
            _os.Write(sExpDrops, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            igroupID = (int) _is.Read(igroupID, 0, false);

            sCoinDrops = (string) _is.Read(sCoinDrops, 1, false);

            sExpDrops = (string) _is.Read(sExpDrops, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(igroupID, "igroupID");
            _ds.Display(sCoinDrops, "sCoinDrops");
            _ds.Display(sExpDrops, "sExpDrops");
        }

    }
}

