// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualFriendVoteAssistClientItem : Wup.Jce.JceStruct
    {
        public JOCManualVoteAssistClientUnit unit;

        public string name = "";

        public string pic = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(unit, 0);
            _os.Write(name, 1);
            _os.Write(pic, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            unit = (JOCManualVoteAssistClientUnit) _is.Read(unit, 0, false);

            name = (string) _is.Read(name, 1, false);

            pic = (string) _is.Read(pic, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(unit, "unit");
            _ds.Display(name, "name");
            _ds.Display(pic, "pic");
        }

        public override void Clear()
        {
            unit = null;
            name = "";
            pic = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualFriendVoteAssistClientItem();
            copied.unit = (JOCManualVoteAssistClientUnit)JceUtil.DeepClone(this.unit);
            copied.name = this.name;
            copied.pic = this.pic;
            return copied;
        }
    }
}

