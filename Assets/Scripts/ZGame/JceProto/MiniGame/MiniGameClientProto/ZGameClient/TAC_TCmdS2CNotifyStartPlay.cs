// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyStartPlay : Wup.Jce.JceStruct
    {
        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        bool _isFinalStart = true;
        public bool isFinalStart
        {
            get
            {
                 return _isFinalStart;
            }
            set
            {
                _isFinalStart = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(i8ChairID, 0);
            _os.Write(isFinalStart, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            i8ChairID = (int) _is.Read(i8ChairID, 0, false);

            isFinalStart = (bool) _is.Read(isFinalStart, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(isFinalStart, "isFinalStart");
        }

        public override void Clear()
        {
            i8ChairID = 0;
            isFinalStart = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyStartPlay();
            copied.i8ChairID = this.i8ChairID;
            copied.isFinalStart = this.isFinalStart;
            return copied;
        }
    }
}

