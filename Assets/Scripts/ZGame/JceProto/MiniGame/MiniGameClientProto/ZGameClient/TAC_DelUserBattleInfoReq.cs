// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_DelUserBattleInfoReq : Wup.Jce.JceStruct
    {
        int _iIndex = 0;
        public int iIndex
        {
            get
            {
                 return _iIndex;
            }
            set
            {
                _iIndex = value; 
            }
        }

        int _iBattleType = 0;
        public int iBattleType
        {
            get
            {
                 return _iBattleType;
            }
            set
            {
                _iBattleType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iBattleType, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iBattleType = (int) _is.Read(iBattleType, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iBattleType, "iBattleType");
        }

        public override void Clear()
        {
            iIndex = 0;
            iBattleType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_DelUserBattleInfoReq();
            copied.iIndex = this.iIndex;
            copied.iBattleType = this.iBattleType;
            return copied;
        }
    }
}

