//所在的Excel 【Achivement.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAchivementList_Client : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iID = 0;

        public string sResIcon = "";

        public int iQuality = 0;

        public string sName = "";

        public string sStartTime = "";

        public int iStartTime = 0;

        public string sDesc = "";

        public int iHideBeforeFinish = 0;

        public string sProgressTarget = "";

        public int iTargetType = 0;

        public string sCondition = "";

        public int iItemID1 = 0;

        public int iItemCount1 = 0;

        public int iItemID2 = 0;

        public int iItemCount2 = 0;

        public int iSerialID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iID, 1);
            _os.Write(sResIcon, 2);
            _os.Write(iQuality, 3);
            _os.Write(sName, 4);
            _os.Write(sStartTime, 5);
            _os.Write(iStartTime, 6);
            _os.Write(sDesc, 7);
            _os.Write(iHideBeforeFinish, 8);
            _os.Write(sProgressTarget, 9);
            _os.Write(iTargetType, 10);
            _os.Write(sCondition, 11);
            _os.Write(iItemID1, 12);
            _os.Write(iItemCount1, 13);
            _os.Write(iItemID2, 14);
            _os.Write(iItemCount2, 15);
            _os.Write(iSerialID, 16);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iID = (int) _is.Read(iID, 1, false);

            sResIcon = (string) _is.Read(sResIcon, 2, false);

            iQuality = (int) _is.Read(iQuality, 3, false);

            sName = (string) _is.Read(sName, 4, false);

            sStartTime = (string) _is.Read(sStartTime, 5, false);

            iStartTime = (int) _is.Read(iStartTime, 6, false);

            sDesc = (string) _is.Read(sDesc, 7, false);

            iHideBeforeFinish = (int) _is.Read(iHideBeforeFinish, 8, false);

            sProgressTarget = (string) _is.Read(sProgressTarget, 9, false);

            iTargetType = (int) _is.Read(iTargetType, 10, false);

            sCondition = (string) _is.Read(sCondition, 11, false);

            iItemID1 = (int) _is.Read(iItemID1, 12, false);

            iItemCount1 = (int) _is.Read(iItemCount1, 13, false);

            iItemID2 = (int) _is.Read(iItemID2, 14, false);

            iItemCount2 = (int) _is.Read(iItemCount2, 15, false);

            iSerialID = (int) _is.Read(iSerialID, 16, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iID, "iID");
            _ds.Display(sResIcon, "sResIcon");
            _ds.Display(iQuality, "iQuality");
            _ds.Display(sName, "sName");
            _ds.Display(sStartTime, "sStartTime");
            _ds.Display(iStartTime, "iStartTime");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iHideBeforeFinish, "iHideBeforeFinish");
            _ds.Display(sProgressTarget, "sProgressTarget");
            _ds.Display(iTargetType, "iTargetType");
            _ds.Display(sCondition, "sCondition");
            _ds.Display(iItemID1, "iItemID1");
            _ds.Display(iItemCount1, "iItemCount1");
            _ds.Display(iItemID2, "iItemID2");
            _ds.Display(iItemCount2, "iItemCount2");
            _ds.Display(iSerialID, "iSerialID");
        }

        public override void Clear()
        {
            iIndex = 0;
            iID = 0;
            sResIcon = "";
            iQuality = 0;
            sName = "";
            sStartTime = "";
            iStartTime = 0;
            sDesc = "";
            iHideBeforeFinish = 0;
            sProgressTarget = "";
            iTargetType = 0;
            sCondition = "";
            iItemID1 = 0;
            iItemCount1 = 0;
            iItemID2 = 0;
            iItemCount2 = 0;
            iSerialID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAchivementList_Client();
            copied.iIndex = this.iIndex;
            copied.iID = this.iID;
            copied.sResIcon = this.sResIcon;
            copied.iQuality = this.iQuality;
            copied.sName = this.sName;
            copied.sStartTime = this.sStartTime;
            copied.iStartTime = this.iStartTime;
            copied.sDesc = this.sDesc;
            copied.iHideBeforeFinish = this.iHideBeforeFinish;
            copied.sProgressTarget = this.sProgressTarget;
            copied.iTargetType = this.iTargetType;
            copied.sCondition = this.sCondition;
            copied.iItemID1 = this.iItemID1;
            copied.iItemCount1 = this.iItemCount1;
            copied.iItemID2 = this.iItemID2;
            copied.iItemCount2 = this.iItemCount2;
            copied.iSerialID = this.iSerialID;
            return copied;
        }
    }
}

