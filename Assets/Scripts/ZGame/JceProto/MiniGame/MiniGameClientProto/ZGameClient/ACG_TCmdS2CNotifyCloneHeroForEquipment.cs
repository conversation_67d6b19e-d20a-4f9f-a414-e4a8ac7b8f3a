// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifyCloneHeroForEquipment : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public TAC_WaitHero stClonedWaitHero {get; set;} 

        public ACG_Equipment stEquipment {get; set;} 

        public TAC_HeroEntity stHeroEntity {get; set;} 

        public System.Collections.Generic.List<ACG_Equipment> vecPlayerEquipment {get; set;} 

        bool _bIsPartnerClone = false;
        public bool bIsPartnerClone
        {
            get
            {
                 return _bIsPartnerClone;
            }
            set
            {
                _bIsPartnerClone = value; 
            }
        }

        bool _bNeedTips = true;
        public bool bNeedTips
        {
            get
            {
                 return _bNeedTips;
            }
            set
            {
                _bNeedTips = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(stClonedWaitHero, 2);
            _os.Write(stEquipment, 3);
            _os.Write(stHeroEntity, 4);
            _os.Write(vecPlayerEquipment, 5);
            _os.Write(bIsPartnerClone, 6);
            _os.Write(bNeedTips, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            stClonedWaitHero = (TAC_WaitHero) _is.Read(stClonedWaitHero, 2, false);

            stEquipment = (ACG_Equipment) _is.Read(stEquipment, 3, false);

            stHeroEntity = (TAC_HeroEntity) _is.Read(stHeroEntity, 4, false);

            vecPlayerEquipment = (System.Collections.Generic.List<ACG_Equipment>) _is.Read(vecPlayerEquipment, 5, false);

            bIsPartnerClone = (bool) _is.Read(bIsPartnerClone, 6, false);

            bNeedTips = (bool) _is.Read(bNeedTips, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(stClonedWaitHero, "stClonedWaitHero");
            _ds.Display(stEquipment, "stEquipment");
            _ds.Display(stHeroEntity, "stHeroEntity");
            _ds.Display(vecPlayerEquipment, "vecPlayerEquipment");
            _ds.Display(bIsPartnerClone, "bIsPartnerClone");
            _ds.Display(bNeedTips, "bNeedTips");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            stClonedWaitHero = null;
            stEquipment = null;
            stHeroEntity = null;
            vecPlayerEquipment = null;
            bIsPartnerClone = false;
            bNeedTips = true;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifyCloneHeroForEquipment();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.stClonedWaitHero = (TAC_WaitHero)JceUtil.DeepClone(this.stClonedWaitHero);
            copied.stEquipment = (ACG_Equipment)JceUtil.DeepClone(this.stEquipment);
            copied.stHeroEntity = (TAC_HeroEntity)JceUtil.DeepClone(this.stHeroEntity);
            copied.vecPlayerEquipment = (System.Collections.Generic.List<ACG_Equipment>)JceUtil.DeepClone(this.vecPlayerEquipment);
            copied.bIsPartnerClone = this.bIsPartnerClone;
            copied.bNeedTips = this.bNeedTips;
            return copied;
        }
    }
}

