// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualGetFriendVoteAssistListReq : Wup.Jce.JceStruct
    {
        public int dummy = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(dummy, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            dummy = (int) _is.Read(dummy, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(dummy, "dummy");
        }

        public override void Clear()
        {
            dummy = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualGetFriendVoteAssistListReq();
            copied.dummy = this.dummy;
            return copied;
        }
    }
}

