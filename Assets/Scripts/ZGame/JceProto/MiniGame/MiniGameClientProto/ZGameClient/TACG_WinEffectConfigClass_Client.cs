// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_WinEffectConfigClass_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sWinRule = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sWinRule, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sWinRule = (string) _is.Read(sWinRule, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sWinRule, "sWinRule");
        }

        public override void Clear()
        {
            iID = 0;
            sWinRule = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_WinEffectConfigClass_Client();
            copied.iID = this.iID;
            copied.sWinRule = this.sWinRule;
            return copied;
        }
    }
}

