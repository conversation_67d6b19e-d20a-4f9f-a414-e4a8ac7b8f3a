// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class CommonMallGlobalSetting : Wup.Jce.JceStruct
    {
        int _id = 0;
        public int id
        {
            get
            {
                 return _id;
            }
            set
            {
                _id = value; 
            }
        }

        int _openFlag = 0;
        public int openFlag
        {
            get
            {
                 return _openFlag;
            }
            set
            {
                _openFlag = value; 
            }
        }

        int _startTime = 0;
        public int startTime
        {
            get
            {
                 return _startTime;
            }
            set
            {
                _startTime = value; 
            }
        }

        int _endTime = 0;
        public int endTime
        {
            get
            {
                 return _endTime;
            }
            set
            {
                _endTime = value; 
            }
        }

        int _type = 0;
        public int type
        {
            get
            {
                 return _type;
            }
            set
            {
                _type = value; 
            }
        }

        string _background = "";
        public string background
        {
            get
            {
                 return _background;
            }
            set
            {
                _background = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(id, 0);
            _os.Write(openFlag, 1);
            _os.Write(startTime, 2);
            _os.Write(endTime, 3);
            _os.Write(type, 4);
            _os.Write(background, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            id = (int) _is.Read(id, 0, false);

            openFlag = (int) _is.Read(openFlag, 1, false);

            startTime = (int) _is.Read(startTime, 2, false);

            endTime = (int) _is.Read(endTime, 3, false);

            type = (int) _is.Read(type, 4, false);

            background = (string) _is.Read(background, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(id, "id");
            _ds.Display(openFlag, "openFlag");
            _ds.Display(startTime, "startTime");
            _ds.Display(endTime, "endTime");
            _ds.Display(type, "type");
            _ds.Display(background, "background");
        }

        public override void Clear()
        {
            id = 0;
            openFlag = 0;
            startTime = 0;
            endTime = 0;
            type = 0;
            background = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new CommonMallGlobalSetting();
            copied.id = this.id;
            copied.openFlag = this.openFlag;
            copied.startTime = this.startTime;
            copied.endTime = this.endTime;
            copied.type = this.type;
            copied.background = this.background;
            return copied;
        }
    }
}

