// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class HeroSkinGetSetConfResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public System.Collections.Generic.List<TCardSet_Server> heroSkinSets;

        public System.Collections.Generic.List<int> curSetIDs;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(heroSkinSets, 1);
            _os.Write(curSetIDs, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            heroSkinSets = (System.Collections.Generic.List<TCardSet_Server>) _is.Read(heroSkinSets, 1, false);

            curSetIDs = (System.Collections.Generic.List<int>) _is.Read(curSetIDs, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(heroSkinSets, "heroSkinSets");
            _ds.Display(curSetIDs, "curSetIDs");
        }

        public override void Clear()
        {
            err = 0;
            heroSkinSets = null;
            curSetIDs = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new HeroSkinGetSetConfResp();
            copied.err = this.err;
            copied.heroSkinSets = (System.Collections.Generic.List<TCardSet_Server>)JceUtil.DeepClone(this.heroSkinSets);
            copied.curSetIDs = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.curSetIDs);
            return copied;
        }
    }
}

