// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterWantGiftRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public TClientForbidInfo stForbidInfo;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(stForbidInfo, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            stForbidInfo = (TClientForbidInfo) _is.Read(stForbidInfo, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(stForbidInfo, "stForbidInfo");
        }

        public override void Clear()
        {
            iRet = 0;
            stForbidInfo = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterWantGiftRsp();
            copied.iRet = this.iRet;
            copied.stForbidInfo = (TClientForbidInfo)JceUtil.DeepClone(this.stForbidInfo);
            return copied;
        }
    }
}

