// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class THelloRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public int iTime = 0;

        public int iMsSecs = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(iTime, 1);
            _os.Write(iMsSecs, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            iTime = (int) _is.Read(iTime, 1, false);

            iMsSecs = (int) _is.Read(iMsSecs, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(iTime, "iTime");
            _ds.Display(iMsSecs, "iMsSecs");
        }

        public override void Clear()
        {
            iRet = 0;
            iTime = 0;
            iMsSecs = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new THelloRsp();
            copied.iRet = this.iRet;
            copied.iTime = this.iTime;
            copied.iMsSecs = this.iMsSecs;
            return copied;
        }
    }
}

