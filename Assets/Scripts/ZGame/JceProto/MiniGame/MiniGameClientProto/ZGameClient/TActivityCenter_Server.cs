// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TActivityCenter_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sJumpParam = "";

        public int iShowWeight = 0;

        public string sActivityName = "";

        public int iRelatedActivityType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sJumpParam, 2);
            _os.Write(iShowWeight, 3);
            _os.Write(sActivityName, 4);
            _os.Write(iRelatedActivityType, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sJumpParam = (string) _is.Read(sJumpParam, 2, false);

            iShowWeight = (int) _is.Read(iShowWeight, 3, false);

            sActivityName = (string) _is.Read(sActivityName, 4, false);

            iRelatedActivityType = (int) _is.Read(iRelatedActivityType, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sJumpParam, "sJumpParam");
            _ds.Display(iShowWeight, "iShowWeight");
            _ds.Display(sActivityName, "sActivityName");
            _ds.Display(iRelatedActivityType, "iRelatedActivityType");
        }

        public override void Clear()
        {
            iID = 0;
            sJumpParam = "";
            iShowWeight = 0;
            sActivityName = "";
            iRelatedActivityType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TActivityCenter_Server();
            copied.iID = this.iID;
            copied.sJumpParam = this.sJumpParam;
            copied.iShowWeight = this.iShowWeight;
            copied.sActivityName = this.sActivityName;
            copied.iRelatedActivityType = this.iRelatedActivityType;
            return copied;
        }
    }
}

