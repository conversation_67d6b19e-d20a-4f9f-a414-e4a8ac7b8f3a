//所在的Excel 【ACG_SpriteHelper.xlsm】
//**************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_SpriteHelper_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iTriggerID = 0;

        public int iConditionID = 0;

        public int iTipsID = 0;

        public int iSetID = 0;

        public int iLevel = 0;

        public int iPriority = 0;

        public string sBanMode = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iTriggerID, 1);
            _os.Write(iConditionID, 2);
            _os.Write(iTipsID, 3);
            _os.Write(iSetID, 4);
            _os.Write(iLevel, 5);
            _os.Write(iPriority, 6);
            _os.Write(sBanMode, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iTriggerID = (int) _is.Read(iTriggerID, 1, false);

            iConditionID = (int) _is.Read(iConditionID, 2, false);

            iTipsID = (int) _is.Read(iTipsID, 3, false);

            iSetID = (int) _is.Read(iSetID, 4, false);

            iLevel = (int) _is.Read(iLevel, 5, false);

            iPriority = (int) _is.Read(iPriority, 6, false);

            sBanMode = (string) _is.Read(sBanMode, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iTriggerID, "iTriggerID");
            _ds.Display(iConditionID, "iConditionID");
            _ds.Display(iTipsID, "iTipsID");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iPriority, "iPriority");
            _ds.Display(sBanMode, "sBanMode");
        }

        public override void Clear()
        {
            iID = 0;
            iTriggerID = 0;
            iConditionID = 0;
            iTipsID = 0;
            iSetID = 0;
            iLevel = 0;
            iPriority = 0;
            sBanMode = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_SpriteHelper_Client();
            copied.iID = this.iID;
            copied.iTriggerID = this.iTriggerID;
            copied.iConditionID = this.iConditionID;
            copied.iTipsID = this.iTipsID;
            copied.iSetID = this.iSetID;
            copied.iLevel = this.iLevel;
            copied.iPriority = this.iPriority;
            copied.sBanMode = this.sBanMode;
            return copied;
        }
    }
}

