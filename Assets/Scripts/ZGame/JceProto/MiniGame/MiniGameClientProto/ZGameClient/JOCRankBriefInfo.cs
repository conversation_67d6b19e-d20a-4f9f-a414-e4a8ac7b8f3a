// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCRankBriefInfo : Wup.Jce.JceStruct
    {
        public TJOCRank_Server rankConfig;

        public int score = 0;

        public int tier = 0;

        public int rankNumber = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(rankConfig, 0);
            _os.Write(score, 1);
            _os.Write(tier, 2);
            _os.Write(rankNumber, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            rankConfig = (TJOCRank_Server) _is.Read(rankConfig, 0, false);

            score = (int) _is.Read(score, 1, false);

            tier = (int) _is.Read(tier, 2, false);

            rankNumber = (int) _is.Read(rankNumber, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(rankConfig, "rankConfig");
            _ds.Display(score, "score");
            _ds.Display(tier, "tier");
            _ds.Display(rankNumber, "rankNumber");
        }

        public override void Clear()
        {
            rankConfig = null;
            score = 0;
            tier = 0;
            rankNumber = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCRankBriefInfo();
            copied.rankConfig = (TJOCRank_Server)JceUtil.DeepClone(this.rankConfig);
            copied.score = this.score;
            copied.tier = this.tier;
            copied.rankNumber = this.rankNumber;
            return copied;
        }
    }
}

