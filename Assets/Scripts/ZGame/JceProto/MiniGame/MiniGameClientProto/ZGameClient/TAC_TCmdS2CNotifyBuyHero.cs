// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifyBuyHero : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        public TAC_WaitHero stWaitHero {get; set;} 

        int _iMoney = 0;
        public int iMoney
        {
            get
            {
                 return _iMoney;
            }
            set
            {
                _iMoney = value; 
            }
        }

        int _iLife = -1;
        public int iLife
        {
            get
            {
                 return _iLife;
            }
            set
            {
                _iLife = value; 
            }
        }

        public System.Collections.Generic.List<TAC_WaitHero> vecSortedWaitHero {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(stWaitHero, 2);
            _os.Write(iMoney, 3);
            _os.Write(iLife, 4);
            _os.Write(vecSortedWaitHero, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            stWaitHero = (TAC_WaitHero) _is.Read(stWaitHero, 2, false);

            iMoney = (int) _is.Read(iMoney, 3, false);

            iLife = (int) _is.Read(iLife, 4, false);

            vecSortedWaitHero = (System.Collections.Generic.List<TAC_WaitHero>) _is.Read(vecSortedWaitHero, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(stWaitHero, "stWaitHero");
            _ds.Display(iMoney, "iMoney");
            _ds.Display(iLife, "iLife");
            _ds.Display(vecSortedWaitHero, "vecSortedWaitHero");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            stWaitHero = null;
            iMoney = 0;
            iLife = 0;
            vecSortedWaitHero = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifyBuyHero();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.stWaitHero = (TAC_WaitHero)JceUtil.DeepClone(this.stWaitHero);
            copied.iMoney = this.iMoney;
            copied.iLife = this.iLife;
            copied.vecSortedWaitHero = (System.Collections.Generic.List<TAC_WaitHero>)JceUtil.DeepClone(this.vecSortedWaitHero);
            return copied;
        }
    }
}

