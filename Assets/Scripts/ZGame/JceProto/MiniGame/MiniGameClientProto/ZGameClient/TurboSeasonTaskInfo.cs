// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TurboSeasonTaskInfo : Wup.Jce.JceStruct
    {
        public int iTaskID = 0;

        public TKFrame.TKDictionary<int, int> mapProgress;

        public int iStatus = 0;

        public System.Collections.Generic.List<TItemInfo> vecItems;

        public string sTaskDes = "";

        public int iRankID = 0;

        public string sTaskTitle = "";

        public string sProgressTarget = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iTaskID, 0);
            _os.Write(mapProgress, 1);
            _os.Write(iStatus, 2);
            _os.Write(vecItems, 3);
            _os.Write(sTaskDes, 4);
            _os.Write(iRankID, 5);
            _os.Write(sTaskTitle, 6);
            _os.Write(sProgressTarget, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iTaskID = (int) _is.Read(iTaskID, 0, false);

            mapProgress = (TKFrame.TKDictionary<int, int>) _is.Read(mapProgress, 1, false);

            iStatus = (int) _is.Read(iStatus, 2, false);

            vecItems = (System.Collections.Generic.List<TItemInfo>) _is.Read(vecItems, 3, false);

            sTaskDes = (string) _is.Read(sTaskDes, 4, false);

            iRankID = (int) _is.Read(iRankID, 5, false);

            sTaskTitle = (string) _is.Read(sTaskTitle, 6, false);

            sProgressTarget = (string) _is.Read(sProgressTarget, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iTaskID, "iTaskID");
            _ds.Display(mapProgress, "mapProgress");
            _ds.Display(iStatus, "iStatus");
            _ds.Display(vecItems, "vecItems");
            _ds.Display(sTaskDes, "sTaskDes");
            _ds.Display(iRankID, "iRankID");
            _ds.Display(sTaskTitle, "sTaskTitle");
            _ds.Display(sProgressTarget, "sProgressTarget");
        }

        public override void Clear()
        {
            iTaskID = 0;
            mapProgress = null;
            iStatus = 0;
            vecItems = null;
            sTaskDes = "";
            iRankID = 0;
            sTaskTitle = "";
            sProgressTarget = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TurboSeasonTaskInfo();
            copied.iTaskID = this.iTaskID;
            copied.mapProgress = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.mapProgress);
            copied.iStatus = this.iStatus;
            copied.vecItems = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.vecItems);
            copied.sTaskDes = this.sTaskDes;
            copied.iRankID = this.iRankID;
            copied.sTaskTitle = this.sTaskTitle;
            copied.sProgressTarget = this.sProgressTarget;
            return copied;
        }
    }
}

