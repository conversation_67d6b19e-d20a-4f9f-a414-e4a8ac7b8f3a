//所在的Excel 【TAC_AITree.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_AIDropEquip_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iTurnCount = 0;

        public int iDifficult1 = 0;

        public int iDifficult2 = 0;

        public int iDifficult3 = 0;

        public int iDifficult4 = 0;

        public int iDifficultPct1 = 0;

        public int iGuarantee1 = 0;

        public int iDifficultPct2 = 0;

        public int iGuarantee2 = 0;

        public int iDifficultPct3 = 0;

        public int iGuarantee3 = 0;

        public int iDifficultPct4 = 0;

        public int iGuarantee4 = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iTurnCount, 1);
            _os.Write(iDifficult1, 2);
            _os.Write(iDifficult2, 3);
            _os.Write(iDifficult3, 4);
            _os.Write(iDifficult4, 5);
            _os.Write(iDifficultPct1, 6);
            _os.Write(iGuarantee1, 7);
            _os.Write(iDifficultPct2, 8);
            _os.Write(iGuarantee2, 9);
            _os.Write(iDifficultPct3, 10);
            _os.Write(iGuarantee3, 11);
            _os.Write(iDifficultPct4, 12);
            _os.Write(iGuarantee4, 13);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iTurnCount = (int) _is.Read(iTurnCount, 1, false);

            iDifficult1 = (int) _is.Read(iDifficult1, 2, false);

            iDifficult2 = (int) _is.Read(iDifficult2, 3, false);

            iDifficult3 = (int) _is.Read(iDifficult3, 4, false);

            iDifficult4 = (int) _is.Read(iDifficult4, 5, false);

            iDifficultPct1 = (int) _is.Read(iDifficultPct1, 6, false);

            iGuarantee1 = (int) _is.Read(iGuarantee1, 7, false);

            iDifficultPct2 = (int) _is.Read(iDifficultPct2, 8, false);

            iGuarantee2 = (int) _is.Read(iGuarantee2, 9, false);

            iDifficultPct3 = (int) _is.Read(iDifficultPct3, 10, false);

            iGuarantee3 = (int) _is.Read(iGuarantee3, 11, false);

            iDifficultPct4 = (int) _is.Read(iDifficultPct4, 12, false);

            iGuarantee4 = (int) _is.Read(iGuarantee4, 13, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iTurnCount, "iTurnCount");
            _ds.Display(iDifficult1, "iDifficult1");
            _ds.Display(iDifficult2, "iDifficult2");
            _ds.Display(iDifficult3, "iDifficult3");
            _ds.Display(iDifficult4, "iDifficult4");
            _ds.Display(iDifficultPct1, "iDifficultPct1");
            _ds.Display(iGuarantee1, "iGuarantee1");
            _ds.Display(iDifficultPct2, "iDifficultPct2");
            _ds.Display(iGuarantee2, "iGuarantee2");
            _ds.Display(iDifficultPct3, "iDifficultPct3");
            _ds.Display(iGuarantee3, "iGuarantee3");
            _ds.Display(iDifficultPct4, "iDifficultPct4");
            _ds.Display(iGuarantee4, "iGuarantee4");
        }

        public override void Clear()
        {
            iID = 0;
            iTurnCount = 0;
            iDifficult1 = 0;
            iDifficult2 = 0;
            iDifficult3 = 0;
            iDifficult4 = 0;
            iDifficultPct1 = 0;
            iGuarantee1 = 0;
            iDifficultPct2 = 0;
            iGuarantee2 = 0;
            iDifficultPct3 = 0;
            iGuarantee3 = 0;
            iDifficultPct4 = 0;
            iGuarantee4 = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_AIDropEquip_Client();
            copied.iID = this.iID;
            copied.iTurnCount = this.iTurnCount;
            copied.iDifficult1 = this.iDifficult1;
            copied.iDifficult2 = this.iDifficult2;
            copied.iDifficult3 = this.iDifficult3;
            copied.iDifficult4 = this.iDifficult4;
            copied.iDifficultPct1 = this.iDifficultPct1;
            copied.iGuarantee1 = this.iGuarantee1;
            copied.iDifficultPct2 = this.iDifficultPct2;
            copied.iGuarantee2 = this.iGuarantee2;
            copied.iDifficultPct3 = this.iDifficultPct3;
            copied.iGuarantee3 = this.iGuarantee3;
            copied.iDifficultPct4 = this.iDifficultPct4;
            copied.iGuarantee4 = this.iGuarantee4;
            return copied;
        }
    }
}

