// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameMail
{

    public sealed class TAC_TSendUserMail : Wup.Jce.JceStruct
    {
        public int eMailTag {get; set;} 

        public int eDelType {get; set;} 

        public int eMailScene {get; set;} 

        public TAC_TSenderInfo stSenderInfo {get; set;} 

        public TAC_TMailDetails stMailDetails {get; set;} 

        public TAC_TItemGroup items {get; set;} 

        int _iSource = 0;
        public int iSource
        {
            get
            {
                 return _iSource;
            }
            set
            {
                _iSource = value; 
            }
        }

        int _iAwardReason = 0;
        public int iAwardReason
        {
            get
            {
                 return _iAwardReason;
            }
            set
            {
                _iAwardReason = value; 
            }
        }

        int _iNotify = 1;
        public int iNotify
        {
            get
            {
                 return _iNotify;
            }
            set
            {
                _iNotify = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(eMailTag, 0);
            _os.Write(eDelType, 1);
            _os.Write(eMailScene, 2);
            _os.Write(stSenderInfo, 3);
            _os.Write(stMailDetails, 4);
            _os.Write(items, 5);
            _os.Write(iSource, 6);
            _os.Write(iAwardReason, 7);
            _os.Write(iNotify, 8);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            eMailTag = (int) _is.Read(eMailTag, 0, false);

            eDelType = (int) _is.Read(eDelType, 1, false);

            eMailScene = (int) _is.Read(eMailScene, 2, false);

            stSenderInfo = (TAC_TSenderInfo) _is.Read(stSenderInfo, 3, false);

            stMailDetails = (TAC_TMailDetails) _is.Read(stMailDetails, 4, false);

            items = (TAC_TItemGroup) _is.Read(items, 5, false);

            iSource = (int) _is.Read(iSource, 6, false);

            iAwardReason = (int) _is.Read(iAwardReason, 7, false);

            iNotify = (int) _is.Read(iNotify, 8, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(eMailTag, "eMailTag");
            _ds.Display(eDelType, "eDelType");
            _ds.Display(eMailScene, "eMailScene");
            _ds.Display(stSenderInfo, "stSenderInfo");
            _ds.Display(stMailDetails, "stMailDetails");
            _ds.Display(items, "items");
            _ds.Display(iSource, "iSource");
            _ds.Display(iAwardReason, "iAwardReason");
            _ds.Display(iNotify, "iNotify");
        }

    }
}

