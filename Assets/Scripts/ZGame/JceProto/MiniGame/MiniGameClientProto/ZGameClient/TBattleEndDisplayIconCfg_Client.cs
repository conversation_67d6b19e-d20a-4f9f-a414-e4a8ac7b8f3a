//所在的Excel 【GameModule.xlsm】
//********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TBattleEndDisplayIconCfg_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iSceneType = 0;

        public string sEndDisplayIcon = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iSceneType, 1);
            _os.Write(sEndDisplayIcon, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iSceneType = (int) _is.Read(iSceneType, 1, false);

            sEndDisplayIcon = (string) _is.Read(sEndDisplayIcon, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(sEndDisplayIcon, "sEndDisplayIcon");
        }

        public override void Clear()
        {
            iID = 0;
            iSceneType = 0;
            sEndDisplayIcon = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TBattleEndDisplayIconCfg_Client();
            copied.iID = this.iID;
            copied.iSceneType = this.iSceneType;
            copied.sEndDisplayIcon = this.sEndDisplayIcon;
            return copied;
        }
    }
}

