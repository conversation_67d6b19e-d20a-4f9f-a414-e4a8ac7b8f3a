// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TMallBuyReq : Wup.Jce.JceStruct
    {
        public int iGoodsId = 0;

        public int iBuyCount = 0;

        public int iCurrencyId = 0;

        public string sAccessToken = "";

        public string sPayToken = "";

        public int iExtraTicketNum = 0;

        public TMidasTokenInfo stMidasToken;

        public int iCurrencyNum = 0;

        public int iBuySource = 0;

        public string sExtraInfo = "";

        public int clientCalcDeductItemNum = 0;

        public System.Collections.Generic.List<TItemInfo> useCoupons;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iGoodsId, 0);
            _os.Write(iBuyCount, 1);
            _os.Write(iCurrencyId, 2);
            _os.Write(sAccessToken, 3);
            _os.Write(sPayToken, 4);
            _os.Write(iExtraTicketNum, 5);
            _os.Write(stMidasToken, 6);
            _os.Write(iCurrencyNum, 7);
            _os.Write(iBuySource, 8);
            _os.Write(sExtraInfo, 9);
            _os.Write(clientCalcDeductItemNum, 12);
            _os.Write(useCoupons, 13);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iGoodsId = (int) _is.Read(iGoodsId, 0, false);

            iBuyCount = (int) _is.Read(iBuyCount, 1, false);

            iCurrencyId = (int) _is.Read(iCurrencyId, 2, false);

            sAccessToken = (string) _is.Read(sAccessToken, 3, false);

            sPayToken = (string) _is.Read(sPayToken, 4, false);

            iExtraTicketNum = (int) _is.Read(iExtraTicketNum, 5, false);

            stMidasToken = (TMidasTokenInfo) _is.Read(stMidasToken, 6, false);

            iCurrencyNum = (int) _is.Read(iCurrencyNum, 7, false);

            iBuySource = (int) _is.Read(iBuySource, 8, false);

            sExtraInfo = (string) _is.Read(sExtraInfo, 9, false);

            clientCalcDeductItemNum = (int) _is.Read(clientCalcDeductItemNum, 12, false);

            useCoupons = (System.Collections.Generic.List<TItemInfo>) _is.Read(useCoupons, 13, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iGoodsId, "iGoodsId");
            _ds.Display(iBuyCount, "iBuyCount");
            _ds.Display(iCurrencyId, "iCurrencyId");
            _ds.Display(sAccessToken, "sAccessToken");
            _ds.Display(sPayToken, "sPayToken");
            _ds.Display(iExtraTicketNum, "iExtraTicketNum");
            _ds.Display(stMidasToken, "stMidasToken");
            _ds.Display(iCurrencyNum, "iCurrencyNum");
            _ds.Display(iBuySource, "iBuySource");
            _ds.Display(sExtraInfo, "sExtraInfo");
            _ds.Display(clientCalcDeductItemNum, "clientCalcDeductItemNum");
            _ds.Display(useCoupons, "useCoupons");
        }

        public override void Clear()
        {
            iGoodsId = 0;
            iBuyCount = 0;
            iCurrencyId = 0;
            sAccessToken = "";
            sPayToken = "";
            iExtraTicketNum = 0;
            stMidasToken = null;
            iCurrencyNum = 0;
            iBuySource = 0;
            sExtraInfo = "";
            clientCalcDeductItemNum = 0;
            useCoupons = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TMallBuyReq();
            copied.iGoodsId = this.iGoodsId;
            copied.iBuyCount = this.iBuyCount;
            copied.iCurrencyId = this.iCurrencyId;
            copied.sAccessToken = this.sAccessToken;
            copied.sPayToken = this.sPayToken;
            copied.iExtraTicketNum = this.iExtraTicketNum;
            copied.stMidasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.stMidasToken);
            copied.iCurrencyNum = this.iCurrencyNum;
            copied.iBuySource = this.iBuySource;
            copied.sExtraInfo = this.sExtraInfo;
            copied.clientCalcDeductItemNum = this.clientCalcDeductItemNum;
            copied.useCoupons = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.useCoupons);
            return copied;
        }
    }
}

