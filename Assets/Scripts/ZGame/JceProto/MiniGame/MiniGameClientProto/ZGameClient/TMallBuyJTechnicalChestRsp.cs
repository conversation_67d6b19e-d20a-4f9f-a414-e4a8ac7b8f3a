// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMallBuyJTechnicalChestRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<TItemInfo> vecChestItems;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecChestItems, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecChestItems = (System.Collections.Generic.List<TItemInfo>) _is.Read(vecChestItems, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecChestItems, "vecChestItems");
        }

        public override void Clear()
        {
            iRet = 0;
            vecChestItems = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMallBuyJTechnicalChestRsp();
            copied.iRet = this.iRet;
            copied.vecChestItems = (System.Collections.Generic.List<TItemInfo>)JceUtil.DeepClone(this.vecChestItems);
            return copied;
        }
    }
}

