// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TBPMallItemProto : Wup.Jce.JceStruct
    {
        int _iGoodID = 0;
        public int iGoodID
        {
            get
            {
                 return _iGoodID;
            }
            set
            {
                _iGoodID = value; 
            }
        }

        int _iTotalBuyCount = 0;
        public int iTotalBuyCount
        {
            get
            {
                 return _iTotalBuyCount;
            }
            set
            {
                _iTotalBuyCount = value; 
            }
        }

        int _iHaveItemNum = 0;
        public int iHaveItemNum
        {
            get
            {
                 return _iHaveItemNum;
            }
            set
            {
                _iHaveItemNum = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iGoodID, 0);
            _os.Write(iTotalBuyCount, 1);
            _os.Write(iHaveItemNum, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iGoodID = (int) _is.Read(iGoodID, 0, false);

            iTotalBuyCount = (int) _is.Read(iTotalBuyCount, 1, false);

            iHaveItemNum = (int) _is.Read(iHaveItemNum, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iGoodID, "iGoodID");
            _ds.Display(iTotalBuyCount, "iTotalBuyCount");
            _ds.Display(iHaveItemNum, "iHaveItemNum");
        }

        public override void Clear()
        {
            iGoodID = 0;
            iTotalBuyCount = 0;
            iHaveItemNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TBPMallItemProto();
            copied.iGoodID = this.iGoodID;
            copied.iTotalBuyCount = this.iTotalBuyCount;
            copied.iHaveItemNum = this.iHaveItemNum;
            return copied;
        }
    }
}

