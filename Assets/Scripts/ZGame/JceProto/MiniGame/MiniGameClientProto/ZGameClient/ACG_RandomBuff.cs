// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_RandomBuff : Wup.Jce.JceStruct
    {
        int _buffGroupID = 0;
        public int buffGroupID
        {
            get
            {
                 return _buffGroupID;
            }
            set
            {
                _buffGroupID = value; 
            }
        }

        public System.Collections.Generic.List<int> weightList {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(buffGroupID, 0);
            _os.Write(weightList, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            buffGroupID = (int) _is.Read(buffGroupID, 0, false);

            weightList = (System.Collections.Generic.List<int>) _is.Read(weightList, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(buffGroupID, "buffGroupID");
            _ds.Display(weightList, "weightList");
        }

        public override void Clear()
        {
            buffGroupID = 0;
            weightList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_RandomBuff();
            copied.buffGroupID = this.buffGroupID;
            copied.weightList = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.weightList);
            return copied;
        }
    }
}

