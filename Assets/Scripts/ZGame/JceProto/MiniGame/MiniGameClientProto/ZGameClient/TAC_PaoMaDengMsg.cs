// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_PaoMaDengMsg : Wup.Jce.JceStruct
    {
        int _id = 0;
        public int id
        {
            get
            {
                 return _id;
            }
            set
            {
                _id = value; 
            }
        }

        int _type = 0;
        public int type
        {
            get
            {
                 return _type;
            }
            set
            {
                _type = value; 
            }
        }

        int _times = 0;
        public int times
        {
            get
            {
                 return _times;
            }
            set
            {
                _times = value; 
            }
        }

        int _showDuration = 0;
        public int showDuration
        {
            get
            {
                 return _showDuration;
            }
            set
            {
                _showDuration = value; 
            }
        }

        string _content = "";
        public string content
        {
            get
            {
                 return _content;
            }
            set
            {
                _content = value; 
            }
        }

        int _weight = 0;
        public int weight
        {
            get
            {
                 return _weight;
            }
            set
            {
                _weight = value; 
            }
        }

        int _speed = 0;
        public int speed
        {
            get
            {
                 return _speed;
            }
            set
            {
                _speed = value; 
            }
        }

        int _repeatTimes = 0;
        public int repeatTimes
        {
            get
            {
                 return _repeatTimes;
            }
            set
            {
                _repeatTimes = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(id, 0);
            _os.Write(type, 1);
            _os.Write(times, 2);
            _os.Write(showDuration, 3);
            _os.Write(content, 4);
            _os.Write(weight, 5);
            _os.Write(speed, 6);
            _os.Write(repeatTimes, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            id = (int) _is.Read(id, 0, false);

            type = (int) _is.Read(type, 1, false);

            times = (int) _is.Read(times, 2, false);

            showDuration = (int) _is.Read(showDuration, 3, false);

            content = (string) _is.Read(content, 4, false);

            weight = (int) _is.Read(weight, 5, false);

            speed = (int) _is.Read(speed, 6, false);

            repeatTimes = (int) _is.Read(repeatTimes, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(id, "id");
            _ds.Display(type, "type");
            _ds.Display(times, "times");
            _ds.Display(showDuration, "showDuration");
            _ds.Display(content, "content");
            _ds.Display(weight, "weight");
            _ds.Display(speed, "speed");
            _ds.Display(repeatTimes, "repeatTimes");
        }

        public override void Clear()
        {
            id = 0;
            type = 0;
            times = 0;
            showDuration = 0;
            content = "";
            weight = 0;
            speed = 0;
            repeatTimes = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_PaoMaDengMsg();
            copied.id = this.id;
            copied.type = this.type;
            copied.times = this.times;
            copied.showDuration = this.showDuration;
            copied.content = this.content;
            copied.weight = this.weight;
            copied.speed = this.speed;
            copied.repeatTimes = this.repeatTimes;
            return copied;
        }
    }
}

