// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_TCmdS2CNotifySetRefreshStatus : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        int _i8ChairID = 0;
        public int i8ChairID
        {
            get
            {
                 return _i8ChairID;
            }
            set
            {
                _i8ChairID = value; 
            }
        }

        int _iRefreshStatus = 0;
        public int iRefreshStatus
        {
            get
            {
                 return _iRefreshStatus;
            }
            set
            {
                _iRefreshStatus = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(i8ChairID, 1);
            _os.Write(iRefreshStatus, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            i8ChairID = (int) _is.Read(i8ChairID, 1, false);

            iRefreshStatus = (int) _is.Read(iRefreshStatus, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(i8ChairID, "i8ChairID");
            _ds.Display(iRefreshStatus, "iRefreshStatus");
        }

        public override void Clear()
        {
            iRet = 0;
            i8ChairID = 0;
            iRefreshStatus = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_TCmdS2CNotifySetRefreshStatus();
            copied.iRet = this.iRet;
            copied.i8ChairID = this.i8ChairID;
            copied.iRefreshStatus = this.iRefreshStatus;
            return copied;
        }
    }
}

