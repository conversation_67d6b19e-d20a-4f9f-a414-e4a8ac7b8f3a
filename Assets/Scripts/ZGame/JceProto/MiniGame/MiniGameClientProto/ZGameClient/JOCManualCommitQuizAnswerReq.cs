// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualCommitQuizAnswerReq : Wup.Jce.JceStruct
    {
        public int questionID = 0;

        public System.Collections.Generic.List<int> answerIDs;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(questionID, 0);
            _os.Write(answerIDs, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            questionID = (int) _is.Read(questionID, 0, false);

            answerIDs = (System.Collections.Generic.List<int>) _is.Read(answerIDs, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(questionID, "questionID");
            _ds.Display(answerIDs, "answerIDs");
        }

        public override void Clear()
        {
            questionID = 0;
            answerIDs = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualCommitQuizAnswerReq();
            copied.questionID = this.questionID;
            copied.answerIDs = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.answerIDs);
            return copied;
        }
    }
}

