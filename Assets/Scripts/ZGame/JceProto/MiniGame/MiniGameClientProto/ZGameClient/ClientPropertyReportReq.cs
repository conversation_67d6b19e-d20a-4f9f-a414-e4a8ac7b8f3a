// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClientPropertyReportReq : Wup.Jce.JceStruct
    {
        public string metricName = "";

        public System.Collections.Generic.List<int> types;

        public long value = 0;

        public string customParam = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(metricName, 0);
            _os.Write(types, 1);
            _os.Write(value, 2);
            _os.Write(customParam, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            metricName = (string) _is.Read(metricName, 0, false);

            types = (System.Collections.Generic.List<int>) _is.Read(types, 1, false);

            value = (long) _is.Read(value, 2, false);

            customParam = (string) _is.Read(customParam, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(metricName, "metricName");
            _ds.Display(types, "types");
            _ds.Display(value, "value");
            _ds.Display(customParam, "customParam");
        }

        public override void Clear()
        {
            metricName = "";
            types = null;
            value = 0;
            customParam = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClientPropertyReportReq();
            copied.metricName = this.metricName;
            copied.types = (System.Collections.Generic.List<int>)JceUtil.DeepClone(this.types);
            copied.value = this.value;
            copied.customParam = this.customParam;
            return copied;
        }
    }
}

