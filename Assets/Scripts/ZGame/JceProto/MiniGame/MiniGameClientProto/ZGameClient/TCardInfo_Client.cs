// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TCardInfo_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iCost = 0;

        public string sAniRes = "";

        public int iSetID = 0;

        public int iMaxStar = 0;

        public string sUpgradeOneStarRewards = "";

        public string sUpgradeTwoStarRewards = "";

        public string sUpgradeThreeStarRewards = "";

        public string sName = "";

        public string sBroadcastImageName = "";

        public string sSkillEffectVideoName = "";

        public string sRewardJumpParams = "";

        public string sSeasonName = "";

        public string sHeroGroupIDs = "";

        public int iShowRewardPriority = 0;

        public int iShowRewardTimes = 0;

        public int iType = 0;

        public int iSyncFrom = 0;

        public int iPrevCost = 0;

        public int iSendScrapMailId = 0;

        public string sShareBGFileName = "";

        public string sSkillEffectImageName = "";

        public string sScrapPerCard = "";

        public int iModelHide = 0;

        public string sSkillEffectDrawingName = "";

        public int iCardScoreReportType = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iCost, 4);
            _os.Write(sAniRes, 6);
            _os.Write(iSetID, 16);
            _os.Write(iMaxStar, 17);
            _os.Write(sUpgradeOneStarRewards, 19);
            _os.Write(sUpgradeTwoStarRewards, 20);
            _os.Write(sUpgradeThreeStarRewards, 21);
            _os.Write(sName, 25);
            _os.Write(sBroadcastImageName, 31);
            _os.Write(sSkillEffectVideoName, 32);
            _os.Write(sRewardJumpParams, 35);
            _os.Write(sSeasonName, 37);
            _os.Write(sHeroGroupIDs, 39);
            _os.Write(iShowRewardPriority, 40);
            _os.Write(iShowRewardTimes, 41);
            _os.Write(iType, 42);
            _os.Write(iSyncFrom, 43);
            _os.Write(iPrevCost, 45);
            _os.Write(iSendScrapMailId, 46);
            _os.Write(sShareBGFileName, 49);
            _os.Write(sSkillEffectImageName, 50);
            _os.Write(sScrapPerCard, 51);
            _os.Write(iModelHide, 52);
            _os.Write(sSkillEffectDrawingName, 53);
            _os.Write(iCardScoreReportType, 54);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iCost = (int) _is.Read(iCost, 4, false);

            sAniRes = (string) _is.Read(sAniRes, 6, false);

            iSetID = (int) _is.Read(iSetID, 16, false);

            iMaxStar = (int) _is.Read(iMaxStar, 17, false);

            sUpgradeOneStarRewards = (string) _is.Read(sUpgradeOneStarRewards, 19, false);

            sUpgradeTwoStarRewards = (string) _is.Read(sUpgradeTwoStarRewards, 20, false);

            sUpgradeThreeStarRewards = (string) _is.Read(sUpgradeThreeStarRewards, 21, false);

            sName = (string) _is.Read(sName, 25, false);

            sBroadcastImageName = (string) _is.Read(sBroadcastImageName, 31, false);

            sSkillEffectVideoName = (string) _is.Read(sSkillEffectVideoName, 32, false);

            sRewardJumpParams = (string) _is.Read(sRewardJumpParams, 35, false);

            sSeasonName = (string) _is.Read(sSeasonName, 37, false);

            sHeroGroupIDs = (string) _is.Read(sHeroGroupIDs, 39, false);

            iShowRewardPriority = (int) _is.Read(iShowRewardPriority, 40, false);

            iShowRewardTimes = (int) _is.Read(iShowRewardTimes, 41, false);

            iType = (int) _is.Read(iType, 42, false);

            iSyncFrom = (int) _is.Read(iSyncFrom, 43, false);

            iPrevCost = (int) _is.Read(iPrevCost, 45, false);

            iSendScrapMailId = (int) _is.Read(iSendScrapMailId, 46, false);

            sShareBGFileName = (string) _is.Read(sShareBGFileName, 49, false);

            sSkillEffectImageName = (string) _is.Read(sSkillEffectImageName, 50, false);

            sScrapPerCard = (string) _is.Read(sScrapPerCard, 51, false);

            iModelHide = (int) _is.Read(iModelHide, 52, false);

            sSkillEffectDrawingName = (string) _is.Read(sSkillEffectDrawingName, 53, false);

            iCardScoreReportType = (int) _is.Read(iCardScoreReportType, 54, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iCost, "iCost");
            _ds.Display(sAniRes, "sAniRes");
            _ds.Display(iSetID, "iSetID");
            _ds.Display(iMaxStar, "iMaxStar");
            _ds.Display(sUpgradeOneStarRewards, "sUpgradeOneStarRewards");
            _ds.Display(sUpgradeTwoStarRewards, "sUpgradeTwoStarRewards");
            _ds.Display(sUpgradeThreeStarRewards, "sUpgradeThreeStarRewards");
            _ds.Display(sName, "sName");
            _ds.Display(sBroadcastImageName, "sBroadcastImageName");
            _ds.Display(sSkillEffectVideoName, "sSkillEffectVideoName");
            _ds.Display(sRewardJumpParams, "sRewardJumpParams");
            _ds.Display(sSeasonName, "sSeasonName");
            _ds.Display(sHeroGroupIDs, "sHeroGroupIDs");
            _ds.Display(iShowRewardPriority, "iShowRewardPriority");
            _ds.Display(iShowRewardTimes, "iShowRewardTimes");
            _ds.Display(iType, "iType");
            _ds.Display(iSyncFrom, "iSyncFrom");
            _ds.Display(iPrevCost, "iPrevCost");
            _ds.Display(iSendScrapMailId, "iSendScrapMailId");
            _ds.Display(sShareBGFileName, "sShareBGFileName");
            _ds.Display(sSkillEffectImageName, "sSkillEffectImageName");
            _ds.Display(sScrapPerCard, "sScrapPerCard");
            _ds.Display(iModelHide, "iModelHide");
            _ds.Display(sSkillEffectDrawingName, "sSkillEffectDrawingName");
            _ds.Display(iCardScoreReportType, "iCardScoreReportType");
        }

        public override void Clear()
        {
            iID = 0;
            iCost = 0;
            sAniRes = "";
            iSetID = 0;
            iMaxStar = 0;
            sUpgradeOneStarRewards = "";
            sUpgradeTwoStarRewards = "";
            sUpgradeThreeStarRewards = "";
            sName = "";
            sBroadcastImageName = "";
            sSkillEffectVideoName = "";
            sRewardJumpParams = "";
            sSeasonName = "";
            sHeroGroupIDs = "";
            iShowRewardPriority = 0;
            iShowRewardTimes = 0;
            iType = 0;
            iSyncFrom = 0;
            iPrevCost = 0;
            iSendScrapMailId = 0;
            sShareBGFileName = "";
            sSkillEffectImageName = "";
            sScrapPerCard = "";
            iModelHide = 0;
            sSkillEffectDrawingName = "";
            iCardScoreReportType = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TCardInfo_Client();
            copied.iID = this.iID;
            copied.iCost = this.iCost;
            copied.sAniRes = this.sAniRes;
            copied.iSetID = this.iSetID;
            copied.iMaxStar = this.iMaxStar;
            copied.sUpgradeOneStarRewards = this.sUpgradeOneStarRewards;
            copied.sUpgradeTwoStarRewards = this.sUpgradeTwoStarRewards;
            copied.sUpgradeThreeStarRewards = this.sUpgradeThreeStarRewards;
            copied.sName = this.sName;
            copied.sBroadcastImageName = this.sBroadcastImageName;
            copied.sSkillEffectVideoName = this.sSkillEffectVideoName;
            copied.sRewardJumpParams = this.sRewardJumpParams;
            copied.sSeasonName = this.sSeasonName;
            copied.sHeroGroupIDs = this.sHeroGroupIDs;
            copied.iShowRewardPriority = this.iShowRewardPriority;
            copied.iShowRewardTimes = this.iShowRewardTimes;
            copied.iType = this.iType;
            copied.iSyncFrom = this.iSyncFrom;
            copied.iPrevCost = this.iPrevCost;
            copied.iSendScrapMailId = this.iSendScrapMailId;
            copied.sShareBGFileName = this.sShareBGFileName;
            copied.sSkillEffectImageName = this.sSkillEffectImageName;
            copied.sScrapPerCard = this.sScrapPerCard;
            copied.iModelHide = this.iModelHide;
            copied.sSkillEffectDrawingName = this.sSkillEffectDrawingName;
            copied.iCardScoreReportType = this.iCardScoreReportType;
            return copied;
        }
    }
}

