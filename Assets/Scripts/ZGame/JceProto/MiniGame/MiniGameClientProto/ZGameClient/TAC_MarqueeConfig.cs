// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_MarqueeConfig : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iStartTime = 0;

        public int iOverTime = 0;

        public int iInterval = 0;

        public string sContent = "";

        public int iConfType = 0;

        public int iShowDuration = 0;

        public int iSourceType = 0;

        public int iSpeed = 0;

        public int iRepeatTimes = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iStartTime, 1);
            _os.Write(iOverTime, 2);
            _os.Write(iInterval, 3);
            _os.Write(sContent, 4);
            _os.Write(iConfType, 5);
            _os.Write(iShowDuration, 6);
            _os.Write(iSourceType, 7);
            _os.Write(iSpeed, 8);
            _os.Write(iRepeatTimes, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iStartTime = (int) _is.Read(iStartTime, 1, false);

            iOverTime = (int) _is.Read(iOverTime, 2, false);

            iInterval = (int) _is.Read(iInterval, 3, false);

            sContent = (string) _is.Read(sContent, 4, false);

            iConfType = (int) _is.Read(iConfType, 5, false);

            iShowDuration = (int) _is.Read(iShowDuration, 6, false);

            iSourceType = (int) _is.Read(iSourceType, 7, false);

            iSpeed = (int) _is.Read(iSpeed, 8, false);

            iRepeatTimes = (int) _is.Read(iRepeatTimes, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iStartTime, "iStartTime");
            _ds.Display(iOverTime, "iOverTime");
            _ds.Display(iInterval, "iInterval");
            _ds.Display(sContent, "sContent");
            _ds.Display(iConfType, "iConfType");
            _ds.Display(iShowDuration, "iShowDuration");
            _ds.Display(iSourceType, "iSourceType");
            _ds.Display(iSpeed, "iSpeed");
            _ds.Display(iRepeatTimes, "iRepeatTimes");
        }

        public override void Clear()
        {
            iID = 0;
            iStartTime = 0;
            iOverTime = 0;
            iInterval = 0;
            sContent = "";
            iConfType = 0;
            iShowDuration = 0;
            iSourceType = 0;
            iSpeed = 0;
            iRepeatTimes = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_MarqueeConfig();
            copied.iID = this.iID;
            copied.iStartTime = this.iStartTime;
            copied.iOverTime = this.iOverTime;
            copied.iInterval = this.iInterval;
            copied.sContent = this.sContent;
            copied.iConfType = this.iConfType;
            copied.iShowDuration = this.iShowDuration;
            copied.iSourceType = this.iSourceType;
            copied.iSpeed = this.iSpeed;
            copied.iRepeatTimes = this.iRepeatTimes;
            return copied;
        }
    }
}

