// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

	[Serializable]
    public sealed class TAC_HeroEntity : Wup.Jce.JceStruct
    {
        int _iEntityID = 0;
        public int iEntityID
        {
            get
            {
                 return _iEntityID;
            }
            set
            {
                _iEntityID = value; 
            }
        }

        int _iHeroConfID = 0;
        public int iHeroConfID
        {
            get
            {
                 return _iHeroConfID;
            }
            set
            {
                _iHeroConfID = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iEntityID, 0);
            _os.Write(iHeroConfID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iEntityID = (int) _is.Read(iEntityID, 0, false);

            iHeroConfID = (int) _is.Read(iHeroConfID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iEntityID, "iEntityID");
            _ds.Display(iHeroConfID, "iHeroConfID");
        }

        public override void Clear()
        {
            iEntityID = 0;
            iHeroConfID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_HeroEntity();
            copied.iEntityID = this.iEntityID;
            copied.iHeroConfID = this.iHeroConfID;
            return copied;
        }
    }
}

