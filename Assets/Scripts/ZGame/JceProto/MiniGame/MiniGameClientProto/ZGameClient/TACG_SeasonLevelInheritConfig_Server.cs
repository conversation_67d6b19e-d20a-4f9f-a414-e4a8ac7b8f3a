// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_SeasonLevelInheritConfig_Server : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iSeasonID = 0;

        public int iOldLevelID = 0;

        public string sOldLevelName = "";

        public int iNewLevelID = 0;

        public string sNewLevelName = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iSeasonID, 1);
            _os.Write(iOldLevelID, 2);
            _os.Write(sOldLevelName, 3);
            _os.Write(iNewLevelID, 4);
            _os.Write(sNewLevelName, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iSeasonID = (int) _is.Read(iSeasonID, 1, false);

            iOldLevelID = (int) _is.Read(iOldLevelID, 2, false);

            sOldLevelName = (string) _is.Read(sOldLevelName, 3, false);

            iNewLevelID = (int) _is.Read(iNewLevelID, 4, false);

            sNewLevelName = (string) _is.Read(sNewLevelName, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iSeasonID, "iSeasonID");
            _ds.Display(iOldLevelID, "iOldLevelID");
            _ds.Display(sOldLevelName, "sOldLevelName");
            _ds.Display(iNewLevelID, "iNewLevelID");
            _ds.Display(sNewLevelName, "sNewLevelName");
        }

        public override void Clear()
        {
            iIndex = 0;
            iSeasonID = 0;
            iOldLevelID = 0;
            sOldLevelName = "";
            iNewLevelID = 0;
            sNewLevelName = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_SeasonLevelInheritConfig_Server();
            copied.iIndex = this.iIndex;
            copied.iSeasonID = this.iSeasonID;
            copied.iOldLevelID = this.iOldLevelID;
            copied.sOldLevelName = this.sOldLevelName;
            copied.iNewLevelID = this.iNewLevelID;
            copied.sNewLevelName = this.sNewLevelName;
            return copied;
        }
    }
}

