// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class HonorLevelBasicData : Wup.Jce.JceStruct
    {
        public System.Collections.Generic.List<THonorPrivilegedItems_Server> honorPrivilegedItemsList;

        public int level = 0;

        public string desc = "";

        public bool allowShared = true;

        public int acceptShareTimes = 0;

        /// <summary>
        /// 月刷新任务
        /// </summary>
        public System.Collections.Generic.List<THonorTask_Server> monthlyRefreshTaskList;

        /// <summary>
        /// 一次性任务
        /// </summary>
        public System.Collections.Generic.List<THonorTask_Server> onceTaskList;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(honorPrivilegedItemsList, 1);
            _os.Write(level, 2);
            _os.Write(desc, 3);
            _os.Write(allowShared, 4);
            _os.Write(acceptShareTimes, 5);
            _os.Write(monthlyRefreshTaskList, 6);
            _os.Write(onceTaskList, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            honorPrivilegedItemsList = (System.Collections.Generic.List<THonorPrivilegedItems_Server>) _is.Read(honorPrivilegedItemsList, 1, false);

            level = (int) _is.Read(level, 2, false);

            desc = (string) _is.Read(desc, 3, false);

            allowShared = (bool) _is.Read(allowShared, 4, false);

            acceptShareTimes = (int) _is.Read(acceptShareTimes, 5, false);

            monthlyRefreshTaskList = (System.Collections.Generic.List<THonorTask_Server>) _is.Read(monthlyRefreshTaskList, 6, false);

            onceTaskList = (System.Collections.Generic.List<THonorTask_Server>) _is.Read(onceTaskList, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(honorPrivilegedItemsList, "honorPrivilegedItemsList");
            _ds.Display(level, "level");
            _ds.Display(desc, "desc");
            _ds.Display(allowShared, "allowShared");
            _ds.Display(acceptShareTimes, "acceptShareTimes");
            _ds.Display(monthlyRefreshTaskList, "monthlyRefreshTaskList");
            _ds.Display(onceTaskList, "onceTaskList");
        }

        public override void Clear()
        {
            honorPrivilegedItemsList = null;
            level = 0;
            desc = "";
            allowShared = true;
            acceptShareTimes = 0;
            monthlyRefreshTaskList = null;
            onceTaskList = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new HonorLevelBasicData();
            copied.honorPrivilegedItemsList = (System.Collections.Generic.List<THonorPrivilegedItems_Server>)JceUtil.DeepClone(this.honorPrivilegedItemsList);
            copied.level = this.level;
            copied.desc = this.desc;
            copied.allowShared = this.allowShared;
            copied.acceptShareTimes = this.acceptShareTimes;
            copied.monthlyRefreshTaskList = (System.Collections.Generic.List<THonorTask_Server>)JceUtil.DeepClone(this.monthlyRefreshTaskList);
            copied.onceTaskList = (System.Collections.Generic.List<THonorTask_Server>)JceUtil.DeepClone(this.onceTaskList);
            return copied;
        }
    }
}

