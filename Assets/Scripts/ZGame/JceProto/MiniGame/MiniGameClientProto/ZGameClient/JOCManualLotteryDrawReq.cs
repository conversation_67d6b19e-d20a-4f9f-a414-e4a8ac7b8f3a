// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualLotteryDrawReq : Wup.Jce.JceStruct
    {
        /// <summary>
        /// 抽奖类型：0单抽，1连抽，枚举：JOCManualLotteryDrawType
        /// </summary>
        public int drawType = 0;

        /// <summary>
        /// 米大师token
        /// </summary>
        public TMidasTokenInfo midasToken;

        /// <summary>
        /// 0不确认，需要检测背包/赠礼中心等情况；非0表示确认，无需检测直接抽取，枚举值 JOCManualLotteryPossessCondition
        /// </summary>
        public int confirmCondition = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(drawType, 0);
            _os.Write(midasToken, 1);
            _os.Write(confirmCondition, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            drawType = (int) _is.Read(drawType, 0, false);

            midasToken = (TMidasTokenInfo) _is.Read(midasToken, 1, false);

            confirmCondition = (int) _is.Read(confirmCondition, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(drawType, "drawType");
            _ds.Display(midasToken, "midasToken");
            _ds.Display(confirmCondition, "confirmCondition");
        }

        public override void Clear()
        {
            drawType = 0;
            midasToken = null;
            confirmCondition = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualLotteryDrawReq();
            copied.drawType = this.drawType;
            copied.midasToken = (TMidasTokenInfo)JceUtil.DeepClone(this.midasToken);
            copied.confirmCondition = this.confirmCondition;
            return copied;
        }
    }
}

