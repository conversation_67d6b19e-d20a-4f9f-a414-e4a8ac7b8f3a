// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TContestCfgItem : Wup.Jce.JceStruct
    {
        int _iContestID = 0;
        public int iContestID
        {
            get
            {
                 return _iContestID;
            }
            set
            {
                _iContestID = value; 
            }
        }

        string _sContestName = "";
        public string sContestName
        {
            get
            {
                 return _sContestName;
            }
            set
            {
                _sContestName = value; 
            }
        }

        int _iSceneType = 0;
        public int iSceneType
        {
            get
            {
                 return _iSceneType;
            }
            set
            {
                _iSceneType = value; 
            }
        }

        int _iOrderPower = 0;
        public int iOrderPower
        {
            get
            {
                 return _iOrderPower;
            }
            set
            {
                _iOrderPower = value; 
            }
        }

        int _iBeginTime = 0;
        public int iBeginTime
        {
            get
            {
                 return _iBeginTime;
            }
            set
            {
                _iBeginTime = value; 
            }
        }

        int _iEndTime = 0;
        public int iEndTime
        {
            get
            {
                 return _iEndTime;
            }
            set
            {
                _iEndTime = value; 
            }
        }

        int _iSignBeginTime = 0;
        public int iSignBeginTime
        {
            get
            {
                 return _iSignBeginTime;
            }
            set
            {
                _iSignBeginTime = value; 
            }
        }

        int _iSignEndTime = 0;
        public int iSignEndTime
        {
            get
            {
                 return _iSignEndTime;
            }
            set
            {
                _iSignEndTime = value; 
            }
        }

        string _sContestOpenDays = "";
        public string sContestOpenDays
        {
            get
            {
                 return _sContestOpenDays;
            }
            set
            {
                _sContestOpenDays = value; 
            }
        }

        int _iContestBeginHour = 0;
        public int iContestBeginHour
        {
            get
            {
                 return _iContestBeginHour;
            }
            set
            {
                _iContestBeginHour = value; 
            }
        }

        int _iContestEndHour = 0;
        public int iContestEndHour
        {
            get
            {
                 return _iContestEndHour;
            }
            set
            {
                _iContestEndHour = value; 
            }
        }

        int _iTicket1ItemId = 0;
        public int iTicket1ItemId
        {
            get
            {
                 return _iTicket1ItemId;
            }
            set
            {
                _iTicket1ItemId = value; 
            }
        }

        int _iTicket1ItemCount = 0;
        public int iTicket1ItemCount
        {
            get
            {
                 return _iTicket1ItemCount;
            }
            set
            {
                _iTicket1ItemCount = value; 
            }
        }

        int _iTicket2ItemId = 0;
        public int iTicket2ItemId
        {
            get
            {
                 return _iTicket2ItemId;
            }
            set
            {
                _iTicket2ItemId = value; 
            }
        }

        int _iTicket2ItemCount = 0;
        public int iTicket2ItemCount
        {
            get
            {
                 return _iTicket2ItemCount;
            }
            set
            {
                _iTicket2ItemCount = value; 
            }
        }

        int _iWinCount = 0;
        public int iWinCount
        {
            get
            {
                 return _iWinCount;
            }
            set
            {
                _iWinCount = value; 
            }
        }

        int _iRetryCount = 0;
        public int iRetryCount
        {
            get
            {
                 return _iRetryCount;
            }
            set
            {
                _iRetryCount = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iContestID, 0);
            _os.Write(sContestName, 1);
            _os.Write(iSceneType, 2);
            _os.Write(iOrderPower, 3);
            _os.Write(iBeginTime, 4);
            _os.Write(iEndTime, 5);
            _os.Write(iSignBeginTime, 6);
            _os.Write(iSignEndTime, 7);
            _os.Write(sContestOpenDays, 8);
            _os.Write(iContestBeginHour, 9);
            _os.Write(iContestEndHour, 10);
            _os.Write(iTicket1ItemId, 11);
            _os.Write(iTicket1ItemCount, 12);
            _os.Write(iTicket2ItemId, 13);
            _os.Write(iTicket2ItemCount, 14);
            _os.Write(iWinCount, 15);
            _os.Write(iRetryCount, 16);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iContestID = (int) _is.Read(iContestID, 0, false);

            sContestName = (string) _is.Read(sContestName, 1, false);

            iSceneType = (int) _is.Read(iSceneType, 2, false);

            iOrderPower = (int) _is.Read(iOrderPower, 3, false);

            iBeginTime = (int) _is.Read(iBeginTime, 4, false);

            iEndTime = (int) _is.Read(iEndTime, 5, false);

            iSignBeginTime = (int) _is.Read(iSignBeginTime, 6, false);

            iSignEndTime = (int) _is.Read(iSignEndTime, 7, false);

            sContestOpenDays = (string) _is.Read(sContestOpenDays, 8, false);

            iContestBeginHour = (int) _is.Read(iContestBeginHour, 9, false);

            iContestEndHour = (int) _is.Read(iContestEndHour, 10, false);

            iTicket1ItemId = (int) _is.Read(iTicket1ItemId, 11, false);

            iTicket1ItemCount = (int) _is.Read(iTicket1ItemCount, 12, false);

            iTicket2ItemId = (int) _is.Read(iTicket2ItemId, 13, false);

            iTicket2ItemCount = (int) _is.Read(iTicket2ItemCount, 14, false);

            iWinCount = (int) _is.Read(iWinCount, 15, false);

            iRetryCount = (int) _is.Read(iRetryCount, 16, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iContestID, "iContestID");
            _ds.Display(sContestName, "sContestName");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(iOrderPower, "iOrderPower");
            _ds.Display(iBeginTime, "iBeginTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(iSignBeginTime, "iSignBeginTime");
            _ds.Display(iSignEndTime, "iSignEndTime");
            _ds.Display(sContestOpenDays, "sContestOpenDays");
            _ds.Display(iContestBeginHour, "iContestBeginHour");
            _ds.Display(iContestEndHour, "iContestEndHour");
            _ds.Display(iTicket1ItemId, "iTicket1ItemId");
            _ds.Display(iTicket1ItemCount, "iTicket1ItemCount");
            _ds.Display(iTicket2ItemId, "iTicket2ItemId");
            _ds.Display(iTicket2ItemCount, "iTicket2ItemCount");
            _ds.Display(iWinCount, "iWinCount");
            _ds.Display(iRetryCount, "iRetryCount");
        }

        public override void Clear()
        {
            iContestID = 0;
            sContestName = "";
            iSceneType = 0;
            iOrderPower = 0;
            iBeginTime = 0;
            iEndTime = 0;
            iSignBeginTime = 0;
            iSignEndTime = 0;
            sContestOpenDays = "";
            iContestBeginHour = 0;
            iContestEndHour = 0;
            iTicket1ItemId = 0;
            iTicket1ItemCount = 0;
            iTicket2ItemId = 0;
            iTicket2ItemCount = 0;
            iWinCount = 0;
            iRetryCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TContestCfgItem();
            copied.iContestID = this.iContestID;
            copied.sContestName = this.sContestName;
            copied.iSceneType = this.iSceneType;
            copied.iOrderPower = this.iOrderPower;
            copied.iBeginTime = this.iBeginTime;
            copied.iEndTime = this.iEndTime;
            copied.iSignBeginTime = this.iSignBeginTime;
            copied.iSignEndTime = this.iSignEndTime;
            copied.sContestOpenDays = this.sContestOpenDays;
            copied.iContestBeginHour = this.iContestBeginHour;
            copied.iContestEndHour = this.iContestEndHour;
            copied.iTicket1ItemId = this.iTicket1ItemId;
            copied.iTicket1ItemCount = this.iTicket1ItemCount;
            copied.iTicket2ItemId = this.iTicket2ItemId;
            copied.iTicket2ItemCount = this.iTicket2ItemCount;
            copied.iWinCount = this.iWinCount;
            copied.iRetryCount = this.iRetryCount;
            return copied;
        }
    }
}

