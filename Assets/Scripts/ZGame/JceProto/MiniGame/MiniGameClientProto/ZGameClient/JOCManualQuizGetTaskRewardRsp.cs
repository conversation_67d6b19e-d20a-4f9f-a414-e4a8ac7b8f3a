// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualQuizGetTaskRewardRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public int taskID = 0;

        public TItemInfo reward;

        public JOCManualTaskClient task;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(taskID, 1);
            _os.Write(reward, 2);
            _os.Write(task, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            taskID = (int) _is.Read(taskID, 1, false);

            reward = (TItemInfo) _is.Read(reward, 2, false);

            task = (JOCManualTaskClient) _is.Read(task, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(taskID, "taskID");
            _ds.Display(reward, "reward");
            _ds.Display(task, "task");
        }

        public override void Clear()
        {
            ret = 0;
            taskID = 0;
            reward = null;
            task = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualQuizGetTaskRewardRsp();
            copied.ret = this.ret;
            copied.taskID = this.taskID;
            copied.reward = (TItemInfo)JceUtil.DeepClone(this.reward);
            copied.task = (JOCManualTaskClient)JceUtil.DeepClone(this.task);
            return copied;
        }
    }
}

