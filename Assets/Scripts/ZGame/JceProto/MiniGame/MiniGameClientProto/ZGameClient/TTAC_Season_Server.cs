// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_Season_Server : Wup.Jce.JceStruct
    {
        int _iSeasonId = 0;
        public int iSeasonId
        {
            get
            {
                 return _iSeasonId;
            }
            set
            {
                _iSeasonId = value; 
            }
        }

        string _sStartTime = "";
        public string sStartTime
        {
            get
            {
                 return _sStartTime;
            }
            set
            {
                _sStartTime = value; 
            }
        }

        string _sEndTime = "";
        public string sEndTime
        {
            get
            {
                 return _sEndTime;
            }
            set
            {
                _sEndTime = value; 
            }
        }

        int _iStartTime = 0;
        public int iStartTime
        {
            get
            {
                 return _iStartTime;
            }
            set
            {
                _iStartTime = value; 
            }
        }

        int _iEndTime = 0;
        public int iEndTime
        {
            get
            {
                 return _iEndTime;
            }
            set
            {
                _iEndTime = value; 
            }
        }

        string _sSeasonName = "";
        public string sSeasonName
        {
            get
            {
                 return _sSeasonName;
            }
            set
            {
                _sSeasonName = value; 
            }
        }

        string _sSeasonDate = "";
        public string sSeasonDate
        {
            get
            {
                 return _sSeasonDate;
            }
            set
            {
                _sSeasonDate = value; 
            }
        }

        int _iBPMaxFreeLv = 0;
        public int iBPMaxFreeLv
        {
            get
            {
                 return _iBPMaxFreeLv;
            }
            set
            {
                _iBPMaxFreeLv = value; 
            }
        }

        int _iBPMaxPayLv = 0;
        public int iBPMaxPayLv
        {
            get
            {
                 return _iBPMaxPayLv;
            }
            set
            {
                _iBPMaxPayLv = value; 
            }
        }

        int _iBPLvExp = 0;
        public int iBPLvExp
        {
            get
            {
                 return _iBPLvExp;
            }
            set
            {
                _iBPLvExp = value; 
            }
        }

        int _iEnableBP = 0;
        public int iEnableBP
        {
            get
            {
                 return _iEnableBP;
            }
            set
            {
                _iEnableBP = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iSeasonId, 0);
            _os.Write(sStartTime, 1);
            _os.Write(sEndTime, 2);
            _os.Write(iStartTime, 3);
            _os.Write(iEndTime, 4);
            _os.Write(sSeasonName, 5);
            _os.Write(sSeasonDate, 6);
            _os.Write(iBPMaxFreeLv, 7);
            _os.Write(iBPMaxPayLv, 8);
            _os.Write(iBPLvExp, 9);
            _os.Write(iEnableBP, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iSeasonId = (int) _is.Read(iSeasonId, 0, false);

            sStartTime = (string) _is.Read(sStartTime, 1, false);

            sEndTime = (string) _is.Read(sEndTime, 2, false);

            iStartTime = (int) _is.Read(iStartTime, 3, false);

            iEndTime = (int) _is.Read(iEndTime, 4, false);

            sSeasonName = (string) _is.Read(sSeasonName, 5, false);

            sSeasonDate = (string) _is.Read(sSeasonDate, 6, false);

            iBPMaxFreeLv = (int) _is.Read(iBPMaxFreeLv, 7, false);

            iBPMaxPayLv = (int) _is.Read(iBPMaxPayLv, 8, false);

            iBPLvExp = (int) _is.Read(iBPLvExp, 9, false);

            iEnableBP = (int) _is.Read(iEnableBP, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iSeasonId, "iSeasonId");
            _ds.Display(sStartTime, "sStartTime");
            _ds.Display(sEndTime, "sEndTime");
            _ds.Display(iStartTime, "iStartTime");
            _ds.Display(iEndTime, "iEndTime");
            _ds.Display(sSeasonName, "sSeasonName");
            _ds.Display(sSeasonDate, "sSeasonDate");
            _ds.Display(iBPMaxFreeLv, "iBPMaxFreeLv");
            _ds.Display(iBPMaxPayLv, "iBPMaxPayLv");
            _ds.Display(iBPLvExp, "iBPLvExp");
            _ds.Display(iEnableBP, "iEnableBP");
        }

    }
}

