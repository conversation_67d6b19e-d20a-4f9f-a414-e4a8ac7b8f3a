// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TExchangeWeeklyLoginRsp : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> rewards {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(rewards, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            rewards = (TKFrame.TKDictionary<int, int>) _is.Read(rewards, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(rewards, "rewards");
        }

        public override void Clear()
        {
            iRet = 0;
            rewards = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TExchangeWeeklyLoginRsp();
            copied.iRet = this.iRet;
            copied.rewards = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.rewards);
            return copied;
        }
    }
}

