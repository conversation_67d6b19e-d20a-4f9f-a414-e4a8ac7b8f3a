// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetFriendTalkInfoRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public System.Collections.Generic.List<TFriendTalkInfo> vecFriendTalkInfo;

        public int iSecClearPostTimestamp = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecFriendTalkInfo, 1);
            _os.Write(iSecClearPostTimestamp, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecFriendTalkInfo = (System.Collections.Generic.List<TFriendTalkInfo>) _is.Read(vecFriendTalkInfo, 1, false);

            iSecClearPostTimestamp = (int) _is.Read(iSecClearPostTimestamp, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecFriendTalkInfo, "vecFriendTalkInfo");
            _ds.Display(iSecClearPostTimestamp, "iSecClearPostTimestamp");
        }

        public override void Clear()
        {
            iRet = 0;
            vecFriendTalkInfo = null;
            iSecClearPostTimestamp = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetFriendTalkInfoRsp();
            copied.iRet = this.iRet;
            copied.vecFriendTalkInfo = (System.Collections.Generic.List<TFriendTalkInfo>)JceUtil.DeepClone(this.vecFriendTalkInfo);
            copied.iSecClearPostTimestamp = this.iSecClearPostTimestamp;
            return copied;
        }
    }
}

