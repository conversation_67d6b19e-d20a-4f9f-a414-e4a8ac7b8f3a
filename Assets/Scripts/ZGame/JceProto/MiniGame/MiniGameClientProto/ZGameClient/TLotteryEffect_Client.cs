// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TLotteryEffect_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iLotteryType = 0;

        public int iLotteryID = 0;

        public int iItemID = 0;

        public int iItemCount = 0;

        public string sEffect = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iLotteryType, 1);
            _os.Write(iLotteryID, 2);
            _os.Write(iItemID, 3);
            _os.Write(iItemCount, 4);
            _os.Write(sEffect, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iLotteryType = (int) _is.Read(iLotteryType, 1, false);

            iLotteryID = (int) _is.Read(iLotteryID, 2, false);

            iItemID = (int) _is.Read(iItemID, 3, false);

            iItemCount = (int) _is.Read(iItemCount, 4, false);

            sEffect = (string) _is.Read(sEffect, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iLotteryType, "iLotteryType");
            _ds.Display(iLotteryID, "iLotteryID");
            _ds.Display(iItemID, "iItemID");
            _ds.Display(iItemCount, "iItemCount");
            _ds.Display(sEffect, "sEffect");
        }

        public override void Clear()
        {
            iID = 0;
            iLotteryType = 0;
            iLotteryID = 0;
            iItemID = 0;
            iItemCount = 0;
            sEffect = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TLotteryEffect_Client();
            copied.iID = this.iID;
            copied.iLotteryType = this.iLotteryType;
            copied.iLotteryID = this.iLotteryID;
            copied.iItemID = this.iItemID;
            copied.iItemCount = this.iItemCount;
            copied.sEffect = this.sEffect;
            return copied;
        }
    }
}

