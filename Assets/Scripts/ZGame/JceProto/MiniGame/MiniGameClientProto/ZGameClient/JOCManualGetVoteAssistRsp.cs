// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualGetVoteAssistRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public bool isFriendVoteAssistNotifyRed = false;

        public TKFrame.TKDictionary<int, long> globalVote;

        public long voteProgress = 0;

        public TKFrame.TKDictionary<int, TJOCManualVoteReward_Server> voteRewardCfg;

        public TKFrame.TKDictionary<int, TJOCManualVote_Server> voteCfg;

        public TKFrame.TKDictionary<int, long> voteRewardRecvTime;

        public int selfVoteItemID = 0;

        public int selfVoteItemCount = 0;

        public int assistVoteItemID = 0;

        public int assistVoteItemCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(isFriendVoteAssistNotifyRed, 1);
            _os.Write(globalVote, 2);
            _os.Write(voteProgress, 3);
            _os.Write(voteRewardCfg, 4);
            _os.Write(voteCfg, 5);
            _os.Write(voteRewardRecvTime, 6);
            _os.Write(selfVoteItemID, 7);
            _os.Write(selfVoteItemCount, 8);
            _os.Write(assistVoteItemID, 9);
            _os.Write(assistVoteItemCount, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            isFriendVoteAssistNotifyRed = (bool) _is.Read(isFriendVoteAssistNotifyRed, 1, false);

            globalVote = (TKFrame.TKDictionary<int, long>) _is.Read(globalVote, 2, false);

            voteProgress = (long) _is.Read(voteProgress, 3, false);

            voteRewardCfg = (TKFrame.TKDictionary<int, TJOCManualVoteReward_Server>) _is.Read(voteRewardCfg, 4, false);

            voteCfg = (TKFrame.TKDictionary<int, TJOCManualVote_Server>) _is.Read(voteCfg, 5, false);

            voteRewardRecvTime = (TKFrame.TKDictionary<int, long>) _is.Read(voteRewardRecvTime, 6, false);

            selfVoteItemID = (int) _is.Read(selfVoteItemID, 7, false);

            selfVoteItemCount = (int) _is.Read(selfVoteItemCount, 8, false);

            assistVoteItemID = (int) _is.Read(assistVoteItemID, 9, false);

            assistVoteItemCount = (int) _is.Read(assistVoteItemCount, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(isFriendVoteAssistNotifyRed, "isFriendVoteAssistNotifyRed");
            _ds.Display(globalVote, "globalVote");
            _ds.Display(voteProgress, "voteProgress");
            _ds.Display(voteRewardCfg, "voteRewardCfg");
            _ds.Display(voteCfg, "voteCfg");
            _ds.Display(voteRewardRecvTime, "voteRewardRecvTime");
            _ds.Display(selfVoteItemID, "selfVoteItemID");
            _ds.Display(selfVoteItemCount, "selfVoteItemCount");
            _ds.Display(assistVoteItemID, "assistVoteItemID");
            _ds.Display(assistVoteItemCount, "assistVoteItemCount");
        }

        public override void Clear()
        {
            ret = 0;
            isFriendVoteAssistNotifyRed = false;
            globalVote = null;
            voteProgress = 0;
            voteRewardCfg = null;
            voteCfg = null;
            voteRewardRecvTime = null;
            selfVoteItemID = 0;
            selfVoteItemCount = 0;
            assistVoteItemID = 0;
            assistVoteItemCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualGetVoteAssistRsp();
            copied.ret = this.ret;
            copied.isFriendVoteAssistNotifyRed = this.isFriendVoteAssistNotifyRed;
            copied.globalVote = (TKFrame.TKDictionary<int, long>)JceUtil.DeepClone(this.globalVote);
            copied.voteProgress = this.voteProgress;
            copied.voteRewardCfg = (TKFrame.TKDictionary<int, TJOCManualVoteReward_Server>)JceUtil.DeepClone(this.voteRewardCfg);
            copied.voteCfg = (TKFrame.TKDictionary<int, TJOCManualVote_Server>)JceUtil.DeepClone(this.voteCfg);
            copied.voteRewardRecvTime = (TKFrame.TKDictionary<int, long>)JceUtil.DeepClone(this.voteRewardRecvTime);
            copied.selfVoteItemID = this.selfVoteItemID;
            copied.selfVoteItemCount = this.selfVoteItemCount;
            copied.assistVoteItemID = this.assistVoteItemID;
            copied.assistVoteItemCount = this.assistVoteItemCount;
            return copied;
        }
    }
}

