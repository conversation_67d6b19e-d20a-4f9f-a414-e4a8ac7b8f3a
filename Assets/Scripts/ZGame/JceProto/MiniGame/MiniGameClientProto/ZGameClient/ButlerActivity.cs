// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ButlerActivity : Wup.Jce.JceStruct
    {
        public int AreaId = 0;

        public int PlatId = 0;

        public int Partition = 0;

        public int TabType = 0;

        public long Id = 0;

        public int Type = 0;

        public int ShowOrder = 0;

        public int RedDotRule = 0;

        public int ImgType = 0;

        public string ImgPath = "";

        public int JumpType = 0;

        public string JumpPath = "";

        public string Name = "";

        public string Instruction = "";

        public string DetailInstruction = "";

        public string BeginTime = "";

        public string EndTime = "";

        public string BeginDisplayTime = "";

        public string DisLevelRequirements = "";

        public string JoinLevelRequirements = "";

        public string VersionLimit = "";

        public System.Collections.Generic.List<ButlerActivityTask> TaskList;

        public string Source = "";

        public string Serial = "";

        public int TaskList_count = 0;

        public int IconUrl = 0;

        public System.Collections.Generic.List<ButlerActivityTask> RealTaskList;

        public System.Collections.Generic.List<ButlerActivityTask> ExchangeList;

        public string ExchangeActivityTab = "";

        public int BeginTimestamp = 0;

        public int EndTimestamp = 0;

        public int BeginDisplayTimestamp = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(AreaId, 0);
            _os.Write(PlatId, 1);
            _os.Write(Partition, 2);
            _os.Write(TabType, 3);
            _os.Write(Id, 4);
            _os.Write(Type, 5);
            _os.Write(ShowOrder, 6);
            _os.Write(RedDotRule, 7);
            _os.Write(ImgType, 9);
            _os.Write(ImgPath, 10);
            _os.Write(JumpType, 11);
            _os.Write(JumpPath, 12);
            _os.Write(Name, 13);
            _os.Write(Instruction, 14);
            _os.Write(DetailInstruction, 15);
            _os.Write(BeginTime, 16);
            _os.Write(EndTime, 17);
            _os.Write(BeginDisplayTime, 18);
            _os.Write(DisLevelRequirements, 19);
            _os.Write(JoinLevelRequirements, 20);
            _os.Write(VersionLimit, 21);
            _os.Write(TaskList, 22);
            _os.Write(Source, 23);
            _os.Write(Serial, 24);
            _os.Write(TaskList_count, 25);
            _os.Write(IconUrl, 26);
            _os.Write(RealTaskList, 27);
            _os.Write(ExchangeList, 28);
            _os.Write(ExchangeActivityTab, 29);
            _os.Write(BeginTimestamp, 30);
            _os.Write(EndTimestamp, 31);
            _os.Write(BeginDisplayTimestamp, 32);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            AreaId = (int) _is.Read(AreaId, 0, false);

            PlatId = (int) _is.Read(PlatId, 1, false);

            Partition = (int) _is.Read(Partition, 2, false);

            TabType = (int) _is.Read(TabType, 3, false);

            Id = (long) _is.Read(Id, 4, false);

            Type = (int) _is.Read(Type, 5, false);

            ShowOrder = (int) _is.Read(ShowOrder, 6, false);

            RedDotRule = (int) _is.Read(RedDotRule, 7, false);

            ImgType = (int) _is.Read(ImgType, 9, false);

            ImgPath = (string) _is.Read(ImgPath, 10, false);

            JumpType = (int) _is.Read(JumpType, 11, false);

            JumpPath = (string) _is.Read(JumpPath, 12, false);

            Name = (string) _is.Read(Name, 13, false);

            Instruction = (string) _is.Read(Instruction, 14, false);

            DetailInstruction = (string) _is.Read(DetailInstruction, 15, false);

            BeginTime = (string) _is.Read(BeginTime, 16, false);

            EndTime = (string) _is.Read(EndTime, 17, false);

            BeginDisplayTime = (string) _is.Read(BeginDisplayTime, 18, false);

            DisLevelRequirements = (string) _is.Read(DisLevelRequirements, 19, false);

            JoinLevelRequirements = (string) _is.Read(JoinLevelRequirements, 20, false);

            VersionLimit = (string) _is.Read(VersionLimit, 21, false);

            TaskList = (System.Collections.Generic.List<ButlerActivityTask>) _is.Read(TaskList, 22, false);

            Source = (string) _is.Read(Source, 23, false);

            Serial = (string) _is.Read(Serial, 24, false);

            TaskList_count = (int) _is.Read(TaskList_count, 25, false);

            IconUrl = (int) _is.Read(IconUrl, 26, false);

            RealTaskList = (System.Collections.Generic.List<ButlerActivityTask>) _is.Read(RealTaskList, 27, false);

            ExchangeList = (System.Collections.Generic.List<ButlerActivityTask>) _is.Read(ExchangeList, 28, false);

            ExchangeActivityTab = (string) _is.Read(ExchangeActivityTab, 29, false);

            BeginTimestamp = (int) _is.Read(BeginTimestamp, 30, false);

            EndTimestamp = (int) _is.Read(EndTimestamp, 31, false);

            BeginDisplayTimestamp = (int) _is.Read(BeginDisplayTimestamp, 32, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(AreaId, "AreaId");
            _ds.Display(PlatId, "PlatId");
            _ds.Display(Partition, "Partition");
            _ds.Display(TabType, "TabType");
            _ds.Display(Id, "Id");
            _ds.Display(Type, "Type");
            _ds.Display(ShowOrder, "ShowOrder");
            _ds.Display(RedDotRule, "RedDotRule");
            _ds.Display(ImgType, "ImgType");
            _ds.Display(ImgPath, "ImgPath");
            _ds.Display(JumpType, "JumpType");
            _ds.Display(JumpPath, "JumpPath");
            _ds.Display(Name, "Name");
            _ds.Display(Instruction, "Instruction");
            _ds.Display(DetailInstruction, "DetailInstruction");
            _ds.Display(BeginTime, "BeginTime");
            _ds.Display(EndTime, "EndTime");
            _ds.Display(BeginDisplayTime, "BeginDisplayTime");
            _ds.Display(DisLevelRequirements, "DisLevelRequirements");
            _ds.Display(JoinLevelRequirements, "JoinLevelRequirements");
            _ds.Display(VersionLimit, "VersionLimit");
            _ds.Display(TaskList, "TaskList");
            _ds.Display(Source, "Source");
            _ds.Display(Serial, "Serial");
            _ds.Display(TaskList_count, "TaskList_count");
            _ds.Display(IconUrl, "IconUrl");
            _ds.Display(RealTaskList, "RealTaskList");
            _ds.Display(ExchangeList, "ExchangeList");
            _ds.Display(ExchangeActivityTab, "ExchangeActivityTab");
            _ds.Display(BeginTimestamp, "BeginTimestamp");
            _ds.Display(EndTimestamp, "EndTimestamp");
            _ds.Display(BeginDisplayTimestamp, "BeginDisplayTimestamp");
        }

        public override void Clear()
        {
            AreaId = 0;
            PlatId = 0;
            Partition = 0;
            TabType = 0;
            Id = 0;
            Type = 0;
            ShowOrder = 0;
            RedDotRule = 0;
            ImgType = 0;
            ImgPath = "";
            JumpType = 0;
            JumpPath = "";
            Name = "";
            Instruction = "";
            DetailInstruction = "";
            BeginTime = "";
            EndTime = "";
            BeginDisplayTime = "";
            DisLevelRequirements = "";
            JoinLevelRequirements = "";
            VersionLimit = "";
            TaskList = null;
            Source = "";
            Serial = "";
            TaskList_count = 0;
            IconUrl = 0;
            RealTaskList = null;
            ExchangeList = null;
            ExchangeActivityTab = "";
            BeginTimestamp = 0;
            EndTimestamp = 0;
            BeginDisplayTimestamp = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ButlerActivity();
            copied.AreaId = this.AreaId;
            copied.PlatId = this.PlatId;
            copied.Partition = this.Partition;
            copied.TabType = this.TabType;
            copied.Id = this.Id;
            copied.Type = this.Type;
            copied.ShowOrder = this.ShowOrder;
            copied.RedDotRule = this.RedDotRule;
            copied.ImgType = this.ImgType;
            copied.ImgPath = this.ImgPath;
            copied.JumpType = this.JumpType;
            copied.JumpPath = this.JumpPath;
            copied.Name = this.Name;
            copied.Instruction = this.Instruction;
            copied.DetailInstruction = this.DetailInstruction;
            copied.BeginTime = this.BeginTime;
            copied.EndTime = this.EndTime;
            copied.BeginDisplayTime = this.BeginDisplayTime;
            copied.DisLevelRequirements = this.DisLevelRequirements;
            copied.JoinLevelRequirements = this.JoinLevelRequirements;
            copied.VersionLimit = this.VersionLimit;
            copied.TaskList = (System.Collections.Generic.List<ButlerActivityTask>)JceUtil.DeepClone(this.TaskList);
            copied.Source = this.Source;
            copied.Serial = this.Serial;
            copied.TaskList_count = this.TaskList_count;
            copied.IconUrl = this.IconUrl;
            copied.RealTaskList = (System.Collections.Generic.List<ButlerActivityTask>)JceUtil.DeepClone(this.RealTaskList);
            copied.ExchangeList = (System.Collections.Generic.List<ButlerActivityTask>)JceUtil.DeepClone(this.ExchangeList);
            copied.ExchangeActivityTab = this.ExchangeActivityTab;
            copied.BeginTimestamp = this.BeginTimestamp;
            copied.EndTimestamp = this.EndTimestamp;
            copied.BeginDisplayTimestamp = this.BeginDisplayTimestamp;
            return copied;
        }
    }
}

