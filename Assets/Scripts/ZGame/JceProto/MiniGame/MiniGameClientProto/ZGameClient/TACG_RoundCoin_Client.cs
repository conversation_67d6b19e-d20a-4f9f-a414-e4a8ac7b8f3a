//所在的Excel 【ACG_RoundCoinExp.xlsm】
//**************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_RoundCoin_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sTurnAddMoney = "";

        public string sConWinAddMoney = "";

        public string sConLoseAddMoney = "";

        public int iCountInterest = 0;

        public string sRankAddCoin = "";

        public int iInitCoin = 0;

        public int iPlan = 0;

        public int iWinAddCoin = 0;

        public string sTeamAddCoin = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sTurnAddMoney, 1);
            _os.Write(sConWinAddMoney, 2);
            _os.Write(sConLoseAddMoney, 3);
            _os.Write(iCountInterest, 4);
            _os.Write(sRankAddCoin, 5);
            _os.Write(iInitCoin, 6);
            _os.Write(iPlan, 7);
            _os.Write(iWinAddCoin, 8);
            _os.Write(sTeamAddCoin, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sTurnAddMoney = (string) _is.Read(sTurnAddMoney, 1, false);

            sConWinAddMoney = (string) _is.Read(sConWinAddMoney, 2, false);

            sConLoseAddMoney = (string) _is.Read(sConLoseAddMoney, 3, false);

            iCountInterest = (int) _is.Read(iCountInterest, 4, false);

            sRankAddCoin = (string) _is.Read(sRankAddCoin, 5, false);

            iInitCoin = (int) _is.Read(iInitCoin, 6, false);

            iPlan = (int) _is.Read(iPlan, 7, false);

            iWinAddCoin = (int) _is.Read(iWinAddCoin, 8, false);

            sTeamAddCoin = (string) _is.Read(sTeamAddCoin, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sTurnAddMoney, "sTurnAddMoney");
            _ds.Display(sConWinAddMoney, "sConWinAddMoney");
            _ds.Display(sConLoseAddMoney, "sConLoseAddMoney");
            _ds.Display(iCountInterest, "iCountInterest");
            _ds.Display(sRankAddCoin, "sRankAddCoin");
            _ds.Display(iInitCoin, "iInitCoin");
            _ds.Display(iPlan, "iPlan");
            _ds.Display(iWinAddCoin, "iWinAddCoin");
            _ds.Display(sTeamAddCoin, "sTeamAddCoin");
        }

        public override void Clear()
        {
            iID = 0;
            sTurnAddMoney = "";
            sConWinAddMoney = "";
            sConLoseAddMoney = "";
            iCountInterest = 0;
            sRankAddCoin = "";
            iInitCoin = 0;
            iPlan = 0;
            iWinAddCoin = 0;
            sTeamAddCoin = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_RoundCoin_Client();
            copied.iID = this.iID;
            copied.sTurnAddMoney = this.sTurnAddMoney;
            copied.sConWinAddMoney = this.sConWinAddMoney;
            copied.sConLoseAddMoney = this.sConLoseAddMoney;
            copied.iCountInterest = this.iCountInterest;
            copied.sRankAddCoin = this.sRankAddCoin;
            copied.iInitCoin = this.iInitCoin;
            copied.iPlan = this.iPlan;
            copied.iWinAddCoin = this.iWinAddCoin;
            copied.sTeamAddCoin = this.sTeamAddCoin;
            return copied;
        }
    }
}

