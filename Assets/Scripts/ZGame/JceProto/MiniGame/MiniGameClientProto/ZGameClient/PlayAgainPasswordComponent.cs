// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class PlayAgainPasswordComponent : Wup.Jce.JceStruct
    {
        public long guid = 0;

        public long minUin = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(guid, 1);
            _os.Write(minUin, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            guid = (long) _is.Read(guid, 1, false);

            minUin = (long) _is.Read(minUin, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(guid, "guid");
            _ds.Display(minUin, "minUin");
        }

        public override void Clear()
        {
            guid = 0;
            minUin = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new PlayAgainPasswordComponent();
            copied.guid = this.guid;
            copied.minUin = this.minUin;
            return copied;
        }
    }
}

