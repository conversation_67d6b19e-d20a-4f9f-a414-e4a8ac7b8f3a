// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualBulletScreenGetRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public System.Collections.Generic.List<JOCManualBulletScreenClientData> items;

        public System.Collections.Generic.List<JOCManualBulletScreenClientCfg> cfgs;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(items, 1);
            _os.Write(cfgs, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            items = (System.Collections.Generic.List<JOCManualBulletScreenClientData>) _is.Read(items, 1, false);

            cfgs = (System.Collections.Generic.List<JOCManualBulletScreenClientCfg>) _is.Read(cfgs, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(items, "items");
            _ds.Display(cfgs, "cfgs");
        }

        public override void Clear()
        {
            ret = 0;
            items = null;
            cfgs = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualBulletScreenGetRsp();
            copied.ret = this.ret;
            copied.items = (System.Collections.Generic.List<JOCManualBulletScreenClientData>)JceUtil.DeepClone(this.items);
            copied.cfgs = (System.Collections.Generic.List<JOCManualBulletScreenClientCfg>)JceUtil.DeepClone(this.cfgs);
            return copied;
        }
    }
}

