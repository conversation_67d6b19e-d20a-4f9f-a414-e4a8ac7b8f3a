// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGame
{

    public sealed class TObRoomAIPlayerInfo : Wup.Jce.JceStruct
    {
        long _lUin = 0;
        public long lUin
        {
            get
            {
                 return _lUin;
            }
            set
            {
                _lUin = value; 
            }
        }

        string _strAINickName = "";
        public string strAINickName
        {
            get
            {
                 return _strAINickName;
            }
            set
            {
                _strAINickName = value; 
            }
        }

        string _strAIFaceUrl = "";
        public string strAIFaceUrl
        {
            get
            {
                 return _strAIFaceUrl;
            }
            set
            {
                _strAIFaceUrl = value; 
            }
        }

        int _iLevel = 0;
        public int iLevel
        {
            get
            {
                 return _iLevel;
            }
            set
            {
                _iLevel = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lUin, 0);
            _os.Write(strAINickName, 1);
            _os.Write(strAIFaceUrl, 2);
            _os.Write(iLevel, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lUin = (long) _is.Read(lUin, 0, false);

            strAINickName = (string) _is.Read(strAINickName, 1, false);

            strAIFaceUrl = (string) _is.Read(strAIFaceUrl, 2, false);

            iLevel = (int) _is.Read(iLevel, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lUin, "lUin");
            _ds.Display(strAINickName, "strAINickName");
            _ds.Display(strAIFaceUrl, "strAIFaceUrl");
            _ds.Display(iLevel, "iLevel");
        }

        public override void Clear()
        {
            lUin = 0;
            strAINickName = "";
            strAIFaceUrl = "";
            iLevel = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TObRoomAIPlayerInfo();
            copied.lUin = this.lUin;
            copied.strAINickName = this.strAINickName;
            copied.strAIFaceUrl = this.strAIFaceUrl;
            copied.iLevel = this.iLevel;
            return copied;
        }
    }
}

