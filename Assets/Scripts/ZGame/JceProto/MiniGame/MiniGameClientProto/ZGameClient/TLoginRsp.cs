// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TLoginRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public string sIconUrl = "";

        public string sUserName = "";

        public int iHelloInterval = 0;

        public string sBanReason = "";

        public bool bShieldIOSVersion = false;

        public long lPriviFlag = 0;

        public string sErrMsg = "";

        public int iLoginWaitingTime = 0;

        public int iContinueLoginWaitingTime = 0;

        public int iMatchMaxTime = 0;

        public int iNeedMod = 0;

        public TAC_UserTinyTotalData stTAC_UserTinyTotalData;

        public int iFriendMatchRoomHeartBeatInterval = 10;

        public TAC_TUserLevel stLavaUserLevel;

        public int iCurrentAvatarBoxID = 0;

        public int iCoupons = 0;

        public int iBlueEssence = 0;

        public int iHeadIconID = 0;

        public bool bIsOpenTLogReport = false;

        public TKFrame.TKDictionary<int, string> mapClientData;

        public bool isNewPlayer = false;

        public TClientForbidInfo clientForbidInfo;

        public int iSpecificMidasZoneID = 0;

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<TMainHallGoods>> mapMainHallGoods;

        public LoginRspItemData stItemData;

        public MainHallData stMainHallData;

        public int iLoginedMaxClientVersion = 0;

        public string clientIP = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(sIconUrl, 4);
            _os.Write(sUserName, 5);
            _os.Write(iHelloInterval, 7);
            _os.Write(sBanReason, 9);
            _os.Write(bShieldIOSVersion, 12);
            _os.Write(lPriviFlag, 15);
            _os.Write(sErrMsg, 16);
            _os.Write(iLoginWaitingTime, 17);
            _os.Write(iContinueLoginWaitingTime, 18);
            _os.Write(iMatchMaxTime, 23);
            _os.Write(iNeedMod, 25);
            _os.Write(stTAC_UserTinyTotalData, 26);
            _os.Write(iFriendMatchRoomHeartBeatInterval, 27);
            _os.Write(stLavaUserLevel, 28);
            _os.Write(iCurrentAvatarBoxID, 29);
            _os.Write(iCoupons, 30);
            _os.Write(iBlueEssence, 31);
            _os.Write(iHeadIconID, 32);
            _os.Write(bIsOpenTLogReport, 33);
            _os.Write(mapClientData, 35);
            _os.Write(isNewPlayer, 36);
            _os.Write(clientForbidInfo, 37);
            _os.Write(iSpecificMidasZoneID, 38);
            _os.Write(mapMainHallGoods, 39);
            _os.Write(stItemData, 40);
            _os.Write(stMainHallData, 41);
            _os.Write(iLoginedMaxClientVersion, 42);
            _os.Write(clientIP, 43);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            sIconUrl = (string) _is.Read(sIconUrl, 4, false);

            sUserName = (string) _is.Read(sUserName, 5, false);

            iHelloInterval = (int) _is.Read(iHelloInterval, 7, false);

            sBanReason = (string) _is.Read(sBanReason, 9, false);

            bShieldIOSVersion = (bool) _is.Read(bShieldIOSVersion, 12, false);

            lPriviFlag = (long) _is.Read(lPriviFlag, 15, false);

            sErrMsg = (string) _is.Read(sErrMsg, 16, false);

            iLoginWaitingTime = (int) _is.Read(iLoginWaitingTime, 17, false);

            iContinueLoginWaitingTime = (int) _is.Read(iContinueLoginWaitingTime, 18, false);

            iMatchMaxTime = (int) _is.Read(iMatchMaxTime, 23, false);

            iNeedMod = (int) _is.Read(iNeedMod, 25, false);

            stTAC_UserTinyTotalData = (TAC_UserTinyTotalData) _is.Read(stTAC_UserTinyTotalData, 26, false);

            iFriendMatchRoomHeartBeatInterval = (int) _is.Read(iFriendMatchRoomHeartBeatInterval, 27, false);

            stLavaUserLevel = (TAC_TUserLevel) _is.Read(stLavaUserLevel, 28, false);

            iCurrentAvatarBoxID = (int) _is.Read(iCurrentAvatarBoxID, 29, false);

            iCoupons = (int) _is.Read(iCoupons, 30, false);

            iBlueEssence = (int) _is.Read(iBlueEssence, 31, false);

            iHeadIconID = (int) _is.Read(iHeadIconID, 32, false);

            bIsOpenTLogReport = (bool) _is.Read(bIsOpenTLogReport, 33, false);

            mapClientData = (TKFrame.TKDictionary<int, string>) _is.Read(mapClientData, 35, false);

            isNewPlayer = (bool) _is.Read(isNewPlayer, 36, false);

            clientForbidInfo = (TClientForbidInfo) _is.Read(clientForbidInfo, 37, false);

            iSpecificMidasZoneID = (int) _is.Read(iSpecificMidasZoneID, 38, false);

            mapMainHallGoods = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TMainHallGoods>>) _is.Read(mapMainHallGoods, 39, false);

            stItemData = (LoginRspItemData) _is.Read(stItemData, 40, false);

            stMainHallData = (MainHallData) _is.Read(stMainHallData, 41, false);

            iLoginedMaxClientVersion = (int) _is.Read(iLoginedMaxClientVersion, 42, false);

            clientIP = (string) _is.Read(clientIP, 43, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(sIconUrl, "sIconUrl");
            _ds.Display(sUserName, "sUserName");
            _ds.Display(iHelloInterval, "iHelloInterval");
            _ds.Display(sBanReason, "sBanReason");
            _ds.Display(bShieldIOSVersion, "bShieldIOSVersion");
            _ds.Display(lPriviFlag, "lPriviFlag");
            _ds.Display(sErrMsg, "sErrMsg");
            _ds.Display(iLoginWaitingTime, "iLoginWaitingTime");
            _ds.Display(iContinueLoginWaitingTime, "iContinueLoginWaitingTime");
            _ds.Display(iMatchMaxTime, "iMatchMaxTime");
            _ds.Display(iNeedMod, "iNeedMod");
            _ds.Display(stTAC_UserTinyTotalData, "stTAC_UserTinyTotalData");
            _ds.Display(iFriendMatchRoomHeartBeatInterval, "iFriendMatchRoomHeartBeatInterval");
            _ds.Display(stLavaUserLevel, "stLavaUserLevel");
            _ds.Display(iCurrentAvatarBoxID, "iCurrentAvatarBoxID");
            _ds.Display(iCoupons, "iCoupons");
            _ds.Display(iBlueEssence, "iBlueEssence");
            _ds.Display(iHeadIconID, "iHeadIconID");
            _ds.Display(bIsOpenTLogReport, "bIsOpenTLogReport");
            _ds.Display(mapClientData, "mapClientData");
            _ds.Display(isNewPlayer, "isNewPlayer");
            _ds.Display(clientForbidInfo, "clientForbidInfo");
            _ds.Display(iSpecificMidasZoneID, "iSpecificMidasZoneID");
            _ds.Display(mapMainHallGoods, "mapMainHallGoods");
            _ds.Display(stItemData, "stItemData");
            _ds.Display(stMainHallData, "stMainHallData");
            _ds.Display(iLoginedMaxClientVersion, "iLoginedMaxClientVersion");
            _ds.Display(clientIP, "clientIP");
        }

        public override void Clear()
        {
            iRet = 0;
            sIconUrl = "";
            sUserName = "";
            iHelloInterval = 0;
            sBanReason = "";
            bShieldIOSVersion = false;
            lPriviFlag = 0;
            sErrMsg = "";
            iLoginWaitingTime = 0;
            iContinueLoginWaitingTime = 0;
            iMatchMaxTime = 0;
            iNeedMod = 0;
            stTAC_UserTinyTotalData = null;
            iFriendMatchRoomHeartBeatInterval = 10;
            stLavaUserLevel = null;
            iCurrentAvatarBoxID = 0;
            iCoupons = 0;
            iBlueEssence = 0;
            iHeadIconID = 0;
            bIsOpenTLogReport = false;
            mapClientData = null;
            isNewPlayer = false;
            clientForbidInfo = null;
            iSpecificMidasZoneID = 0;
            mapMainHallGoods = null;
            stItemData = null;
            stMainHallData = null;
            iLoginedMaxClientVersion = 0;
            clientIP = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TLoginRsp();
            copied.iRet = this.iRet;
            copied.sIconUrl = this.sIconUrl;
            copied.sUserName = this.sUserName;
            copied.iHelloInterval = this.iHelloInterval;
            copied.sBanReason = this.sBanReason;
            copied.bShieldIOSVersion = this.bShieldIOSVersion;
            copied.lPriviFlag = this.lPriviFlag;
            copied.sErrMsg = this.sErrMsg;
            copied.iLoginWaitingTime = this.iLoginWaitingTime;
            copied.iContinueLoginWaitingTime = this.iContinueLoginWaitingTime;
            copied.iMatchMaxTime = this.iMatchMaxTime;
            copied.iNeedMod = this.iNeedMod;
            copied.stTAC_UserTinyTotalData = (TAC_UserTinyTotalData)JceUtil.DeepClone(this.stTAC_UserTinyTotalData);
            copied.iFriendMatchRoomHeartBeatInterval = this.iFriendMatchRoomHeartBeatInterval;
            copied.stLavaUserLevel = (TAC_TUserLevel)JceUtil.DeepClone(this.stLavaUserLevel);
            copied.iCurrentAvatarBoxID = this.iCurrentAvatarBoxID;
            copied.iCoupons = this.iCoupons;
            copied.iBlueEssence = this.iBlueEssence;
            copied.iHeadIconID = this.iHeadIconID;
            copied.bIsOpenTLogReport = this.bIsOpenTLogReport;
            copied.mapClientData = (TKFrame.TKDictionary<int, string>)JceUtil.DeepClone(this.mapClientData);
            copied.isNewPlayer = this.isNewPlayer;
            copied.clientForbidInfo = (TClientForbidInfo)JceUtil.DeepClone(this.clientForbidInfo);
            copied.iSpecificMidasZoneID = this.iSpecificMidasZoneID;
            copied.mapMainHallGoods = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TMainHallGoods>>)JceUtil.DeepClone(this.mapMainHallGoods);
            copied.stItemData = (LoginRspItemData)JceUtil.DeepClone(this.stItemData);
            copied.stMainHallData = (MainHallData)JceUtil.DeepClone(this.stMainHallData);
            copied.iLoginedMaxClientVersion = this.iLoginedMaxClientVersion;
            copied.clientIP = this.clientIP;
            return copied;
        }
    }
}

