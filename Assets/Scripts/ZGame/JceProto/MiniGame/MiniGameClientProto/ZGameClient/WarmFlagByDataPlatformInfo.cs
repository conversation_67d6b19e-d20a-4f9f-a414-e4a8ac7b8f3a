// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class WarmFlagByDataPlatformInfo : Wup.Jce.JceStruct
    {
        int _warmFlag = 0;
        public int warmFlag
        {
            get
            {
                 return _warmFlag;
            }
            set
            {
                _warmFlag = value; 
            }
        }

        bool _limitTriggered = false;
        public bool limitTriggered
        {
            get
            {
                 return _limitTriggered;
            }
            set
            {
                _limitTriggered = value; 
            }
        }

        int _dataPlatformRequestStatus = 0;
        public int dataPlatformRequestStatus
        {
            get
            {
                 return _dataPlatformRequestStatus;
            }
            set
            {
                _dataPlatformRequestStatus = value; 
            }
        }

        string _algoType = "";
        public string algoType
        {
            get
            {
                 return _algoType;
            }
            set
            {
                _algoType = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(warmFlag, 0);
            _os.Write(limitTriggered, 1);
            _os.Write(dataPlatformRequestStatus, 2);
            _os.Write(algoType, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            warmFlag = (int) _is.Read(warmFlag, 0, false);

            limitTriggered = (bool) _is.Read(limitTriggered, 1, false);

            dataPlatformRequestStatus = (int) _is.Read(dataPlatformRequestStatus, 2, false);

            algoType = (string) _is.Read(algoType, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(warmFlag, "warmFlag");
            _ds.Display(limitTriggered, "limitTriggered");
            _ds.Display(dataPlatformRequestStatus, "dataPlatformRequestStatus");
            _ds.Display(algoType, "algoType");
        }

        public override void Clear()
        {
            warmFlag = 0;
            limitTriggered = false;
            dataPlatformRequestStatus = 0;
            algoType = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new WarmFlagByDataPlatformInfo();
            copied.warmFlag = this.warmFlag;
            copied.limitTriggered = this.limitTriggered;
            copied.dataPlatformRequestStatus = this.dataPlatformRequestStatus;
            copied.algoType = this.algoType;
            return copied;
        }
    }
}

