//所在的Excel 【ACG_GuideConfig.xlsm】
//***************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_GuideTipsPool_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iPoolID = 0;

        public int iModeID = 0;

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iPoolID, 1);
            _os.Write(iModeID, 2);
            _os.Write(iWeight, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iPoolID = (int) _is.Read(iPoolID, 1, false);

            iModeID = (int) _is.Read(iModeID, 2, false);

            iWeight = (int) _is.Read(iWeight, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iPoolID, "iPoolID");
            _ds.Display(iModeID, "iModeID");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iPoolID = 0;
            iModeID = 0;
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_GuideTipsPool_Client();
            copied.iID = this.iID;
            copied.iPoolID = this.iPoolID;
            copied.iModeID = this.iModeID;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

