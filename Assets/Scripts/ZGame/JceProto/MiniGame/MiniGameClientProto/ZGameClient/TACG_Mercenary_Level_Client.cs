//所在的Excel 【ACG_Mercenary.xlsm】
//*****************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_Mercenary_Level_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iWeight, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iWeight = (int) _is.Read(iWeight, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_Mercenary_Level_Client();
            copied.iID = this.iID;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

