// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ACG_TCmdS2CNotifySingleHeroRecordChangeInfo : Wup.Jce.JceStruct
    {
        int _ChairID = 0;
        public int ChairID
        {
            get
            {
                 return _ChairID;
            }
            set
            {
                _ChairID = value; 
            }
        }

        public TKFrame.TKDictionary<int, int> dict {get; set;} 

        int _HeroEnityId = 0;
        public int HeroEnityId
        {
            get
            {
                 return _HeroEnityId;
            }
            set
            {
                _HeroEnityId = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ChairID, 0);
            _os.Write(dict, 1);
            _os.Write(HeroEnityId, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ChairID = (int) _is.Read(ChairID, 0, false);

            dict = (TKFrame.TKDictionary<int, int>) _is.Read(dict, 1, false);

            HeroEnityId = (int) _is.Read(HeroEnityId, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ChairID, "ChairID");
            _ds.Display(dict, "dict");
            _ds.Display(HeroEnityId, "HeroEnityId");
        }

        public override void Clear()
        {
            ChairID = 0;
            dict = null;
            HeroEnityId = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ACG_TCmdS2CNotifySingleHeroRecordChangeInfo();
            copied.ChairID = this.ChairID;
            copied.dict = (TKFrame.TKDictionary<int, int>)JceUtil.DeepClone(this.dict);
            copied.HeroEnityId = this.HeroEnityId;
            return copied;
        }
    }
}

