// **********************************************************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_PKGEquipPool_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iPoolID = 0;

        public int iEquipID = 0;

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iPoolID, 1);
            _os.Write(iEquipID, 2);
            _os.Write(iWeight, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iPoolID = (int) _is.Read(iPoolID, 1, false);

            iEquipID = (int) _is.Read(iEquipID, 2, false);

            iWeight = (int) _is.Read(iWeight, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iPoolID, "iPoolID");
            _ds.Display(iEquipID, "iEquipID");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iPoolID = 0;
            iEquipID = 0;
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_PKGEquipPool_Client();
            copied.iID = this.iID;
            copied.iPoolID = this.iPoolID;
            copied.iEquipID = this.iEquipID;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

