// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TReportStartGameFailedReq : Wup.Jce.JceStruct
    {
        public int reason = 0;

        public string message = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(reason, 0);
            _os.Write(message, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            reason = (int) _is.Read(reason, 0, false);

            message = (string) _is.Read(message, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(reason, "reason");
            _ds.Display(message, "message");
        }

        public override void Clear()
        {
            reason = 0;
            message = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TReportStartGameFailedReq();
            copied.reason = this.reason;
            copied.message = this.message;
            return copied;
        }
    }
}

