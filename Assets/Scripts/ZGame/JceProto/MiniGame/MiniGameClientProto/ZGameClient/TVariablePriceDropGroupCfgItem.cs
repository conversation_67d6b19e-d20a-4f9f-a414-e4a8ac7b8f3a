// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TVariablePriceDropGroupCfgItem : Wup.Jce.JceStruct
    {
        public int iGoodsID = 0;

        public int iGoodsNum = 0;

        public int iOriginSinglePrice = 0;

        public int iNowSinglePrice = 0;

        public bool bHaveItem = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iGoodsID, 0);
            _os.Write(iGoodsNum, 1);
            _os.Write(iOriginSinglePrice, 2);
            _os.Write(iNowSinglePrice, 3);
            _os.Write(bHaveItem, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iGoodsID = (int) _is.Read(iGoodsID, 0, false);

            iGoodsNum = (int) _is.Read(iGoodsNum, 1, false);

            iOriginSinglePrice = (int) _is.Read(iOriginSinglePrice, 2, false);

            iNowSinglePrice = (int) _is.Read(iNowSinglePrice, 3, false);

            bHaveItem = (bool) _is.Read(bHaveItem, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iGoodsID, "iGoodsID");
            _ds.Display(iGoodsNum, "iGoodsNum");
            _ds.Display(iOriginSinglePrice, "iOriginSinglePrice");
            _ds.Display(iNowSinglePrice, "iNowSinglePrice");
            _ds.Display(bHaveItem, "bHaveItem");
        }

        public override void Clear()
        {
            iGoodsID = 0;
            iGoodsNum = 0;
            iOriginSinglePrice = 0;
            iNowSinglePrice = 0;
            bHaveItem = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TVariablePriceDropGroupCfgItem();
            copied.iGoodsID = this.iGoodsID;
            copied.iGoodsNum = this.iGoodsNum;
            copied.iOriginSinglePrice = this.iOriginSinglePrice;
            copied.iNowSinglePrice = this.iNowSinglePrice;
            copied.bHaveItem = this.bHaveItem;
            return copied;
        }
    }
}

