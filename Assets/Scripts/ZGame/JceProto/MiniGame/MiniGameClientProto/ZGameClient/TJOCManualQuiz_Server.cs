// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCManualQuiz_Server : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iManualID = 0;

        public int iQuizID = 0;

        public string sDesc = "";

        public int iType = 0;

        public int iExp = 0;

        public int iCorrectOptionID = 0;

        public int iOptionID = 0;

        public string sOptionDesc = "";

        public string sOptionPic = "";

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iManualID, 1);
            _os.Write(iQuizID, 2);
            _os.Write(sDesc, 3);
            _os.Write(iType, 4);
            _os.Write(iExp, 5);
            _os.Write(iCorrectOptionID, 6);
            _os.Write(iOptionID, 7);
            _os.Write(sOptionDesc, 8);
            _os.Write(sOptionPic, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iManualID = (int) _is.Read(iManualID, 1, false);

            iQuizID = (int) _is.Read(iQuizID, 2, false);

            sDesc = (string) _is.Read(sDesc, 3, false);

            iType = (int) _is.Read(iType, 4, false);

            iExp = (int) _is.Read(iExp, 5, false);

            iCorrectOptionID = (int) _is.Read(iCorrectOptionID, 6, false);

            iOptionID = (int) _is.Read(iOptionID, 7, false);

            sOptionDesc = (string) _is.Read(sOptionDesc, 8, false);

            sOptionPic = (string) _is.Read(sOptionPic, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iManualID, "iManualID");
            _ds.Display(iQuizID, "iQuizID");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(iType, "iType");
            _ds.Display(iExp, "iExp");
            _ds.Display(iCorrectOptionID, "iCorrectOptionID");
            _ds.Display(iOptionID, "iOptionID");
            _ds.Display(sOptionDesc, "sOptionDesc");
            _ds.Display(sOptionPic, "sOptionPic");
        }

        public override void Clear()
        {
            iID = 0;
            iManualID = 0;
            iQuizID = 0;
            sDesc = "";
            iType = 0;
            iExp = 0;
            iCorrectOptionID = 0;
            iOptionID = 0;
            sOptionDesc = "";
            sOptionPic = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCManualQuiz_Server();
            copied.iID = this.iID;
            copied.iManualID = this.iManualID;
            copied.iQuizID = this.iQuizID;
            copied.sDesc = this.sDesc;
            copied.iType = this.iType;
            copied.iExp = this.iExp;
            copied.iCorrectOptionID = this.iCorrectOptionID;
            copied.iOptionID = this.iOptionID;
            copied.sOptionDesc = this.sOptionDesc;
            copied.sOptionPic = this.sOptionPic;
            return copied;
        }
    }
}

