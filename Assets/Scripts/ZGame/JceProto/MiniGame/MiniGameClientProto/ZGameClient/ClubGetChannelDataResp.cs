// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class ClubGetChannelDataResp : Wup.Jce.JceStruct
    {
        public ClubChannelData channelData;

        public TKFrame.TKDictionary<TUserID, ClubExtraUserInfoForClient> extraInfoMap;

        public int err = 0;

        public string clubName = "";

        public int currentMemberNum = 0;

        public int onlineMemberNum = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(channelData, 0);
            _os.Write(extraInfoMap, 1);
            _os.Write(err, 2);
            _os.Write(clubName, 3);
            _os.Write(currentMemberNum, 4);
            _os.Write(onlineMemberNum, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            channelData = (ClubChannelData) _is.Read(channelData, 0, false);

            extraInfoMap = (TKFrame.TKDictionary<TUserID, ClubExtraUserInfoForClient>) _is.Read(extraInfoMap, 1, false);

            err = (int) _is.Read(err, 2, false);

            clubName = (string) _is.Read(clubName, 3, false);

            currentMemberNum = (int) _is.Read(currentMemberNum, 4, false);

            onlineMemberNum = (int) _is.Read(onlineMemberNum, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(channelData, "channelData");
            _ds.Display(extraInfoMap, "extraInfoMap");
            _ds.Display(err, "err");
            _ds.Display(clubName, "clubName");
            _ds.Display(currentMemberNum, "currentMemberNum");
            _ds.Display(onlineMemberNum, "onlineMemberNum");
        }

        public override void Clear()
        {
            channelData = null;
            extraInfoMap = null;
            err = 0;
            clubName = "";
            currentMemberNum = 0;
            onlineMemberNum = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new ClubGetChannelDataResp();
            copied.channelData = (ClubChannelData)JceUtil.DeepClone(this.channelData);
            copied.extraInfoMap = (TKFrame.TKDictionary<TUserID, ClubExtraUserInfoForClient>)JceUtil.DeepClone(this.extraInfoMap);
            copied.err = this.err;
            copied.clubName = this.clubName;
            copied.currentMemberNum = this.currentMemberNum;
            copied.onlineMemberNum = this.onlineMemberNum;
            return copied;
        }
    }
}

