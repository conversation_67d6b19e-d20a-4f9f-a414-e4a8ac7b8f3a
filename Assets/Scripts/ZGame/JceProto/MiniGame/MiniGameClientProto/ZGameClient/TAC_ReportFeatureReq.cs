// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TAC_ReportFeatureReq : Wup.Jce.JceStruct
    {
        int _iFeatureId = 0;
        public int iFeatureId
        {
            get
            {
                 return _iFeatureId;
            }
            set
            {
                _iFeatureId = value; 
            }
        }

        int _iFeatureType = 0;
        public int iFeatureType
        {
            get
            {
                 return _iFeatureType;
            }
            set
            {
                _iFeatureType = value; 
            }
        }

        int _iFeatureOpType = 0;
        public int iFeatureOpType
        {
            get
            {
                 return _iFeatureOpType;
            }
            set
            {
                _iFeatureOpType = value; 
            }
        }

        int _iClientVer = 0;
        public int iClientVer
        {
            get
            {
                 return _iClientVer;
            }
            set
            {
                _iClientVer = value; 
            }
        }

        string _sParam = "";
        public string sParam
        {
            get
            {
                 return _sParam;
            }
            set
            {
                _sParam = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iFeatureId, 0);
            _os.Write(iFeatureType, 1);
            _os.Write(iFeatureOpType, 2);
            _os.Write(iClientVer, 3);
            _os.Write(sParam, 4);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iFeatureId = (int) _is.Read(iFeatureId, 0, false);

            iFeatureType = (int) _is.Read(iFeatureType, 1, false);

            iFeatureOpType = (int) _is.Read(iFeatureOpType, 2, false);

            iClientVer = (int) _is.Read(iClientVer, 3, false);

            sParam = (string) _is.Read(sParam, 4, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iFeatureId, "iFeatureId");
            _ds.Display(iFeatureType, "iFeatureType");
            _ds.Display(iFeatureOpType, "iFeatureOpType");
            _ds.Display(iClientVer, "iClientVer");
            _ds.Display(sParam, "sParam");
        }

        public override void Clear()
        {
            iFeatureId = 0;
            iFeatureType = 0;
            iFeatureOpType = 0;
            iClientVer = 0;
            sParam = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TAC_ReportFeatureReq();
            copied.iFeatureId = this.iFeatureId;
            copied.iFeatureType = this.iFeatureType;
            copied.iFeatureOpType = this.iFeatureOpType;
            copied.iClientVer = this.iClientVer;
            copied.sParam = this.sParam;
            return copied;
        }
    }
}

