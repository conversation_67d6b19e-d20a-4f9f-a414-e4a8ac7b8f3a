// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetBPHistoryRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public TKFrame.TKDictionary<int, TBPHistoryData> mapHistory;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(mapHistory, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            mapHistory = (TKFrame.TKDictionary<int, TBPHistoryData>) _is.Read(mapHistory, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(mapHistory, "mapHistory");
        }

        public override void Clear()
        {
            iRet = 0;
            mapHistory = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetBPHistoryRsp();
            copied.iRet = this.iRet;
            copied.mapHistory = (TKFrame.TKDictionary<int, TBPHistoryData>)JceUtil.DeepClone(this.mapHistory);
            return copied;
        }
    }
}

