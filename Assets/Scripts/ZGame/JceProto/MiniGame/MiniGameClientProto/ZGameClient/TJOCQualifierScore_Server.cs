// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TJOCQualifierScore_Server : Wup.Jce.JceStruct
    {
        public int iIndex = 0;

        public int iSceneType = 0;

        public int iScore1 = 0;

        public int iScore2 = 0;

        public int iScore3 = 0;

        public int iScore4 = 0;

        public int iScore5 = 0;

        public int iScore6 = 0;

        public int iScore7 = 0;

        public int iScore8 = 0;

        public int iJOCModuleID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iIndex, 0);
            _os.Write(iSceneType, 1);
            _os.Write(iScore1, 2);
            _os.Write(iScore2, 3);
            _os.Write(iScore3, 4);
            _os.Write(iScore4, 5);
            _os.Write(iScore5, 6);
            _os.Write(iScore6, 7);
            _os.Write(iScore7, 8);
            _os.Write(iScore8, 9);
            _os.Write(iJOCModuleID, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iIndex = (int) _is.Read(iIndex, 0, false);

            iSceneType = (int) _is.Read(iSceneType, 1, false);

            iScore1 = (int) _is.Read(iScore1, 2, false);

            iScore2 = (int) _is.Read(iScore2, 3, false);

            iScore3 = (int) _is.Read(iScore3, 4, false);

            iScore4 = (int) _is.Read(iScore4, 5, false);

            iScore5 = (int) _is.Read(iScore5, 6, false);

            iScore6 = (int) _is.Read(iScore6, 7, false);

            iScore7 = (int) _is.Read(iScore7, 8, false);

            iScore8 = (int) _is.Read(iScore8, 9, false);

            iJOCModuleID = (int) _is.Read(iJOCModuleID, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iIndex, "iIndex");
            _ds.Display(iSceneType, "iSceneType");
            _ds.Display(iScore1, "iScore1");
            _ds.Display(iScore2, "iScore2");
            _ds.Display(iScore3, "iScore3");
            _ds.Display(iScore4, "iScore4");
            _ds.Display(iScore5, "iScore5");
            _ds.Display(iScore6, "iScore6");
            _ds.Display(iScore7, "iScore7");
            _ds.Display(iScore8, "iScore8");
            _ds.Display(iJOCModuleID, "iJOCModuleID");
        }

        public override void Clear()
        {
            iIndex = 0;
            iSceneType = 0;
            iScore1 = 0;
            iScore2 = 0;
            iScore3 = 0;
            iScore4 = 0;
            iScore5 = 0;
            iScore6 = 0;
            iScore7 = 0;
            iScore8 = 0;
            iJOCModuleID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TJOCQualifierScore_Server();
            copied.iIndex = this.iIndex;
            copied.iSceneType = this.iSceneType;
            copied.iScore1 = this.iScore1;
            copied.iScore2 = this.iScore2;
            copied.iScore3 = this.iScore3;
            copied.iScore4 = this.iScore4;
            copied.iScore5 = this.iScore5;
            copied.iScore6 = this.iScore6;
            copied.iScore7 = this.iScore7;
            copied.iScore8 = this.iScore8;
            copied.iJOCModuleID = this.iJOCModuleID;
            return copied;
        }
    }
}

