// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TDualPlaySwitchSeatRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
        }

        public override void Clear()
        {
            iRet = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TDualPlaySwitchSeatRsp();
            copied.iRet = this.iRet;
            return copied;
        }
    }
}

