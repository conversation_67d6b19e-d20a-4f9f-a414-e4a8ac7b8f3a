// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TRewardInJTechnicalChest : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iIsMythicalItem = 0;

        public int iCount = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iIsMythicalItem, 1);
            _os.Write(iCount, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iIsMythicalItem = (int) _is.Read(iIsMythicalItem, 1, false);

            iCount = (int) _is.Read(iCount, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iIsMythicalItem, "iIsMythicalItem");
            _ds.Display(iCount, "iCount");
        }

        public override void Clear()
        {
            iID = 0;
            iIsMythicalItem = 0;
            iCount = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TRewardInJTechnicalChest();
            copied.iID = this.iID;
            copied.iIsMythicalItem = this.iIsMythicalItem;
            copied.iCount = this.iCount;
            return copied;
        }
    }
}

