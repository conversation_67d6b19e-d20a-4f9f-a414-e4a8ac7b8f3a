// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GuildFeedsComm : Wup.Jce.JceStruct
    {
        public int type = 0;

        public TUserID uid;

        public bool isManualShare = false;

        public int chatBubbleID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(type, 1);
            _os.Write(uid, 2);
            _os.Write(isManualShare, 8);
            _os.Write(chatBubbleID, 9);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            type = (int) _is.Read(type, 1, false);

            uid = (TUserID) _is.Read(uid, 2, false);

            isManualShare = (bool) _is.Read(isManualShare, 8, false);

            chatBubbleID = (int) _is.Read(chatBubbleID, 9, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(type, "type");
            _ds.Display(uid, "uid");
            _ds.Display(isManualShare, "isManualShare");
            _ds.Display(chatBubbleID, "chatBubbleID");
        }

        public override void Clear()
        {
            type = 0;
            uid = null;
            isManualShare = false;
            chatBubbleID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GuildFeedsComm();
            copied.type = this.type;
            copied.uid = (TUserID)JceUtil.DeepClone(this.uid);
            copied.isManualShare = this.isManualShare;
            copied.chatBubbleID = this.chatBubbleID;
            return copied;
        }
    }
}

