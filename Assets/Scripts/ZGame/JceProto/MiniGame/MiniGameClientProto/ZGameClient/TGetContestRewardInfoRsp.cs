// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetContestRewardInfoRsp : Wup.Jce.JceStruct
    {
        int _iRet = 0;
        public int iRet
        {
            get
            {
                 return _iRet;
            }
            set
            {
                _iRet = value; 
            }
        }

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>> vecSingleRankReward {get; set;} 

        public TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>> vecPassMissionReward {get; set;} 

        public System.Collections.Generic.List<TAC_TItemInfo> vecPassAllMissionOnceTimeReward {get; set;} 

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(vecSingleRankReward, 1);
            _os.Write(vecPassMissionReward, 2);
            _os.Write(vecPassAllMissionOnceTimeReward, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            vecSingleRankReward = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>) _is.Read(vecSingleRankReward, 1, false);

            vecPassMissionReward = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>) _is.Read(vecPassMissionReward, 2, false);

            vecPassAllMissionOnceTimeReward = (System.Collections.Generic.List<TAC_TItemInfo>) _is.Read(vecPassAllMissionOnceTimeReward, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(vecSingleRankReward, "vecSingleRankReward");
            _ds.Display(vecPassMissionReward, "vecPassMissionReward");
            _ds.Display(vecPassAllMissionOnceTimeReward, "vecPassAllMissionOnceTimeReward");
        }

        public override void Clear()
        {
            iRet = 0;
            vecSingleRankReward = null;
            vecPassMissionReward = null;
            vecPassAllMissionOnceTimeReward = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetContestRewardInfoRsp();
            copied.iRet = this.iRet;
            copied.vecSingleRankReward = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>)JceUtil.DeepClone(this.vecSingleRankReward);
            copied.vecPassMissionReward = (TKFrame.TKDictionary<int, System.Collections.Generic.List<TAC_TItemInfo>>)JceUtil.DeepClone(this.vecPassMissionReward);
            copied.vecPassAllMissionOnceTimeReward = (System.Collections.Generic.List<TAC_TItemInfo>)JceUtil.DeepClone(this.vecPassAllMissionOnceTimeReward);
            return copied;
        }
    }
}

