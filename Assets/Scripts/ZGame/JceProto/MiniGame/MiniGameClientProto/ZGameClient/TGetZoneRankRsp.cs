// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TGetZoneRankRsp : Wup.Jce.JceStruct
    {
        public int iRankID = 0;

        public int iPageIndex = 0;

        public int iRankLogicID = 0;

        public int iTotalCount = 0;

        public int iResult = 0;

        public System.Collections.Generic.List<TZoneRankItem> vecItems;

        public TContestZoneRankParam contestZoneRankParam;

        public bool isApexTierBalancing = false;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRankID, 0);
            _os.Write(iPageIndex, 1);
            _os.Write(iRankLogicID, 2);
            _os.Write(iTotalCount, 3);
            _os.Write(iResult, 4);
            _os.Write(vecItems, 5);
            _os.Write(contestZoneRankParam, 6);
            _os.Write(isApexTierBalancing, 7);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRankID = (int) _is.Read(iRankID, 0, false);

            iPageIndex = (int) _is.Read(iPageIndex, 1, false);

            iRankLogicID = (int) _is.Read(iRankLogicID, 2, false);

            iTotalCount = (int) _is.Read(iTotalCount, 3, false);

            iResult = (int) _is.Read(iResult, 4, false);

            vecItems = (System.Collections.Generic.List<TZoneRankItem>) _is.Read(vecItems, 5, false);

            contestZoneRankParam = (TContestZoneRankParam) _is.Read(contestZoneRankParam, 6, false);

            isApexTierBalancing = (bool) _is.Read(isApexTierBalancing, 7, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRankID, "iRankID");
            _ds.Display(iPageIndex, "iPageIndex");
            _ds.Display(iRankLogicID, "iRankLogicID");
            _ds.Display(iTotalCount, "iTotalCount");
            _ds.Display(iResult, "iResult");
            _ds.Display(vecItems, "vecItems");
            _ds.Display(contestZoneRankParam, "contestZoneRankParam");
            _ds.Display(isApexTierBalancing, "isApexTierBalancing");
        }

        public override void Clear()
        {
            iRankID = 0;
            iPageIndex = 0;
            iRankLogicID = 0;
            iTotalCount = 0;
            iResult = 0;
            vecItems = null;
            contestZoneRankParam = null;
            isApexTierBalancing = false;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TGetZoneRankRsp();
            copied.iRankID = this.iRankID;
            copied.iPageIndex = this.iPageIndex;
            copied.iRankLogicID = this.iRankLogicID;
            copied.iTotalCount = this.iTotalCount;
            copied.iResult = this.iResult;
            copied.vecItems = (System.Collections.Generic.List<TZoneRankItem>)JceUtil.DeepClone(this.vecItems);
            copied.contestZoneRankParam = (TContestZoneRankParam)JceUtil.DeepClone(this.contestZoneRankParam);
            copied.isApexTierBalancing = this.isApexTierBalancing;
            return copied;
        }
    }
}

