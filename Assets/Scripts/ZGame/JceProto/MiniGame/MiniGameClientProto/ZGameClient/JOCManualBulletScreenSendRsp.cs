// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class JOCManualBulletScreenSendRsp : Wup.Jce.JceStruct
    {
        public int ret = 0;

        public int bulletScreenID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(ret, 0);
            _os.Write(bulletScreenID, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            ret = (int) _is.Read(ret, 0, false);

            bulletScreenID = (int) _is.Read(bulletScreenID, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(ret, "ret");
            _ds.Display(bulletScreenID, "bulletScreenID");
        }

        public override void Clear()
        {
            ret = 0;
            bulletScreenID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new JOCManualBulletScreenSendRsp();
            copied.ret = this.ret;
            copied.bulletScreenID = this.bulletScreenID;
            return copied;
        }
    }
}

