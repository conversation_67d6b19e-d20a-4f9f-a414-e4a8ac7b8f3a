//所在的Excel 【ACG_Jump.xlsm】
//**********************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_ItemGetType_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public string sTitle = "";

        public string sDesc = "";

        public string sJumpParam = "";

        public string sExpireDate = "";

        public int iExpireDateSecs = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sTitle, 1);
            _os.Write(sDesc, 2);
            _os.Write(sJumpParam, 3);
            _os.Write(sExpireDate, 4);
            _os.Write(iExpireDateSecs, 5);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sTitle = (string) _is.Read(sTitle, 1, false);

            sDesc = (string) _is.Read(sDesc, 2, false);

            sJumpParam = (string) _is.Read(sJumpParam, 3, false);

            sExpireDate = (string) _is.Read(sExpireDate, 4, false);

            iExpireDateSecs = (int) _is.Read(iExpireDateSecs, 5, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sTitle, "sTitle");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sJumpParam, "sJumpParam");
            _ds.Display(sExpireDate, "sExpireDate");
            _ds.Display(iExpireDateSecs, "iExpireDateSecs");
        }

        public override void Clear()
        {
            iID = 0;
            sTitle = "";
            sDesc = "";
            sJumpParam = "";
            sExpireDate = "";
            iExpireDateSecs = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_ItemGetType_Client();
            copied.iID = this.iID;
            copied.sTitle = this.sTitle;
            copied.sDesc = this.sDesc;
            copied.sJumpParam = this.sJumpParam;
            copied.sExpireDate = this.sExpireDate;
            copied.iExpireDateSecs = this.iExpireDateSecs;
            return copied;
        }
    }
}

