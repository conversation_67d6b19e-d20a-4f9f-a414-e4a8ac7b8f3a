// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class GiftCenterDelGiveGiftRecordReq : Wup.Jce.JceStruct
    {
        public long lRecordID = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(lRecordID, 0);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            lRecordID = (long) _is.Read(lRecordID, 0, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(lRecordID, "lRecordID");
        }

        public override void Clear()
        {
            lRecordID = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new GiftCenterDelGiveGiftRecordReq();
            copied.lRecordID = this.lRecordID;
            return copied;
        }
    }
}

