//所在的Excel 【TAC_NewItem.xlsm】
//*******************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_NewItem_Client : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        int _iType = 0;
        public int iType
        {
            get
            {
                 return _iType;
            }
            set
            {
                _iType = value; 
            }
        }

        int _iChildType = 0;
        public int iChildType
        {
            get
            {
                 return _iChildType;
            }
            set
            {
                _iChildType = value; 
            }
        }

        int _iLevel = 0;
        public int iLevel
        {
            get
            {
                 return _iLevel;
            }
            set
            {
                _iLevel = value; 
            }
        }

        int _iLimitUseLevel = 0;
        public int iLimitUseLevel
        {
            get
            {
                 return _iLimitUseLevel;
            }
            set
            {
                _iLimitUseLevel = value; 
            }
        }

        int _iMaxNum = 0;
        public int iMaxNum
        {
            get
            {
                 return _iMaxNum;
            }
            set
            {
                _iMaxNum = value; 
            }
        }

        int _iIsCanUse = 0;
        public int iIsCanUse
        {
            get
            {
                 return _iIsCanUse;
            }
            set
            {
                _iIsCanUse = value; 
            }
        }

        int _iIsCanSell = 0;
        public int iIsCanSell
        {
            get
            {
                 return _iIsCanSell;
            }
            set
            {
                _iIsCanSell = value; 
            }
        }

        int _iSellPrice = 0;
        public int iSellPrice
        {
            get
            {
                 return _iSellPrice;
            }
            set
            {
                _iSellPrice = value; 
            }
        }

        int _iUnuse = 0;
        public int iUnuse
        {
            get
            {
                 return _iUnuse;
            }
            set
            {
                _iUnuse = value; 
            }
        }

        string _sTypeName = "";
        public string sTypeName
        {
            get
            {
                 return _sTypeName;
            }
            set
            {
                _sTypeName = value; 
            }
        }

        string _sDesc = "";
        public string sDesc
        {
            get
            {
                 return _sDesc;
            }
            set
            {
                _sDesc = value; 
            }
        }

        string _sIcon = "";
        public string sIcon
        {
            get
            {
                 return _sIcon;
            }
            set
            {
                _sIcon = value; 
            }
        }

        int _iFirstLevelTab = 0;
        public int iFirstLevelTab
        {
            get
            {
                 return _iFirstLevelTab;
            }
            set
            {
                _iFirstLevelTab = value; 
            }
        }

        int _iSecondLevelTab = 0;
        public int iSecondLevelTab
        {
            get
            {
                 return _iSecondLevelTab;
            }
            set
            {
                _iSecondLevelTab = value; 
            }
        }

        int _iSortWeight = 0;
        public int iSortWeight
        {
            get
            {
                 return _iSortWeight;
            }
            set
            {
                _iSortWeight = value; 
            }
        }

        int _iStayDayNum = 0;
        public int iStayDayNum
        {
            get
            {
                 return _iStayDayNum;
            }
            set
            {
                _iStayDayNum = value; 
            }
        }

        string _sOverTime = "";
        public string sOverTime
        {
            get
            {
                 return _sOverTime;
            }
            set
            {
                _sOverTime = value; 
            }
        }

        int _iEffectType = 0;
        public int iEffectType
        {
            get
            {
                 return _iEffectType;
            }
            set
            {
                _iEffectType = value; 
            }
        }

        int _iEffectP1 = 0;
        public int iEffectP1
        {
            get
            {
                 return _iEffectP1;
            }
            set
            {
                _iEffectP1 = value; 
            }
        }

        int _iEffectP2 = 0;
        public int iEffectP2
        {
            get
            {
                 return _iEffectP2;
            }
            set
            {
                _iEffectP2 = value; 
            }
        }

        int _iEffectP3 = 0;
        public int iEffectP3
        {
            get
            {
                 return _iEffectP3;
            }
            set
            {
                _iEffectP3 = value; 
            }
        }

        string _sItemName = "";
        public string sItemName
        {
            get
            {
                 return _sItemName;
            }
            set
            {
                _sItemName = value; 
            }
        }

        string _sSmallIcon = "";
        public string sSmallIcon
        {
            get
            {
                 return _sSmallIcon;
            }
            set
            {
                _sSmallIcon = value; 
            }
        }

        string _sBigIcon = "";
        public string sBigIcon
        {
            get
            {
                 return _sBigIcon;
            }
            set
            {
                _sBigIcon = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iType, 1);
            _os.Write(iChildType, 2);
            _os.Write(iLevel, 3);
            _os.Write(iLimitUseLevel, 4);
            _os.Write(iMaxNum, 5);
            _os.Write(iIsCanUse, 6);
            _os.Write(iIsCanSell, 7);
            _os.Write(iSellPrice, 8);
            _os.Write(iUnuse, 9);
            _os.Write(sTypeName, 10);
            _os.Write(sDesc, 11);
            _os.Write(sIcon, 12);
            _os.Write(iFirstLevelTab, 13);
            _os.Write(iSecondLevelTab, 14);
            _os.Write(iSortWeight, 15);
            _os.Write(iStayDayNum, 16);
            _os.Write(sOverTime, 17);
            _os.Write(iEffectType, 18);
            _os.Write(iEffectP1, 19);
            _os.Write(iEffectP2, 20);
            _os.Write(iEffectP3, 21);
            _os.Write(sItemName, 22);
            _os.Write(sSmallIcon, 23);
            _os.Write(sBigIcon, 24);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iType = (int) _is.Read(iType, 1, false);

            iChildType = (int) _is.Read(iChildType, 2, false);

            iLevel = (int) _is.Read(iLevel, 3, false);

            iLimitUseLevel = (int) _is.Read(iLimitUseLevel, 4, false);

            iMaxNum = (int) _is.Read(iMaxNum, 5, false);

            iIsCanUse = (int) _is.Read(iIsCanUse, 6, false);

            iIsCanSell = (int) _is.Read(iIsCanSell, 7, false);

            iSellPrice = (int) _is.Read(iSellPrice, 8, false);

            iUnuse = (int) _is.Read(iUnuse, 9, false);

            sTypeName = (string) _is.Read(sTypeName, 10, false);

            sDesc = (string) _is.Read(sDesc, 11, false);

            sIcon = (string) _is.Read(sIcon, 12, false);

            iFirstLevelTab = (int) _is.Read(iFirstLevelTab, 13, false);

            iSecondLevelTab = (int) _is.Read(iSecondLevelTab, 14, false);

            iSortWeight = (int) _is.Read(iSortWeight, 15, false);

            iStayDayNum = (int) _is.Read(iStayDayNum, 16, false);

            sOverTime = (string) _is.Read(sOverTime, 17, false);

            iEffectType = (int) _is.Read(iEffectType, 18, false);

            iEffectP1 = (int) _is.Read(iEffectP1, 19, false);

            iEffectP2 = (int) _is.Read(iEffectP2, 20, false);

            iEffectP3 = (int) _is.Read(iEffectP3, 21, false);

            sItemName = (string) _is.Read(sItemName, 22, false);

            sSmallIcon = (string) _is.Read(sSmallIcon, 23, false);

            sBigIcon = (string) _is.Read(sBigIcon, 24, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iType, "iType");
            _ds.Display(iChildType, "iChildType");
            _ds.Display(iLevel, "iLevel");
            _ds.Display(iLimitUseLevel, "iLimitUseLevel");
            _ds.Display(iMaxNum, "iMaxNum");
            _ds.Display(iIsCanUse, "iIsCanUse");
            _ds.Display(iIsCanSell, "iIsCanSell");
            _ds.Display(iSellPrice, "iSellPrice");
            _ds.Display(iUnuse, "iUnuse");
            _ds.Display(sTypeName, "sTypeName");
            _ds.Display(sDesc, "sDesc");
            _ds.Display(sIcon, "sIcon");
            _ds.Display(iFirstLevelTab, "iFirstLevelTab");
            _ds.Display(iSecondLevelTab, "iSecondLevelTab");
            _ds.Display(iSortWeight, "iSortWeight");
            _ds.Display(iStayDayNum, "iStayDayNum");
            _ds.Display(sOverTime, "sOverTime");
            _ds.Display(iEffectType, "iEffectType");
            _ds.Display(iEffectP1, "iEffectP1");
            _ds.Display(iEffectP2, "iEffectP2");
            _ds.Display(iEffectP3, "iEffectP3");
            _ds.Display(sItemName, "sItemName");
            _ds.Display(sSmallIcon, "sSmallIcon");
            _ds.Display(sBigIcon, "sBigIcon");
        }

        public override void Clear()
        {
            iID = 0;
            iType = 0;
            iChildType = 0;
            iLevel = 0;
            iLimitUseLevel = 0;
            iMaxNum = 0;
            iIsCanUse = 0;
            iIsCanSell = 0;
            iSellPrice = 0;
            iUnuse = 0;
            sTypeName = "";
            sDesc = "";
            sIcon = "";
            iFirstLevelTab = 0;
            iSecondLevelTab = 0;
            iSortWeight = 0;
            iStayDayNum = 0;
            sOverTime = "";
            iEffectType = 0;
            iEffectP1 = 0;
            iEffectP2 = 0;
            iEffectP3 = 0;
            sItemName = "";
            sSmallIcon = "";
            sBigIcon = "";
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TTAC_NewItem_Client();
            copied.iID = this.iID;
            copied.iType = this.iType;
            copied.iChildType = this.iChildType;
            copied.iLevel = this.iLevel;
            copied.iLimitUseLevel = this.iLimitUseLevel;
            copied.iMaxNum = this.iMaxNum;
            copied.iIsCanUse = this.iIsCanUse;
            copied.iIsCanSell = this.iIsCanSell;
            copied.iSellPrice = this.iSellPrice;
            copied.iUnuse = this.iUnuse;
            copied.sTypeName = this.sTypeName;
            copied.sDesc = this.sDesc;
            copied.sIcon = this.sIcon;
            copied.iFirstLevelTab = this.iFirstLevelTab;
            copied.iSecondLevelTab = this.iSecondLevelTab;
            copied.iSortWeight = this.iSortWeight;
            copied.iStayDayNum = this.iStayDayNum;
            copied.sOverTime = this.sOverTime;
            copied.iEffectType = this.iEffectType;
            copied.iEffectP1 = this.iEffectP1;
            copied.iEffectP2 = this.iEffectP2;
            copied.iEffectP3 = this.iEffectP3;
            copied.sItemName = this.sItemName;
            copied.sSmallIcon = this.sSmallIcon;
            copied.sBigIcon = this.sBigIcon;
            return copied;
        }
    }
}

