// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TMallReqJTechnicalChestInfoRsp : Wup.Jce.JceStruct
    {
        public int iRet = 0;

        public TKFrame.TKDictionary<int, TJTechnicalChestInfo> mapChest;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iRet, 0);
            _os.Write(mapChest, 1);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iRet = (int) _is.Read(iRet, 0, false);

            mapChest = (TKFrame.TKDictionary<int, TJTechnicalChestInfo>) _is.Read(mapChest, 1, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iRet, "iRet");
            _ds.Display(mapChest, "mapChest");
        }

        public override void Clear()
        {
            iRet = 0;
            mapChest = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TMallReqJTechnicalChestInfoRsp();
            copied.iRet = this.iRet;
            copied.mapChest = (TKFrame.TKDictionary<int, TJTechnicalChestInfo>)JceUtil.DeepClone(this.mapChest);
            return copied;
        }
    }
}

