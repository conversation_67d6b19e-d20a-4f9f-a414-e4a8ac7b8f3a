// **********************************************************************
// This file was generated by a TAF parser!
// Generated from `SGameDBConfigProto.jce'
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TTAC_SignInNew_Server : Wup.Jce.JceStruct
    {
        int _iID = 0;
        public int iID
        {
            get
            {
                 return _iID;
            }
            set
            {
                _iID = value; 
            }
        }

        string _sActivityMenu = "";
        public string sActivityMenu
        {
            get
            {
                 return _sActivityMenu;
            }
            set
            {
                _sActivityMenu = value; 
            }
        }

        string _sStartTime = "";
        public string sStartTime
        {
            get
            {
                 return _sStartTime;
            }
            set
            {
                _sStartTime = value; 
            }
        }

        string _sFinalTime = "";
        public string sFinalTime
        {
            get
            {
                 return _sFinalTime;
            }
            set
            {
                _sFinalTime = value; 
            }
        }

        string _sDayIndex = "";
        public string sDayIndex
        {
            get
            {
                 return _sDayIndex;
            }
            set
            {
                _sDayIndex = value; 
            }
        }

        string _sRewardItemID_0 = "";
        public string sRewardItemID_0
        {
            get
            {
                 return _sRewardItemID_0;
            }
            set
            {
                _sRewardItemID_0 = value; 
            }
        }

        string _sRewardItemCount_0 = "";
        public string sRewardItemCount_0
        {
            get
            {
                 return _sRewardItemCount_0;
            }
            set
            {
                _sRewardItemCount_0 = value; 
            }
        }

        string _sRewardItemID_1 = "";
        public string sRewardItemID_1
        {
            get
            {
                 return _sRewardItemID_1;
            }
            set
            {
                _sRewardItemID_1 = value; 
            }
        }

        string _sRewardItemCount_1 = "";
        public string sRewardItemCount_1
        {
            get
            {
                 return _sRewardItemCount_1;
            }
            set
            {
                _sRewardItemCount_1 = value; 
            }
        }

        string _sRewardItemID_2 = "";
        public string sRewardItemID_2
        {
            get
            {
                 return _sRewardItemID_2;
            }
            set
            {
                _sRewardItemID_2 = value; 
            }
        }

        string _sRewardItemCount_2 = "";
        public string sRewardItemCount_2
        {
            get
            {
                 return _sRewardItemCount_2;
            }
            set
            {
                _sRewardItemCount_2 = value; 
            }
        }

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(sActivityMenu, 1);
            _os.Write(sStartTime, 2);
            _os.Write(sFinalTime, 3);
            _os.Write(sDayIndex, 4);
            _os.Write(sRewardItemID_0, 5);
            _os.Write(sRewardItemCount_0, 6);
            _os.Write(sRewardItemID_1, 7);
            _os.Write(sRewardItemCount_1, 8);
            _os.Write(sRewardItemID_2, 9);
            _os.Write(sRewardItemCount_2, 10);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            sActivityMenu = (string) _is.Read(sActivityMenu, 1, false);

            sStartTime = (string) _is.Read(sStartTime, 2, false);

            sFinalTime = (string) _is.Read(sFinalTime, 3, false);

            sDayIndex = (string) _is.Read(sDayIndex, 4, false);

            sRewardItemID_0 = (string) _is.Read(sRewardItemID_0, 5, false);

            sRewardItemCount_0 = (string) _is.Read(sRewardItemCount_0, 6, false);

            sRewardItemID_1 = (string) _is.Read(sRewardItemID_1, 7, false);

            sRewardItemCount_1 = (string) _is.Read(sRewardItemCount_1, 8, false);

            sRewardItemID_2 = (string) _is.Read(sRewardItemID_2, 9, false);

            sRewardItemCount_2 = (string) _is.Read(sRewardItemCount_2, 10, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(sActivityMenu, "sActivityMenu");
            _ds.Display(sStartTime, "sStartTime");
            _ds.Display(sFinalTime, "sFinalTime");
            _ds.Display(sDayIndex, "sDayIndex");
            _ds.Display(sRewardItemID_0, "sRewardItemID_0");
            _ds.Display(sRewardItemCount_0, "sRewardItemCount_0");
            _ds.Display(sRewardItemID_1, "sRewardItemID_1");
            _ds.Display(sRewardItemCount_1, "sRewardItemCount_1");
            _ds.Display(sRewardItemID_2, "sRewardItemID_2");
            _ds.Display(sRewardItemCount_2, "sRewardItemCount_2");
        }

    }
}

