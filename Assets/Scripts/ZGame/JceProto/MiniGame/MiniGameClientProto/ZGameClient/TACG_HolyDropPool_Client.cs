//所在的Excel 【ACG_HundredReward.xlsm】
//*************************
// This file was generated by a TARS parser!
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class TACG_HolyDropPool_Client : Wup.Jce.JceStruct
    {
        public int iID = 0;

        public int iPoolId = 0;

        public string sDropParams = "";

        public int iWeight = 0;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(iID, 0);
            _os.Write(iPoolId, 1);
            _os.Write(sDropParams, 2);
            _os.Write(iWeight, 3);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            iID = (int) _is.Read(iID, 0, false);

            iPoolId = (int) _is.Read(iPoolId, 1, false);

            sDropParams = (string) _is.Read(sDropParams, 2, false);

            iWeight = (int) _is.Read(iWeight, 3, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(iID, "iID");
            _ds.Display(iPoolId, "iPoolId");
            _ds.Display(sDropParams, "sDropParams");
            _ds.Display(iWeight, "iWeight");
        }

        public override void Clear()
        {
            iID = 0;
            iPoolId = 0;
            sDropParams = "";
            iWeight = 0;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new TACG_HolyDropPool_Client();
            copied.iID = this.iID;
            copied.iPoolId = this.iPoolId;
            copied.sDropParams = this.sDropParams;
            copied.iWeight = this.iWeight;
            return copied;
        }
    }
}

