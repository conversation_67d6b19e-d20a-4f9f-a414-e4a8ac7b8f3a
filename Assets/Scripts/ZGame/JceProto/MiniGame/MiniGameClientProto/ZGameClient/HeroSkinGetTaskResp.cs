// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.1.0.
// **********************************************************************

using System;
namespace ZGameClient
{

    public sealed class HeroSkinGetTaskResp : Wup.Jce.JceStruct
    {
        public int err = 0;

        public System.Collections.Generic.List<ACG_TaskInfo> tasks;

        public System.Collections.Generic.List<THeroSkinCollectTask_Server> taskConfigs;

        public override void WriteTo(Wup.Jce.JceOutputStream _os)
        {
            _os.Write(err, 0);
            _os.Write(tasks, 1);
            _os.Write(taskConfigs, 2);
        }

        public override void ReadFrom(Wup.Jce.JceInputStream _is)
        {
            err = (int) _is.Read(err, 0, false);

            tasks = (System.Collections.Generic.List<ACG_TaskInfo>) _is.Read(tasks, 1, false);

            taskConfigs = (System.Collections.Generic.List<THeroSkinCollectTask_Server>) _is.Read(taskConfigs, 2, false);

        }

        public override void Display(System.Text.StringBuilder _os, int _level)
        {
            Wup.Jce.JceDisplayer _ds = new Wup.Jce.JceDisplayer(_os, _level);
            _ds.Display(err, "err");
            _ds.Display(tasks, "tasks");
            _ds.Display(taskConfigs, "taskConfigs");
        }

        public override void Clear()
        {
            err = 0;
            tasks = null;
            taskConfigs = null;
        }

        public override Wup.Jce.JceStruct DeepClone()
        {
            var copied = new HeroSkinGetTaskResp();
            copied.err = this.err;
            copied.tasks = (System.Collections.Generic.List<ACG_TaskInfo>)JceUtil.DeepClone(this.tasks);
            copied.taskConfigs = (System.Collections.Generic.List<THeroSkinCollectTask_Server>)JceUtil.DeepClone(this.taskConfigs);
            return copied;
        }
    }
}

