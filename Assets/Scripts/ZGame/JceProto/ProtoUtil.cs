using System.Collections;
using System.Collections.Generic;
using Wup.Jce;
using System.Text;
using MiniGameHttpProto;
using ZGameClient;
using System.IO;
using TKFrame;
using ZGame;
using UnityEngine;
using System;
#if ACGGAME_CLIENT
using TKPlugins;
#endif


namespace MiniGameClientProto
{
    public enum FireMsgType { T_PKG, HTTP_PKG }

    public class FireMsgData
    {
        public int msgId;
        public byte[] buffer;
        public FireMsgType msgType;

        public FireMsgData(int id, byte[] data, FireMsgType type)
        {
            msgId = id;
            msgType = type;
            buffer = new byte[data.Length];
            data.CopyTo(buffer, 0);
        }
    }


    public class ProtoUtil
    {
        private static long uUin = 0;
        private static System.Random random = new System.Random();
        private static int m_iSequence = random.Next(0, 999999);
        public static int iSequence
        {
            get { return m_iSequence; }
            private set { m_iSequence = value; }
        }
        private static int m_gameId;

//        /// <summary>
//        /// 手机的IMEI
//        /// 已废弃
//        /// 国家规定不再允许收集IMEI
//        /// </summary>
//        /// 
//#if ACGGAME_CLIENT
//        private static string _DeviceIMEI = "";
//        public static string DeviceIMEI
//        {
//            get
//            {
//                if(string.IsNullOrEmpty(_DeviceIMEI))
//                {
//                    _DeviceIMEI = TKPlatform.GetDeviceIMEI();
//                }
//                return _DeviceIMEI;
//            }
//        }
//#endif

        //测试分区ID
        public static int iTestZoneID = 1000;
        //体验分区ID
        public static int iExpZoneID = 2000;
        //正式环境和预发布环境分区ID都用1000
        public static int iReleaseZoneID = 1000;
        //默认分区ID
        private static int iZoneID = 12000;

        //midas的支付分区
        public static int iMidasZoneID;

        //房间ID
        public static int iRoomID = 0;
        //桌子ID
        public static int iTableID = 0;
        //座位ID
        public static int iSeatID = 0;
        //坐下模式
        public static int iMode = (short)SITDOWN_MODE.SITMODE_NOMAL;
        //游戏模式
        public static short iGameType = (short)E_GAME_TYPE.E_GAME_TYPE_PVP;

        public static bool beNeedPrintMsgLog = true;

        public static bool isAuto_Chess = true;

        /// <summary>
        /// iOS评审期开关
        /// </summary>
        public static bool iOSBlock = false;

        //玩家登录帐号ID（微号/QQ），不是唯一的
        public static long uin
        {
            set
            {
                uUin = value;
                uid = ""; //清空唯一UID串

                //crashsight需要主动设置用户id
                if(uUin!=0)
                {                   
                    string uUinStr = uUin.ToString();
#if ACGGAME_CLIENT
                    PlayerPrefs.SetString("CrashSight_Player_UIN",uUinStr);
                    PlayerPrefs.Save();
#endif
                }                                  
            }
            get { return uUin; }
        }

        private static string uid = "";

        //返回玩家游戏账号的唯一UID
        //由zoneid, os_type, uin三个值生成的字符串
#if ACGGAME_CLIENT
        public static string UID
        {
            get
            {
                return uid;
            }
        }

        public static int iMobilePlatType
        {
            get
            {
                return 0;
            }
        }
#endif

        //         public static bool IsPreview
        //         {
        //             //是否是体验环境标志
        //             get{
        // #if ACGGAME_CLIENT
        // 		        return VersionInfo.IsPreview;
        // #endif
        //                 return false;
        //             }
        //         }

        // public static int gameId
        // {
        //     set { m_gameId = value; }
        // }

        //前后端约定的通信协议密钥
        public static byte[] encryptKey;

        public static string signData;


        public static int GetZoneID()
        {
#if ACGGAME_CLIENT
            return 0;
#elif ACGGAME_AUTOFIGHT
            return QQGameSystem.GetInstance().login_zone_id;
#else
            return iReleaseZoneID;
#endif
            //if (VersionInfo.BuildMode != "debug")
            //{
            //    return iReleaseZoneID;
            //}
            //else
            //{
            //    //如果没有缓存，就读取代码中预设分区
            //    if (Setting.ServerEnv == (int)Build._ENV._PRE_ENV)
            //    {
            //        return Setting.iZoneID;
            //    }
            //    else
            //    {
            //        if (Setting.iZoneID > 0)
            //        {
            //            return Setting.iZoneID;
            //        }

            //        return iZoneID;
            //    }
            //}
        }

        public static string GetZoneName()
        {
#if ACGGAME_CLIENT
            return "";
#elif ACGGAME_AUTOFIGHT
            return QQGameSystem.GetInstance().login_zone_id.ToString();
#else
            return iReleaseZoneID.ToString();
#endif
            //if (VersionInfo.BuildMode != "debug")
            //{
            //    return iReleaseZoneID;
            //}
            //else
            //{
            //    //如果没有缓存，就读取代码中预设分区
            //    if (Setting.ServerEnv == (int)Build._ENV._PRE_ENV)
            //    {
            //        return Setting.iZoneID;
            //    }
            //    else
            //    {
            //        if (Setting.iZoneID > 0)
            //        {
            //            return Setting.iZoneID;
            //        }

            //        return iZoneID;
            //    }
            //}
        }


        public static string GetChanellId()
        {
#if UNITY_ANDROID
            return "10001";
#elif UNITY_IOS
            return "10002";
#else
            return "10001";
#endif
        }
#if ACGGAME_CLIENT
        public static int AreaID = 0;
        public static int GetAreaID()
        {
            if (AreaID > 0)
                return AreaID;

            int areaID = 0;

            // if (VersionInfo.IsDebugVersion)
            // {
            //     //测试服
            //     areaID += 990;
            // }
            // else if(VersionInfo.IsExperVersion)
            // {
            //     //抢先服
            //     areaID += 880;
            // }
            // else if(VersionInfo.IsAuditVersion)
            // {
            //     //版署服
            // }
            // else if(VersionInfo.IsReleaseVersion)
            // {
            //     //正式服
            // }
            // else if(VersionInfo.IsPreRelease)
            // {
            //     //预发布
            //     areaID += 770;
            // }

            return areaID;
        }
#endif

        // public static string GetAppID()
        // {
        //     //体验版AppID
        //     if (IsPreview)
        //         return "1106030923";
        //     //正式版AppID
        //     else
        //         return "1104831336";
        // }
        //
        // public static string GetAppKey()
        // {
        //     //体验版AppKey
        //     if (IsPreview)
        //         return "BSqe4vF9K4jcZXtg";
        //     //正式版AppKey
        //     else
        //         return "YQkd34mC5iXC9iAh";
        // }

        // public static string GetWXAppID()
        // {
        //     //体验版WXAppID
        //     if (IsPreview)
        //         return "wxe3e3e8f447375d31";
        //     //正式版WXAppID
        //     else
        //         return "wx6898f5152427d6e1";
        // }
        //
        // public static string GetWXAppKey()
        // {
        //     //体验版WXAppKey
        //     if (IsPreview)
        //         return "ba1d4e55d155359051d712efc8b449da";
        //     //正式版WXAppKey
        //     else
        //         return "8f916e53c584112dcf98b58365096f93";
        // }
#if ACGGAME_CLIENT
        public static string GetCacheIpPortFilePath()
        {
            {
                return TKApplication.StoragePath + "poll_ok.conf";
            }
        }



        public static List<IpPort> GenerateIpPortByUrl(string url)
        {
            List<IpPort> vecIpPortList = new List<IpPort>();
            if (string.IsNullOrEmpty(url))
            {
                return vecIpPortList;
            }
            FastStringSplit urlArray = url.BeginSplit(' ');
            for (int i = 0; i < urlArray.Length; i++)
            {
                FastStringSplit portArray = urlArray[i].BeginSplit(':');
                for (int j = 1; j < portArray.Length; j++)
                {
                    vecIpPortList.Add(new IpPort() { ip = portArray[0], port = portArray.ParseInt32(j) });
                }
                portArray.EndSplit();
            }
            urlArray.EndSplit();
            return vecIpPortList;
        }
#endif

        private static void ResetMemoryStream(MemoryStream ms)
        {
            ms.Seek(0, SeekOrigin.Begin);
            ms.SetLength(0);
        }

        public static byte[] JceStructToBytes(JceStruct msg)
        {
#if ACGGAME_AUTOFIGHT
            MemoryStream jceMs;
            BinaryWriter jceBw;
            if (msg is THelloReq)
            {
                jceMs = ThreadData.GetJceWriteMemoryStream_th();
                jceBw = ThreadData.GetJceBinaryWriter_th();
            }
            else
            {
                jceMs = ThreadData.GetJceWriteMemoryStream();
                jceBw = ThreadData.GetJceBinaryWriter();
            }
#else
            MemoryStream jceMs = ThreadData.GetJceWriteMemoryStream();
            BinaryWriter jceBw = ThreadData.GetJceBinaryWriter();
#endif

            ResetMemoryStream(jceMs);

            JceOutputStream msgOS = new JceOutputStream(jceMs, jceBw);
            if (msg != null)
                msg.WriteTo(msgOS);
            return msgOS.toByteArray();
        }

        //public static void JceStructToBytes(JceStruct msg, MemoryStream ms, BinaryWriter bw)
        //{
        //    JceOutputStream msgOS = new JceOutputStream(ms, bw);
        //    if (msg != null)
        //        msg.WriteTo(msgOS);
        //}

        public static void BytesToJceStruct(byte[] msgBytes, JceStruct msg)
        {
            // 先获取MemoryStream
            MemoryStream jceMs = ThreadData.GetJceReadMemoryStream();
            ResetMemoryStream(jceMs);

            // 将数据写入MemoryStream
            BinaryWriter jceBw = ThreadData.GetJceReadMemoryStreamWriter();
            jceBw.Write(msgBytes);

            // 获取BinaryReader
            BinaryReader jceBr = ThreadData.GetJceBinaryReader();
            jceMs.Seek(0, SeekOrigin.Begin);

            JceInputStream msgIS = new JceInputStream(jceMs, jceBr);
            msg.ReadFrom(msgIS);
        }

        public static void BytesToJceStruct(MemoryStream ms, JceStruct msg)
        {
            JceInputStream msgIS = new JceInputStream(ms);
            msg.ReadFrom(msgIS);
        }

        public static List<E_SVR_MSG_ID> m_NotPrintSendMsg = new List<E_SVR_MSG_ID>()
        {
            E_SVR_MSG_ID.E_TAC_SVR_MSG_ID_REPORT_STATISTICS_INFO,
            E_SVR_MSG_ID.E_SVR_MSG_ID_TAC_REPORT_BALANCE,
            E_SVR_MSG_ID.E_SVR_MSG_ID_TAC_REPORT_PLAY_STATE
        };

        public static List<int> m_NotPrintRcvMsg = new List<int>()
        {
            (int)E_SVR_MSG_ID.E_SVR_MSG_ID_TAC_REPORT_GAMEOVER,
            (int)E_SVR_MSG_ID.E_TAC_SVR_MSG_ID_CHAT_PUSH_MSG,
            (int)E_SVR_MSG_ID.E_TAC_SVR_MSG_ID_MALL_LIST
        };

        private static THttpPackage ToHttpPackage(byte[] httpContent)
        {
            string httpResponseStr = System.Text.Encoding.ASCII.GetString(httpContent);
            string httpResponseBodyStr = "";
            int _nPos = httpResponseStr.IndexOf("\r\n\r\n");

            if (_nPos > 0)
            {
                httpResponseBodyStr = httpResponseStr.Substring(_nPos + 4);
            }
            if (httpResponseBodyStr.Length <= 0)
            {
                return null;
            }

            byte[] httpBody = System.Text.Encoding.ASCII.GetBytes(httpResponseBodyStr);


            THttpPackage tHttpPackage = new THttpPackage();
            BytesToJceStruct(httpBody, tHttpPackage);
            return tHttpPackage;
        }


        //加密和解密可能是分别在不同的两个线程执行，所以建两个加解密器
        static TxTEA ms_encrypt_tea = new TxTEA();
        static TxTEA ms_decrypt_tea = new TxTEA();


        //不加密的协议消息ID集合
        static HashSet<int> ms_exclude_encrypt_msg_set = new HashSet<int>();

        //设置不加密的协议消息ID集合
        public static void SetExcludeEncryptMsgList(int[] msg_id_list)
        {
            ms_exclude_encrypt_msg_set.Clear();

            if (msg_id_list != null)
            {
                foreach (int id in msg_id_list)
                    ms_exclude_encrypt_msg_set.Add(id);
            }
        }

        static object crypt_lock = new object();
        //通信协议加密
        public static byte[] Encrypt(byte[] data)
        {
            if (encryptKey != null)
            {
#if LOGIC_THREAD
                lock (ACG.Core.NetDriverAdapter.sysLock)
#elif !ACGGAME_CLIENT
                lock (crypt_lock)
#endif
                {
                    return ms_encrypt_tea.encrypt(data, encryptKey);
                }
            }
            return data;
        }

        //通信协议解密
        public static byte[] Decrypt(byte[] data)
        {
            if (encryptKey != null)
            {
#if LOGIC_THREAD
                lock (ACG.Core.NetDriverAdapter.sysLock)
#elif !ACGGAME_CLIENT
                lock (crypt_lock)
#endif
                {
                    return ms_decrypt_tea.decrypt(data, encryptKey);
                }
            }
            return data;
        }


        /*
        public static FireMsgData[] ToFireMsgDatas(TPackage tPackage) 
        {
            FireMsgData[] fireMsgDatas = new FireMsgData[tPackage.vecMsgHead.Count];

            for (int i = 0; i < tPackage.vecMsgHead.Count; i++)
            {
                short msgId = tPackage.vecMsgHead[i].nMsgID;
                if (msgId == (short)MiniGameClientProto.MSGID.MSGID_TRANSMIT_HTTPSVR)
                {
                    fireMsgDatas[i] = TransHttpRspToFireMsgData(tPackage.vecMsgData[i].ToArray());
                }
                else
                {
                    fireMsgDatas[i] = new FireMsgData(msgId, tPackage.vecMsgData[i].ToArray(), FireMsgType.T_PKG);
                }
            }
            return fireMsgDatas;
        }
        */
    }
}

