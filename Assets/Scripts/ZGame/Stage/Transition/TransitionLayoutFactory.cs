using TKFrame.Item;
using ZGameChess;

namespace ZGame.Stage
{
    public class TransitionLayoutFactory
    {
        public enum Chess_TransitionType
        {
            CommonLoad,
        }

        public static PopLayout CreateLayout(Chess_TransitionType type)
        {
            PopLayout layout = null;

            bool blur = false;
            switch (type)
            {
                case Chess_TransitionType.CommonLoad:
                    layout = new TransitionLayout("prefab/ui/chesstransition/chess_transition_common", "chess_transition_common", "", true);
                    break;
            }

            if (layout != null)
            {
                layout.isBlurBackGround = blur;
                layout.IsSystemDialog = true;
                layout.HideWhenNewDialog = false;
                layout.isAlphaMask = false;
            }

            return layout;
        }
    }
}
