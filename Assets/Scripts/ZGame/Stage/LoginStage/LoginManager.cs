
namespace ZGame.Stage
{
    public class LoginManager
    {
        private static LoginManager _Instance = null;
        public static LoginManager Instance
        {
            get
            {
                if (_Instance == null)
                {
                    _Instance = new LoginManager();
                }
                return _Instance;
            }
        }

        public void Clear()
        {
            _Instance = null;
        }

        //是否在游戏中
        private bool _isInGame;
        public bool IsInGame
        {
            get => _isInGame;
            set
            {
                _isInGame = value;
            }
        }
    }
}

