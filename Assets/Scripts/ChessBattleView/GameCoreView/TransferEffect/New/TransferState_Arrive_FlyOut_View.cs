#if ACGGAME_CLIENT 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lucifer.ActCore;
using ZGameChess;

namespace ACG.Core
{
    public class TransferState_Arrive_FlyOut_View : TransferEventHandleBase
    {
        protected override void OnCustom(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            // 切换观察者的处理
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (e.customEventName == "OnObserverChanged" && (int)e.param2 == battleModel.MyPlayerId)
            {
                var time = (int)e.param / 1000f;
                OnEnter(time);
            }
        }

        // 客场视角
        private void PlayerFlyOut(int playerId, ChessPlayerUnit chessPlayerUnit, object param)
        {
            var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
            if (pm.observerId != GetFightBattleIndex())
            { // 客场的人跳出
                chessPlayerUnit.ShowBody();
                ChessBattleGlobal.Instance.ChessPlayerCtrl.JumpToImpl(chessPlayerUnit, EnemyTransferPos, 1.0f, (float)param);
            }
            else
            { // 主场的人隐藏
                chessPlayerUnit.HideBody();
            }
        }

        // 主场视角
        private void SetPlayerVisible(int playerId, ChessPlayerUnit chessPlayerUnit, object param)
        {
            var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
            if (pm.observerId != GetFightBattleIndex())
            { // 客场的人隐藏
                chessPlayerUnit.HideBody();
            }
            else
            { // 主场的人显示
                chessPlayerUnit.ShowBody();
            }
        }

        protected void OnEnter(float offset = 0)
        {
            // 如果纯逻辑播放的话，可能这个ChessBattleGlobal.Instance.ChessPlayerCtrl是没有的
            if (ChessBattleGlobal.Instance.ChessPlayerCtrl == null)
            {
                return;
            }
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_Hex_Info_Show_State, false);
            var currentObserverFightIndex = GetFightBattleIndex();

            SetHerosVisible(currentObserverIndex, true, false);
            //ChessBattleGlobal.Instance.ChessPlayerCtrl.SetVisible(currentObserverIndex, true);

            // 客场 需要跳出效果
            if (currentObserverIndex != currentObserverFightIndex)
            {
                // 隐藏己方等待区英雄
                SetWaitHeroVisible(currentObserverIndex, false);
                SetOutFieldUnitVisible(currentObserverIndex, false);
                // 隐藏敌方战斗单元
                SetHerosVisible(currentObserverFightIndex, false, false);

                // 显示传送特效
                DisplayTransferEffect(currentObserverIndex, EnemyTransferPos, true);
                DisplayTransferSound(currentObserverIndex, (int)TranferSoundType.TransferStartSound);

                ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction(PlayerFlyOut, offset);
                var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(currentObserverIndex);
                if (pm.observerId == currentObserverFightIndex)     // 如果此时我关注的是我的敌人的话 那我这时候应该在敌人的战场 而不是跳圈。
                {
                    // 这里的英雄直接原地隐藏掉
                    PlayHideHerosEffect(currentObserverIndex, 0.5f - offset, false, null);
                }
                else
                {
                    // 自己英雄化成光飞进自己自己小队长身体里
                    PlayHideHerosEffect(currentObserverIndex, 0.5f - offset, false);
                }
            }
            else
            {
                PlayerModel player = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(currentObserverIndex);
                // 隐藏敌方战斗单元
                SetHerosVisible(player.EnemyPlayerID, false, false);
                // 设置双方小队长显示隐藏
                ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction(SetPlayerVisible, null);

                int index = GetTargetIndex();
                bool isMirrorPlayer = index == ChessBattleModel.MIRR_PLAYER_ID;
                if (!isMirrorPlayer)
                {
                    // 显示传送特效
                    DisplayTransferEffect(index, EnemyTransferPos);
                    DisplayTransferSound(currentObserverIndex, (int)TranferSoundType.TransferStartSound);
                }
            }
        }

        protected override void OnStateEnter(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            OnEnter();
        }

        protected override void OnStateExit(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            // 客场 隐藏传送特效
            var currentObserverFightIndex = GetFightBattleIndex();
            if (currentObserverIndex != currentObserverFightIndex)
            {
                FreeTransferEffect(true);

                // 如果是客场 这里再强制隐藏一下我方的英雄 避免追帧的时候没隐藏掉的bug
                SetHerosVisible(currentObserverIndex, false, false);
            }
        }

        //private void PlayPlayerGotoTransferEffect(float offset)
        //{
        //    var currentObserverFightIndex = GetFightBattleIndex();
        //    if (currentObserverIndex != currentObserverFightIndex)
        //    {
        //        PlayPlayerGotoTransferEffect(currentObserverIndex, EnemyTransferPos, 1.0f, offset);
        //    }
        //}
    }
}

#endif