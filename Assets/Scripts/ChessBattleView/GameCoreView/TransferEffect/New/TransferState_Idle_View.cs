#if ACGGAME_CLIENT 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lucifer.ActCore;
using ZGameChess;

namespace ACG.Core
{
    public class TransferState_Idle_View : TransferEventHandleBase
    {
        protected override void OnCustom(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            //throw new NotImplementedException();
        }

        protected override void OnStateEnter(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            // Idle的时候 强制保证一下所有元素都在显示
            ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction((_, unit) => unit.ShowBody());
            // 显示自己
            SetHerosVisible(currentObserverIndex, true, false, true);
           //var chessPlayerCtrl = ChessBattleGlobal.Instance.ChessPlayerCtrl;
           // if (chessPlayerCtrl != null)
           //     chessPlayerCtrl.SetVisible(currentObserverIndex, true);

            // 显示敌人
            PlayerModel player = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(currentObserverIndex);
            SetHerosVisible(player.EnemyPlayerID, true, player.EnemyPlayerID == ChessBattleModel.MIRR_PLAYER_ID, true);
            //if (chessPlayerCtrl != null && player.EnemyPlayerID != ChessBattleModel.MIRR_PLAYER_ID)
            //    chessPlayerCtrl.SetVisible(player.EnemyPlayerID, true);
        }

        protected override void OnStateExit(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            //throw new NotImplementedException();
        }
    }
}

#endif