#if ACGGAME_CLIENT 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lucifer.ActCore;
using ZGameChess;

namespace ACG.Core
{
    public class TransferState_Arrive_Transfer_View : TransferEventHandleBase
    {
        protected override void OnCustom(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            // 切换观察者的处理
            var battleModel = ChessModelManager.Instance.GetBattleModel();
            if (e.customEventName == "OnObserverChanged" && (int)e.param2 == battleModel.MyPlayerId)
            {
                var time = (int)e.param / 1000f;
                OnEnter(time);

                // 切玩家 要在这时候隐藏自己和对手的英雄模型
                SetHerosVisible(currentObserverIndex, false, false);
                PlayerModel player = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(currentObserverIndex);
                if (player != null)
                {
                    SetHerosVisible(player.EnemyPlayerID, false, false);
                    //ChessBattleGlobal.Instance.ChessPlayerCtrl.SetVisible(player.EnemyPlayerID, false);
                }

                // 这里还要隐藏小小英雄、
                var currentObserverFightIndex = GetFightBattleIndex();
                if (currentObserverIndex != currentObserverFightIndex) // 客场 隐藏主场小小英雄
                {
                    ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction((playerId, unit) =>
                    {
                        var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
                        if (pm.observerId == GetFightBattleIndex())
                            unit.HideBody();
                    });
                }
                else // 主场 隐藏客场小小英雄
                {
                    ChessBattleGlobal.Instance.ChessPlayerCtrl.DoAction((playerId, unit) =>
                    {
                        var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
                        if (pm.observerId != GetFightBattleIndex())
                            unit.HideBody();
                    });
                }

            }
        }

        private void OnEnter(float offsetTime = 0)
        {
            // 如果纯逻辑播放的话，可能这个ChessBattleGlobal.Instance.ChessPlayerCtrl是没有的
            if (ChessBattleGlobal.Instance.ChessPlayerCtrl == null)
            {
                return;
            }
            var currentObserverFightIndex = GetFightBattleIndex();

            if (currentObserverIndex != currentObserverFightIndex)
            {
                ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_StartTransfer);

                //ACGEventManager.Instance.Send(EquipmentPanel.STOP_DRAG);

                AddDelayEvent(0.32f - offsetTime, Tranfer);
            }
        }

        protected override void OnStateEnter(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            OnEnter();
        }

        protected override void OnStateExit(ZGameClient.ACG_TCmdS2CTransferMsg e)
        {
            // 如果纯逻辑播放的话，可能这个ChessBattleGlobal.Instance.ChessPlayerCtrl是没有的
            if (ChessBattleGlobal.Instance.ChessPlayerCtrl == null)
            {
                return;
            }
            ACGEventManager.Instance.Send(EventType_BattleView.AutoChess_Battle_EndTransfer);

            var currentObserverFightIndex = GetFightBattleIndex();
            if (currentObserverIndex != currentObserverFightIndex)
            {
                ChessPlayerController chessPlayerController = ChessBattleGlobal.Instance.ChessPlayerCtrl;
                // 显示小队长血条
                chessPlayerController.DoAction((playerId, unit) =>
                {
                    if (!unit.IsHideBody)
                        unit.DoShowHeadInfo(true);
                });
            }
        }

        protected void Tranfer()
        {
            var currentObserverFightIndex = GetFightBattleIndex();
            if (currentObserverIndex != currentObserverFightIndex) // 客场
            {
                Log.InfoChannel(LogChannel.Transfer, " 切换场景");


                // 显示敌方战斗单元
                SetHerosVisible(currentObserverFightIndex, true, false);
                
                ChessPlayerController chessPlayerController = ChessBattleGlobal.Instance.ChessPlayerCtrl;

                // 显示主场玩家小队长
                chessPlayerController.DoAction((playerId, unit) =>
                {
                    var pm = ChessModelManager.Instance.GetBattleModel().GetPlayerModel(playerId);
                    if (pm.observerId == GetFightBattleIndex())
                    {
                        unit.ShowBody();
                        // 但是这个时候先不显示血条
                        unit.DoShowHeadInfo(false);
                    }
                });
                //chessPlayerController.SetVisible(currentObserverFightIndex, true);
                chessPlayerController.ChessPlayerFallToGround(currentObserverFightIndex);

                //隐藏小队长血条
                //chessPlayerController.SetHeadInfoVisible(currentObserverFightIndex, false);


                // 显示传送特效
                DisplayTransferEffect(currentObserverIndex, EnemyTransferPos);
                DisplayTransferSound(currentObserverIndex, (int)TranferSoundType.TransferTransSound);

            }
        }
    }
}

#endif