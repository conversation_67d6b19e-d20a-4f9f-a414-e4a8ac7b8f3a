#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

public class AllLodEffectResult
{
    public List<EffectScanResult> allResults = new List<EffectScanResult>();
    public int allPassCount = 0;
    public string path = string.Empty;
}

public class EffectScanConfig : BaseScanConfig
{
    public enum CheckType
    {
        Lod = 3,
        MaxBatches,
        AvgBatches,
        MaxOverDraw,
        AvgOverDraw,
        MaxPixelFill,
        AvgPixelFill,
        MaxDrawCall,
        AvgDrawCall,
        MaxParticle,
        AvgParticle,
        LodExist,
    }

    protected override List<int> NewTypeList()
    {
        List<int> typeList = new List<int>();
        Array array = Enum.GetValues(typeof(CheckType));
        foreach (int value in array)
        {
            typeList.Add(value);
        }
        return typeList;
    }

    protected override Dictionary<int, string> NewPrefabNameDict()
    {
        return new Dictionary<int, string>()
            {
                { (int)CheckType.MaxBatches,        "最大Batches" },
                { (int)CheckType.AvgBatches,        "平均Batches" },
                { (int)CheckType.MaxOverDraw,       "最大OverDraw" },
                { (int)CheckType.AvgOverDraw,       "平均OverDraw" },
                { (int)CheckType.MaxPixelFill,      "最大像素填充率" },
                { (int)CheckType.AvgPixelFill,      "平均像素填充率" },
                { (int)CheckType.MaxDrawCall,       "最大DrawCall" },
                { (int)CheckType.AvgDrawCall,       "平均DrawCall" },
                { (int)CheckType.MaxParticle,       "最大粒子数" },
                { (int)CheckType.AvgParticle,       "平均粒子数" },
                { (int)CheckType.Lod,               "Lod" },
                { (int)CheckType.LodExist,          "是否存在LOD" },
            };
    }

    protected override Dictionary<int, string> NewPrefabTipsDict()
    {
        return new Dictionary<int, string>()
            {
                { (int)CheckType.MaxBatches,        "最大Batches Lod0: 1 Lod1: 3 Lod2: 5" },
                { (int)CheckType.AvgBatches,        "平均Batches Lod0: 1 Lod1: 3 Lod2: 5" },
                { (int)CheckType.MaxOverDraw,       "最大OverDraw Lod0: 3 Lod1: 3 Lod2: 3" },
                { (int)CheckType.AvgOverDraw,       "平均OverDraw Lod0: 3 Lod1: 3 Lod2: 3" },
                { (int)CheckType.MaxPixelFill,      "最大像素填充率 Lod0: 1 Lod1: 1 Lod2: 1" },
                { (int)CheckType.AvgPixelFill,      "平均像素填充率 Lod0: 1 Lod1: 1 Lod2: 1" },
                { (int)CheckType.MaxDrawCall,       "最大DrawCall Lod0: 1 Lod1: 3 Lod2: 5" },
                { (int)CheckType.AvgDrawCall,       "平均DrawCall Lod0: 1 Lod1: 3 Lod2: 5" },
                { (int)CheckType.MaxParticle,       "最大粒子数 Lod0: 10 Lod1: 20 Lod2: 30" },
                { (int)CheckType.AvgParticle,       "最大粒子数 Lod0: 10 Lod1: 20 Lod2: 30" },
                { (int)CheckType.Lod,               "Lod" },
                { (int)CheckType.LodExist,          "必须存在Lod" },
            };
    }

    public override string GetWholeTips()
    {
        StringBuilder subHead = new StringBuilder();

        subHead.Append(EmailUtil.MakeEmailTableHead());
        subHead.Append(EmailUtil.CombineAHTMLRow(ParseColumn(EffectStandData.GetTitle()), 0));
        subHead.Append(EmailUtil.MakeEmailSeprator());

        for(int i = 0; i < PerfData.LODStandData.Length; i++)
        {
            EffectStandData data = PerfData.LODStandData[i];
            StringBuilder sb = new StringBuilder();
            if(i == 0)
            {
                sb.Append("Pub");
            }
            else
            {
                sb.Append("LOD");
                sb.Append(i - 1);
            }
            sb.Append(",");
            sb.Append(data.ToString());
            string[] standardData = sb.ToString().Split(',');
            subHead.Append(EmailUtil.CombineAHTMLRow(ParseColumn(standardData), i + 1));
        }
        subHead.Append(EmailUtil.MakeEmailTableTile());

        return subHead.ToString();
    }

    public override int Compare(BaseScanResult a, BaseScanResult b)
    {
        return 0;
    }

    public override string GetWhiteListPath()
    {
        return "Assets/Extensions/EffectPerformanceTool/特效白名单.txt";
    }

    public override List<BaseScanResult> GetScanResults()
    {
        List<BaseScanResult> prefabList = new List<BaseScanResult>();
        Dictionary<string, PerfData> m_effectPerfData = EffectTestScene.Instance.effectPerfData;

        Dictionary<string, AllLodEffectResult> allResult = new Dictionary<string, AllLodEffectResult>();
        foreach (PerfData info in m_effectPerfData.Values)
        {
            EffectScanResult result = new EffectScanResult(info, this);
            AllLodEffectResult lodeffect = null;
            if(allResult.TryGetValue(info.path, out lodeffect))
            {
                lodeffect.allResults.Add(result);
                lodeffect.allPassCount += result.passCount;
            }
            else
            {
                lodeffect = new AllLodEffectResult();
                lodeffect.allResults.Add(result);
                lodeffect.allPassCount = result.passCount;
                lodeffect.path = info.path;

                allResult.Add(info.path, lodeffect);
            }
        }

        List<AllLodEffectResult> sortList = new List<AllLodEffectResult>();
        sortList.AddRange(allResult.Values);

        sortList.Sort((a, b) => { return a.allPassCount.CompareTo(b.allPassCount); });

        foreach(AllLodEffectResult lodResult in sortList)
        {
            prefabList.AddRange(lodResult.allResults);
        }

        return prefabList;
    }

    private string[] ParseColumn(string[] colNames)
    {
        for (int i = 0; i < colNames.Length; ++i)
        {
            colNames[i] = string.Format("<th>{0}</th>", colNames[i]);
        }
        return colNames;
    }
}
#endif