#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

public abstract class BaseScanConfig
{
    public const string STR_COLOR = "<color={0}>{1}</color>";

    #region 检查项目名
    private Dictionary<int, string> m_PrefabNameDict;

    private Dictionary<int, string> GetPrefabNameDict()
    {
        if(m_PrefabNameDict == null)
        {
            m_PrefabNameDict = NewPrefabNameDict();
        }
        return m_PrefabNameDict;
    }

    protected virtual Dictionary<int, string> NewPrefabNameDict()
    {
        return new Dictionary<int, string>();
    }

    public virtual string GetCheckName(int type)
    {
        string value = string.Empty;
        GetPrefabNameDict().TryGetValue(type, out value);

        return value;
    }
    #endregion

    #region 检查提示
    private Dictionary<int, string> m_PrefabTipsDict;
    private Dictionary<int, string> GetPrefabTipsDict()
    {
        if (m_PrefabTipsDict == null)
        {
            m_PrefabTipsDict = NewPrefabTipsDict();
        }
        return m_PrefabTipsDict;
    }

    protected virtual Dictionary<int, string> NewPrefabTipsDict()
    {
        return new Dictionary<int, string>();
    }

    public virtual string GetCheckTips(int type)
    {
        string value = string.Empty;
        GetPrefabTipsDict().TryGetValue(type, out value);

        return value;
    }

    // 所有提示
    public virtual string GetWholeTips()
    {
        List<int> typeList = GetTypeList();
        StringBuilder subHead = new StringBuilder();
        foreach (int type in typeList)
        {
            subHead.Append(string.Format("</br>{0}：{1}", GetCheckName(type), BaseScanInfo.RemoveColor(GetCheckTips(type))));
        }

        subHead.Append("</br>");
        return subHead.ToString();
    }
    #endregion

    #region 检查类型枚举
    private List<int> m_Type;

    public List<int> GetTypeList()
    {
        if (m_Type == null)
        {
            m_Type = NewTypeList();
        }
        return m_Type;
    }

    protected virtual List<int> NewTypeList()
    {
        return new List<int>();
    }
    #endregion

    #region 检查白名单
    protected HashSet<string> m_whiteList = new HashSet<string>();

    private void LoadWhiteList()
    {
        //string path = GetWhiteListPath();
        //if(!string.IsNullOrEmpty(path))
        //{
        //    TextAsset textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
        //    StringReader sr = new StringReader(textAsset.text);
        //    string line = string.Empty;
        //    while((line = sr.ReadLine()) != null)
        //    {
        //        m_whiteList.Add(line);
        //    }
        //}
    }

    public virtual string GetWhiteListPath()
    {
        return string.Empty;
    }

    public bool isWhiteList(string path)
    {
        return m_whiteList.Contains(path);
    }
    #endregion

    #region csv

    public string GetCSVCol()
    {
        StringBuilder sb = new StringBuilder();

        sb.Append("文件路径");

        Dictionary<int, string> checkNames = GetPrefabNameDict();
        foreach(string value in checkNames.Values)
        {
            sb.Append(",");
            sb.Append(value);
        }

        return sb.ToString();
    }

    #endregion

    #region 结果排序方式

    public virtual int Compare(BaseScanResult a, BaseScanResult b)
    {
        return a.passCount.CompareTo(b.passCount);
    }

    #endregion

    public BaseScanConfig()
    {
        LoadWhiteList();
    }

    // 扫描
    public virtual List<BaseScanResult> GetScanResults()
    {
        return new List<BaseScanResult>();
    }

    // 邮件的行
    public virtual string[] GetColumn()
    {
        List<int> typeList = GetTypeList();
        string[] columnNames = new string[typeList.Count + 2];
        for (int i = columnNames.Length; --i >= 0;)
        {
            string name = null;
            if (i == 0)
            {
                name = "资源路径";
            }
            else if (i == 1)
            {
                name = "最后修改";
            }
            else
            {
                name = GetCheckName(typeList[i - 2]);
            }
            columnNames[i] = string.Format("<th>{0}</th>", name);
        }

        return columnNames;
    }

    public static string RemoveColor(string str)
    {
        int index = str.IndexOf("<color", 0);
        if (index < 0)
        {
            return str;
        }

        int endIndex = str.IndexOf(">", index);
        if (endIndex < 0)
        {
            return str;
        }

        int count = endIndex - index + 1;
        str = str.Remove(index, count);

        index = str.IndexOf("</color>");
        if (index < 0)
        {
            return str;
        }

        str = str.Remove(index, 8);

        return RemoveColor(str);
    }
}
#endif