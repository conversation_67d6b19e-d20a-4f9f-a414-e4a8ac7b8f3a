//#if UNITY_EDITOR
using System;
using System.IO;
using System.Net.Mail;
using System.Net;
using System.Text;

public class EmailUtil
{
    #region EmailGeneration

    static public string MakeAHTMLHeadRow(string[] element)
    {
        return "<tr><th>" + string.Join("</th><th>", element) + "</th></tr>";
    }

    static public string MakeAHTMLSingleRow(string[] element)
    {
        // old style, fast but without control
        return "<tr><td>" + string.Join("</td><td>", element) + "</td></tr>";
    }

    static public string MakeAHTMLSingleRow2(string[] element, string color)
    {
        // old style, fast but without control
        return "<tr style=\"background-color: #" + color + ";\"><td>" + string.Join("</td><td>", element) +
                "</td></tr>";
    }

    static public string CombineAHTMLRow(string[] element, int count)
    {
        // new style
        StringBuilder ret = new StringBuilder();

        if (count % 2 == 0)
        {
            ret.Append("<tr>");
        }
        else
        {
            ret.Append("<tr style=\"background-color: #f8f8f8;\">");
        }

        for (int i = 0; i < element.Length; ++i)
        {
            ret.Append(element[i]);
        }

        ret.Append("</tr>");

        return ret.ToString();
    }

    static public void CombineAHTMLRow(StringBuilder sb, string[] element, int count)
    {
        // new style

        if (count % 2 == 0)
        {
            sb.Append("<tr>");
        }
        else
        {
            sb.Append("<tr style=\"background-color: #f8f8f8;\">");
        }

        for (int i = 0; i < element.Length; ++i)
        {
            sb.Append(element[i]);
        }

        sb.Append("</tr>");
    }

    static public string MakeAHTMLSingleRow(string[] element, float[] ranking, int count)
    {
        // new style

        string ret = "";


        if (count % 2 == 0)
        {
            ret += "<tr>";
        }
        else
        {
            ret += "<tr style=\"background-color: #f8f8f8;\">";
        }


        for (int i = 0; i < element.Length; ++i)
        {
            string elem = element[i];
            float rank = ranking[i];

            if (rank < 0)
            {
                ret += string.Format("<td>{0}</td>", elem);
            }
            else
            {
                ret += string.Format("<td style=\"color:{0};\">{1}[{2:F1}%]</td>",
                    rank > 1.0f ? "red" : (rank > 0.8f ? "darkorange" : "green"), elem, rank * 100);
            }
        }

        ret += "</tr>";

        return ret;
    }

    static public string MakeEmailHead(string subject, string secondSubject)
    {
        string ret = "<style type=\"text/css\"> "
                        + "*{font-family: Microsoft YaHei;}"
                        + ".markdownbody{padding: 45px; border: 0;}"
                        + ".markdownsubbody{ padding: 1px 45px; border: 0; }"
                        + ".markdownbody table{    display: block; width: 100 %; overflow: auto; padding: 6px 13px; border: 1px solid #ddd; border-color: #ccc; border-collapse: collapse;}"
                        + ".markdownbody table tr{ background-color: #fff;border-top: 1px solid #ccc;}"
                        + ".markdownbody table th{ padding: 6px 13px; border: 1px solid #ddd;}"
                        + ".markdownbody table td{ padding: 6px 13px; border: 1px solid #ddd;}"
                        + "</style>"
                        + "<div class=\"markdownbody\" style=\"background-color: black; color: white;\">"
                        + string.Format("<p style=\"font-size: x-large;font-weight: bolder;\">{0}</p>", subject)
                        + string.Format("<p style=\"font-size: small;font-weight: bolder;\">{0}</p>", secondSubject)
                        + "</div><br><br>";


        return ret;
    }

    static public string MakeEmailHead(string subject, string secondSubject, string customStype)
    {
        string ret = "<style type=\"text/css\"> "
                        + "*{font-family: Microsoft YaHei;}"
                        + ".markdownbody{padding: 45px; border: 0;}"
                        + ".markdownsubbody{ padding: 1px 45px; border: 0; }"
                        + ".markdownbody table{    display: block; width: 100 %; overflow: auto; padding: 6px 13px; border: 1px solid #ddd; border-color: #ccc; border-collapse: collapse;}"
                        + ".markdownbody table tr{ background-color: #fff;border-top: 1px solid #ccc;}"
                        + ".markdownbody table th{ padding: 6px 13px; border: 1px solid #ddd;}"
                        + ".markdownbody table td{ padding: 6px 13px; border: 1px solid #ddd;}"
                        + customStype
                        + "</style>"
                        + "<div class=\"markdownbody\" style=\"background-color: black; color: white;\">"
                        + string.Format("<p style=\"font-size: x-large;font-weight: bolder;\">{0}</p>", subject)
                        + string.Format("<p style=\"font-size: small;font-weight: bolder;\">{0}</p>", secondSubject)
                        + "</div><br><br>";


        return ret;
    }

    static public string MakeSubTitle(string subject, string secondSubject)
    {
        string ret = ""
                        + "<div class=\"markdownsubbody\" style=\"background-color: #639; color: white;\">"
                        + string.Format("<p style=\"font-size: 3em;font-weight: bolder;\">{0}</p>", subject)
                        + string.Format("<p style=\"font-size: small;font-weight: bolder;\">{0}</p>", secondSubject)
                        + "</div>";


        return ret;
    }

    static public string MakeSubTitle2(string subject, string secondSubject)
    {
        string ret = ""
                        + "<div class=\"markdownsubbody\" style=\"background-color: #639; color: white;\">"
                        + string.Format("<p style=\"font-size: 24;font-weight: bolder;\">{0}</p>", subject)
                        + string.Format("<p style=\"font-size: 20;font-weight: bolder;\">{0}</p>", secondSubject)
                        + "</div>";

        return ret;
    }

    static public string MakeEmailTableHead()
    {
        string ret = "<div class=\"markdownbody\">"
                        + "<table><thead>";

        return ret;
    }

    static public string MakeEmailTableTile()
    {
        string ret = "</tbody></table>" + "</div>";
        return ret;
    }



    static public string MakeEmailSeprator()
    {
        string ret = "</thead><tbody>";
        return ret;
    }

    static public string MakeEmailTile()
    {
        string ret = "<div class=\"markdownbody\" style=\"background-color: lightslategrey; color: white;\">";
        ret += string.Format("<br><p>{0}</p>", "每项指标超过预算时会标记红色，达到预算80%时会标记橙色。相关人员需进行优化调整。");
        ret += "</div>";
        return ret;
    }


    /// <summary>
    /// SendMail
    /// </summary>
    /// <param name="subject"> 标题 </param>
    /// <param name="bodyhtml"> 正文 </param>
    /// <param name="attachFile"> 附件 </param>
    static public void SendMail(string subject, string bodyhtml, string attachFile = "")
    {
        string path = UnityEngine.Application.dataPath + @"/../ResourcesCheck/EmailBackup.html";
        path = path.Replace("\\", "/");

        FileInfo fileInfo = new FileInfo(path);
        if (fileInfo.Exists)
        {
            fileInfo.Delete();
        }
        else if (!fileInfo.Directory.Exists)
        {
            fileInfo.Directory.Create();
        }

        FileStream fs = null;
        StreamWriter sw = null;
        MailMessage mail = null;
        try
        {
            fs = File.Open(path, FileMode.CreateNew);
            if (fs != null)
            {
                sw = new StreamWriter(fs);
                sw.Write(bodyhtml);
            }

            mail = new MailMessage(EmailConfig.MailSender, EmailConfig.MailReceiver);

            SmtpClient client = new SmtpClient();
            client.Port = 25;
            client.EnableSsl = false;
            client.DeliveryMethod = SmtpDeliveryMethod.Network;
            client.UseDefaultCredentials = false;
            client.Credentials = new System.Net.NetworkCredential(EmailConfig.MailUsername, EmailConfig.MailPassword) as ICredentialsByHost;
            client.Host = EmailConfig.MailHost;

            mail.Priority = MailPriority.High;
            mail.IsBodyHtml = true;
            mail.Subject = subject;
            mail.Body = bodyhtml;

            if (File.Exists(attachFile))
            {
                Attachment attach = new Attachment(attachFile);
                mail.Attachments.Add(attach);
            }

            if (EmailConfig.Tos.Count != 0)
            {
                for (int i = 0; i < EmailConfig.Tos.Count; ++i)
                {
                    mail.To.Add(EmailConfig.Tos[i]);
                }
            }

            if (EmailConfig.CCs.Count != 0)
            {
                for (int i = 0; i < EmailConfig.CCs.Count; ++i)
                {
                    mail.CC.Add(EmailConfig.CCs[i]);
                }
            }

            //client.Send(mail);
        }
        catch (Exception e)
        {
            throw e;
        }
        finally
        {
            if (sw != null)
                sw.Close();
            if (fs != null)
                fs.Close();
            if (mail != null)
                mail.Dispose();
        }
    }

    #endregion
}
//#endif