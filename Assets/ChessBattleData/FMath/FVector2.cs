///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-7-3 16:57
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace GameFramework.FMath
{
    public struct FVector2
    {
        public Fix64 x;
        public Fix64 y;

        public FVector2(Fix64 x, Fix64 y)
        {
            this.x = x;
            this.y = y;
        }

        public FVector2(float x, float y)
        {
            this.x = x.ToFix64();
            this.y = y.ToFix64();
        }

        public static FVector2 down { get { return _down; } }
        static FVector2 _down = new FVector2(Fix64.zero, -Fix64.one);

        public static FVector2 left { get { return _left; } }
        static FVector2 _left = new FVector2(-Fix64.one, Fix64.zero);

        public static FVector2 one { get { return _one; } }
        static FVector2 _one = new FVector2(Fix64.one, Fix64.one);

        public static FVector2 right { get { return _right; } }
        static FVector2 _right = new FVector2(Fix64.one, Fix64.zero);

        public static FVector2 up { get { return _up; } }
        static FVector2 _up = new FVector2(Fix64.zero, Fix64.one);

        public static FVector2 zero { get { return _zero; } }
        static FVector2 _zero = new FVector2(Fix64.zero, Fix64.zero);

        /// <summary>
        /// length of vector.
        /// </summary>
        public Fix64 magnitude
        {
            get
            {
                return (x * x + y * y).Sqrt();
            }
        }

        /// <summary>
        /// squared of length.
        /// </summary>
        public Fix64 sqrMagnitude
        {
            get
            {
                return x * x + y * y;
            }
        }

        public FVector2 normalized
        {
            get
            {
                Fix64 len = magnitude;
                return new FVector2(x / len, y / len);
            }
        }

        public void Normalize()
        {
            Fix64 len = magnitude;
            x = x / len;
            y = y / len;
        }

        public void Scale(FVector2 scale)
        {
            x = x * scale.x;
            y = y * scale.y;
        }

        public void ClampMagnitude(Fix64 maxLength)
        {
            Fix64 len = magnitude;

            if (len > maxLength)
            {
                // x / magnitude * maxLength, y / magnitude * maxLength
                // x * (maxLength / magnitude), y * (maxLength / magnitude)
                Fix64 scaleFactor = maxLength / len;

                x = x * scaleFactor;
                y = y * scaleFactor;
            }
        }

        public override bool Equals(object obj)
        {
            return ((FVector2)obj).x == x && ((FVector2)obj).y == y;
        }

        public override int GetHashCode()
        {
            return magnitude.GetHashCode();
        }

        public void Set(Fix64 x, Fix64 y)
        {
            this.x = x;
            this.y = y;
        }

        public static FVector2 operator +(FVector2 a, FVector2 b)
        {
            return new FVector2(a.x + b.x, a.y + b.y);
        }

        public static FVector2 operator -(FVector2 a)
        {
            return new FVector2(-a.x, -a.y);
        }

        public static FVector2 operator -(FVector2 a, FVector2 b)
        {
            return new FVector2(a.x - b.x, a.y - b.y);
        }

        public static FVector2 operator *(Fix64 d, FVector2 a)
        {
            //return new FVector2(Fix64.Mul(a.x, d), Fix64.Mul(a.y, d));
            return new FVector2(a.x * d, a.y * d);
        }

        public static FVector2 operator *(FVector2 a, Fix64 d)
        {
            //return new FVector2(Fix64.Mul(a.x, d), Fix64.Mul(a.y, d));
            return new FVector2(a.x * d, a.y * d);
        }

        public static FVector2 operator /(FVector2 a, Fix64 d)
        {
            //return new FVector2(Fix64.Div(a.x, d), Fix64.Div(a.y, d));
            return new FVector2(a.x / d, a.y / d);
        }

        public static bool operator ==(FVector2 lhs, FVector2 rhs)
        {
            return lhs.x == rhs.x && lhs.y == rhs.y;
        }

        public static bool operator !=(FVector2 lhs, FVector2 rhs)
        {
            return lhs.x != rhs.x || lhs.y != rhs.y;
        }

        public override string ToString()
        {
            return x.ToString() + "," + y.ToString();
        }

        /// <summary>
        /// 求两个向量的欧几里得距离.
        /// </summary>
        /// <returns></returns>
        public static Fix64 Distance(FVector2 a, FVector2 b)
        {
            Fix64 dx = a.x - b.x;
            Fix64 dy = a.y - b.y;

            //return Fix64.Sqrt(Fix64.Square(dx) + Fix64.Square(dy));
            return (dx * dx + dy * dy).Sqrt();
        }

        /// <summary>
        /// 求两个向量的欧几里得距离的平方.
        /// </summary>
        /// <returns></returns>
        public Fix64 SqrDistance(FVector2 a, FVector2 b)
        {
            Fix64 dx = a.x - b.x;
            Fix64 dy = a.y - b.y;

            //return Fix64.Square(dx) + Fix64.Square(dy);
            return dx * dx + dy * dy;
        }

        public static Fix64 Dot(FVector2 a, FVector2 b)
        {
            //return Fix64.Mul(a.x, b.x) + Fix64.Mul(a.y, b.y);
            return a.x * b.x + a.y * b.y;
        }

        public static FVector2 Scale(FVector2 a, FVector2 b)
        {
            //return new FVector2(Fix64.Mul(a.x, b.x), Fix64.Mul(a.y, b.y));
            return new FVector2(a.x * b.x, a.y * b.y);
        }

        public static FVector2 Lerp(FVector2 a, FVector2 b, Fix64 t)
        {
            return new FVector2(
                Fix64.Lerp(a.x, b.x, t),
                Fix64.Lerp(a.y, b.y, t));
        }

        public static FVector2 LerpClamped(FVector2 a, FVector2 b, Fix64 t)
        {
            if (t > Fix64.one)
            {
                t = Fix64.one;
            }
            else if (t < Fix64.zero)
            {
                t = Fix64.zero;
            }

            return Lerp(a, b, t);
        }

        public static FVector2 Max(FVector2 a, FVector2 b)
        {
            return new FVector2(Fix64.Max(a.x, b.x), Fix64.Max(a.y, b.y));
        }

        public static FVector2 Min(FVector2 a, FVector2 b)
        {
            return new FVector2(Fix64.Min(a.x, b.x), Fix64.Min(a.y, b.y));
        }

        /// <summary>
        /// 求两个向量的夹角.(依赖于反三角函数的实现)
        /// </summary>
        /// <param name="from"></param>
        /// <param name="to"></param>
        /// <returns></returns>
        //public static Fix64 Angle(FVector2 from, FVector2 to)
        //{
        //    Fix64 dot = Dot(from, to);
        //    Fix64 scale = Fix64.Mul(from.magnitude, to.magnitude);

        //    return Fix64.ACos(Fix64.Div(scale));
        //}


        /// <summary>
        /// 求反射向量.
        /// </summary>
        /// <param name="inDirection">单位向量</param>
        /// <param name="inNormal">单位向量</param>
        /// <returns></returns>   
        public static FVector2 Reflect(FVector2 inDirection, FVector2 inNormal)
        {
            Fix64 projectionLen2 = Dot(inDirection, inNormal) * 2;
            FVector2 v = inNormal * projectionLen2;

            return inDirection +  v;
        }

        /// <summary>
        /// 向量v在n上的投影.
        /// </summary>
        /// <param name="v"></param>
        /// <param name="n">n为单位向量</param>
        /// <returns></returns>
        public static FVector2 Project(FVector2 v, FVector2 n)
        {
            return n * Dot(v, n);
        }
    }
}
