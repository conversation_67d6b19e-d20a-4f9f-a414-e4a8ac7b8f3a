namespace Lucifer.ActCore
{
    public static class ActCore_SpecializerTypeUint
    {
        public static void Init()
        {
            ActCore_SpecializerData<uint>.m_GetAFCValue = GetAFCValue;
            ActCore_SpecializerData<uint>.m_GetData = GetData;
            ActCore_SpecializerData<uint>.m_SetData = SetData;
        }

        private static string GetAFCValue()
        {
            return AFCValue.uint_type;
        }

        private static uint GetData(BaseVariableData variableData)
        {
            return variableData.variableValueuint;
        }

        private static void SetData(BaseVariableData variableData, uint data)
        {
            variableData.variableValueuint = data;
        }
    }
}


