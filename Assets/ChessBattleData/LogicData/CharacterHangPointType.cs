#if UNITY_EDITOR
using UnityEditor;
#endif
#if ACGGAME_CLIENT
using UnityEngine;
#else
using UnityEngine4Server;
#endif

public partial class CharacterHangPoint
{
    #region 支持的挂点类型

    //支持的骨骼点类型;
    public enum SupportHangPointType
    {
        #region SupportHangPointTypeAutoCreate 配合美术需求，这里的type依据配置自动生成吧, 值从1000起;
        [HangPointAttribute("ground_loc", "脚底", false, -1, "idle", "", true)]
        GROUND_LOC = 0,
        [HangPointAttribute("spine_loc", "胸部", false, -1, "idle", "Chest;Chest_position_scale", false)]
        SPINE_LOC = 1,
        [HangPointAttribute("top_loc", "头顶", false, -1, "idle", "", true)]
        TOP_LOC = 2,
        [HangPointAttribute("fire_l_loc", "左手腕", false, -1, "idle", "", false)]
        FIRE_L_LOC = 3,
        [HangPointAttribute("fire_r_loc", "右手腕", false, -1, "idle", "", false)]
        FIRE_R_LOC = 4,
        [HangPointAttribute("head_loc", "头部血条", false, -1, "idle", "", true)]
        HEAD_LOC = 5,
        [HangPointAttribute("neck_loc", "脖子", false, -1, "idle", "", false)]
        NECK_LOC = 6,
        [HangPointAttribute("tail_loc", "尾巴", false, -1, "idle", "", false)]
        TAIL_LOC = 7,
        [HangPointAttribute("mouth_loc", "嘴巴", false, -1, "idle", "", false)]
        MOUTH_LOC = 8,
        [HangPointAttribute("ground_loc", "脚底(无高度)", true, -1, "idle", "", false)]
        GROUND_NOHEIGHT_LOC = 9,
        [HangPointAttribute("bip_loc", "盆骨", false, -1, "idle", "Root_B;Root_B_position_scale", true)]
        BIP_LOC = 10,
        [HangPointAttribute("fa_l_loc", "飞行道具发射点左", false, -1, "idle", "", false)]
        FA_L_LOC = 11,
        [HangPointAttribute("fa_r_loc", "飞行道具发射点右", false, -1, "idle", "", false)]
        FA_R_LOC = 12,
        [HangPointAttribute("fa_l_loc01", "飞行道具发射点左特殊", false, -1, "idle", "", false)]
        FA_L_LOC01 = 13,
        [HangPointAttribute("fa_r_loc01", "飞行道具发射点右特殊", false, -1, "idle", "", false)]
        FA_R_LOC01 = 14,
        [HangPointAttribute("foot_loc", "脚上", false, -1, "idle", "", false)]
        FOOT_LOC = 15,
        [HangPointAttribute("t01_loc", "武器", false, -1, "idle", "Weapon;Weapon_position_scale", false)]
        T01_LOC = 16,
        [HangPointAttribute("t01_l_loc", "武器_左", false, -1, "idle", "L_Weapon;L_Weapon_position_scale", false)]
        T01_L_LOC = 17,
        [HangPointAttribute("t01_r_loc", "武器_右", false, -1, "idle", "R_Weapon;R_Weapon_position_scale", false)]
        T01_R_LOC = 18,
        [HangPointAttribute("fire_l_cloak_loc", "斗篷_左手", false, -1, "idle", "", false)]
        FIRE_L_cloak_LOC = 101,
        [HangPointAttribute("fire_r_cloak_loc", "斗篷_右手", false, -1, "idle", "", false)]
        FIRE_R_cloak_LOC = 102,
        [HangPointAttribute("t01_l_cloak_loc", "斗篷_武器_左", false, -1, "idle", "", false)]
        T01_L_CLOAK_H = 103,
        [HangPointAttribute("t01_r_cloak_loc", "斗篷_武器_右", false, -1, "idle", "", false)]
        T01_R_CLOAK_H = 104,
        [HangPointAttribute("spine_cloak_loc", "斗篷_胸部", false, -1, "idle", "", false)]
        SPINE_CLOAK_LOC = 105,
        [HangPointAttribute("head2_loc", "头部2", false, -1, "idle", "", false)]
        HEAD2_LOC = 106,
        [HangPointAttribute("spine02_loc", "胸部2", false, -1, "idle", "", false)]
        SPINE02_LOC = 107,
        [HangPointAttribute("eye_r_loc", "右眼", false, -1, "idle", "", false)]
        EYE_R_LOC = 108,
        [HangPointAttribute("eye_l_loc", "左眼", false, -1, "idle", "", false)]
        EYE_L_LOC = 109,
        [HangPointAttribute("foot_r_f_loc", "右前脚", false, -1, "idle", "", false)]
        FOOT_R_F_LOC = 110,
        [HangPointAttribute("foot_l_f_loc", "左前脚", false, -1, "idle", "", false)]
        FOOT_L_F_LOC = 111,
        [HangPointAttribute("foot_r_b_loc", "右后脚", false, -1, "idle", "", false)]
        FOOT_R_B_LOC = 112,
        [HangPointAttribute("foot_l_b_loc", "左后脚", false, -1, "idle", "", false)]
        FOOT_L_B_LOC = 113,
        [HangPointAttribute("spine_loc", "受击点", false, 0, "idle", "Chest;Chest_position_scale", true)]
        HURT_IDLE_LOC = 114,
        [HangPointAttribute("custom_03_loc", "自定义03", false, -1, "idle", "", false)]
        CUSTOM_03_LOC = 997,
        [HangPointAttribute("custom_02_loc", "自定义02", false, -1, "idle", "", false)]
        CUSTOM_02_LOC = 998,
        [HangPointAttribute("custom_loc", "自定义01", false, -1, "idle", "", false)]
        CUSTOM_LOC = 999,
        [HangPointAttribute("hair_loc", "头发", false, -1, "idle", "", false)]
        HAIR_LOC = 1000,
        [HangPointAttribute("wing_l_loc", "左翅膀", false, -1, "idle", "", false)]
        WING_L_LOC = 1001,
        [HangPointAttribute("wing_r_loc", "右翅膀", false, -1, "idle", "", false)]
        WING_R_LOC = 1002,
        [HangPointAttribute("hand_r_loc", "右手", false, -1, "idle", "R_Hand;R_Hand_position_scale", true)]
        HAND_R_LOC = 1003,
        [HangPointAttribute("hand_l_loc", "左手", false, -1, "idle", "L_Hand;L_Hand_position_scale", true)]
        HAND_L_LOC = 1004,
        [HangPointAttribute("hand2_l_loc", "左手2", false, -1, "idle", "Elbow_L_RBT_position_scale", true)]
        HAND2_L_LOC = 1005,
        [HangPointAttribute("hand2_r_loc", "右手2", false, -1, "idle", "Elbow_R_RBT_position_scale", true)]
        HAND2_R_LOC = 1006,
        [HangPointAttribute("foot_l_loc", "左脚", false, -1, "idle", "L_Foot;L_Foot_position_scale", true)]
        FOOT_L_LOC = 1007,
        [HangPointAttribute("foot_r_loc", "右脚", false, -1, "idle", "R_Foot;R_Foot_position_scale", true)]
        FOOT_R_LOC = 1008,
        [HangPointAttribute("ground_02_loc", "脚底02", false, -1, "idle", "", true)]
        GROUND_02_LOC = 1009,
        [HangPointAttribute("t02_l_loc", "武器_左2", false, -1, "idle", "", false)]
        T02_L_LOC = 1010,
        [HangPointAttribute("t03_l_loc", "武器_左3", false, -1, "idle", "", false)]
        T03_L_LOC = 1011,
        [HangPointAttribute("t02_r_loc", "武器_右2", false, -1, "idle", "", false)]
        T02_R_LOC = 1012,
        [HangPointAttribute("t03_r_loc", "武器_右3", false, -1, "idle", "", false)]
        T03_R_LOC = 1013,
        [HangPointAttribute("custom_04_loc", "自定义04", false, -1, "idle", "", false)]
        CUSTOM_04_LOC = 1014,
        [HangPointAttribute("custom_05_loc", "自定义05", false, -1, "idle", "", false)]
        CUSTOM_05_LOC = 1015,
        [HangPointAttribute("custom_06_loc", "自定义06", false, -1, "idle", "", false)]
        CUSTOM_06_LOC = 1016,
        [HangPointAttribute("custom_07_loc", "自定义07", false, -1, "idle", "", false)]
        CUSTOM_07_LOC = 1017,
        [HangPointAttribute("custom_08_loc", "自定义08", false, -1, "idle", "", false)]
        CUSTOM_08_LOC = 1018,
        [HangPointAttribute("custom_09_loc", "自定义09", false, -1, "idle", "", false)]
        CUSTOM_09_LOC = 1019,
        [HangPointAttribute("custom_10_loc", "自定义10", false, -1, "idle", "", false)]
        CUSTOM_10_LOC = 1020,
        [HangPointAttribute("custom_11_loc", "自定义11", false, -1, "idle", "", false)]
        CUSTOM_11_LOC = 1021,
        [HangPointAttribute("custom_12_loc", "自定义12", false, -1, "idle", "", false)]
        CUSTOM_12_LOC = 1022,
        [HangPointAttribute("custom_13_loc", "自定义13", false, -1, "idle", "", false)]
        CUSTOM_13_LOC = 1023,
        [HangPointAttribute("custom_14_loc", "自定义14", false, -1, "idle", "", false)]
        CUSTOM_14_LOC = 1024,
        [HangPointAttribute("custom_15_loc", "自定义15", false, -1, "idle", "", false)]
        CUSTOM_15_LOC = 1025,
        [HangPointAttribute("custom_16_loc", "自定义16", false, -1, "idle", "", false)]
        CUSTOM_16_LOC = 1026,
        #endregion SupportHangPointTypeAutoCreate
    }

    /// <summary>
    /// 支持的系统类型;
    /// </summary>
    public enum SupportSystemType
    {
        [EnumName("招募面板")] RECRUIT = 0,
    }

    /// <summary>
    /// 支持的部位类型;
    /// </summary>
    public enum SupportActionPart
    {
        [EnumName("全身")] BODY = 0,
        [EnumName("武器")] WEAPON = 1,
    }

    /// <summary>
    /// 不支持的绑点类型;
    /// </summary>
    public enum NotSupportHangPointType
    {
        [EnumName("HurtGroup_0")] HurtGroup_0 = 0,
    }

    #endregion
    
    #if UNITY_EDITOR
    
#region menuItemAutoCreate 这里是由工具自动生成的了,这个region里不要手动写代码;
    [MenuItem("GameObject/添加挂点/ground_loc(脚底)", true, 0)]
    static bool CheckCreateHangPointType_ground_loc_0()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)0, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/ground_loc(脚底)", false, 0)]
    static void CreateHangPointType_ground_loc_0()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)0, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/spine_loc(胸部)", true, 0)]
    static bool CheckCreateHangPointType_spine_loc_1()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/spine_loc(胸部)", false, 0)]
    static void CreateHangPointType_spine_loc_1()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/top_loc(头顶)", true, 0)]
    static bool CheckCreateHangPointType_top_loc_2()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)2, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/top_loc(头顶)", false, 0)]
    static void CreateHangPointType_top_loc_2()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)2, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fire_l_loc(左手腕)", true, 0)]
    static bool CheckCreateHangPointType_fire_l_loc_3()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)3, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fire_l_loc(左手腕)", false, 0)]
    static void CreateHangPointType_fire_l_loc_3()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)3, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fire_r_loc(右手腕)", true, 0)]
    static bool CheckCreateHangPointType_fire_r_loc_4()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)4, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fire_r_loc(右手腕)", false, 0)]
    static void CreateHangPointType_fire_r_loc_4()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)4, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/head_loc(头部血条)", true, 0)]
    static bool CheckCreateHangPointType_head_loc_5()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)5, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/head_loc(头部血条)", false, 0)]
    static void CreateHangPointType_head_loc_5()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)5, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/neck_loc(脖子)", true, 0)]
    static bool CheckCreateHangPointType_neck_loc_6()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)6, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/neck_loc(脖子)", false, 0)]
    static void CreateHangPointType_neck_loc_6()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)6, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/tail_loc(尾巴)", true, 0)]
    static bool CheckCreateHangPointType_tail_loc_7()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)7, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/tail_loc(尾巴)", false, 0)]
    static void CreateHangPointType_tail_loc_7()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)7, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/mouth_loc(嘴巴)", true, 0)]
    static bool CheckCreateHangPointType_mouth_loc_8()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)8, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/mouth_loc(嘴巴)", false, 0)]
    static void CreateHangPointType_mouth_loc_8()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)8, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/ground_loc(脚底(无高度))", true, 0)]
    static bool CheckCreateHangPointType_ground_loc_9()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)9, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/ground_loc(脚底(无高度))", false, 0)]
    static void CreateHangPointType_ground_loc_9()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)9, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/bip_loc(盆骨)", true, 0)]
    static bool CheckCreateHangPointType_bip_loc_10()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)10, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/bip_loc(盆骨)", false, 0)]
    static void CreateHangPointType_bip_loc_10()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)10, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fa_l_loc(飞行道具发射点左)", true, 0)]
    static bool CheckCreateHangPointType_fa_l_loc_11()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)11, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fa_l_loc(飞行道具发射点左)", false, 0)]
    static void CreateHangPointType_fa_l_loc_11()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)11, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fa_r_loc(飞行道具发射点右)", true, 0)]
    static bool CheckCreateHangPointType_fa_r_loc_12()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)12, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fa_r_loc(飞行道具发射点右)", false, 0)]
    static void CreateHangPointType_fa_r_loc_12()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)12, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fa_l_loc01(飞行道具发射点左特殊)", true, 0)]
    static bool CheckCreateHangPointType_fa_l_loc01_13()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)13, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fa_l_loc01(飞行道具发射点左特殊)", false, 0)]
    static void CreateHangPointType_fa_l_loc01_13()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)13, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fa_r_loc01(飞行道具发射点右特殊)", true, 0)]
    static bool CheckCreateHangPointType_fa_r_loc01_14()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)14, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fa_r_loc01(飞行道具发射点右特殊)", false, 0)]
    static void CreateHangPointType_fa_r_loc01_14()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)14, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_loc(脚上)", true, 0)]
    static bool CheckCreateHangPointType_foot_loc_15()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)15, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_loc(脚上)", false, 0)]
    static void CreateHangPointType_foot_loc_15()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)15, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t01_loc(武器)", true, 0)]
    static bool CheckCreateHangPointType_t01_loc_16()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)16, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t01_loc(武器)", false, 0)]
    static void CreateHangPointType_t01_loc_16()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)16, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t01_l_loc(武器_左)", true, 0)]
    static bool CheckCreateHangPointType_t01_l_loc_17()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)17, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t01_l_loc(武器_左)", false, 0)]
    static void CreateHangPointType_t01_l_loc_17()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)17, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t01_r_loc(武器_右)", true, 0)]
    static bool CheckCreateHangPointType_t01_r_loc_18()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)18, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t01_r_loc(武器_右)", false, 0)]
    static void CreateHangPointType_t01_r_loc_18()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)18, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fire_l_cloak_loc(斗篷_左手)", true, 0)]
    static bool CheckCreateHangPointType_fire_l_cloak_loc_101()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)101, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fire_l_cloak_loc(斗篷_左手)", false, 0)]
    static void CreateHangPointType_fire_l_cloak_loc_101()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)101, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/fire_r_cloak_loc(斗篷_右手)", true, 0)]
    static bool CheckCreateHangPointType_fire_r_cloak_loc_102()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)102, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/fire_r_cloak_loc(斗篷_右手)", false, 0)]
    static void CreateHangPointType_fire_r_cloak_loc_102()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)102, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t01_l_cloak_loc(斗篷_武器_左)", true, 0)]
    static bool CheckCreateHangPointType_t01_l_cloak_loc_103()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)103, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t01_l_cloak_loc(斗篷_武器_左)", false, 0)]
    static void CreateHangPointType_t01_l_cloak_loc_103()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)103, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t01_r_cloak_loc(斗篷_武器_右)", true, 0)]
    static bool CheckCreateHangPointType_t01_r_cloak_loc_104()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)104, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t01_r_cloak_loc(斗篷_武器_右)", false, 0)]
    static void CreateHangPointType_t01_r_cloak_loc_104()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)104, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/spine_cloak_loc(斗篷_胸部)", true, 0)]
    static bool CheckCreateHangPointType_spine_cloak_loc_105()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)105, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/spine_cloak_loc(斗篷_胸部)", false, 0)]
    static void CreateHangPointType_spine_cloak_loc_105()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)105, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/head2_loc(头部2)", true, 0)]
    static bool CheckCreateHangPointType_head2_loc_106()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)106, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/head2_loc(头部2)", false, 0)]
    static void CreateHangPointType_head2_loc_106()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)106, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/spine02_loc(胸部2)", true, 0)]
    static bool CheckCreateHangPointType_spine02_loc_107()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)107, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/spine02_loc(胸部2)", false, 0)]
    static void CreateHangPointType_spine02_loc_107()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)107, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/eye_r_loc(右眼)", true, 0)]
    static bool CheckCreateHangPointType_eye_r_loc_108()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)108, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/eye_r_loc(右眼)", false, 0)]
    static void CreateHangPointType_eye_r_loc_108()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)108, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/eye_l_loc(左眼)", true, 0)]
    static bool CheckCreateHangPointType_eye_l_loc_109()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)109, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/eye_l_loc(左眼)", false, 0)]
    static void CreateHangPointType_eye_l_loc_109()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)109, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_r_f_loc(右前脚)", true, 0)]
    static bool CheckCreateHangPointType_foot_r_f_loc_110()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)110, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_r_f_loc(右前脚)", false, 0)]
    static void CreateHangPointType_foot_r_f_loc_110()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)110, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_l_f_loc(左前脚)", true, 0)]
    static bool CheckCreateHangPointType_foot_l_f_loc_111()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)111, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_l_f_loc(左前脚)", false, 0)]
    static void CreateHangPointType_foot_l_f_loc_111()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)111, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_r_b_loc(右后脚)", true, 0)]
    static bool CheckCreateHangPointType_foot_r_b_loc_112()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)112, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_r_b_loc(右后脚)", false, 0)]
    static void CreateHangPointType_foot_r_b_loc_112()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)112, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_l_b_loc(左后脚)", true, 0)]
    static bool CheckCreateHangPointType_foot_l_b_loc_113()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)113, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_l_b_loc(左后脚)", false, 0)]
    static void CreateHangPointType_foot_l_b_loc_113()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)113, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/spine_loc(受击点)", true, 0)]
    static bool CheckCreateHangPointType_spine_loc_114()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)114, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/spine_loc(受击点)", false, 0)]
    static void CreateHangPointType_spine_loc_114()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)114, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_03_loc(自定义03)", true, 0)]
    static bool CheckCreateHangPointType_custom_03_loc_997()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)997, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_03_loc(自定义03)", false, 0)]
    static void CreateHangPointType_custom_03_loc_997()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)997, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_02_loc(自定义02)", true, 0)]
    static bool CheckCreateHangPointType_custom_02_loc_998()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)998, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_02_loc(自定义02)", false, 0)]
    static void CreateHangPointType_custom_02_loc_998()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)998, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_loc(自定义01)", true, 0)]
    static bool CheckCreateHangPointType_custom_loc_999()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)999, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_loc(自定义01)", false, 0)]
    static void CreateHangPointType_custom_loc_999()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)999, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/hair_loc(头发)", true, 0)]
    static bool CheckCreateHangPointType_hair_loc_1000()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1000, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/hair_loc(头发)", false, 0)]
    static void CreateHangPointType_hair_loc_1000()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1000, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/wing_l_loc(左翅膀)", true, 0)]
    static bool CheckCreateHangPointType_wing_l_loc_1001()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1001, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/wing_l_loc(左翅膀)", false, 0)]
    static void CreateHangPointType_wing_l_loc_1001()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1001, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/wing_r_loc(右翅膀)", true, 0)]
    static bool CheckCreateHangPointType_wing_r_loc_1002()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1002, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/wing_r_loc(右翅膀)", false, 0)]
    static void CreateHangPointType_wing_r_loc_1002()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1002, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/hand_r_loc(右手)", true, 0)]
    static bool CheckCreateHangPointType_hand_r_loc_1003()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1003, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/hand_r_loc(右手)", false, 0)]
    static void CreateHangPointType_hand_r_loc_1003()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1003, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/hand_l_loc(左手)", true, 0)]
    static bool CheckCreateHangPointType_hand_l_loc_1004()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1004, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/hand_l_loc(左手)", false, 0)]
    static void CreateHangPointType_hand_l_loc_1004()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1004, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/hand2_l_loc(左手2)", true, 0)]
    static bool CheckCreateHangPointType_hand2_l_loc_1005()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1005, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/hand2_l_loc(左手2)", false, 0)]
    static void CreateHangPointType_hand2_l_loc_1005()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1005, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/hand2_r_loc(右手2)", true, 0)]
    static bool CheckCreateHangPointType_hand2_r_loc_1006()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1006, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/hand2_r_loc(右手2)", false, 0)]
    static void CreateHangPointType_hand2_r_loc_1006()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1006, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_l_loc(左脚)", true, 0)]
    static bool CheckCreateHangPointType_foot_l_loc_1007()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1007, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_l_loc(左脚)", false, 0)]
    static void CreateHangPointType_foot_l_loc_1007()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1007, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/foot_r_loc(右脚)", true, 0)]
    static bool CheckCreateHangPointType_foot_r_loc_1008()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1008, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/foot_r_loc(右脚)", false, 0)]
    static void CreateHangPointType_foot_r_loc_1008()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1008, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/ground_02_loc(脚底02)", true, 0)]
    static bool CheckCreateHangPointType_ground_02_loc_1009()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1009, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/ground_02_loc(脚底02)", false, 0)]
    static void CreateHangPointType_ground_02_loc_1009()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1009, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t02_l_loc(武器_左2)", true, 0)]
    static bool CheckCreateHangPointType_t02_l_loc_1010()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1010, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t02_l_loc(武器_左2)", false, 0)]
    static void CreateHangPointType_t02_l_loc_1010()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1010, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t03_l_loc(武器_左3)", true, 0)]
    static bool CheckCreateHangPointType_t03_l_loc_1011()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1011, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t03_l_loc(武器_左3)", false, 0)]
    static void CreateHangPointType_t03_l_loc_1011()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1011, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t02_r_loc(武器_右2)", true, 0)]
    static bool CheckCreateHangPointType_t02_r_loc_1012()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1012, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t02_r_loc(武器_右2)", false, 0)]
    static void CreateHangPointType_t02_r_loc_1012()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1012, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/t03_r_loc(武器_右3)", true, 0)]
    static bool CheckCreateHangPointType_t03_r_loc_1013()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1013, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/t03_r_loc(武器_右3)", false, 0)]
    static void CreateHangPointType_t03_r_loc_1013()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1013, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_04_loc(自定义04)", true, 0)]
    static bool CheckCreateHangPointType_custom_04_loc_1014()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1014, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_04_loc(自定义04)", false, 0)]
    static void CreateHangPointType_custom_04_loc_1014()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1014, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_05_loc(自定义05)", true, 0)]
    static bool CheckCreateHangPointType_custom_05_loc_1015()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1015, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_05_loc(自定义05)", false, 0)]
    static void CreateHangPointType_custom_05_loc_1015()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1015, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_06_loc(自定义06)", true, 0)]
    static bool CheckCreateHangPointType_custom_06_loc_1016()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1016, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_06_loc(自定义06)", false, 0)]
    static void CreateHangPointType_custom_06_loc_1016()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1016, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_07_loc(自定义07)", true, 0)]
    static bool CheckCreateHangPointType_custom_07_loc_1017()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1017, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_07_loc(自定义07)", false, 0)]
    static void CreateHangPointType_custom_07_loc_1017()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1017, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_08_loc(自定义08)", true, 0)]
    static bool CheckCreateHangPointType_custom_08_loc_1018()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1018, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_08_loc(自定义08)", false, 0)]
    static void CreateHangPointType_custom_08_loc_1018()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1018, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_09_loc(自定义09)", true, 0)]
    static bool CheckCreateHangPointType_custom_09_loc_1019()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1019, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_09_loc(自定义09)", false, 0)]
    static void CreateHangPointType_custom_09_loc_1019()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1019, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_10_loc(自定义10)", true, 0)]
    static bool CheckCreateHangPointType_custom_10_loc_1020()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1020, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_10_loc(自定义10)", false, 0)]
    static void CreateHangPointType_custom_10_loc_1020()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1020, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_11_loc(自定义11)", true, 0)]
    static bool CheckCreateHangPointType_custom_11_loc_1021()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1021, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_11_loc(自定义11)", false, 0)]
    static void CreateHangPointType_custom_11_loc_1021()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1021, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_12_loc(自定义12)", true, 0)]
    static bool CheckCreateHangPointType_custom_12_loc_1022()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1022, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_12_loc(自定义12)", false, 0)]
    static void CreateHangPointType_custom_12_loc_1022()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1022, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_13_loc(自定义13)", true, 0)]
    static bool CheckCreateHangPointType_custom_13_loc_1023()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1023, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_13_loc(自定义13)", false, 0)]
    static void CreateHangPointType_custom_13_loc_1023()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1023, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_14_loc(自定义14)", true, 0)]
    static bool CheckCreateHangPointType_custom_14_loc_1024()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1024, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_14_loc(自定义14)", false, 0)]
    static void CreateHangPointType_custom_14_loc_1024()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1024, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_15_loc(自定义15)", true, 0)]
    static bool CheckCreateHangPointType_custom_15_loc_1025()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1025, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_15_loc(自定义15)", false, 0)]
    static void CreateHangPointType_custom_15_loc_1025()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1025, Selection.activeGameObject.transform);
        }
    }
    [MenuItem("GameObject/添加挂点/custom_16_loc(自定义16)", true, 0)]
    static bool CheckCreateHangPointType_custom_16_loc_1026()
    {
        if(Selection.activeGameObject != null){
                return CheckCanCreateSupportHangPointGo((SupportHangPointType)1026, Selection.activeGameObject.transform);
        }
        return false;
    }
    [MenuItem("GameObject/添加挂点/custom_16_loc(自定义16)", false, 0)]
    static void CreateHangPointType_custom_16_loc_1026()
    {
        if(Selection.activeGameObject != null){
                CreateSupportHangPointGo((SupportHangPointType)1026, Selection.activeGameObject.transform);
        }
    }
#endregion menuItemAutoCreate 这里是由工具自动生成的了,这个region里不要手动写代码;   

    public static bool CheckCanCreateSupportHangPointGo(SupportHangPointType spType, Transform parentTrans)
    {
        CharacterHangPoint chp = parentTrans.GetComponentInParent<CharacterHangPoint>();
        if (chp != null)
        {
            CharacterHangPointData chpData = chp.__GetReallyPosData(spType, null, isUseSecond:false);
            if (chpData != null)
            {
                Debug.Log("已存在对应的挂点了");
                return false;
            }
            return true;
        }
        else
        {
            Debug.LogError("没有找到挂点脚本");
            return false;
        }
    }

    public static void CreateSupportHangPointGo(SupportHangPointType shpType, Transform parentTrans)
    {
        CharacterHangPoint chp = parentTrans.GetComponentInParent<CharacterHangPoint>();
        if (chp != null)
        {
            string locName = ParticalCommonUtil.GetEnumNameAttributeValue(shpType);
            GameObject locGo = new GameObject(locName);
            locGo.transform.SetParent(parentTrans);
            locGo.transform.localPosition = Vector3.zero;
            locGo.transform.localEulerAngles = Vector3.zero;
            locGo.transform.localScale = Vector3.one;
            //直接增加一个挂点数据项;
            CharacterHangPointData chpData = new CharacterHangPointData(locGo.transform, Vector3.zero,
                Vector3.one, Vector3.zero, shpType);
            HangPointAttribute hangPointAttr = ParticalCommonUtil.GetEnumNameArrAttributeValue(shpType)[0] as HangPointAttribute;
            chpData.IsIgnoreHeight = hangPointAttr.IsIgnoreHeight;
            chp.pointPosData.Add(chpData);
        }
        else
        {
            Debug.LogError("没有找到挂点脚本");
        }
    }
    #endif
}
