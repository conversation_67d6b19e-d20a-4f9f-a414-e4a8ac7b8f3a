using System;
using System.Collections.Generic;
using TKFrame;
#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
#endif

public class EnumNameAttribute : System.Attribute
{
    public int __enumValue; //一些编辑器的特殊用途，对于业务无意义;
    public string __paramName; //一些编辑器的特殊用途，对于业务无意义;

    public string Value { get; set; }
    public string Desc { get; set; }

    public EnumNameAttribute()
    {
        
    }

    public EnumNameAttribute(string value, string desc = "")
    {
        Value = value;
        Desc = desc;
    }
}

/// <summary>
/// 参数限制类型;
/// </summary>
public enum ST_Param_LimitType
{
    None = 0,
    GridRange = 1,
}

/// <summary>
/// 参数值的初始类型, 因为部分值可能是static的，无法在属性里直接指定;
/// </summary>
public enum ST_ParamValue_InitType
{
    AutoChessConfig_HEXAGON_FORMATION_LINE_MAX_COUNT = 1,
    Vector3_Zero = 2,
}

[AttributeUsage(AttributeTargets.All, AllowMultiple = true)]
public class ST_ParamAttribute : Attribute
{
    public object _defaultValue;
    public object defaultValue {
        get
        {
            return _defaultValue;
        }
        set
        {
            _defaultValue = value;
            if (_defaultValue != null && _defaultValue is Enum)
            {
                if (this.indexEunmValueDic != null)
                {
                    foreach (var kvp in this.indexEunmValueDic)
                    {
                        if (kvp.Value == (int)_defaultValue)
                        {
                            this.enumIndex = kvp.Key;
                            break;
                        }
                    }
                }
            }
        }
    }
    public string desc { get; set; }
    private TKDictionary<int, string> enumValueDescDic;
    private TKDictionary<int, int> indexEunmValueDic;
    private string[] enumDescArr;
    private int enumIndex = -1;
    
    public ST_ParamAttribute()
    {
    
    }

    public ST_ParamAttribute(string desc)
    {
        this.desc = desc;
    }
    
    public ST_ParamAttribute Clone()
    {
        ST_ParamAttribute cloneData = new ST_ParamAttribute(this.desc);
        cloneData.defaultValue = this.defaultValue;
        return cloneData;
    }

    public void Paint()
    {
#if UNITY_EDITOR
        Type defaultValueType = this.defaultValue.GetType();
        bool isFindType = true;
        do
        {
            if (this.enumIndex < 0)
            {
                if (defaultValueType.Equals(typeof(int)))
                {
                    this.defaultValue =
                        (int) EditorGUILayout.IntField((int) this.defaultValue, GUILayout.Width(300.0f));
                    break;
                }
                else if (defaultValueType.Equals(typeof(float)))
                {
                    this.defaultValue =
                        (float) EditorGUILayout.FloatField((float) this.defaultValue, GUILayout.Width(300.0f));
                    break;
                }
                else if (defaultValueType.Equals(typeof(string)))
                {
                    this.defaultValue =
                        (string) EditorGUILayout.TextField((string) this.defaultValue, GUILayout.Width(300.0f));
                    break;
                }
                else if (defaultValueType.Equals(typeof(bool)))
                {
                    this.defaultValue =
                        (bool) EditorGUILayout.Toggle((bool) this.defaultValue, GUILayout.Width(300.0f));
                    break;
                }
                else if (defaultValueType.Equals(typeof(Vector3)))
                {
                    this.defaultValue = (Vector3) EditorGUILayout.Vector3Field("",
                        (Vector3) this.defaultValue, GUILayout.Width(300.0f));
                    break;
                }
                isFindType = false;
            }
            else
            {
                isFindType = false;
            }
        } while (false);

        if (!isFindType)
        {
            //来到这里认为是枚举了;
            if (enumValueDescDic == null)
            {
                Type valueType = this.defaultValue.GetType();
                enumValueDescDic = new TKDictionary<int, string>();
                indexEunmValueDic = new TKDictionary<int, int>();
                List<string> enumDescList = new List<string>();
                var valueArr = Enum.GetValues(this.defaultValue.GetType());
                for (int j = 0; j < valueArr.Length; j++)
                {
                    int value = (int) valueArr.GetValue(j);
                    var enumValue = Enum.ToObject(this.defaultValue.GetType(), value);
                    EnumNameAttribute[] enumNameAttributeArr =
                        ParticalCommonUtil.GetEnumNameArrAttributeValue((Enum) enumValue);
                    string title;
                    if (enumNameAttributeArr != null && enumNameAttributeArr.Length > 0)
                    {
                        EnumNameAttribute enumNameAttribute = enumNameAttributeArr[0] as EnumNameAttribute;
                        title = string.Format("【{0}】{2}{3}=>{1}", value, enumValue, enumNameAttribute.Value, enumNameAttribute.Desc);
                    }
                    else
                    {
                        title = string.Format("【{0}】{1}", value, enumValue);
                    }
                    enumValueDescDic.Add(value, title);
                    enumDescList.Add(title);
                    if (this._defaultValue.Equals(enumValue))
                    {
                        this.enumIndex = j;
                    }
                    indexEunmValueDic.Add(j, value);
                }
                enumDescArr = enumDescList.ToArray();
            }
            EditorGUI.BeginChangeCheck();
            this.enumIndex = EditorGUILayout.Popup(this.enumIndex, this.enumDescArr, GUILayout.Width(350.0f));
            if (EditorGUI.EndChangeCheck())
            {
                this.defaultValue = this.indexEunmValueDic[this.enumIndex];
            }            
        }
        #endif
    }
}
    
public class ST_TitleAttribute : Attribute
{
    public string desc { get; set; }
    public string mark { get; set; }

    public ST_TitleAttribute()
    {
    
    }

    public ST_TitleAttribute(string desc, string mark = null)
    {
        this.desc = desc;
        this.mark = mark;
    }

    public ST_TitleAttribute Clone()
    {
        ST_TitleAttribute cloneData = new ST_TitleAttribute(this.desc, this.mark);
        return cloneData;
    }
}

public class HitShapeAttribute : Attribute
{
    public bool enable { get; set; }
    public string title { get; set; }

    public HitShapeAttribute(string title, bool enable = true)
    {
        this.title = title;
        this.enable = enable;
    }

    public HitShapeAttribute Clone()
    {
        HitShapeAttribute cloneData = new HitShapeAttribute(this.title, this.enable);
        return cloneData;
    }
}

/**
 * 描述要开放到显示层的参数信息;
 */
public class ExecuteMethodAttribute : Attribute
{
    public int fromIndex = 0;
    public string[] paramCommnets;
    public ExecuteMethodAttribute(int fromIndex, params string[] paramComments)
    {
        this.fromIndex = fromIndex;
        this.paramCommnets = paramComments;
    }
}

/**
 * 描述接口的返回结果信息;
 */
public class ExecuteResultAttribute : Attribute
{
    public string comment;
    public Type type;
    public ExecuteResultAttribute(Type type, string comment)
    {
        this.type = type;
        this.comment = comment;
    }
}

/**
 * 描述接口的中间返回结果信息;
 */
[AttributeUsage(AttributeTargets.All, AllowMultiple = true)]
public class ExecuteMiddleResultAttribute : Attribute
{
    public string comment;
    public Type type;
    public ExecuteMiddleResultAttribute(Type type, string comment)
    {
        this.type = type;
        this.comment = comment;
    }
}

/**
 * 描述接口执行完后的流向，用于指向多个流向，如if等分支判断;
 */
[AttributeUsage(AttributeTargets.All, AllowMultiple = true)]
public class ExecuteFlowAttribute : Attribute
{
    public int index;
    public string desc;
    public ExecuteFlowAttribute(int index, string desc)
    {
        this.index = index;
        this.desc = desc;
    }
}
#if ACGGAME_CLIENT
// commonutil scripts/zgame/util/
public class ParticalCommonUtil
{
    
    //递归查找子节点
    public static UnityEngine.GameObject GetChild(UnityEngine.Transform root, string name)
    {
        UnityEngine.Transform node = FindTransform(root, name);
        if (node != null)
        {
            return node.gameObject;
        }
        return null;
    }

    //递归查找子节点
    public static UnityEngine.Transform FindTransform(UnityEngine.Transform root, string name)
    {
        UnityEngine.Transform dt = root.Find(name);
        if (null != dt)
        {
            return dt;
        }
        else
        {
            foreach (UnityEngine.Transform child in root)
            {
                dt = FindTransform(child, name);
                if (dt)
                {
                    return dt;
                }
            }
        }
        return null;
    }
    public static string GetEnumNameAttributeValue(System.Enum value)
    {
        string output = null;
        System.Type type = value.GetType();
        System.Reflection.FieldInfo fi = type.GetField(value.ToString());
        if (fi == null)
            return null;
        EnumNameAttribute[] attrs = fi.GetCustomAttributes(typeof(EnumNameAttribute), false) as EnumNameAttribute[];
        if (attrs.Length > 0)
        {
            output = attrs[0].Value;
        }
        else output = value.ToString();
        return output;
    }

    public static EnumNameAttribute[] GetEnumNameArrAttributeValue(System.Enum value)
    {
        System.Type type = value.GetType();
        System.Reflection.FieldInfo fi = type.GetField(value.ToString());
        if (fi == null)
            return null;
        EnumNameAttribute[] attrs = fi.GetCustomAttributes(typeof(EnumNameAttribute), false) as EnumNameAttribute[];
        return attrs;
    }
    
    public static T[] GetEnumAttributes<T>(System.Enum value) 
    {
        string output = null;
        System.Type type = value.GetType();
        System.Reflection.FieldInfo fi = type.GetField(value.ToString());
        if (fi == null)
            return null;
        T[] attrs = fi.GetCustomAttributes(typeof(T), false) as T[];
        return attrs;
    }
}
#endif