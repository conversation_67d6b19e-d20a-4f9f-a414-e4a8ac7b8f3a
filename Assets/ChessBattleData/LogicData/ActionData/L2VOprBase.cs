#if ACGGAME_CLIENT

public class L2VOprBase
{
    public virtual void Execute(L2VDataBaseParam data)
    {
        
    }
    // public ChessBattleUnit GetChessBattleUnitByUid(int playerId, int guid)
    // {
    //     // 如果纯逻辑播放的话，可能这个battle unit service是没有的
    //     if (ChessBattleGlobal.Instance.BattleUnitService == null)
    //     {
    //         return null;
    //     }
    //     ChessBattleUnit battleUnit = ChessBattleGlobal.Instance.BattleUnitService.GetBattleUnitById(playerId, guid);
    //     return battleUnit;
    // }

//    public BaseViewContainer GetViewContainer(ulong ObjectID)
//    {
//
//       
//    }
}
#endif