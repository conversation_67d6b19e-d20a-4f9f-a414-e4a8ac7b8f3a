#if ACGGAME_CLIENT
using System.Collections.Generic;
using Lucifer.ActCore;
[System.Serializable]
public class AllCharacterSkinConfigCollections : UnityEngine.ScriptableObject
{
    //所有的角色皮肤
    public CharacterSkinConfigData[] AllCharacterSkins;
}

[System.Serializable]
public class CharacterSkinConfigData
{
    public string name;
    public string ConfigName;
    public string tag = null;
    public System.Collections.Generic.List<Lucifer.ActCore.SkinReplaceData> ReplaceDatas;//需要替换的特效
    public string SkinConfigPath;
    public string SkinConfigPath_Pre;

    //for server  for multi thread
    public void FillSkinConfig(Lucifer.ActCore.CharacterSkinConfig skinConfig)
    {
        skinConfig.name = ConfigName;
        skinConfig.ReplaceDatas = new List<SkinReplaceData>();
        skinConfig.ReplaceDatas.AddRange(ReplaceDatas);
    }
}
#endif
