using TKPlugins;

namespace ZGameChess
{
    /// <summary>
    /// 配置项基类
    /// </summary>
    public class BaseConfig
    {
        protected DataAsset _dataAsset;
        protected DataGroupAsset _dataGroupAsset;

        public DataAsset dataAsset
        {
            get { return _dataAsset; }
        }
        
        public DataGroupAsset dataGroupAsset
        {
            get { return _dataGroupAsset; }
        }

        public virtual void Init(DataAsset dataAsset)
        {
            _dataAsset = dataAsset;
            InitParams();
        }
        
        public virtual void Init(DataGroupAsset dataGroupAsset)
        {
            _dataGroupAsset = dataGroupAsset;
            InitParams();
        }

        protected virtual void InitParams()
        {
            return;
        }

        public virtual void Reload()
        {
            InitParams();
        }
    }
}
