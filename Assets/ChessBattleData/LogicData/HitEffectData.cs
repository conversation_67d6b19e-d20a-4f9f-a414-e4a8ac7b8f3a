using TDRConfig;
#if !ACGGAME_CLIENT || LOGIC_THREAD
using UnityEngine4Server;
#else
using UnityEngine; 
#endif
namespace Lucifer.ActCore
{
    public class HitEffectData
    {
        public string url;
        public float size = 1;
        public float lifeTime = 1;
        public bool needRotateOffset;
        public bool rotationByHitterToBear;
        public Vector3 rotateOffset;
        public CharacterHangPoint.SupportHangPointType hangPoint = CharacterHangPoint.SupportHangPointType.SPINE_LOC;
        public TriggerAttackType_Enum triggerType = TriggerAttackType_Enum.Trigger_All;

        public bool bindTargetPosition = false;
        //public bool rotateWithAttack = false;
        public HitEffectData()
        {
            
        }
        public HitEffectData(string url, float size, float lifeTime,
            bool bindTargetPosition,
            bool needRotateOffset,
            bool rotationByHitterToBear,
            Vector3 rotateOffset, CharacterHangPoint.SupportHangPointType hangPoint)//,bool rotateWithAttack)
        {
            this.url = url;
            this.size = size;
            this.lifeTime = lifeTime;
            this.needRotateOffset = needRotateOffset;
            this.rotationByHitterToBear = rotationByHitterToBear;
            this.rotateOffset = rotateOffset;
            this.hangPoint = hangPoint;
            this.bindTargetPosition = bindTargetPosition;
            //this.rotateWithAttack = rotateWithAttack;
        }

        public void Clone(HitEffectData to)
        {
            to.url = url;
            to.size = size;
            to.lifeTime = lifeTime;
            to.needRotateOffset = needRotateOffset;
            to.rotationByHitterToBear = rotationByHitterToBear;
            to.rotateOffset = rotateOffset;
            to.hangPoint = hangPoint;
            to.bindTargetPosition = bindTargetPosition;
        }

        public void Reset()
        {
            url = null;
        }
    }
}

