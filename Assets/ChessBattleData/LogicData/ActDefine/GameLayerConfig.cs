#if ACGGAME_CLIENT
using UnityEngine;

namespace Lucifer.ActCore 
{
	public class GameLayerConfig
	{
		public const string LAYER_DEFAULT = "Default";
        //public const string LAYER_PLAYER = "Player";

        public const string LAYER_CHARACTER = "Hero";//"character";
		public const string LAYER_MONSTER = "monster";
        public const string LAYER_MONSTER_NO_COLLIDER = "monsterNoCollider";
        public const string LAYER_MAP = "map";
		public const string LAYER_NAV = "naviPath";
		public const string LAYER_AIRWALL = "airwall";
		public const string LAYER_NGUI = "NGUI";
        public const string LAYER_SCENE_EFFECT = "SceneEffect";
        public const string LAYER_EFFECT = "BattleEffect";//"Effect";
        public const string LAYER_TITANCOLLIDER = "TitanCollider";
		public const string LAYER_MAP2D = "map2d";
        public const string LAYER_UITimeLine = "UITimeLine";
        public const string LAYER_UI = "UI";
        public const string LAYER_MAPOBJS = "MapObjs";
        public const string LAYER_LEVEL = "Level";
        public const string LAYER_LEVEL_HELPER = "LevelHelper";

        public static int LAYER_DEFAULTR_INT = 0;
		//public static int LAYER_PLAYER_INT = 0;
		public static int LAYER_CHARACTER_INT = 0;
		public static int LAYER_MONSTER_INT = 0;
        public static int LAYER_MONSTER_NO_COLLIDER_INT = 0;
        public static int LAYER_MAP_INT = 0;
		public static int LAYER_AIRWALL_INT = 0;
		public static int LAYER_NAV_INT = 0;
        public static int LAYER_UI_INT = 0;
        public static int LAYER_NGUI_INT = 0;
		public static int LAYER_EFFECT_INT = 0;
        public static int LAYER_SCENE_EFFECT_INT = 0;
		public static int LAYER_MAP2D_INT = 0;
        public static int LAYER_MAPOBJS_INT = 0;
        public static int LAYER_LEVEL_INT = 0;
        public static int LAYER_LEVEL_HELPER_INT = 0;

        public static void Init()
        {
           
        }

        static GameLayerConfig()
        {
            LAYER_DEFAULTR_INT = LayerMask.NameToLayer(LAYER_DEFAULT);
            //LAYER_PLAYER_INT = LayerMask.NameToLayer(LAYER_PLAYER);
            LAYER_CHARACTER_INT = LayerMask.NameToLayer(LAYER_CHARACTER);
            LAYER_MONSTER_INT = LayerMask.NameToLayer(LAYER_MONSTER);
            LAYER_MONSTER_NO_COLLIDER_INT = LayerMask.NameToLayer(LAYER_MONSTER_NO_COLLIDER);
            LAYER_MAP_INT = LayerMask.NameToLayer(LAYER_MAP);
            LAYER_AIRWALL_INT = LayerMask.NameToLayer(LAYER_AIRWALL);
            LAYER_NAV_INT = LayerMask.NameToLayer(LAYER_NAV);
            LAYER_UI_INT = LayerMask.NameToLayer(LAYER_UI);
            LAYER_NGUI_INT = LayerMask.NameToLayer(LAYER_NGUI);
            LAYER_EFFECT_INT = LayerMask.NameToLayer(LAYER_EFFECT);
            LAYER_SCENE_EFFECT_INT = LayerMask.NameToLayer(LAYER_SCENE_EFFECT);
            LAYER_MAP2D_INT = LayerMask.NameToLayer(LAYER_MAP2D);
            LAYER_MAPOBJS_INT = LayerMask.NameToLayer(LAYER_MAPOBJS);
            LAYER_LEVEL_INT = LayerMask.NameToLayer(LAYER_LEVEL);
            LAYER_LEVEL_HELPER_INT = LayerMask.NameToLayer(LAYER_LEVEL_HELPER);
        }
    }
	
	public class GameTagConfig
	{
		public static string TAG_PLAYER = "Player";
		public static string TAG_BLOCK = "Block";
		public static string TAG_CHARACTER = "Character";
		public static string TAG_GUARDIAN = "Guardian";
        public static string TAG_AirWallEffect = "AirWallEffect";
		public static bool IsPlayer(GameObject go)
		{
			if(go != null)
			{
				return go.CompareTag(TAG_PLAYER);
			}
			return false;
		}
	}
}
#endif