using UnityEngine;

[System.Serializable]
public class SpringBoneData
{
#if false
    public Vector3 boneAxis = new Vector3(-1.0f, 0.0f, 0.0f);
    public float radius = 0.05f;
    public bool isUseEachBoneForceSettings = false;
    public float stiffnessForce = 0.01f;
    public float dragForce = 0.4f;
    public Vector3 springForce = new Vector3(0.0f, -0.0001f, 0.0f);
    public bool debug = false;
    public float threshold = 0.01f;
#else
    public string BoneName ;
    public Vector3 boneAxis =  new Vector3 (-1.0f, 0.0f, 0.0f);
    public float radius = 0.02f;
    public float stiffnessForce = 0f;
    public float dragForce = 0f;
    public float softness = 1.0f;
    public float threshold = 0.01f;
    public Vector3 springForce ;
#endif
}
