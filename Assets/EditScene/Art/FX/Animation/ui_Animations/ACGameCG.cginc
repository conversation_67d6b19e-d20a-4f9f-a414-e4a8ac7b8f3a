#ifndef ACGAME_CG_INCLUDED
#define ACGAME_CG_INCLUDED

fixed4 _GradientColor;
fixed _HalfLambertValue;
float _NormalFactor;
fixed _HeightFactor;
fixed4 _FogStartColor;
fixed4 _FogMiddleColor;
fixed4 _FogEndColor;
fixed4 _FogTopColor;
float _FogStart;
float _FogMiddle;
float _FogEnd;
float _FogBottom;
float _FogTop;
fixed4 _LightColor0;
fixed _BackBrightness;
fixed _Strength;

//effect
sampler2D _MainTex;
float4 _MainTex_ST;
sampler2D _DissolveTex;
float4 _DissolveTex_ST;
sampler2D _MaskTex;
float4 _MaskTex_ST;

fixed _Cutoff;
half _Dissolve;
fixed _DissolveToggle;
half _Mask;
fixed _MaskToggle;
float _Power;
fixed _SpeedToggle;
float _SpeedX;
float _SpeedY;
float _SpeedX2;
float _SpeedY2;
float _SpeedX3;
float _SpeedY3;
half _UVRotate;
fixed4 _MaskSecUVOffset;
float _Rotate;
fixed4 _Color;
fixed4 _Color2;
fixed4 _CeilColorA;
fixed4 _CeilColorB;
fixed _GradientValue;

fixed _ScreenSpaceUV;

//#ifdef _OFFSCREEN_RENDER
sampler2D_float _CameraDepthTexture;
//#endif

#ifdef _UIEFFECT_ON
float _XMin;
float _XMax;
float _YMin;
float _YMax;
fixed4 _FadeRange;
#endif

//effect
struct effect_in_base
{
	float4 vertex : POSITION;
	float4 texcoord : TEXCOORD0;
	float4 control : TEXCOORD1;
#ifdef _OFFSCREEN_RENDER
	float4 screenPos       : TEXCOORD2;
#endif
	half4 color : COLOR;
	UNITY_VERTEX_INPUT_INSTANCE_ID
};

struct effect_in_full
{
	float4 vertex : POSITION;
	float4 texcoord : TEXCOORD0;
	float4 control : TEXCOORD1;
#ifdef _OFFSCREEN_RENDER
	float4 screenPos       : TEXCOORD2;
#endif
	float3 normal : NORMAL;
	half color : COLOR;
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
struct effect_v2f_base
{
	float4 vertex : SV_POSITION;
	half4 color : COLOR;
	half2 uv : TEXCOORD0;
	float4 control : TEXCOORD1;
	float4 screenPos : TEXCOORD2;
#ifdef _UIEFFECT_ON
	float4 ndcPos : TEXCOORD3;
#endif
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
struct effect_v2f_mask
{
	float4 vertex : SV_POSITION;
	half4 color : COLOR;
	half4 uv : TEXCOORD0;
	float4 control : TEXCOORD1;
#ifdef _OFFSCREEN_RENDER
	float4 screenPos       : TEXCOORD2;
#endif
#ifdef _UIEFFECT_ON
	float4 ndcPos : TEXCOORD3;
#endif
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
struct effect_v2f_diss
{
	float4 vertex : SV_POSITION;
	half4 color : COLOR;
	half4 uv : TEXCOORD0;//xy:uv;  zw:dissolve or mask
	float4 control : TEXCOORD1;//xy:main's uv curve;  zw:dissolve's or mask's uv curve
	half2 uv1 : TEXCOORD2;
	float4 screenPos  : TEXCOORD3;
#ifdef _UIEFFECT_ON
	float4 ndcPos : TEXCOORD4;
#endif
	half2 uv2 : TEXCOORD5;
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
struct effect_v2f_dima
{
	float4 vertex : SV_POSITION;
	half4 color : COLOR;
	float4 uv : TEXCOORD0;//xy:uv;  zw:dissolve or mask
	float4 control : TEXCOORD1;//xy:main's uv curve;  zw:dissolve's or mask's uv curve
	half4 uv1 : TEXCOORD2;
#ifdef _OFFSCREEN_RENDER
	float4 screenPos       : TEXCOORD3;
#endif
#ifdef _UIEFFECT_ON
	float4 ndcPos : TEXCOORD4;
#endif
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
struct effect_v2f_full
{
	float4 vertex : SV_POSITION;
	half4 color : COLOR;
	float4 uv : TEXCOORD0;//xy:uv;  zw:dissolve and mask
	float4 control : TEXCOORD1;//xy:main's uv curve;  zw:dissolve's or mask's uv curve
	float2 control2 : TEXCOOED2;//x:distortion;  y:unknown
	half4 uv1 : TEXCOORD2;
	half3 normalDir : TEXCOORD3;
#ifdef _OFFSCREEN_RENDER
	float4 screenPos       : TEXCOORD4;
#endif
#ifdef _UIEFFECT_ON
	float4 ndcPos : TEXCOORD5;
#endif
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
struct effect_v2f_scan
{
	float4 pos : SV_POSITION;
	half4 color : COLOR;
	float4 uv : TEXCOORD0;//xy:uv;  zw:Object Pos
	float4 control : TEXCOORD1;//xy:main's uv curve;  zw:dissolve's or mask's uv curve
#ifdef _UIEFFECT_ON
	float4 ndcPos : TEXCOORD2;
#endif
#ifdef _OFFSCREEN_RENDER
	float4 screenPos       : TEXCOORD3;
#endif
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
//when equal
inline fixed When_EQ(half x, half y)
{
    return 1.0 - abs(sign(x - y));
}
//when not equal
inline fixed When_NEQ(half x, half y)
{
    return abs(sign(x - y));
}
//when greater than or equal to
inline fixed When_GE(half x, half y)
{
    return step(y, x);
}
//when less than or equal to
inline fixed When_LE(half x, half y)
{
    return step(x, y);
}
//when greater than
inline fixed When_GT(half x, half y)
{
    return 1.0 - When_LE(x, y);
}
//when less than
inline fixed When_LT(half x, half y)
{
    return 1.0 - When_GE(x, y);
}
//and
inline fixed And(fixed a, fixed b)
{
    return a * b;
}
//flip texture uv
inline half2 FlipUV(half2 sourceUV)
{
    half2 uv = sourceUV;
    fixed value1 = And(When_LT(_UVRotate, 2.0), When_GE(_UVRotate, 1.0));
    fixed value2 = And(When_LT(_UVRotate, 3.0), When_GE(_UVRotate, 2.0));
    fixed value3 = When_GE(_UVRotate, 3.0);
    if (value1)
    {
        uv.x = 1.0 - sourceUV.y;
        uv.y = sourceUV.x;
    }
    else if (value2)
    {
        uv.x = 1.0 - sourceUV.x;
        uv.y = 1.0 - sourceUV.y;
    }
    else if (value3)
    {
        uv.x = sourceUV.y;
        uv.y = 1.0 - sourceUV.x;
    }
    return uv;
}
//rotate texture uv
inline float2 RotateUV(float2 uv)
{
	float2 rotateUV = uv.xy - float2(0.5f,0.5f);
	float s =sin(_Rotate/57.2957796);
	float c = cos(_Rotate/57.2957796);
	rotateUV = float2( rotateUV.x *c -rotateUV.y*s, rotateUV.x*s + rotateUV.y*c);
	rotateUV += float2(0.5f,0.5f);
	return rotateUV;
}
inline fixed4 CeilColor(fixed powValue,fixed powControl)
{
	fixed4 color = lerp(_CeilColorA,_CeilColorB,saturate(ceil(pow(powValue,_Power * powControl))));
	return color;
}
//set color.a by dissolve texture and control value
inline fixed AlphaByDissolve(half2 uv,fixed control)
{
	fixed dissolve = tex2D(_DissolveTex,uv).r;
	fixed alpha = saturate(dissolve - control);
	return alpha;
}
//set color.a by particle.alpha and control value
inline fixed AlphaByDissolve(fixed source,fixed control)
{
	fixed alpha = saturate(source - control);
	return alpha;
}
//get uv by time and speed
//source UV,custom datas,uniform data.
inline half2 GetAnimationUV(half2 sourceUV,half2 sourceSpeed,half2 baseSpeed)
{
	half2 uv = sourceUV;

#ifdef _ISCLAMP
	float2 speed = lerp(float2(_Time.y,_Time.y),sourceSpeed,_SpeedToggle);
	uv.x = uv.x + baseSpeed.x * speed.x;
	uv.y = uv.y + baseSpeed.y * speed.y;
#else
	float2 speed = lerp(float2(_Time.y,_Time.y),sourceSpeed,_SpeedToggle);
	uv.x = uv.x + frac(baseSpeed.x * speed.x);
	uv.y = uv.y + frac(baseSpeed.y * speed.y);
#endif
	
	return uv;
} 
//base vert
effect_v2f_base effectVertexBase(effect_in_base v)
{
	effect_v2f_base o = (effect_v2f_base)0;

	UNITY_SETUP_INSTANCE_ID(v);
	UNITY_TRANSFER_INSTANCE_ID(v,o);

	o.vertex = UnityObjectToClipPos(v.vertex);
//#if _OFFSCREEN_RENDER
	o.screenPos = ComputeScreenPos(o.vertex);
	COMPUTE_EYEDEPTH(o.screenPos.z);
//#endif
	//#if _OFFSCREEN_RENDER
	////o.screenPos.z += _NormalFactor;
	//COMPUTE_EYEDEPTH(o.screenPos.z);
	//#endif

	half2 uv = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
	//half2 uv2 = screenPos.xy/screenPos.ww;
	//uv2 = v.vertex.xz;
	//uv2 = TRANSFORM_TEX(uv2, _MainTex);
	//o.uv.xy = lerp(uv1, uv2, _ScreenSpaceUV);
	o.uv.xy = FlipUV(uv);
	o.color.rgba = v.color.rgba;
	o.control.xyzw = v.control.xyzw;
#if UNITY_UV_STARTS_AT_TOP
	o.vertex.z += _NormalFactor;
#else
	o.vertex.z -= _NormalFactor;
#endif
#ifdef _UIEFFECT_ON
	o.ndcPos = o.vertex / o.vertex.w;
#endif
	return o;
}

//mask texture
//dissovle texture
//only Animate uv
effect_v2f_diss effectVertexDissUV(effect_in_base v)
{
	effect_v2f_diss o = (effect_v2f_diss)0;
	o.vertex = UnityObjectToClipPos(v.vertex);
	#if _OFFSCREEN_RENDER
	o.screenPos = ComputeScreenPos(o.vertex);
	COMPUTE_EYEDEPTH(o.screenPos.z);
	#endif
	o.uv.xy = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
	o.uv.xy = FlipUV(o.uv.xy);
	o.uv.zw = v.texcoord.zw;//dissolve and mask
	

	o.uv1.xy = TRANSFORM_TEX(v.texcoord.xy,_DissolveTex);
	o.color.rgba = v.color.rgba;
	o.control.xyzw = v.control.xyzw;//xy : main texture,zw : dissolve or mask texture

#if UNITY_UV_STARTS_AT_TOP
	o.vertex.z += _NormalFactor;
#else
	o.vertex.z -= _NormalFactor;
#endif


#ifdef _UIEFFECT_ON
	o.ndcPos = o.vertex / o.vertex.w;
#endif
	half2 uv = o.uv.xy;
	o.uv.xy = GetAnimationUV(o.uv.xy, o.control.xy, half2(_SpeedX,_SpeedY));
	o.uv2.xy = uv;
	return o;
}

//mask texture
//dissovle texture
//only Animate uv
effect_v2f_diss effectVertexDissUV1(effect_in_base v)
{
	effect_v2f_diss o = (effect_v2f_diss)0;
	o.vertex = UnityObjectToClipPos(v.vertex);
	#if _OFFSCREEN_RENDER
	o.screenPos = ComputeScreenPos(o.vertex);
	COMPUTE_EYEDEPTH(o.screenPos.z);
	#endif
	o.uv.xy = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
	o.uv.xy = FlipUV(o.uv.xy);
	o.uv.zw = v.texcoord.zw;//dissolve and mask
	

	o.uv1.xy = TRANSFORM_TEX(v.texcoord.xy,_DissolveTex);
	o.color.rgba = v.color.rgba;
	o.control.xyzw = v.control.xyzw;//xy : main texture,zw : dissolve or mask texture

#if UNITY_UV_STARTS_AT_TOP
	o.vertex.z += _NormalFactor;
#else
	o.vertex.z -= _NormalFactor;
#endif
#ifdef _UIEFFECT_ON
	o.ndcPos = o.vertex / o.vertex.w;
#endif
	o.uv1.xy = GetAnimationUV(o.uv1.xy, o.control.zw, half2(0, 0));
	o.uv2.xy = o.uv.xy;
	return o;
}

//mask texture
//dissovle texture
effect_v2f_diss effectVertexDiss(effect_in_base v)
{
	effect_v2f_diss o = (effect_v2f_diss)0;
	o.vertex = UnityObjectToClipPos(v.vertex);

#if _OFFSCREEN_RENDER
	o.screenPos = ComputeScreenPos(o.vertex);
	COMPUTE_EYEDEPTH(o.screenPos.z);
#endif

	o.uv.xy = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
	o.uv.xy = FlipUV(o.uv.xy);
	o.uv.zw = v.texcoord.zw;//dissolve and mask
	

	o.uv1.xy = TRANSFORM_TEX(v.texcoord.xy,_DissolveTex);
	//【868936899】 MaskTex UV Offset -- neozfzheng
	o.uv2.xy = TRANSFORM_TEX(v.texcoord.xy,_MaskTex);
	o.color.rgba = v.color.rgba;
	o.control.xyzw = v.control.xyzw;//xy : main texture,zw : dissolve or mask texture

#if UNITY_UV_STARTS_AT_TOP
	o.vertex.z += _NormalFactor;
#else
	o.vertex.z -= _NormalFactor;
#endif
#ifdef _UIEFFECT_ON
	o.ndcPos = o.vertex / o.vertex.w;
#endif

	o.uv.xy = GetAnimationUV(o.uv.xy, o.control.xy, half2(_SpeedX,_SpeedY));
	o.uv1.xy = GetAnimationUV(o.uv1.xy, o.control.zw,half2(_SpeedX2,_SpeedY2));
	//【868936899】 MaskTex UV Offset -- neozfzheng
	o.uv2.xy = GetAnimationUV(o.uv2.xy, _Time.yy, half2(_SpeedX3, _SpeedY3));
	
	return o;
}
//dissolve and mask texture
effect_v2f_dima effectVertexDima(effect_in_base v)
{
	effect_v2f_dima o = (effect_v2f_dima)0;
	o.vertex = UnityObjectToClipPos(v.vertex);
#if UNITY_UV_STARTS_AT_TOP
	o.vertex.z += _NormalFactor;
#else
	o.vertex.z -= _NormalFactor;
#endif
	o.uv.xy = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
	o.uv.xy = FlipUV(o.uv.xy);
	o.uv.zw = v.texcoord.zw;
	o.uv1.xy = TRANSFORM_TEX(v.texcoord.xy,_DissolveTex);
	o.uv1.zw = TRANSFORM_TEX(v.texcoord.xy,_MaskTex);
	o.color.rgba = v.color.rgba;
	o.control.xyzw = v.control.xyzw;

#if _OFFSCREEN_RENDER
	o.screenPos = ComputeScreenPos(o.vertex);
	COMPUTE_EYEDEPTH(o.screenPos.z);
#endif
#ifdef _UIEFFECT_ON
	o.ndcPos = o.vertex / o.vertex.w;
#endif
	return o;
}

//【864774823】Grid Scan		--horacezhao
effect_v2f_scan effectVertexScan(effect_in_base v)
{
	effect_v2f_scan o = (effect_v2f_scan)0;
	o.pos = UnityObjectToClipPos(v.vertex);
#if UNITY_UV_STARTS_AT_TOP
	o.pos.z += _NormalFactor;
#else
	o.pos.z -= _NormalFactor;
#endif
	o.uv.xy = TRANSFORM_TEX(v.texcoord.xy,_MainTex);
	o.uv.xy = FlipUV(o.uv.xy);
	o.uv.zw = RotateUV(v.vertex.xy);
	o.color.rgba = v.color.rgba;
	o.control.xyzw = v.control.xyzw;
#if _OFFSCREEN_RENDER
	o.screenPos = ComputeScreenPos(o.pos);
	COMPUTE_EYEDEPTH(o.screenPos.z);
#endif
	return o;
}

float GetOffscreenAlpha(float alpha, float4 screenPos)
{
#if _OFFSCREEN_RENDER
	// Do Z clip
	float depth = SAMPLE_DEPTH_TEXTURE_PROJ(_CameraDepthTexture, UNITY_PROJ_COORD(screenPos));
	//depth -= _NormalFactor;
	float zbuf = LinearEyeDepth(depth);
	float partZ = screenPos.z;
	float zalpha = saturate((zbuf - partZ + 1e-2f) * 10000);
	return saturate(alpha * zalpha);
#endif
}
#endif